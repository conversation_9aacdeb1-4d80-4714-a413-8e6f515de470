package com.abl.qr.web;

import com.abl.qr.db.AlipayHostTxnLog;
import com.abl.qr.db.DBUtils;
import com.google.gson.Gson;
import org.apache.activemq.ActiveMQConnectionFactory;
import org.apache.commons.configuration.ConfigurationException;
import org.apache.commons.configuration.PropertiesConfiguration;
import org.apache.commons.dbcp2.BasicDataSource;
import org.apache.log4j.Logger;

import javax.jms.*;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Date;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 12/5/17.
 */
public class AlipayNotify extends HttpServlet {
    public static Logger logger=Logger.getLogger(AlipayNotify.class);

    private String mqUrl="tcp://localhost:61616";
    private String dbUrl="",dbDriver,dbUser,dbPass;
    private BasicDataSource ds;
    public long timeToLive=45*1000;
    ActiveMQConnectionFactory connectionFactory;
    Connection connection;
    Gson gson;

    PropertiesConfiguration configuration=null;

    @Override
    public void init() throws ServletException {
        super.init();    //To change body of overridden methods use File | Settings | File Templates.
        logger.debug("In init()");
        gson = new Gson();
        try {
            configuration=new PropertiesConfiguration("WebNotify.properties");
        } catch (ConfigurationException e) {
            logger.error("Unable to load configuration",e);
            return;
        }

        mqUrl=configuration.getString("mq.url",mqUrl);
        dbUrl=configuration.getString("db.url");
        dbUser=configuration.getString("db.user");
        dbPass=configuration.getString("db.pass");
        dbDriver=configuration.getString("db.driver");

        try {
            ds = new BasicDataSource();
            Class.forName(dbDriver);
            ds.setUsername(dbUser);
            ds.setPassword(dbPass);
            ds.setUrl(dbUrl);
            ds.setTestOnBorrow(configuration.getBoolean("db.test.on.borrow",true));
            ds.setValidationQuery(configuration.getString("db.validation.query","select 1 from dual"));
            ds.setInitialSize(configuration.getInt("db.initial.size", 0));
            ds.setMaxTotal(configuration.getInt("db.max.active",8));
            ds.setMaxIdle(configuration.getInt("db.max.idle",8));
            ds.setMinIdle(configuration.getInt("db.min.idle",0));
            ds.setTestWhileIdle(configuration.getBoolean("db.test.while.idel",true));
            ds.setTimeBetweenEvictionRunsMillis(configuration.getLong("db.time.between.eviction.runs.milliseconds",5*60*1000));
            ds.setMaxWaitMillis(configuration.getLong("db.max.wait.milliseconds",15000));
            ds.setValidationQueryTimeout(configuration.getInt("db.validation.query.timeout.seconds",5));
        } catch (ClassNotFoundException e) {
            logger.error("Unable to load DB Driver "+e.toString());
        }

        timeToLive=configuration.getLong("time.to.live",timeToLive);

        connectionFactory = new ActiveMQConnectionFactory(mqUrl);

        try {
            connection = connectionFactory.createConnection();
            connection.start();
        } catch (JMSException e) {
            logger.error("JMS Exception",e);
            logger.error(e.toString());
            connection=null;
        }
    }

    @Override
    public void destroy() {
        super.destroy();
        logger.debug("in destroy");
        try {
            if (connection!=null) {
                connection.stop();
                connection.close();
                connection=null;
            }
        } catch (JMSException e) {
            logger.error("Unable to stop and close connection",e);
        }
    }

    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        doPost(req, resp);
    }

    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        if (configuration!=null) {
            try {
                configuration.refresh();
            } catch (ConfigurationException e) {
                logger.error("Unable to reload Configuration",e);
            }
        }

        BufferedReader in = req.getReader();

        StringBuilder buffer = new StringBuilder();
        String line;
        while((line = in.readLine()) != null){
            buffer.append(line);
        }
        String input = buffer.toString();
        logger.debug("Received "+input);

        Map<String,String> requests=Utils.splitQuery(input);

        String ref=requests.get("out_trade_no");
        logger.debug("ref:"+ref);
        String amt=requests.get("total_fee");
        if (amt!=null) {
            amt=amt.replace(".","");
        } else {
            amt="-";
        }
        String tradeStatus=requests.get("trade_status");

        QRMessage message=new QRMessage(ref, input);


        if  (connection==null) {
            logger.debug("MQ connection null, restarting");
            connectionFactory = new ActiveMQConnectionFactory(mqUrl);

            try {
                logger.debug("Connecting to MQ "+input);
                connection = connectionFactory.createConnection();
                connection.start();
            } catch (JMSException e) {
                logger.error("JMS Exception",e);
                logger.error(e.toString());
                connection=null;
            }
        }

        // after logging, parse and send to queue
        try {
            if (connection!=null) {
                // Create a Session
                Session session = connection.createSession(false, Session.AUTO_ACKNOWLEDGE);

                // Create the destination (Topic or Queue)
                Destination destination = session.createTopic(configuration.getString("mq.alipay.topic", "alipay.notify"));

                // Create a MessageProducer from the Session to the Topic or Queue
                MessageProducer producer = session.createProducer(destination);
                producer.setDeliveryMode(DeliveryMode.PERSISTENT);
                producer.setTimeToLive(timeToLive);

                // Create a messages
                logger.debug("Sending message "+gson.toJson(message));
                TextMessage msg=session.createTextMessage(gson.toJson(message));
                producer.send(msg);

                logger.debug("Sent message for ref:"+ref);

                // Clean up
                session.close();
            } else {
                logger.debug("Connection is null!");
            }

            //connection.close();
        } catch (Exception e) {
            logger.error("Exception sending to MQ");
            if (connection!=null) {
                try {
                    connection.close();
                } catch (JMSException e1) {
                    logger.error("Attempt to close mq",e1);
                }
                connection=null;
            }

        }
        //resp.setContentType("application/json");
        logger.debug("Reply success for ref:"+ref);
        PrintWriter out = resp.getWriter();
        out.write("success");
        out.flush();

        // Log in DB the request received
        logToDB(input, ref, amt, tradeStatus);
        logger.debug("Finish log to DB for ref:"+ref);

    }

    private void logToDB(String input, String refStr, String amt, String status) {
        logger.debug("Logging to DB: "+refStr+" - "+input);

        AlipayHostTxnLog alipayHostTxnLog=new AlipayHostTxnLog();
        alipayHostTxnLog.setMessageType("notify");

        alipayHostTxnLog.setAlipayReferenceNumber(refStr);
        alipayHostTxnLog.setAmount(amt);
        alipayHostTxnLog.setVehicleId("");
        alipayHostTxnLog.setJobId("");
        alipayHostTxnLog.setDriverId("");
        alipayHostTxnLog.setSerialNo("");
        //alipayHostTxnLog.setTransDateTime(com.abl.qr.utils.Utils.format(new Date(),"ddMMyy HHmmss"));
        alipayHostTxnLog.setAlipayRequestMessage("");
        alipayHostTxnLog.setAlipayResponseMessage(input);
        alipayHostTxnLog.setAlipayResponseResult(status);
        alipayHostTxnLog.setTransmitStatus("OK");

        DBUtils.logAlipayHostTxnLog(ds, alipayHostTxnLog);

    }

}
