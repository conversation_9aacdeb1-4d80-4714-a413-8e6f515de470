package com.abl.qr.web;

import org.apache.commons.configuration.ConfigurationException;
import org.apache.commons.configuration.PropertiesConfiguration;
import org.apache.log4j.Logger;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Enumeration;

/**
 * Created by <PERSON><PERSON>tzewei on 12/5/17.
 */
public class LogNotify extends HttpServlet {
    public static Logger logger=Logger.getLogger(LogNotify.class);
    PropertiesConfiguration configuration=null;

    @Override
    public void init() throws ServletException {
        super.init();    //To change body of overridden methods use File | Settings | File Templates.
        logger.debug("In init()");

        try {
            configuration=new PropertiesConfiguration("WebNotify.properties");
            //configuration.setReloadingStrategy(new FileChangedReloadingStrategy());
        } catch (ConfigurationException e) {
            logger.error("Unable to load configuration",e);
            return;
        }

        logger.debug("mq.url"+configuration.getString("mq.url"));
    }

    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        doPost(req,resp);
    }

    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        if (configuration!=null) {
            try {
                configuration.refresh();
            } catch (ConfigurationException e) {
                logger.error("Unable to reload Configuration",e);
            }
        }

        logger.debug("QueryString: "+req.getQueryString());
        try {
            String uri = req.getRequestURI();
            logger.debug("Requested Resource::"+uri);

            Enumeration<String> enumeration = req.getParameterNames();

            while(enumeration.hasMoreElements()) {
                String parametername = enumeration.nextElement();
                logger.debug(parametername + " : " +req.getParameter(parametername));
            }
        } catch (Exception e) {
            logger.error(e,e);
        }

        PrintWriter out = resp.getWriter();
        out.write("success");

        /*try {
            if (configuration!=null)
                configuration.save(out);
        } catch (ConfigurationException e) {
            e.printStackTrace();
        }*/

    }

}
