version '1.0'

apply plugin: 'java'
apply plugin: 'idea'
apply plugin: 'application'
apply plugin: 'war'

defaultTasks 'clean','jar'

mainClassName = ""

println "env=" + env
println "rootDir=" + rootDir
println "projectDir=" + projectDir
println "resources=" + sourceSets.main.resources

ext.tokens = new Properties()

def propFile = new File("$projectDir/${env}.properties")
if (propFile.canRead()) {
    tokens.load(new FileInputStream(propFile))
    println tokens
}

sourceCompatibility = 1.6

task initSourceFolders { // add << before { to prevent executing during configuration phase
    sourceSets*.java.srcDirs*.each { it.mkdirs() }
    sourceSets*.resources.srcDirs*.each { it.mkdirs() }
}

repositories {
    // maven { url 'http://download.oracle.com/maven' }
    flatDir {
        dirs 'libs'
    }
    mavenCentral()
}

dependencies {
    testCompile group: 'junit', name: 'junit', version: '4.11'

    compile 'log4j:log4j:1.2.16+'
    compile 'commons-configuration:commons-configuration:1.10+'
    // https://mvnrepository.com/artifact/org.apache.activemq/activemq-client
    compile group: 'org.apache.activemq', name: 'activemq-client', version: '5.14.4'
    // https://mvnrepository.com/artifact/javax.servlet/servlet-api
    providedCompile group: 'javax.servlet', name: 'servlet-api', version: '2.5'
    // https://mvnrepository.com/artifact/org.apache.commons/commons-dbcp2
    compile group: 'org.apache.commons', name: 'commons-dbcp2', version: '2.1.1'
    // https://mvnrepository.com/artifact/org.apache.httpcomponents/httpcore
    compile group: 'org.apache.httpcomponents', name: 'httpcore', version: '4.4.6'
    // https://mvnrepository.com/artifact/com.google.code.gson/gson
    compile group: 'com.google.code.gson', name: 'gson', version: '2.8.0'

    compile fileTree(dir: 'libs', include: '*.jar')
}

import org.apache.tools.ant.filters.ReplaceTokens

task copyConf {
    doLast {
        copy {
            from "conf"
            into "$webAppDir/WEB-INF/classes"
            filter ReplaceTokens, tokens: tokens
        }
    }
}

war {
    dependsOn (copyConf, jar)
    archiveName = 'WebNotify.war'
}