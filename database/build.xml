<?xml version="1.0"?>

<project basedir="." default="deploy" name="comfortSG-database">

    <property file="build.properties"/>

    <property name="project.jar" value="${project.name}-${project.version}.jar"/>

    <path id="master.classpath">
        <fileset dir="${repos.dir}">
            <include name="abl/abl-common*.jar"/>
            <include name="hibernate/*.jar"/>
            <include name="log4j/*.jar"/>
            <include name="spring-3.2.9/*.jar"/>
            <include name="spring-security-3.0.7/*.jar"/>
             <include name="commons/*.jar"/>
            <include name="jcs-1.3/*.jar"/>
        </fileset>
    </path>

    <target name="clean">
        <delete includeemptydirs="true" failonerror="false">
            <fileset dir="${build.dir}" includes="**/*" excludes="META-INF/*"/>
        </delete>
        <delete includeemptydirs="true" failonerror="false">
            <fileset dir="${dist.dir}" includes="**/*"/>
        </delete>
    </target>

    <target name="compile">
        <mkdir dir="${build.dir}"/>
        <javac source="${ant.build.javac.source}" target="${ant.build.javac.target}" destdir="${build.dir}" debug="true" includeAntRuntime="false"
               deprecation="false" optimize="false" failonerror="true">
            <src path="${src.dir}"/>
            <classpath refid="master.classpath"/>
        </javac>
    </target>

    <path id="hibernatetool.classpath">
        <fileset dir="${repos.dir}">
            <include name="commons/*.jar"/>
            <include name="hibernate/*.jar"/>
            <include name="hibernate-tools/*.jar"/>
            <include name="log4j/*.jar"/>
            <include name="sl4j-1.6.0/*.jar"/>
            <include name="spring-security-3.0.7/*.jar"/>
        </fileset>
        <pathelement location="${build.dir}"/>
    </path>

    <taskdef name="hibernatetool"
             classname="org.hibernate.tool.ant.HibernateToolTask"
             classpathref="hibernatetool.classpath"/>

    <target name="schemaexport" description="Generate sql script" depends="compile">
        <mkdir dir="${sql.dir}"/>
        <hibernatetool destdir="${sql.dir}">
            <classpath path="${build.dir}"/>
            <!--
               <annotationconfiguration configurationfile="hibernate.cfg.xml" />
               -->
            <!--configuration configurationfile="hibernate.cfg.xml"/-->
            <jpaconfiguration persistenceunit="p1"/>
            <hbm2ddl export="false" drop="false" create="true"
                     outputfilename="${sql.file}" delimiter=";" format="true"/>
        </hibernatetool>
    </target>

    <target name="hibernatedoc" depends="build" description="Generates hibernate java doc">
        <hibernatetool destdir="${doc.dir}/schema">
            <classpath path="${build.dir}"/>
            <annotationconfiguration configurationfile="${conf.dir}/${hibernate.config}"/>
            <hbm2doc/>
        </hibernatetool>
    </target>

    <target name="build" depends="compile">
        <mkdir dir="${dist.dir}"/>
        <tstamp>
            <format property="today" pattern="dd-MMM-yyyy HH:mm:ss"/>
        </tstamp>
        <jar basedir="${build.dir}"
             destfile="${dist.dir}/${project.jar}"
             excludes="*.jar">
            <manifest>
                <attribute name="Built-By" value="${user.name}"/>
                <attribute name="Built-Date" value="${today}"/>
                <attribute name="Implementation-Version" value="${project.version}"/>
            </manifest>
        </jar>
    </target>

    <target name="deploy" depends="build, schemaexport">
        <copy todir="${deploy.dir}">
            <fileset dir="${sql.dir}">
                <include name="*.sql"/>
            </fileset>
        </copy>
        <copy todir="${repos.dir}/abl">
            <fileset dir="${dist.dir}">
                <include name="${project.jar}"/>
            </fileset>
        </copy>
    </target>

</project>
