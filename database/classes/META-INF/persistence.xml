<persistence xmlns="http://java.sun.com/xml/ns/persistence"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xsi:schemaLocation="http://java.sun.com/xml/ns/persistence http://java.sun.com/xml/ns/persistence/persistence_2_0.xsd"
             version="2.0">
    <persistence-unit name="p1">
        <jta-data-source>java:/DefaultDS</jta-data-source>
        <properties>
            <!-- <property name="hibernate.dialect" value="org.hibernate.dialect.Oracle10gDialect"/>-->
            <!-- <property name="hibernate.dialect" value="org.hibernate.dialect.Oracle10gDialect"/>-->
            <property name="hibernate.dialect" value="org.hibernate.dialect.MySQLDialect"/>
            <property name="hibernate.hbm2ddl.auto" value="create-drop"/>
        </properties>
    </persistence-unit>
</persistence>

