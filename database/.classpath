<?xml version="1.0" encoding="UTF-8"?>
<classpath>
	<classpathentry kind="src" path="src"/>
	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER"/>
	<classpathentry kind="lib" path="C:/Source/Comfort Castles TMS/castle-tms_214/lib/commons/commons-beanutils-1.8.3.jar"/>
	<classpathentry kind="lib" path="C:/Source/Comfort Castles TMS/castle-tms_214/lib/commons/commons-cli-1.2.jar"/>
	<classpathentry kind="lib" path="C:/Source/Comfort Castles TMS/castle-tms_214/lib/commons/commons-codec-1.4.jar"/>
	<classpathentry kind="lib" path="C:/Source/Comfort Castles TMS/castle-tms_214/lib/commons/commons-collections-3.2.1.jar"/>
	<classpathentry kind="lib" path="C:/Source/Comfort Castles TMS/castle-tms_214/lib/commons/commons-configuration-1.7.jar"/>
	<classpathentry kind="lib" path="C:/Source/Comfort Castles TMS/castle-tms_214/lib/commons/commons-dbcp-1.4.jar"/>
	<classpathentry kind="lib" path="C:/Source/Comfort Castles TMS/castle-tms_214/lib/commons/commons-digester-2.1.jar"/>
	<classpathentry kind="lib" path="C:/Source/Comfort Castles TMS/castle-tms_214/lib/commons/commons-fileupload-1.1.1.jar"/>
	<classpathentry kind="lib" path="C:/Source/Comfort Castles TMS/castle-tms_214/lib/commons/commons-io-2.0.1.jar"/>
	<classpathentry kind="lib" path="C:/Source/Comfort Castles TMS/castle-tms_214/lib/commons/commons-lang-2.6.jar"/>
	<classpathentry kind="lib" path="C:/Source/Comfort Castles TMS/castle-tms_214/lib/commons/commons-logging-1.1.1.jar"/>
	<classpathentry kind="lib" path="C:/Source/Comfort Castles TMS/castle-tms_214/lib/commons/commons-pool-1.5.5.jar"/>
	<classpathentry kind="lib" path="C:/Source/Comfort Castles TMS/castle-tms_214/lib/hibernate/antlr-2.7.6.jar"/>
	<classpathentry kind="lib" path="C:/Source/Comfort Castles TMS/castle-tms_214/lib/hibernate/cglib-2.2.jar"/>
	<classpathentry kind="lib" path="C:/Source/Comfort Castles TMS/castle-tms_214/lib/hibernate/dom4j-1.6.1.jar"/>
	<classpathentry kind="lib" path="C:/Source/Comfort Castles TMS/castle-tms_214/lib/hibernate/hibernate3.jar"/>
	<classpathentry kind="lib" path="C:/Source/Comfort Castles TMS/castle-tms_214/lib/hibernate/hibernate-jpa-2.0-api-1.0.0.Final.jar"/>
	<classpathentry kind="lib" path="C:/Source/Comfort Castles TMS/castle-tms_214/lib/hibernate/javassist-3.9.0.GA.jar"/>
	<classpathentry kind="lib" path="C:/Source/Comfort Castles TMS/castle-tms_214/lib/hibernate/jta-1.1.jar"/>
	<classpathentry kind="lib" path="C:/Source/Comfort Castles TMS/castle-tms_214/lib/hibernate-tools/bsh-2.0b1.jar"/>
	<classpathentry kind="lib" path="C:/Source/Comfort Castles TMS/castle-tms_214/lib/hibernate-tools/freemarker.jar"/>
	<classpathentry kind="lib" path="C:/Source/Comfort Castles TMS/castle-tms_214/lib/hibernate-tools/hibernate-tools.jar"/>
	<classpathentry kind="lib" path="C:/Source/Comfort Castles TMS/castle-tms_214/lib/hibernate-tools/jtidy-r8-20060801.jar"/>
	<classpathentry kind="lib" path="C:/Source/Comfort Castles TMS/castle-tms_214/lib/log4j/log4j-1.2.16.jar"/>
	<classpathentry kind="lib" path="C:/Source/Comfort Castles TMS/castle-tms_214/lib/sl4j-1.6.0/slf4j-api-1.6.0.jar"/>
	<classpathentry kind="lib" path="C:/Source/Comfort Castles TMS/castle-tms_214/lib/sl4j-1.6.0/slf4j-log4j12-1.6.0.jar"/>
	<classpathentry kind="lib" path="C:/Source/Comfort Castles TMS/castle-tms_214/lib/spring-security-3.0.7/spring-security-acl-3.0.7.RELEASE.jar"/>
	<classpathentry kind="lib" path="C:/Source/Comfort Castles TMS/castle-tms_214/lib/spring-security-3.0.7/spring-security-aspects-3.0.7.RELEASE.jar"/>
	<classpathentry kind="lib" path="C:/Source/Comfort Castles TMS/castle-tms_214/lib/spring-security-3.0.7/spring-security-cas-client-3.0.7.RELEASE.jar"/>
	<classpathentry kind="lib" path="C:/Source/Comfort Castles TMS/castle-tms_214/lib/spring-security-3.0.7/spring-security-config-3.0.7.RELEASE.jar"/>
	<classpathentry kind="lib" path="C:/Source/Comfort Castles TMS/castle-tms_214/lib/spring-security-3.0.7/spring-security-core-3.0.7.RELEASE.jar"/>
	<classpathentry kind="lib" path="C:/Source/Comfort Castles TMS/castle-tms_214/lib/spring-security-3.0.7/spring-security-ldap-3.0.7.RELEASE.jar"/>
	<classpathentry kind="lib" path="C:/Source/Comfort Castles TMS/castle-tms_214/lib/spring-security-3.0.7/spring-security-openid-3.0.7.RELEASE.jar"/>
	<classpathentry kind="lib" path="C:/Source/Comfort Castles TMS/castle-tms_214/lib/spring-security-3.0.7/spring-security-taglibs-3.0.7.RELEASE.jar"/>
	<classpathentry kind="lib" path="C:/Source/Comfort Castles TMS/castle-tms_214/lib/spring-security-3.0.7/spring-security-web-3.0.7.RELEASE.jar"/>
	<classpathentry kind="output" path="bin"/>
</classpath>
