------------------------------------------------------------------------------------------------
SET echo OFF
SET timing OFF
SET heading OFF
SET feedback OFF
SET serveroutput ON size 100000
SET define ON 
column dt new_value new_dt 
column ext new_value new_ext

SELECT  'TMS_DDL_1_20210915.sql_' || sys_context('userenv','instance_name') || '_' || to_char(sysdate,'YYYYMMDD_HH24MISS') dt,'.txt' ext FROM dual;
SPOOL  &new_dt.&new_ext

SELECT 'Running at ' || sys_context('userenv', 'host') || '[' || sys_context('userenv', 'ip_address') || ']' || '@' || sys_context('userenv', 'os_user') 
       || ' as ' || sys_context('userenv', 'current_user') || '@' || sys_context('userenv', 'db_unique_name') as where_run
FROM dual;

SELECT 'Run start: ' || to_char(sysdate,'dd/mm/yyyy hh24:mi:ss') as script_run_start
FROM dual;

SET linesize 10000
SET pages 5000
SET feedback ON
SET timing ON
SET heading ON
SET echo ON
SET define OFF

------------------------------------------------------------------------------------------------

ALTER SESSION SET current_schema = cn2tmsys;

------------------------------------------------------------------------------------------------


-- increase data len for TMTB_RELEASE_FILE to support larger size download
ALTER TABLE TMTB_RELEASE_FILE
	MODIFY data varchar2(4000 char);
	
------------------------------------------------------------------------------------------------

SET echo OFF
SET timing OFF
SET heading OFF
SET feedback OFF
SELECT 'Run end: ' || TO_CHAR(sysdate,'dd/mm/yyyy hh24:mi:ss') AS script_run_end
FROM dual;
spool OFF
SET echo ON
SET timing ON
SET heading ON
SET feedback ON


