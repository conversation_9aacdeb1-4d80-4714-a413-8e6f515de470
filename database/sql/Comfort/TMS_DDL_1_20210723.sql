------------------------------------------------------------------------------------------------
SET echo OFF
SET timing OFF
SET heading OFF
SET feedback OFF
SET serveroutput ON size 100000
SET define ON 
column dt new_value new_dt 
column ext new_value new_ext

SELECT  'TMS_DDL_1_20210723.sql_' || sys_context('userenv','instance_name') || '_' || to_char(sysdate,'YYYYMMDD_HH24MISS') dt,'.txt' ext FROM dual; 
SPOOL  &new_dt.&new_ext

SELECT 'Running at ' || sys_context('userenv', 'host') || '[' || sys_context('userenv', 'ip_address') || ']' || '@' || sys_context('userenv', 'os_user') 
       || ' as ' || sys_context('userenv', 'current_user') || '@' || sys_context('userenv', 'db_unique_name') as where_run
FROM dual;

SELECT 'Run start: ' || to_char(sysdate,'dd/mm/yyyy hh24:mi:ss') as script_run_start
FROM dual;

SET linesize 10000
SET pages 5000
SET feedback ON
SET timing ON
SET heading ON
SET echo ON
SET define OFF

------------------------------------------------------------------------------------------------

ALTER SESSION SET current_schema = cn2tmsys;

------------------------------------------------------------------------------------------------

-- new table TMTB_STUCK_TRANSACTION to store uploaded stuck transaction from payment terminal
CREATE TABLE TMTB_STUCK_TRANSACTION (
	id number(19,0) not null,
	admin_fee number(19,0),
	card_no varchar2(20 char),
	driver_id varchar2(9 char),
	fare number(19,0),
	gst number(19,0),
	job_no varchar2(12 char),
	mid varchar2(15 char),
	release_ver varchar2(6 char),
	rrn varchar2(20 char),
	state varchar2(16 char) not null,
	tid varchar2(8 char),
	trigger_by varchar2(30 char),
	txn_amt number(19,0),
	txn_date_time timestamp,
	txn_type varchar2(16 char) not null,
	upload_date_time timestamp,
	terminal_id number(19,0) not null,
	PRIMARY KEY (id)
);

ALTER TABLE TMTB_STUCK_TRANSACTION
	ADD CONSTRAINT TMFC_STUCK_TXN_TERMINAL
	FOREIGN KEY (terminal_id)
	REFERENCES TMTB_TMS_TERMINAL;

COMMENT ON TABLE TMTB_STUCK_TRANSACTION IS 'Stores the uploaded transactions stuck in the payment terminal.';

COMMENT ON column TMTB_STUCK_TRANSACTION.admin_fee is 'Admin fee';
COMMENT ON column TMTB_STUCK_TRANSACTION.card_no is 'Card number first 6 last 4';
COMMENT ON column TMTB_STUCK_TRANSACTION.driver_id is 'Driver id';
COMMENT ON column TMTB_STUCK_TRANSACTION.fare is 'Fare amount';
COMMENT ON column TMTB_STUCK_TRANSACTION.gst is 'GST';
COMMENT ON column TMTB_STUCK_TRANSACTION.job_no is 'job number';
COMMENT ON column TMTB_STUCK_TRANSACTION.mid is 'MID';
COMMENT ON column TMTB_STUCK_TRANSACTION.release_ver is 'Release version on payment terminal';
COMMENT ON column TMTB_STUCK_TRANSACTION.rrn is 'Retrival Reference Number or Approval Code if availabile';
COMMENT ON column TMTB_STUCK_TRANSACTION.state is 'State of this stuck transaction, Stuck, Pending, Auto Cleared, Cleared';
COMMENT ON column TMTB_STUCK_TRANSACTION.tid is 'TID';
COMMENT ON column TMTB_STUCK_TRANSACTION.trigger_by is 'The admin id that triggered the clearing of stuck transaction';
COMMENT ON column TMTB_STUCK_TRANSACTION.txn_amt is 'Transaction amount';
COMMENT ON column TMTB_STUCK_TRANSACTION.txn_date_time is 'Date time when this transaction being logged';
COMMENT ON column TMTB_STUCK_TRANSACTION.txn_type is 'Type of stuck transaction';
COMMENT ON column TMTB_STUCK_TRANSACTION.upload_date_time is 'Date time when this transaction is uploaded';
COMMENT ON column TMTB_STUCK_TRANSACTION.terminal_id is 'ID representing the terminal uploading the transaction';

-- index on foreign key
CREATE INDEX TMIX_STUCK_TXN__TERMINAL_ID on TMTB_STUCK_TRANSACTION (terminal_id);

-- Sequences for stuck transaction id
CREATE SEQUENCE TMSQ_STUCK_TRANSACTION;

-- Add new TMTB_APP_DOWNLOAD_JOB_INFO table
CREATE TABLE TMTB_APP_DOWNLOAD_JOB_INFO (
	start_date date,
	application_download_job_id number(19,0) not null,
	PRIMARY KEY (application_download_job_id),
	UNIQUE (application_download_job_id)
);

ALTER TABLE TMTB_APP_DOWNLOAD_JOB_INFO
	ADD CONSTRAINT TMFC_APP_DOWNLOAD_JOB_INFO
	FOREIGN KEY (application_download_job_id)
	REFERENCES TMTB_APPLICATION_DOWNLOAD_JOB;

COMMENT ON TABLE TMTB_APP_DOWNLOAD_JOB_INFO IS 'Storing of start date for jobs to minimise impact on old application.';

COMMENT ON column TMTB_APP_DOWNLOAD_JOB_INFO.start_date is 'Date set by admin for the download job to start';

------------------------------------------------------------------------------------------------

SET echo OFF
SET timing OFF
SET heading OFF
SET feedback OFF
SELECT 'Run end: ' || TO_CHAR(sysdate,'dd/mm/yyyy hh24:mi:ss') AS script_run_end
FROM dual;
spool OFF
SET echo ON
SET timing ON
SET heading ON
SET feedback ON


