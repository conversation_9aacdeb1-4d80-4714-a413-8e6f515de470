-- Update view to include start date field
CREATE OR REPLACE VIEW TMVW_TERM_APP_DOWNLOAD_SUMMARY AS
SELECT
     Row_Number() OVER(ORDER BY job_id ASC) as id,
     A."JOB_ID",A."START_WINDOW",A."END_WINDOW",A."CONCURRENT_DOWNLOAD",A."CREATION_DATE",A."RELEASE_ID",A."DESCRIPTION",A."VERSION",A."MODEL_ID",A."MODEL_NAME",A."JOB_STATUS",A."GROUP_ID",A."COMPLETED",A."PENDING",A."NOT_STARTED",A."DELETED",A."START_DATE"
FROM
	(SELECT DISTINCT
		adj.id as job_id,
		adj.start_window,
		adj.end_window,
		adj.concurrent_download,
		adj.create_date_time as creation_date,
		r.id as release_id,
		r.description as description,
		r.version as version,
		m.id as model_id,
		m.model_name as model_name,
		adj.status as job_status,
		g.id as group_id,
       		(CASE
			WHEN (SELECT COUNT(DISTINCT ta.terminal_id)
          			FROM
					TMVW_TERM_APP_LATEST ta,
					tmtb_tms_terminal t,
					tmtb_release r2,
					tmtb_grouping g2,
					tmtb_vehicle_group vg
          			WHERE
					ta.release_id  = r2.id AND
					r2.id  = g2.release_id AND
					g2.id  = g.id AND
					ta.terminal_id  = t.id AND
					t.vehicle_no  = vg.vehicle_id AND
					vg.group_id  = g2.id) IS NULL THEN 0
          		ELSE (SELECT COUNT(DISTINCT ta.terminal_id)
          			FROM
					TMVW_TERM_APP_LATEST ta,
          				tmtb_tms_terminal t,
					tmtb_release r2,
					tmtb_grouping g2,
					tmtb_vehicle_group vg
          			WHERE
					ta.release_id  = r2.id AND
					r2.id  = g2.release_id AND
					g2.id  = g.id AND
					ta.terminal_id  = t.id AND
					t.vehicle_no  = vg.vehicle_id AND
					vg.group_id  = g2.id AND
					t.vehicle_unpaired=0)
			END) as completed,
          	(CASE
			WHEN (SELECT COUNT(*)
				FROM
					tmtb_term_app_download_job d,
	 				tmtb_grouping g2
				WHERE
					d.application_download_job_id  = adj.id AND
					d.status  = 1 AND
					d.release_id  = r.id AND
					r.deleted  = 0 AND
					r.id  = g2.release_id AND
					g2.id  = g.id) IS NULL THEN 0
			ELSE (SELECT COUNT(*)
				FROM
					tmtb_term_app_download_job d,
	 				tmtb_grouping g2
				WHERE
					d.application_download_job_id  = adj.id AND
					d.status  = 1 AND
					d.release_id  = r.id AND
					r.deleted  = 0 AND
					r.id  = g2.release_id AND
					g2.id  = g.id)
			END) as pending,
		(CASE
			WHEN (SELECT COUNT(*)
				FROM  tmvw_terminal_not_downloaded td
				WHERE td.group_id  = adj.group_id) IS NULL THEN 0
			ELSE (SELECT COUNT(*)
				FROM  tmvw_terminal_not_downloaded td
				WHERE td.group_id  = adj.group_id
				)
			END) as not_started,
		adj.deleted as deleted,
		adji.start_date
	FROM
		tmtb_application_download_job adj,
		tmtb_app_download_job_info adji,
		tmtb_grouping g,
		tmtb_release r,
		tmtb_release_tmtb_application ra,
		tmtb_application app,
		tmtb_release_tmtb_model rm,
		tmtb_model m,
		tmtb_term_app_download_job tadj
	WHERE
		adj.group_id  = g.id AND
		g.release_id  = r.id AND
		r.id  = ra.tmtb_release_id AND
		ra.application_id  = app.id AND
		g.release_id  = rm.tmtb_release_id AND
		rm.model_id  = m.id AND
		tadj.application_download_job_id (+) = adj.id AND
		adji.application_download_job_id (+) = adj.id
	) A;

