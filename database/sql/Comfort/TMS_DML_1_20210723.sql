------------------------------------------------------------------------------------------------
SET echo OFF
SET timing OFF
SET heading OFF
SET feedback OFF
SET serveroutput ON size 100000
SET define ON 
column dt new_value new_dt 
column ext new_value new_ext

SELECT  'TMS_DML_1_20210723.sql_' || sys_context('userenv','instance_name') || '_' || to_char(sysdate,'YYYYMMDD_HH24MISS') dt,'.txt' ext FROM dual; 
SPOOL  &new_dt.&new_ext

SELECT 'Running at ' || sys_context('userenv', 'host') || '[' || sys_context('userenv', 'ip_address') || ']' || '@' || sys_context('userenv', 'os_user') 
       || ' as ' || sys_context('userenv', 'current_user') || '@' || sys_context('userenv', 'db_unique_name') as where_run
FROM dual;

SELECT 'Run start: ' || to_char(sysdate,'dd/mm/yyyy hh24:mi:ss') as script_run_start
FROM dual;

SET linesize 10000
SET pages 5000
SET feedback ON
SET timing ON
SET heading ON
SET echo ON
SET define OFF

------------------------------------------------------------------------------------------------

ALTER SESSION SET current_schema = cn2tmsys;

------------------------------------------------------------------------------------------------

-- Add in the new terminal model
INSERT INTO TMTB_MODEL(id, create_date_time, deleted, model_name, description) VALUES (tmsq_model.nextval,sysdate, 0, 'S1000E', 'Castle S1E');

-- Add in new page for castle tms to parse log
INSERT INTO TMTB_PAGE(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) VALUES ('terminal.parseTerminalLog', 'Parse Terminal Log', '/terminal/parseTerminalLog.html', 0, 0, 0, '3.05', 1, 'terminal', 'Device');

-- Add in new page for castle tms to view stuck transaction
INSERT INTO TMTB_PAGE(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) VALUES ('terminal.listStuckTransactions', 'Stuck Transactions Management', '/terminal/listStuckTransactions.html', 0, 0, 0, '3.06', 1, 'terminal', 'Device');

------------------------------------------------------------------------------------------------

SET echo OFF
SET timing OFF
SET heading OFF
SET feedback OFF
SELECT 'Run end: ' || TO_CHAR(sysdate,'dd/mm/yyyy hh24:mi:ss') AS script_run_end
FROM dual;
spool OFF
SET echo ON
SET timing ON
SET heading ON
SET feedback ON


