insert into tmtb_module(name, label, url, display_order, module_type, visible)
values ('system', 'System', '/', '0.0', 1, 0);
insert into tmtb_module(name, label, url, display_order, module_type, visible)
values ('admin', 'Users Admin', '/admin', '1.0', 2, 1);
insert into tmtb_module(name, label, url, display_order, module_type, visible)
values ('terminal', 'Device Admin', '/terminal', '2.0', 0, 1);
insert into tmtb_module(name, label, url, display_order, module_type, visible)
values ('report', 'Reports', '/report', '3.0', 0, 1);
insert into tmtb_module(name, label, url, display_order, module_type, visible)
values ('auditTrail', 'Audit Trail', '/auditTrail', '4.0', 0, 1);
insert into tmtb_module(name, label, url, display_order, module_type, visible)
values ('dash', 'Dash', '/dash', '5.0', 0, 1);
insert into tmtb_module(name, label, url, display_order, module_type, visible)
values ('alipay', 'Alipay', '/alipay', '6.0', 0, 1);

insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('system.login', 'Login', '/login.html', 1, 1, 0, '0.00', 0, 'system', null);
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('system.main', 'Main', '/main.html', 1, 1, 0, '0.01', 1, 'system', null);

insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('admin.listAccessProfiles', 'List Access Profiles', '/admin/listAccessProfiles.html', 0, 1, 0, '1.08', 1, 'admin', null);
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name,group_name) 
values ('admin.addAccessProfile', 'Add Access Profile', '/admin/addAccessProfile.html', 0, 1, 0, '1.09', 1, 'admin', null);
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name,group_name) 
values ('admin.editAccessProfile', 'Edit Access Profile', '/admin/editAccessProfile.html', 0, 1, 0, '1.10', 0, 'admin', null);
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name,group_name) 
values ('admin.viewAccessProfile', 'View Access Profile', '/admin/viewAccessProfile.html', 0, 1, 0, '1.11', 0, 'admin', null);
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name,group_name) 
values ('admin.deleteAccessProfile', 'Delete Access Profile', '/admin/deleteAccessProfile.html', 0, 1, 0, '1.12', 0, 'admin', null);

insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name,group_name) 
values ('admin.viewAdminLogs', 'View Admin Logs', '/admin/viewAdminLogs.html', 0, 1, 0, '1.18', 1, 'admin', null);
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name,group_name) 
values ('admin.viewAdminLogDetails', 'View Admin Log Details', '/admin/viewAdminLogDetails.html', 0, 1, 0, '1.19', 0, 'admin', null);

insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('terminal.listTerminals', 'Device Management', '/terminal/listTerminals.html', 0, 0, 0, '3.01', 1, 'terminal', 'Device');
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('terminal.UpdateUploadLog', 'Update Terminal', '/terminal/updateUploadLog.html', 0, 0, 0, '3.02', 0, 'terminal', 'Device');
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('terminal.viewTerminalLogs', 'View Terminal Logs', '/terminal/viewTerminalLogs.html', 0, 0, 0, '3.03', 0, 'terminal', 'Device');
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('terminal.viewTerminalApplication', 'View Terminal Applications Info', '/terminal/viewTerminalApplication.html', 0, 0, 0, '3.04', 0, 'terminal', 'Device');
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('terminal.parseTerminalLog', 'Parse Terminal Log', '/terminal/parseTerminalLog.html', 0, 0, 0, '3.05', 1, 'terminal', 'Device');
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('terminal.listStuckTransactions', 'Stuck Transactions Management', '/terminal/listStuckTransactions.html', 0, 0, 0, '3.06', 1, 'terminal', 'Device');

insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('vehicle.vehicleGroupingSummary', 'Vehicle Grouping Summary', '/vehicle/vehicleGroupingSummary.html', 0, 0, 0, '1.01', 1, 'terminal', 'Vehicle Grouping');
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('vehicle.deleteVehicleGroupingSummary', 'Delete Vehicle Grouping Summary', '/vehicle/deleteVehicleGroupingSummary.html', 0, 0, 0, '1.02', 0, 'terminal', 'Vehicle Grouping');
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('vehicle.assignReleasePackage', 'Assign Release Package', '/vehicle/assignReleasePackage.html', 0, 0, 0, '1.03', 0, 'terminal', 'Vehicle Grouping');
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('vehicle.downloadJobSummary', 'Download Job Summary (Grouping)', '/vehicle/downloadJobSummary.html', 0, 0, 0, '1.04', 0, 'terminal', 'Vehicle Grouping');
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('vehicle.resumeDownloadJob', 'Resume Download Job Summary', '/vehicle/resumeDownloadJob.html', 0, 0, 0, '1.05', 0, 'terminal', 'Vehicle Grouping');
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('vehicle.suspendDownloadJob', 'Suspend Download Job Summary', '/vehicle/suspendDownloadJob.html', 0, 0, 0, '1.06', 0, 'terminal', 'Vehicle Grouping');
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('vehicle.deleteDownloadJob', 'Delete Download Job Summary', '/vehicle/deleteDownloadJob.html', 0, 0, 0, '1.07', 0, 'terminal', 'Vehicle Grouping');
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('vehicle.createDownloadJob', 'Schedule Download Job', '/vehicle/createDownloadJob.html', 0, 0, 0, '1.20', 0, 'terminal', 'Vehicle Grouping');
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('vehicle.createVehicleGroup', 'Create Vehicle Group', '/vehicle/createVehicleGroup.html', 0, 0, 0, '1.011', 1, 'terminal', 'Vehicle Grouping');
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('vehicle.resolveDiscrepancies', 'Resolve Discrepancies', '/vehicle/resolveDiscrepancies.html', 0, 0, 0, '1.012', 0, 'terminal', 'Vehicle Grouping');
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('vehicle.listVehicles', 'Search Vehicle', '/vehicle/listVehicles.html', 0, 0, 0, '1.013', 1, 'terminal', 'Vehicle Grouping');
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('vehicle.updateVehicles', 'Update Vehicles', '/vehicle/updateVehicles.html', 0, 0, 0, '1.014', 0, 'terminal', 'Vehicle Grouping');
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('vehicle.viewTerminalWithDownloadJob', 'View Terminal Downloading Status', '/vehicle/viewTerminalWithDownloadJob.html', 0, 0, 0, '1.015', 0, 'terminal', 'Vehicle Grouping');
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('vehicle.viewTerminalWithoutDownloadJob', 'View Terminal Downloading Status(Not Started)', '/vehicle/viewTerminalWithoutDownloadJob.html', 0, 0, 0, '1.016', 0, 'terminal', 'Vehicle Grouping');
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('vehicle.viewTerminalWithDownloadedApp', 'View Terminal Downloading Status(Completed)', '/vehicle/viewTerminalWithDownloadedApp.html', 0, 0, 0, '1.017', 0, 'terminal', 'Vehicle Grouping');
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('vehicle.reassignVehicleGrouping', 'Reassign Vehicle Grouping', '/vehicle/reassignVehicleGrouping.html', 0, 0, 0, '1.018', 1, 'terminal', 'Vehicle Grouping');
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('vehicle.reassignVehicleGroupSummary', 'Reassign Vehicle Grouping Summary', '/vehicle/reassignVehicleGroupSummary.html', 0, 0, 0, '1.019', 0, 'terminal', 'Vehicle Grouping');



insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('terminalProfile.terminalProfileSummary', 'Terminal Profile Summary', '/terminalProfile/terminalProfileSummary.html', 0, 0, 0, '5.01', 0, 'terminal', 'Vehicle Grouping');
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('terminalProfile.createTerminalProfile', 'Create Terminal Profile', '/terminalProfile/createTerminalProfile.html', 0, 0, 0, '5.02', 0, 'terminal', 'Vehicle Grouping');
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('terminalProfile.updateTerminalProfile', 'Update Terminal Profile', '/terminalProfile/updateTerminalProfile.html', 0, 0, 0, '5.03', 0, 'terminal', 'Vehicle Grouping');
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('terminalProfile.deleteBin', 'Delete Bin', '/terminalProfile/deleteBin.html', 0, 0, 0, '5.04', 0, 'terminal', 'Vehicle Grouping');
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('terminalProfile.updateBin', 'Update Bin', '/terminalProfile/updateBin.html', 0, 0, 0, '5.05', 0, 'terminal', 'Vehicle Grouping');
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('terminalProfile.deleteFee', 'Delete Fee', '/terminalProfile/deleteFee.html', 0, 0, 0, '5.06', 0, 'terminal', 'Vehicle Grouping');
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('terminalProfile.updateFee', 'Update Fee', '/terminalProfile/updateFee.html', 0, 0, 0, '5.07', 0, 'terminal', 'Vehicle Grouping');

insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('blacklist.scheduleBlacklist', 'Schedule Summary', '/blacklist/scheduleBlacklist.html', 0, 0, 0, '4.01', 1, 'terminal', 'Black List');
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('blacklist.viewBlacklistScheduleSummary', 'View Blacklist Schedule Summary', '/blacklist/viewBlacklistScheduleSummary.html', 0, 0, 0, '4.02', 0, 'terminal', 'Black List');
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('blacklist.deleteBlacklistScheduleSummary', 'Delete Blacklist Schedule Summary', '/blacklist/deleteBlacklistScheduleSummary.html', 0, 0, 0, '4.03', 0, 'terminal', 'Black List');
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('blacklist.addBlacklistJobSummary', 'Blacklist Job Summary', '/blacklist/addBlacklistJobSummary.html', 0, 0, 0, '4.04', 1, 'terminal', 'Black List');
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('blacklist.resumeBlacklistJob', 'Resume Blacklist Job Summary', '/blacklist/resumeBlacklistJob.html', 0, 0, 0, '4.05', 0, 'terminal', 'Black List');
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('blacklist.suspendBlacklistJob', 'Suspend Blacklist Job Summary', '/blacklist/suspendBlacklistJob.html', 0, 0, 0, '4.06', 0, 'terminal', 'Black List');
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('blacklist.deleteBlacklistJob', 'Delete Blacklist Job Summary', '/blacklist/deleteBlacklistJob.html', 0, 0, 0, '4.07', 0, 'terminal', 'Black List');
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('blacklist.viewTerminalBlacklistWithDownloadJob', 'View Blacklist Downloading Job', '/blacklist/viewTerminalBlacklistWithDownloadJob.html', 0, 0, 0, '4.08', 0, 'terminal', 'Black List');
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('blacklist.viewTerminalBlacklistCompletedJob', 'View Blacklist Completed Job', '/blacklist/viewTerminalBlacklistCompletedJob.html', 0, 0, 0, '4.09', 0, 'terminal', 'Black List');
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('blacklist.viewTerminalBlacklistNotDownloaded', 'View Blacklist Not Downloaded', '/blacklist/viewTerminalBlacklistNotDownloaded.html', 0, 0, 0, '4.10', 0, 'terminal', 'Black List');



insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('release.releasePackageSummary', 'Package Summary', '/release/releasePackageSummary.html', 0, 0, 0, '2.01', 1, 'terminal', 'Application Release');
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('release.loadReleasePackage', 'Load Package', '/release/loadReleasePackage.html', 0, 0, 0, '2.02', 1, 'terminal', 'Application Release');
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('release.viewReleasePackage', 'View Release Package', '/release/viewReleasePackage.html', 0, 0, 0, '2.03', 0, 'terminal', 'Application Release');
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('release.deleteReleasePackage', 'Delete Release Package', '/release/deleteReleasePackage.html', 0, 0, 0, '2.04', 0, 'terminal', 'Application Release');
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('release.viewReleasePackageLoadedSuccessful', 'View Release Package Loaded Successful', '/release/viewReleasePackageLoadedSuccessful.html', 0, 0, 0, '2.05', 0, 'terminal', 'Application Release');

insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('report.otaStatusReport', 'OTA Status Report', '/report/otaStatusReport.html', 0, 0, 0, '1.010', 1, 'report', null);
insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('report.failedDownloadVehiclesReport', 'Failed Download Vehicles Report', '/report/failedDownloadVehiclesReport.html', 0, 0, 0, '1.020', 1, 'report', null);
insert into tmtb_page (name,admin_flag, common_flag,display_order,domain_flag,label,url,visible, module_name) 
values ('report.otaStatusReportExportCSV',0,0,'1.0101',0,'OTA Status Export to CSV','/report/otaStatusReportExportCSV.html',0, 'report');
insert into tmtb_page (name,admin_flag, common_flag,display_order,domain_flag,label,url,visible, module_name) 
values ('report.otaStatusReportExportExcel',0,0,'1.0102',0,'OTA Status Export to Excel','/report/otaStatusReportExportExcel.html',0, 'report');
insert into tmtb_page (name,admin_flag, common_flag,display_order,domain_flag,label,url,visible, module_name) 
values ('report.failedDownloadVehiclesReportExportCSV',0,0,'1.0201',0,'Failed Download Vehicles Export to CSV','/report/failedDownloadVehiclesReportExportCSV.html',0, 'report');
insert into tmtb_page (name,admin_flag, common_flag,display_order,domain_flag,label,url,visible, module_name) 
values ('report.failedDownloadVehiclesReportExportExcel',0,0,'1.0202',0,'Failed Download Vehicles Export to Excel','/report/failedDownloadVehiclesReportExportExcel.html',0, 'report');

insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('auditTrail.listAuditTrail', 'Audit Trail', '/auditTrail/listAuditTrail.html', 0, 0, 0, '2.010', 1, 'auditTrail', null);

--insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name)
--values ('dash.queryPinpadDashTxn', 'Query Pinpad Dash Txn', '/dash/queryPinpadDashTxn.html', 0, 0, 0, '3.010', 1, 'dash', null);
--insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name)
--values ('dash.querySingtelAppTxn', 'Query Singtel App Txn', '/dash/querySingtelAppTxn.html', 0, 0, 0, '3.020', 1, 'dash', null);

-- for alipay
--insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name)
--values ('alipay.queryAlipayTxn', 'Query Alipay Txn', '/alipay/queryAlipayTxn.html', 0, 0, 0, '4.010', 1, 'alipay', null);
--insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name)
--values ('alipay.queryAlipayHostTxn', 'Query Alipay Host Txn', '/alipay/queryAlipayHostTxn.html', 0, 0, 0, '4.020', 1, 'alipay', null);

-- for first set up, create a default access profile and related access rights
insert into tmtb_acc_profile (id,create_date, modify_date,name) values (tmsq_access_profile.nextval,sysdate,sysdate,'ABL_ALLACCESS');

insert into tmtb_acc_profile_tmtb_page (tmtb_acc_profile_id, pages_name) values (1,'admin.listAccessProfiles');
insert into tmtb_acc_profile_tmtb_page (tmtb_acc_profile_id, pages_name) values (1,'admin.addAccessProfile');
insert into tmtb_acc_profile_tmtb_page (tmtb_acc_profile_id, pages_name) values (1,'admin.editAccessProfile');
insert into tmtb_acc_profile_tmtb_page (tmtb_acc_profile_id, pages_name) values (1,'admin.viewAccessProfile');
insert into tmtb_acc_profile_tmtb_page (tmtb_acc_profile_id, pages_name) values (1,'admin.deleteAccessProfile');

insert into tmtb_model(id,create_date_time, deleted, model_name, description) values (tmsq_model.nextval,sysdate, 0, 'Vx820', 'Vx820');

insert into tmtb_grouping (id,create_date_time, created_by, deleted, name) values (tmsq_group.nextval,sysdate, 'SYSTEM', 0, 'DEFAULT');

insert into tmtb_txn_type(code, name) values ('0000',   'Unknown');

insert into tmtb_txn_type(code, name) values ('0901',  'Param Version');
insert into tmtb_txn_type(code, name) values ('0902',  'Param Download');
insert into tmtb_txn_type(code, name) values ('0903',  'Application Version');
insert into tmtb_txn_type(code, name) values ('0904',  'File Download');
insert into tmtb_txn_type(code, name) values ('0905',  'Log Upload');
insert into tmtb_txn_type(code, name) values ('0906',  'Blacklist Version');

insert into tmtb_txn_type(code, name) values ('0907',  'Sale');
insert into tmtb_txn_type(code, name) values ('0908',  'Reversal');
insert into tmtb_txn_type(code, name) values ('0909',  'Void');
insert into tmtb_txn_type(code, name) values ('0910',  'Offline Sale');
insert into tmtb_txn_type(code, name) values ('0911',  'Void Reversal');
insert into tmtb_txn_type(code, name) values ('0912',  'Stuck Transactions Upload');


insert into tmtb_txn_result (code, description, response_code) values ('0000', 'APPROVED', '00');
insert into tmtb_txn_result (code, description, response_code) values ('0001', 'ERROR', 'ER');

insert into tmtb_txn_result (code, description, response_code) values ('1000', 'SYSERR', 'SE');
insert into tmtb_txn_result (code, description, response_code) values ('1001', 'SYSERR HSM', 'HE');
insert into tmtb_txn_result (code, description, response_code) values ('1002', 'SYSERR COMM', 'CE');

insert into tmtb_txn_result (code, description, response_code) values ('2001', 'MSG FORMAT ERROR', 'FE');

insert into tmtb_txn_result (code, description, response_code) values ('3001', 'INV TERM', '89');
insert into tmtb_txn_result (code, description, response_code) values ('3002', 'INV MODEL', '07');
insert into tmtb_txn_result (code, description, response_code) values ('3003', 'INV REQ DATA', '86');
insert into tmtb_txn_result (code, description, response_code) values ('3004', 'NO PROFILE', '76');
insert into tmtb_txn_result (code, description, response_code) values ('3005', 'NO DOWNLOAD', '01');
insert into tmtb_txn_result (code, description, response_code) values ('3006', 'APPLICATION NOT FOUND', '02');
insert into tmtb_txn_result (code, description, response_code) values ('3007', 'OUT OF TIME WINDOW', '03');
insert into tmtb_txn_result (code, description, response_code) values ('3008', 'DOWNLOAD JOB SUSPENDED', '01');
insert into tmtb_txn_result (code, description, response_code) values ('3009', 'UPLOAD LOG SUSPENDED', '01');
insert into tmtb_txn_result (code, description, response_code) values ('3010', 'GENERAL TMS ERROR', 'TE');
insert into tmtb_txn_result (code, description, response_code) values ('3011', 'NO GROUP FOUND', '04');
insert into tmtb_txn_result (code, description, response_code) values ('3012', 'PROFILE NOT VALID', '05');
insert into tmtb_txn_result (code, description, response_code) values ('3013', 'MIN VERSION NOT MET', '05');
insert into tmtb_txn_result (code, description, response_code) values ('3014', 'INV VEHICLE', '06');
insert into tmtb_txn_result (code, description, response_code) values ('3015', 'INV FILE NAME', '02');
insert into tmtb_txn_result (code, description, response_code) values ('3016', 'INV FILE VERSION', '05');
insert into tmtb_txn_result (code, description, response_code) values ('3017', 'INV FILE NO', '08');
insert into tmtb_txn_result (code, description, response_code) values ('4001', 'FAILED', 'FA');
insert into tmtb_txn_result (code, description, response_code) values ('4002', 'TIMEOUT', 'TO');
insert into tmtb_txn_result (code, description, response_code) values ('4003', 'HTTP_ERR', 'IE');
insert into tmtb_txn_result (code, description, response_code) values ('4004', 'SYS_MALFUNCTION', '96');
insert into tmtb_txn_result (code, description, response_code) values ('4005', 'ADMIN FEE IS NULL', '11');
insert into tmtb_txn_result (code, description, response_code) values ('4006', 'COMPANY CODE IS NULL', '12');
insert into tmtb_txn_result (code, description, response_code) values ('4007', 'DRIVER ID IS NULL', '13');
insert into tmtb_txn_result (code, description, response_code) values ('4008', 'FARE AMT IS NULL', '14');
insert into tmtb_txn_result (code, description, response_code) values ('4009', 'GST AMT IS NULL', '15');
insert into tmtb_txn_result (code, description, response_code) values ('4010', 'JOB NO IS NULL', '16');
insert into tmtb_txn_result (code, description, response_code) values ('4011', 'TAXI NO IS NULL', '17');
insert into tmtb_txn_result (code, description, response_code) values ('4012', 'TXN AMT IS NULL', '18');
insert into tmtb_txn_result (code, description, response_code) values ('4013', 'DUPLICATE TXN', '94');
insert into tmtb_txn_result (code, description, response_code) values ('4014', 'TXN NOT FOUND', '78');

-- populate old application version
insert into tmtb_application (id,description, name,version) values (tmsq_application.nextval,'Credit Application', 'CREDITAPP', 1);
insert into tmtb_application (id,description, name,version) values (tmsq_application.nextval,'Credit Application', 'CREDITAPP', 2);
insert into tmtb_application (id,description, name,version) values (tmsq_application.nextval,'Credit Application', 'CREDITAPP', 3);
insert into tmtb_application (id,description, name,version) values (tmsq_application.nextval,'Credit Application', 'CREDITAPP', 4);

insert into tmtb_application (id,description, name,version) values (tmsq_application.nextval,'EPINS application', 'EPINSAPP', 1);
insert into tmtb_application (id,description, name,version) values (tmsq_application.nextval,'EPINS application', 'EPINSAPP', 2);
