
    create table BATCHB_VW_EZLINK_DOUBLE_DEBIT (
        id varchar(255) not null,
        amount bigint,
        can varchar(16) not null,
        driverId varchar(255),
        jobNo varchar(255),
        logDT datetime not null,
        ptc varchar(6) not null,
        taxiNo varchar(255),
        transDT datetime not null,
        transactionMessage longtext not null,
        primary key (id)
    );

    create table BATCHTB_EZLINK_TRANSACTIONS (
        id bigint not null auto_increment,
        batchControlfilename varchar(31),
        batchFilename varchar(41),
        batchStatus varchar(2),
        can varchar(16) not null,
        driverInfo varchar(43) not null,
        logDT datetime not null,
        mid varchar(15) not null,
        ptc varchar(6) not null,
        samID varchar(8) not null,
        tid varchar(8) not null,
        transDT datetime not null,
        transactionMessage longtext not null,
        primary key (id)
    );

    create table BATCHTB_LOYALTY_FILE (
        id integer not null auto_increment,
        create_dt datetime not null,
        filename varchar(255) not null,
        last_generated_dt datetime not null,
        primary key (id)
    );

    create table BATCHTB_LOYALTY_TRANSACTIONS (
        id bigint not null auto_increment,
        admin varchar(255),
        booking_channel varchar(255),
        card_no varchar(255),
        complete_time varchar(255),
        company_code varchar(255),
        create_dt datetime not null,
        distance varchar(255),
        fare varchar(255),
        gst varchar(255),
        job_no varchar(255),
        payment_card_no varchar(255),
        payment_mode varchar(255),
        prev_transport_mode varchar(255),
        receipt longblob,
        remarks varchar(255),
        stan varchar(255) not null,
        tid varchar(8) not null,
        trip_end varchar(255),
        trip_no varchar(255),
        trip_start varchar(255),
        vehicle_no varchar(255),
        warn_code varchar(255),
        loyalty_file_id integer,
        primary key (id)
    );

    create table CN2_V_TMS_IVD (
        firmware_ver varchar(3) not null,
        primary key (firmware_ver)
    );

    create table CN2_V_TMS_VEHICLE (
        vehicle_id varchar(20) not null,
        company_id varchar(12) not null,
        credit_app_version varchar(6),
        driver_name varchar(40) not null,
        driver_nric varchar(12) not null,
        driving_mode varchar(6),
        epins_app_version varchar(6),
        ezlink_app_version varchar(6),
        ezlink_sam_serial_no varchar(20),
        firmware_ver varchar(3),
        full_black_list_can varchar(20),
        full_black_list_range varchar(20),
        ivd_model_id varchar(10),
        ivd_no varchar(10),
        master_app_version varchar(6),
        nets_app_version varchar(6),
        phone1 varchar(8),
        phone2 varchar(8),
        pinpad_serial_no varchar(20),
        small_black_list_can varchar(20),
        small_black_list_range varchar(20),
        update_dt datetime,
        vehicle_type varchar(30),
        primary key (vehicle_id)
    );

    create table CN2_V_TMS_VEHICLEMODEL (
        ivd_model_id varchar(10) not null,
        primary key (ivd_model_id)
    );

    create table CN2_V_TMS_VEHICLETYPE (
        veh_typeid bigint not null,
        veh_type varchar(30),
        primary key (veh_typeid)
    );

    create table TMTB_ACC_PROFILE (
        id bigint not null auto_increment,
        create_date datetime,
        modify_date datetime,
        name varchar(255) not null,
        primary key (id)
    );

    create table TMTB_ACC_PROFILE_TMTB_PAGE (
        TMTB_ACC_PROFILE_id bigint not null,
        pages_name varchar(100) not null,
        primary key (TMTB_ACC_PROFILE_id, pages_name)
    );

    create table TMTB_ADMIN_LOG (
        id bigint not null auto_increment,
        log_date_time datetime not null,
        message_code varchar(255),
        message_params blob,
        new_data longtext,
        old_data varchar(255),
        success bit not null,
        user_name varchar(32),
        domain_id bigint,
        page_name varchar(100),
        primary key (id)
    );

    create table TMTB_ADM_USR (
        id bigint not null auto_increment,
        administrator bit not null,
        comments varchar(255),
        create_date_time datetime not null,
        delete_date_time datetime,
        deleted int default 0 not null,
        last_login_date_time datetime,
        login varchar(20) not null unique,
        login_date_time datetime,
        login_fail_count smallint not null,
        login_fail_date_time datetime,
        name varchar(80) not null,
        password varchar(80) not null,
        password_change_date date,
        password_expiry date,
        password_locked bit not null,
        password_reset bit not null,
        status integer not null,
        status_date_time datetime not null,
        user_type integer not null,
        domain_id bigint,
        primary key (id)
    );

    create table TMTB_ADM_USR_TMTB_ACC_PROFILE (
        TMTB_ADM_USR_id bigint not null,
        accessProfiles_id bigint not null,
        primary key (TMTB_ADM_USR_id, accessProfiles_id)
    );

    create table TMTB_ALIPAY_HOST_TXN_LOG (
        id bigint not null auto_increment,
        alipay_reference_number   varchar(30),
        alipay_request_message  longtext,
        alipay_response_message  longtext,
        alipay_response_result longtext,
        amount varchar(12) not null,
        create_dt datetime not null,
        driver_id varchar(9),
        job_number varchar(10),
        message_type varchar(30),
        serial_no varchar(20),
        transaction_id varchar(30),
        transmit_status varchar(50),
        vehicle_id varchar(15),
        primary key (id)
    );

    create table TMTB_ALIPAY_TML_TXN_LOG (
        id bigint not null auto_increment,
        amount bigint,
        approval_code varchar(6),
        company_code varchar(4),
        driver_id varchar(9),
        fare_admin varchar(6),
        fare_amount varchar(6),
        fare_gst varchar(6),
        job_number varchar(10),
        log_date_time datetime,
        mti varchar(4),
        pinpad_sn varchar(20),
        proc_code varchar(6),
        resp_code varchar(2),
        response_date_time datetime,
        rrn varchar(12),
        stan varchar(6),
        taxi_number varchar(12),
        tid varchar(8),
        primary key (id)
    );

    create table TMTB_ALI_ALIPAY_TXN_LOG (
        id bigint not null auto_increment,
        alipay_code varchar(30),
        alipay_status varchar(30),
        alipay_sub_code varchar(30),
        auth_code varchar(16),
        auth_no varchar(80),
        date_time datetime,
        gmt_create datetime,
        gmt_trans datetime,
        mid varchar(15),
        msg varchar(50),
        msg_type varchar(20),
        mti varchar(4),
        operation_id varchar(80),
        operation_type varchar(50),
        order_title varchar(100),
        orig_auth_code varchar(16),
        out_order_no varchar(50),
        out_request_no varchar(50),
        out_trade_no varchar(80),
        pay_timeout varchar(10),
        payer_logon_id varchar(100),
        payer_user_id varchar(50),
        pos_cond_code varchar(8),
        pos_entry_mode varchar(8),
        proc_code varchar(6),
        product_code varchar(50),
        resp_code varchar(2),
        rrn varchar(32),
        source varchar(32),
        stan varchar(6),
        status varchar(12) not null,
        sub_msg varchar(100),
        tid varchar(8),
        trade_no varchar(80),
        trade_status varchar(30),
        trans_currency varchar(10),
        tx_result varchar(32),
        txn_amount varchar(255),
        txn_date varchar(4),
        txn_time varchar(6),
        ali_txn_log_id bigint,
        primary key (id)
    );

    create table TMTB_ALI_TXN_LOG (
        id bigint not null auto_increment,
        app_id varchar(32),
        auth_code varchar(16),
        auth_no varchar(80),
        booking_ref varchar(50),
        company_code varchar(4),
        date_time datetime not null,
        dcp_status varchar(12) not null,
        dcp_try_count integer,
        driver_id varchar(9),
        fare_admin bigint,
        fare_amt bigint,
        fare_gst bigint,
        h5_status varchar(12) not null,
        h5_try_count integer,
        job_number varchar(10),
        last_sent_to_dcp datetime,
        last_sent_to_h5 datetime,
        last_sent_to_alipay datetime,
        mid varchar(15),
        msg_type varchar(64),
        msg_ver varchar(16),
        mti varchar(4),
        is_online bit not null,
        orig_booking_ref varchar(50),
        pinpad_sn varchar(20),
        proc_code varchar(6),
        remark varchar(100),
        resp_code varchar(2),
        rrn varchar(24),
        source varchar(32),
        stan varchar(6),
        status varchar(12) not null,
        taxi_no varchar(12),
        tid varchar(8),
        trans_id varchar(64),
        tx_result varchar(32),
        txn_amount bigint,
        txn_date varchar(6),
        txn_time varchar(6),
        user_id varchar(255),
        primary key (id)
    );

    create table TMTB_APPLICATION (
        id bigint not null auto_increment,
        description varchar(200),
        name varchar(10) not null,
        version integer not null,
        primary key (id)
    );

    create table TMTB_APPLICATION_BIN_RANGE (
        id bigint not null auto_increment,
        pan_high varchar(200) not null,
        pan_high_additional_info longtext,
        pan_high_type smallint not null,
        pan_low varchar(200) not null,
        pan_low_additional_info longtext,
        pan_low_type smallint not null,
        application_id bigint not null,
        primary key (id)
    );

    create table TMTB_APPLICATION_BIN_VALUE (
        value varchar(200) not null,
        parameter_definition_id bigint not null,
        application_bin_range_id bigint not null,
        primary key (application_bin_range_id, parameter_definition_id)
    );

    create table TMTB_APPLICATION_DOWNLOAD_JOB (
        id bigint not null auto_increment,
        concurrent_download integer not null,
        create_date_time datetime not null,
        created_by varchar(30) not null,
        deleted int default 0 not null,
        end_window varchar(4) not null,
        start_window varchar(4) not null,
        status smallint not null,
        group_id bigint not null,
        primary key (id)
    );

    create table TMTB_APPLICATION_FEE (
        id bigint not null auto_increment,
        description varchar(200),
        desc_additional_info longtext,
        desc_type smallint not null,
        application_id bigint not null,
        primary key (id)
    );

    create table TMTB_APPLICATION_FEE_VALUE (
        fee_value varchar(200),
        parameter_definition_id bigint not null,
        application_fee_id bigint not null,
        primary key (application_fee_id, parameter_definition_id)
    );

    create table TMTB_BLACKLIST_DOWNLOAD_JOB (
        id bigint not null auto_increment,
        concurrent_download integer not null,
        create_date_time datetime not null,
        created_by varchar(30) not null,
        deleted int default 0 not null,
        end_window varchar(4) not null,
        start_window varchar(4) not null,
        status smallint not null,
        primary key (id)
    );

    create table TMTB_BLACKLIST_FILE (
        id bigint not null auto_increment,
        data longtext not null,
        record_no integer not null,
        blacklist_version_id bigint not null,
        primary key (id)
    );

    create table TMTB_BLACKLIST_VERSION (
        id bigint not null auto_increment,
        create_date_time datetime not null,
        created_by varchar(30) not null,
        full_cans varchar(19),
        full_range varchar(19),
        schedule_date date not null,
        small_cans varchar(19),
        small_range varchar(19),
        status smallint not null,
        version integer not null unique,
        primary key (id)
    );

    create table TMTB_CABC_TXN_LOG (
        CABC_TXN_LOG_id int not null auto_increment,
        auth_code varchar(255),
        company_code varchar(4) not null,
        dcp_code varchar(255),
        dcp_desc varchar(255),
        dcp_job_no varchar(255),
        dcp_req_id varchar(255),
        dcp_txn_id varchar(255),
        dcp_txn_state varchar(255),
        driver_id varchar(9) not null,
        expiry_date varchar(10),
        fare_admin bigint not null,
        fare_amt bigint not null,
        first_dcp_call date,
        fare_gst bigint not null,
        job_no varchar(10) not null,
        last_dcp_call date,
        masked_can varchar(19),
        mid varchar(15),
        msg_type varchar(20) not null,
        mti varchar(4),
        pinpad_sn varchar(20) not null,
        proc_code varchar(6),
        resp_code varchar(2),
        retry int default 0 not null,
        reversed bit,
        rrn varchar(255),
        stan varchar(6),
        status varchar(12) not null,
        taxi_no varchar(12) not null,
        tid varchar(8),
        trip_info longtext,
        txn_amount bigint not null,
        txn_date varchar(4),
        txn_date_time date,
        txn_detail longtext not null,
        txn_time varchar(6),
        voided bit,
        txn_result varchar(255),
        primary key (CABC_TXN_LOG_id)
    );

    create table TMTB_COF_TXN_LOG (
        id bigint not null auto_increment,
        auth_code varchar(255),
        company_code varchar(4),
        data longtext,
        dcp_code varchar(255),
        dcp_desc varchar(255),
        dcp_job_no varchar(255),
        dcp_req_id varchar(255),
        dcp_txn_id varchar(255),
        dcp_txn_state varchar(255),
        driver_id varchar(9),
        expiry_date varchar(10),
        fare_admin bigint,
        fare_amt bigint,
        first_dcp_call datetime,
        fare_gst bigint,
        job_number varchar(10),
        last_dcp_call datetime,
        masked_can varchar(19),
        mid varchar(15),
        msg_type varchar(20),
        mti varchar(4),
        pinpad_sn varchar(20),
        private_data longtext,
        proc_code varchar(6),
        resp_code varchar(2),
        retry int default 0,
        reversed bit,
        rrn varchar(255),
        stan varchar(6),
        status varchar(12),
        taxi_no varchar(12),
        tid varchar(8),
        txn_amount bigint,
        txn_date varchar(4),
        txn_date_time datetime,
        txn_time varchar(6),
        voided bit,
        txn_result varchar(255),
        primary key (id)
    );

    create table TMTB_DASH_SINGTEL_LOG (
        id bigint not null auto_increment,
        additional_data varchar(200),
        amount varchar(12) not null,
        counter_id varchar(20),
        create_dt datetime not null,
        dash_final_amount varchar(20),
        dash_message longtext,
        dash_new_balance varchar(20),
        dash_original_amount varchar(20),
        dash_reference_number varchar(20),
        dash_response_message longtext,
        dash_response_result varchar(100),
        dash_txn_status varchar(15),
        driver_id varchar(9),
        job_number varchar(10),
        message_class varchar(30),
        serial_no varchar(15),
        tid varchar(15),
        trans_date_time varchar(16),
        transaction_id varchar(30),
        transmit_status varchar(50),
        vehicle_id varchar(15),
        primary key (id)
    );

    create table TMTB_DASH_TXN_LOG (
        id bigint not null auto_increment,
        amount bigint,
        approval_code varchar(6),
        company_code varchar(4),
        driver_id varchar(9),
        fare_admin varchar(6),
        fare_amount varchar(6),
        fare_gst varchar(6),
        job_number varchar(10),
        log_date_time datetime,
        mti varchar(4),
        pinpad_sn varchar(20),
        proc_code varchar(6),
        resp_code varchar(2),
        response_date_time datetime,
        rrn varchar(12),
        stan varchar(6),
        taxi_number varchar(12),
        tid varchar(8),
        primary key (id)
    );

    create table TMTB_DOMAIN (
        id bigint not null auto_increment,
        create_date_time datetime,
        delete_date_time datetime,
        deleted int default 0 not null,
        modify_date_time datetime,
        name varchar(80) not null unique,
        short_name varchar(10) not null unique,
        primary key (id)
    );

    create table TMTB_EZL_TXN_LOG (
        id bigint not null auto_increment,
        auth_code varchar(6),
        company_code varchar(4),
        date_time datetime not null,
        driver_id varchar(9),
        fare_admin bigint,
        fare_amt bigint,
        fare_gst bigint,
        job_number varchar(10),
        mid varchar(15),
        msg_type varchar(20),
        mti varchar(4),
        pan varchar(19),
        pinpad_sn varchar(20),
        proc_code varchar(6),
        resp_code varchar(2),
        rrn varchar(24),
        source varchar(32),
        stan varchar(6),
        status varchar(12) not null,
        taxi_no varchar(12),
        tid varchar(8),
        tx_result varchar(32),
        txn_amount bigint,
        validation_val longtext,
        primary key (id)
    );

    create table TMTB_EZL_WC_TXN_LOG (
        id bigint not null auto_increment,
        auth_code varchar(6),
        date_time datetime,
        mid varchar(15),
        msg_type varchar(20),
        mti varchar(4),
        pos_cond_code varchar(8),
        proc_code varchar(6),
        resp_code varchar(2),
        rrn varchar(32),
        source varchar(32),
        stan varchar(6),
        status varchar(12) not null,
        tid varchar(8),
        tx_result varchar(32),
        txn_amount bigint,
        txn_date varchar(4),
        txn_time varchar(6),
        validation_val longtext,
        ezl_txn_log_id bigint,
        primary key (id)
    );

    create table TMTB_FAILED_DOWNLOAD (
        vehicle_no varchar(20) not null,
        deleted int default 0 not null,
        failed_reason longtext not null,
        serial_no varchar(20) not null,
        update_date_time datetime not null,
        current_release_id bigint,
        download_release_id bigint,
        primary key (vehicle_no)
    );

    create table TMTB_GROUPING (
        id bigint not null auto_increment,
        assign_date_time datetime,
        create_date_time datetime not null,
        created_by varchar(30) not null,
        deleted int default 0 not null,
        name varchar(100) not null unique,
        update_date_time datetime,
        release_id bigint,
        primary key (id)
    );

    create table TMTB_GROUP_RELEASE_HISTORY (
        id bigint not null auto_increment,
        assign_date_time datetime not null,
        group_id bigint not null,
        release_id bigint not null,
        primary key (id)
    );

    create table TMTB_MODEL (
        id bigint not null auto_increment,
        create_date_time datetime not null,
        deleted int default 0 not null,
        description varchar(200),
        model_name varchar(50) not null,
        primary key (id)
    );

    create table TMTB_MODULE (
        name varchar(30) not null,
        display_order varchar(10) unique,
        label varchar(30) not null unique,
        module_type integer,
        url varchar(255) not null unique,
        visible bit not null,
        primary key (name)
    );

    create table TMTB_MP_TXN_LOG (
        id bigint not null auto_increment,
        auth_code varchar(255),
        company_code varchar(4),
        data longtext,
        dcp_code varchar(255),
        dcp_desc varchar(255),
        dcp_job_no varchar(255),
        dcp_req_id varchar(255),
        dcp_txn_id varchar(255),
        dcp_txn_state varchar(255),
        driver_id varchar(9),
        expiry_date varchar(10),
        fare_admin bigint,
        fare_amt bigint,
        first_dcp_call datetime,
        fare_gst bigint,
        job_number varchar(10),
        last_dcp_call datetime,
        masked_can varchar(19),
        mid varchar(15),
        msg_type varchar(20),
        mti varchar(4),
        pinpad_sn varchar(20),
        private_data longtext,
        proc_code varchar(6),
        resp_code varchar(2),
        retry int default 0,
        reversed bit,
        rrn varchar(255),
        stan varchar(6),
        status varchar(12),
        taxi_no varchar(12),
        tid varchar(8),
        txn_amount bigint,
        txn_date varchar(4),
        txn_date_time datetime,
        txn_time varchar(6),
        voided bit,
        txn_result varchar(255),
        primary key (id)
    );

    create table TMTB_NOF_ACCOUNT (
        id bigint not null auto_increment,
        create_date_time datetime not null,
        delete_date_time datetime,
        guid varchar(255) not null,
        iss_fiid varchar(10),
        iss_name varchar(32),
        last_4 varchar(4),
        merch_token_id varchar(255) not null unique,
        mid varchar(255) not null,
        muid varchar(255) not null,
        status integer not null,
        token varchar(255) not null,
        token_expiry varchar(4),
        token_hash varchar(255) not null,
        token_index varchar(2) not null,
        primary key (id),
        unique (guid, muid, token_index, delete_date_time)
    );

    create table TMTB_NOF_NETS_TXN_LOG (
        id bigint not null auto_increment,
        auth_code varchar(16),
        capture_date varchar(4),
        date_time datetime,
        f126 longtext,
        merchant_name varchar(255),
        mid varchar(15),
        msg_type varchar(20),
        mti varchar(4),
        muid varchar(64),
        orig_auth_code varchar(16),
        pos_cond_code varchar(8),
        pos_entry_mode varchar(8),
        proc_code varchar(6),
        responder_code char(1),
        resp_code varchar(2),
        reversal_state integer,
        reversal_try_count integer,
        reversed bit not null,
        rrn varchar(32),
        source varchar(32),
        stan varchar(6),
        status varchar(12) not null,
        tid varchar(8),
        token_index varchar(2),
        trans_date_time varchar(12),
        tx_result varchar(32),
        txn_amount bigint,
        txn_date varchar(4),
        txn_time varchar(6),
        voided bit not null,
        nof_txn_log_id bigint,
        primary key (id)
    );

    create table TMTB_NOF_TXN_LOG (
        id bigint not null auto_increment,
        auth_code varchar(16),
        company_code varchar(4),
        data_for_nets longtext,
        date_time datetime not null,
        dcp_status varchar(12) not null,
        dcp_try_count integer,
        device_info varchar(255),
        driver_id varchar(9),
        fare_admin bigint,
        fare_amt bigint,
        gps_coord varchar(255),
        fare_gst bigint,
        guid varchar(255),
        job_number varchar(10),
        last_sent_to_dcp datetime,
        merch_token_id varchar(64),
        mid varchar(15),
        msg_type varchar(64),
        msg_ver varchar(16),
        mti varchar(4),
        muid varchar(255),
        is_online bit not null,
        orig_auth_code varchar(16),
        orig_rrn varchar(24),
        orig_tid varchar(16),
        orig_trans_date varchar(8),
        orig_trans_time varchar(8),
        pinpad_sn varchar(20),
        proc_code varchar(6),
        resp_code varchar(2),
        reversed bit not null,
        rrn varchar(24),
        source varchar(32),
        stan varchar(6),
        status varchar(12) not null,
        taxi_no varchar(12),
        tid varchar(8),
        token_expiry varchar(8),
        token_index varchar(2),
        transid varchar(64),
        tx_result varchar(32),
        txn_amount bigint,
        txn_date varchar(6),
        txn_time varchar(6),
        voided bit not null,
        primary key (id)
    );

    create table TMTB_PAGE (
        name varchar(100) not null,
        admin_flag bit not null,
        common_flag bit not null,
        display_order varchar(10) unique,
        domain_flag bit not null,
        group_name varchar(30),
        label varchar(50) not null unique,
        url varchar(255) not null unique,
        visible bit not null,
        module_name varchar(30) not null,
        primary key (name)
    );

    create table TMTB_PARAMETER_DEFINITION (
        id bigint not null auto_increment,
        additional_info longtext,
        default_value varchar(200),
        deleted int default 0 not null,
        description varchar(200),
        param_level smallint not null,
        name varchar(50) not null,
        type smallint not null,
        application_id bigint not null,
        primary key (id),
        unique (application_id, name)
    );

    create table TMTB_PL_ACCOUNT (
        id bigint not null auto_increment,
        create_date_time datetime not null,
        ec_setup_id varchar(255) not null,
        msg_id varchar(255) not null,
        status integer not null,
        user_id varchar(255) not null,
        primary key (id)
    );

    create table TMTB_PL_PAYLAH_TXN_LOG (
        id bigint not null auto_increment,
        approval_code varchar(255),
        date_time datetime,
        ec_setup_id varchar(255),
        http_status_code integer,
        msg_id varchar(255),
        msg_type varchar(20),
        orig_bank_txn_ref_id varchar(255),
        orig_msg_id varchar(255),
        refunded bit,
        reversal_try_count integer,
        status varchar(12) not null,
        tx_result varchar(32),
        txn_amount varchar(255),
        txn_ref_id varchar(255),
        txn_status varchar(10),
        txn_status_code varchar(10),
        txn_status_desc varchar(255),
        txn_type varchar(32),
        voided bit not null,
        pl_txn_log_id bigint,
        primary key (id)
    );

    create table TMTB_PL_TXN_LOG (
        id bigint not null auto_increment,
        auth_code varchar(16),
        company_code varchar(4),
        date_time datetime not null,
        dcp_status varchar(12) not null,
        dcp_try_count integer,
        device_info varchar(255),
        driver_id varchar(9),
        fare_admin bigint,
        fare_amt bigint,
        gps_coord varchar(255),
        fare_gst bigint,
        job_number varchar(10),
        last_sent_to_dcp datetime,
        mid varchar(15),
        msg_type varchar(64),
        msg_ver varchar(16),
        mti varchar(4),
        is_online bit not null,
        pinpad_sn varchar(20),
        proc_code varchar(6),
        resp_code varchar(2),
        reversed bit not null,
        rrn varchar(24),
        source varchar(32),
        stan varchar(6),
        status varchar(12) not null,
        taxi_no varchar(12),
        tid varchar(8),
        transid varchar(64),
        tx_result varchar(32),
        txn_amount bigint,
        txn_date varchar(6),
        txn_status varchar(255),
        txn_status_desc varchar(255),
        txn_time varchar(6),
        user_id varchar(255),
        voided bit not null,
        primary key (id)
    );

    create table TMTB_PROFILE_BIN_RANGE (
        id bigint not null auto_increment,
        pan_high varchar(200) not null,
        pan_high_additional_info longtext,
        pan_high_type smallint not null,
        pan_low varchar(200) not null,
        pan_low_additional_info longtext,
        pan_low_type smallint not null,
        terminal_profile_id bigint not null,
        primary key (id)
    );

    create table TMTB_PROFILE_BIN_VALUE (
        value varchar(200) not null,
        parameter_definition_id bigint not null,
        profile_bin_range_id bigint not null,
        primary key (parameter_definition_id, profile_bin_range_id)
    );

    create table TMTB_PROFILE_FEE (
        id bigint not null auto_increment,
        description varchar(200),
        desc_additional_info longtext,
        desc_type smallint not null,
        terminal_profile_id bigint not null,
        primary key (id)
    );

    create table TMTB_PROFILE_FEE_VALUE (
        fee_value varchar(200),
        parameter_definition_id bigint not null,
        profile_fee_id bigint not null,
        primary key (parameter_definition_id, profile_fee_id)
    );

    create table TMTB_PROFILE_PARAMETER_VALUE (
        value varchar(200) not null,
        parameter_definition_id bigint not null,
        terminal_profile_id bigint not null,
        primary key (parameter_definition_id, terminal_profile_id)
    );

    create table TMTB_QUERY_FARE_TXN_LOG (
        id bigint not null auto_increment,
        booking_job varchar(32),
        card_number varchar(20),
        date_time datetime not null,
        fare_amount varchar(6),
        payment_mode varchar(32),
        request_id varchar(18) not null,
        resp_code varchar(2),
        source varchar(32),
        vehicle_no varchar(10),
        primary key (id)
    );

    create table TMTB_RELEASE (
        id bigint not null auto_increment,
        created_by varchar(30) not null,
        deleted int default 0 not null,
        description varchar(200),
        file_size integer not null,
        load_date_time datetime not null,
        min_version integer,
        version integer not null,
        primary key (id)
    );

    create table TMTB_RELEASE_FILE (
        id bigint not null auto_increment,
        data longtext not null,
        record_no integer not null,
        release_id bigint not null,
        primary key (id)
    );

    create table TMTB_RELEASE_TMTB_APPLICATION (
        TMTB_RELEASE_id bigint not null,
        application_id bigint not null,
        primary key (TMTB_RELEASE_id, application_id)
    );

    create table TMTB_RELEASE_TMTB_MODEL (
        TMTB_RELEASE_id bigint not null,
        model_id bigint not null,
        primary key (TMTB_RELEASE_id, model_id)
    );

    create table TMTB_SEQUENCE_TABLE (
        id bigint not null auto_increment,
        name varchar(255) unique,
        value bigint,
        primary key (id)
    );

    create table TMTB_TERMINAL_APPLICATION (
        update_date_time datetime not null,
        terminal_id bigint not null,
        application_id bigint not null,
        release_id bigint not null,
        primary key (application_id, release_id, terminal_id)
    );

    create table TMTB_TERMINAL_BL_DOWNLOAD_JOB (
        first_request_date_time datetime,
        last_request_date_time datetime,
        record_no integer not null,
        status smallint not null,
        update_date_time datetime not null,
        terminal_id bigint not null,
        blacklist_version_id bigint not null,
        blacklist_download_job_id bigint not null,
        vehicle_id varchar(20) not null,
        primary key (blacklist_download_job_id, blacklist_version_id, terminal_id, vehicle_id)
    );

    create table TMTB_TERMINAL_BL_VERSION (
        update_date_time datetime not null,
        terminal_id bigint not null,
        blacklist_version_id bigint not null,
        primary key (blacklist_version_id, terminal_id)
    );

    create table TMTB_TERMINAL_LOG (
        id bigint not null auto_increment,
        log_date date not null,
        terminal_id bigint not null,
        primary key (id),
        unique (terminal_id, log_date)
    );

    create table TMTB_TERMINAL_LOG_RECORD (
        id bigint not null auto_increment,
        data longtext,
        length integer not null,
        sequence_no integer not null,
        source varchar(3) not null,
        timestamp datetime not null,
        upload_date_time datetime not null,
        terminal_log_id bigint not null,
        primary key (id)
    );

    create table TMTB_TERMINAL_PROFILE (
        id bigint not null auto_increment,
        create_date_time datetime not null,
        deleted int default 0 not null,
        effective_date datetime,
        profile_name varchar(50) not null,
        profile_version integer not null,
        application_id bigint not null,
        group_id bigint not null,
        primary key (id),
        unique (group_id, application_id, profile_version)
    );

    create table TMTB_TERMINAL_PROFILE_FILE (
        id bigint not null auto_increment,
        data longtext not null,
        record_no integer not null,
        terminal_profile_id bigint not null,
        primary key (id)
    );

    create table TMTB_TERM_APP_DOWNLOAD_JOB (
        first_request_date_time datetime,
        last_request_date_time datetime,
        record_no integer not null,
        status smallint not null,
        update_date_time datetime not null,
        terminal_id bigint not null,
        vehicle_id varchar(20) not null,
        application_download_job_id bigint not null,
        release_id bigint not null,
        primary key (application_download_job_id, release_id, terminal_id, vehicle_id)
    );

    create table TMTB_TMS_TERMINAL (
        id bigint not null auto_increment,
        create_date_time datetime not null,
        ctls_version varchar(20) not null,
        deleted int default 0 not null,
        disabled_logging int default 0 not null,
        os_version varchar(20) not null,
        ptid varchar(20) not null,
        serial_no varchar(20) not null unique,
        update_date_time datetime,
        upload_log int default 0 not null,
        vehicle_no varchar(20) not null,
        vehicle_unpaired int default 0 not null,
        model_id bigint not null,
        primary key (id)
    );

    create table TMTB_TXN_LOG (
        id bigint not null auto_increment,
        data longblob,
        mti varchar(4),
        proc_code varchar(6),
        resp_code varchar(2),
        term_serial_no varchar(60),
        tid varchar(8),
        trans_header varchar(8),
        txn_date_time datetime,
        txn_source integer,
        txn_result varchar(255),
        txn_type varchar(5),
        primary key (id)
    );

    create table TMTB_TXN_RESULT (
        code varchar(255) not null,
        description varchar(50),
        response_code varchar(3),
        primary key (code)
    );

    create table TMTB_TXN_TYPE (
        code varchar(5) not null,
        name varchar(30),
        primary key (code)
    );

    create table TMTB_VEHICLE_GROUP (
        id bigint not null auto_increment,
        assign_date_time datetime not null,
        group_id bigint not null,
        vehicle_id varchar(20) not null unique,
        primary key (id)
    );

    create table TMVW_APPLICATION_BIN_SUMMARY (
        id bigint not null,
        app_id bigint,
        bin_info varchar(255),
        bin_description varchar(255),
        bin_param_name varchar(255),
        bin_range_id bigint,
        bin_value varchar(255),
        pan_high varchar(255),
        pan_high_info varchar(255),
        pan_high_type integer,
        pan_low varchar(255),
        pan_low_info varchar(255),
        pan_low_type integer,
        param_definition_id bigint,
        type integer,
        primary key (id)
    );

    create table TMVW_APPLICATION_FEE_SUMMARY (
        id bigint not null,
        app_id bigint,
        description varchar(255),
        desc_info varchar(255),
        desc_type integer,
        fee_info varchar(255),
        fee_description varchar(255),
        fee_id bigint,
        fee_param_name varchar(255),
        fee_value varchar(255),
        param_definition_id bigint,
        type integer,
        primary key (id)
    );

    create table TMVW_FAILED_DOWNLOAD_VEHICLES (
        id bigint not null,
        company_id varchar(12) not null,
        credit_app_version varchar(6),
        current_release varchar(200),
        current_release_description varchar(255),
        current_release_version varchar(255),
        download_dt datetime,
        download_release varchar(200) not null,
        download_release_description varchar(255),
        download_release_version varchar(255),
        driver_name varchar(40) not null,
        driver_nric varchar(12) not null,
        driving_mode varchar(6),
        epins_app_version varchar(6),
        ezlink_app_version varchar(6),
        ezlink_sam_serial_no varchar(20),
        failed_reason varchar(200) not null,
        firmware_ver varchar(3),
        full_black_list_can varchar(20),
        full_black_list_range varchar(20),
        ivd_model_id varchar(10),
        master_app_version varchar(6),
        nets_app_version varchar(6),
        phone1 varchar(8),
        phone2 varchar(8),
        serial_no varchar(255),
        small_black_list_can varchar(20),
        small_black_list_range varchar(20),
        update_dt datetime,
        vehicle_id varchar(20) not null,
        vehicle_type varchar(30),
        primary key (id)
    );

    create table TMVW_GROUP_REL_HISTORY_SUMMARY (
        id bigint not null,
        group_id bigint,
        group_name varchar(255),
        model_name varchar(255),
        release_desc varchar(255),
        release_id bigint,
        release_version varchar(255),
        primary key (id)
    );

    create table TMVW_OTA_STATUS_REPORT_SUMMARY (
        group_id varchar(255) not null,
        completed integer,
        downloading integer,
        file_size integer,
        group_name varchar(255),
        not_started integer,
        online_completed integer,
        release_desc varchar(255),
        time_diff bigint,
        total_vehicles integer,
        version integer,
        primary key (group_id)
    );

    create table TMVW_PROFILE_BIN_VALUE_SUMMARY (
        id bigint not null,
        app_id bigint,
        bin_info varchar(255),
        bin_description varchar(255),
        bin_param_name varchar(255),
        profile_bin_range_id bigint,
        bin_value varchar(255),
        pan_high varchar(255),
        pan_high_info varchar(255),
        pan_high_type integer,
        pan_low varchar(255),
        pan_low_info varchar(255),
        pan_low_type integer,
        param_definition_id bigint,
        terminal_profile_id bigint,
        type integer,
        primary key (id)
    );

    create table TMVW_PROFILE_BIN_VALUE_VIEW (
        id bigint not null,
        parameter_definition_id bigint,
        profile_bin_range_id bigint,
        value varchar(255),
        primary key (id)
    );

    create table TMVW_PROFILE_FEE_VALUE_SUMMARY (
        id bigint not null,
        app_id bigint,
        description varchar(255),
        desc_info varchar(255),
        desc_type integer,
        fee_info varchar(255),
        fee_id bigint,
        fee_description varchar(255),
        fee_param_name varchar(255),
        fee_value varchar(255),
        param_definition_id bigint,
        terminal_profile_id bigint,
        type integer,
        primary key (id)
    );

    create table TMVW_PROFILE_FEE_VALUE_VIEW (
        id bigint not null,
        parameter_definition_id bigint,
        profile_fee_id bigint,
        value varchar(255),
        primary key (id)
    );

    create table TMVW_PROFILE_PARAM_VALUE_VIEW (
        id bigint not null,
        parameter_definition_id bigint,
        terminal_profile_id bigint,
        value varchar(255),
        primary key (id)
    );

    create table TMVW_PROF_PARAM_VALUE_SUMMARY (
        id bigint not null,
        additional_info varchar(255),
        application_id bigint,
        description varchar(255),
        param_level integer,
        name varchar(255),
        parameter_definition_id bigint,
        terminal_profile_id bigint,
        type integer,
        value varchar(255),
        primary key (id)
    );

    create table TMVW_RELEASE_PACKAGE_SUMMARY (
        releaseId bigint not null,
        description varchar(255),
        file_size integer,
        load_date_time datetime,
        min_version integer,
        model_name varchar(255),
        no_groups integer,
        no_terminal bigint,
        version integer,
        primary key (releaseId)
    );

    create table TMVW_TERMINAL_NOT_DOWNLOADED (
        terminal_id bigint not null,
        group_id bigint,
        release_id bigint,
        serial_no varchar(255),
        vehicle_id varchar(255),
        primary key (terminal_id)
    );

    create table TMVW_TERMINAL_PROFILE_SUMMARY (
        id bigint not null,
        app_id bigint,
        app_name varchar(255),
        app_version integer,
        group_id bigint,
        group_name varchar(255),
        min_release_version integer,
        model_name varchar(255),
        profile_create_dt datetime,
        profile_effective_date datetime,
        profile_id bigint,
        profile_name varchar(255),
        profile_version integer,
        release_desc varchar(255),
        release_id bigint,
        release_version integer,
        primary key (id)
    );

    create table TMVW_TERMINAL_SUMMARY (
        id bigint not null,
        download_status varchar(255),
        model_id bigint,
        model_name varchar(255),
        record_no integer,
        release_desc varchar(255),
        release_version integer,
        serial_no varchar(255),
        total_record_no integer,
        update_date_time datetime,
        upload_log bit,
        vehicle_no varchar(255),
        vehicle_unpaired bit,
        primary key (id)
    );

    create table TMVW_TERM_APP_DOWNLOAD_SUMMARY (
        id bigint not null,
        job_id bigint,
        completed integer,
        concurrent_download integer not null,
        creation_date datetime,
        deleted bit,
        description varchar(255),
        end_window varchar(4) not null,
        group_id bigint,
        job_status smallint,
        model_id bigint,
        model_name varchar(50) not null,
        not_started integer,
        pending integer,
        release_id bigint,
        start_window varchar(4) not null,
        version varchar(255),
        primary key (id)
    );

    create table TMVW_TERM_APP_SUMMARY (
        id bigint not null,
        description varchar(255),
        app_name varchar(255),
        app_version integer,
        application_id bigint,
        serial_no varchar(255),
        terminal_id bigint,
        update_date_time datetime,
        vehicle_id varchar(255),
        primary key (id)
    );

    create table TMVW_TERM_BL_COMPLETED_JOB (
        id bigint not null,
        blacklist_version integer,
        blacklist_version_id bigint,
        model_name varchar(255),
        serial_no varchar(255),
        update_date_time datetime not null,
        vehicle_id varchar(255),
        primary key (id)
    );

    create table TMVW_TERM_BL_DOWNLOAD_SUMMARY (
        id bigint not null,
        job_id bigint,
        completed integer,
        concurrent_download integer not null,
        create_date_time datetime,
        deleted bit,
        end_window varchar(4) not null,
        job_status smallint,
        not_started integer,
        pending integer,
        schedule_date datetime,
        start_window varchar(4) not null,
        primary key (id)
    );

    create table TMVW_TERM_BL_NOT_DOWNLOADED (
        id bigint not null,
        model_id bigint,
        model_name varchar(255),
        serial_no varchar(255),
        terminal_id bigint,
        upload_log smallint,
        vehicle_id varchar(255),
        primary key (id)
    );

    create table TMVW_TERM_BL_WITH_DOWNLOAD_JOB (
        terminal_id bigint not null,
        blacklist_download_id bigint,
        blacklist_version_id bigint,
        last_downloaded datetime not null,
        model_description varchar(255),
        model varchar(255),
        record_no integer,
        serial_no varchar(255),
        start_time datetime not null,
        terminal_bl_download_status integer,
        total_record_no integer,
        vehicle_id varchar(255),
        primary key (terminal_id)
    );

    create table TMVW_TERM_PROF_SUMMARY_VIEW (
        id bigint not null,
        app_id bigint,
        app_name varchar(255),
        app_version integer,
        group_id bigint,
        group_name varchar(255),
        min_release_version integer,
        model_name varchar(255),
        profile_create_dt datetime,
        profile_effective_date datetime,
        profile_id bigint,
        profile_name varchar(255),
        profile_version integer,
        release_desc varchar(255),
        release_id bigint,
        release_version integer,
        terminal_profile_summary_id bigint,
        primary key (id)
    );

    create table TMVW_TERM_WITH_DOWNLOADED_APP (
        terminal_id bigint not null,
        group_id bigint,
        release_id bigint,
        serial_no varchar(255),
        vehicle_id varchar(255),
        primary key (terminal_id)
    );

    create table TMVW_TERM_WITH_DOWNLOAD_JOB (
        id bigint not null,
        app_download_id bigint,
        group_id bigint,
        group_name varchar(255),
        last_downloaded datetime not null,
        record_no integer,
        release_id bigint,
        serial_no varchar(255),
        start_time datetime not null,
        terminal_download_status integer,
        terminal_id bigint,
        total_record_no integer,
        vehicle_id varchar(255),
        primary key (id)
    );

    create table TMVW_VEHICLE_GROUPING_LIST (
        vehicle_id varchar(255) not null,
        assign_date_time datetime,
        firmware_ver varchar(3),
        group_id bigint,
        group_name varchar(255),
        ivd_model_id varchar(10),
        vehicle_type varchar(30),
        primary key (vehicle_id)
    );

    create table TMVW_VEHICLE_GROUPING_SUMMARY (
        id bigint not null,
        assign_date datetime,
        create_date datetime,
        file_size integer,
        group_id bigint,
        group_name varchar(255),
        min_version integer,
        model_id bigint,
        model_name varchar(255),
        description varchar(255),
        release_id bigint,
        total_vehicles bigint,
        version integer,
        primary key (id)
    );

    create table TMVW_Vehicle (
        vehicle_id varchar(20) not null,
        credit_app_version varchar(6),
        epins_app_version varchar(6),
        ezlink_app_version varchar(6),
        ezlink_sam_serial_no varchar(20),
        firmware_ver varchar(3),
        full_black_list_can varchar(20),
        full_black_list_range varchar(20),
        ivd_model_id varchar(10),
        ivd_no varchar(10),
        master_app_version varchar(6),
        nets_app_version varchar(6),
        pinpad_serial_no varchar(20),
        small_black_list_can varchar(20),
        small_black_list_range varchar(20),
        vehicle_type varchar(30),
        primary key (vehicle_id)
    );

    create index BATCHIX_EZLINK_CAN on BATCHTB_EZLINK_TRANSACTIONS (can);

    create index BATCHIX_EZL_PTC on BATCHTB_EZLINK_TRANSACTIONS (ptc);

    alter table BATCHTB_LOYALTY_TRANSACTIONS 
        add index FKF378BDA56F42C742 (loyalty_file_id), 
        add constraint FKF378BDA56F42C742 
        foreign key (loyalty_file_id) 
        references BATCHTB_LOYALTY_FILE (id);

    alter table TMTB_ACC_PROFILE_TMTB_PAGE 
        add index TMFC_ACC_PROFILE_ID_PAGE (TMTB_ACC_PROFILE_id), 
        add constraint TMFC_ACC_PROFILE_ID_PAGE 
        foreign key (TMTB_ACC_PROFILE_id) 
        references TMTB_ACC_PROFILE (id);

    alter table TMTB_ACC_PROFILE_TMTB_PAGE 
        add index TMFC_PAGE_NAME_ACC_PROFILE (pages_name), 
        add constraint TMFC_PAGE_NAME_ACC_PROFILE 
        foreign key (pages_name) 
        references TMTB_PAGE (name);

    alter table TMTB_ADMIN_LOG 
        add index TMFC_DOMAIN_ID_ADMIN_LOG (domain_id), 
        add constraint TMFC_DOMAIN_ID_ADMIN_LOG 
        foreign key (domain_id) 
        references TMTB_DOMAIN (id);

    alter table TMTB_ADMIN_LOG 
        add index TMFC_PAGE_NAME_ADMIN_LOG (page_name), 
        add constraint TMFC_PAGE_NAME_ADMIN_LOG 
        foreign key (page_name) 
        references TMTB_PAGE (name);

    alter table TMTB_ADM_USR 
        add index TMFC_DOMAIN_ID_ADM_USR (domain_id), 
        add constraint TMFC_DOMAIN_ID_ADM_USR 
        foreign key (domain_id) 
        references TMTB_DOMAIN (id);

    alter table TMTB_ADM_USR_TMTB_ACC_PROFILE 
        add index TMFC_ACC_PROFILE_ID_ADM_USR (accessProfiles_id), 
        add constraint TMFC_ACC_PROFILE_ID_ADM_USR 
        foreign key (accessProfiles_id) 
        references TMTB_ACC_PROFILE (id);

    alter table TMTB_ADM_USR_TMTB_ACC_PROFILE 
        add index TMFC_ADM_USR_ID_ACC_PROFILE (TMTB_ADM_USR_id), 
        add constraint TMFC_ADM_USR_ID_ACC_PROFILE 
        foreign key (TMTB_ADM_USR_id) 
        references TMTB_ADM_USR (id);

    alter table TMTB_ALI_ALIPAY_TXN_LOG 
        add index TMFC_ALI_ALP_TXN_LOG__TXNLOGID (ali_txn_log_id), 
        add constraint TMFC_ALI_ALP_TXN_LOG__TXNLOGID 
        foreign key (ali_txn_log_id) 
        references TMTB_ALI_TXN_LOG (id);

    alter table TMTB_APPLICATION_BIN_RANGE 
        add index TMFC_APP_ID_APP_BIN_RANGE (application_id), 
        add constraint TMFC_APP_ID_APP_BIN_RANGE 
        foreign key (application_id) 
        references TMTB_APPLICATION (id);

    alter table TMTB_APPLICATION_BIN_VALUE 
        add index TMFC_PARAM_DEF_ID_APPBV (parameter_definition_id), 
        add constraint TMFC_PARAM_DEF_ID_APPBV 
        foreign key (parameter_definition_id) 
        references TMTB_PARAMETER_DEFINITION (id);

    alter table TMTB_APPLICATION_BIN_VALUE 
        add index TMFC_APPBR_ID_APPBV (application_bin_range_id), 
        add constraint TMFC_APPBR_ID_APPBV 
        foreign key (application_bin_range_id) 
        references TMTB_APPLICATION_BIN_RANGE (id);

    alter table TMTB_APPLICATION_DOWNLOAD_JOB 
        add index TMFC_GROUP_ID_APP_DJOB (group_id), 
        add constraint TMFC_GROUP_ID_APP_DJOB 
        foreign key (group_id) 
        references TMTB_GROUPING (id);

    alter table TMTB_APPLICATION_FEE 
        add index TMFC_APP_ID_APP_FEE (application_id), 
        add constraint TMFC_APP_ID_APP_FEE 
        foreign key (application_id) 
        references TMTB_APPLICATION (id);

    alter table TMTB_APPLICATION_FEE_VALUE 
        add index TMFC_APPF_ID_APPFV (application_fee_id), 
        add constraint TMFC_APPF_ID_APPFV 
        foreign key (application_fee_id) 
        references TMTB_APPLICATION_FEE (id);

    alter table TMTB_APPLICATION_FEE_VALUE 
        add index TMFC_PARAM_DEF_ID_APPFV (parameter_definition_id), 
        add constraint TMFC_PARAM_DEF_ID_APPFV 
        foreign key (parameter_definition_id) 
        references TMTB_PARAMETER_DEFINITION (id);

    alter table TMTB_BLACKLIST_FILE 
        add index TMFC_BL_VERSION_ID_BL_FILE (blacklist_version_id), 
        add constraint TMFC_BL_VERSION_ID_BL_FILE 
        foreign key (blacklist_version_id) 
        references TMTB_BLACKLIST_VERSION (id);

    create index TMIX_CABC_TXN_LOG__JOB_NO on TMTB_CABC_TXN_LOG (job_no);

    create index TMIX_CABC_TXN_LOG__TAXI_NO on TMTB_CABC_TXN_LOG (taxi_no);

    create index TMIX_CABC_TXN_LOG__STATUS on TMTB_CABC_TXN_LOG (status);

    create index TMIX_CABC_TXN_LOG__MSG_TYPE on TMTB_CABC_TXN_LOG (msg_type);

    create index TMIX_CABC_TXN_LOG__RESP_CODE on TMTB_CABC_TXN_LOG (resp_code);

    create index TMIX_CABC_TXN_LOG__RETRY on TMTB_CABC_TXN_LOG (retry);

    create index TMIX_CABC_TXN_LOG__TXN_DT_TM on TMTB_CABC_TXN_LOG (txn_date_time);

    create index TMIX_CABC_TXN_LOG__PINPADP_SN on TMTB_CABC_TXN_LOG (pinpad_sn);

    alter table TMTB_CABC_TXN_LOG 
        add index TMFC_RESULT_CODE_VC_TXN_LOG (txn_result), 
        add constraint TMFC_RESULT_CODE_VC_TXN_LOG 
        foreign key (txn_result) 
        references TMTB_TXN_RESULT (code);

    alter table TMTB_COF_TXN_LOG 
        add index TMFC_RESULT_CODE_COF_TXN_LOG (txn_result), 
        add constraint TMFC_RESULT_CODE_COF_TXN_LOG 
        foreign key (txn_result) 
        references TMTB_TXN_RESULT (code);

    alter table TMTB_EZL_WC_TXN_LOG 
        add index TMFC_LOG_ID_EZL_WC_TXN_LOG (ezl_txn_log_id), 
        add constraint TMFC_LOG_ID_EZL_WC_TXN_LOG 
        foreign key (ezl_txn_log_id) 
        references TMTB_EZL_TXN_LOG (id);

    alter table TMTB_FAILED_DOWNLOAD 
        add index TMFC_REL_DL_REL_ID_FAILED_DL (download_release_id), 
        add constraint TMFC_REL_DL_REL_ID_FAILED_DL 
        foreign key (download_release_id) 
        references TMTB_RELEASE (id);

    alter table TMTB_FAILED_DOWNLOAD 
        add index TMFC_REL_CURR_REL_ID_FAILED_DL (current_release_id), 
        add constraint TMFC_REL_CURR_REL_ID_FAILED_DL 
        foreign key (current_release_id) 
        references TMTB_RELEASE (id);

    alter table TMTB_GROUPING 
        add index TMFC_REL_ID_GROUP (release_id), 
        add constraint TMFC_REL_ID_GROUP 
        foreign key (release_id) 
        references TMTB_RELEASE (id);

    alter table TMTB_GROUP_RELEASE_HISTORY 
        add index TMFC_REL_ID_GROUP_REL_HIST (release_id), 
        add constraint TMFC_REL_ID_GROUP_REL_HIST 
        foreign key (release_id) 
        references TMTB_RELEASE (id);

    alter table TMTB_GROUP_RELEASE_HISTORY 
        add index TMFC_GROUP_ID_GROUP_REL_HIST (group_id), 
        add constraint TMFC_GROUP_ID_GROUP_REL_HIST 
        foreign key (group_id) 
        references TMTB_GROUPING (id);

    alter table TMTB_MP_TXN_LOG 
        add index TMFC_RESULT_CODE_MP_TXN_LOG (txn_result), 
        add constraint TMFC_RESULT_CODE_MP_TXN_LOG 
        foreign key (txn_result) 
        references TMTB_TXN_RESULT (code);

    alter table TMTB_NOF_NETS_TXN_LOG 
        add index FKD7A274D84EB4341F (nof_txn_log_id), 
        add constraint FKD7A274D84EB4341F 
        foreign key (nof_txn_log_id) 
        references TMTB_NOF_TXN_LOG (id);

    alter table TMTB_PAGE 
        add index TMFC_MODULE_NAME_PAGE (module_name), 
        add constraint TMFC_MODULE_NAME_PAGE 
        foreign key (module_name) 
        references TMTB_MODULE (name);

    alter table TMTB_PARAMETER_DEFINITION 
        add index TMFC_APP_ID_PARAM_DEF (application_id), 
        add constraint TMFC_APP_ID_PARAM_DEF 
        foreign key (application_id) 
        references TMTB_APPLICATION (id);

    alter table TMTB_PL_PAYLAH_TXN_LOG 
        add index FKADBA6C6624315069 (pl_txn_log_id), 
        add constraint FKADBA6C6624315069 
        foreign key (pl_txn_log_id) 
        references TMTB_PL_TXN_LOG (id);

    alter table TMTB_PROFILE_BIN_RANGE 
        add index TMFC_TERM_PROF_ID_PROFBR (terminal_profile_id), 
        add constraint TMFC_TERM_PROF_ID_PROFBR 
        foreign key (terminal_profile_id) 
        references TMTB_TERMINAL_PROFILE (id);

    alter table TMTB_PROFILE_BIN_VALUE 
        add index TMFC_PARAM_DEF_ID_PROFBV (parameter_definition_id), 
        add constraint TMFC_PARAM_DEF_ID_PROFBV 
        foreign key (parameter_definition_id) 
        references TMTB_PARAMETER_DEFINITION (id);

    alter table TMTB_PROFILE_BIN_VALUE 
        add index TMFC_PROFBR_ID_PROFBV (profile_bin_range_id), 
        add constraint TMFC_PROFBR_ID_PROFBV 
        foreign key (profile_bin_range_id) 
        references TMTB_PROFILE_BIN_RANGE (id);

    alter table TMTB_PROFILE_FEE 
        add index TMFC_TERM_PROF_ID_PROFFEE (terminal_profile_id), 
        add constraint TMFC_TERM_PROF_ID_PROFFEE 
        foreign key (terminal_profile_id) 
        references TMTB_TERMINAL_PROFILE (id);

    alter table TMTB_PROFILE_FEE_VALUE 
        add index TMFC_PROFF_ID_PROFFV (profile_fee_id), 
        add constraint TMFC_PROFF_ID_PROFFV 
        foreign key (profile_fee_id) 
        references TMTB_PROFILE_FEE (id);

    alter table TMTB_PROFILE_FEE_VALUE 
        add index TMFC_PARAM_DEF_ID_PROFFV (parameter_definition_id), 
        add constraint TMFC_PARAM_DEF_ID_PROFFV 
        foreign key (parameter_definition_id) 
        references TMTB_PARAMETER_DEFINITION (id);

    alter table TMTB_PROFILE_PARAMETER_VALUE 
        add index TMFC_PARAMDEF_ID_PRO_PARAM_VAL (parameter_definition_id), 
        add constraint TMFC_PARAMDEF_ID_PRO_PARAM_VAL 
        foreign key (parameter_definition_id) 
        references TMTB_PARAMETER_DEFINITION (id);

    alter table TMTB_PROFILE_PARAMETER_VALUE 
        add index TMFC_TERM_PRO_ID_PRO_PARAM_VAL (terminal_profile_id), 
        add constraint TMFC_TERM_PRO_ID_PRO_PARAM_VAL 
        foreign key (terminal_profile_id) 
        references TMTB_TERMINAL_PROFILE (id);

    alter table TMTB_RELEASE_FILE 
        add index TMFC_REL_ID_REL_FILE (release_id), 
        add constraint TMFC_REL_ID_REL_FILE 
        foreign key (release_id) 
        references TMTB_RELEASE (id);

    alter table TMTB_RELEASE_TMTB_APPLICATION 
        add index TMFC_REL_ID_APP (TMTB_RELEASE_id), 
        add constraint TMFC_REL_ID_APP 
        foreign key (TMTB_RELEASE_id) 
        references TMTB_RELEASE (id);

    alter table TMTB_RELEASE_TMTB_APPLICATION 
        add index TMFC_APP_ID_REL (application_id), 
        add constraint TMFC_APP_ID_REL 
        foreign key (application_id) 
        references TMTB_APPLICATION (id);

    alter table TMTB_RELEASE_TMTB_MODEL 
        add index TMFC_REL_ID_MODEL (TMTB_RELEASE_id), 
        add constraint TMFC_REL_ID_MODEL 
        foreign key (TMTB_RELEASE_id) 
        references TMTB_RELEASE (id);

    alter table TMTB_RELEASE_TMTB_MODEL 
        add index TMFC_MODEL_ID_REL (model_id), 
        add constraint TMFC_MODEL_ID_REL 
        foreign key (model_id) 
        references TMTB_MODEL (id);

    alter table TMTB_TERMINAL_APPLICATION 
        add index TMFC_REL_ID_TERM_APP (release_id), 
        add constraint TMFC_REL_ID_TERM_APP 
        foreign key (release_id) 
        references TMTB_RELEASE (id);

    alter table TMTB_TERMINAL_APPLICATION 
        add index TMFC_APP_ID_TMTB_TERM_APP (application_id), 
        add constraint TMFC_APP_ID_TMTB_TERM_APP 
        foreign key (application_id) 
        references TMTB_APPLICATION (id);

    alter table TMTB_TERMINAL_APPLICATION 
        add index TMFC_TERM_ID__TERM_APP (terminal_id), 
        add constraint TMFC_TERM_ID__TERM_APP 
        foreign key (terminal_id) 
        references TMTB_TMS_TERMINAL (id);

    alter table TMTB_TERMINAL_BL_DOWNLOAD_JOB 
        add index TMFC_BL_DJOB_ID_TERM_BL_DJOB (blacklist_download_job_id), 
        add constraint TMFC_BL_DJOB_ID_TERM_BL_DJOB 
        foreign key (blacklist_download_job_id) 
        references TMTB_BLACKLIST_DOWNLOAD_JOB (id);

    alter table TMTB_TERMINAL_BL_DOWNLOAD_JOB 
        add index TMFC_BL_VER_ID_TERM_BL_DL_JOB (blacklist_version_id), 
        add constraint TMFC_BL_VER_ID_TERM_BL_DL_JOB 
        foreign key (blacklist_version_id) 
        references TMTB_BLACKLIST_VERSION (id);

    alter table TMTB_TERMINAL_BL_DOWNLOAD_JOB 
        add index TMFC_VEH_ID_TERM_BL_DL_JOB (vehicle_id), 
        add constraint TMFC_VEH_ID_TERM_BL_DL_JOB 
        foreign key (vehicle_id) 
        references TMVW_Vehicle (vehicle_id);

    alter table TMTB_TERMINAL_BL_DOWNLOAD_JOB 
        add index TMFC_TERM_ID_TERM_BL_DL_JOB (terminal_id), 
        add constraint TMFC_TERM_ID_TERM_BL_DL_JOB 
        foreign key (terminal_id) 
        references TMTB_TMS_TERMINAL (id);

    alter table TMTB_TERMINAL_BL_VERSION 
        add index TMFC_BL_VER_ID_TERM_BL_VER (blacklist_version_id), 
        add constraint TMFC_BL_VER_ID_TERM_BL_VER 
        foreign key (blacklist_version_id) 
        references TMTB_BLACKLIST_VERSION (id);

    alter table TMTB_TERMINAL_BL_VERSION 
        add index TMFC_TERM_ID_TERM_BL_VER (terminal_id), 
        add constraint TMFC_TERM_ID_TERM_BL_VER 
        foreign key (terminal_id) 
        references TMTB_TMS_TERMINAL (id);

    alter table TMTB_TERMINAL_LOG 
        add index TMFC_TERM_ID_TERM_LOG (terminal_id), 
        add constraint TMFC_TERM_ID_TERM_LOG 
        foreign key (terminal_id) 
        references TMTB_TMS_TERMINAL (id);

    alter table TMTB_TERMINAL_LOG_RECORD 
        add index TMFC_TERM_LOG_ID_TERM_LOG_REC (terminal_log_id), 
        add constraint TMFC_TERM_LOG_ID_TERM_LOG_REC 
        foreign key (terminal_log_id) 
        references TMTB_TERMINAL_LOG (id);

    alter table TMTB_TERMINAL_PROFILE 
        add index TMFC_GROUP_ID_TERM_PROF (group_id), 
        add constraint TMFC_GROUP_ID_TERM_PROF 
        foreign key (group_id) 
        references TMTB_GROUPING (id);

    alter table TMTB_TERMINAL_PROFILE 
        add index TMFC_APP_ID_TERM_PROFILE (application_id), 
        add constraint TMFC_APP_ID_TERM_PROFILE 
        foreign key (application_id) 
        references TMTB_APPLICATION (id);

    alter table TMTB_TERMINAL_PROFILE_FILE 
        add index TMFC_TERM_PRO_ID_TERM_PRO_FILE (terminal_profile_id), 
        add constraint TMFC_TERM_PRO_ID_TERM_PRO_FILE 
        foreign key (terminal_profile_id) 
        references TMTB_TERMINAL_PROFILE (id);

    alter table TMTB_TERM_APP_DOWNLOAD_JOB 
        add index TMFC_REL_ID_TERM_APP_DL_JOB (release_id), 
        add constraint TMFC_REL_ID_TERM_APP_DL_JOB 
        foreign key (release_id) 
        references TMTB_RELEASE (id);

    alter table TMTB_TERM_APP_DOWNLOAD_JOB 
        add index TMFC_APP_DJOB_ID_TERM_APP_DJOB (application_download_job_id), 
        add constraint TMFC_APP_DJOB_ID_TERM_APP_DJOB 
        foreign key (application_download_job_id) 
        references TMTB_APPLICATION_DOWNLOAD_JOB (id);

    alter table TMTB_TERM_APP_DOWNLOAD_JOB 
        add index TMFC_VEH_ID_TERM_APP_DL_JOB (vehicle_id), 
        add constraint TMFC_VEH_ID_TERM_APP_DL_JOB 
        foreign key (vehicle_id) 
        references TMVW_Vehicle (vehicle_id);

    alter table TMTB_TERM_APP_DOWNLOAD_JOB 
        add index TMFC_TERM_ID_TERM_APP_DL_JOB (terminal_id), 
        add constraint TMFC_TERM_ID_TERM_APP_DL_JOB 
        foreign key (terminal_id) 
        references TMTB_TMS_TERMINAL (id);

    alter table TMTB_TMS_TERMINAL 
        add index TMFC_MODEL_ID_TERM (model_id), 
        add constraint TMFC_MODEL_ID_TERM 
        foreign key (model_id) 
        references TMTB_MODEL (id);

    alter table TMTB_TXN_LOG 
        add index TMFC_TXN_TYPE_CODE_TXN_LOG (txn_type), 
        add constraint TMFC_TXN_TYPE_CODE_TXN_LOG 
        foreign key (txn_type) 
        references TMTB_TXN_TYPE (code);

    alter table TMTB_TXN_LOG 
        add index TMFC_TXN_RESULT_CODE_TXN_LOG (txn_result), 
        add constraint TMFC_TXN_RESULT_CODE_TXN_LOG 
        foreign key (txn_result) 
        references TMTB_TXN_RESULT (code);

    alter table TMTB_VEHICLE_GROUP 
        add index TMFC_GROUP_ID_VEH_GROUP (group_id), 
        add constraint TMFC_GROUP_ID_VEH_GROUP 
        foreign key (group_id) 
        references TMTB_GROUPING (id);

    alter table TMTB_VEHICLE_GROUP 
        add index TMFC_VEH_ID_VEH_GROUP (vehicle_id), 
        add constraint TMFC_VEH_ID_VEH_GROUP 
        foreign key (vehicle_id) 
        references TMVW_Vehicle (vehicle_id);
