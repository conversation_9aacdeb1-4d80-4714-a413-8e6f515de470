create table TMTB_COF_TXN_LOG (
        id number(19,0) not null,
        auth_code varchar2(255 char),
        company_code varchar2(4 char),
        data varchar2(999 char),
        dcp_code varchar2(255 char),
        dcp_desc varchar2(255 char),
        dcp_job_no varchar2(255 char),
        dcp_req_id varchar2(255 char),
        dcp_txn_id varchar2(255 char),
        dcp_txn_state varchar2(255 char),
        driver_id varchar2(9 char),
        expiry_date varchar2(10 char),
        fare_admin number(19,0),
        fare_amt number(19,0),
        first_dcp_call timestamp,
        fare_gst number(19,0),
        job_number varchar2(10 char),
        last_dcp_call timestamp,
        masked_can varchar2(19 char),
        mid varchar2(15 char),
        msg_type varchar2(20 char),
        mti varchar2(4 char),
        pinpad_sn varchar2(20 char),
        private_data varchar2(999 char),
        proc_code varchar2(6 char),
        resp_code varchar2(2 char),
        retry int default 0,
        reversed number(1,0),
        rrn varchar2(255 char),
        stan varchar2(6 char),
        status varchar2(12 char),
        taxi_no varchar2(12 char),
        tid varchar2(8 char),
        txn_amount number(19,0),
        txn_date varchar2(4 char),
        txn_date_time timestamp,
        txn_time varchar2(6 char),
        voided number(1,0),
        txn_result varchar2(255 char),
        primary key (id)
    );

      alter table TMTB_COF_TXN_LOG
            add constraint TMFC_RESULT_CODE_COF_TXN_LOG
            foreign key (txn_result)
            references TMTB_TXN_RESULT;