CREATE VIEW TMVW_TERM_APP_LATEST
AS
select release_id, terminal_id from
  (select terminal_id, release_id, row_number() over (partition by terminal_id order by update_date_time desc) rn
   from TMTB_TERMINAL_APPLICATION)
  where rn=1;

DROP TABLE TMVW_VEHICLE_GROUPING_SUMMARY;

CREATE VIEW TMVW_VEHICLE_GROUPING_SUMMARY  
as 
SELECT
		 Row_Number() OVER(ORDER BY g.id,
			 r.id ASC) as id,
		 g.id as group_id,
		 g.assign_date_time assign_date,
		 g.create_date_time as create_date,
		 g.name as group_name,
		 (DECODE(1 , DECODE( 1 ,DECODE( ( SELECT COUNT(*)
FROM  tmtb_vehicle_group 
WHERE	 g.id  = tmtb_vehicle_group.group_id
GROUP BY  group_id 
 ) , NULL , 1 , 0 ) , 1 , 0 ) ,0,(SELECT COUNT(*)
FROM  tmtb_vehicle_group 
WHERE	 g.id  = tmtb_vehicle_group.group_id
GROUP BY  group_id 
))) as total_vehicles,
		 (DECODE(1 , DECODE( 1 ,DECODE( r.id , NULL , 1 , 0 ) , 1 , 0 ) ,0,r.id)) as release_id,
		 r.description as description,
		 (DECODE(1 , DECODE( 1 ,DECODE( r.file_size , NULL , 1 , 0 ) , 1 , 0 ) ,0,r.file_size)) as file_size,
		 (DECODE(1 , DECODE( 1 ,DECODE( r.min_version , NULL , 1 , 0 ) , 1 , 0 ) ,0,r.min_version)) as min_version,
		 (DECODE(1 , DECODE( 1 ,DECODE( r.version , NULL , 1 , 0 ) , 1 , 0 ) ,0,r.version)) as version,
		 (DECODE(1 , DECODE( 1 ,DECODE( m.model_id , NULL , 1 , 0 ) , 1 , 0 ) ,0,m.model_id)) as model_id,
		 model.model_name as model_name
FROM  tmtb_grouping g,
	 tmtb_release r,
	(	SELECT
			 tmtb_release_id,
			 MIN(model_id) as model_id
	FROM  tmtb_release_tmtb_model rm2 
	GROUP BY  tmtb_release_id 
) m,
	 tmtb_model model 
WHERE	 g.release_id  = r.id (+)
 AND	(r.id  = m.tmtb_release_id (+))
 AND	(m.model_id  = model.id (+))
 AND	(g.deleted  = 0);
 
DROP TABLE TMVW_TERMINAL_PROFILE_SUMMARY;
 
CREATE VIEW TMVW_TERMINAL_PROFILE_SUMMARY  
as 
SELECT
		 Row_Number() OVER(ORDER BY tp.id,
			 r.id,
			 g.id ASC) as id,
		 g.id as group_id,
		 g.name as group_name,
		 r.id as release_id,
		 r.description as release_desc,
		 r.version as release_version,
		 r.min_version as min_release_version,
		 tp.create_date_time as profile_create_dt,
		 tp.effective_date as profile_effective_date,
		 a.id as app_id,
		 a.name as app_name,
		 a.version as app_version,
		 tp.profile_name as profile_name,
		 tp.id as profile_id,
		 tp.profile_version as profile_version,
		 m.model_name as model_name,
		 m.id as model_id
FROM  tmtb_release_tmtb_application ra,
	 tmtb_application a,
	 tmtb_release r,
	(	SELECT
			 tmtb_release_id,
			 MIN(model_id) as model_id
	FROM  tmtb_release_tmtb_model 
	GROUP BY  tmtb_release_id 
) rm,
	 tmtb_model m,
	 tmtb_grouping g,
	 tmtb_terminal_profile tp 
WHERE	 r.id  = rm.tmtb_release_id
 AND	(rm.model_id  = m.id)
 AND	(ra.application_id  = a.id
 AND	ra.tmtb_release_id  = r.id
 AND	tp.group_id  = g.id
 AND	tp.application_id  = a.id
 AND	tp.deleted  = 0
 AND	g.deleted  = 0
 AND	r.deleted  = 0)
UNION
 SELECT
		 0 as id,
		 g2.id as group_id,
		 g2.name as group_name,
		 r2.id as release_id,
		 r2.description as release_desc,
		 r2.version as release_version,
		 r2.min_version as min_release_version,
		 null,
		 null,
		 a2.id as app_id,
		 a2.name as app_name,
		 a2.version as app_version,
		 null,
		 0,
		 0,
		 m2.model_name as model_name,
		 m2.id as model_id
FROM  tmtb_release_tmtb_application ra2,
	 tmtb_application a2,
	 tmtb_release r2,
	(	SELECT
			 tmtb_release_id,
			 MIN(model_id) as model_id
	FROM  tmtb_release_tmtb_model 
	GROUP BY  tmtb_release_id 
) rm2,
	 tmtb_model m2,
	 tmtb_grouping g2 
WHERE	 r2.id  = rm2.tmtb_release_id
 AND	(rm2.model_id  = m2.id)
 AND	NOT (  exists
	(
 	SELECT 'x'
	FROM  tmtb_terminal_profile tp2 
	WHERE	 tp2.application_id  = a2.id
	 AND	tp2.group_id  = g2.id
	 AND	tp2.deleted  = 0
	 AND	r2.deleted  = 0
	))
 AND	ra2.application_id  = a2.id
 AND	ra2.tmtb_release_id  = r2.id
 AND	r2.deleted  = 0
UNION
 SELECT
		 0 as id,
		 g.id as group_id,
		 g.name as group_name,
		 r.id as release_id,
		 r.description as release_desc,
		 r.version as release_version,
		 r.min_version as min_release_version,
		 null,
		 null,
		 a.id as app_id,
		 a.name as app_name,
		 a.version as app_version,
		 null,
		 0,
		 0,
		 m.model_name as model_name,
		 m.id as model_id
FROM  tmtb_application a,
	 tmtb_release r,
	(	SELECT
			 tmtb_release_id,
			 MIN(model_id) as model_id
	FROM  tmtb_release_tmtb_model 
	GROUP BY  tmtb_release_id 
) rm,
	 tmtb_model m,
	 tmtb_release_tmtb_application ra,
	 tmtb_grouping g,
	 tmtb_terminal_profile tp 
WHERE	 r.id  = rm.tmtb_release_id
 AND	(rm.model_id  = m.id)
 AND	(ra.application_id  = a.id
 AND	ra.tmtb_release_id  = r.id
 AND	a.id  = tp.application_id
 AND	g.id  = tp.group_id
 AND	tp.deleted  = 1
 AND	r.deleted  = 0);
 
 
DROP TABLE TMVW_TERM_PROF_SUMMARY_VIEW;

CREATE VIEW TMVW_TERM_PROF_SUMMARY_VIEW  
as 
SELECT
		 Row_Number() OVER(ORDER BY group_id,
			 release_id,
			 app_id ASC) as id,
		 id as terminal_profile_summary_id,
		 group_id,
		 group_name,
		 release_id,
		 release_desc,
		 release_version,
		 min_release_version,
		 profile_create_dt,
		 profile_effective_date,
		 app_id,
		 app_name,
		 app_version,
		 profile_name,
		 profile_id,
		 profile_version,
		 model_name,
		 model_id
FROM  TMVW_TERMINAL_PROFILE_SUMMARY ;


DROP TABLE TMVW_APPLICATION_BIN_SUMMARY;

CREATE VIEW TMVW_APPLICATION_BIN_SUMMARY  
as 
SELECT
		 Row_Number() OVER(ORDER BY abr.id,
			 pd.id) as id,
		 abr.id as bin_range_id,
		 abr.application_id as app_id,
		 pd.id as param_definition_id,
		 pd.type as type,
		 abr.pan_high as pan_high,
		 abr.pan_high_type as pan_high_type,
		 abr.pan_high_additional_info as pan_high_info,
		 abr.pan_low as pan_low,
		 abr.pan_low_type as pan_low_type,
		 abr.pan_low_additional_info as pan_low_info,
		 abv.value as bin_value,
		 pd.name as bin_param_name,
		 pd.description as bin_description,
		 pd.additional_info as bin_info
FROM  tmtb_application_bin_range abr,
	 tmtb_application_bin_value abv,
	 tmtb_parameter_definition pd 
WHERE	 abv.application_bin_range_id  = abr.id
 AND	abv.parameter_definition_id  = pd.id;
 

DROP TABLE TMVW_PROF_PARAM_VALUE_SUMMARY;

CREATE VIEW TMVW_PROF_PARAM_VALUE_SUMMARY  
as 
SELECT
		 Row_Number() OVER(ORDER BY pd.id,
			 p.terminal_profile_id) as id,
		 pd.id as parameter_definition_id,
		 p.terminal_profile_id as terminal_profile_id,
		 pd.application_id as application_id,
		 pd.additional_info as additional_info,
		 pd.name as name,
		 pd.description as description,
		 pd.type as type,
		 pd.param_level as param_level,
		 p.value as value
FROM  tmtb_profile_parameter_value p,
	 tmtb_parameter_definition pd 
WHERE	 p.parameter_definition_id  = pd.id;
 

DROP TABLE TMVW_PROFILE_BIN_VALUE_SUMMARY;


CREATE VIEW TMVW_PROFILE_BIN_VALUE_SUMMARY  
as 
SELECT
		 Row_Number() OVER(ORDER BY pbr.id,
			 pd.id) as id,
		 pbr.id as profile_bin_range_id,
		 pbr.terminal_profile_id as terminal_profile_id,
		 pd.application_id as app_id,
		 pd.id as param_definition_id,
		 pd.type as type,
		 pbr.pan_high as pan_high,
		 pbr.pan_high_type as pan_high_type,
		 pbr.pan_high_additional_info as pan_high_info,
		 pbr.pan_low as pan_low,
		 pbr.pan_low_type as pan_low_type,
		 pbr.pan_low_additional_info as pan_low_info,
		 pbv.value as bin_value,
		 pd.name as bin_param_name,
		 pd.description as bin_description,
		 pd.additional_info as bin_info
FROM  tmtb_profile_bin_range pbr,
	 tmtb_profile_bin_value pbv,
	 tmtb_parameter_definition pd 
WHERE	 pbv.profile_bin_range_id  = pbr.id
 AND	pbv.parameter_definition_id  = pd.id ;
 

DROP TABLE TMVW_PROFILE_PARAM_VALUE_VIEW;

CREATE VIEW TMVW_PROFILE_PARAM_VALUE_VIEW  
as 
SELECT
		 Row_Number() OVER(ORDER BY p.terminal_profile_id,
			 p.parameter_definition_id) as id,
		 p.terminal_profile_id as terminal_profile_id,
		 p.parameter_definition_id as parameter_definition_id,
		 p.value as value
FROM  tmtb_profile_parameter_value p;


DROP TABLE TMVW_PROFILE_BIN_VALUE_VIEW;

CREATE VIEW TMVW_PROFILE_BIN_VALUE_VIEW  
as 
SELECT
		 Row_Number() OVER(ORDER BY p.profile_bin_range_id,
			 p.parameter_definition_id) as id,
		 p.profile_bin_range_id as profile_bin_range_id,
		 p.parameter_definition_id as parameter_definition_id,
		 p.value as value
FROM  tmtb_profile_bin_value p ;
 

DROP TABLE TMVW_RELEASE_PACKAGE_SUMMARY;

CREATE VIEW TMVW_RELEASE_PACKAGE_SUMMARY  
as 
SELECT
		 r.id as releaseId,
		 r.description as description,
		 r.file_size as file_size,
		 r.version as version,
		 r.min_version as min_version,
		 r.load_date_time as load_date_time,
		 (DECODE(1 , DECODE( 1 ,DECODE( ( SELECT COUNT(DISTINCT ta.terminal_id)
FROM  tmtb_terminal_application ta 
WHERE	 r.id  = ta.release_id
GROUP BY  ta.release_id 
 ) , NULL , 1 , 0 ) , 1 , 0 ) ,0,(SELECT COUNT(DISTINCT ta.terminal_id)
FROM  tmtb_terminal_application ta 
WHERE	 r.id  = ta.release_id
GROUP BY  ta.release_id 
))) as no_terminal,
		 m.model_name as model_name,
		 (		SELECT COUNT(*)
		FROM  tmtb_grouping g 
		WHERE	 g.release_id  = r.id and g.deleted=0
) as no_groups
FROM  tmtb_release r,
	 tmtb_release_tmtb_model rm,
	 tmtb_model m 
WHERE	 r.id  = rm.tmtb_release_id (+)
 AND	(rm.model_id  = m.id)
 AND	(r.deleted  = 0);
 
DROP TABLE TMVW_VEHICLE;

CREATE VIEW TMVW_VEHICLE  
as 
SELECT distinct vehicle_id,credit_app_version ,
        epins_app_version ,
        ezlink_app_version ,
        ezlink_sam_serial_no ,
        company_id,
        firmware_ver ,
        full_black_list_can ,
        full_black_list_range ,
        ivd_model_id ,
        ivd_no ,
        master_app_version,
        nets_app_version ,
        pinpad_serial_no ,
        small_black_list_can ,
        small_black_list_range ,
        vehicle_type 
        FROM cn2_v_tms_vehicle;

DROP TABLE TMVW_TERM_WITH_DOWNLOAD_JOB;


CREATE VIEW TMVW_TERM_WITH_DOWNLOAD_JOB  
as 
SELECT
		 Row_Number() OVER(ORDER BY tadj.application_download_job_id,
			 tadj.release_id,
			 tadj.vehicle_id,
			 tadj.terminal_id) as id,
		 t.id as terminal_id,
		 v.vehicle_id as vehicle_id,
		 g.id as group_id,
		 g.name as group_name,
		 t.serial_no as serial_no,
		 tadj.application_download_job_id as app_download_id,
		 tadj.status as terminal_download_status,
		 (DECODE(1 , DECODE( 1 ,DECODE( tadj.record_no , NULL , 1 , 0 ) , 1 , 0 ) ,0,tadj.record_no)) as record_no,
		 tadj.first_request_date_time as start_time,
		 tadj.last_request_date_time as last_downloaded,
		 tadj.release_id,
		 (case when(SELECT MAX(record_no)
		FROM  tmtb_release_file rf 
		WHERE	 rf.release_id  = rel.id
)is null then 0 else (SELECT MAX(record_no)
		FROM  tmtb_release_file rf 
		WHERE	 rf.release_id  = rel.id)end) as total_record_no
FROM  tmtb_term_app_download_job tadj,
	 TMVW_VEHICLE v,
	 tmtb_release_tmtb_model rm,
	 tmtb_model m,
	 tmtb_release rel,
	 tmtb_application_download_job adj,
	 tmtb_grouping g,
	 tmtb_tms_terminal t,
	 tmtb_vehicle_group vg 
WHERE	 tadj.vehicle_id  = v.vehicle_id
 AND	tadj.application_download_job_id  = adj.id
 AND	tadj.release_id  = rel.id
 AND	tadj.terminal_id  = t.id
 AND	rel.id  = g.release_id
 AND	rel.id  = rm.tmtb_release_id
 AND	tadj.terminal_id  = t.id
 AND	tadj.vehicle_id  = v.vehicle_id
 AND	rm.model_id  = m.id
 AND	v.vehicle_id  = vg.vehicle_id
 AND	vg.group_id  = g.id
 AND	g.deleted  = 0
 AND	t.deleted  = 0
 AND    t.vehicle_unpaired=0
 AND	tadj.status  = 1;
 

DROP TABLE TMVW_TERM_WITH_DOWNLOADED_APP;

CREATE VIEW TMVW_TERM_WITH_DOWNLOADED_APP  
as 
SELECT DISTINCT
		 ta.terminal_id as terminal_id,
		 t.serial_no as serial_no,
		 g.id as group_id,
		 r.id as release_id,
		 t.vehicle_no as vehicle_id
FROM  tmtb_terminal_application ta,
	 tmtb_tms_terminal t,
	 tmtb_model m,
	 tmtb_application a,
	 tmtb_release_tmtb_application ra,
	 tmtb_release r,
	 tmtb_grouping g,
	 tmtb_vehicle_group vg 
WHERE	 ta.terminal_id  = t.id
 AND	t.model_id  = m.id
 AND	ta.application_id  = a.id
 AND	ta.release_id  = r.id
 AND	t.deleted  = 0
 AND    t.vehicle_unpaired=0
 AND	a.id  = ra.application_id
 AND	ra.tmtb_release_id  = r.id
 AND	r.id  = g.release_id
 AND	t.vehicle_no  = vg.vehicle_id
 AND	vg.group_id  = g.id
 AND	g.deleted  = 0
 AND	r.deleted  = 0;
 

DROP TABLE TMVW_TERMINAL_NOT_DOWNLOADED;


CREATE VIEW TMVW_TERMINAL_NOT_DOWNLOADED  
as 
SELECT DISTINCT
		 t.id as terminal_id,
		 t.serial_no as serial_no,
		 g.id as group_id,
		 g.release_id as release_id,
		 t.vehicle_no as vehicle_id
FROM  tmtb_tms_terminal t,
	 tmtb_grouping g,
	 tmtb_vehicle_group vg 
WHERE	 t.vehicle_no  = vg.vehicle_id
 AND	vg.group_id  = g.id
 AND	NOT   exists
	(
 	SELECT 'x'
	FROM  tmtb_term_app_download_job tadj,
		 tmtb_application_download_job adj 
	WHERE	 tadj.application_download_job_id  = adj.id
	 AND	adj.group_id  = g.id
	 AND	tadj.terminal_id  = t.id
	 AND	tadj.release_id  = g.release_id
	 AND	tadj.status  = 1
	)
 AND	NOT   exists
	(
 	SELECT 'x'
	FROM  TMVW_TERM_APP_LATEST ta 
	WHERE	 ta.terminal_id  = t.id
	 AND	ta.release_id  = g.release_id
	)
 AND	t.deleted  = 0 AND t.vehicle_unpaired=0;
 

DROP TABLE TMVW_TERM_APP_DOWNLOAD_SUMMARY;

CREATE VIEW TMVW_TERM_APP_DOWNLOAD_SUMMARY  
as 
SELECT
		 Row_Number() OVER(ORDER BY job_id ASC) as id,
		 A.*
FROM (	SELECT DISTINCT adj.id as job_id,adj.start_window,
			 adj.end_window, adj.concurrent_download, adj.create_date_time as creation_date,
			 r.id as release_id, r.description as description,
			 r.version as version, m.id as model_id,
			 m.model_name as model_name, adj.status as job_status, g.id as group_id,
       (case when 
          ( SELECT COUNT(DISTINCT ta.terminal_id)
          FROM  TMVW_TERM_APP_LATEST ta, tmtb_tms_terminal t, tmtb_release r2, tmtb_grouping g2, tmtb_vehicle_group vg 
          WHERE	 ta.release_id  = r2.id AND	r2.id  = g2.release_id
          AND	g2.id  = g.id AND	ta.terminal_id  = t.id
          AND	t.vehicle_no  = vg.vehicle_id AND	vg.group_id  = g2.id
          )is null then 0 
          else (SELECT COUNT(DISTINCT ta.terminal_id)
          FROM  TMVW_TERM_APP_LATEST ta,
          tmtb_tms_terminal t,tmtb_release r2, tmtb_grouping g2, tmtb_vehicle_group vg 
          WHERE	 ta.release_id  = r2.id AND	r2.id  = g2.release_id
          AND	g2.id  = g.id AND	ta.terminal_id  = t.id
          AND	t.vehicle_no  = vg.vehicle_id AND	vg.group_id  = g2.id
          AND   t.vehicle_unpaired=0
          )end)as completed,
          (case when ( SELECT COUNT(*)
FROM  tmtb_term_app_download_job d,
	 tmtb_grouping g2 
WHERE	 d.application_download_job_id  = adj.id
 AND	d.status  = 1
 AND	d.release_id  = r.id
 AND	r.deleted  = 0
 AND	r.id  = g2.release_id
 AND	g2.id  = g.id
 ) is null then 0 else (SELECT COUNT(*)
FROM  tmtb_term_app_download_job d,
	 tmtb_grouping g2 
WHERE	 d.application_download_job_id  = adj.id
 AND	d.status  = 1
 AND	d.release_id  = r.id
 AND	r.deleted  = 0
 AND	r.id  = g2.release_id
 AND	g2.id  = g.id
)end)as pending,
(case when ( SELECT COUNT(*)
FROM  tmvw_terminal_not_downloaded td 
WHERE	 td.group_id  = adj.group_id
 )is null then 0 else (SELECT COUNT(*)
FROM  tmvw_terminal_not_downloaded td 
WHERE	 td.group_id  = adj.group_id
)end )as not_started,
			 adj.deleted as deleted
	FROM  tmtb_application_download_job adj,
		 tmtb_grouping g,
		 tmtb_release r,
		 tmtb_release_tmtb_application ra,
		 tmtb_application app,
		 tmtb_release_tmtb_model rm,
		 tmtb_model m,
		 tmtb_term_app_download_job tadj 
	WHERE	 adj.group_id  = g.id
	 AND	(g.release_id  = r.id)
	 AND	(r.id  = ra.tmtb_release_id)
	 AND	(ra.application_id  = app.id)
	 AND	(g.release_id  = rm.tmtb_release_id)
	 AND	(rm.model_id  = m.id)
	 AND	(tadj.application_download_job_id (+) = adj.id)
) A ;


DROP TABLE TMVW_TERM_BL_COMPLETED_JOB;


CREATE VIEW TMVW_TERM_BL_COMPLETED_JOB  
as 
SELECT
		 Row_Number() OVER(ORDER BY tbv.terminal_id ASC) as id,
		 t.serial_no as serial_no,
		 t.vehicle_no as vehicle_id,
		 m.model_name as model_name,
		 tbv.blacklist_version_id as blacklist_version_id,
		 tbv.update_date_time as update_date_time,
		 bv.version as blacklist_version
FROM  tmtb_terminal_bl_version tbv,
	 tmtb_blacklist_version bv,
	 tmtb_tms_terminal t,
	 tmtb_model m 
WHERE	 tbv.terminal_id  = t.id
 AND	tbv.blacklist_version_id  = bv.id
 AND	t.model_id  = m.id
 AND	bv.status  = 1
 AND    t.vehicle_unpaired=0
 AND	blacklist_version_id  in
	(
 	SELECT id
 FROM
(	SELECT id
	FROM  tmtb_blacklist_version bv2 
	WHERE	 bv2.status  = 1
ORDER BY bv2.version DESC 
) tmtb_blacklist_version 	WHERE	 ROWNUM  < 2
	);
 

DROP TABLE TMVW_TERM_BL_WITH_DOWNLOAD_JOB;


CREATE VIEW TMVW_TERM_BL_WITH_DOWNLOAD_JOB  
as 
SELECT
		 t.id as terminal_id,
		 bv.id as blacklist_version_id,
		 tbdj.vehicle_id as vehicle_id,
		 t.serial_no as serial_no,
		 m.model_name as model,
		 m.description as model_description,
		 tbdj.blacklist_download_job_id as blacklist_download_id,
		 tbdj.status as terminal_bl_download_status,
     (case when (tbdj.record_no) is null then 0 else (tbdj.record_no) end) as record_no,
		 tbdj.first_request_date_time as start_time,
		 tbdj.last_request_date_time as last_downloaded,
		 (		SELECT MAX(record_no)
		FROM  tmtb_blacklist_file bf 
		WHERE	 bf.blacklist_version_id  = bv.id
) as total_record_no
FROM  tmtb_tms_terminal t,
	 tmtb_terminal_bl_download_job tbdj,
	 tmtb_blacklist_download_job bdj,
	 tmtb_blacklist_version bv,
	 tmtb_model m 
WHERE	 tbdj.blacklist_download_job_id  = bdj.id
 AND	tbdj.terminal_id  = t.id
 AND	tbdj.blacklist_version_id  = bv.id
 AND	t.model_id  = m.id
 AND	t.deleted  = 0
 AND    t.vehicle_unpaired=0
 AND	tbdj.status  = 1
 AND	bv.id  =
	(
 	SELECT id
 FROM
(	SELECT id
	FROM  tmtb_blacklist_version 
	WHERE	 status  = 1
ORDER BY version DESC 
) tmtb_blacklist_version 	WHERE	 ROWNUM  < 2
	);
 


DROP TABLE TMVW_TERM_BL_NOT_DOWNLOADED;


CREATE VIEW TMVW_TERM_BL_NOT_DOWNLOADED  
as 
SELECT
		 Row_Number() OVER(ORDER BY t.id,
			 m.id ASC) as id,
		 t.id as terminal_id,
		 t.serial_no as serial_no,
		 t.upload_log as upload_log,
		 t.vehicle_no as vehicle_id,
		 m.id as model_id,
		 m.model_name as model_name
FROM  tmtb_tms_terminal t,
	 tmtb_model m 
WHERE	 t.model_id  = m.id
 AND	NOT   exists
	(
 	SELECT 'x'
	FROM  tmtb_terminal_bl_download_job tbdj,
		 tmtb_blacklist_download_job bdj 
	WHERE	 tbdj.blacklist_download_job_id  = bdj.id
	 AND	tbdj.terminal_id  = t.id
	 AND	tbdj.vehicle_id  = t.vehicle_no
	 AND	tbdj.status  = 1
	 AND	tbdj.blacklist_version_id  =
		(
 		SELECT id
 FROM
(		SELECT id
		FROM  tmtb_blacklist_version 
		WHERE	 status  = 1
ORDER BY version DESC 
) tmtb_blacklist_version 		WHERE	 ROWNUM  < 2
		)
	)
 AND	NOT   exists
	(
 	SELECT 'x'
	FROM  tmtb_terminal_bl_version tbv,
		 tmtb_blacklist_version bv 
	WHERE	 tbv.terminal_id  = t.id
	 AND	tbv.blacklist_version_id  = bv.id
	 AND	bv.status  = 1
	 AND	bv.id  =
		(
 		SELECT id
 FROM
(		SELECT id
		FROM  tmtb_blacklist_version 
		WHERE	 status  = 1
ORDER BY version DESC 
) tmtb_blacklist_version 		WHERE	 ROWNUM  < 2
		)
	)
 AND	t.deleted  = 0 AND t.vehicle_unpaired=0;
 

DROP TABLE TMVW_TERM_BL_DOWNLOAD_SUMMARY;


CREATE VIEW TMVW_TERM_BL_DOWNLOAD_SUMMARY  
as 
SELECT
		 Row_Number() OVER(ORDER BY job_id ASC) as id,
		 A.*
FROM (	SELECT DISTINCT
			 bdj.id as job_id,
			 bdj.start_window,
			 bdj.end_window,
			 bdj.concurrent_download,
			 bdj.create_date_time,
			 bdj.status as job_status,
       (case when ( SELECT COUNT(DISTINCT id)
FROM  tmvw_term_bl_completed_job d 
 ) is null then 0 else (SELECT COUNT(DISTINCT id)
FROM  tmvw_term_bl_completed_job d 
)end )as completed,
(case when ( SELECT COUNT(DISTINCT d.terminal_id)
FROM  tmtb_terminal_bl_download_job d 
WHERE	 d.blacklist_download_job_id  = bdj.id
 AND	d.status  = 1
 ) is null then 0 else (SELECT COUNT(DISTINCT d.terminal_id)
FROM  tmtb_terminal_bl_download_job d 
WHERE	 d.blacklist_download_job_id  = bdj.id
 AND	d.status  = 1
)end )as pending,
(case when ( SELECT COUNT(DISTINCT td.terminal_id)
FROM  tmvw_term_bl_not_downloaded td 
 ) is null then 0 else (SELECT COUNT(DISTINCT td.terminal_id)
FROM  tmvw_term_bl_not_downloaded td 
)end) as not_started, bdj.deleted as deleted,
(SELECT schedule_date
 FROM
			 (			SELECT bv.schedule_date
			FROM  tmtb_blacklist_version bv 
			WHERE bv.status  = 1
			ORDER BY version DESC )
			 tmtb_blacklist_version 	WHERE	 ROWNUM  < 2
) as schedule_date
	FROM  tmtb_blacklist_download_job bdj,
		 tmtb_terminal_bl_download_job tbdj,
		 tmtb_tms_terminal t,
		 tmtb_model m 
	WHERE	 bdj.id = tbdj.blacklist_download_job_id (+)
	 AND	(tbdj.terminal_id  = t.id (+))
	 AND	(t.model_id  = m.id (+))
) A  ;
 


DROP TABLE TMVW_TERM_APP_SUMMARY;

CREATE VIEW TMVW_TERM_APP_SUMMARY  
as 
SELECT
		 Row_Number() OVER(ORDER BY t.id ASC) as id,
		 t.id as terminal_id,
		 t.serial_no as serial_no,
		 t.vehicle_no as vehicle_id,
		 a.id as application_id,
		 a.version as app_version,
		 a.name as app_name,
		 a.description as description,
		 ta.update_date_time as update_date_time
FROM  tmtb_tms_terminal t,
	 tmtb_terminal_application ta,
	 tmtb_release r,
	 tmtb_application a 
WHERE	 ta.release_id  = r.id
 AND	t.id  = ta.terminal_id
 AND	ta.application_id  = a.id;
 
 
DROP TABLE TMVW_OTA_STATUS_REPORT_SUMMARY;

CREATE VIEW TMVW_OTA_STATUS_REPORT_SUMMARY  
as 
SELECT 
                                g.id as group_id, 
                                g.name as group_name, 
     (case when (r.description) is null then 'No Release' else (r.description)end )as release_desc, 
     (case when (r.version) is null then 0 else (r.version)end ) as version, 
     (case when (r.file_size) is null then 0 else (r.file_size)end)  as file_size, 
                                (                             SELECT COUNT(vg.vehicle_id) 
                                FROM  tmtb_vehicle_group vg 
                                WHERE vg.group_id  = g.id 
) as total_vehicles, 
                                (                             SELECT COUNT(distinct tnd.vehicle_id) 
                                FROM  tmvw_terminal_not_downloaded tnd 
                                WHERE tnd.group_id  = g.id 
) as not_started, 
                                (                             SELECT COUNT(distinct twa.vehicle_id) 
                                FROM  tmvw_term_with_downloaded_app twa 
                                WHERE twa.group_id  = g.id 
) as completed, 
                                (                             SELECT COUNT(distinct twj.vehicle_id) 
                                FROM  tmvw_term_with_download_job twj 
                                WHERE twj.group_id  = g.id 
) as downloading, 
    (case when ( select sum(( CAST(tadj.last_request_date_time AS DATE)  -  CAST(tadj.first_request_date_time AS DATE) ) *24*60*60) from tmtb_term_app_download_job tadj, tmtb_application_download_job adj 
where tadj.application_download_job_id =adj.id 
and tadj.release_id = r.id 
and adj.group_id = g.id 
and tadj.status=0 
) is null then 0 else (select sum(( CAST(tadj.last_request_date_time AS DATE)  -  CAST(tadj.first_request_date_time AS DATE) ) *24*60*60) from tmtb_term_app_download_job tadj, tmtb_application_download_job adj 
where tadj.application_download_job_id =adj.id 
and tadj.release_id = r.id 
and adj.group_id = g.id 
and tadj.status=0 
)end )as time_diff, 
(case when (select count(*) from tmtb_term_app_download_job tadj, tmtb_application_download_job adj 
where tadj.application_download_job_id =adj.id 
and tadj.release_id = r.id 
and adj.group_id = g.id 
and tadj.status=0 
group by adj.group_id ) is null then 0 else (select count(*) from tmtb_term_app_download_job tadj, tmtb_application_download_job adj 
where tadj.application_download_job_id =adj.id 
and tadj.release_id = r.id 
and adj.group_id = g.id 
and tadj.status=0 
group by adj.group_id )end)as online_completed 
FROM  tmtb_grouping g, 
                tmtb_release r 
WHERE g.release_id  = r.id (+) 
AND      (g.deleted  = 0); 

 


CREATE VIEW tmvw_term_latest_app_version as
SELECT app.version, term.serial_no, app.name, ta.update_date_time
FROM tmtb_terminal_application ta , tmtb_tms_terminal term, tmtb_application app
where ta.terminal_id = term.id
 AND ta.application_id = app.id and ta.update_date_time= (
 select max(ta2.update_date_time) from tmtb_terminal_application ta2 , tmtb_tms_terminal term2, tmtb_application app2 
 where term2.serial_no=term.serial_no and app2.name=app.name and ta2.terminal_id = term2.id and ta2.application_id = app2.id)
order by term.serial_no, app.name, app.version;

DROP TABLE TMVW_FAILED_DOWNLOAD_VEHICLES ;


CREATE VIEW TMVW_FAILED_DOWNLOAD_VEHICLES  
as 
SELECT Row_Number() OVER(ORDER BY v.vehicle_id,v.driver_name ASC) as id,
		 v.vehicle_id as vehicle_id,
		 v.vehicle_type as vehicle_type,
		 v.company_id as company_id,
		 v.firmware_ver as firmware_ver,
		 v.ivd_model_id as ivd_model_id,
		 v.update_dt as update_dt,
		 f.update_date_time as download_dt,
		 f.serial_no as serial_no,
		 v.ezlink_sam_serial_no as ezlink_sam_serial_no,
     (case when (SELECT version
FROM TMVW_TERM_LATEST_APP_VERSION latest_version
WHERE	latest_version.serial_no =f.serial_no
 AND	latest_version.name  = 'MASTERAPP' 
) is null then 0 else (SELECT version
FROM TMVW_TERM_LATEST_APP_VERSION latest_version
WHERE	latest_version.serial_no =f.serial_no
 AND	latest_version.name  = 'MASTERAPP'
)end )as master_app_version,
(case when (SELECT version
FROM TMVW_TERM_LATEST_APP_VERSION latest_version
WHERE	latest_version.serial_no =f.serial_no
 AND	latest_version.name  = 'CREDITAPP'
)
is null then 0 else (SELECT version
FROM TMVW_TERM_LATEST_APP_VERSION latest_version
WHERE	latest_version.serial_no =f.serial_no
 AND	latest_version.name  = 'CREDITAPP'
)end ) as credit_app_version,
 (case when (SELECT version
FROM TMVW_TERM_LATEST_APP_VERSION latest_version
WHERE	latest_version.serial_no =f.serial_no
 AND	latest_version.name  = 'NETSAPP'
 )is null then 0 else (SELECT version
FROM TMVW_TERM_LATEST_APP_VERSION latest_version
WHERE	latest_version.serial_no =f.serial_no
 AND	latest_version.name  = 'NETSAPP'
)end ) as nets_app_version, 
(case when (SELECT version
FROM TMVW_TERM_LATEST_APP_VERSION latest_version
WHERE	latest_version.serial_no =f.serial_no
 AND	latest_version.name  = 'EPINSAPP'
 )is null then 0 else (SELECT version
FROM TMVW_TERM_LATEST_APP_VERSION latest_version
WHERE	latest_version.serial_no =f.serial_no
 AND	latest_version.name  = 'EPINSAPP'
)end ) as epins_app_version,
(case when (SELECT version
FROM TMVW_TERM_LATEST_APP_VERSION latest_version
WHERE	latest_version.serial_no =f.serial_no
 AND	latest_version.name  = 'EZLINKAPP'
 ) is null then 0 else (SELECT version
FROM TMVW_TERM_LATEST_APP_VERSION latest_version
WHERE	latest_version.serial_no =f.serial_no
 AND	latest_version.name  = 'EZLINKAPP'
)end ) as ezlink_app_version,
 v.full_black_list_range as full_black_list_range,
		 v.full_black_list_can as full_black_list_can,
		 v.small_black_list_range as small_black_list_range,
		 v.small_black_list_can as small_black_list_can,
		 v.driver_name as driver_name,
		 v.driver_nric as driver_nric,
		 v.driving_mode as driving_mode,
		 v.phone1 as phone1,
		 v.phone2 as phone2,
		 f.current_release_id as current_release,
		 f.download_release_id as download_release,
		 f.failed_reason,
     (case when (r.description) is null then '0' else (r.description)end ) as current_release_description,
     (case when (r.version) is null then 0 else (r.version)end)as current_release_version, 
     (case when ( SELECT description
                  FROM  tmtb_release r2 
                  WHERE	 r2.id  = f.download_release_id ) is null then '0' else ( SELECT description
FROM  tmtb_release r2 
WHERE	 r2.id  = f.download_release_id
 )end ) as download_release_description,
    (case when ( SELECT version
FROM  tmtb_release r2 
WHERE	 r2.id  = f.download_release_id
 ) is null then 0 else ( SELECT version
FROM  tmtb_release r2 
WHERE	 r2.id  = f.download_release_id
 )end) as download_release_version
FROM  tmtb_failed_download f,
	 tmtb_release r,
	 CN2_V_TMS_VEHICLE v 
WHERE	 f.current_release_id  = r.id (+)
 AND	(f.vehicle_no  = v.vehicle_id
 AND	f.deleted  = 0);



DROP TABLE TMVW_TERMINAL_SUMMARY;

CREATE VIEW TMVW_TERMINAL_SUMMARY  
as 
SELECT
		 t.id as id,
		 r.description as release_desc,
		 (DECODE(1 , DECODE( 1 ,DECODE( (r.version) , NULL , 1 , 0 ) , 1 , 0 ) ,0,(r.version))) as release_version,
		 t.vehicle_no as vehicle_no,
		 t.serial_no as serial_no,
		 t.update_date_time as update_date_time,
		 t.upload_log as upload_log,
		 m.id as model_id,
		 m.model_name as model_name,
		 t.vehicle_unpaired as vehicle_unpaired,
		 (DECODE(1 , DECODE( 1 ,DECODE( 1 , (SELECT 1
FROM  tmvw_term_with_downloaded_app twdp 
WHERE	 twdp.terminal_id  = t.id
 AND	twdp.release_id  = r.id
 AND	ROWNUM  < 2
 ),1,0 ) , 1 , 0 ) ,'Completed', DECODE( 1 ,DECODE( 1 , (SELECT 1
FROM  tmvw_term_with_download_job twdj 
WHERE	 twdj.terminal_id  = t.id
 AND	twdj.release_id  = r.id
 AND	ROWNUM  < 2
 ),1,0 ) , 1 , 0 ) ,'Downloading','Not Started')) as download_status,
		 (DECODE(1 , DECODE( 1 ,DECODE( ( SELECT record_no
FROM  tmtb_term_app_download_job tadj 
WHERE	 tadj.release_id  = r.id
 AND	tadj.terminal_id  = t.id
 AND	tadj.status  = 1
 AND	ROWNUM  < 2
 ) , NULL , 1 , 0 ) , 1 , 0 ) ,0,(SELECT record_no
FROM  tmtb_term_app_download_job tadj 
WHERE	 tadj.release_id  = r.id
 AND	tadj.terminal_id  = t.id
 AND	tadj.status  = 1
 AND	ROWNUM  < 2
))) as record_no,
		 (case when(		SELECT MAX(record_no)
		FROM  tmtb_release_file rf 
		WHERE	 rf.release_id  = r.id
)is null then 0 else (		SELECT MAX(record_no)
		FROM  tmtb_release_file rf 
		WHERE	 rf.release_id  = r.id
)end ) as total_record_no
FROM  tmtb_tms_terminal t,
	 tmtb_vehicle_group vg,
	 tmtb_model m,
	 tmtb_grouping g,
	 tmtb_release r 
WHERE	 g.release_id  = r.id (+)
 AND	(t.model_id  = m.id
 AND	t.vehicle_no  = vg.vehicle_id
 AND	vg.group_id  = g.id
 AND	t.deleted  = 0);

DROP TABLE TMVW_GROUP_REL_HISTORY_SUMMARY;

CREATE VIEW TMVW_GROUP_REL_HISTORY_SUMMARY  
as 
SELECT
		 Row_Number() OVER(ORDER BY g.id,
			 r.id,
			 g.id ASC) as id,
		 g.id as group_id,
		 g.name as group_name,
		 r.id as release_id,
		 r.description as release_desc,
		 r.version as release_version,
		 (SELECT model_name
		FROM
			(		SELECT model_name, tmtb_release_id
					FROM  tmtb_release_tmtb_model rm,
					tmtb_model m 
					WHERE	 rm.model_id  = m.id
					
					ORDER BY rm.model_id ASC )m2
				WHERE	 ROWNUM  < 2 and m2.tmtb_release_id=r.id
) as model_name
FROM  tmtb_group_release_history h,
	 tmtb_grouping g,
	 tmtb_release r 
WHERE	 h.group_id  = g.id
 AND	h.release_id  = r.id
 AND	r.deleted  = 0
 AND	g.deleted  = 0;
 
DROP TABLE TMVW_VEHICLE_GROUPING_LIST;

CREATE VIEW TMVW_VEHICLE_GROUPING_LIST  
as 
SELECT v.vehicle_id as vehicle_id,v.firmware_ver as firmware_ver,v.vehicle_type as vehicle_type,v.ivd_model_id as ivd_model_id, vg.assign_date_time,(case when (vg.group_id is null) then 0 else (vg.group_id) end) as group_id, g.name as group_name
FROM tmvw_vehicle v left join tmtb_vehicle_group vg on v.vehicle_id=vg.vehicle_id left join tmtb_grouping g on vg.group_id= g.id;

DROP TABLE TMVW_APPLICATION_FEE_SUMMARY;

CREATE VIEW TMVW_APPLICATION_FEE_SUMMARY  
as 
SELECT 
		Row_Number() OVER(ORDER BY af.id,
			 pd.id) as id,
		(case when af.id is null then 0 else af.id end) as fee_id,
		 pd.application_id as app_id,
		 pd.id as param_definition_id,
		 pd.type as type,
		 (case when af.description is null then pd.description else af.description end) as description,
		 (case when af.desc_type is null then pd.type else af.desc_type end) as desc_type,
		 (case when af.desc_additional_info is null then pd.additional_info else af.desc_additional_info end)  as desc_info,
		 afv.fee_value  as fee_value,
		 pd.name as fee_param_name,
		 pd.description as fee_description,
		 pd.additional_info as fee_info
     FROM tmtb_parameter_definition pd left join tmtb_application_fee_value afv on pd.id=afv.parameter_definition_id
     left join tmtb_application_fee af on afv.application_fee_id  = af.id
     where pd.param_level=2;

DROP TABLE TMVW_PROFILE_FEE_VALUE_SUMMARY;

CREATE VIEW TMVW_PROFILE_FEE_VALUE_SUMMARY  
as 
SELECT
 Row_Number() OVER(ORDER BY pf.id,
			 pd.id) as id,
		(case when pf.id is null then 0 else pf.id end) as fee_id,
		(case when pf.terminal_profile_id is null then 0 else pf.terminal_profile_id end)  as terminal_profile_id,
		 pd.application_id as app_id,
		 pd.id as param_definition_id,
		 pd.type as type,
		 pf.description as description,
     (case when pf.desc_type is null then pd.type else pf.desc_type end) as desc_type,
		 (case when pf.desc_additional_info is null then pd.additional_info else pf.desc_additional_info end) as desc_info,
		 pfv.fee_value as fee_value,
		 pd.name as fee_param_name,
		 pd.description as fee_description,
		 pd.additional_info as fee_info
  FROM tmtb_parameter_definition pd left join tmtb_profile_fee_value pfv on pd.id=pfv.parameter_definition_id
     left join tmtb_profile_fee pf on pfv.profile_fee_id  = pf.id
     where pd.param_level=2;
     
--- for ezlink--
DROP TABLE batchb_vw_ezlink_double_debit;


CREATE VIEW batchb_vw_ezlink_double_debit
as 
select * from   (
select concat('A', id) as id,
               to_number(substr(transactionmessage, 61, 6), 'XXXXXX') as ptc,
               transdt,
               logdt as logdt,
               transactionMessage,
               can,
               substr(driverinfo, 1, 12) as taxino,
               substr(driverinfo, 13, 9) as driverid,
               substr(driverinfo, 22, 10) as jobno,
               16777216 -
               to_number(substr(transactionmessage, 33, 6), 'XXXXXX') as amount
        from   batchtb_ezlink_transactions
        union all
        select concat('B', id) as id,
               to_number(substr(transactionmessage, 149, 6), 'XXXXXX') as ptc,
               to_date('01-01-1995', 'DD-MM-YYYY') +
               numtodsinterval(to_number(substr(transactionmessage, 127, 8),
                                         'XXXXXXXX'),
                               'SECOND') as transdt,
               logdt as logdt,
               transactionMessage,
               can,
               '' as taxino,
               '' as driverid,
               '' as jobno,
               16777216 -
               to_number(substr(transactionmessage, 121, 6), 'XXXXXX') as amount
        from   batchtb_ezlink_transactions
        where  substr(transactionmessage, 135, 8) = '81ce0f00' and
               to_number(substr(transactionmessage, 149, 6), 'XXXXXX') not in
               (select to_number(substr(transactionmessage, 61, 6), 'XXXXXX') as ptc
                from   batchtb_ezlink_transactions)
)
order  by PTC desc;
