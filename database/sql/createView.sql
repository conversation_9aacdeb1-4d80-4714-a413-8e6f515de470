drop table TMVW_VEHICLE_GROUPING_SUMMARY
go
create view TMVW_VEHICLE_GROUPING_SUMMARY as
select Row_Number() OVER (ORDER BY g.id, r.id ASC) as id,g.id as group_id, g.assign_date_time assign_date, g.create_date_time as create_date, g.name as group_name,
 (case when (select COUNT(*) from tmtb_vehicle_group where g.id=tmtb_vehicle_group.group_id
 group by group_id) is null then 0 
 else (select COUNT(*) from tmtb_vehicle_group where g.id=tmtb_vehicle_group.group_id
 group by group_id) end)as total_vehicles,
(case when r.id is null then 0 else r.id end) as release_id,r.description as description, (case when r.file_size is null then 0 else r.file_size end) as file_size,
(case when r.min_version is null then 0 else r.min_version end) as min_version, 
(case when r.version is null then 0 else r.version end) as version,
(case when m.model_id is null then 0 else m.model_id end) as model_id, model.model_name as model_name
 from tmtb_grouping g left outer join tmtb_release r 
 on g.release_id=r.id 
 left outer join (select tmtb_release_id, min(model_id)as model_id from tmtb_release_tmtb_model rm2 group by tmtb_release_id)AS m on r.id=m.tmtb_release_id left outer join tmtb_model model on m.model_id=model.id
 where g.deleted=0 
 go
 
 drop table TMVW_TERMINAL_PROFILE_SUMMARY
 go
 
create view TMVW_TERMINAL_PROFILE_SUMMARY as
select Row_Number() OVER (ORDER BY tp.id, r.id,g.id ASC) as id, g.id as group_id,g.name as group_name,r.id as release_id, r.description as release_desc,r.version as release_version, r.min_version as min_release_version,tp.create_date_time as profile_create_dt, tp.effective_date as profile_effective_date, a.id as app_id,a.name as app_name,a.version as app_version, tp.profile_name as profile_name, tp.id as profile_id, tp.profile_version as profile_version, m.model_name as model_name, m.id as model_id 
from tmtb_release_tmtb_application ra, tmtb_application a, tmtb_release r inner join (select tmtb_release_id, min(model_id)as model_id from tmtb_release_tmtb_model group by tmtb_release_id)as rm on r.id=rm.tmtb_release_id inner join tmtb_model m on rm.model_id=m.id, tmtb_grouping g, tmtb_terminal_profile tp 
where ra.application_id=a.id
and ra.tmtb_release_id=r.id
--and g.release_id=r.id 
and tp.group_id=g.id
and tp.application_id=a.id
and tp.deleted=0 and g.deleted=0 and r.deleted=0
union
select 0 as id,g2.id as group_id,g2.name as group_name,r2.id as release_id, r2.description as release_desc,r2.version as release_version, r2.min_version as min_release_version, null, null,a2.id as app_id, a2.name as app_name,a2.version as app_version, null, 0 ,0, m2.model_name as model_name, m2.id as model_id  
from tmtb_release_tmtb_application ra2, tmtb_application a2, tmtb_release r2 inner join (select tmtb_release_id, min(model_id)as model_id from tmtb_release_tmtb_model group by tmtb_release_id)as rm2 on r2.id=rm2.tmtb_release_id inner join tmtb_model m2 on rm2.model_id=m2.id,  tmtb_grouping g2
where not exists (select 'x' from tmtb_terminal_profile tp2 where tp2.application_id=a2.id and tp2.group_id=g2.id
and tp2.deleted=0 and r2.deleted=0)
and ra2.application_id=a2.id
and ra2.tmtb_release_id=r2.id
and r2.deleted=0
union
select 0 as id, g.id as group_id,g.name as group_name,r.id as release_id, r.description as release_desc,r.version as release_version, r.min_version as min_release_version,null, null,a.id as app_id, a.name as app_name,a.version as app_version, null, 0, 0, m.model_name as model_name, m.id as model_id 
from tmtb_application a, tmtb_release r inner join (select tmtb_release_id, min(model_id)as model_id from tmtb_release_tmtb_model group by tmtb_release_id)as rm on r.id=rm.tmtb_release_id inner join tmtb_model m on rm.model_id=m.id,tmtb_release_tmtb_application ra,  tmtb_grouping g, tmtb_terminal_profile tp
where ra.application_id=a.id
and ra.tmtb_release_id=r.id
and a.id=tp.application_id and g.id=tp.group_id and tp.deleted=1
and r.deleted=0;
go


drop table TMVW_TERM_PROF_SUMMARY_VIEW;
go

create view TMVW_TERM_PROF_SUMMARY_VIEW as
select Row_Number() OVER (ORDER BY group_id,release_id,app_id ASC) as id, id as terminal_profile_summary_id, group_id,group_name,release_id, release_desc, release_version,  min_release_version, profile_create_dt,  profile_effective_date, app_id, app_name,app_version,  profile_name,  profile_id,  profile_version,model_name, model_id
from TMVW_TERMINAL_PROFILE_SUMMARY;
go



drop table TMVW_APPLICATION_BIN_SUMMARY;
go

create view TMVW_APPLICATION_BIN_SUMMARY as 
select Row_Number() OVER (ORDER BY abr.id, pd.id) as id,abr.id as bin_range_id, abr.application_id as app_id, pd.id as param_definition_id,pd.type as type, abr.pan_high as pan_high,  abr.pan_high_type as pan_high_type, abr.pan_high_additional_info as pan_high_info,abr.pan_low as pan_low, abr.pan_low_type as pan_low_type,  abr.pan_low_additional_info as pan_low_info, abv.value as bin_value, pd.name as bin_param_name, pd.description as bin_description, pd.additional_info as bin_info 
from tmtb_application_bin_range abr, tmtb_application_bin_value abv, tmtb_parameter_definition pd
where abv.application_bin_range_id = abr.id
and  abv.parameter_definition_id= pd.id;
go

drop table TMVW_PROF_PARAM_VALUE_SUMMARY;
go

create view TMVW_PROF_PARAM_VALUE_SUMMARY as
select Row_Number() OVER (ORDER BY pd.id, p.terminal_profile_id) as id, pd.id as parameter_definition_id, p.terminal_profile_id as terminal_profile_id, pd.application_id as application_id, pd.additional_info as additional_info, pd.name as name, pd.description as description, pd.type as type, pd.param_level as param_level, p.value as value
from tmtb_profile_parameter_value p, tmtb_parameter_definition pd
where p.parameter_definition_id=pd.id;
go

drop table TMVW_PROFILE_BIN_VALUE_SUMMARY;
go

create view TMVW_PROFILE_BIN_VALUE_SUMMARY as
select Row_Number() OVER (ORDER BY pbr.id, pd.id ) as id, pbr.id as profile_bin_range_id, pbr.terminal_profile_id as terminal_profile_id, pd.application_id as app_id, pd.id as param_definition_id, pd.type as type, pbr.pan_high as pan_high, pbr.pan_high_type as pan_high_type, pbr.pan_high_additional_info as pan_high_info,pbr.pan_low as pan_low, pbr.pan_low_type as pan_low_type,  pbr.pan_low_additional_info as pan_low_info, pbv.value as bin_value, pd.name as bin_param_name, pd.description as bin_description, pd.additional_info as bin_info
from tmtb_profile_bin_range pbr, tmtb_profile_bin_value pbv, tmtb_parameter_definition pd
where pbv.profile_bin_range_id= pbr.id
and pbv.parameter_definition_id=pd.id;
go

drop table TMVW_PROFILE_PARAM_VALUE_VIEW;
go

create view TMVW_PROFILE_PARAM_VALUE_VIEW as
select Row_Number() OVER (ORDER BY p.terminal_profile_id, p.parameter_definition_id) as id, p.terminal_profile_id as terminal_profile_id, p.parameter_definition_id as parameter_definition_id, p.value as value
from tmtb_profile_parameter_value p;
go

drop table TMVW_PROFILE_BIN_VALUE_VIEW;
go

create view TMVW_PROFILE_BIN_VALUE_VIEW as
select Row_Number() OVER (ORDER BY p.profile_bin_range_id, p.parameter_definition_id) as id, p.profile_bin_range_id as profile_bin_range_id, p.parameter_definition_id as parameter_definition_id, p.value as value
from tmtb_profile_bin_value p;
go

drop table TMVW_RELEASE_PACKAGE_SUMMARY;
go
create view TMVW_RELEASE_PACKAGE_SUMMARY as
select r.id as releaseId, r.description as description, r.file_size as file_size, r.version as version, r.min_version as min_version, r.load_date_time as load_date_time, 
(case when (select COUNT(distinct ta.terminal_id) from tmtb_terminal_application ta where r.id=ta.release_id
 group by ta.release_id) is null then 0 else (select COUNT(distinct ta.terminal_id) from tmtb_terminal_application ta where r.id=ta.release_id
 group by ta.release_id) end ) as no_terminal, m.model_name as model_name, (select COUNT(*) from tmtb_grouping g where g.release_id=r.id ) as no_groups
 from tmtb_release r left outer join tmtb_release_tmtb_model rm  on  r.id = rm.tmtb_release_id join tmtb_model m on rm.model_id=m.id
 --left outer join grouping g on r.id = g.release_id
  where r.deleted=0
go

drop table TMVW_TERM_WITH_DOWNLOAD_JOB;
go

create view TMVW_TERM_WITH_DOWNLOAD_JOB as
select Row_Number() OVER (ORDER BY tadj.application_download_job_id, tadj.release_id, tadj.vehicle_id, tadj.terminal_id) as id, t.id as terminal_id,v.vehicle_id as vehicle_id, g.id as group_id, g.name as group_name, t.serial_no as serial_no,tadj.application_download_job_id as app_download_id,tadj.status as terminal_download_status, (case when tadj.record_no is null then 0 else tadj.record_no end) as record_no,tadj.first_request_date_time as start_time, tadj.last_request_date_time as last_downloaded,tadj.release_id, (select max(record_no) from tmtb_release_file rf where rf.release_id=rel.id) as total_record_no
from tmtb_term_app_download_job tadj, tmtb_vehicle v, tmtb_release_tmtb_model rm,tmtb_model m, tmtb_release rel,  tmtb_application_download_job adj, tmtb_grouping g, tmtb_tms_terminal t,tmtb_vehicle_group vg
where tadj.vehicle_id=v.vehicle_id
and tadj.application_download_job_id = adj.id
and tadj.release_id=rel.id
and tadj.terminal_id=t.id
and rel.id=g.release_id
and rel.id=rm.tmtb_release_id
and tadj.terminal_id=t.id
and tadj.vehicle_id=v.vehicle_id
and rm.model_id=m.id
and v.vehicle_id=vg.vehicle_id
and vg.group_id=g.id
--and adj.group_id=grouping.id
and g.deleted=0
and t.deleted=0
and t.vehicle_unpaired=0
and tadj.status=1
go

drop table TMVW_TERM_WITH_DOWNLOADED_APP;
go


create view TMVW_TERM_WITH_DOWNLOADED_APP as
select distinct ta.terminal_id as terminal_id, t.serial_no as serial_no, g.id as group_id, r.id as release_id, t.vehicle_no as vehicle_id-- Row_Number() OVER (ORDER BY ta.terminal_id, g.id, ta.release_id asc) as id--, ta.terminal_id as terminal_id, g.id as group_id, r.id as release_id, t.serial_no as serial_no--,ta.application_id as application_id, ta.terminal_id as terminal_id, t.serial_no as serial_no, t.vehicle_no as vehicle_id,m.model_name as model, m.description as model_description, a.name as name, a.version as version,r.id as release_id, r.description as release_desc,r.version as release_version, r.file_size as file_size, 0 as terminal_download_status,g.id as group_id, g.name as group_name
from tmtb_terminal_application ta, tmtb_tms_terminal t, tmtb_model m, tmtb_application a, tmtb_release_tmtb_application ra, tmtb_release r, tmtb_grouping g,tmtb_vehicle_group vg
where ta.terminal_id=t.id
and t.model_id=m.id
and ta.application_id=a.id
and ta.release_id=r.id
and t.deleted=0
and a.id=ra.application_id
and ra.tmtb_release_id=r.id
and r.id=g.release_id
and t.vehicle_no=vg.vehicle_id
and vg.group_id=g.id
and g.deleted=0
and r.deleted=0
and t.vehicle_unpaired=0
go

drop table TMVW_TERMINAL_NOT_DOWNLOADED;
go


create view TMVW_TERMINAL_NOT_DOWNLOADED as
select distinct t.id as terminal_id, t.serial_no as serial_no, g.id as group_id, g.release_id as release_id, t.vehicle_no as vehicle_id
from tmtb_tms_terminal t, tmtb_grouping g, tmtb_vehicle_group vg
where t.vehicle_no=vg.vehicle_id
and vg.group_id=g.id
and not exists (select 'x' from tmtb_term_app_download_job tadj, tmtb_application_download_job adj where tadj.application_download_job_id=adj.id and adj.group_id=g.id and tadj.terminal_id=t.id AND tadj.release_id=g.release_id and tadj.status=1)
and not exists (select 'x' from tmtb_terminal_application ta where ta.terminal_id=t.id and ta.release_id=g.release_id)
and t.deleted = 0
go

drop table TMVW_TERM_APP_DOWNLOAD_SUMMARY;
go

create view TMVW_TERM_APP_DOWNLOAD_SUMMARY as
select Row_Number() OVER (ORDER BY job_id ASC) as id, * from
(select distinct adj.id as job_id, adj.start_window, adj.end_window, adj.concurrent_download, adj.create_date_time as creation_date, r.id as release_id,r.description as description, r.version as version,m.id as model_id, m.model_name as model_name,  adj.status as job_status, g.id as group_id,
(case when
(select COUNT( DISTINCT ta.terminal_id ) from tmtb_terminal_application ta, tmtb_tms_terminal t, tmtb_release r2, tmtb_grouping  g2, tmtb_vehicle_group vg where ta.release_id=r2.id and r2.id=g2.release_id and g2.id=g.id and ta.terminal_id=t.id and t.vehicle_no=vg.vehicle_id and vg.group_id=g2.id) is null then 0
 else (select COUNT( DISTINCT ta.terminal_id ) from tmtb_terminal_application ta, tmtb_tms_terminal t, tmtb_release r2, tmtb_grouping  g2, tmtb_vehicle_group vg where ta.release_id=r2.id and r2.id=g2.release_id and g2.id=g.id and ta.terminal_id=t.id and t.vehicle_no=vg.vehicle_id and vg.group_id=g2.id)
  end) as completed,
(case when
(select COUNT(*) from tmtb_term_app_download_job d , tmtb_grouping g2 where d.application_download_job_id=adj.id and d.status =1 and d.release_id =r.id and r.deleted=0 and r.id=g2.release_id and g2.id=g.id) is null then 0
 else (select COUNT(*) from tmtb_term_app_download_job d, tmtb_grouping g2 where d.application_download_job_id=adj.id and d.status =1 and d.release_id =r.id and r.deleted=0 and r.id=g2.release_id and g2.id=g.id)
 end) as pending,
 (case when
 (select COUNT(*) from tmvw_terminal_not_downloaded td where td.group_id=adj.group_id) is null then 0
 else (select COUNT(*) from tmvw_terminal_not_downloaded td where td.group_id=adj.group_id)
 end) as not_started, adj.deleted as deleted
from tmtb_application_download_job adj inner join 
tmtb_grouping g on adj.group_id=g.id inner join
tmtb_release r on g.release_id=r.id
inner join tmtb_release_tmtb_application ra on r.id=ra.tmtb_release_id
inner join tmtb_application app on ra.application_id =app.id
inner join tmtb_release_tmtb_model rm on g.release_id=rm.tmtb_release_id

inner join tmtb_model m on rm.model_id=m.id left join
tmtb_term_app_download_job tadj on tadj.application_download_job_id=adj.id) A
go

drop table TMVW_TERM_BL_COMPLETED_JOB
go

create view TMVW_TERM_BL_COMPLETED_JOB as
select Row_Number() OVER (ORDER BY tbv.terminal_id ASC)  as id,t.serial_no as serial_no, t.vehicle_no as vehicle_id, m.model_name as model_name, tbv.blacklist_version_id as blacklist_version_id, tbv.update_date_time as update_date_time,bv.version as blacklist_version
from tmtb_terminal_bl_version tbv, tmtb_blacklist_version bv,tmtb_tms_terminal t, tmtb_model m
where tbv.terminal_id=t.id
and tbv.blacklist_version_id=bv.id
and t.model_id=m.id
and bv.status=1
and t.vehicle_unpaired=0
and blacklist_version_id in (select top (1)id from tmtb_blacklist_version bv2 where bv2.status=1 order by bv2.version desc)

go

drop table TMVW_TERM_BL_WITH_DOWNLOAD_JOB;
go

create view TMVW_TERM_BL_WITH_DOWNLOAD_JOB as
select t.id as terminal_id,bv.id as blacklist_version_id, tbdj.vehicle_id as vehicle_id,t.serial_no as serial_no, m.model_name as model, m.description as model_description, tbdj.blacklist_download_job_id as blacklist_download_id,tbdj.status as terminal_bl_download_status, (case when tbdj.record_no is null then 0 else tbdj.record_no end) as record_no,tbdj.first_request_date_time as start_time, tbdj.last_request_date_time as last_downloaded, (select max(record_no) from tmtb_blacklist_file bf where bf.blacklist_version_id=bv.id) as total_record_no
from tmtb_tms_terminal t,tmtb_terminal_bl_download_job tbdj,tmtb_blacklist_download_job bdj, tmtb_blacklist_version bv,tmtb_model m
where tbdj.blacklist_download_job_id = bdj.id
and tbdj.terminal_id=t.id
and tbdj.blacklist_version_id=bv.id
and t.model_id=m.id
and t.deleted=0
and t.vehicle_unpaired=0
and tbdj.status=1 and bv.id =(select top 1 id from tmtb_blacklist_version where status=1 order by version desc)
go


drop table TMVW_TERM_BL_NOT_DOWNLOADED
go

create view TMVW_TERM_BL_NOT_DOWNLOADED as
select Row_Number() OVER (ORDER BY t.id, m.id ASC) as id, t.id as terminal_id, t.serial_no as serial_no, t.upload_log as upload_log, t.vehicle_no as vehicle_id, m.id as model_id, m.model_name as model_name
from tmtb_tms_terminal t, tmtb_model m
where t.model_id=m.id
and not exists (select 'x' from tmtb_terminal_bl_download_job tbdj, tmtb_blacklist_download_job bdj
				where tbdj.blacklist_download_job_id = bdj.id and tbdj.terminal_id=t.id and tbdj.vehicle_id = t.vehicle_no and tbdj.status=1
				and tbdj.blacklist_version_id =(select top 1 id from tmtb_blacklist_version where status=1 order by version desc))
and not exists (select 'x' from tmtb_terminal_bl_version tbv, tmtb_blacklist_version bv where  tbv.terminal_id=t.id and tbv.blacklist_version_id=bv.id and bv.status=1 and bv.id =(select top 1 id from tmtb_blacklist_version where status =1 order by version desc))
and t.deleted = 0
and t.vehicle_unpaired=0
go

drop table TMVW_TERM_BL_DOWNLOAD_SUMMARY
go

create view TMVW_TERM_BL_DOWNLOAD_SUMMARY as
select Row_Number() OVER (ORDER BY job_id ASC) as id, * from
(select distinct bdj.id as job_id, bdj.start_window, bdj.end_window, bdj.concurrent_download, bdj.create_date_time,bdj.status as job_status,
 (case when
(select COUNT(distinct id) from tmvw_term_bl_completed_job d ) is null then 0
 else (select COUNT(distinct id) from tmvw_term_bl_completed_job d )
 end) as completed,
(case when
(select COUNT(distinct d.terminal_id) from tmtb_terminal_bl_download_job d where d.blacklist_download_job_id=bdj.id and d.status =1) is null then 0
 else (select COUNT(distinct d.terminal_id) from tmtb_terminal_bl_download_job d where d.blacklist_download_job_id=bdj.id and d.status =1)
 end) as pending,
 (case when
 (select COUNT(distinct td.terminal_id) from tmvw_term_bl_not_downloaded td) is null then 0
 else (select COUNT(distinct td.terminal_id) from tmvw_term_bl_not_downloaded td)
 end) as not_started, bdj.deleted as deleted, (case when (tbdj.blacklist_version_id is null) then 0 else tbdj.blacklist_version_id end)as blacklist_version_id ,(select bv.schedule_date from tmtb_blacklist_version bv where bv.id = tbdj.blacklist_version_id and bv.status=1 ) as schedule_date
from tmtb_blacklist_download_job bdj left join tmtb_terminal_bl_download_job tbdj  on tbdj.blacklist_download_job_id = bdj.id
left join  tmtb_tms_terminal t on tbdj.terminal_id =t.id left join tmtb_model  m on t.model_id=m.id ) A
where blacklist_version_id =0 or blacklist_version_id =(select top 1 id from tmtb_blacklist_version where status=1 order by version desc)
go


drop table TMVW_TERM_APP_SUMMARY
go

create view TMVW_TERM_APP_SUMMARY as
select Row_Number() OVER (ORDER BY t.id ASC) as id, t.id as terminal_id,t.serial_no as serial_no, t.vehicle_no as vehicle_id, a.id as application_id,a.version as app_version,a.name as app_name, a.description as description, ta.update_date_time as update_date_time
from tmtb_tms_terminal t, tmtb_terminal_application ta, tmtb_release r,tmtb_application a
where ta.release_id = r.id
and t.id=ta.terminal_id
and ta.application_id = a.id
go

drop table TMVW_OTA_STATUS_REPORT_SUMMARY
go

create view TMVW_OTA_STATUS_REPORT_SUMMARY as
select g.id as group_id,g.name as group_name, 
(case when (r.description) is null then 'No Release' else (r.description) end) as release_desc, 
(case when (r.version) is null then '0' else (r.version)end) as version, 
(case when (r.file_size) is null then '0' else (r.file_size)end)  as file_size,
(select COUNT(vg.vehicle_id) from tmtb_vehicle_group vg where vg.group_id=g.id )as total_vehicles, 
(select COUNT(tnd.vehicle_id) from tmvw_terminal_not_downloaded tnd where tnd.group_id=g.id) as not_started,(select COUNT(twa.vehicle_id) from tmvw_term_with_downloaded_app twa where twa.group_id=g.id) as completed,
(select COUNT(twj.vehicle_id) from tmvw_term_with_download_job twj where twj.group_id=g.id) as downloading,
(case when (select datediff(second,twdj.start_time,twdj.last_downloaded) from tmvw_term_with_download_job twdj where twdj.group_id=g.id and twdj.terminal_download_status=0) is null then 0 else (select datediff(second,twdj.start_time,twdj.last_downloaded) from tmvw_term_with_download_job twdj where twdj.group_id=g.id and twdj.terminal_download_status=0) end ) as time_diff
from tmtb_grouping g left join tmtb_release r on g.release_id=r.id
where g.deleted=0
go

drop table TMVW_FAILED_DOWNLOAD_VEHICLES 
go

create view TMVW_FAILED_DOWNLOAD_VEHICLES as
select v.vehicle_id as vehicle_id, v.vehicle_type as vehicle_type, v.company_id as company_id, v.firmware_ver as firmware_ver, v.ivd_model_id as ivd_model_id,f.update_date_time as update_dt,f.serial_no as serial_no, v.ezlink_sam_serial_no as ezlink_sam_serial_no,
(case when(select top 1 version from tmtb_terminal_application ta , tmtb_tms_terminal term, tmtb_application app 
	where ta.terminal_id = term.id AND ta.application_id = app.id AND term.serial_no =f.serial_no AND	app.name='MASTERAPP' order by ta.update_date_time desc) is null then '0' else (select top 1 version from tmtb_terminal_application ta , tmtb_tms_terminal term, tmtb_application app 
	where ta.terminal_id = term.id AND ta.application_id = app.id AND term.serial_no =f.serial_no AND	app.name='MASTERAPP' order by ta.update_date_time desc)end) as master_app_version,
(case when(select top 1 version from tmtb_terminal_application ta , tmtb_tms_terminal term, tmtb_application app 
	where ta.terminal_id = term.id AND ta.application_id = app.id AND term.serial_no =f.serial_no AND	app.name='CREDITAPP' order by ta.update_date_time desc) is null then '0' else (select top 1 version from tmtb_terminal_application ta , tmtb_tms_terminal term, tmtb_application app 
	where ta.terminal_id = term.id AND ta.application_id = app.id AND term.serial_no =f.serial_no AND	app.name='CREDITAPP' order by ta.update_date_time desc)end) as credit_app_version,
(case when(select top 1 version from tmtb_terminal_application ta , tmtb_tms_terminal term, tmtb_application app 
	where ta.terminal_id = term.id AND ta.application_id = app.id AND term.serial_no =f.serial_no AND	app.name='NETSAPP' order by ta.update_date_time desc) is null then '0' else (select top 1 version from tmtb_terminal_application ta , tmtb_tms_terminal term, tmtb_application app 
	where ta.terminal_id = term.id AND ta.application_id = app.id AND term.serial_no =f.serial_no AND	app.name='NETSAPP' order by ta.update_date_time desc)end) as nets_app_version,
(case when(select top 1 version from tmtb_terminal_application ta , tmtb_tms_terminal term, tmtb_application app 
	where ta.terminal_id = term.id AND ta.application_id = app.id AND term.serial_no =f.serial_no AND	app.name='NETSAPP' order by ta.update_date_time desc) is null then '0' else (select top 1 version from tmtb_terminal_application ta , tmtb_tms_terminal term, tmtb_application app 
	where ta.terminal_id = term.id AND ta.application_id = app.id AND term.serial_no =f.serial_no AND	app.name='NETSAPP' order by ta.update_date_time desc)end) as nets_app_version,
(case when(select top 1 version from tmtb_terminal_application ta , tmtb_tms_terminal term, tmtb_application app 
	where ta.terminal_id = term.id AND ta.application_id = app.id AND term.serial_no =f.serial_no AND	app.name='EPINSAPP' order by ta.update_date_time desc) is null then '0' else (select top 1 version from tmtb_terminal_application ta , tmtb_tms_terminal term, tmtb_application app 
	where ta.terminal_id = term.id AND ta.application_id = app.id AND term.serial_no =f.serial_no AND	app.name='EPINSAPP' order by ta.update_date_time desc)end) as epins_app_version,
(case when(select top 1 version from tmtb_terminal_application ta , tmtb_tms_terminal term, tmtb_application app 
	where ta.terminal_id = term.id AND ta.application_id = app.id AND term.serial_no =f.serial_no AND	app.name='EZLINKAPP' order by ta.update_date_time desc) is null then '0' else (select top 1 version from tmtb_terminal_application ta , tmtb_tms_terminal term, tmtb_application app 
	where ta.terminal_id = term.id AND ta.application_id = app.id AND term.serial_no =f.serial_no AND	app.name='EZLINKAPP' order by ta.update_date_time desc)end) as ezlink_app_version,
v.full_black_list_range as full_black_list_range, v.full_black_list_can as full_black_list_can, v.small_black_list_range as small_black_list_range, v.small_black_list_can as small_black_list_can,v.driver_name as driver_name, v.driver_nric as driver_nric, v.driving_mode as driving_mode,v.phone1 as phone1 ,v.phone2 as phone2, f.current_release_id as current_release, f.download_release_id as download_release,f.failed_reason
	,(case when (r.description is null) then '0' else (r.description)end) as current_release_description
	,(case when (r.version is null) then '0' else (r.version)end) as current_release_version
	,(case when (select description from tmtb_release r2 where r2.id=f.download_release_id)is null then '0' else (select description from tmtb_release r2 where r2.id=f.download_release_id) end)as download_release_description
	,(case when (select version from tmtb_release r2 where r2.id= f.download_release_id)is null then '0' else (select version from tmtb_release r2 where r2.id= f.download_release_id)end) as download_release_version
from tmtb_failed_download f left outer join tmtb_release r on f.current_release_id = r.id , tmtb_vehicle v
where  f.vehicle_no=v.vehicle_id
and f.deleted=0
go

drop table TMVW_TERMINAL_SUMMARY;
go

create view TMVW_TERMINAL_SUMMARY as
select t.id as id,r.description as release_desc, (case when (r.version) is null then 0 else (r.version)end ) as release_version,t.vehicle_no as vehicle_no, t.serial_no as serial_no, t.update_date_time as update_date_time, t.upload_log as upload_log,m.id as model_id, m.model_name as model_name,t.vehicle_unpaired as vehicle_unpaired,
(case when (exists (select 'x' from tmvw_term_with_downloaded_app twdp where twdp.terminal_id = t.id and twdp.release_id=r.id) )then 'Completed' 
	  when (exists (select 'x' from tmvw_term_with_download_job twdj where twdj.terminal_id = t.id and twdj.release_id=r.id )) then 'Downloading' 
	  else 'Not Started' end) as download_status, 
	 (case when (select top 1 record_no from tmtb_term_app_download_job tadj where tadj.release_id=r.id and tadj.terminal_id= t.id and tadj.status =1) is null then 0 else (select top 1 record_no from tmtb_term_app_download_job tadj where tadj.release_id=r.id and tadj.terminal_id= t.id and tadj.status =1)end )  as record_no,
	  (case when (select max(record_no) from tmtb_release_file rf where rf.release_id=r.id)is null then 0 else (select max(record_no) from tmtb_release_file rf where rf.release_id=r.id) end ) as total_record_no
from tmtb_tms_terminal t ,tmtb_vehicle_group vg , tmtb_model m, tmtb_grouping g left outer join tmtb_release r on g.release_id=r.id
where t.model_id=m.id 
and t.vehicle_no=vg.vehicle_id
and vg.group_id=g.id
and t.deleted=0
go

drop table TMVW_GROUP_REL_HISTORY_SUMMARY;
go

create view TMVW_GROUP_REL_HISTORY_SUMMARY as
select  Row_Number() OVER (ORDER BY g.id, r.id,g.id ASC) as id,g.id as group_id, g.name as group_name, r.id as release_id,r.description as release_desc, r.version as release_version,  
(select top 1 model_name from tmtb_release_tmtb_model rm, tmtb_model m where rm.model_id=m.id and rm.tmtb_release_id=r.id 
order by rm.model_id asc)as model_name 
from tmtb_group_release_history h, tmtb_grouping g, tmtb_release r
where h.group_id=g.id and h.release_id=r.id and r.deleted=0 and g.deleted=0

