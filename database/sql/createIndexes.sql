create index IND_RELEASEFILE_ID_RECORDNO on TMTB_RELEASE_FILE(release_id, record_no);

create index IND_BLACKLISTFILE_ID_RECORDNO on TMTB_BLACKLIST_FILE(blacklist_version_id, record_no);

create index IND_TMSTERMINAL_VEHICLENO on TMTB_TMS_TERMINAL(vehicle_no);

create index IND_RELEASE_VERSION on TMTB_RELEASE(version);

create index IND_APPDLJOB_GROUP on TMTB_APPLICATION_DOWNLOAD_JOB(deleted, group_id, status);

create index IND_TERMAPPDLJOB_STATUS on TMTB_TERM_APP_DOWNLOAD_JOB(application_download_job_id, terminal_id, vehicle_id, release_id, status);

create index IND_FAILEDDL_SERIALNO on TMTB_FAILED_DOWNLOAD(serial_no);
