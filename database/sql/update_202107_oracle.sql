
-- new table TMTB_STUCK_TRANSACTION
    create table TMTB_STUCK_TRANSACTION (
        id number(19,0) not null,
        admin_fee number(19,0),
        card_no varchar2(20 char),
        driver_id varchar2(9 char),
        fare number(19,0),
        gst number(19,0),
        job_no varchar2(12 char),
        mid varchar2(15 char),
        release_ver varchar2(6 char),
        rrn varchar2(20 char),
        state varchar2(16 char) not null,
        tid varchar2(8 char),
        trigger_by varchar2(30 char),
        txn_amt number(19,0),
        txn_date_time timestamp,
        txn_type varchar2(16 char) not null,
        upload_date_time timestamp,
        terminal_id number(19,0) not null,
        primary key (id)
    );

    alter table TMTB_STUCK_TRANSACTION
        add constraint TMFC_STUCK_TXN_TERMINAL
        foreign key (terminal_id)
        references TMTB_TMS_TERMINAL;

    create index TMIX_STUCK_TXN__TERMINAL_ID on TMTB_STUCK_TRANSACTION (terminal_id);

-- add new TMTB_APP_DOWNLOAD_JOB_INFO table
    create table TMTB_APP_DOWNLOAD_JOB_INFO (
        start_date date,
        application_download_job_id number(19,0) not null,
        primary key (application_download_job_id),
        unique (application_download_job_id)
    );

    alter table TMTB_APP_DOWNLOAD_JOB_INFO 
        add constraint TMFC_APP_DOWNLOAD_JOB_INFO
        foreign key (application_download_job_id)
        references TMTB_APPLICATION_DOWNLOAD_JOB;

-- increase data len for TMTB_TERMINAL_LOG_RECORD
alter table TMTB_TERMINAL_LOG_RECORD
	modify data varchar2(4000 char);

-- removing alipay and dash pages

delete from tmtb_admin_log where page_name like 'alipay.%';
delete from tmtb_admin_log where page_name like 'dash.%';

delete from tmtb_acc_profile_tmtb_page where pages_name like 'alipay.%';
delete from tmtb_acc_profile_tmtb_page where pages_name like 'dash.%';

delete from tmtb_page where module_name in ('alipay', 'dash');

delete from tmtb_module where name in ('alipay', 'dash');

-- removing blacklist pages

delete from tmtb_admin_log where page_name like 'blacklist.%';

delete from tmtb_acc_profile_tmtb_page where pages_name like 'blacklist.%';

delete from tmtb_page where name like 'blacklist.%';

-- add pages

insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('terminal.parseTerminalLog', 'Parse Terminal Log', '/terminal/parseTerminalLog.html', 0, 0, 0, '3.05', 1, 'terminal', 'Device');

insert into tmtb_page(name, label, url, common_flag, admin_flag, domain_flag, display_order, visible, module_name, group_name) 
values ('terminal.listStuckTransactions', 'Stuck Transactions Management', '/terminal/listStuckTransactions.html', 0, 0, 0, '3.06', 1, 'terminal', 'Device');

-- update TMVW_TERM_APP_DOWNLOAD_SUMMARY view

DROP VIEW TMVW_TERM_APP_DOWNLOAD_SUMMARY;

CREATE VIEW TMVW_TERM_APP_DOWNLOAD_SUMMARY  
as 
SELECT
	Row_Number() OVER(ORDER BY job_id ASC) as id,
	A.*
FROM 
	(SELECT DISTINCT 
		adj.id as job_id,
		adj.start_window,
		adj.end_window,
		adj.concurrent_download, 
		adj.create_date_time as creation_date,
		r.id as release_id, 
		r.description as description,
		r.version as version, 
		m.id as model_id,
		m.model_name as model_name, 
		adj.status as job_status, 
		g.id as group_id,
       		(CASE 
			WHEN (SELECT COUNT(DISTINCT ta.terminal_id)
          			FROM  
					TMVW_TERM_APP_LATEST ta, 
					tmtb_tms_terminal t, 
					tmtb_release r2, 
					tmtb_grouping g2, 
					tmtb_vehicle_group vg 
          			WHERE	
					ta.release_id  = r2.id AND
					r2.id  = g2.release_id AND
					g2.id  = g.id AND
					ta.terminal_id  = t.id AND
					t.vehicle_no  = vg.vehicle_id AND
					vg.group_id  = g2.id) IS NULL THEN 0 
          		ELSE (SELECT COUNT(DISTINCT ta.terminal_id)
          			FROM  
					TMVW_TERM_APP_LATEST ta,
          				tmtb_tms_terminal t,
					tmtb_release r2, 
					tmtb_grouping g2, 
					tmtb_vehicle_group vg 
          			WHERE	
					ta.release_id  = r2.id AND
					r2.id  = g2.release_id AND
					g2.id  = g.id AND
					ta.terminal_id  = t.id AND
					t.vehicle_no  = vg.vehicle_id AND
					vg.group_id  = g2.id AND  
					t.vehicle_unpaired=0)
			END) as completed,
          	(CASE 
			WHEN (SELECT COUNT(*)
				FROM  
					tmtb_term_app_download_job d,
	 				tmtb_grouping g2 
				WHERE	
					d.application_download_job_id  = adj.id AND
					d.status  = 1 AND
					d.release_id  = r.id AND
					r.deleted  = 0 AND
					r.id  = g2.release_id AND
					g2.id  = g.id) IS NULL THEN 0 
			ELSE (SELECT COUNT(*)
				FROM  
					tmtb_term_app_download_job d,
	 				tmtb_grouping g2 
				WHERE	
					d.application_download_job_id  = adj.id AND
					d.status  = 1 AND
					d.release_id  = r.id AND
					r.deleted  = 0 AND
					r.id  = g2.release_id AND
					g2.id  = g.id)
			END) as pending,
		(CASE 
			WHEN (SELECT COUNT(*)
				FROM  tmvw_terminal_not_downloaded td 
				WHERE td.group_id  = adj.group_id) IS NULL THEN 0 
			ELSE (SELECT COUNT(*)
				FROM  tmvw_terminal_not_downloaded td 
				WHERE td.group_id  = adj.group_id
				)
			END) as not_started,
		adj.deleted as deleted,
		adji.start_date
	FROM  
		tmtb_application_download_job adj,
		tmtb_app_download_job_info adji,
		tmtb_grouping g,
		tmtb_release r,
		tmtb_release_tmtb_application ra,
		tmtb_application app,
		tmtb_release_tmtb_model rm,
		tmtb_model m,
		tmtb_term_app_download_job tadj 
	WHERE	
		adj.group_id  = g.id AND
		g.release_id  = r.id AND
		r.id  = ra.tmtb_release_id AND
		ra.application_id  = app.id AND
		g.release_id  = rm.tmtb_release_id AND
		rm.model_id  = m.id AND
		tadj.application_download_job_id (+) = adj.id AND
		adji.application_download_job_id (+) = adj.id
	) A;


