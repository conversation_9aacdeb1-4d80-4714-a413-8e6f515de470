package com.abl.db.service;

import java.io.InputStream;
import java.util.List;

import com.abl.db.model.AdminUser;
import com.abl.db.model.Group;
import com.abl.db.model.VehicleGroup;
import com.abl.db.model.Vehicle;

public interface VehicleGroupService {

	public VehicleGroup getVehicleGroupById(long id);

	public VehicleGroup getVehicleGroup(String vehicleId);

	public VehicleGroup getVehicleGroupByVehicleId(String vehicleId);

	public void save(VehicleGroup vehicleGroup);

	public List<VehicleGroup> getVehicleGroupByGroupId(long id);

	public void saveAll(List<VehicleGroup> vehicleGroup);

	public List<VehicleGroup> uploadVehicle(InputStream inputStream, String groupName,
			AdminUser adminUser) throws Exception;

	public List<VehicleGroup> searchVehicles(String vehicleNo, String orderBy, boolean ascending,
			int offset, int maxResults);

	public int countVehicles(String vehicleNo);

	public List<Vehicle> getVehiclesWithoutGroup(List<String> ivdVersion,
			List<String> vehicleType, List<String> vehicleModel, String vehicleNo);

	public List<Vehicle> getVehiclesWithDefaultGroup(List<String> ivdVersion,
			List<String> vehicleType, List<String> vehicleModel, String vehicleNo);

	public List<VehicleGroup> getVehiclesWithGroup(List<String> ivdVersion, List<String> vehicleType,
			List<String> vehicleModel, String vehicleNo);
	
	public void saveVehicleGroup(Group group, List<Vehicle> newVehicles,
			List<Vehicle> defaultVgList);

	public List<VehicleGroup> getVehicleGroupByList(List<Vehicle> vehicleList);

}
