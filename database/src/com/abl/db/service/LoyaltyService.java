package com.abl.db.service;

import com.abl.db.model.Loyalty;
import com.abl.db.model.LoyaltyFile;

import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: chinyew
 * Date: 21/12/12
 * Time: 3:16 PM
 * To change this template use File | Settings | File Templates.
 */
public interface LoyaltyService {

    public void save(Loyalty loyalty);
    public void save(LoyaltyFile loyaltyFile);
    public boolean checkDuplicate(String tid, String completeTime, String tripNo);
    public List<Loyalty> getUnprocessedLoyalty();
    public List<LoyaltyFile> getLoyaltyFiles(Date from, Date to);

}
