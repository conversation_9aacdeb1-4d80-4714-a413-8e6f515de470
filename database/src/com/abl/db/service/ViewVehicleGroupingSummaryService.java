package com.abl.db.service;

import java.util.List;

import com.abl.db.model.ViewVehicleGroupingSummary;

public interface ViewVehicleGroupingSummaryService {
	public List<ViewVehicleGroupingSummary> getVehicleGroupingSummary();
	
	public List<ViewVehicleGroupingSummary> getGroupSummaryByGroupAndReleaseId(long groupId, long releaseId, String orderBy, boolean ascending, int offset, int maxResults);
	
	public int countGroupSummaryByGroupAndReleaseId(long groupId, long releaseId);
	
	public ViewVehicleGroupingSummary getVehicleGroupingSummaryById(long id);

	public ViewVehicleGroupingSummary getVehicleGroupingSummaryByGroupId(long id);

}
