package com.abl.db.service;

import com.abl.db.model.ApplicationDownloadJob;

import java.util.List;

public interface ApplicationDownloadJobService {

    public ApplicationDownloadJob getApplicationDownloadJob(long id);

    public void save(ApplicationDownloadJob applicationDownloadJob);

    public ApplicationDownloadJob getApplicationDownloadFetchApplication(long id);

    public List<ApplicationDownloadJob> getCurrentJobs(long groupId);

    public List<ApplicationDownloadJob> getApplicationDownloadJobs(long groupId, short status);
    
    public ApplicationDownloadJob getApplicationDownloadJobFetchGroup(long id);
}
