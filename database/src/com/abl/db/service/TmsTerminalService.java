package com.abl.db.service;

import com.abl.db.model.TmsTerminal;

import java.util.List;

public interface TmsTerminalService {

    public TmsTerminal getTerminal(long id);

    public TmsTerminal getTerminalFetchModel(long id);

    public void saveAll(List<TmsTerminal> object);

    public int countTerminals(String serialNo, String vehicleNo, long modelId);

    public List<TmsTerminal> searchTerminals(String serialNo, String vehicleNo,long modelId, String orderBy,
                                             boolean ascending, int offset, int maxResults);
    public void save(TmsTerminal terminal);

    public TmsTerminal getTerminalBySerialNo(String serialNo);

    public TmsTerminal getTerminalForUpdate(String serialNo);
    
    public int unpairOtherTerminalsForVehicleNo(String vehicleNo, String serialNo);
    
    
}