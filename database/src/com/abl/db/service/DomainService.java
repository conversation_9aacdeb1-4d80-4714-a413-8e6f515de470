package com.abl.db.service;

import java.util.List;

import com.abl.db.model.Domain;

public interface DomainService {
	
	public Domain getDefaultDomain();
	
	public Domain getDomain(long id);
	
	public List<Domain> getDomains();
	
	public Domain getDomainByShortName(String shortName);
	
	public Domain getDomainByName(String name);
	
	public int countDomains();

	public List<Domain> searchDomains(String orderBy, boolean ascending, int offset, int maxResults);

	public void save(Domain domain);
}
