package com.abl.db.service;

import com.abl.db.model.Application;

import java.util.List;

public interface ApplicationService {

    public Application getApplication(long id);

    public List<Application> getApplications();

    //public List<Application> searchApplicationByNameVersion(String applicationName, int version) throws Exception;
    public List<Application> searchApplicationByNameVersion(String applicationName, int version);

    public void save(Application application);

}
