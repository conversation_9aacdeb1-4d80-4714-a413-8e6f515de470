package com.abl.db.service;

import java.util.List;

import com.abl.db.model.ViewOtaStatusReportSummary;

public interface ViewOtaStatusReportSummaryService {
	
	public List<ViewOtaStatusReportSummary> searchSummary(String orderBy, boolean ascending, int offset, int maxResults);
	
	public List<ViewOtaStatusReportSummary> searchSelectedGroupSummary(List<String> groupIdList, String orderBy, boolean ascending, int offset, int maxResults);
	
	public int countSummary();

}
