package com.abl.db.service;

import java.util.List;

import com.abl.db.model.Group;

public interface GroupService {

    public Group getGroupByName(String name);
    
    public List<Group> getGroupFetchRelease();
    
    public Group getGroupById(long id);
    
    public void save(Group group);
    
    public List<Group> getAllGroups();
    
    public Group getGroupByReleaseId(long releaseId);
    
    public List<Group> getAllGroupsExcludeId(long id);

    public Group getActiveGroupFetchReleaseById(long groupId);
}
