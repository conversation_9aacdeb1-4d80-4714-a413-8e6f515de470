package com.abl.db.service;

import java.util.List;

import com.abl.db.model.AccessProfile;

public interface AccessProfileService {

	public AccessProfile getAccessProfile(long id);
	
	public AccessProfile getAccessProfile(String name);
	
	public AccessProfile getAccessProfileFetchPages(String name);
	
	public AccessProfile getAccessProfileFetchPages(long id);
	
	public List<AccessProfile> getAccessProfiles();
	
	public int countAccessProfiles();
	
	public List<AccessProfile> searchAccessProfiles(String orderBy, boolean ascending, int offset, int maxResults);
	
	public void save(AccessProfile accessProfile);
	
	public void delete(AccessProfile accessProfile);
}
