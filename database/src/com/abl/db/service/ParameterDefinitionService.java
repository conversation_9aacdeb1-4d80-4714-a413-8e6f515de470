package com.abl.db.service;

import com.abl.db.model.ParameterDefinition;

import java.util.List;

public interface ParameterDefinitionService {

    public ParameterDefinition getParameterDefinition(long id);

    public void save(ParameterDefinition parameterDefinition);

    public List<ParameterDefinition> getParameterDefinitionFetchApplication(long appId);

    public List<ParameterDefinition> getBinParameterDefinitionFetchApplication(long appId);

    public ParameterDefinition getParameterDefinitionByNameApp(String name, long applicationId);

    public int countParameters(long appId);
    
    public List<ParameterDefinition> getFeeParameterDefinitionFetchApplication(long appId);
}
