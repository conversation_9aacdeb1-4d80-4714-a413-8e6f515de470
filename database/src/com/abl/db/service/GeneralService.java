package com.abl.db.service;

import java.util.List;

import com.abl.db.model.Application;
import com.abl.db.model.Release;
import com.abl.db.model.BlacklistFile;
import com.abl.db.model.BlacklistVersion;
import com.abl.db.model.Group;
import com.abl.db.model.ParameterDefinition;
import com.abl.db.model.ApplicationBinRange;
import com.abl.db.model.ApplicationBinValue;
import com.abl.db.model.ProfileBinRange;
import com.abl.db.model.ProfileBinValue;
import com.abl.db.model.ProfileFee;
import com.abl.db.model.ProfileFeeValue;
import com.abl.db.model.ProfileParameterValue;
import com.abl.db.model.ReleaseFile;
import com.abl.db.model.TerminalProfile;
import com.abl.db.model.TerminalProfileFile;
import com.abl.db.model.VehicleGroup;
import com.abl.db.model.ViewProfileBinValue;
import com.abl.db.model.ViewProfileFeeValue;
import com.abl.db.model.ViewProfileParameterValue;
import com.abl.db.model.ApplicationFee;
import com.abl.db.model.ApplicationFeeValue;


public interface GeneralService {

	public void save(Release release,List<Application> appList, List<ParameterDefinition> paramDef,
			List<ApplicationBinRange> appBinRange,
			List<ApplicationBinValue> appBinValue,
			List<ReleaseFile> releaseFile, List<ApplicationFee> feeList, List<ApplicationFeeValue> feeValueList);

	public void save(TerminalProfile terminalProfile, List<ProfileParameterValue> profileParamList,  List<ProfileBinRange>profileBinRangeList, List<ProfileBinValue> profileBinValueList, List<TerminalProfileFile>terminalProfileFileList,List<ProfileFee> profileFeeList,
			List<ProfileFeeValue> profileFeeValueList);

//	public void save(TerminalProfile terminalProfile, List<ProfileParameterValue> profileParamList, List<ViewProfileParameterValue> profileParamViewList, List<ProfileBinRange>profileBinRangeList, List<ProfileBinValue> profileBinValueList, List<ViewProfileBinValue> profileBinValueViewList, List<TerminalProfileFile> terminalProfileFileList,List<ProfileFee> profileFeeList,
//			List<ProfileFeeValue> profileFeeValueList, List<ViewProfileFeeValue> profileFeeValueViewList);
	
	public void save(Group group, List<VehicleGroup> vehicleGroup);
	
	public void save(Group group, List<VehicleGroup> vehicleGroup, List<VehicleGroup> updateDefaultVehicleGroup);
	
	public void save(BlacklistVersion blacklistVersion, List<BlacklistFile> blacklistFile);

}
