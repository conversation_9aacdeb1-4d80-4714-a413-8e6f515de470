package com.abl.db.service;

import java.util.Date;
import java.util.List;

import com.abl.db.model.PlTxnLog;

public interface PlTxnLogService {

    public PlTxnLog getPlTxnLog(long id);

    /**
     * get PlTxnLog by rrn
     * if more than 1 rec, get the latest one
     * 
     * @param rrn
     * @return
     */
    public PlTxnLog getPlTxnLogByRrn(String rrn);

    /**
     * get PlTxnLog by mid, tid, stan, resp code where mti is 0220
     * if more than 1 rec, get the latest one
     * 
     * @param mid
     * @param tid
     * @param stan
     * @return
     */
    public PlTxnLog getOfflineSalePlTxnLogByMidTidStanRespCode(String mid, String tid, String stan, String respCode);
    
    /**
     * get PlTxnLog by transId, respCode where msgType='pl_offline_sale'
     * if more than 1 rec, get the latest one
     * 
     * @param transId
     * @param respCode
     * @return
     */
    public PlTxnLog getOfflinePurchasePlTxnLogByTransIdRespCode(String transId, String respCode);
    
    /**
     * get PlTxnLog by mid, tid, stan where mti is 0200
     * if more than 1 rec, get the latest one
     * 
     * @param mid
     * @param tid
     * @param stan
     * @return
     */
    public PlTxnLog getSalePlTxnLogByMidTidStan(String mid, String tid, String stan);
    
    /**
     * get PlTxnLog by transId where msgType='pl_sale'
     * if more than 1 rec, get the latest one
     * 
     * @param transId
     * @param respCode
     * @return
     */
    public PlTxnLog getSalePlTxnLogByTransId(String transId);

    /**
     * get PlTxnLogs by status order by id
     * 
     * @param status
     * @param maxResults
     * @return
     */
    public List<PlTxnLog> getPlTxnLogs(PlTxnLog.Status status, int maxResults);
    
    /**
     * update PlTxnLog record with id=id
     * set txResult and status
     * 
     * @param id
     * @param txResult
     * @param status
     * @return
     */
    public int updateTxResultAndStatus(long id, String txResult, PlTxnLog.Status status);
    
    /**
     * find PlTxnLog records with status=PROCESSING
     * set status to PENDING
     * 
     * @return
     */

    /**
     * update PlTxnLog record with id=id
     * set txResult, status and dcp status
     *
     * @param id
     * @param txResult
     * @param status
     * @param dcpStatus
     * @return
     */
    public int updateTxResultAndStatusAndDcpStatus(long id, String txResult, PlTxnLog.Status status, PlTxnLog.DcpStatus dcpStatus);

    /**
     * find PlTxnLog records with status=PROCESSING
     * set status to PENDING
     *
     * @return
     */
    public int resetPendingStatus();
    
    /**
     * find PlTxnLog records with status=PROCESSING2
     * set status to  ENQUIRING
     * 
     * @return
     */
    public int resetEnquiringStatus();
    
    /**
     * insert into PlTxnLog
     * 
     * @param txnLog
     */
    public void save(PlTxnLog txnLog);

    /**
     *
     * @param dcpStatus
     * @param maxResults
     * @return
     */
    public List<PlTxnLog> getPaylahDeclinedPaymentTxns(PlTxnLog.DcpStatus dcpStatus, Date retryDt, int maxResults);

    /**
     *
     * @param id
     * @param dcpStatus
     * @return
     */
    public int updateDcpStatus(long id, PlTxnLog.DcpStatus dcpStatus);

}
