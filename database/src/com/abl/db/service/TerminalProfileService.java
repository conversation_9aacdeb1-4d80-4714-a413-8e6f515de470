package com.abl.db.service;

import com.abl.db.bean.TermProfileAppBean;
import com.abl.db.model.TerminalProfile;

import java.util.List;

public interface TerminalProfileService {

    public TerminalProfile getTerminalProfile(long id);

    public TerminalProfile getTerminalProfileFetchApplication(long id);

    public void save(TerminalProfile terminalProfile);

    public TerminalProfile getTerminalProfileFetchApplication(long appId, long groupId);
    
    public TerminalProfile getLatestActiveTerminalProfile(long appId, long groupId);

    public List<TermProfileAppBean> getTerminalProfileFetchApplicationRelease(long groupId, long releaseId);

}
