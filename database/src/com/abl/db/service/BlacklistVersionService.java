package com.abl.db.service;

import java.util.Date;
import java.util.List;

import com.abl.db.model.BlacklistVersion;

public interface BlacklistVersionService {
    public BlacklistVersion getBlacklistVersion(long id);

    public BlacklistVersion getBlacklistVersionByVersion(int version);

    public BlacklistVersion getLatestBlacklistVersion(short status);

    public BlacklistVersion getBlacklistVersion(String fullCans, String fullRange, String smallCans, String smallRange);
    
    public void save(BlacklistVersion blacklistVersion);
    
    public List<BlacklistVersion> getBlacklistVersionByStatus(short status);
    
    public void delete(BlacklistVersion blacklistVersion);
    
    public BlacklistVersion getBlacklistVersionByScheduleDate(Date scheduleDate);
    
    public BlacklistVersion getPendingBlacklistVersionByScheduleDateForBatch(Date scheduleDate);
}
