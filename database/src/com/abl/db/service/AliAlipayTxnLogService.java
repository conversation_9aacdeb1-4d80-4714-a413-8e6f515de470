package com.abl.db.service;

import com.abl.db.model.AliAlipayTxnLog;

public interface AliAlipayTxnLogService {

    public AliAlipayTxnLog getAliAlipayTxnLog(long id);
    

    /**
     * insert into AliAlipayTxnLog
     * 
     * @param txnLog
     */
    public void save(AliAlipayTxnLog txnLog);
    
    /**
     * update AliAlipayTxnLog record
     * 
     * @param txnLog
     */
    public void update(AliAlipayTxnLog txnLog);

    /**
     * get aliAlipayTxnLog by aliTxnLogId
     * get the latest 1 if more than 1
     *
     * @param id
     * @return
     */

    public AliAlipayTxnLog getAliAlipayTxnLogByAliTxnLogId(long id);

    AliAlipayTxnLog getSuccessfulFreezeByAliTxnLogId(long id);
    int updateAliAlipayStatus(long id, AliAlipayTxnLog.Status status);

    AliAlipayTxnLog getByBookingRef(String bookingRef, String msgType, final String ascOrDesc);
}
