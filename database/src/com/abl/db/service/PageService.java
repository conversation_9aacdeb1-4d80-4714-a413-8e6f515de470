package com.abl.db.service;

import java.util.List;
import java.util.Map;

import com.abl.db.model.AdminUser;
import com.abl.db.model.Module;
import com.abl.db.model.Page;

public interface PageService {

	public Map<String, Page> getUrlPageMap();
	
	/**
	 * This returns the page with the url.
	 * Note that this uses a cached copy of pages.
	 * 
	 * @param url
	 * @return
	 */
	public Page getPageByUrl(String url);
	
	/**
	 * returns the page with name
	 * 
	 * @param name
	 * @return
	 */
	public Page getPage(String name);
	
	public Page getPageFetchModule(String name);
	
	/**
	 * gets pages join fetch module, order by displayOrder
	 * 
	 * @return
	 */
	public List<Page> getPages();
	
	/**
	 * gets pages join fetch module, not ordered
	 * 
	 * @param appUser
	 * @return
	 */
	public List<Page> getAuthorizedPages(AdminUser appUser);
	
	/**
	 * gets admin pages join fetch module, not ordered
	 * 
	 * @return
	 */
	public List<Page> getAdminPages();
	
	/**
	 * gets list of page that belongs to the module, join fetch module, order by displayOrder
	 * 
	 * @param module
	 * @return
	 */
	public List<Page> getModulePages(Module module);
	
	
	public List<Page> getVisiblePages();
}
