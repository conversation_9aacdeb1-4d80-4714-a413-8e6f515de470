package com.abl.db.service;

import java.util.Date;
import java.util.List;

import com.abl.db.model.DashTxnLog;

public interface DashTxnLogService {

	public List<DashTxnLog> searchDashTxnLogs(Date startDate, Date endDate, String taxiNumber, String jobNumber, String driverId, String orderBy, boolean ascending, int offset, int maxResults);
	
	public int countDashTxnLogs( Date startDate, Date endDate, String taxiNumber, String jobNumber, String driverId);

}
