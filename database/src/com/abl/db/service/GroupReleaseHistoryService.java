package com.abl.db.service;

import java.util.List;

import com.abl.db.model.GroupReleaseHistory;

public interface GroupReleaseHistoryService {
	
	 public void save(GroupReleaseHistory groupReleaseHistory);
	 
	 public List<GroupReleaseHistory> getGroupReleaseHistoryByGroupId(long groupId);
	 
	 public List<GroupReleaseHistory> searchGroupReleaseHistory(long groupId,long releaseId,
             String orderBy, boolean ascending, int offset, int maxResults) ;
	 
	 public int countGroupReleaseHistory(long groupId,long releaseId);
	 
}
