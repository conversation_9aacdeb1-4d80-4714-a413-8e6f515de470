package com.abl.db.service;

import java.util.List;

import com.abl.db.model.Vehicle;

public interface VehicleService {

    public Vehicle getVehicle(String vehicleId);
    
    public List<Vehicle> getVehiclesByMultipleCriteria(List<String> ivdVersion, List<String>vehicleType, List<String>vehicleModel, String vehicleNo);
    
    public List<Vehicle> searchVehicles(String vehicleNo, String orderBy, boolean ascending, int offset, int maxResults);
    
    public int countVehicles(String vehicleNo);
    
    public int countVehiclesByMultipleCriteria(List<String> ivdVersion, List<String>vehicleType, List<String>vehicleModel, String vehicleNo);
    
   

}
