package com.abl.db.service;

import com.abl.db.model.TerminalLogRecord;

import java.util.Date;
import java.util.List;

public interface TerminalLogRecordService {

    public TerminalLogRecord getTerminaLogRecord(long id);

    public List<TerminalLogRecord> getTerminaLogRecordByLogSequence(long terminalLogId, int sequenceNo);

    public void save(TerminalLogRecord terminalLogRecord);

    public void delete(TerminalLogRecord terminalLogRecord);

    public List<TerminalLogRecord> getTerminaLogFetchRecordByTerminal(long terminalId, Date startDate, Date endDate, String data, String source);
}
