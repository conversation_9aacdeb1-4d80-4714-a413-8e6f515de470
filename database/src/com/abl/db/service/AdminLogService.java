package com.abl.db.service;

import java.util.Date;
import java.util.List;

import com.abl.db.model.AdminLog;

public interface AdminLogService {

	public AdminLog getAdminLogFetchAll(long id);
	
	public int countAdminLogs();
	
	public List<AdminLog> searchAdminLogs(String orderBy, boolean ascending, int offset, int maxResults);
	
	public void save(AdminLog adminLog);
	
	public List<AdminLog> searchAdminLogs(String login, Date startDate, Date endDate, String orderBy, boolean ascending, int offset, int maxResults);
	
	public int countAdminLogs(String login, Date startDate, Date endDate);

}
