package com.abl.db.service;

import java.util.List;

import com.abl.db.model.AdminUser;
import com.abl.db.model.Model;
import com.abl.db.model.Release;

public interface ReleaseService {

    public Release getRelease(long id);

    public Release getReleaseByVersion(int version);

    public boolean hasModel(long releaseId, Model model);
    
    public List loadReleasePackage (AdminUser adminUser, String filePath)throws Exception;
    
    public Release getLatestRelease();
    
    public List<Release> getAllReleases();
    
    public void save(Release release);
    
    public Release getAllReleasesFetchApplication(long id);
   
   
}
