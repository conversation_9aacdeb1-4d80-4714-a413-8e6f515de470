package com.abl.db.service;

import java.util.List;

import com.abl.db.model.NofAccount;

public interface NofAccountService {

    public NofAccount getNofAccount(long id);

    public NofAccount getNofAccountByMerchTokenId(String merchTokenId);
    
    public List<NofAccount> getNofAccountsByGuidMuid(String guid, String muid);
    
    public NofAccount getUndeletedNofAccountsByGuidMuidTokenIndex(String guid, String muid, String tokenIndex);
    
    public void save(NofAccount account);
    
    public void update(NofAccount account);
}
