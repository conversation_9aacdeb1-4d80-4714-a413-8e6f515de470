package com.abl.db.service;

import com.abl.db.model.AliTxnLog;

import java.util.Date;
import java.util.List;

public interface AliTxnLogService {

    public AliTxnLog getAliTxnLog(long id);

    public void save(AliTxnLog txnLog);

    public AliTxnLog getAliTxnLogByBookingRef(String bookingRef, String msgType );

    public AliTxnLog getPaymentAliTxnLogByTransId(String transId);

	public AliTxnLog getAliTxnLogByBookingRefExcludeCurrentId(String bookingRef, String msgType, Long currId);

    public AliTxnLog getOfflinePaymentMidTidStanRespCode(String mid, String tid, String stan, String respCode);

    public List<AliTxnLog> getAlipayDeclinedPaymentTxns(AliTxnLog.H5Status h5Status, Date retryDt, int maxResults);

    public int updateDcpStatus(long id, AliTxnLog.DcpStatus dcpStatus);
    List<AliTxnLog> listPending(String msgType, int offset, int limit,
                                Date txnLogDateTimeNotBefore, Date lastSentToAlipayNotAfter);
    int updateStatus(long id, AliTxnLog.Status status);
    int updateStatus(long id, AliTxnLog.Status status, AliTxnLog.Status whereStatus);
}
