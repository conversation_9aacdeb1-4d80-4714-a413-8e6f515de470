package com.abl.db.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.db.dao.DashTxnLogDao;
import com.abl.db.model.DashTxnLog;
import com.abl.db.service.DashTxnLogService;

@Service
@Transactional(readOnly = true)
public class DashTxnLogServiceImpl implements DashTxnLogService {
	@Autowired DashTxnLogDao dashTxnLogDao;
	
	@Override
	public List<DashTxnLog> searchDashTxnLogs(Date startDate, Date endDate, String taxiNumber, String jobNumber, String driverId, String orderBy, boolean ascending, int offset, int maxResults) {
		return dashTxnLogDao.searchDashTxnLogs(startDate, endDate, taxiNumber, jobNumber, driverId, orderBy, ascending, offset, maxResults);
	}
	
	@Override
	public int countDashTxnLogs( Date startDate, Date endDate, String taxiNumber, String jobNumber, String driverId){
		return dashTxnLogDao.countDashTxnLogs(startDate, endDate, taxiNumber, jobNumber, driverId);
	}
	

}
