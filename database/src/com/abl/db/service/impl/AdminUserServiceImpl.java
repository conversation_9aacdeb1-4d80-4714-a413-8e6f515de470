package com.abl.db.service.impl;

import java.util.*;

import com.abl.db.model.AccessProfile;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.db.dao.AdminUserDao;
import com.abl.db.model.AdminUser;
import com.abl.db.model.Domain;
import com.abl.db.service.AdminUserService;

@Service
@Transactional(readOnly = true)
public class AdminUserServiceImpl implements AdminUserService {

	@Autowired private AdminUserDao adminUserDao;
	
	@Override
	public AdminUser getAdminUser(long id) {
		return adminUserDao.getAdminUser(id);
	}
	
	@Override
	public AdminUser getAdminUserFetchDomain(long id) {
		return adminUserDao.getAdminUserFetchDomain(id);
	}
	
	@Override
	public AdminUser getAdminUserForUpdate(long id) {
		return adminUserDao.getAdminUserForUpdate(id);
	}

	@Override
	public AdminUser getAdminUser(String login) {
		return adminUserDao.getAdminUser(login);
	}
	
	@Override
	public AdminUser getAdminUserForUpdate(String login) {
		return adminUserDao.getAdminUserForUpdate(login);
	}
	
	@Override
	public AdminUser getAdminUserFetchAccessProfiles(long id) {
		return adminUserDao.getAdminUserFetchAccessProfiles(id);
	}

	@Override
	public AdminUser getAdminUserFetchAccessProfiles(String login) {
		return adminUserDao.getAdminUserFetchAccessProfiles(login);
	}

	@Override
	public int countAdminUsers(String login, String name, Integer status, Domain bank) {
		return adminUserDao.countAdminUsers(login, name, status, bank);
	}

	@Override
	public List<AdminUser> searchAdminUsers(String login, String name, Integer status, Domain bank,
			String orderBy, boolean ascending, int offset, int maxResults) {
		return adminUserDao.searchAdminUsers(login, name, status, bank, orderBy, ascending, offset, maxResults);
	}

	@Override
	@Transactional(readOnly = false)
	public void save(AdminUser adminUser) {
		adminUserDao.save(adminUser);
	}

    public AdminUser getDefaultAdminUser(AccessProfile accessProfile, Domain acquirer) {
        AdminUser au = adminUserDao.getAdminUser(2);
        if(au == null){
            Set<AccessProfile> sAP = new HashSet<AccessProfile>();

            sAP.add(accessProfile);
            au = new AdminUser();
            au.setAccessProfiles(sAP);
            au.setLogin("default");
            au.setName("Default");
            au.setDomain(acquirer);
            au.setPassword("0000000000000000BF11620171FBB0C7E6219A14BDAB0C7A8B627249A835EBA7585EFD9BB237540A");
            au.setLoginFailCount((short)0);
            au.setPasswordLocked(false);
            au.setPasswordReset(false);
            au.setStatus(1);
            Date dateNow = new Date();
            au.setStatusDateTime(dateNow);
            au.setCreateDateTime(dateNow);
            au.setType(0);
            adminUserDao.save(au);
        }
        return au;
    }
}
