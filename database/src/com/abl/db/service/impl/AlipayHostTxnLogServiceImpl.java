package com.abl.db.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.db.dao.AlipayHostTxnLogDao;
import com.abl.db.model.AlipayHostTxnLog;
import com.abl.db.service.AlipayHostTxnLogService;

@Service
@Transactional(readOnly = true)
public class AlipayHostTxnLogServiceImpl implements AlipayHostTxnLogService {
	@Autowired AlipayHostTxnLogDao alipayHostTxnLogDao;
	
	@Override
	public List<AlipayHostTxnLog> searchAlipayHostTxnLogs(Date startDate, Date endDate, String taxiNumber, String jobNumber, String driverId, String orderBy, boolean ascending, int offset, int maxResults) {
		return alipayHostTxnLogDao.searchAlipayHostTxnLogs(startDate, endDate, taxiNumber, jobNumber, driverId, orderBy, ascending, offset, maxResults);
	}
	
	@Override
	public int countAlipayHostTxnLogs( Date startDate, Date endDate, String taxiNumber, String jobNumber, String driverId){
		return alipayHostTxnLogDao.countAlipayHostTxnLogs(startDate, endDate, taxiNumber, jobNumber, driverId);
	}
	

}
