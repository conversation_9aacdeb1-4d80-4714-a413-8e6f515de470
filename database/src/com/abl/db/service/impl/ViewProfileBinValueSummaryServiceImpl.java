package com.abl.db.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.db.dao.ViewProfileBinValueSummaryDao;
import com.abl.db.model.ViewProfileBinValueSummary;
import com.abl.db.service.ViewProfileBinValueSummaryService;;

@Service
@Transactional(readOnly = true)
public class ViewProfileBinValueSummaryServiceImpl implements
ViewProfileBinValueSummaryService{
	@Autowired
	private ViewProfileBinValueSummaryDao viewProfileBinValueSummaryDao;
	
	public List<ViewProfileBinValueSummary> getProfileBinValueSummary(long profileId) {
		return viewProfileBinValueSummaryDao.getProfileBinValueSummary(profileId);
	}
	
	public ViewProfileBinValueSummary getLastProfileBinValueSummary() {
		return viewProfileBinValueSummaryDao.getLastProfileBinValueSummary();
	}

}
