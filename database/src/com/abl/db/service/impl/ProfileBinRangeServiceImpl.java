package com.abl.db.service.impl;

import com.abl.db.bean.ProfileBinValueBean;
import com.abl.db.dao.ProfileBinRangeDao;
import com.abl.db.model.ProfileBinRange;
import com.abl.db.service.ProfileBinRangeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(readOnly = true)
public class ProfileBinRangeServiceImpl implements ProfileBinRangeService {

    @Autowired
    private ProfileBinRangeDao profileBinRangeDao;

    @Override
    public List<ProfileBinValueBean> getAllProfileBinRangesByProfile(long terminalProfileId) {
        return profileBinRangeDao.getAllProfileBinRangesByProfile(terminalProfileId);

    }
}
