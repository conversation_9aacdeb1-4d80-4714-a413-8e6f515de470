package com.abl.db.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.db.dao.ViewGroupReleaseHistorySummaryDao;
import com.abl.db.model.ViewGroupReleaseHistorySummary;
import com.abl.db.service.ViewGroupReleaseHistorySummaryService;

@Service
@Transactional(readOnly = true)
public class ViewGroupReleaseHistorySummaryServiceImpl implements ViewGroupReleaseHistorySummaryService {
	@Autowired
	private ViewGroupReleaseHistorySummaryDao viewGroupReleaseHistorySummaryDao;
	
	public List<ViewGroupReleaseHistorySummary> getGroupReleaseHistoryByGroupRelease(long groupId, long releaseId){
		return viewGroupReleaseHistorySummaryDao.getGroupReleaseHistoryByGroupRelease(groupId, releaseId);
	}
}
