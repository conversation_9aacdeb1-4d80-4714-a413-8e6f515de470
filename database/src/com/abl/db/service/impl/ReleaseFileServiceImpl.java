package com.abl.db.service.impl;

import com.abl.db.dao.ReleaseFileDao;
import com.abl.db.model.ReleaseFile;
import com.abl.db.service.ReleaseFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(readOnly = true)
public class ReleaseFileServiceImpl implements ReleaseFileService {

    @Autowired
    private ReleaseFileDao releaseFileDao;

    @Override
    public ReleaseFile getReleaseFile(long releaseId, int recordNo) {
        return releaseFileDao.getReleaseFile(releaseId, recordNo);
    }

    @Override
    public int getNextRecordNo(long releaseId, int recordNo) {
        return releaseFileDao.getNextRecordNo(releaseId, recordNo);
    }

    @Override
    @Transactional(readOnly = false)
    public void saveAll(List<ReleaseFile> object) {
        releaseFileDao.saveAll(object);
    }
}
