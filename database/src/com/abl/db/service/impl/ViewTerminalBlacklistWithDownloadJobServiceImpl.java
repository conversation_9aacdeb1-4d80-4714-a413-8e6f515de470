package com.abl.db.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import com.abl.db.dao.ViewTerminalBlacklistWithDownloadJobDao;
import com.abl.db.model.ViewTerminalBlacklistWithDownloadJob;
import com.abl.db.service.ViewTerminalBlacklistWithDownloadJobService;



@Service
@Transactional(readOnly = true)
public class ViewTerminalBlacklistWithDownloadJobServiceImpl implements ViewTerminalBlacklistWithDownloadJobService {
	@Autowired
	private ViewTerminalBlacklistWithDownloadJobDao viewTerminalBlacklistWithDownloadJobDao;
	
	public List<ViewTerminalBlacklistWithDownloadJob> getTerminalBlacklistWithDownloadJob(long jobId) {
		return viewTerminalBlacklistWithDownloadJobDao.getTerminalBlacklistWithDownloadJob(jobId);
	}


}
