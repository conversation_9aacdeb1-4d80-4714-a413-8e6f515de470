package com.abl.db.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.db.dao.DashSingtelLogDao;
import com.abl.db.model.DashSingtelLog;
import com.abl.db.service.DashSingtelLogService;

@Service
@Transactional(readOnly = true)
public class DashSingtelLogServiceImpl implements DashSingtelLogService {
	@Autowired DashSingtelLogDao DashSingtelLogDao;
	
	@Override
	public List<DashSingtelLog> searchDashSingtelLogs(Date startDate, Date endDate, String taxiNumber, String jobNumber, String driverId, String orderBy, boolean ascending, int offset, int maxResults) {
		return DashSingtelLogDao.searchDashSingtelLogs(startDate, endDate, taxiNumber, jobNumber, driverId, orderBy, ascending, offset, maxResults);
	}
	
	@Override
	public int countDashSingtelLogs( Date startDate, Date endDate, String taxiNumber, String jobNumber, String driverId){
		return DashSingtelLogDao.countDashSingtelLogs(startDate, endDate, taxiNumber, jobNumber, driverId);
	}
	

}
