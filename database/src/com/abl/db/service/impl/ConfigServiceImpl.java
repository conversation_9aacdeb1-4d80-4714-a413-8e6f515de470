package com.abl.db.service.impl;

import com.abl.db.service.ConfigService;

import java.util.Map;

public class ConfigServiceImpl implements ConfigService {

    private Map<String, String> config;

    public Map<String, String> getConfig() {
        return config;
    }

    public void setConfig(Map<String, String> config) {
        this.config = config;
    }

    public String getConfig(String key) {
        if (config == null) {
            return null;
        }
        return config.get(key);
    }
}
