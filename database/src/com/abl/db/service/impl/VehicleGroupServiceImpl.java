package com.abl.db.service.impl;

import java.io.InputStream;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

import com.abl.db.dao.GroupDao;
import com.abl.db.dao.VehicleGroupDao;
import com.abl.db.model.AdminUser;
import com.abl.db.model.Group;
import com.abl.db.model.Vehicle;
import com.abl.db.model.VehicleGroup;
import com.abl.db.service.VehicleGroupService;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional(readOnly = true)
public class VehicleGroupServiceImpl implements VehicleGroupService {
	private static final Logger logger = Logger.getLogger(VehicleGroupServiceImpl.class);

	@Autowired
	private VehicleGroupDao vehicleGroupDao;

	@Autowired
	private GroupDao groupDao;

	@Override
	public VehicleGroup getVehicleGroup(String vehicleId) {
		return vehicleGroupDao.getVehicleGroup(vehicleId);
	}

	@Override
	public VehicleGroup getVehicleGroupByVehicleId(String vehicleId) {
		return vehicleGroupDao.getVehicleGroupByVehicleId(vehicleId);
	}

	@Override
	@Transactional(readOnly = false)
	public void save(VehicleGroup vehicleGroup) {
		vehicleGroupDao.save(vehicleGroup);
	}

	@Override
	public List<VehicleGroup> getVehicleGroupByGroupId(long id) {
		return vehicleGroupDao.getVehicleGroupByGroupId(id);
	}

	@Override
	@Transactional(readOnly = false)
	public void saveAll(List<VehicleGroup> vehicleGroup) {
		vehicleGroupDao.saveAll(vehicleGroup);
	}

	@Override
	public List<VehicleGroup> uploadVehicle(InputStream inputStream, String groupName, AdminUser adminUser)
			throws Exception {
		return vehicleGroupDao.uploadVehicle(inputStream, groupName, adminUser);
	}

	@Override
	public List<VehicleGroup> searchVehicles(String vehicleNo, String orderBy, boolean ascending, int offset,
			int maxResults) {
		return vehicleGroupDao.searchVehicles(vehicleNo, orderBy, ascending, offset, maxResults);

	}

	@Override
	public int countVehicles(String vehicleNo) {
		return vehicleGroupDao.countVehicles(vehicleNo);

	}

	@Override
	public VehicleGroup getVehicleGroupById(long id) {
		return vehicleGroupDao.getVehicleGroupById(id);
	}

	@Override
	public List<Vehicle> getVehiclesWithoutGroup(List<String> ivdVersion, List<String> vehicleType,
			List<String> vehicleModel, String vehicleNo) {
		return vehicleGroupDao.getVehiclesWithoutGroup(ivdVersion, vehicleType, vehicleModel, vehicleNo);
	}

	@Override
	public List<Vehicle> getVehiclesWithDefaultGroup(List<String> ivdVersion, List<String> vehicleType,
			List<String> vehicleModel, String vehicleNo) {
		return vehicleGroupDao.getVehiclesWithDefaultGroup(ivdVersion, vehicleType, vehicleModel, vehicleNo);
	}

	@Override
	public List<VehicleGroup> getVehiclesWithGroup(List<String> ivdVersion, List<String> vehicleType,
			List<String> vehicleModel, String vehicleNo) {
		return vehicleGroupDao.getVehiclesWithGroup(ivdVersion, vehicleType, vehicleModel, vehicleNo);
	}

	@Override
	@Transactional(readOnly = false)
	public void saveVehicleGroup(Group group, List<Vehicle> newVehicles, List<Vehicle> defaultVgList) {
		List<VehicleGroup> vgList = new ArrayList<VehicleGroup>();
		Date today = new Date();
		try {
			groupDao.save(group);
			logger.debug("group:" + group.getId() + ", group name:" + group.getName());
			if (newVehicles != null && !newVehicles.isEmpty()) {
				logger.debug("total new vehicle=" + newVehicles.size());
				for (Iterator iterator = newVehicles.iterator(); iterator.hasNext();) {
					Vehicle v = (Vehicle) iterator.next();
					VehicleGroup vg = new VehicleGroup();
					vg.setGroup(group);
					vg.setVehicle(v);
					vg.setAssignDateTime(today);
					vgList.add(vg);
				}

			}
			if (defaultVgList != null && !defaultVgList.isEmpty()) {
				logger.debug("total new vehicle=" + defaultVgList.size());
				for (Iterator iterator = defaultVgList.iterator(); iterator.hasNext();) {
					Vehicle v = (Vehicle) iterator.next();
					VehicleGroup vg = vehicleGroupDao.getVehicleGroup(v.getVehicleId());
					vg.setAssignDateTime(new Date());
					vg.setGroup(group);
					vgList.add(vg);
				}
			}
			vehicleGroupDao.saveAll(vgList);
		} catch (Exception e) {
			logger.debug("new group was unable to add " + e);
		} 
	}

	@Override
	public List<VehicleGroup> getVehicleGroupByList(List<Vehicle> vehicleList) {
		return vehicleGroupDao.getVehicleGroupByList(vehicleList);
	}
}
