package com.abl.db.service.impl;

import java.util.List;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.db.dao.ApplicationBinRangeDao;
import com.abl.db.dao.ApplicationBinValueDao;
import com.abl.db.dao.ApplicationDao;
import com.abl.db.dao.ApplicationFeeDao;
import com.abl.db.dao.ApplicationFeeValueDao;
import com.abl.db.dao.BlacklistFileDao;
import com.abl.db.dao.BlacklistVersionDao;
import com.abl.db.dao.GroupDao;
import com.abl.db.dao.ProfileBinValueDao;
import com.abl.db.dao.ProfileBinRangeDao;
import com.abl.db.dao.ProfileFeeValueDao;
import com.abl.db.dao.ProfileFeeDao;
import com.abl.db.dao.ParameterDefinitionDao;
import com.abl.db.dao.ProfileParameterValueDao;
import com.abl.db.dao.VehicleGroupDao;
import com.abl.db.dao.ViewProfileBinValueDao;
import com.abl.db.dao.ViewProfileFeeValueDao;
import com.abl.db.dao.ViewProfileParameterValueDao;
import com.abl.db.dao.ReleaseDao;
import com.abl.db.dao.ReleaseFileDao;
import com.abl.db.dao.TerminalProfileDao;
import com.abl.db.dao.TerminalProfileFileDao;
import com.abl.db.model.Application;
import com.abl.db.model.ApplicationBinRange;
import com.abl.db.model.ApplicationBinValue;
import com.abl.db.model.ApplicationFee;
import com.abl.db.model.ApplicationFeeValue;
import com.abl.db.model.BlacklistFile;
import com.abl.db.model.BlacklistVersion;
import com.abl.db.model.Group;
import com.abl.db.model.ProfileFee;
import com.abl.db.model.ProfileFeeValue;
import com.abl.db.model.VehicleGroup;
import com.abl.db.model.ReleaseFile;
import com.abl.db.model.ParameterDefinition;
import com.abl.db.model.ProfileBinRange;
import com.abl.db.model.ProfileBinValue;
import com.abl.db.model.ProfileParameterValue;
import com.abl.db.model.Release;
import com.abl.db.model.TerminalProfile;
import com.abl.db.model.TerminalProfileFile;
import com.abl.db.service.GeneralService;

@Service
@Transactional(readOnly = true)
public class GeneralServiceImpl implements GeneralService {
	private static final Logger logger = Logger.getLogger(GeneralServiceImpl.class);
	@Autowired
	private ApplicationBinRangeDao applicationBinRangeDao;
	@Autowired
	private ApplicationBinValueDao applicationBinValueDao;

	@Autowired
	private ReleaseDao releaseDao;

	@Autowired
	private ReleaseFileDao releaseFileDao;

	@Autowired
	private ApplicationDao applicationDao;
	@Autowired
	private TerminalProfileDao terminalProfileDao;
	@Autowired
	private ParameterDefinitionDao parameterDefinitionDao;

	@Autowired
	private ProfileParameterValueDao profileParameterValueDao;

	@Autowired
	private ProfileBinRangeDao profileBinRangeDao;

	@Autowired
	private ProfileBinValueDao profileBinValueDao;

	@Autowired
	private ViewProfileParameterValueDao viewProfileParameterValueDao;

	@Autowired
	private ViewProfileBinValueDao viewProfileBinValueDao;

	@Autowired
	private TerminalProfileFileDao terminalProfileFileDao;

	@Autowired
	GroupDao groupDao;

	@Autowired
	VehicleGroupDao vehicleGroupDao;

	@Autowired
	BlacklistVersionDao blacklistVersionDao;

	@Autowired
	BlacklistFileDao blacklistFileDao;

	@Autowired
	ApplicationFeeDao applicationFeeDao;

	@Autowired
	ApplicationFeeValueDao applicationFeeValueDao;

	@Autowired
	private ProfileFeeDao profileFeeDao;

	@Autowired
	private ProfileFeeValueDao profileFeeValueDao;

	@Autowired
	private ViewProfileFeeValueDao viewProfileFeeValueDao;

	@Override
	@Transactional(readOnly = false)
	public void save(Release release, List<Application> appList, List<ParameterDefinition> paramList,
			List<ApplicationBinRange> appBinRangeList, List<ApplicationBinValue> appBinValueList,
			List<ReleaseFile> releaseFileList, List<ApplicationFee> feeList,
			List<ApplicationFeeValue> feeValueList) {

		releaseDao.save(release);
		applicationDao.saveAll(appList);
		parameterDefinitionDao.saveAll(paramList);
		applicationBinRangeDao.saveAll(appBinRangeList);
		applicationBinValueDao.saveAll(appBinValueList);
		//System.out.println("releaseFileList()" + releaseFileList.size());
		try {
			releaseFileDao.saveAll(releaseFileList);
		} catch (Exception e) {
			logger.error(e);
			//System.out.println("ERROR" + e.getMessage());
		}
		applicationFeeDao.saveAll(feeList);
		applicationFeeValueDao.saveAll(feeValueList);

	}

	@Override
	@Transactional(readOnly = false)
	public void save(TerminalProfile terminalProfile, List<ProfileParameterValue> profileParamList,
			List<ProfileBinRange> profileBinRangeList, List<ProfileBinValue> profileBinValueList,
			List<TerminalProfileFile> terminalProfileFileList, List<ProfileFee> profileFeeList,
			List<ProfileFeeValue> profileFeeValueList) {
		terminalProfileDao.save(terminalProfile);
		profileParameterValueDao.saveAll(profileParamList);
		profileBinRangeDao.saveAll(profileBinRangeList);
		profileBinValueDao.saveAll(profileBinValueList);
		terminalProfileFileDao.saveAll(terminalProfileFileList);
		profileFeeDao.saveAll(profileFeeList);
		profileFeeValueDao.saveAll(profileFeeValueList);
	}

	// @Override
	// @Transactional(readOnly = false)
	// public void save(TerminalProfile terminalProfile,
	// List<ProfileParameterValue> profileParamList,
	// List<ViewProfileParameterValue> profileParamViewList,
	// List<ProfileBinRange> profileBinRangeList,
	// List<ProfileBinValue> profileBinValueList,
	// List<ViewProfileBinValue> profileBinValueViewList,
	// List<TerminalProfileFile> terminalProfileFileList,List<ProfileFee>
	// profileFeeList,
	// List<ProfileFeeValue> profileFeeValueList, List<ViewProfileFeeValue>
	// profileFeeValueViewList) {
	// terminalProfileDao.save(terminalProfile);
	// if (profileParamList != null) {
	// profileParameterValueDao.saveAll(profileParamList);
	// }
	// if (profileParamViewList != null) {
	// viewProfileParameterValueDao.saveAll(profileParamViewList);
	// }
	// if (profileBinRangeList != null) {
	// profileBinRangeDao.saveAll(profileBinRangeList);
	// }
	// if (profileBinValueList != null) {
	// profileBinValueDao.saveAll(profileBinValueList);
	// }
	// if (profileBinValueViewList != null) {
	// viewProfileBinValueDao.saveAll(profileBinValueViewList);
	// }
	// if (terminalProfileFileList != null) {
	// terminalProfileFileDao.saveAll(terminalProfileFileList);
	//
	// }
	// if (profileFeeList != null) {
	// profileFeeDao.saveAll(profileFeeList);
	//
	// }
	// if (profileFeeValueList != null) {
	// profileFeeValueDao.saveAll(profileFeeValueList);
	//
	// }
	// if (profileFeeValueViewList != null) {
	// viewProfileFeeValueDao.saveAll(profileFeeValueViewList);
	//
	// }
	//
	// }

	@Override
	@Transactional(readOnly = false)
	public void save(Group group, List<VehicleGroup> vehicleGroup) {
		groupDao.save(group);
		vehicleGroupDao.saveAll(vehicleGroup);

	}

	@Override
	@Transactional(readOnly = false)
	public void save(Group group, List<VehicleGroup> vehicleGroup,
			List<VehicleGroup> defaultVehicleGroup) {

		groupDao.save(group);
		
		logger.debug("group saved:" + group.getId());
		if (vehicleGroup != null) {
			vehicleGroupDao.saveAll(vehicleGroup);
		}
		if (defaultVehicleGroup != null) {
			vehicleGroupDao.saveAll(defaultVehicleGroup);
		}
	}

	@Override
	@Transactional(readOnly = false)
	public void save(BlacklistVersion blacklistVersion, List<BlacklistFile> blacklistFile) {
		blacklistVersionDao.save(blacklistVersion);
		blacklistFileDao.saveAll(blacklistFile);
	}

}
