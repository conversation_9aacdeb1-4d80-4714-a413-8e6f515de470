package com.abl.db.service.impl;

import com.abl.db.dao.TerminalBlacklistDownloadJobDao;
import com.abl.db.model.TerminalBlacklistDownloadJob;
import com.abl.db.model.TerminalBlacklistDownloadJobId;
import com.abl.db.service.TerminalBlacklistDownloadJobService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(readOnly = true)
public class TerminalBlacklistDownloadJobServiceImpl implements TerminalBlacklistDownloadJobService {

    @Autowired
    private TerminalBlacklistDownloadJobDao terminalBlacklistDownloadJobDao;

    @Override
    @Transactional(readOnly = false)
    public void save(TerminalBlacklistDownloadJob terminalBlacklistDownloadJob) {
        terminalBlacklistDownloadJobDao.save(terminalBlacklistDownloadJob);
    }

    @Override
    public List<TerminalBlacklistDownloadJob> getNotSuccessTerminalBlacklistDownloadJobs(long blacklistVersionId, long terminalId, String vehicleId) {
        return terminalBlacklistDownloadJobDao.getNotSuccessTerminalBlacklistDownloadJobs(blacklistVersionId, terminalId, vehicleId);
    }

    @Override
    public List<TerminalBlacklistDownloadJob> getInProgressTerminalBlacklistDownloadJobs(long terminalId) {
        return terminalBlacklistDownloadJobDao.getInProgressTerminalBlacklistDownloadJobs(terminalId);
    }

    @Override
    public List<TerminalBlacklistDownloadJob> getInProgressTerminalBlacklistDownloadJobs(String vehicleId) {
        return terminalBlacklistDownloadJobDao.getInProgressTerminalBlacklistDownloadJobs(vehicleId);
    }

    @Override
    public int countTerminalBlacklistDownloadJobs(long jobId, short status, long terminalId) {
        return terminalBlacklistDownloadJobDao.countTerminalBlacklistDownloadJobs(jobId, status, terminalId);
    }

    @Override
    public TerminalBlacklistDownloadJob getByPk(TerminalBlacklistDownloadJobId terminalBlacklistDownloadJobId) {
        return terminalBlacklistDownloadJobDao.getByPk(terminalBlacklistDownloadJobId);
    }

    @Override
    @Transactional(readOnly = false)
    public int releaseOtherJobs(TerminalBlacklistDownloadJob terminalBlacklistDownloadJob) {
        return terminalBlacklistDownloadJobDao.releaseOtherJobs(terminalBlacklistDownloadJob);
    }

    @Override
    public TerminalBlacklistDownloadJob findTerminalBlacklistDownloadJob(long terminalId, String vehicleId, long blacklistVersionId) {
        return terminalBlacklistDownloadJobDao.findTerminalBlacklistDownloadJob(terminalId, vehicleId, blacklistVersionId);
    }
}
