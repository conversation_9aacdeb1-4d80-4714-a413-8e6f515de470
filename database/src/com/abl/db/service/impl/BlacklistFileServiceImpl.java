package com.abl.db.service.impl;

import com.abl.db.dao.BlacklistFileDao;
import com.abl.db.model.BlacklistFile;
import com.abl.db.service.BlacklistFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(readOnly = true)
public class BlacklistFileServiceImpl implements BlacklistFileService {

    @Autowired
    private BlacklistFileDao blacklistFileDao;

    @Override
    public BlacklistFile getBlacklistFile(long blacklistVersionId, int recordNo) {
        return blacklistFileDao.getBlacklistFile(blacklistVersionId, recordNo);
    }

    @Override
    public int getNextRecordNo(long blacklistVersionId, int recordNo) {
        return blacklistFileDao.getNextRecordNo(blacklistVersionId, recordNo);
    }

    @Override
    @Transactional(readOnly = false)
    public void saveAll(List<BlacklistFile> object) {
        blacklistFileDao.saveAll(object);
    }
}
