package com.abl.db.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.db.dao.DomainDao;
import com.abl.db.model.Domain;
import com.abl.db.service.DomainService;

@Service
@Transactional(readOnly = true)
public class DomainServiceImpl implements DomainService {

	@Autowired private DomainDao domainDao;

	@Override
	@Transactional(readOnly = false)
	public Domain getDefaultDomain() {
		Domain domain = domainDao.getDomain(1);
		if (domain == null) {
			domain = new Domain();
			domain.setName("Default");
			domain.setShortName("default");
			save(domain);
		}
		return domain;
	}
	
	@Override
	public Domain getDomain(long id) {
		return domainDao.getDomain(id);
	}

	@Override
	public List<Domain> getDomains() {
		return domainDao.getDomains();
	}
	
	@Override
	public Domain getDomainByShortName(String shortName) {
		return domainDao.getDomainByShortName(shortName);
	}

	@Override
	public Domain getDomainByName(String name) {
		return domainDao.getDomainByName(name);
	}
	
	@Override
	public int countDomains() {
		return domainDao.countDomains();
	}
	
	@Override
	public List<Domain> searchDomains(String orderBy,
			boolean ascending, int offset, int maxResults) {
		return domainDao.searchDomains(orderBy, ascending, offset, maxResults);
	}

	@Override
	@Transactional(readOnly = false)
	public void save(Domain domain) {
		domainDao.save(domain);
	}
	
}
