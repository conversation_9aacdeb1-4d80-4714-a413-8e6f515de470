package com.abl.db.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.db.model.ViewTerminalsNotDownloaded;
import com.abl.db.service.ViewTerminalsNotDownloadedService;
import com.abl.db.dao.ViewTerminalsNotDownloadedDao;

@Service
@Transactional(readOnly = true)
public class ViewTerminalsNotDownloadedServiceImpl implements ViewTerminalsNotDownloadedService {
	@Autowired private ViewTerminalsNotDownloadedDao viewTerminalsNotDownloadedDao;
	
	public List<ViewTerminalsNotDownloaded> getTerminalWithoutDownloadJob(long groupId) {
		return viewTerminalsNotDownloadedDao.getTerminalWithoutDownloadJob(groupId);
	}

}
