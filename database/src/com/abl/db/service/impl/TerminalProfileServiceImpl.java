package com.abl.db.service.impl;

import com.abl.db.bean.TermProfileAppBean;
import com.abl.db.dao.TerminalProfileDao;
import com.abl.db.model.TerminalProfile;
import com.abl.db.service.TerminalProfileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(readOnly = true)
public class TerminalProfileServiceImpl implements TerminalProfileService {
    @Autowired
    private TerminalProfileDao terminalProfileDao;

    @Override
    public TerminalProfile getTerminalProfile(long id) {
        return terminalProfileDao.getTerminalProfile(id);
    }

    @Override
    @Transactional(readOnly = false)
    public void save(TerminalProfile terminalProfile) {
        terminalProfileDao.save(terminalProfile);
    }

    @Override
    public TerminalProfile getTerminalProfileFetchApplication(long id) {
        return terminalProfileDao.getTerminalProfileFetchApplication(id);

    }

    @Override
    public TerminalProfile getTerminalProfileFetchApplication(long appId, long groupId){
    	return terminalProfileDao.getTerminalProfileFetchApplication(appId, groupId);
    	
    }

  @Override
    public TerminalProfile getLatestActiveTerminalProfile(long appId, long groupId) {
        return terminalProfileDao.getLatestActiveTerminalProfile(appId, groupId);
    }

    @Override
    public List<TermProfileAppBean> getTerminalProfileFetchApplicationRelease(long groupId, long releaseId) {
        return terminalProfileDao.getTerminalProfileFetchApplicationRelease(groupId, releaseId);
    }

}
