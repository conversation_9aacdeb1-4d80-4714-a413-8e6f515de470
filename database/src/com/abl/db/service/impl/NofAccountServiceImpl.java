package com.abl.db.service.impl;

import com.abl.db.dao.NofAccountDao;
import com.abl.db.model.NofAccount;
import com.abl.db.service.NofAccountService;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class NofAccountServiceImpl implements NofAccountService {

    @Autowired
    private NofAccountDao nofAccountDao;

    @Override
    @Transactional(readOnly = true)
    public NofAccount getNofAccount(long id) {
        return nofAccountDao.getNofAccount(id);
    }

	@Override
	@Transactional(readOnly = true)
	public NofAccount getNofAccountByMerchTokenId(String merchTokenId) {
		return nofAccountDao.getNofAccountByMerchTokenId(merchTokenId);
	}

	@Override
	@Transactional(readOnly = true)
	public List<NofAccount> getNofAccountsByGuidMuid(String guid, String muid) {
		return nofAccountDao.getNofAccountsByGuidMuid(guid, muid);
	}
	
	@Override
	@Transactional(readOnly = true)
	public NofAccount getUndeletedNofAccountsByGuidMuidTokenIndex(String guid, String muid, String tokenIndex) {
		return nofAccountDao.getUndeletedNofAccountsByGuidMuidTokenIndex(guid, muid, tokenIndex);
	}
	
    @Override
    @Transactional(readOnly = false)
    public void save(NofAccount account) {
    	nofAccountDao.save(account);
    }

	@Override
	@Transactional(readOnly = false)
	public void update(NofAccount account) {
		nofAccountDao.update(account);
	}

}
