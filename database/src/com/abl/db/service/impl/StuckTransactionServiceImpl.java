package com.abl.db.service.impl;

import com.abl.db.bean.StuckTransactionSearchParam;
import com.abl.db.dao.StuckTransactionDao;
import com.abl.db.model.StuckTransaction;
import com.abl.db.model.TmsTerminal;
import com.abl.db.service.StuckTransactionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Service
@Transactional(readOnly = true)
public class StuckTransactionServiceImpl implements StuckTransactionService {

    @Autowired
    private StuckTransactionDao stuckTransactionDao;

    @Transactional(readOnly = false)
    @Override
    public void save(StuckTransaction stuckTxn) {
        stuckTransactionDao.save(stuckTxn);
    }

    @Override
    public StuckTransaction getStuckTransaction(long id) {
        return stuckTransactionDao.getStuckTransaction(id);
    }

    @Override
    public List<StuckTransaction> getStuckTransactions(StuckTransactionSearchParam searchParam,
                                                       int offset, int maxResults) {

        return stuckTransactionDao.getStuckTransactions(searchParam, offset, maxResults);
    }

    @Override
    public int countStuckTransactions(StuckTransactionSearchParam searchParam) {

        return stuckTransactionDao.countStuckTransactions(searchParam);
    }

    @Override
    public int countStuckTransactionsByTerminalAndTimestamp(TmsTerminal terminal, Date timestamp) {
        return stuckTransactionDao.countStuckTransactionsByTerminalAndTimestamp(terminal, timestamp);
    }

    @Override
    public StuckTransaction getStuckTransactionByTerminalAndTimestamp(TmsTerminal terminal, Date timestamp) {
        return stuckTransactionDao.getStuckTransactionByTerminalAndTimestamp(terminal, timestamp);
    }

    @Override
    public List<StuckTransaction> getStuckTransactionsWithPendingActions(TmsTerminal terminal, int maxResults) {
        return stuckTransactionDao.getStuckTransactionsWithPendingActions(terminal, maxResults);
    }

}
