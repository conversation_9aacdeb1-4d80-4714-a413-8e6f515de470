package com.abl.db.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.db.dao.VehicleModelDao;
import com.abl.db.model.VehicleModel;
import com.abl.db.service.VehicleModelService;

@Service
@Transactional(readOnly = true)
public class VehicleModelServiceImpl implements VehicleModelService {

	@Autowired
	private VehicleModelDao vehicleModelDao;

	public List<VehicleModel> getVehicleModels() {
		return vehicleModelDao.getVehicleModels();

	}

}
