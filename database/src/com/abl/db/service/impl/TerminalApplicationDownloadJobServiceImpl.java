package com.abl.db.service.impl;

import com.abl.db.dao.TerminalApplicationDownloadJobDao;
import com.abl.db.model.TerminalApplicationDownloadJob;
import com.abl.db.model.TerminalApplicationDownloadJobId;
import com.abl.db.service.TerminalApplicationDownloadJobService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(readOnly = true)
public class TerminalApplicationDownloadJobServiceImpl implements
        TerminalApplicationDownloadJobService {

    @Autowired
    private TerminalApplicationDownloadJobDao terminalApplicationDownloadJobDao;

    @Override
    @Transactional(readOnly = false)
    public void save(TerminalApplicationDownloadJob terminalApplicationDownloadJob) {
        terminalApplicationDownloadJobDao.save(terminalApplicationDownloadJob);
    }

    @Override
    public List<TerminalApplicationDownloadJob> getNotSuccessTerminalApplicationDownloadJobs(int releaseVersion, long terminalId, String vehicleId) {
        return terminalApplicationDownloadJobDao.getNotSuccessTerminalApplicationDownloadJobs(releaseVersion, terminalId, vehicleId);
    }

    @Override
    public List<TerminalApplicationDownloadJob> getInProgressTerminalApplicationDownloadJobs(long terminalId) {
        return terminalApplicationDownloadJobDao.getInProgressTerminalApplicationDownloadJobs(terminalId);
    }

    @Override
    public List<TerminalApplicationDownloadJob> getInProgressTerminalApplicationDownloadJobs(String vehicleId) {
        return terminalApplicationDownloadJobDao.getInProgressTerminalApplicationDownloadJobs(vehicleId);
    }

    @Override
    public int countTerminalApplicationDownloadJobs(long jobId, short status, long terminalId) {
        return terminalApplicationDownloadJobDao.countTerminalApplicationDownloadJobs(jobId, status, terminalId);
    }

    @Override
    public TerminalApplicationDownloadJob getByPk(TerminalApplicationDownloadJobId terminalApplicationDownloadJobId) {
        return terminalApplicationDownloadJobDao.getByPk(terminalApplicationDownloadJobId);
    }

    @Override
    @Transactional(readOnly = false)
    public int releaseOtherJobs(TerminalApplicationDownloadJob terminalApplicationDownloadJob) {
        return terminalApplicationDownloadJobDao.releaseOtherJobs(terminalApplicationDownloadJob);
    }

    @Override
    public TerminalApplicationDownloadJob findTerminalApplicationDownloadJob(long groupId, long terminalId, String vehicleId, long releaseId) {
        return terminalApplicationDownloadJobDao.findTerminalApplicationDownloadJob(groupId, terminalId, vehicleId, releaseId);
    }
}
