package com.abl.db.service.impl;

import com.abl.db.dao.ModelDao;
import com.abl.db.model.Model;
import com.abl.db.service.ModelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(readOnly = true)
public class ModelServiceImpl implements ModelService {

    @Autowired
    private ModelDao modelDao;

    @Override
    public Model getModel(long id) {
        return modelDao.getModel(id);
    }

    @Override
    public List<Model> getModels() {
        return modelDao.getModels();

    }

    @Override
    public Model getModelByName(String name) {
        return modelDao.getModelByName(name);

    }
}
