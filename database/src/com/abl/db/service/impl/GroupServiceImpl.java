package com.abl.db.service.impl;

import java.util.List;

import com.abl.db.dao.GroupDao;
import com.abl.db.model.Group;
import com.abl.db.service.GroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional(readOnly = true)
public class GroupServiceImpl implements GroupService {

    @Autowired
    private GroupDao groupDao;

    @Override
    public Group getGroupByName(String name) {
        return groupDao.getGroupByName(name);
    }
    
    @Override
    public List<Group> getGroupFetchRelease(){
    	return groupDao.getGroupFetchRelease();
    }
    
    @Override
    public Group getGroupById(long id){
    	return groupDao.getGroupById(id);
    	
    }
    
    @Override
	@Transactional(readOnly = false)
    public void save(Group group){
    	groupDao.save(group);
    }
    
    @Override
    public List<Group> getAllGroups(){
    	return groupDao.getAllGroups();
    	
    }
    
    @Override
    public Group getGroupByReleaseId(long releaseId){
    	return groupDao.getGroupByReleaseId(releaseId);
    }
    @Override
    public List<Group> getAllGroupsExcludeId(long id) {
    	return groupDao.getAllGroupsExcludeId(id);
    }

    @Override
    public Group getActiveGroupFetchReleaseById(long groupId) {
        return groupDao.getActiveGroupFetchReleaseById(groupId);

    }
    
}
