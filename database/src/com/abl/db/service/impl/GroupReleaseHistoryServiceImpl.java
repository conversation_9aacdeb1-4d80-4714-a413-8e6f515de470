package com.abl.db.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.db.dao.GroupReleaseHistoryDao;
import com.abl.db.model.GroupReleaseHistory;
import com.abl.db.service.GroupReleaseHistoryService;

@Service
@Transactional(readOnly = true)
public class GroupReleaseHistoryServiceImpl implements GroupReleaseHistoryService {
	@Autowired
    private GroupReleaseHistoryDao groupReleaseHistoryDao;
	
	@Override
	@Transactional(readOnly = false)
    public void save(GroupReleaseHistory groupReleaseHistory){
		groupReleaseHistoryDao.save(groupReleaseHistory);
    }
	
	@Override
	public List<GroupReleaseHistory> getGroupReleaseHistoryByGroupId(long groupId){
		return groupReleaseHistoryDao.getGroupReleaseHistoryByGroupId(groupId);
		
	}
	
	@Override
	public List<GroupReleaseHistory> searchGroupReleaseHistory(long groupId,long releaseId,
    String orderBy, boolean ascending, int offset, int maxResults){
		return groupReleaseHistoryDao.searchGroupReleaseHistory(groupId, releaseId, orderBy, ascending, offset, maxResults);
		
	}
	
	@Override
	public int countGroupReleaseHistory(long groupId,long releaseId){
		return groupReleaseHistoryDao.countGroupReleaseHistory(groupId, releaseId);
		
	}
	

}
