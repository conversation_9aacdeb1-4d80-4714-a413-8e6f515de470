package com.abl.db.service.impl;

import com.abl.db.dao.ApplicationDownloadJobDao;
import com.abl.db.model.ApplicationDownloadJob;
import com.abl.db.service.ApplicationDownloadJobService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(readOnly = true)
public class ApplicationDownloadJobServiceImpl implements
        ApplicationDownloadJobService {

    @Autowired
    private ApplicationDownloadJobDao applicationDownloadJobDao;

    @Override
    public ApplicationDownloadJob getApplicationDownloadJob(long id) {
        return applicationDownloadJobDao.getApplicationDownloadJob(id);
    }

    @Override
    @Transactional(readOnly = false)
    public void save(ApplicationDownloadJob applicationDownloadJob) {
        applicationDownloadJobDao.save(applicationDownloadJob);
    }

    @Override
    public ApplicationDownloadJob getApplicationDownloadFetchApplication(long id) {
        return applicationDownloadJobDao.getApplicationDownloadFetchApplication(id);
    }

    @Override
    public List<ApplicationDownloadJob> getCurrentJobs(long groupId) {
        return applicationDownloadJobDao.getCurrentJobs(groupId);
    }

    @Override
    public List<ApplicationDownloadJob> getApplicationDownloadJobs(long groupId, short status) {
        return applicationDownloadJobDao.getApplicationDownloadJobs(groupId, status);
    }
    
    @Override
    public ApplicationDownloadJob getApplicationDownloadJobFetchGroup(long id){
		return applicationDownloadJobDao.getApplicationDownloadJobFetchGroup(id);
    	
    }
}
