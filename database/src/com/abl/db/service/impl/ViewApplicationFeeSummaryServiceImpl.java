package com.abl.db.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.db.dao.ViewApplicationFeeSummaryDao;
import com.abl.db.model.ViewApplicationFeeSummary;
import com.abl.db.service.ViewApplicationFeeSummaryService;

@Service
@Transactional(readOnly = true)
public class ViewApplicationFeeSummaryServiceImpl implements ViewApplicationFeeSummaryService {
	
	@Autowired private ViewApplicationFeeSummaryDao viewapplicationFeeSummaryDao;
	
	public List<ViewApplicationFeeSummary> getApplicationFeeSummaryByApp(long appId) {
		return viewapplicationFeeSummaryDao.getApplicationFeeSummaryByApp(appId);
	}
	
	public ViewApplicationFeeSummary getLastApplicationFeeSummary() {
		return viewapplicationFeeSummaryDao.getLastApplicationFeeSummary();
	}
	
	

}
