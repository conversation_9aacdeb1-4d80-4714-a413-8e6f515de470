package com.abl.db.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.db.dao.ViewFailedDownloadVehiclesDao;
import com.abl.db.model.ViewFailedDownloadVehicles;
import com.abl.db.service.ViewFailedDownloadVehiclesService;


@Service
@Transactional(readOnly = true)
public class ViewFailedDownloadVehiclesServiceImpl implements ViewFailedDownloadVehiclesService {
	
	@Autowired
    private ViewFailedDownloadVehiclesDao viewFailedDownloadVehiclesDao;
	
	public List<ViewFailedDownloadVehicles> searchSummary(String orderBy, boolean ascending, int offset, int maxResults){
		return viewFailedDownloadVehiclesDao.searchSummary(orderBy, ascending, offset, maxResults);
	}
	
	public int countSummary(){
		return viewFailedDownloadVehiclesDao.countSummary();
		
	}
	

}
