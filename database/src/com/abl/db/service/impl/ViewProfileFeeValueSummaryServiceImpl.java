package com.abl.db.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.db.dao.ViewProfileFeeValueSummaryDao;
import com.abl.db.model.ViewProfileFeeValueSummary;
import com.abl.db.service.ViewProfileFeeValueSummaryService;;

@Service
@Transactional(readOnly = true)
public class ViewProfileFeeValueSummaryServiceImpl implements
ViewProfileFeeValueSummaryService{
	@Autowired
	private ViewProfileFeeValueSummaryDao viewProfileFeeValueSummaryDao;
	
	public List<ViewProfileFeeValueSummary> getProfileFeeValueSummary(long profileId) {
		return viewProfileFeeValueSummaryDao.getProfileFeeValueSummary(profileId);
	}
	
	public ViewProfileFeeValueSummary getLastProfileFeeValueSummary() {
		return viewProfileFeeValueSummaryDao.getLastProfileFeeValueSummary();
	}

}
