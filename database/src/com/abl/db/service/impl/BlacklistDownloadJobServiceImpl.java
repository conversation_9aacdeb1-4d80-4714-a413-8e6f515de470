package com.abl.db.service.impl;

import com.abl.db.dao.BlacklistDownloadJobDao;
import com.abl.db.model.BlacklistDownloadJob;
import com.abl.db.model.BlacklistVersion;
import com.abl.db.service.BlacklistDownloadJobService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(readOnly = true)
public class BlacklistDownloadJobServiceImpl implements
        BlacklistDownloadJobService {

    @Autowired
    private BlacklistDownloadJobDao blacklistDownloadJobDao;

    @Override
    public List<BlacklistDownloadJob> getBlacklistDownloadJobs(short status) {
        return blacklistDownloadJobDao.getBlacklistDownloadJobs(status);
    }

    @Override
    public List<BlacklistDownloadJob> getCurrentJobs() {
        return blacklistDownloadJobDao.getCurrentJobs();
    }
    
    @Override
	@Transactional(readOnly = false)
	public void save(BlacklistDownloadJob blacklistDownloadJob) {
    	blacklistDownloadJobDao.save(blacklistDownloadJob);
	}
    
    @Override
    public List<BlacklistDownloadJob> getBlacklistDownloadJob(){
    	return blacklistDownloadJobDao.getBlacklistDownloadJob();
    }
    
    @Override
    public BlacklistDownloadJob getBlacklistDownloadJobs(long id){
    	return blacklistDownloadJobDao.getBlacklistDownloadJobs(id);
    }
    
    @Override
	@Transactional(readOnly = false)
    public void delete(BlacklistDownloadJob blacklistDownloadJob){
    	blacklistDownloadJobDao.delete(blacklistDownloadJob);
    }
}
