package com.abl.db.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.db.dao.ViewTerminalProfileSummaryDao;
import com.abl.db.model.ViewTerminalProfileSummary;
import com.abl.db.service.ViewTerminalProfileSummaryService;

@Service
@Transactional(readOnly = true)
public class ViewTerminalProfileSummaryServiceImpl implements ViewTerminalProfileSummaryService {
	@Autowired
    private ViewTerminalProfileSummaryDao viewTerminalProfileSummaryDao;
	public List<ViewTerminalProfileSummary> getTerminalProfileSummaryByGroup(long groupId, long releaseId){
		return viewTerminalProfileSummaryDao.getTerminalProfileSummaryByGroup(groupId, releaseId);
	}
}
