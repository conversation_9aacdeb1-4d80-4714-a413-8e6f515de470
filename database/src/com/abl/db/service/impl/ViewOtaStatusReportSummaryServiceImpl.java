package com.abl.db.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.db.dao.ViewOtaStatusReportSummaryDao;
import com.abl.db.model.ViewOtaStatusReportSummary;
import com.abl.db.service.ViewOtaStatusReportSummaryService;

@Service
@Transactional(readOnly = true)
public class ViewOtaStatusReportSummaryServiceImpl implements ViewOtaStatusReportSummaryService {
	
	@Autowired
    private ViewOtaStatusReportSummaryDao viewOtaStatusReportSummaryDao;
	
	public List<ViewOtaStatusReportSummary> searchSummary(String orderBy, boolean ascending, int offset, int maxResults){
		return viewOtaStatusReportSummaryDao.searchSummary(orderBy, ascending, offset, maxResults);
	}
	
	public int countSummary(){
		return viewOtaStatusReportSummaryDao.countSummary();
		
	}
	
	public List<ViewOtaStatusReportSummary> searchSelectedGroupSummary(List<String> groupIdList, String orderBy, boolean ascending, int offset, int maxResults){
		return viewOtaStatusReportSummaryDao.searchSelectedGroupSummary(groupIdList, orderBy, ascending, offset, maxResults);
	}
	

}
