package com.abl.db.service.impl;

import com.abl.db.bean.ProfileFeeValueBean;
import com.abl.db.dao.ProfileFeeValueDao;
import com.abl.db.model.ProfileFeeValue;
import com.abl.db.service.ProfileFeeValueService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(readOnly = true)
public class ProfileFeeValueServiceImpl implements
        ProfileFeeValueService {
    @Autowired
    private ProfileFeeValueDao profileFeeValueDao;

    @Override
    public List<ProfileFeeValue> getProfileFeeValuesByFee(long profileFeeId) {
        return profileFeeValueDao.getProfileFeeValuesByFee(profileFeeId);
    }

    @Override
    public List<ProfileFeeValueBean> getProfileFeeValueSummary(long terminalProfileId) {
        return profileFeeValueDao.getProfileFeeValueSummary(terminalProfileId);
    }
}
