package com.abl.db.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.db.dao.AlipayTxnLogDao;
import com.abl.db.model.AlipayTxnLog;
import com.abl.db.service.AlipayTxnLogService;

@Service
@Transactional(readOnly = true)
public class AlipayTxnLogServiceImpl implements AlipayTxnLogService {
	@Autowired AlipayTxnLogDao alipayTxnLogDao;
	
	@Override
	public List<AlipayTxnLog> searchAlipayTxnLogs(Date startDate, Date endDate, String taxiNumber, String jobNumber, String driverId, String orderBy, boolean ascending, int offset, int maxResults) {
		return alipayTxnLogDao.searchAlipayTxnLogs(startDate, endDate, taxiNumber, jobNumber, driverId, orderBy, ascending, offset, maxResults);
	}
	
	@Override
	public int countAlipayTxnLogs( Date startDate, Date endDate, String taxiNumber, String jobNumber, String driverId){
		return alipayTxnLogDao.countAlipayTxnLogs(startDate, endDate, taxiNumber, jobNumber, driverId);
	}
	

}
