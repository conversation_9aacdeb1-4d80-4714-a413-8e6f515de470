package com.abl.db.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.db.dao.ViewTerminalBlacklistCompletedJobDao;
import com.abl.db.model.ViewTerminalBlacklistCompletedJob;
import com.abl.db.service.ViewTerminalBlacklistCompletedJobService;


@Service
@Transactional(readOnly = true)
public class ViewTerminalBlacklistCompletedJobServiceImpl implements ViewTerminalBlacklistCompletedJobService {
	@Autowired
	private ViewTerminalBlacklistCompletedJobDao viewTerminalBlacklistCompletedJobDao;
	
	public List<ViewTerminalBlacklistCompletedJob> getTerminalBlacklistCompletedJob(long jobId) {
		return viewTerminalBlacklistCompletedJobDao.getTerminalBlacklistCompletedJob(jobId);
	}
	
	public List<ViewTerminalBlacklistCompletedJob> getAllTerminalBlacklistCompletedJob(){
		return viewTerminalBlacklistCompletedJobDao.getAllTerminalBlacklistCompletedJob();
	}


}
