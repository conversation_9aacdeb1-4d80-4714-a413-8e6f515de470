package com.abl.db.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.db.dao.ViewTerminalSummaryDao;
import com.abl.db.model.ViewTerminalSummary;
import com.abl.db.service.ViewTerminalSummaryService;

@Service
@Transactional(readOnly = true)
public class ViewTerminalSummaryServiceImpl implements ViewTerminalSummaryService {
	
	@Autowired
	private ViewTerminalSummaryDao viewTerminalSummaryDao;
	
	public List<ViewTerminalSummary> searchTerminals(String serialNo, String vehicleNo, long modelId, String orderBy,
			boolean ascending, int offset, int maxResults) {
		return viewTerminalSummaryDao.searchTerminals(serialNo, vehicleNo, modelId, orderBy, ascending, offset, maxResults);
	}

	public int countTerminals(String serialNo, String vehicleNo,long modelId){
		return viewTerminalSummaryDao.countTerminals(serialNo, vehicleNo, modelId);
	}

}
