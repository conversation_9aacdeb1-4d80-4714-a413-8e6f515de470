package com.abl.db.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.db.dao.ViewTerminalApplicationSummaryDao;
import com.abl.db.model.ViewTerminalApplicationSummary;
import com.abl.db.service.ViewTerminalApplicationSummaryService;

@Service
@Transactional(readOnly = true)
public class ViewTerminalApplicationSummaryServiceImpl implements  ViewTerminalApplicationSummaryService {
	@Autowired
    private ViewTerminalApplicationSummaryDao viewTerminalApplicationSummaryDao;
	
	public List<ViewTerminalApplicationSummary> getTerminalApplicationSummary(long terminalId){
		return viewTerminalApplicationSummaryDao.getTerminalApplicationSummary(terminalId);
	}

}
