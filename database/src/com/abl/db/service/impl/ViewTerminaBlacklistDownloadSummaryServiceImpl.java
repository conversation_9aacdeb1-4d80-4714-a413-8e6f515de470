package com.abl.db.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.db.dao.ViewTerminalBlacklistDownloadSummaryDao;
import com.abl.db.model.ViewTerminalBlacklistDownloadSummary;
import com.abl.db.service.ViewTerminalBlacklistDownloadSummaryService;

@Service
@Transactional(readOnly = true)
public class ViewTerminaBlacklistDownloadSummaryServiceImpl implements
		ViewTerminalBlacklistDownloadSummaryService {

	@Autowired
	private ViewTerminalBlacklistDownloadSummaryDao viewTerminalBlacklistDownloadSummaryDao;

	@Override
	public ViewTerminalBlacklistDownloadSummary getTerminalBlacklistDownloadSummaryByJobId(long jobId){
		return viewTerminalBlacklistDownloadSummaryDao
				.getTerminalBlacklistDownloadSummaryByJobId(jobId);

	}

	@Override
	public List<ViewTerminalBlacklistDownloadSummary> getAllTerminalBlacklistDownloadSummary() {
		return viewTerminalBlacklistDownloadSummaryDao
				.getAllTerminalBlacklistDownloadSummary();
	}

}
