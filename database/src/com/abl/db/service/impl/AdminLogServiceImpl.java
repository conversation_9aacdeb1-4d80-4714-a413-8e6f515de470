package com.abl.db.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.db.dao.AdminLogDao;
import com.abl.db.model.AdminLog;
import com.abl.db.service.AdminLogService;

@Service
@Transactional(readOnly = true)
public class AdminLogServiceImpl implements AdminLogService {

	@Autowired AdminLogDao adminLogDao;
	
	@Override
	public AdminLog getAdminLogFetchAll(long id) {
		return adminLogDao.getAdminLogFetchAll(id);
	}
	
	@Override
	public int countAdminLogs() {
		return adminLogDao.countAdminLogs();
	}

	@Override
	public List<AdminLog> searchAdminLogs(String orderBy, boolean ascending, int offset, int maxResults) {
		return adminLogDao.searchAdminLogs(orderBy, ascending, offset, maxResults);
	}

	@Override
	@Transactional(readOnly = false)
	public void save(AdminLog adminLog) {
		adminLogDao.save(adminLog);
	}
	
	@Override
	public List<AdminLog> searchAdminLogs(String login, Date startDate, Date endDate,String orderBy, boolean ascending, int offset, int maxResults) {
		return adminLogDao.searchAdminLogs(login, startDate, endDate, orderBy, ascending, offset, maxResults);
	}
	
	@Override
	public int countAdminLogs(String login, Date startDate, Date endDate) {
		return adminLogDao.countAdminLogs(login, startDate, endDate);
	}

}
