package com.abl.db.service.impl;

import com.abl.db.dao.ApplicationDao;
import com.abl.db.model.Application;
import com.abl.db.service.ApplicationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(readOnly = true)
public class ApplicationServiceImpl implements ApplicationService {

    @Autowired
    private ApplicationDao applicationDao;

    @Override
    public Application getApplication(long id) {
        return applicationDao.getApplication(id);
    }

    @Override
    public List<Application> getApplications() {
        return applicationDao.getApplications();
    }

    @Override
    @Transactional(readOnly = false)
    public void save(Application application) {
        applicationDao.save(application);
    }

    @Override
    public List<Application> searchApplicationByNameVersion(String applicationName, int version) {
        return applicationDao.searchApplicationByNameVersion(applicationName, version);
    }

}
