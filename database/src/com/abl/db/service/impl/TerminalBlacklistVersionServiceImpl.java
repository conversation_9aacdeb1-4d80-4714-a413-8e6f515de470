package com.abl.db.service.impl;

import com.abl.db.dao.TerminalBlacklistVersionDao;
import com.abl.db.model.TerminalBlacklistVersion;
import com.abl.db.service.TerminalBlacklistVersionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional(readOnly = true)
public class TerminalBlacklistVersionServiceImpl implements TerminalBlacklistVersionService {
    @Autowired
    private TerminalBlacklistVersionDao terminalBlacklistVersionDao;

    @Override
    @Transactional(readOnly = false)
    public void save(TerminalBlacklistVersion terminalBlacklistVersion) {
        terminalBlacklistVersionDao.save(terminalBlacklistVersion);
    }
}
