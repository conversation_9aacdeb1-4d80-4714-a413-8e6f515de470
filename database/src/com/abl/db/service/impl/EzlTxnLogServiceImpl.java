package com.abl.db.service.impl;
import com.abl.db.dao.EzlTxnLogDao;
import com.abl.db.model.EzlTxnLog;
import com.abl.db.service.EzlTxnLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class EzlTxnLogServiceImpl implements EzlTxnLogService {

    @Autowired
    private EzlTxnLogDao ezlTxnLogDao;

    @Override
    @Transactional(readOnly = true)
    public EzlTxnLog getEzlTxnLog(long id) {
        return ezlTxnLogDao.getEzlTxnLog(id);
    }

    @Override
    @Transactional(readOnly = false)
    public void save(EzlTxnLog txnLog) {
        ezlTxnLogDao.save(txnLog);
    }
}
