package com.abl.db.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.db.dao.ViewTerminalProfileSummary2Dao;
import com.abl.db.model.ViewTerminalProfileSummary2;
import com.abl.db.service.ViewTerminalProfileSummary2Service;

@Service
@Transactional(readOnly = true)
public class ViewTerminalProfileSummary2ServiceImpl implements ViewTerminalProfileSummary2Service {
	@Autowired
    private ViewTerminalProfileSummary2Dao viewTerminalProfileSummaryDao;
	public List<ViewTerminalProfileSummary2> getTerminalProfileSummaryByGroup(long groupId, long releaseId){
		return viewTerminalProfileSummaryDao.getTerminalProfileSummaryByGroup(groupId, releaseId);
	}
}
