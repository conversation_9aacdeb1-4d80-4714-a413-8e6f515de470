package com.abl.db.service.impl;

import java.util.Date;
import java.util.List;

import com.abl.db.dao.MpTxnLogDao;
import com.abl.db.dao.TxnLogDao;
import com.abl.db.model.MpTxnLog;
import com.abl.db.model.TxnLog;
import com.abl.db.model.MpTxnLog.MsgType;
import com.abl.db.model.MpTxnLog.Status;
import com.abl.db.service.MpTxnLogService;
import com.abl.db.service.TxnLogService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional(readOnly = true)
public class MpTxnLogServiceImpl implements MpTxnLogService {

	@Autowired
	private MpTxnLogDao mpOfflineSalesDao;

	@Override
	@Transactional(readOnly = false)
	public void save(MpTxnLog mpOfflineSales) {
		mpOfflineSalesDao.save(mpOfflineSales);
	}
	
	@Override
	@Transactional(readOnly = false)
	public void merge(MpTxnLog mpTxnLog) {
		mpOfflineSalesDao.merge(mpTxnLog);
	}

	@Override
	public Boolean isOfflineTxnExist(String maskedCan, String jobNumber, String stan) {
		return mpOfflineSalesDao.isOfflineTxnExist(maskedCan, jobNumber, stan);
	}

	@Override
	public List<MpTxnLog>getNewOfflineSalesTxnByDateAndStatus(Status connErrStatus,Date newTxnDt, Date connErrDt, Date timeoutDt, Date lastRetryDt){
		return mpOfflineSalesDao.getNewOfflineSalesTxnByDateAndStatus(connErrStatus, newTxnDt, connErrDt, timeoutDt, lastRetryDt);

	}

	@Override
	@Transactional(readOnly = false)
	public void saveAll(List<MpTxnLog> mpOfflineSales) {
		mpOfflineSalesDao.saveAll(mpOfflineSales);
	}

	@Override
	public MpTxnLog getMpTxnLogs(String maskedCan, String jobNumber, String stan, String msgType) {
		return mpOfflineSalesDao.getMpTxnLogs(maskedCan, jobNumber, stan, msgType);
	}
	
	@Override
	public MpTxnLog getReversedMpTxnLogs(String maskedCan, String jobNumber, MsgType msgType,
			String stan) {
		return mpOfflineSalesDao.getReversedMpTxnLogs(maskedCan, jobNumber, msgType, stan);
	}
	
	@Override
	public Boolean isMpTxnExist(String maskedCan, String jobNumber, String stan, String msgType){
		return mpOfflineSalesDao.isMpTxnExist(maskedCan, jobNumber, stan, msgType);
	}
	
	@Override
	public MpTxnLog getOriSaleMpTxnForVoid(String rrn, MsgType msgType){
		return mpOfflineSalesDao.getOriSaleMpTxnForVoid(rrn, msgType);
	}
	
	@Override
	public MpTxnLog getOriVoidMpTxnForReversal(String stan){
		return mpOfflineSalesDao.getOriVoidMpTxnForReversal(stan);
	}
	
	@Override
	public List<MpTxnLog>getAllOfflineSalesTxnByDateAndStatus(Status queueStatus, Status processStatus,Status connErrStatus,Date newTxnDt, Date connErrDt, Date timeoutDt, Date lastRetryDt) {
		return mpOfflineSalesDao.getAllOfflineSalesTxnByDateAndStatus(queueStatus,processStatus, connErrStatus, newTxnDt, connErrDt, timeoutDt, lastRetryDt);
	}
	
//	@Override
//	public void updateMpTxnInQueue(List<MpTxnLog> txnLogs, Status queueStatus){
//		mpOfflineSalesDao.updateMpTxnInQueue(txnLogs, queueStatus);
//	}

}
