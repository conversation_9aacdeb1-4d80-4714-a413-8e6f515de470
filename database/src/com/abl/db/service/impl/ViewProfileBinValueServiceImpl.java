package com.abl.db.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.db.dao.ProfileBinValueDao;
import com.abl.db.dao.ViewProfileBinValueDao;
import com.abl.db.model.ProfileBinValue;
import com.abl.db.model.ViewProfileBinValue;
import com.abl.db.service.ViewProfileBinValueService;

@Service
@Transactional(readOnly = true)
public class ViewProfileBinValueServiceImpl implements ViewProfileBinValueService {
	
	@Autowired
	private ViewProfileBinValueDao viewProfileBinValueDao;
	
	public ViewProfileBinValue getProfileBinValue(long profileBinRangeId, long paramId) {
		return viewProfileBinValueDao.getProfileBinValue(profileBinRangeId, paramId);
	}
	
	
	@Override
	@Transactional(readOnly = false)
	public void saveAll(List<ViewProfileBinValue> object) {
		viewProfileBinValueDao.saveAll(object);

	}

}
