package com.abl.db.service.impl;

import com.abl.Initializable;
import com.abl.db.dao.TxnResultDao;
import com.abl.db.model.TxnResult;
import com.abl.db.service.TxnResultService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Transactional(readOnly = true)
public class TxnResultServiceImpl implements TxnResultService, Initializable {

    @Autowired
    private TxnResultDao txnResultDao;

    private Map<String, TxnResult> rcTxnResultMap;

    private boolean initialized = false;

    @Override
    public void initialize() {
        rcTxnResultMap = new HashMap<String, TxnResult>();
        List<TxnResult> txnResultList = txnResultDao.getTxnResults();
        if (txnResultList != null) {
            for (TxnResult txnResult : txnResultList) {
                rcTxnResultMap.put(txnResult.getCode(), txnResult);
            }
        }
        initialized = true;
    }

    @Override
    public boolean isInitialized() {
        return initialized;
    }

    @Override
    public void deinitialize() {
    }

    @Override
    public TxnResult getTxnResult(String code) {
        if (!isInitialized()) initialize();
        if (rcTxnResultMap != null) {
            TxnResult txnResult = rcTxnResultMap.get(code);
            return txnResult;
        }
        return null;
    }

    @Override
    public String getResponseCode(String resultCode) {
        if (rcTxnResultMap != null) {
            TxnResult txnResult = rcTxnResultMap.get(resultCode);
            if (txnResult != null) {
                return txnResult.getResponseCode();
            }
        }
        return null;
    }
}
