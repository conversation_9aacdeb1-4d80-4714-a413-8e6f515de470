package com.abl.db.service.impl;

import com.abl.db.dao.QueryFareTxnLogDao;
import com.abl.db.model.QueryFareTxnLog;
import com.abl.db.service.QueryFareTxnLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


@Service
public class QueryFareTxnLogServiceImpl implements QueryFareTxnLogService {
    @Autowired
    private QueryFareTxnLogDao queryFareTxnLogDao;

    @Override
    @Transactional(readOnly = false)
    public void save(QueryFareTxnLog txnLog) {
        queryFareTxnLogDao.save(txnLog);
    }
}
