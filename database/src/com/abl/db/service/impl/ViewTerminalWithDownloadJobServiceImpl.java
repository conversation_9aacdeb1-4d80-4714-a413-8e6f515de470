package com.abl.db.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.db.dao.ViewTerminalWithDownloadJobDao;
import com.abl.db.model.ViewTerminalWithDownloadJob;
import com.abl.db.service.ViewTerminalWithDownloadJobService;

@Service
@Transactional(readOnly = true)
public class ViewTerminalWithDownloadJobServiceImpl implements ViewTerminalWithDownloadJobService {
	@Autowired
	private ViewTerminalWithDownloadJobDao viewTerminalWithDownloadJobDao;
	
	public List<ViewTerminalWithDownloadJob> getTerminalWithDownloadJob(long jobId,long groupId) {
		return viewTerminalWithDownloadJobDao.getTerminalWithDownloadJob(jobId,groupId);
	}

}
