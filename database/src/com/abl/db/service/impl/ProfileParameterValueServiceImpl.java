package com.abl.db.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.db.dao.ProfileParameterValueDao;
import com.abl.db.model.ProfileParameterValue;
import com.abl.db.service.ProfileParameterValueService;

@Service
@Transactional(readOnly = true)
public class ProfileParameterValueServiceImpl implements
		ProfileParameterValueService {

	@Autowired
	private ProfileParameterValueDao profileParameterDao;

	@Override
	@Transactional(readOnly = false)
	public void saveAll(List<ProfileParameterValue> object) {
		profileParameterDao.saveAll(object);

	}

    @Override
    public List<ProfileParameterValue> getProfileParameterValuesByProfile(long terminalProfileId) {
        return profileParameterDao.getProfileParameterValuesByProfile(terminalProfileId);
    }
}
