package com.abl.db.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.db.dao.ViewReleasePackageSummaryDao;
import com.abl.db.model.Release;
import com.abl.db.model.ViewReleasePackageSummary;
import com.abl.db.service.ViewReleasePackageSummaryService;

@Service
@Transactional(readOnly = true)
public class ViewReleasePackageSummaryServiceImpl implements ViewReleasePackageSummaryService {
	@Autowired private ViewReleasePackageSummaryDao viewReleasePackageSummaryDao;

	 @Override
	    public int countReleases(long releaseId){
	    	return viewReleasePackageSummaryDao.countReleases(releaseId);
	    	
	    }

	    @Override
	    public List<ViewReleasePackageSummary> searchReleases(long releaseId, String orderBy,
	                                             boolean ascending, int offset, int maxResults){
	    	return viewReleasePackageSummaryDao.searchReleases(releaseId, orderBy, ascending, offset, maxResults);
	    }

}
