package com.abl.db.service.impl;

import com.abl.db.dao.TerminalLogRecordDao;
import com.abl.db.model.TerminalLogRecord;
import com.abl.db.service.TerminalLogRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Service
@Transactional(readOnly = true)
public class TerminalLogRecordServiceImpl implements TerminalLogRecordService {
    @Autowired
    private TerminalLogRecordDao terminalLogRecordDao;

    @Override
    public TerminalLogRecord getTerminaLogRecord(long id) {
        return terminalLogRecordDao.getTerminaLogRecord(id);
    }

    @Override
    public List<TerminalLogRecord> getTerminaLogRecordByLogSequence(long terminalLogId, int sequenceNo) {
        return terminalLogRecordDao.getTerminaLogRecordByLogSequence(terminalLogId, sequenceNo);
    }

    @Override
    @Transactional(readOnly = false)
    public void save(TerminalLogRecord terminalLogRecord) {
        terminalLogRecordDao.save(terminalLogRecord);
    }

    @Override
    @Transactional(readOnly = false)
    public void delete(TerminalLogRecord terminalLogRecord) {
        terminalLogRecordDao.delete(terminalLogRecord);
    }

    @Override
    public List<TerminalLogRecord> getTerminaLogFetchRecordByTerminal(long terminalId, Date startDate, Date endDate, String data, String source) {
        return terminalLogRecordDao.getTerminaLogFetchRecordByTerminal(terminalId, startDate, endDate, data, source);
    }
}
