package com.abl.db.service.impl;

import com.abl.db.dao.ProfileBinValueDao;
import com.abl.db.model.ProfileBinValue;
import com.abl.db.service.ProfileBinValueService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(readOnly = true)
public class ProfileBinValueServiceImpl implements
        ProfileBinValueService {
    @Autowired
    private ProfileBinValueDao profileBinValueDao;

    @Override
    public List<ProfileBinValue> getProfileBinValuesByBinRange(long profileBinRangeId) {
        return profileBinValueDao.getProfileBinValuesByBinRange(profileBinRangeId);
    }
}
