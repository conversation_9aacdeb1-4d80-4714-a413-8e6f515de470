package com.abl.db.service.impl;

import java.util.List;

import com.abl.db.dao.ReleaseDao;
import com.abl.db.model.AdminUser;
import com.abl.db.model.Model;
import com.abl.db.model.Release;
import com.abl.db.service.ReleaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional(readOnly = true)
public class ReleaseServiceImpl implements ReleaseService {

    @Autowired
    private ReleaseDao releaseDao;

    @Override
    public Release getRelease(long id) {
        return releaseDao.getRelease(id);
    }

    @Override
    public Release getReleaseByVersion(int version) {
        return releaseDao.getReleaseByVersion(version);
    }

    @Override
    public boolean hasModel(long releaseId, Model model) {
        return releaseDao.hasModel(releaseId, model);
    }
    
    @Override
    public List loadReleasePackage (AdminUser adminUser, String filePath)throws Exception{
    	return releaseDao.loadReleasePackage(adminUser, filePath);
    }
    
    @Override
    public Release getLatestRelease(){
    	return releaseDao.getLatestRelease();
    }
    
    @Override
    public List<Release> getAllReleases(){
    	return releaseDao.getAllReleases();
    }
    
    @Override
    @Transactional(readOnly = false)
    public void save(Release release) {
        releaseDao.save(release);

    }
    
    @Override
    public Release getAllReleasesFetchApplication(long id){
    	return releaseDao.getAllReleasesFetchApplication(id);
    }
   
   
}
