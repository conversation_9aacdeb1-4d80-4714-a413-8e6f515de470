package com.abl.db.service.impl;

import com.abl.Initializable;
import com.abl.db.dao.TxnTypeDao;
import com.abl.db.model.TxnType;
import com.abl.db.service.TxnTypeService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Transactional(readOnly = true)
public class TxnTypeServiceImpl implements TxnTypeService, Initializable {

    private static final Logger logger = Logger.getLogger(TxnTypeServiceImpl.class);

    @Autowired
    private TxnTypeDao txnTypeDao;

    private Map<String, TxnType> codeMap;    // maps txnType code to txnType
    private Map<String, TxnType> nameMap;    // maps txnType name to txnType
    private boolean initialized = false;

    @Override
    public void initialize() {
        logger.debug("initialize 1");
        codeMap = new HashMap<String, TxnType>();
        nameMap = new HashMap<String, TxnType>();
        List<TxnType> txnTypeList = txnTypeDao.getTxnTypes();
        if (txnTypeList != null) {
            for (TxnType txnType : txnTypeList) {
                logger.debug("add code:" + txnType.getCode());
                logger.debug("add name:" + txnType.getName());
                codeMap.put(txnType.getCode(), txnType);
                nameMap.put(txnType.getName(), txnType);
            }
        }
    }

    @Override
    public boolean isInitialized() {
        return initialized;
    }

    @Override
    public void deinitialize() {
    }

    @Override
    public TxnType getTxnType(String code) {
        logger.debug("code " + code + " => " + codeMap.get(code));
        return codeMap.get(code);
    }

    @Override
    public TxnType getTxnTypeForName(String name) {
        return nameMap.get(name);
    }

    @Override
    public List<TxnType> getTxnTypes() {
        return txnTypeDao.getTxnTypes();
    }

}
