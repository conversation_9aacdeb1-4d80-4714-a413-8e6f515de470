package com.abl.db.service.impl;

import com.abl.db.dao.SequenceDao;
import com.abl.db.service.SequenceService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class SequenceServiceImpl implements SequenceService {

    @Autowired
    private SequenceDao sequenceDao;

    @Override
    @Transactional(readOnly=false)
    public void createSequence(String sequenceName) {
    	sequenceDao.createSequence(sequenceName);
    }
    
    @Override
    @Transactional(readOnly=false)
	public Long nextSequence(String sequenceName) {
		return sequenceDao.nextSequence(sequenceName);
	}

}
