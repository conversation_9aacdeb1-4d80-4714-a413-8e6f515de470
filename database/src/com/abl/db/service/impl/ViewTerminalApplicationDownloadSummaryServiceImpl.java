package com.abl.db.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.db.dao.ViewTerminalApplicationDownloadSummaryDao;
import com.abl.db.model.ViewTerminalApplicationDownloadSummary;
import com.abl.db.service.ViewTerminalApplicationDownloadSummaryService;

@Service
@Transactional(readOnly = true)
public class ViewTerminalApplicationDownloadSummaryServiceImpl implements
		ViewTerminalApplicationDownloadSummaryService {

	@Autowired
	private ViewTerminalApplicationDownloadSummaryDao viewTerminalApplicationDownloadSummaryDao;

	@Override
	public List<ViewTerminalApplicationDownloadSummary> getAllTerminalApplicationDownloadSummary(long groupId, short jobStatus) {
		return viewTerminalApplicationDownloadSummaryDao.getAllTerminalApplicationDownloadSummary(groupId, jobStatus);
	}

}
