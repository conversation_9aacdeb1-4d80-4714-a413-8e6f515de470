package com.abl.db.service.impl;

import com.abl.db.dao.TerminalProfileFileDao;
import com.abl.db.model.TerminalProfileFile;
import com.abl.db.service.TerminalProfileFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(readOnly = true)
public class TerminalProfileFileServiceImpl implements TerminalProfileFileService {
    @Autowired
    private TerminalProfileFileDao terminalProfileFileDao;

    @Override
    public List<TerminalProfileFile> getTerminalProfileFile(long terminalProfileId, int recordNo) {
        return terminalProfileFileDao.getTerminalProfileFile(terminalProfileId, recordNo);
    }

    @Override
    public int getNextRecordNo(long terminalProfileId, int recordNo) {
        return terminalProfileFileDao.getNextRecordNo(terminalProfileId, recordNo);
    }
}
