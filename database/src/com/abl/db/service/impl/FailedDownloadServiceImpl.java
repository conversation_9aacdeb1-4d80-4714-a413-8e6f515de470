package com.abl.db.service.impl;

import com.abl.db.dao.FailedDownloadDao;
import com.abl.db.model.FailedDownload;
import com.abl.db.service.FailedDownloadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(readOnly = true)
public class FailedDownloadServiceImpl implements FailedDownloadService {

    @Autowired
    private FailedDownloadDao failedDownloadDao;

    @Override
    public List<FailedDownload> getFailedDownload(String vehicleNo, String serialNo) {
        return failedDownloadDao.getFailedDownload(vehicleNo, serialNo);
    }

    @Override
    @Transactional(readOnly = false)
    public void save(FailedDownload failedDownload) {
        failedDownloadDao.save(failedDownload);
    }
}
