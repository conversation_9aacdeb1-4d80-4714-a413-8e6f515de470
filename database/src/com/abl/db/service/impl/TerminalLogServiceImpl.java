package com.abl.db.service.impl;

import com.abl.db.dao.TerminalLogDao;
import com.abl.db.model.TerminalLog;
import com.abl.db.service.TerminalLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Service
@Transactional(readOnly = true)
public class TerminalLogServiceImpl implements TerminalLogService {
    @Autowired
    private TerminalLogDao terminalLogDao;

    @Override
    public TerminalLog getTerminaLogByTerminalDate(long terminalId, Date date) {
        return terminalLogDao.getTerminaLogByTerminalDate(terminalId, date);
    }

    @Override
    @Transactional(readOnly = false)
    public void save(TerminalLog terminalLog) {
        terminalLogDao.save(terminalLog);
    }
}
