package com.abl.db.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.Initializable;
import com.abl.db.dao.PageDao;
import com.abl.db.model.AdminUser;
import com.abl.db.model.Module;
import com.abl.db.model.Page;
import com.abl.db.service.PageService;

@Service
@Transactional(readOnly = true)
public class PageServiceImpl implements PageService, Initializable {

	private static final Logger logger=Logger.getLogger(PageServiceImpl.class);
	
	@Autowired
	private PageDao pageDao;
	
	private Map<String, Page> urlPageMap;	// maps page url to the corresponding page
	private boolean initialized=false;
	
	@Override
	public synchronized void initialize() throws Exception {
		if (initialized) {
			return;
		}
		logger.debug("initialize");
		urlPageMap = new HashMap<String, Page>(); 
		List<Page> pages = getPages();	//TODO: what happen if db is not available at startup?
		for (Page page: pages) {
			urlPageMap.put(page.getUrl(), page);
		}
		initialized = true;
	}
	
	@Override
	public synchronized boolean isInitialized() {
		return initialized;
	}

	@Override
	public synchronized void deinitialize() {
		urlPageMap = null;
		initialized = false;
	}
	
	public Map<String, Page> getUrlPageMap() {
		return urlPageMap;
	}

	@Override
	public Page getPage(String name) {
		return pageDao.getPage(name);
	}

	@Override
	public Page getPageFetchModule(String name) {
		return pageDao.getPageFetchModule(name);
	}
	
	@Override
	public Page getPageByUrl(String url) {
		return urlPageMap.get(url);
	}

	@Override
	public List<Page> getPages() {
		return pageDao.getPages();
	}

	@Override
	public List<Page> getAuthorizedPages(AdminUser appUser) {
		return pageDao.getAuthorizedPages(appUser);
	}

	@Override
	public List<Page> getAdminPages() {
		return pageDao.getAdminPages();
	}

	@Override
	public List<Page> getModulePages(Module module) {
		return pageDao.getModulePages(module);
	}
	
	@Override
	public List<Page> getVisiblePages(){
		return pageDao.getVisiblePages();
	}

}
