package com.abl.db.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.db.dao.ViewApplicationBinSummaryDao;
import com.abl.db.model.ViewApplicationBinSummary;
import com.abl.db.service.ViewApplicationBinSummaryService;

@Service
@Transactional(readOnly = true)
public class ViewApplicationBinSummaryServiceImpl implements ViewApplicationBinSummaryService {
	
	@Autowired private ViewApplicationBinSummaryDao viewapplicationBinSummaryDao;
	
	public List<ViewApplicationBinSummary> getApplicationBinSummaryByApp(long appId) {
		return viewapplicationBinSummaryDao.getApplicationBinSummaryByApp(appId);
	}
	
	public ViewApplicationBinSummary getLastApplicationBinSummary() {
		return viewapplicationBinSummaryDao.getLastApplicationBinSummary();
	}
	
	

}
