package com.abl.db.service.impl;

import com.abl.db.dao.AliAlipayTxnLogDao;
import com.abl.db.model.AliAlipayTxnLog;
import com.abl.db.service.AliAlipayTxnLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class AliAlipayTxnLogServiceImpl implements AliAlipayTxnLogService {

    @Autowired
    private AliAlipayTxnLogDao aliAlipayTxnLogDao;

    @Override
    @Transactional(readOnly = true)
    public AliAlipayTxnLog getAliAlipayTxnLog(long id) {
        return aliAlipayTxnLogDao.getAliAlipayTxnLog(id);
    }

    @Override
    @Transactional(readOnly = false)
    public void save(AliAlipayTxnLog txnLog) {
    	aliAlipayTxnLogDao.save(txnLog);
    }

    @Override
    @Transactional(readOnly = false)
    public void update(AliAlipayTxnLog txnLog) {
        aliAlipayTxnLogDao.update(txnLog);
    }

    @Override
    @Transactional(readOnly = true)
    public AliAlipayTxnLog getAliAlipayTxnLogByAliTxnLogId(long id) {
        return aliAlipayTxnLogDao.getAliAlipayTxnLogByAliTxnLogId(id);
    }

    @Override
    public AliAlipayTxnLog getSuccessfulFreezeByAliTxnLogId(long id) {
        return aliAlipayTxnLogDao.getSuccessfulFreezeByAliTxnLogId(id);
    }

    @Override
    @Transactional(readOnly = false)
    public int updateAliAlipayStatus(long id, AliAlipayTxnLog.Status status){
        return aliAlipayTxnLogDao.updateAliAlipayStatus(id, status);
    }

    @Override
    @Transactional(readOnly = true)
    public AliAlipayTxnLog getByBookingRef(final String bookingRef, final String msgType, final String ascOrDesc) {
        return aliAlipayTxnLogDao.getByBookingRef(bookingRef, msgType, ascOrDesc);
    }
}
