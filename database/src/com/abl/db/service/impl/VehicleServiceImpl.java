package com.abl.db.service.impl;

import java.util.List;

import com.abl.db.dao.VehicleDao;
import com.abl.db.model.AdminUser;
import com.abl.db.model.Vehicle;
import com.abl.db.model.VehicleGroup;
import com.abl.db.service.VehicleService;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional(readOnly = true)
public class VehicleServiceImpl implements VehicleService {
    private static final Logger logger = Logger.getLogger(VehicleServiceImpl.class);

    @Autowired
    private VehicleDao vehicleDao;

    @Override
    public Vehicle getVehicle(String vehicleId) {
        return vehicleDao.getVehicle(vehicleId);
    }
    
    @Override
    public List<Vehicle> getVehiclesByMultipleCriteria(List<String> ivdVersion, List<String>vehicleType, List<String>vehicleModel, String vehicleNo){
    	return vehicleDao.getVehiclesByMultipleCriteria(ivdVersion, vehicleType, vehicleModel, vehicleNo);
    }
    
    @Override
    public List<Vehicle> searchVehicles(String vehicleNo, String orderBy, boolean ascending, int offset, int maxResults){
    	return vehicleDao.searchVehicles(vehicleNo,orderBy, ascending, offset, maxResults);
    }
    
    @Override
    public int countVehicles(String vehicleNo){
    	return vehicleDao.countVehicles(vehicleNo);
    	
    }
    @Override
    public int countVehiclesByMultipleCriteria(List<String> ivdVersion, List<String>vehicleType, List<String>vehicleModel, String vehicleNo){
    	return vehicleDao.countVehiclesByMultipleCriteria(ivdVersion, vehicleType, vehicleModel, vehicleNo);
    }
    
  
    
  
}
