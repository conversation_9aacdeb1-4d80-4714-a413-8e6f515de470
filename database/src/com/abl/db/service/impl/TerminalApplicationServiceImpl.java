package com.abl.db.service.impl;

import com.abl.db.dao.TerminalApplicationDao;
import com.abl.db.model.Release;
import com.abl.db.model.TerminalApplication;
import com.abl.db.model.TmsTerminal;
import com.abl.db.service.TerminalApplicationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional(readOnly = true)
public class TerminalApplicationServiceImpl implements TerminalApplicationService {
    @Autowired
    private TerminalApplicationDao terminalApplicationDao;

    @Override
    public Release getLatestRelease(TmsTerminal terminal) {
        return terminalApplicationDao.getLatestRelease(terminal);
    }

    @Override
    @Transactional(readOnly = false)
    public void save(TerminalApplication terminalApplication) {
        terminalApplicationDao.save(terminalApplication);
    }
}
