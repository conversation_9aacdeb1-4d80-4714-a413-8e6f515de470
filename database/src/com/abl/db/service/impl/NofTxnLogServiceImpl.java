package com.abl.db.service.impl;

import com.abl.db.dao.NofTxnLogDao;
import com.abl.db.model.NofTxnLog;
import com.abl.db.service.NofTxnLogService;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class NofTxnLogServiceImpl implements NofTxnLogService {

    @Autowired
    private NofTxnLogDao nofTxnLogDao;

    @Override
    @Transactional(readOnly = true)
    public NofTxnLog getNofTxnLog(long id) {
        return nofTxnLogDao.getNofTxnLog(id);
    }

    @Override
    @Transactional(readOnly = true)
    public NofTxnLog getNofTxnLogByRrn(String rrn) {
    	return nofTxnLogDao.getNofTxnLogByRrn(rrn);
    }
    
    @Override
    @Transactional(readOnly = true)
    public NofTxnLog getCfaNofTxnLogByRrn(String rrn) {
    	NofTxnLog nofTxnLog = nofTxnLogDao.getNofTxnLogByRrn(rrn);
    	if (nofTxnLog==null) {
    		return null;
    	} else if (NofTxnLog.MSG_TYPE_CFA.equals(nofTxnLog.getMsgType())) {
    		return nofTxnLog;
    	} else {
    		return null;
    	}
    }
    
    @Override
    @Transactional(readOnly = true)
    public NofTxnLog getPurchaseNofTxnLogByMidTidStan(String mid, String tid, String stan) {
    	return nofTxnLogDao.getPurchaseNofTxnLogByMidTidStan(mid, tid, stan);
    }

    @Override
    @Transactional(readOnly = true)
    public NofTxnLog getOfflinePurchaseNofTxnLogByMidTidStanRespCode(String mid, String tid, String stan, String respCode) {
    	return nofTxnLogDao.getOfflinePurchaseNofTxnLogByMidTidStanRespCode(mid, tid, stan, respCode);
    }

    @Override
    @Transactional(readOnly = true)
    public NofTxnLog getOfflinePurchaseNofTxnLogByTransIdRespCode(String transId, String respCode) {
    	return nofTxnLogDao.getOfflinePurchaseNofTxnLogByTransIdRespCode(transId, respCode);
    }
    
    @Override
    @Transactional(readOnly = true)
    public NofTxnLog getPurchaseNofTxnLogByTransId(String transId) {
    	return nofTxnLogDao.getPurchaseNofTxnLogByTransId(transId);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<NofTxnLog> getNofTxnLogs(NofTxnLog.Status status, int maxResults) {
    	return nofTxnLogDao.getNofTxnLogs(status, maxResults);
    }
    
    @Override
    @Transactional(readOnly = false)
    public int updateTxResultAndStatus(long id, String txResult, NofTxnLog.Status status) {
    	return nofTxnLogDao.updateTxResultAndStatus(id, txResult, status);
    }
    
    @Override
    @Transactional(readOnly = false)
    public int resetPendingStatus() {
    	return nofTxnLogDao.resetPendingStatus();
    }
    
    @Override
    @Transactional(readOnly = false)
    public void save(NofTxnLog txnLog) {
    	nofTxnLogDao.save(txnLog);
    }

    @Override
    @Transactional(readOnly = true)
    public List<NofTxnLog> getNofDeclinedPaymentTxns(NofTxnLog.DcpStatus dcpStatus, Date retryDt, int maxResults) {
        return nofTxnLogDao.getNofDeclinedPaymentTxns(dcpStatus, retryDt, maxResults);
    }

    @Override
    @Transactional(readOnly = false)
    public int updateDcpStatus(long id, NofTxnLog.DcpStatus dcpStatus) {
        return nofTxnLogDao.updateDcpStatus(id, dcpStatus);
    }

}
