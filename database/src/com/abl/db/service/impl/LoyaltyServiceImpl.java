package com.abl.db.service.impl;

import com.abl.db.dao.LoyaltyDao;
import com.abl.db.model.Loyalty;
import com.abl.db.model.LoyaltyFile;
import com.abl.db.service.LoyaltyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: chinyew
 * Date: 21/12/12
 * Time: 3:15 PM
 * To change this template use File | Settings | File Templates.
 */
@Service
@Transactional(readOnly = true)
public class LoyaltyServiceImpl implements LoyaltyService  {

    @Autowired
    private LoyaltyDao loyaltyDao;

    @Override
    @Transactional(readOnly = false)
    public void save(Loyalty loyalty) {
        loyaltyDao.save(loyalty);
    }

    @Override
    @Transactional(readOnly = false)
    public void save(LoyaltyFile loyaltyFile) {
        loyaltyDao.save(loyaltyFile);
    }

    @Override
    public boolean checkDuplicate(String tid, String completeTime, String tripNo) {
        return loyaltyDao.checkDuplicate(tid, completeTime, tripNo);
    }

    @Override
    @Transactional(readOnly = false)
    public List<Loyalty> getUnprocessedLoyalty() {
        return loyaltyDao.getUnprocessedLoyalty();
    }

    public List<LoyaltyFile> getLoyaltyFiles(Date from, Date to) {
        return loyaltyDao.getLoyaltyFiles(from, to);
    }

}
