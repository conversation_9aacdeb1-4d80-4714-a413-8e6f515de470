package com.abl.db.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.db.model.ViewTerminalsBlacklistNotDownloaded;
import com.abl.db.service.ViewTerminalsBlacklistNotDownloadedService;
import com.abl.db.dao.ViewTerminalsBlacklistNotDownloadedDao;

@Service
@Transactional(readOnly = true)
public class ViewTerminalsBlacklistNotDownloadedServiceImpl implements ViewTerminalsBlacklistNotDownloadedService {
	@Autowired private ViewTerminalsBlacklistNotDownloadedDao viewTerminalsBlacklistNotDownloadedDao;
	
    @Override
	public List<ViewTerminalsBlacklistNotDownloaded> getTerminalBlacklistWithoutDownloadJob(long jobId) {
		return viewTerminalsBlacklistNotDownloadedDao.getTerminalBlacklistWithoutDownloadJob(jobId);
	}
	
	@Override
	public List<ViewTerminalsBlacklistNotDownloaded> getAllTerminalBlacklistWithoutDownloadJob() {
		return viewTerminalsBlacklistNotDownloadedDao.getAllTerminalBlacklistWithoutDownloadJob();
	}

}
