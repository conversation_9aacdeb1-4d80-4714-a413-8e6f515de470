package com.abl.db.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.db.dao.VcTxnLogDao;
import com.abl.db.model.VcTxnLog;
import com.abl.db.model.VcTxnLog.MsgType;
import com.abl.db.model.VcTxnLog.Status;
import com.abl.db.service.VcTxnLogService;

@Service
@Transactional(readOnly = true)
public class VcTxnLogServiceImpl implements VcTxnLogService {

	
	@Autowired
	private VcTxnLogDao vcTxnLogDao;
	
	@Override
	@Transactional(readOnly = false)
	public void save(VcTxnLog vcTxnLog) {
		vcTxnLogDao.save(vcTxnLog);
	}

	@Override
	public Boolean isOfflineTxnExist(String maskedCan, String jobNumber, String stan) {
		return vcTxnLogDao.isOfflineTxnExist(maskedCan, jobNumber, stan);
	}

	@Override
	public List<VcTxnLog> getNewOfflineSalesTxnByDateAndStatus(
			Status connErrStatus, Date newTxnDt, Date connErrDt,
			Date timeoutDt, Date lastRetryDt) {
		return vcTxnLogDao.getNewOfflineSalesTxnByDateAndStatus(
				connErrStatus, newTxnDt, connErrDt, timeoutDt, lastRetryDt);
	}

	@Override
	@Transactional(readOnly = false)
	public void saveAll(List<VcTxnLog> salesList) {
		vcTxnLogDao.saveAll(salesList);
	}

	@Override
	public VcTxnLog getVcTxnLogs(
			String maskedCan, String jobNumber, String stan, String msgType) {
		return vcTxnLogDao.getVcTxnLogs(maskedCan, jobNumber, stan, msgType);
	}

	@Override
	public VcTxnLog getReversedVcTxnLogs(String maskedCan, String jobNumber, MsgType msgType, String stan) {
		return vcTxnLogDao.getReversedVcTxnLogs(maskedCan, jobNumber, msgType, stan);
	}

	@Override
	public Boolean isVcTxnExist(String maskedCan, String jobNumber, String stan, String msgType) {
		return vcTxnLogDao.isVcTxnExist(maskedCan, jobNumber, stan, msgType);
	}

	@Override
	public VcTxnLog getOriSaleVcTxnForVoid(String rrn, MsgType msgType) {
		return vcTxnLogDao.getOriSaleVcTxnForVoid(rrn, msgType);
	}

	@Override
	public VcTxnLog getOriVoidVcTxnForReversal(String stan) {
		return vcTxnLogDao.getOriVoidVcTxnForReversal(stan);
	}

	@Override
	@Transactional(readOnly = false)
	public void merge(VcTxnLog vcTxnLog) {
		vcTxnLogDao.merge(vcTxnLog);
	}

	@Override
	public List<VcTxnLog> getAllOfflineSalesTxnByDateAndStatus(
			Status queueStatus, Status processStatus,
			Status connErrStatus, Date newTxnDt, Date connErrDt, Date timeoutDt, Date lastRetryDt) {
		return vcTxnLogDao.getAllOfflineSalesTxnByDateAndStatus(
				queueStatus,processStatus, connErrStatus, newTxnDt, 
				connErrDt, timeoutDt, lastRetryDt);

	}

	@Override
	public VcTxnLog getOriSaleVcTxnForVoid(String rrn, String jobNumber, MsgType msgType) {
		return vcTxnLogDao.getOriSaleVcTxnForVoid(rrn, jobNumber, msgType);
	}

	@Override
	public VcTxnLog getOriVoidVcTxnForReversal(String rrn, String jobNumber, MsgType msgType) {
		return vcTxnLogDao.getOriVoidVcTxnForReversal(rrn, jobNumber, msgType);		
	}

	@Override
	public List<VcTxnLog> getReversalVcTxnLogs(String maskedCan, String jobNumber, String stan) {		
		return vcTxnLogDao.getReversalVcTxnLogs(maskedCan, jobNumber, stan);
	}

}
