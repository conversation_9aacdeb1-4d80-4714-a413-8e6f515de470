package com.abl.db.service.impl;

import com.abl.db.dao.PlPaylahTxnLogDao;
import com.abl.db.model.PlPaylahTxnLog;
import com.abl.db.service.PlPaylahTxnLogService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class PlPaylahTxnLogServiceImpl implements PlPaylahTxnLogService {

    @Autowired
    private PlPaylahTxnLogDao plPaylahTxnLogDao;

    @Override
    @Transactional(readOnly = true)
    public PlPaylahTxnLog getPlPaylahTxnLog(long id) {
        return plPaylahTxnLogDao.getPlPaylahTxnLog(id);
    }
    
    @Override
    @Transactional(readOnly = true)
    public PlPaylahTxnLog getPlPaylahTxnLogByPlTxnLogId(long id) {
        return plPaylahTxnLogDao.getPlPaylahTxnLogByPlTxnLogId(id);
    }
    
    @Override
    @Transactional(readOnly = true)
    public PlPaylahTxnLog getPlPaylahTxnLogByPlTxnLogIdAndMsgType(long id, String msgType) {
    	return plPaylahTxnLogDao.getPlPaylahTxnLogByPlTxnLogIdAndMsgType(id, msgType);
    }
    
    @Override
    @Transactional(readOnly = false)
    public void save(PlPaylahTxnLog txnLog) {
    	plPaylahTxnLogDao.save(txnLog);
    }

    @Override
    @Transactional(readOnly = false)
    public void update(PlPaylahTxnLog txnLog) {
    	plPaylahTxnLogDao.update(txnLog);
    }
}
