package com.abl.db.service.impl;

import java.util.Date;
import java.util.List;

import com.abl.db.dao.CofTxnLogDao;
import com.abl.db.dao.TxnLogDao;
import com.abl.db.model.CofTxnLog;
import com.abl.db.model.TxnLog;
import com.abl.db.model.CofTxnLog.MsgType;
import com.abl.db.model.CofTxnLog.Status;
import com.abl.db.service.CofTxnLogService;
import com.abl.db.service.TxnLogService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional(readOnly = true)
public class CofTxnLogServiceImpl implements CofTxnLogService {

	@Autowired
	private CofTxnLogDao cofTxnLogDao;

	@Override
	@Transactional(readOnly = false)
	public void save(CofTxnLog cofTxnLog) {
		cofTxnLogDao.save(cofTxnLog);
	}

	@Override
	@Transactional(readOnly = false)
	public void merge(CofTxnLog cofTxnLog) {
		cofTxnLogDao.merge(cofTxnLog);
	}

	@Override
	public Boolean isTxnExist(String maskedCan, String jobNumber, String stan) {
		return cofTxnLogDao.isTxnExist(maskedCan, jobNumber, stan);
	}

	@Override
	public List<CofTxnLog>getNewOfflineSalesTxnByDateAndStatus(Status connErrStatus,Date newTxnDt, Date connErrDt, Date timeoutDt, Date lastRetryDt){
		return cofTxnLogDao.getNewOfflineSalesTxnByDateAndStatus(connErrStatus, newTxnDt, connErrDt, timeoutDt, lastRetryDt);

	}

	@Override
	@Transactional(readOnly = false)
	public void saveAll(List<CofTxnLog> cofTxnLogs) {
		cofTxnLogDao.saveAll(cofTxnLogs);
	}

	@Override
	public CofTxnLog getCofTxnLogs(String maskedCan, String jobNumber, String stan, String msgType) {
		return cofTxnLogDao.getCofTxnLogs(maskedCan, jobNumber, stan, msgType);
	}

	@Override
	public CofTxnLog getReversedCofTxnLogs(String maskedCan, String jobNumber, MsgType msgType,
			String stan) {
		return cofTxnLogDao.getReversedCofTxnLogs(maskedCan, jobNumber, msgType, stan);
	}

	@Override
	public List<CofTxnLog>getAllOfflineSalesTxnByDateAndStatus(Status queueStatus, Status processStatus,Status connErrStatus,Date newTxnDt, Date connErrDt, Date timeoutDt, Date lastRetryDt) {
		return cofTxnLogDao.getAllOfflineSalesTxnByDateAndStatus(queueStatus,processStatus, connErrStatus, newTxnDt, connErrDt, timeoutDt, lastRetryDt);
	}

}
