package com.abl.db.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.db.dao.ViewProfileParameterValueSummaryDao;
import com.abl.db.model.ViewProfileParameterValueSummary;
import com.abl.db.service.ViewProfileParameterValueSummaryService;


@Service
@Transactional(readOnly = true)
public class ViewProfileParameterValueSummaryServiceImpl implements ViewProfileParameterValueSummaryService {
	
	@Autowired
	private ViewProfileParameterValueSummaryDao viewProfileParameterValueSummaryDao;
	
	public List<ViewProfileParameterValueSummary> getProfileParameterValueSummary(long profileId) {
		return viewProfileParameterValueSummaryDao.getProfileParameterValueSummary(profileId);
	}

}
