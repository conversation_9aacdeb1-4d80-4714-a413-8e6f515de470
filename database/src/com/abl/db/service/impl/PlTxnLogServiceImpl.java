package com.abl.db.service.impl;

import com.abl.db.dao.PlTxnLogDao;
import com.abl.db.model.PlTxnLog;
import com.abl.db.service.PlTxnLogService;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class PlTxnLogServiceImpl implements PlTxnLogService {

    @Autowired
    private PlTxnLogDao plTxnLogDao;

    @Override
    @Transactional(readOnly = true)
    public PlTxnLog getPlTxnLog(long id) {
        return plTxnLogDao.getPlTxnLog(id);
    }

    @Override
    @Transactional(readOnly = true)
    public PlTxnLog getPlTxnLogByRrn(String rrn) {
    	return plTxnLogDao.getPlTxnLogByRrn(rrn);
    }
    
    @Override
    @Transactional(readOnly = true)
    public PlTxnLog getOfflineSalePlTxnLogByMidTidStanRespCode(String mid, String tid, String stan, String respCode) {
    	return plTxnLogDao.getOfflineSalePlTxnLogByMidTidStanRespCode(mid, tid, stan, respCode);
    }
    
    @Override
    @Transactional(readOnly = true)
    public PlTxnLog getOfflinePurchasePlTxnLogByTransIdRespCode(String transId, String respCode) {
    	return plTxnLogDao.getOfflinePurchasePlTxnLogByTransIdRespCode(transId, respCode);
    }
    
    @Override
    @Transactional(readOnly = true)
    public PlTxnLog getSalePlTxnLogByMidTidStan(String mid, String tid, String stan) {
    	return plTxnLogDao.getSalePlTxnLogByMidTidStan(mid, tid, stan);
    }
    
    @Override
    @Transactional(readOnly = true)
    public PlTxnLog getSalePlTxnLogByTransId(String transId) {
    	return plTxnLogDao.getSalePlTxnLogByTransId(transId);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<PlTxnLog> getPlTxnLogs(PlTxnLog.Status status, int maxResults) {
    	return plTxnLogDao.getPlTxnLogs(status, maxResults);
    }
    
    @Override
    @Transactional(readOnly = false)
    public int updateTxResultAndStatus(long id, String txResult, PlTxnLog.Status status) {
    	return plTxnLogDao.updateTxResultAndStatus(id, txResult, status);
    }

    @Override
    @Transactional(readOnly = false)
    public int updateTxResultAndStatusAndDcpStatus(long id, String txResult, PlTxnLog.Status status, PlTxnLog.DcpStatus dcpStatus) {
        return plTxnLogDao.updateTxResultAndStatusAndDcpStatus(id, txResult, status, dcpStatus);
    }

    @Override
    @Transactional(readOnly = false)
    public int resetPendingStatus() {
    	return plTxnLogDao.resetPendingStatus();
    }
    
    @Override
    @Transactional(readOnly = false)
    public int resetEnquiringStatus() {
    	return plTxnLogDao.resetEnquiringStatus();
    }
    
    @Override
    @Transactional(readOnly = false)
    public void save(PlTxnLog txnLog) {
    	plTxnLogDao.save(txnLog);
    }

    @Override
    @Transactional(readOnly = true)
    public List<PlTxnLog> getPaylahDeclinedPaymentTxns(PlTxnLog.DcpStatus dcpStatus, Date retryDt, int maxResults) {
        return plTxnLogDao.getPaylahDeclinedPaymentTxns(dcpStatus, retryDt, maxResults);
    }

    @Override
    @Transactional(readOnly = false)
    public int updateDcpStatus(long id, PlTxnLog.DcpStatus dcpStatus) {
        return plTxnLogDao.updateDcpStatus(id, dcpStatus);
    }

}
