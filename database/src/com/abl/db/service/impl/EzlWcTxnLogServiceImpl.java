package com.abl.db.service.impl;

import com.abl.db.dao.EzlWcTxnLogDao;
import com.abl.db.model.EzlWcTxnLog;
import com.abl.db.service.EzlWcTxnLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class EzlWcTxnLogServiceImpl implements EzlWcTxnLogService {

    @Autowired
    private EzlWcTxnLogDao nofNetsTxnLogDao;

    @Override
    @Transactional(readOnly = true)
    public EzlWcTxnLog getEzlWcTxnLog(long id) {
        return nofNetsTxnLogDao.getEzlWcTxnLog(id);
    }

    @Override
    @Transactional(readOnly = true)
    public EzlWcTxnLog getEzlWcTxnLogByEzlTxnLogId(long id) {
    	return nofNetsTxnLogDao.getEzlWcTxnLogByEzlTxnLogId(id);
    }

    @Override
    @Transactional(readOnly = false)
    public void save(EzlWcTxnLog txnLog) {
    	nofNetsTxnLogDao.save(txnLog);
    }

    @Override
    @Transactional(readOnly = false)
    public void update(EzlWcTxnLog txnLog) {
    	nofNetsTxnLogDao.update(txnLog);
    }
}
