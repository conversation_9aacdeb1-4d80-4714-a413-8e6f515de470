package com.abl.db.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.db.dao.ModuleDao;
import com.abl.db.model.Module;
import com.abl.db.service.ModuleService;

@Service
@Transactional(readOnly = true)
public class ModuleServiceImpl implements ModuleService {

	@Autowired
	private ModuleDao moduleDao;
	
	@Override
	public List<Module> getModules() {
		return moduleDao.getModules();
	}

	@Override
	public List<Module> getModules(boolean isVisible) {
		return moduleDao.getModules(isVisible);
	}

	
}
