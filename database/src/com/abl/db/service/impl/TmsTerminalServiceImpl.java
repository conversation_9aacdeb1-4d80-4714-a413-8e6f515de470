package com.abl.db.service.impl;

import com.abl.db.dao.TmsTerminalDao;
import com.abl.db.model.TmsTerminal;
import com.abl.db.service.TmsTerminalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(readOnly = true)
public class TmsTerminalServiceImpl implements TmsTerminalService {

    @Autowired
    private TmsTerminalDao terminalDao;

    @Override
    public TmsTerminal getTerminal(long id) {
        return terminalDao.getTerminal(id);

    }

    @Override
    public TmsTerminal getTerminalFetchModel(long id) {
        return terminalDao.getTerminalFetchModel(id);
    }

    @Override
    @Transactional(readOnly = false)
    public void saveAll(List<TmsTerminal> object) {
        terminalDao.saveAll(object);
    }

    @Override
    public List<TmsTerminal> searchTerminals(String serialNo, String vehicleNo, long modelId,
                                             String orderBy, boolean ascending, int offset, int maxResults) {
        return terminalDao.searchTerminals(serialNo, vehicleNo,modelId, orderBy, ascending,
                offset, maxResults);
    }

    @Override
    public int countTerminals(String serialNo, String vehicleNo, long modelId) {
        return terminalDao.countTerminals(serialNo, vehicleNo, modelId);
    }

    @Override
    @Transactional(readOnly = false)
    public void save(TmsTerminal terminal) {
        terminalDao.save(terminal);

    }

    @Override
    public TmsTerminal getTerminalBySerialNo(String serialNo) {
        return terminalDao.getTerminalBySerialNo(serialNo);
    }

    @Override
    public TmsTerminal getTerminalForUpdate(String serialNo) {
        return terminalDao.getTerminalForUpdate(serialNo);
    }
    
    @Override
    @Transactional(readOnly = false)
    public int unpairOtherTerminalsForVehicleNo(String vehicleNo, String serialNo){
    	return terminalDao.unpairOtherTerminalsForVehicleNo(vehicleNo, serialNo);
    }

}
