package com.abl.db.service.impl;

import com.abl.db.dao.ParameterDefinitionDao;
import com.abl.db.model.ParameterDefinition;
import com.abl.db.service.ParameterDefinitionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(readOnly = true)
public class ParameterDefinitionServiceImpl implements
        ParameterDefinitionService {

    @Autowired
    private ParameterDefinitionDao parameterDefinitionDao;

    @Override
    public ParameterDefinition getParameterDefinition(long id) {
        return parameterDefinitionDao.getParameterDefinition(id);
    }

    @Override
    @Transactional(readOnly = false)
    public void save(ParameterDefinition parameterDefinition) {
        parameterDefinitionDao.save(parameterDefinition);
    }

    @Override
    public List<ParameterDefinition> getParameterDefinitionFetchApplication(long appId) {
        return parameterDefinitionDao.getParameterDefinitionFetchApplication(appId);

    }

    @Override
    public List<ParameterDefinition> getBinParameterDefinitionFetchApplication(long appId) {
        return parameterDefinitionDao.getBinParameterDefinitionFetchApplication(appId);

    }

    @Override
    public ParameterDefinition getParameterDefinitionByNameApp(String name, long applicationId) {
        return parameterDefinitionDao.getParameterDefinitionByNameApp(name, applicationId);
    }

    @Override
    public int countParameters(long appId) {
        return parameterDefinitionDao.countParameters(appId);
    }
    
    @Override
    public List<ParameterDefinition> getFeeParameterDefinitionFetchApplication(long appId) {
        return parameterDefinitionDao.getFeeParameterDefinitionFetchApplication(appId);

    }

}
