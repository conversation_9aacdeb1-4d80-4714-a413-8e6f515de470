package com.abl.db.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.db.dao.IvdDao;
import com.abl.db.model.Ivd;
import com.abl.db.service.IvdService;

@Service
@Transactional(readOnly = true)
public class IvdServiceImpl implements IvdService {

	@Autowired
	private IvdDao ivdDao;

	public List<Ivd> getIvd() {
		return ivdDao.getIvd();

	}

}
