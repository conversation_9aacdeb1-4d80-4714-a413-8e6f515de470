package com.abl.db.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.db.dao.VehicleTypeDao;
import com.abl.db.model.VehicleType;
import com.abl.db.service.VehicleTypeService;

@Service
@Transactional(readOnly = true)
public class VehicleTypeServiceImpl implements VehicleTypeService {

	@Autowired
	private VehicleTypeDao vehicleTypeDao;

	public List<VehicleType> getVehicleTypes() {
		return vehicleTypeDao.getVehicleTypes();

	}

}
