package com.abl.db.service.impl;

import com.abl.db.dao.PlAccountDao;
import com.abl.db.model.PlAccount;
import com.abl.db.service.PlAccountService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class PlAccountServiceImpl implements PlAccountService {

    @Autowired
    private PlAccountDao plAccountDao;

    @Override
    @Transactional(readOnly = true)
    public PlAccount getPlAccount(long id) {
        return plAccountDao.getPlAccount(id);
    }

	@Override
	@Transactional(readOnly = true)
	public PlAccount getPlAccountByUserId(String userId) {
		return plAccountDao.getPlAccountByUserId(userId);
	}
	
	@Override
    @Transactional(readOnly = false)
	public void saveOrUpdate(PlAccount account) {
		plAccountDao.saveOrUpdate(account);
	}
	
    @Override
    @Transactional(readOnly = false)
    public void save(PlAccount account) {
    	plAccountDao.save(account);
    }

	@Override
	@Transactional(readOnly = false)
	public void update(PlAccount account) {
		plAccountDao.update(account);
	}

}
