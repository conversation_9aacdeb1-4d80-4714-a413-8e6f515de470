package com.abl.db.service.impl;

import com.abl.db.dao.NofNetsTxnLogDao;
import com.abl.db.model.NofNetsTxnLog;
import com.abl.db.service.NofNetsTxnLogService;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class NofNetsTxnLogServiceImpl implements NofNetsTxnLogService {

    @Autowired
    private NofNetsTxnLogDao nofNetsTxnLogDao;

    @Override
    @Transactional(readOnly = true)
    public NofNetsTxnLog getNofNetsTxnLog(long id) {
        return nofNetsTxnLogDao.getNofNetsTxnLog(id);
    }

    @Override
    @Transactional(readOnly = true)
    public NofNetsTxnLog getNofNetsTxnLogByNofTxnLogId(long id) {
    	return nofNetsTxnLogDao.getNofNetsTxnLogByNofTxnLogId(id);
    }

    @Override
    @Transactional(readOnly = true)
    public List<NofNetsTxnLog> getNofNetsTxnLogsFetchNofTxnLog(NofNetsTxnLog.ReversalState reversalState, int maxResults) {
    	return nofNetsTxnLogDao.getNofNetsTxnLogsFetchNofTxnLog(reversalState, maxResults);
    }
    
    @Override
    @Transactional(readOnly = false)
    public int resetNewNofNetsTxnLogForReversal() {
    	return nofNetsTxnLogDao.resetNewNofNetsTxnLogForReversal();
    }

    @Override
    @Transactional(readOnly = false)
    public int resetReversingNofNetsTxnLogForReversal() {
    	return nofNetsTxnLogDao.resetReversingNofNetsTxnLogForReversal();
    }
    
    @Override
    @Transactional(readOnly = false)
    public void save(NofNetsTxnLog txnLog) {
    	nofNetsTxnLogDao.save(txnLog);
    }

    @Override
    @Transactional(readOnly = false)
    public void update(NofNetsTxnLog txnLog) {
    	nofNetsTxnLogDao.update(txnLog);
    }
}
