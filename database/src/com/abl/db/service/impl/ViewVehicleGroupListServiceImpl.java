package com.abl.db.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.db.dao.ViewVehicleGroupListDao;
import com.abl.db.model.ViewVehicleGroupList;
import com.abl.db.service.ViewVehicleGroupListService;

@Service
@Transactional(readOnly = true)
public class ViewVehicleGroupListServiceImpl implements ViewVehicleGroupListService {
	
	@Autowired
    private ViewVehicleGroupListDao viewVehicleGroupListDao;
	public List<ViewVehicleGroupList> searchVehicles(String vehicleNo, long groupId, String orderBy, boolean ascending, int offset, int maxResults){
		return viewVehicleGroupListDao.searchVehicles(vehicleNo, groupId, orderBy, ascending, offset, maxResults);
	}
	
	public int countVehicles(String vehicleNo, long groupId) {
		return viewVehicleGroupListDao.countVehicles(vehicleNo, groupId);
	}

}
