package com.abl.db.service.impl;

import java.util.Date;
import java.util.List;

import com.abl.db.dao.BlacklistVersionDao;
import com.abl.db.model.BlacklistVersion;
import com.abl.db.model.Domain;
import com.abl.db.service.BlacklistVersionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional(readOnly = true)
public class BlacklistVersionServiceImpl implements BlacklistVersionService {

    @Autowired
    private BlacklistVersionDao blacklistVersionDao;

    @Override
    public BlacklistVersion getBlacklistVersion(long id) {
        return blacklistVersionDao.getBlacklistVersion(id);
    }

    @Override
    public BlacklistVersion getBlacklistVersionByVersion(int version) {
        return blacklistVersionDao.getBlacklistVersionByVersion(version);
    }

    @Override
    public BlacklistVersion getLatestBlacklistVersion(short status) {
        return blacklistVersionDao.getLatestBlacklistVersion(status);
    }

    @Override
    public BlacklistVersion getBlacklistVersion(String fullCans, String fullRange, String smallCans, String smallRange) {
        return blacklistVersionDao.getBlacklistVersion(fullCans, fullRange, smallCans, smallRange);
    }
    
    @Override
	@Transactional(readOnly = false)
	public void save(BlacklistVersion blacklistVersion) {
    	blacklistVersionDao.save(blacklistVersion);
	}
    
    @Override
    public List<BlacklistVersion> getBlacklistVersionByStatus(short status){
    	return blacklistVersionDao.getBlacklistVersionByStatus(status);
    }
    
    @Override
	@Transactional(readOnly = false)
    public void delete(BlacklistVersion blacklistVersion){
    	blacklistVersionDao.delete(blacklistVersion);
    }
    
    @Override
    public BlacklistVersion getBlacklistVersionByScheduleDate(Date scheduleDate){
    	return blacklistVersionDao.getBlacklistVersionByScheduleDate(scheduleDate);
    }
    @Override
    public BlacklistVersion getPendingBlacklistVersionByScheduleDateForBatch(Date scheduleDate){
    	return blacklistVersionDao.getPendingBlacklistVersionByScheduleDateForBatch(scheduleDate);
    }
    
    
   
}
