package com.abl.db.service.impl;

import com.abl.db.dao.TxnLogDao;
import com.abl.db.model.TxnLog;
import com.abl.db.service.TxnLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional(readOnly = true)
public class TxnLogServiceImpl implements TxnLogService {

    @Autowired
    private TxnLogDao txnLogDao;

    @Override
    public TxnLog getTxnLog(long id) {
        return txnLogDao.getTxnLog(id);
    }

    @Override
    public TxnLog getTxnLogForUpdate(long id) {
        return txnLogDao.getTxnLogForUpdate(id);
    }

    @Override
    @Transactional(readOnly = false)
    public void save(TxnLog txnLog) {
        txnLogDao.save(txnLog);
    }

}
