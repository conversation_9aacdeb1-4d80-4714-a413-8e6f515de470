package com.abl.db.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.db.dao.ProfileParameterValueDao;
import com.abl.db.dao.ViewProfileParameterValueDao;
import com.abl.db.model.ProfileParameterValue;
import com.abl.db.model.ViewProfileParameterValue;
import com.abl.db.service.ViewProfileParameterValueService;

@Service
@Transactional(readOnly = true)
public class ViewProfileParameterValuexServiceImpl implements ViewProfileParameterValueService {
	
	@Autowired
	private ViewProfileParameterValueDao viewProfileParameterValueDao;
	
	public ViewProfileParameterValue getProfileParameterValue(long profileId, long paramId) {
		return viewProfileParameterValueDao.getProfileParameterValue(profileId, paramId);
	}
	
	
	@Override
	@Transactional(readOnly = false)
	public void saveAll(List<ViewProfileParameterValue> object) {
		viewProfileParameterValueDao.saveAll(object);

	}

}
