package com.abl.db.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.db.dao.ViewTerminalWithDownloadedAppDao;
import com.abl.db.model.ViewTerminalWithDownloadedApp;
import com.abl.db.service.ViewTerminalWithDownloadedAppService;

@Service
@Transactional(readOnly = true)
public class ViewTerminalWithDownloadedAppServiceImpl implements
		ViewTerminalWithDownloadedAppService {
	@Autowired
	private ViewTerminalWithDownloadedAppDao viewTerminalWithDownloadedAppDao;

	public List<ViewTerminalWithDownloadedApp> getTerminalWithDownloadedApp(
			long groupId) {
		return viewTerminalWithDownloadedAppDao
				.getTerminalWithDownloadedApp(groupId);
	}

}
