package com.abl.db.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.db.dao.ViewVehicleGroupingSummaryDao;
import com.abl.db.model.ViewVehicleGroupingSummary;
import com.abl.db.service.ViewVehicleGroupingSummaryService;

@Service
@Transactional(readOnly = true)
public class ViewVehicleGroupingSummaryServiceImpl implements ViewVehicleGroupingSummaryService {
	@Autowired
	private ViewVehicleGroupingSummaryDao viewVehicleGroupingSummaryDao;

	public List<ViewVehicleGroupingSummary> getVehicleGroupingSummary() {
		return viewVehicleGroupingSummaryDao.getVehicleGroupingSummary();
	}
	

	public List<ViewVehicleGroupingSummary> getGroupSummaryByGroupAndReleaseId(long groupId, long releaseId, String orderBy, boolean ascending, int offset, int maxResults){
		return viewVehicleGroupingSummaryDao.getGroupSummaryByGroupAndReleaseId(groupId, releaseId, orderBy, ascending, offset, maxResults);
	}
	
	public int countGroupSummaryByGroupAndReleaseId(long groupId, long releaseId) {
		return viewVehicleGroupingSummaryDao.countGroupSummaryByGroupAndReleaseId(groupId, releaseId);
	}
	
	public ViewVehicleGroupingSummary getVehicleGroupingSummaryById(long id){
		return viewVehicleGroupingSummaryDao.getVehicleGroupingSummaryById(id);
	}

	public ViewVehicleGroupingSummary getVehicleGroupingSummaryByGroupId(long id){
		return viewVehicleGroupingSummaryDao.getVehicleGroupingSummaryByGroupId(id);
	}

}
