package com.abl.db.service.impl;

import com.abl.db.dao.AliTxnLogDao;
import com.abl.db.model.AliTxnLog;
import com.abl.db.service.AliTxnLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;


@Service
public class AliTxnLogServiceImpl implements AliTxnLogService {

    @Autowired
    private AliTxnLogDao aliTxnLogDao;

    @Override
    @Transactional(readOnly = true)
    public AliTxnLog getAliTxnLog(long id) {
        return aliTxnLogDao.getAliTxnLog(id);
    }

    @Override
    @Transactional(readOnly = false)
    public void save(AliTxnLog txnLog) {
        aliTxnLogDao.save(txnLog);
    }

    @Override
    @Transactional(readOnly = true)
    public AliTxnLog getAliTxnLogByBookingRef(String bookingRef, String msgType ){
        return aliTxnLogDao.getAliTxnLogByBookingRef(bookingRef, msgType);
    }

    @Override
    @Transactional(readOnly = true)
    public AliTxnLog getPaymentAliTxnLogByTransId(String transId){
        return aliTxnLogDao.getPaymentAliTxnLogByTransId(transId);
    }

    @Override
    @Transactional(readOnly = true)
    public AliTxnLog getAliTxnLogByBookingRefExcludeCurrentId(String bookingRef, String msgType, Long currId) {
        return aliTxnLogDao.getAliTxnLogByBookingRefExcludeCurrentId(bookingRef,msgType, currId);
    }

    @Override
    @Transactional(readOnly = true)
    public AliTxnLog getOfflinePaymentMidTidStanRespCode(String mid, String tid, String stan, String respCode){
        return aliTxnLogDao.getOfflinePaymentMidTidStanRespCode(mid, tid, stan, respCode);
    }

    @Override
    @Transactional(readOnly = true)
    public List<AliTxnLog> getAlipayDeclinedPaymentTxns(AliTxnLog.H5Status h5Status, Date retryDt, int maxResults) {
        return aliTxnLogDao.getAlipayDeclinedPaymentTxns(h5Status, retryDt, maxResults);
    }

    @Override
    @Transactional(readOnly = true)
    public int updateDcpStatus(long id, AliTxnLog.DcpStatus dcpStatus) {
        return aliTxnLogDao.updateDcpStatus(id, dcpStatus);
    }

    @Override
    @Transactional(readOnly = true)
    public List<AliTxnLog> listPending(final String msgType, final int offset, final int limit,
                                       Date txnLogDateTimeNotBefore, Date lastSentToAlipayNotAfter) {
        return aliTxnLogDao.listPending(msgType, offset, limit,
                txnLogDateTimeNotBefore, lastSentToAlipayNotAfter);
    }

    @Override
    @Transactional(readOnly = false)
    public int updateStatus(final long id, AliTxnLog.Status status) {
        return aliTxnLogDao.updateStatus(id, status);
    }

    @Override
    @Transactional(readOnly = false)
    public int updateStatus(final long id, AliTxnLog.Status status, AliTxnLog.Status whereStatus) {
        return aliTxnLogDao.updateStatus(id, status, whereStatus);
    }
}
