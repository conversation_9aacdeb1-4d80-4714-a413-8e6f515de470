package com.abl.db.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.db.dao.AccessProfileDao;
import com.abl.db.model.AccessProfile;
import com.abl.db.service.AccessProfileService;

@Service
@Transactional(readOnly = true)
public class AccessProfileServiceImpl implements AccessProfileService {

	@Autowired
	private AccessProfileDao accessProfileDao;
	
	@Override
	public AccessProfile getAccessProfile(long id) {
		return accessProfileDao.getAccessProfile(id);
	}

	@Override
	public AccessProfile getAccessProfile(String name) {
		return accessProfileDao.getAccessProfile(name);
	}

	@Override
	public AccessProfile getAccessProfileFetchPages(String name) {
		return accessProfileDao.getAccessProfileFetchPages(name);
	}
	
	@Override
	public AccessProfile getAccessProfileFetchPages(long id) {
		return accessProfileDao.getAccessProfileFetchPages(id);
	}

	@Override
	public List<AccessProfile> getAccessProfiles() {
		return accessProfileDao.getAccessProfiles();
	}

	@Override
	public int countAccessProfiles() {
		return accessProfileDao.countAccessProfiles();
	}
	
	@Override
	public List<AccessProfile> searchAccessProfiles(String orderBy, boolean ascending, int offset, int maxResults) {
		return accessProfileDao.searchAccessProfiles(orderBy, ascending, offset, maxResults);
	}

	@Override
	@Transactional(readOnly = false)
	public void save(AccessProfile accessProfile) {
		accessProfileDao.save(accessProfile);
	}

	@Override
	@Transactional(readOnly = false)
	public void delete(AccessProfile accessProfile) {
		accessProfileDao.delete(accessProfile);
	}


}
