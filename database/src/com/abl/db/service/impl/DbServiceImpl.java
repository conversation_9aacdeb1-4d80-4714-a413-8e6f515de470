package com.abl.db.service.impl;

import com.abl.db.dao.DbDao;
import com.abl.db.service.DbService;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class DbServiceImpl implements DbService {
	
    @Autowired
    private DbDao dbDao;
    
	@Override
	@Transactional(readOnly = false)
	public void save(Object obj) {
		dbDao.save(obj);
	}

	@Override
	@Transactional(readOnly = false)
	public void save(List list) {
		dbDao.saveOrUpdate(list);
	}

	@Override
	@Transactional(readOnly = false)
	public int executeSql(String sql) {
		return dbDao.executeSql(sql);
	}
}
