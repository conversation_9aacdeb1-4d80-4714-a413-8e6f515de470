package com.abl.db.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.abl.db.dao.EzlinkTransactionDao;
import com.abl.db.model.EzlinkTransaction;
import com.abl.db.model.ViewEzlinkTransaction;
import com.abl.db.service.EzlinkTransactionService;

@Service
@Transactional(readOnly = true)
public class EzlinkTransactionServiceImpl implements EzlinkTransactionService {
	@Autowired private EzlinkTransactionDao ezlinkTransactionDao;
	
	public List<ViewEzlinkTransaction> searchDuplicateEzlinkTxn( String can, String orderBy, boolean ascending, int offset, int maxResults){
		return ezlinkTransactionDao.searchDuplicateEzlinkTxn(can, orderBy, ascending, offset, maxResults);
	}
	
	public int countDuplicateEzlinkTxn( String can){
		return ezlinkTransactionDao.countDuplicateEzlinkTxn(can);
	}
	
	
}
