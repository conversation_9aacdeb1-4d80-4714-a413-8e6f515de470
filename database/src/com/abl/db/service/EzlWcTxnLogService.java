package com.abl.db.service;

import com.abl.db.model.EzlWcTxnLog;

import java.util.List;

public interface EzlWcTxnLogService {

    public EzlWcTxnLog getEzlWcTxnLog(long id);

    /**
     * get EzlWcTxnLog by EzlWcTxnLogId
     * if there are more than 1 recs, get the earliest one
     * 
     * @param id
     * @return
     */
    public EzlWcTxnLog getEzlWcTxnLogByEzlTxnLogId(long id);
    
    /**
     * insert into EzlWcTxnLog
     * 
     * @param txnLog
     */
    public void save(EzlWcTxnLog txnLog);
    
    /**
     * update EzlWcTxnLog record
     * 
     * @param txnLog
     */
    public void update(EzlWcTxnLog txnLog);
}
