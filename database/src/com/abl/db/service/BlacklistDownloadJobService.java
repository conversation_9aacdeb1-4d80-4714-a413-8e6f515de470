package com.abl.db.service;

import com.abl.db.model.BlacklistDownloadJob;

import java.util.List;

public interface BlacklistDownloadJobService {
	
	public BlacklistDownloadJob getBlacklistDownloadJobs(long id);
	
    public List<BlacklistDownloadJob> getBlacklistDownloadJobs(short status);

    public List<BlacklistDownloadJob> getCurrentJobs();
    
    public void save(BlacklistDownloadJob blacklistDownloadJob);
    
    public List<BlacklistDownloadJob> getBlacklistDownloadJob();
    
    public void delete(BlacklistDownloadJob blacklistDownloadJob);
}
