package com.abl.db.service;

import com.abl.db.model.PlPaylahTxnLog;

public interface PlPaylahTxnLogService {

    public PlPaylahTxnLog getPlPaylahTxnLog(long id);
    
    /**
     * get PlPaylahTxnLog by plTxnLogId
     * if there are more than 1 recs, get the earliest one
     * so, if plTxnLog is a sale trans, then this method should return the webcheckout PlPaylahTxnLog record
     * 
     * @param id
     * @return
     */
    public PlPaylahTxnLog getPlPaylahTxnLogByPlTxnLogId(long id);
    
    /**
     * get PlPaylahTxnLog by plTxnLogId and msgType
     * if there are more than 1 recs, get the most recent one
     * 
     * @param id
     * @param msgType
     * @return
     */
    public PlPaylahTxnLog getPlPaylahTxnLogByPlTxnLogIdAndMsgType(long id, String msgType);
    
    /**
     * insert into PlPaylahTxnLog
     * 
     * @param txnLog
     */
    public void save(PlPaylahTxnLog txnLog);
    
    /**
     * update PlPaylahTxnLog record
     * 
     * @param txnLog
     */
    public void update(PlPaylahTxnLog txnLog);
}
