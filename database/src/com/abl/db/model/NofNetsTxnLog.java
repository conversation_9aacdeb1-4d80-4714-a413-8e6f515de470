package com.abl.db.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_NOF_NETS_TXN_LOG")
public class NofNetsTxnLog implements Serializable {

	public enum Source {
		HOST,	// for outgoing msg to NETS, created by nofHost
		BATCH,  // for outgoing msg to NETS, created by nofBatch
		NETS	// for incoming msg from NETS
	}
	
	public enum Status {
		NEW,
		SUCCESS,
		FAIL
	}
	
	public enum ReversalState { 
		PENDING,	// pending batch
		REVERSING,	// is being processed by batch	
		COMPLETED,	// reversed by batch
		ERROR		// batch encounter error reversing
	}
	
	public static final String MSG_TYPE_LOGON="logon";
	public static final String MSG_TYPE_LOGOff="logoff";
	public static final String MSG_TYPE_CUTOVER="cutover";
	public static final String MSG_TYPE_CHANGE_KEY="change_key";
	public static final String MSG_TYPE_NEW_KEY="new_key";
	public static final String MSG_TYPE_GMT="gmt";	// get merchant token
	public static final String MSG_TYPE_QUERY_TOKEN="query_token";
	public static final String MSG_TYPE_CFA="cfa";
	public static final String MSG_TYPE_CFA_CANCEL="cfa_cancel";
	public static final String MSG_TYPE_PURCHASE="purchase";
	public static final String MSG_TYPE_PURCHASE_REVERSAL="purchase_reversal";
	public static final String MSG_TYPE_UNKNOWN="unknown";
	
	private Long id;
	private Source source=Source.HOST;
	private Date dateTime;
	private String mti; // MTI of ISO msg
	private String procCode; // proc code of ISO msg
	private Long txnAmt;
	private String transDateTime;	// P-7 transmission date/time (MMddHHmmss)
	private String stan;			// P-11
	private String txnTime;			// P-12 txn time in request msg (hhmmss)
	private String txnDate;			// P-13 txn date in request msg (mmdd)
	private String captureDate;		// P-17 capture date in request msg (mmdd)
	private String tid;				// P-41
	private String mid;				// P-42
	private String merchName;		// P-43
	private Status status = Status.NEW;
	private String txResult;
	private String responseCode;
	private String rrn;
	private String authCode;
	private String origAuthCode;
	private boolean voided = false;
	private boolean reversed=false;
	private ReversalState reversalState;	// (purchase only) this is state for batch to do reversing
	private int reversalTryCount = 0;	// count numbers of reversal attempts for this trans
	private String msgType;
	private String posEntryMode;
	private String posCondCode;
	private NofTxnLog nofTxnLog;	// link to nofTxnLog; only for nets trans that is triggered by nof trans recv from pinpad or dcp
	private String muid;
	private String tokenIndex;
	private String f126;	// f126 data in request msg
	private Character responderCode; // responder code from header message

	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_NETS_TXN_LOG")
	@SequenceGenerator(name = "SEQGEN_NETS_TXN_LOG", sequenceName = "TMSQ_NETS_TXN_LOG")
	@Column(name = "id")
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Enumerated(EnumType.STRING)
	@Column(name = "source", length=32)
	public Source getSource() {
		return source;
	}

	public void setSource(Source source) {
		this.source = source;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "date_time")
	public Date getDateTime() {
		return dateTime;
	}

	public void setDateTime(Date dateTime) {
		this.dateTime = dateTime;
	}

	@Column(name = "mti", length = 4)
	public String getMti() {
		return mti;
	}

	public void setMti(String mti) {
		this.mti = mti;
	}

	@Column(name = "proc_code", length = 6)
	public String getProcCode() {
		return procCode;
	}

	public void setProcCode(String procCode) {
		this.procCode = procCode;
	}

	@Column(name = "tid", length = 8)
	public String getTid() {
		return tid;
	}

	public void setTid(String tid) {
		this.tid = tid;
	}

	@Column(name = "txn_amount")
	public Long getTxnAmt() {
		return txnAmt;
	}

	public void setTxnAmt(Long txnAmt) {
		this.txnAmt = txnAmt;
	}

	@Column(name = "trans_date_time", length = 12)
	public String getTransDateTime() {
		return transDateTime;
	}

	public void setTransDateTime(String transDateTime) {
		this.transDateTime = transDateTime;
	}

	@Column(name = "stan", length = 6)
	public String getStan() {
		return stan;
	}

	public void setStan(String stan) {
		this.stan = stan;
	}

	@Column(name = "txn_time", length=6)
	public String getTxnTime() {
		return txnTime;
	}

	public void setTxnTime(String txnTime) {
		this.txnTime = txnTime;
	}
	
	@Column(name = "txn_date", length=4)
	public String getTxnDate() {
		return txnDate;
	}

	public void setTxnDate(String txnDate) {
		this.txnDate = txnDate;
	}

	@Column(name = "capture_date", length=4)
	public String getCaptureDate() {
		return captureDate;
	}

	public void setCaptureDate(String captureDate) {
		this.captureDate = captureDate;
	}

	@Column(name = "mid", length = 15)
	public String getMid() {
		return mid;
	}

	public void setMid(String mid) {
		this.mid = mid;
	}

	@Column(name = "merchant_name")
	public String getMerchName() {
		return merchName;
	}

	public void setMerchName(String merchName) {
		this.merchName = merchName;
	}
	
	@Enumerated(EnumType.STRING)
	@Column(name = "status", length=12, nullable=false)
	public Status getStatus() {
		return status;
	}

	public void setStatus(Status status) {
		this.status = status;
	}
	
	@Column(name = "tx_result", length=32)
	public String getTxResult() {
		return txResult;
	}

	public void setTxResult(String txResult) {
		this.txResult = txResult;
	}

	@Column(name = "resp_code", length = 2)
	public String getResponseCode() {
		return responseCode;
	}

	public void setResponseCode(String responseCode) {
		this.responseCode = responseCode;
	}
	
	@Column(name = "rrn", length=32)
	public String getRrn() {
		return rrn;
	}

	public void setRrn(String rrn) {
		this.rrn = rrn;
	}

	@Column(name = "auth_code", length=16)
	public String getAuthCode() {
		return authCode;
	}

	public void setAuthCode(String authCode) {
		this.authCode = authCode;
	}
	
	@Column(name = "orig_auth_code", length=16)
	public String getOrigAuthCode() {
		return origAuthCode;
	}

	public void setOrigAuthCode(String origAuthCode) {
		this.origAuthCode = origAuthCode;
	}

	@Column(name = "msg_type", length=20)
	public String getMsgType() {
		return msgType;
	}

	public void setMsgType(String msgType) {
		this.msgType = msgType;
	}

	@Column(name = "voided", nullable=false)
	public boolean isVoided() {
		return voided;
	}

	public void setVoided(boolean voided) {
		this.voided = voided;
	}

	@Column(name = "reversed", nullable=false)
	public boolean isReversed() {
		return reversed;
	}

	public void setReversed(boolean reversed) {
		this.reversed = reversed;
	}
	
	@Enumerated(EnumType.ORDINAL)
	@Column(name = "reversal_state")
	public ReversalState getReversalState() {
		return reversalState;
	}

	public void setReversalState(ReversalState reversalState) {
		this.reversalState = reversalState;
	}
	
	@Column(name = "reversal_try_count")
	public int getReversalTryCount() {
		return reversalTryCount;
	}

	public void setReversalTryCount(int reversalTryCount) {
		this.reversalTryCount = reversalTryCount;
	}

	@Column(name = "pos_entry_mode", length=8)
	public String getPosEntryMode() {
		return posEntryMode;
	}

	public void setPosEntryMode(String posEntryMode) {
		this.posEntryMode = posEntryMode;
	}

	@Column(name = "pos_cond_code", length=8)
	public String getPosCondCode() {
		return posCondCode;
	}

	public void setPosCondCode(String posCondCode) {
		this.posCondCode = posCondCode;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "nof_txn_log_id")
	public NofTxnLog getNofTxnLog() {
		return nofTxnLog;
	}

	public void setNofTxnLog(NofTxnLog nofTxnLog) {
		this.nofTxnLog = nofTxnLog;
	}

	@Column(name = "muid", length=64)
	public String getMuid() {
		return muid;
	}

	public void setMuid(String muid) {
		this.muid = muid;
	}

	@Column(name = "token_index", length=2)
	public String getTokenIndex() {
		return tokenIndex;
	}

	public void setTokenIndex(String tokenIndex) {
		this.tokenIndex = tokenIndex;
	}

	@Column(name = "f126", length=1000)
	public String getF126() {
		return f126;
	}

	public void setF126(String f126) {
		this.f126 = f126;
	}

    @Column(name = "responder_code", length=1)
    public Character getResponderCode() {
        return responderCode;
    }

    public void setResponderCode(Character responderCode) {
        this.responderCode = responderCode;
    }
}
