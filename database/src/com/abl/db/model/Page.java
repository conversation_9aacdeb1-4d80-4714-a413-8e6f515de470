package com.abl.db.model;

import javax.persistence.*;

import org.hibernate.annotations.ForeignKey;

import java.io.Serializable;

/**
 * represents a "page" for webadmin
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_PAGE")
public class Page implements Serializable {

    private String name;                // name, also id
    private String label;                // label to display on menu
    private String url;                    // url for authorization verification
    private Boolean commonFlag = false;    // true means access is free for all authenticated users
    private Boolean adminFlag = false;    // true means administratir has free acces
    private Boolean domainFlag = false;    // true means only domain users has access
    private Module module;                // module the page belongs to
    private String groupName;
    private String displayOrder;        // display order, on menu and also for adding/editing access profile
    private Boolean visible = false;        // false = will not display on menu

    @Id
    @Column(name = "name", length = 100)
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Column(name = "label", length = 50, unique = true, nullable = false)
    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    @Column(name = "url", unique = true, nullable = false)
    public String getUrl() {
        return url;
    }

    public void setUrl(String id) {
        this.url = id;
    }

    @Column(name = "common_flag", nullable = false)
    public Boolean getCommonFlag() {
        return commonFlag;
    }

    public void setCommonFlag(Boolean commonFlag) {
        this.commonFlag = commonFlag;
    }

    @Column(name = "admin_flag", nullable = false)
    public Boolean getAdminFlag() {
        return adminFlag;
    }

    public void setAdminFlag(Boolean adminFlag) {
        this.adminFlag = adminFlag;
    }

    @Column(name = "domain_flag", nullable = false)
    public Boolean getDomainFlag() {
        return domainFlag;
    }

    public void setDomainFlag(Boolean domainFlag) {
        this.domainFlag = domainFlag;
    }

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "module_name")
    @ForeignKey(name="TMFC_MODULE_NAME_PAGE") 
    public Module getModule() {
        return module;
    }

    public void setModule(Module module) {
        this.module = module;
    }

    @Column(name = "group_name", length = 30)
    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    @Column(name = "display_order", unique = true, length = 10)
    public String getDisplayOrder() {
        return displayOrder;
    }

    public void setDisplayOrder(String displayOrder) {
        this.displayOrder = displayOrder;
    }

    @Column(name = "visible", nullable = false)
    public Boolean isVisible() {
        return visible;
    }

    public void setVisible(Boolean visible) {
        this.visible = visible;
    }

    public String toString() {
        return "Page(name=" + name + ")";
    }

}
