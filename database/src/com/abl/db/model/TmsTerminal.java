package com.abl.db.model;

import javax.persistence.*;

import org.hibernate.annotations.ForeignKey;
import org.hibernate.annotations.Type;

import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_TMS_TERMINAL")
public class TmsTerminal implements Serializable {

    private long id;
    private String serialNo;
    private String vehicleNo;

    private Model model;
    private Date createDateTime;
    private Boolean deleted = false;            // true means deleted.  Most dao methods should not return or modify
    private boolean uploadLog = false;
    private boolean disabledLogging = false;
    private Date updateDateTime;
    private Set<TerminalApplication> terminalApplication = new HashSet<TerminalApplication>(
            0);
    private Set<TerminalApplicationDownloadJob> terminalApplicationDownloadJob = new HashSet<TerminalApplicationDownloadJob>(
            0);

    private String osVersion;
    private String ctlsVersion;
    private String ptid;
    private boolean vehicleUnpaired;
    private String currentVersion;

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_TMS_TERMINAL")
    @SequenceGenerator(name = "SEQGEN_TMS_TERMINAL", sequenceName = "TMSQ_TMS_TERMINAL")
    @Column(name = "id")
    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    @Column(name = "serial_no", nullable = false, length = 20, unique = true)
    public String getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "model_id")
    @ForeignKey(name="TMFC_MODEL_ID_TERM") 
    public Model getModel() {
        return model;
    }

    public void setModel(Model model) {
        this.model = model;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "create_date_time", nullable = false)
    public Date getCreateDateTime() {
        return createDateTime;
    }

    public void setCreateDateTime(Date createDateTime) {
        this.createDateTime = createDateTime;
    }

    @Type(type="boolean")
    @Column(name = "deleted", columnDefinition = "int default 0", nullable = false)
    public Boolean isDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "pk.tmsTerminal", cascade = CascadeType.ALL)
    @ForeignKey(name="TMFC_TERM_ID__TERM_APP")
    public Set<TerminalApplication> getTerminalApplication() {
        return terminalApplication;
    }

    public void setTerminalApplication(
            Set<TerminalApplication> terminalApplication) {
        this.terminalApplication = terminalApplication;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "pk.tmsTerminal", cascade = CascadeType.ALL)
    @ForeignKey(name="TMFC_TERM_ID_TERM_APP_DL_JOB") 
    public Set<TerminalApplicationDownloadJob> getTerminalApplicationDownloadJob() {
        return terminalApplicationDownloadJob;
    }

    public void setTerminalApplicationDownloadJob(
            Set<TerminalApplicationDownloadJob> terminalApplicationDownloadJob) {
        this.terminalApplicationDownloadJob = terminalApplicationDownloadJob;
    }

    @Type(type="boolean")
    @Column(name = "upload_log", columnDefinition = "int default 0", nullable = false)
    public boolean isUploadLog() {
        return uploadLog;
    }

    public void setUploadLog(boolean uploadLog) {
        this.uploadLog = uploadLog;
    }

    @Type(type="boolean")
    @Column(name = "disabled_logging", columnDefinition = "int default 0", nullable = false)
    public boolean isDisabledLogging() {
        return disabledLogging;
    }

    public void setDisabledLogging(boolean disabledLogging) {
        this.disabledLogging = disabledLogging;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "update_date_time")
    public Date getUpdateDateTime() {
        return updateDateTime;
    }

    public void setUpdateDateTime(Date updateDateTime) {
        this.updateDateTime = updateDateTime;
    }

    @Column(name = "vehicle_no", nullable = false, length = 20)
    public String getVehicleNo() {
        return vehicleNo;
    }

    public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
    }

    @Column(name = "ctls_version", nullable = false, length = 20)
    public String getCtlsVersion() {
        return ctlsVersion;
    }

    public void setCtlsVersion(String ctlsVersion) {
        this.ctlsVersion = ctlsVersion;
    }

    @Column(name = "os_version", nullable = false, length = 20)
    public String getOsVersion() {
        return osVersion;
    }

    public void setOsVersion(String osVersion) {
        this.osVersion = osVersion;
    }

    @Column(name = "ptid", nullable = false, length = 20)
    public String getPtid() {
        return ptid;
    }

    public void setPtid(String ptid) {
        this.ptid = ptid;
    }
    
    
    @Type(type="boolean")
    @Column(name = "vehicle_unpaired", columnDefinition = "int default 0", nullable = false)
    public boolean isVehicleUnpaired() {
        return vehicleUnpaired;
    }

    public void setVehicleUnpaired(boolean vehicleUnpaired) {
        this.vehicleUnpaired = vehicleUnpaired;
    }

    @Column(name = "current_version")
    public String getCurrentVersion() {
        return currentVersion;
    }

    public void setCurrentVersion(String currentVersion) {
        this.currentVersion = currentVersion;
    }

}
