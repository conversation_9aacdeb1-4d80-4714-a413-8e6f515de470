package com.abl.db.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

/**
 * This is a view, getting terminal summary
 * 
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "TMVW_TERMINAL_SUMMARY")
public class ViewTerminalSummary implements Serializable {

	private long id;
	private String releaseDesc;
	private int releaseVersion;
	private String vehicleNo;
	private String serialNo;
	private Date updateDateTime;
	private boolean uploadLog;
	private String downloadStatus;
	private int recordNo;
	private int totalRecordNo;
	private long modelId;
	private String modelName;
	private String padVersion;
	private boolean vehicleUnpaired;
	private String currentVersion;

	@Id
	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "update_date_time")
	public Date getUpdateDateTime() {
		return updateDateTime;
	}

	public void setUpdateDateTime(Date updateDateTime) {
		this.updateDateTime = updateDateTime;
	}

	@Column(name="vehicle_no")
	public String getVehicleNo() {
		return vehicleNo;
	}

	public void setVehicleNo(String vehicleNo) {
		this.vehicleNo = vehicleNo;
	}

	@Column(name="serial_no")
	public String getSerialNo() {
		return serialNo;
	}

	public void setSerialNo(String serialNo) {
		this.serialNo = serialNo;
	}

	@Column(name="release_desc")
	public String getReleaseDesc() {
		return releaseDesc;
	}

	public void setReleaseDesc(String releaseDesc) {
		this.releaseDesc = releaseDesc;
	}

	@Column(name="release_version")
	public int getReleaseVersion() {
		return releaseVersion;
	}

	public void setReleaseVersion(int releaseVersion) {
		this.releaseVersion = releaseVersion;
	}

	@Column(name="upload_log")
	public boolean isUploadLog() {
		return uploadLog;
	}

	public void setUploadLog(boolean uploadLog) {
		this.uploadLog = uploadLog;
	}

	@Column(name="download_status")
	public String getDownloadStatus() {
		return downloadStatus;
	}

	public void setDownloadStatus(String downloadStatus) {
		this.downloadStatus = downloadStatus;
	}

	@Column(name="record_no")
	public int getRecordNo() {
		return recordNo;
	}

	public void setRecordNo(int recordNo) {
		this.recordNo = recordNo;
	}

	@Column(name="total_record_no")
	public int getTotalRecordNo() {
		return totalRecordNo;
	}

	public void setTotalRecordNo(int totalRecordNo) {
		this.totalRecordNo = totalRecordNo;
	}

	@Column(name="model_id")
	public long getModelId() {
		return modelId;
	}

	public void setModelId(long modelId) {
		this.modelId = modelId;
	}

	@Column(name="model_name")
	public String getModelName() {
		return modelName;
	}

	public void setModelName(String modelName) {
		this.modelName = modelName;
	}
	
	@Transient
	public String getPadVersion() {

		return String.format("%06d", getReleaseVersion());
	}

	@Column(name="vehicle_unpaired")
	public boolean isVehicleUnpaired() {
		return vehicleUnpaired;
	}

	public void setVehicleUnpaired(boolean vehicleUnpaired) {
		this.vehicleUnpaired = vehicleUnpaired;
	}

	@Column(name="current_version")
	public String getCurrentVersion() {
		return currentVersion;
	}

	public void setCurrentVersion(String currentVersion) {
		this.currentVersion = currentVersion;
	}

}
