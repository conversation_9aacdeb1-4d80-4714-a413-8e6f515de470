package com.abl.db.model;

import org.hibernate.annotations.ForeignKey;
import org.hibernate.annotations.Index;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "TMTB_STUCK_TRANSACTION")
public class StuckTransaction {

    public enum TxnType {
        NFP("NETS Flashpay"),
        EZL("EZLink"),
        CCO("CabCharge Offline Approved"),
        CRO("Credit Offline Approved"),
        CRV("Credit Reversal"),
        CTC("Credit TC"),
        CCR("CabCharge Reversal"),
        APR("Alipay Reversal"),
        DAR("Dash Reversal"),
        QRR("QR Reversal"),
        UPR("UnionPay Reversal"),
        NER("NETS Reversal"),
        EZR("EZLink Reversal");

        private String description;

        private TxnType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    public enum State {
        STUCK("Stuck"),
        PENDING("Pending"),
        CLEARED("Cleared"),
        AUTO_CLEARED("Auto Cleared"),
        NOT_FOUND("Not found");

        private String description;

        private State(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    private long id;
    private TmsTerminal tmsTerminal;
    private Date uploadDateTime;
    private Date txnDateTime;   // stuck time stamp
    private String triggerBy;   // admin user name
    private String jobNumber;
    private String driverId;
    private TxnType txnType;
    private State state;
    private String cardNo;  // masked
    private Long txnAmt;
    private String rrn; // approval code/rrn
    private String mid;
    private String tid;
    private String releaseVer;
    private Long fare;
    private Long gst;
    private Long adminFee;

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_STUCK_TRANSACTION")
    @SequenceGenerator(name = "SEQGEN_STUCK_TRANSACTION", sequenceName = "TMSQ_STUCK_TRANSACTION")
    @Column(name = "id")
    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "terminal_id")
    @ForeignKey(name="TMFC_STUCK_TXN_TERMINAL")
    @Index(name="TMIX_STUCK_TXN__TERMINAL_ID")
    public TmsTerminal getTmsTerminal() {
        return tmsTerminal;
    }

    public void setTmsTerminal(TmsTerminal tmsTerminal) {
        this.tmsTerminal = tmsTerminal;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "upload_date_time")
    public Date getUploadDateTime() {
        return uploadDateTime;
    }

    public void setUploadDateTime(Date uploadDateTime) {
        this.uploadDateTime = uploadDateTime;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "txn_date_time")
    public Date getTxnDateTime() {
        return txnDateTime;
    }

    public void setTxnDateTime(Date txnDateTime) {
        this.txnDateTime = txnDateTime;
    }

    @Column(name = "trigger_by", length = 30)
    public String getTriggerBy() {
        return triggerBy;
    }

    public void setTriggerBy(String triggerBy) {
        this.triggerBy = triggerBy;
    }

    @Column(name = "job_no", length = 12)
    public String getJobNumber() {
        return jobNumber;
    }

    public void setJobNumber(String jobNumber) {
        this.jobNumber = jobNumber;
    }

    @Column(name = "driver_id", length = 9)
    public String getDriverId() {
        return driverId;
    }

    public void setDriverId(String driverId) {
        this.driverId = driverId;
    }

    @Enumerated(EnumType.STRING)
    @Column(name = "txn_type", length=16, nullable=false)
    public TxnType getTxnType() {
        return txnType;
    }

    public void setTxnType(TxnType txnType) {
        this.txnType = txnType;
    }

    @Enumerated(EnumType.STRING)
    @Column(name = "state", length=16, nullable=false)
    public State getState() {
        return state;
    }

    public void setState(State state) {
        this.state = state;
    }

    @Column(name = "card_no", length = 20)
    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    @Column(name = "txn_amt")
    public Long getTxnAmt() {
        return txnAmt;
    }

    public void setTxnAmt(Long txnAmt) {
        this.txnAmt = txnAmt;
    }

    @Column(name = "rrn", length = 20)
    public String getRrn() {
        return rrn;
    }

    public void setRrn(String rrn) {
        this.rrn = rrn;
    }

    @Column(name = "mid", length = 15)
    public String getMid() {
        return mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }

    @Column(name = "tid", length = 8)
    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    @Column(name = "release_ver", length = 6)
    public String getReleaseVer() {
        return releaseVer;
    }

    public void setReleaseVer(String releaseVer) {
        this.releaseVer = releaseVer;
    }

    @Column(name = "fare")
    public Long getFare() {
        return fare;
    }

    public void setFare(Long fare) {
        this.fare = fare;
    }

    @Column(name = "gst")
    public Long getGst() {
        return gst;
    }

    public void setGst(Long gst) {
        this.gst = gst;
    }

    @Column(name = "admin_fee")
    public Long getAdminFee() {
        return adminFee;
    }

    public void setAdminFee(Long adminFee) {
        this.adminFee = adminFee;
    }
}
