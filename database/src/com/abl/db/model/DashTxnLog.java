package com.abl.db.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_DASH_TXN_LOG")
public class DashTxnLog implements Serializable {
	 private Long id;
	 private String mti;
	 private String procCode;
	 private Long amount=0l;
	 private String stan;
	 private Date logDateTime; 
	 private Date responseDateTime; 
	 private String responseCode; 
	 private String tid; 
	 private String taxiNumber;
	 private String driverId;
	 private String jobNumber;
	 private String fareAmount;
	 private String fareGst;
	 private String fareAdmin;
	 private String companyCode;
	 private String pinpadSerialNo;
	 private String approvalCode;
	 private String rrn;
	 
	 
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_DASH_TXN_LOG")
	@SequenceGenerator(name = "SEQGEN_DASH_TXN_LOG", sequenceName = "TMSQ_DASH_TXN_LOG")
	@Column(name = "id")
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	
	@Column(name = "mti", length = 4)
	public String getMti() {
		return mti;
	}
	public void setMti(String mti) {
		this.mti = mti;
	}
	
	@Column(name = "proc_code", length = 6)
	public String getProcCode() {
		return procCode;
	}
	public void setProcCode(String procCode) {
		this.procCode = procCode;
	}
	
	@Column(name = "amount")
	public Long getAmount() {
		return amount;
	}
	public void setAmount(Long amount) {
		this.amount = amount;
	}
		
	@Column(name = "stan", length=6)
	public String getStan() {
		return stan;
	}
	public void setStan(String stan) {
		this.stan = stan;
	}
	@Temporal(TemporalType.TIMESTAMP)
    @Column(name = "log_date_time")
	public Date getLogDateTime() {
		return logDateTime;
	}
	public void setLogDateTime(Date logDateTime) {
		this.logDateTime = logDateTime;
	}
	@Temporal(TemporalType.TIMESTAMP)
    @Column(name = "response_date_time")
	public Date getResponseDateTime() {
		return responseDateTime;
	}
	public void setResponseDateTime(Date responseDateTime) {
		this.responseDateTime = responseDateTime;
	}
	
	
	@Column(name = "resp_code", length=2)
	public String getResponseCode() {
		return responseCode;
	}
	public void setResponseCode(String responseCode) {
		this.responseCode = responseCode;
	}
	
	@Column(name = "tid", length = 8)
	public String getTid() {
		return tid;
	}
	public void setTid(String tid) {
		this.tid = tid;
	}
	@Column(name = "taxi_number", length=12)
	public String getTaxiNumber() {
		return taxiNumber;
	}
	public void setTaxiNumber(String taxiNumber) {
		this.taxiNumber = taxiNumber;
	}
	@Column(name = "driver_id", length=9)
	public String getDriverId() {
		return driverId;
	}
	public void setDriverId(String driverId) {
		this.driverId = driverId;
	}
	@Column(name = "job_number", length=10)
	public String getJobNumber() {
		return jobNumber;
	}
	public void setJobNumber(String jobNumber) {
		this.jobNumber = jobNumber;
	}
	@Column(name = "fare_amount", length=6)
	public String getFareAmount() {
		return fareAmount;
	}
	public void setFareAmount(String fareAmount) {
		this.fareAmount = fareAmount;
	}
	@Column(name = "fare_gst", length=6)
	public String getFareGst() {
		return fareGst;
	}
	public void setFareGst(String fareGst) {
		this.fareGst = fareGst;
	}
	@Column(name = "fare_admin", length=6)
	public String getFareAdmin() {
		return fareAdmin;
	}
	public void setFareAdmin(String fareAdmin) {
		this.fareAdmin = fareAdmin;
	}
	@Column(name = "company_code", length=4)
	public String getCompanyCode() {
		return companyCode;
	}
	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}
	@Column(name = "pinpad_sn", length=20)
	public String getPinpadSerialNo() {
		return pinpadSerialNo;
	}
	public void setPinpadSerialNo(String pinpadSerialNo) {
		this.pinpadSerialNo = pinpadSerialNo;
	}
	
	@Column(name = "approval_code", length=6)
	public String getApprovalCode() {
		return approvalCode;
	}
	public void setApprovalCode(String approvalCode) {
		this.approvalCode = approvalCode;
	}
	
	@Column(name = "rrn", length=12)
	public String getRrn() {
		return rrn;
	}
	public void setRrn(String rrn) {
		this.rrn = rrn;
	}
	 
	    

}
