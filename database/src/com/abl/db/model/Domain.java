package com.abl.db.model;

import javax.persistence.*;

import org.hibernate.annotations.Type;

import java.io.Serializable;
import java.util.Date;

/**
 * Represents grouping of user.  Depending on scenario, it may be used to grouped users into
 * banks, etc.
 * <p/>
 * The "DOMAIN_TYPE" indicates what kind of group it is.  Default is "Bank".
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_DOMAIN")
public class Domain implements Serializable {

    private Long id;
    private String shortName;
    private String name;
    private Date createDateTime;
    private Date modifyDateTime;
    private Boolean deleted = false;
    private Date deleteDateTime;

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_DOMAIN")
    @SequenceGenerator(name = "SEQGEN_DOMAIN", sequenceName = "TMSQ_DOMAIN")
    @Column(name = "id")
    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "short_name", nullable = false, unique = true, length = 10)
    public String getShortName() {
        return shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    @Column(name = "name", nullable = false, unique = true, length = 80)
    public String getName() {
        return name;
    }

    public void setName(String adminName) {
        this.name = adminName;
    }

    @Type(type="boolean")
    @Column(name = "deleted", columnDefinition = "int default 0", nullable = false)
    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "create_date_time")
    public Date getCreateDateTime() {
        return createDateTime;
    }

    public void setCreateDateTime(Date createDateTime) {
        this.createDateTime = createDateTime;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "modify_date_time")
    public Date getModifyDateTime() {
        return modifyDateTime;
    }

    public void setModifyDateTime(Date modifyDateTime) {
        this.modifyDateTime = modifyDateTime;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "delete_date_time")
    public Date getDeleteDateTime() {
        return deleteDateTime;
    }

    public void setDeleteDateTime(Date deleteDateTime) {
        this.deleteDateTime = deleteDateTime;
    }

    public String toString() {
        return "Domain(id=" + id + ", shortName=" + shortName + ", name=" + name + ")";
    }
}
