package com.abl.db.model;

import org.hibernate.annotations.Cascade;
import org.hibernate.annotations.ForeignKey;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.Set;

@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_RELEASE")
public class Release implements Serializable {

    private long id;
    private int version;
    private int minVersion;
    private String description;
    private int fileSize;
    private Date loadDateTime;
    private String createdBy;
    private Boolean deleted = false;            // true means deleted.  Most dao methods should not return or modify
    private String padVersion;
    private String padMinVersion;

    private Set<Model> model;
    private Set<Application> application;

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_RELEASE")
    @SequenceGenerator(name = "SEQGEN_RELEASE", sequenceName = "TMSQ_RELEASE")
    @Column(name = "id")
    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    @Column(name = "version", nullable = false, length = 50)
    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    @Column(name = "min_version", length = 50)
    public int getMinVersion() {
		return minVersion;
	}

	public void setMinVersion(int minVersion) {
		this.minVersion = minVersion;
	}
	
    @Column(name = "description", length = 200)
    public String getDescription() {
        return description;
    }

    

	public void setDescription(String description) {
        this.description = description;
    }

    @Column(name = "file_size", nullable = false)
    public int getFileSize() {
        return fileSize;
    }

    public void setFileSize(int fileSize) {
        this.fileSize = fileSize;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "load_date_time", nullable = false)
    public Date getLoadDateTime() {
        return loadDateTime;
    }

    public void setLoadDateTime(Date loadDateTime) {
        this.loadDateTime = loadDateTime;
    }

    @Column(name = "created_by", nullable = false, length = 30)
    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    @Type(type="boolean")
    @Column(name = "deleted", columnDefinition = "int default 0", nullable = false)
    public Boolean isDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    @ManyToMany(targetEntity = Model.class, cascade = {CascadeType.ALL}, fetch = FetchType.LAZY)
    @Cascade(value = {org.hibernate.annotations.CascadeType.DELETE})
    @ForeignKey(name = "TMFC_REL_ID_MODEL", inverseName = "TMFC_MODEL_ID_REL")
    public Set<Model> getModel() {
        return model;
    }

    public void setModel(Set<Model> model) {
        this.model = model;
    }

    @ManyToMany(targetEntity = Application.class, cascade = {CascadeType.ALL}, fetch = FetchType.LAZY)
    @Cascade(value = {org.hibernate.annotations.CascadeType.DELETE})
    @ForeignKey(name = "TMFC_REL_ID_APP", inverseName = "TMFC_APP_ID_REL")
    public Set<Application> getApplication() {
        return application;
    }

    public void setApplication(Set<Application> application) {
        this.application = application;
    }

    @Transient
    public String getPadVersion() {
        return String.format("%06d", getVersion());
    }

    public void setPadVersion(String padVersion) {
        this.padVersion = padVersion;
    }

	@Transient
	public String getPadMinVersion() {
		return String.format("%06d", getMinVersion());
	}

	

}
