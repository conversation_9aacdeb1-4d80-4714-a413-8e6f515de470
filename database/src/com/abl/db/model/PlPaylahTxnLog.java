package com.abl.db.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_PL_PAYLAH_TXN_LOG")
public class PlPaylahTxnLog implements Serializable {
	
	/**
	 * new: processing did not complete 
	 * success: processing completes successfully
	 * pending: for webcheckout, to indicate processing is completed but the sale status is not known
	 * fail: processing complete with error
	 */
	public enum Status {
		NEW,
		SUCCESS,
		PENDING,
		FAIL
	}
	
	public static final String MSG_TYPE_EC_SETUP="ec_setup";
	public static final String MSG_TYPE_PURCHASE_ENQUIRY="purchase_enquiry";
	public static final String MSG_TYPE_WEB_CHECKOUT="web_checkout";
	public static final String MSG_TYPE_REFUND="web_refund";
	
	private Long id;
	private Date dateTime;
	private String msgId;
	private String origMsgId;
	private String txnAmt;	// amount in cents
	private Status status = Status.NEW;
	private String txnType;	// some msg, e.g. purchase enquiry, has txnType field
	private String txResult;
	private Integer httpStatusCode;
	private String txnStatus;
	private String txnStatusCode;
	private String txnStatusDescription;
	private String txnRefId;
	private String ecSetupId;
	private boolean voided = false;
	private boolean refunded = false;
	private int reversalTryCount = 0;	// count numbers of reversal attempts for this trans
	private String msgType;
	private String approvalCode;
	private String origBankTxnRefId;
	private PlTxnLog plTxnLog;	// link to plTxnLog
	
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_PL_P_TXN_LOG")
	@SequenceGenerator(name = "SEQGEN_PL_P_TXN_LOG", sequenceName = "TMSQ_PL_P_TXN_LOG")
	@Column(name = "id")
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "date_time")
	public Date getDateTime() {
		return dateTime;
	}

	public void setDateTime(Date dateTime) {
		this.dateTime = dateTime;
	}

	@Column(name = "msg_id")
	public String getMsgId() {
		return msgId;
	}

	public void setMsgId(String msgId) {
		this.msgId = msgId;
	}

	@Column(name = "orig_msg_id")
	public String getOrigMsgId() {
		return origMsgId;
	}

	public void setOrigMsgId(String origMsgId) {
		this.origMsgId = origMsgId;
	}

	@Column(name = "txn_amount")
	public String getTxnAmt() {
		return txnAmt;
	}

	public void setTxnAmt(String txnAmt) {
		this.txnAmt = txnAmt;
	}
	
	@Enumerated(EnumType.STRING)
	@Column(name = "status", length=12, nullable=false)
	public Status getStatus() {
		return status;
	}

	public void setStatus(Status status) {
		this.status = status;
	}
	
	@Column(name = "txn_type", length=32)
	public String getTxnType() {
		return txnType;
	}

	public void setTxnType(String txnType) {
		this.txnType = txnType;
	}
	
	@Column(name = "tx_result", length=32)
	public String getTxResult() {
		return txResult;
	}

	public void setTxResult(String txResult) {
		this.txResult = txResult;
	}

	@Column(name = "http_status_code")
	public Integer getHttpStatusCode() {
		return httpStatusCode;
	}

	public void setHttpStatusCode(Integer httpStatusCode) {
		this.httpStatusCode = httpStatusCode;
	}

	@Column(name = "txn_status", length = 10)
	public String getTxnStatus() {
		return txnStatus;
	}

	public void setTxnStatus(String txnStatus) {
		this.txnStatus = txnStatus;
	}

	@Column(name = "txn_status_code", length = 10)
	public String getTxnStatusCode() {
		return txnStatusCode;
	}

	public void setTxnStatusCode(String txnStatusCode) {
		this.txnStatusCode = txnStatusCode;
	}

	@Column(name = "txn_status_desc")
	public String getTxnStatusDescription() {
		return txnStatusDescription;
	}

	public void setTxnStatusDescription(String txnStatusDescription) {
		this.txnStatusDescription = txnStatusDescription;
	}

	@Column(name = "txn_ref_id")
	public String getTxnRefId() {
		return txnRefId;
	}

	public void setTxnRefId(String txnRefId) {
		this.txnRefId = txnRefId;
	}

	@Column(name = "ec_setup_id")
	public String getEcSetupId() {
		return ecSetupId;
	}

	public void setEcSetupId(String ecSetupId) {
		this.ecSetupId = ecSetupId;
	}

	@Column(name = "refunded")
	public boolean isRefunded() {
		return refunded;
	}

	public void setRefunded(boolean refunded) {
		this.refunded = refunded;
	}
	
	@Column(name = "reversal_try_count")
	public int getReversalTryCount() {
		return reversalTryCount;
	}

	public void setReversalTryCount(int reversalTryCount) {
		this.reversalTryCount = reversalTryCount;
	}

	@Column(name = "msg_type", length=20)
	public String getMsgType() {
		return msgType;
	}

	public void setMsgType(String msgType) {
		this.msgType = msgType;
	}

	@Column(name = "voided", nullable=false)
	public boolean isVoided() {
		return voided;
	}

	public void setVoided(boolean voided) {
		this.voided = voided;
	}
	
	@Column(name = "approval_code")
	public String getApprovalCode() {
		return approvalCode;
	}

	public void setApprovalCode(String approvalCode) {
		this.approvalCode = approvalCode;
	}

	@Column(name = "orig_bank_txn_ref_id")
	public String getOrigBankTxnRefId() {
		return origBankTxnRefId;
	}

	public void setOrigBankTxnRefId(String origBankTxnRefId) {
		this.origBankTxnRefId = origBankTxnRefId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "pl_txn_log_id")
	public PlTxnLog getPlTxnLog() {
		return plTxnLog;
	}

	public void setPlTxnLog(PlTxnLog plTxnLog) {
		this.plTxnLog = plTxnLog;
	}
	
}
