package com.abl.db.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_NOF_ACCOUNT", uniqueConstraints = {@UniqueConstraint(columnNames = {
        "guid", "muid", "token_index", "delete_date_time"})})
public class NofAccount implements Serializable {

	public enum Status {
		PRE_REGISTERED,
		BLOCKED,
		DE_REGISTERED,
		REGISTERED,
		SUSPENDED
	}

	private Long id;
	private Date createDateTime;
	private Status status=Status.REGISTERED;
	// guid + muid may not be unique
	// each end user has a guid+muid, but if a user has registered 2 cards, he will have 2 records here with same muid
	// (guid + muid + tokenIndex) is unique for ACTIVE accounts
	private String guid;
	private String muid;
	// mid sent to nets during registration
	// NETSPay side maps (muid + mid) to a token
	private String mid;
	// encrypted merchant token
	// same (muid+mid) should get same token, but let's not assume this 
	private String token;
	private String tokenExpiry;	// yymm
	private String tokenHash;	// sha256 of plaintext token
	private String tokenIndex="00";
	// merchTokenId is uniquely generated by system
	// this uniquely identify the user + registered card
	private String merchTokenId; 
	private String issFiid;		// issuer bank FIID
	private String issName;		// issuer bank short name
	private String last4;		// last 4 digit of pan
	private Date deleteDateTime; // not null means deleted
	
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_NOF_ACCOUNT")
	@SequenceGenerator(name = "SEQGEN_NOF_ACCOUNT", sequenceName = "TMSQ_NOF_ACCOUNT")
	@Column(name = "id")
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "create_date_time", nullable=false)
	public Date getCreateDateTime() {
		return createDateTime;
	}

	public void setCreateDateTime(Date createDateTime) {
		this.createDateTime = createDateTime;
	}

	@Column(name = "status", nullable=false)
	public Status getStatus() {
		return status;
	}

	public void setStatus(Status status) {
		this.status = status;
	}

	@Column(name = "guid", nullable=false)
	public String getGuid() {
		return guid;
	}

	public void setGuid(String guid) {
		this.guid = guid;
	}

	@Column(name = "muid", nullable=false)
	public String getMuid() {
		return muid;
	}

	public void setMuid(String muid) {
		this.muid = muid;
	}

	@Column(name = "mid", nullable=false)
	public String getMid() {
		return mid;
	}

	public void setMid(String mid) {
		this.mid = mid;
	}

	@Column(name = "token", nullable=false)
	public String getToken() {
		return token;
	}

	public void setToken(String token) {
		this.token = token;
	}

	@Column(name = "token_expiry", length=4)
	public String getTokenExpiry() {
		return tokenExpiry;
	}

	public void setTokenExpiry(String tokenExpiry) {
		this.tokenExpiry = tokenExpiry;
	}
	
	@Column(name = "token_hash", nullable=false)
	public String getTokenHash() {
		return tokenHash;
	}

	public void setTokenHash(String tokenHash) {
		this.tokenHash = tokenHash;
	}

	@Column(name = "token_index", length=2, nullable=false)
	public String getTokenIndex() {
		return tokenIndex;
	}

	public void setTokenIndex(String tokenIndex) {
		this.tokenIndex = tokenIndex;
	}
	
	@Column(name = "merch_token_id", nullable=false, unique=true)
	public String getMerchTokenId() {
		return merchTokenId;
	}

	public void setMerchTokenId(String merchTokenId) {
		this.merchTokenId = merchTokenId;
	}
	
	@Column(name = "iss_fiid", length=10)
	public String getIssFiid() {
		return issFiid;
	}

	public void setIssFiid(String issFiid) {
		this.issFiid = issFiid;
	}

	@Column(name = "iss_name", length=32)
	public String getIssName() {
		return issName;
	}

	public void setIssName(String issName) {
		this.issName = issName;
	}
	
	@Column(name = "last_4", length=4)
	public String getLast4() {
		return last4;
	}

	public void setLast4(String last4) {
		this.last4 = last4;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "delete_date_time")
	public Date getDeleteDateTime() {
		return deleteDateTime;
	}

	public void setDeleteDateTime(Date deleteDateTime) {
		this.deleteDateTime = deleteDateTime;
	}
	
}
