package com.abl.db.model;

import javax.persistence.*;

import org.hibernate.annotations.ForeignKey;

import java.io.Serializable;
import java.util.Date;

@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_VEHICLE_GROUP")
public class VehicleGroup implements Serializable {

    private Long id;
    private Group group;
    public Vehicle vehicle;
    private Date assignDateTime;

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_VEHICLE_GROUP")
    @SequenceGenerator(name = "SEQGEN_VEHICLE_GROUP", sequenceName = "TMSQ_VEHICLE_GROUP")
    @Column(name = "id")
    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "group_id")
    @ForeignKey(name="TMFC_GROUP_ID_VEH_GROUP") 
    public Group getGroup() {
        return group;
    }

    public void setGroup(Group group) {
        this.group = group;
    }

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "vehicle_id", unique = true)
    @ForeignKey(name="TMFC_VEH_ID_VEH_GROUP") 
    public Vehicle getVehicle() {
        return vehicle;
    }

    public void setVehicle(Vehicle vehicle) {
        this.vehicle = vehicle;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "assign_date_time", nullable = false)
    public Date getAssignDateTime() {
        return assignDateTime;
    }

    public void setAssignDateTime(Date assignDateTime) {
        this.assignDateTime = assignDateTime;
    }
}
