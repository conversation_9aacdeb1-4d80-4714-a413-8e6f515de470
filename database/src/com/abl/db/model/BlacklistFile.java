package com.abl.db.model;

import javax.persistence.*;

import org.hibernate.annotations.ForeignKey;

import java.io.Serializable;

@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_BLACKLIST_FILE")
public class BlacklistFile implements Serializable {

    private long id;
    private int recordNo;
    private String data;
    private BlacklistVersion blacklistVersion;

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_BLACKLIST_FILE")
    @SequenceGenerator(name = "SEQGEN_BLACKLIST_FILE", sequenceName = "TMSQ_BLACKLIST_FILE")
    @Column(name = "id")
    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    @Column(name = "record_no", nullable = false)
    public int getRecordNo() {
        return recordNo;
    }

    public void setRecordNo(int recordNo) {
        this.recordNo = recordNo;
    }

    @Column(name = "data", nullable = false, length = 1000)
    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "blacklist_version_id")
    @ForeignKey(name="TMFC_BL_VERSION_ID_BL_FILE") 
    public BlacklistVersion getBlacklistVersion() {
        return blacklistVersion;
    }

    public void setBlacklistVersion(BlacklistVersion blacklistVersion) {
        this.blacklistVersion = blacklistVersion;
    }

}
