package com.abl.db.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Index;

import com.abl.utils.ByteUtils;

@Entity 
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@Table(name = "BATCHTB_EZLINK_TRANSACTIONS")
public class EzlinkTransaction {
	private Long id;	
	private String can;
	private String ptc;
	private Date logDT;
	private Date transDT;
	private String transactionMessage;		
	private String driverInfo;
	private String samID;
	private String batchControlfilename;
	private String batchFilename;
	private String batchStatus;
	private String mid;
	private String tid;
	private String taxiNo;
	private String driverId;
	private String jobNo;
	private Long amount;
	
	

	public void setId(Long id) {
		this.id = id;
	}
	
	@Id @GeneratedValue(strategy=GenerationType.AUTO) 
	public Long getId() {
		return id;
	}
	
	public void setCan(String can) {
		this.can = can;
	}
	
	@Column(nullable=false, length=16)
	@Index(name="BATCHIX_EZLINK_CAN")
	public String getCan() {
		return can;
	}
	
	public void setPtc(String ptc) {
		this.ptc = ptc;
	}
	
	@Column(nullable=false, length=6)
	@Index(name="BATCHIX_EZL_PTC")
	public String getPtc() {
		return ptc.toUpperCase();
	}
	
	public void setLogDT(Date logDT) {
		this.logDT = logDT;
	}
	
	@Column(nullable=false)
	public Date getLogDT() {
		return logDT;
	}
	
	public void setTransDT(Date transDT) {
		this.transDT = transDT;
	}
	
	@Column(nullable=false)
	public Date getTransDT() {
		return transDT;
	}
	
	public void setTransactionMessage(String transactionMessage) {
		this.transactionMessage = transactionMessage;
	}
	
	// Note: store as hex
	@Column(nullable=false, length=266)
	public String getTransactionMessage() {
		return transactionMessage;
	}
	
	public void setBatchControlfilename(String batchControlfilename) {
		this.batchControlfilename = batchControlfilename;
	}
	
	@Column(nullable=true, length=31)
	public String getBatchControlfilename() {
		return batchControlfilename;
	}
	
	public void setBatchFilename(String batchFilename) {
		this.batchFilename = batchFilename;
	}
	
	@Column(nullable=true, length=41)	
	public String getBatchFilename() {
		return batchFilename;
	}
	
	public void setBatchStatus(String batchStatus) {
		this.batchStatus = batchStatus;
	}
	
	@Column(nullable=true, length=2)	
	public String getBatchStatus() {
		return batchStatus;
	}

	public void setDriverInfo(String driverInfo) {
		this.driverInfo = driverInfo;
	}

	@Column(nullable=false, length=43)
	public String getDriverInfo() {
		return driverInfo;
	}

	public void setSamID(String samID) {
		this.samID = samID;
	}

	@Column(nullable=false, length=8)
	public String getSamID() {
		return samID;
	}

	public void setMid(String mid) {
		this.mid = mid;
	}
	
	@Column(nullable=false, length=15)
	public String getMid() {
		return mid;
	}

	public void setTid(String tid) {
		this.tid = tid;
	}

	@Column(nullable=false, length=8)
	public String getTid() {
		return tid;
	}

	@Transient
	public String getTaxiNo() {
		return driverInfo.substring(0, 12);
	}

	public void setTaxiNo(String taxiNo) {
		this.taxiNo = taxiNo;
	}

	@Transient
	public String getDriverId() {
		return driverInfo.substring(12,21);
	}

	public void setDriverId(String driverId) {
		this.driverId = driverId;
	}

	@Transient
	public String getJobNo() {
		return driverInfo.substring(21,31);
	}

	public void setJobNo(String jobNo) {
		this.jobNo = jobNo;
	}

	@Transient
	public Long getAmount() {
		Long baseAmt=(long) 16777216;
		int i= Integer.parseInt(transactionMessage.substring(32,38),16);
		Long amount =baseAmt-(long)i;
		return amount;
	}

	public void setAmount(Long amount) {
		this.amount = amount;
	}

}

