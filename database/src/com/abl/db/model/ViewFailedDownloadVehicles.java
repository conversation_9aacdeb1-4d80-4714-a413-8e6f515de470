package com.abl.db.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

@SuppressWarnings("serial")
@Entity
@Table(name = "TMVW_FAILED_DOWNLOAD_VEHICLES")
public class ViewFailedDownloadVehicles implements Serializable {
	
	private long id;
	private String vehicleId;
	private String vehicleType;
	private String companyId;
	private String ivdModelId;
	private String serialNo;
	private String firmwareVersion;
	private String ezlinkSamSerialNo;
	private String masterAppVersion;
	private String creditAppVersion;
	private String netsAppVersion;
	private String epinsAppVersion;
	private String ezlinkAppVersion;
	private String fullBlackListRange;
	private String fullBlackListCan;
	private String smallBlacklistRange;
	private String smallBlackListCan;
	private Date updateDt;
	private String driverNric;
	private String driverName;
	private String drivingMode;
	private String phone1;
	private String phone2;
	private String failedReason;
    private String currentRelease;
    private String downloadRelease;
    private String currentReleaseDescription;
    private String downloadReleaseDescription;
    private String currentReleaseVersion;
    private String downloadReleaseVersion;
	private String padMasterAppVersion;
	private String padCreditAppVersion;
	private String padNetsAppVersion;
	private String padEpinsAppVersion;
	private String padEzlinkAppVersion;
	private String padCurrentReleaseVersion;
	private String padDownloadReleaseVersion;
	private Date downloadDt;
    
    
    @Id
    public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}
    
	@Column(name = "vehicle_id", nullable = false, length = 20)
	public String getVehicleId() {
		return vehicleId;
	}

	public void setVehicleId(String vehicleId) {
		this.vehicleId = vehicleId;
	}

	@Column(name = "vehicle_type", length = 30)
	public String getVehicleType() {
		return vehicleType;
	}

	public void setVehicleType(String vehicleType) {
		this.vehicleType = vehicleType;
	}

	@Column(name = "company_id", nullable = false, length = 12)
	public String getCompanyId() {
		return companyId;
	}

	public void setCompanyId(String companyId) {
		this.companyId = companyId;
	}
	
	@Column(name = "ivd_model_id", length = 10)
	public String getIvdModelId() {
		return ivdModelId;
	}

	public void setIvdModelId(String ivdModelId) {
		this.ivdModelId = ivdModelId;
	}

	@Column(name = "firmware_ver", length = 3)
	public String getFirmwareVersion() {
		return firmwareVersion;
	}

	public void setFirmwareVersion(String firmwareVersion) {
		this.firmwareVersion = firmwareVersion;
	}
	
	@Column(name = "ezlink_sam_serial_no", length = 20)
	public String getEzlinkSamSerialNo() {
		return ezlinkSamSerialNo;
	}

	public void setEzlinkSamSerialNo(String ezlinkSamSerialNo) {
		this.ezlinkSamSerialNo = ezlinkSamSerialNo;
	}

	@Column(name = "master_app_version", length = 6)
	public String getMasterAppVersion() {
		return masterAppVersion;
	}

	public void setMasterAppVersion(String masterAppVersion) {
		this.masterAppVersion = masterAppVersion;
	}

	@Column(name = "credit_app_version", length = 6)
	public String getCreditAppVersion() {
		return creditAppVersion;
	}

	public void setCreditAppVersion(String creditAppVersion) {
		this.creditAppVersion = creditAppVersion;
	}

	@Column(name = "nets_app_version", length = 6)
	public String getNetsAppVersion() {
		return netsAppVersion;
	}

	public void setNetsAppVersion(String netsAppVersion) {
		this.netsAppVersion = netsAppVersion;
	}

	@Column(name = "epins_app_version", length = 6)
	public String getEpinsAppVersion() {
		return epinsAppVersion;
	}

	public void setEpinsAppVersion(String epinsAppVersion) {
		this.epinsAppVersion = epinsAppVersion;
	}

	@Column(name = "ezlink_app_version", length = 6)
	public String getEzlinkAppVersion() {
		return ezlinkAppVersion;
	}

	public void setEzlinkAppVersion(String ezlinkAppVersion) {
		this.ezlinkAppVersion = ezlinkAppVersion;
	}

	@Column(name = "full_black_list_range", length = 20)
	public String getFullBlackListRange() {
		return fullBlackListRange;
	}

	public void setFullBlackListRange(String fullBlackListRange) {
		this.fullBlackListRange = fullBlackListRange;
	}

	@Column(name = "full_black_list_can", length = 20)
	public String getFullBlackListCan() {
		return fullBlackListCan;
	}

	public void setFullBlackListCan(String fullBlackListCan) {
		this.fullBlackListCan = fullBlackListCan;
	}

	@Column(name = "small_black_list_range", length = 20)
	public String getSmallBlacklistRange() {
		return smallBlacklistRange;
	}

	public void setSmallBlacklistRange(String smallBlacklistRange) {
		this.smallBlacklistRange = smallBlacklistRange;
	}

	@Column(name = "small_black_list_can", length = 20)
	public String getSmallBlackListCan() {
		return smallBlackListCan;
	}

	public void setSmallBlackListCan(String smallBlackListCan) {
		this.smallBlackListCan = smallBlackListCan;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "update_dt")
	public Date getUpdateDt() {
		return updateDt;
	}

	public void setUpdateDt(Date updateDt) {
		this.updateDt = updateDt;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "download_dt")
	public Date getDownloadDt() {
		return downloadDt;
	}

	public void setDownloadDt(Date downloadDt) {
		this.downloadDt = downloadDt;
	}

	@Column(name = "driver_nric", nullable = false, length = 12)
	public String getDriverNric() {
		return driverNric;
	}

	public void setDriverNric(String driverNric) {
		this.driverNric = driverNric;
	}

	@Column(name = "driver_name", nullable = false, length = 40)
	public String getDriverName() {
		return driverName;
	}

	public void setDriverName(String driverName) {
		this.driverName = driverName;
	}

	@Column(name = "driving_mode", length = 6)
	public String getDrivingMode() {
		return drivingMode;
	}

	public void setDrivingMode(String drivingMode) {
		this.drivingMode = drivingMode;
	}

	@Column(name = "phone1", length = 8)
	public String getPhone1() {
		return phone1;
	}

	public void setPhone1(String phone1) {
		this.phone1 = phone1;
	}

	@Column(name = "phone2", length = 8)
	public String getPhone2() {
		return phone2;
	}

	public void setPhone2(String phone2) {
		this.phone2 = phone2;
	}
	
	@Column(name = "failed_reason", nullable = false, length = 200)
    public String getFailedReason() {
        return failedReason;
    }

    public void setFailedReason(String failedReason) {
        this.failedReason = failedReason;
    }

    @Column(name = "current_release", length = 200)
    public String getCurrentRelease() {
        return currentRelease;
    }

    public void setCurrentRelease(String currentRelease) {
        this.currentRelease = currentRelease;
    }

    @Column(name = "download_release", nullable = false, length = 200)
    public String getDownloadRelease() {
        return downloadRelease;
    }

    public void setDownloadRelease(String downloadRelease) {
        this.downloadRelease = downloadRelease;
    }

    @Column(name = "serial_no")
	public String getSerialNo() {
		return serialNo;
	}

	public void setSerialNo(String serialNo) {
		this.serialNo = serialNo;
	}

	@Column(name = "current_release_description")
	public String getCurrentReleaseDescription() {
		return currentReleaseDescription;
	}

	public void setCurrentReleaseDescription(String currentReleaseDescription) {
		this.currentReleaseDescription = currentReleaseDescription;
	}

	@Column(name = "download_release_description")
	public String getDownloadReleaseDescription() {
		return downloadReleaseDescription;
	}

	public void setDownloadReleaseDescription(String downloadReleaseDescription) {
		this.downloadReleaseDescription = downloadReleaseDescription;
	}

	@Column(name = "current_release_version")
	public String getCurrentReleaseVersion() {
		return currentReleaseVersion;
	}

	public void setCurrentReleaseVersion(String currentReleaseVersion) {
		this.currentReleaseVersion = currentReleaseVersion;
	}

	@Column(name = "download_release_version")
	public String getDownloadReleaseVersion() {
		return downloadReleaseVersion;
	}

	public void setDownloadReleaseVersion(String downloadReleaseVersion) {
		this.downloadReleaseVersion = downloadReleaseVersion;
	}

	@Transient
	public String getPadMasterAppVersion() {
		return String.format("%06d", Integer.parseInt(getMasterAppVersion()));
	}
	
	@Transient
	public String getPadCreditAppVersion() {
		return String.format("%06d", Integer.parseInt(getCreditAppVersion()));
	}
	
	@Transient
	public String getPadNetsAppVersion() {
		return String.format("%06d", Integer.parseInt(getNetsAppVersion()));
	}
	
	@Transient
	public String getPadEpinsAppVersion() {
		return String.format("%06d", Integer.parseInt(getEpinsAppVersion()));
	}
	
	@Transient
	public String getPadEzlinkAppVersion() {
		return String.format("%06d", Integer.parseInt(getEzlinkAppVersion()));
	}

	@Transient
	public String getPadCurrentReleaseVersion() {
		return String.format("%06d", Integer.parseInt(getCurrentReleaseVersion()));
	}

	@Transient
	public String getPadDownloadReleaseVersion() {
		return String.format("%06d", Integer.parseInt(getDownloadReleaseVersion()));
	}
    
    

}
