package com.abl.db.model;

import javax.persistence.*;

import org.hibernate.annotations.ForeignKey;

import java.io.Serializable;
import java.util.Date;

@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_GROUP_RELEASE_HISTORY")
public class GroupReleaseHistory implements Serializable {

    private Long id;
    private Group group;
    private Release release;
    private Date assignDateTime;

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_GROUP_RELEASE_HISTORY")
    @SequenceGenerator(name = "SEQGEN_GROUP_RELEASE_HISTORY", sequenceName = "TMSQ_GROUP_RELEASE_HISTORY")
    @Column(name = "id")
    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "group_id")
    @ForeignKey(name="TMFC_GROUP_ID_GROUP_REL_HIST") 
    public Group getGroup() {
        return group;
    }

    public void setGroup(Group group) {
        this.group = group;
    }

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "release_id")
    @ForeignKey(name="TMFC_REL_ID_GROUP_REL_HIST") 
    public Release getRelease() {
        return release;
    }

    public void setRelease(Release release) {
        this.release = release;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "assign_date_time", nullable = false)
    public Date getAssignDateTime() {
        return assignDateTime;
    }

    public void setAssignDateTime(Date assignDateTime) {
        this.assignDateTime = assignDateTime;
    }
}
