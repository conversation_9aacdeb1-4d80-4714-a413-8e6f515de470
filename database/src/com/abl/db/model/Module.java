package com.abl.db.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * Represents a collection of "pages" for webadmin
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_MODULE")
public class Module implements Serializable {

    public static final int TYPE_NORMAL = 0;
    public static final int TYPE_SYSTEM = 1;
    public static final int TYPE_ADMIN = 2;

    private String name;                // identifies the record
    private String url;                    // url to access the module
    private String label;                // name to appear in menu
    private String displayOrder;        // order of display in menu
    private Integer type = TYPE_NORMAL;    // module type
    private Boolean visible = false;        // false means:
    // (1) will not display on menu, and
    // (2) access permission cannot be set using access profile

    @Id
    @Column(name = "name", length = 30)
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Column(name = "url", unique = true, nullable = false)
    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    @Column(name = "label", length = 30, unique = true, nullable = false)
    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    @Column(name = "display_order", unique = true, length = 10)
    public String getDisplayOrder() {
        return displayOrder;
    }

    public void setDisplayOrder(String displayOrder) {
        this.displayOrder = displayOrder;
    }

    @Column(name = "module_type")
    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    @Column(name = "visible", nullable = false)
    public Boolean isVisible() {
        return visible;
    }

    public void setVisible(Boolean visible) {
        this.visible = visible;
    }

    public String toString() {
        return "Module(name=" + name + ")";
    }

}
