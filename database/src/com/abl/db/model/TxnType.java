package com.abl.db.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "TMTB_TXN_TYPE")
public class TxnType {

    public static final String TYPE_UNKNOWN = "0000";

    public static final String TYPE_PARAM_VERSION = "0901";
    public static final String TYPE_PARAM_DOWNLOAD = "0902";
    public static final String TYPE_APP_VERSION = "0903";
    public static final String TYPE_FILE_DOWNLOAD = "0904";
    public static final String TYPE_LOG_UPLOAD = "0905";
    public static final String TYPE_BLACKLIST_VERSION = "0906";
    public static final String TYPE_SALES = "0907";
    public static final String TYPE_REVERSAL = "0908";
    public static final String TYPE_VOID = "0909";
    public static final String TYPE_OFFLINE_SALES = "0910";
    public static final String TYPE_VOID_REVERSAL = "0911";
    public static final String TYPE_UPLOAD_STUCK_TXN = "0912";

    private String code;            // unique code for the txn type
    private String name;            // unique name for the txn type

    @Id
    @Column(name = "code", length = 5)
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Column(name = "name", length = 30)
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String toString() {
        return name;
    }
}
