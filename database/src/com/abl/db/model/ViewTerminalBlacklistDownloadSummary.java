package com.abl.db.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

/**
 * This is a view, getting terminal application download summary
 * 
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "TMVW_TERM_BL_DOWNLOAD_SUMMARY")
public class ViewTerminalBlacklistDownloadSummary implements Serializable {

	private long id;
	private long blacklistDownloadJobId;
	private String startWindow;
	private String endWindow;
	private int concurrentDownload;
	private Date createDateTime;
	private short jobStatus;
	private int completed;
	private int pending;
	private int notStarted;
	private boolean deleted;
	private Date scheduleDate;
//	private long blacklistVersionId;

	@Id
	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	@Column(name = "job_id")
	public long getBlacklistDownloadJobId() {
		return blacklistDownloadJobId;
	}

	public void setBlacklistDownloadJobId(long blacklistDownloadJobId) {
		this.blacklistDownloadJobId = blacklistDownloadJobId;
	}

	@Column(name = "start_window", nullable = false, length = 4)
	public String getStartWindow() {
		return startWindow;
	}

	public void setStartWindow(String startWindow) {
		this.startWindow = startWindow;
	}

	@Column(name = "end_window", nullable = false, length = 4)
	public String getEndWindow() {
		return endWindow;
	}

	public void setEndWindow(String endWindow) {
		this.endWindow = endWindow;
	}

	@Column(name = "concurrent_download", nullable = false)
	public int getConcurrentDownload() {
		return concurrentDownload;
	}

	public void setConcurrentDownload(int concurrentDownload) {
		this.concurrentDownload = concurrentDownload;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "create_date_time")
	public Date getCreateDateTime() {
		return createDateTime;
	}

	public void setCreateDateTime(Date createDateTime) {
		this.createDateTime = createDateTime;
	}

	@Column(name = "job_status")
	public short getJobStatus() {
		return jobStatus;
	}

	public void setJobStatus(short jobStatus) {
		this.jobStatus = jobStatus;
	}

	@Column(name = "completed")
	public int getCompleted() {
		return completed;
	}

	public void setCompleted(int completed) {
		this.completed = completed;
	}

	@Column(name = "pending")
	public int getPending() {
		return pending;
	}

	public void setPending(int pending) {
		this.pending = pending;
	}

	@Column(name = "not_started")
	public int getNotStarted() {
		return notStarted;
	}

	public void setNotStarted(int notStarted) {
		this.notStarted = notStarted;
	}

	@Column(name = "deleted")
	public boolean isDeleted() {
		return deleted;
	}

	public void setDeleted(boolean deleted) {
		this.deleted = deleted;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name="schedule_date")
	public Date getScheduleDate() {
		return scheduleDate;
	}

	public void setScheduleDate(Date scheduleDate) {
		this.scheduleDate = scheduleDate;
	}

//	@Column(name="blacklist_version_id")
//	public long getBlacklistVersionId() {
//		return blacklistVersionId;
//	}
//
//	public void setBlacklistVersionId(long blacklistVersionId) {
//		this.blacklistVersionId = blacklistVersionId;
//	}

}
