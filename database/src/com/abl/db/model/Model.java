package com.abl.db.model;

import javax.persistence.*;

import org.hibernate.annotations.Type;

import java.io.Serializable;
import java.util.Date;

@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_MODEL")
public class Model implements Serializable {

    private long id;
    private String modelName;
    private String description;
    private Date createDateTime;
    private Boolean deleted = false;            // true means deleted.  Most dao methods should not return or modify

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_MODEL")
    @SequenceGenerator(name = "SEQGEN_MODEL", sequenceName = "TMSQ_MODEL")
    @Column(name = "id")
    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    @Column(name = "model_name", nullable = false, length = 50)
    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    @Column(name = "description", length = 200)
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "create_date_time", nullable = false)
    public Date getCreateDateTime() {
        return createDateTime;
    }

    public void setCreateDateTime(Date createDateTime) {
        this.createDateTime = createDateTime;
    }

    @Type(type="boolean")
    @Column(name = "deleted", columnDefinition = "int default 0", nullable = false)
    public Boolean isDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }
}
