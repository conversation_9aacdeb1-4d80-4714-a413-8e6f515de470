package com.abl.db.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import java.io.Serializable;
import java.util.Date;


@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_DASH_SINGTEL_LOG")
public class DashSingtelLog implements Serializable {
	private Long id;
    private String amount;
    private Date createDt;
    private String counterId;
    private String transactionId;
    private String vehicleId;
    private String additionalData;
    private String transmitStatus;
    private String dashResponseResult;
    private String dashMessage;
    private String dashReferenceNumber;
    private String dashNewBalance;
    private String dashFinalAmount;
    private String dashOriginalAmount;
    private String tid;
    private String dashTxnStatus;
    private String serialNo;
    private String transDateTime;
    private String messageClass;
    private String dashRespMsg;
    private String jobNumber;
    private String driverId;
    
    
	@Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_DASH_SINGTEL_LOG")
    @SequenceGenerator(name = "SEQGEN_DASH_SINGTEL_LOG", sequenceName = "TMSQ_DASH_SINGTEL_LOG")
    @Column(name = "id")
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	 @Column(name = "amount", length = 12, nullable = false)
	public String getAmount() {
		return amount;
	}
	public void setAmount(String amount) {
		this.amount = amount;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
    @Column(name = "create_dt", nullable = false)
	public Date getCreateDt() {
		return createDt;
	}
	public void setCreateDt(Date createDt) {
		this.createDt = createDt;
	}
	
	 @Column(name = "counter_id", length = 20)
	public String getCounterId() {
		return counterId;
	}
	public void setCounterId(String counterId) {
		this.counterId = counterId;
	}
	
	@Column(name = "transaction_id", length = 30)
	public String getTransactionId() {
		return transactionId;
	}
	public void setTransactionId(String transactionId) {
		this.transactionId = transactionId;
	}
	
	@Column(name = "vehicle_id", length = 15)
	public String getVehicleId() {
		return vehicleId;
	}
	public void setVehicleId(String vehicleId) {
		this.vehicleId = vehicleId;
	}
	
	@Column(name = "additional_data", length = 200)
	public String getAdditionalData() {
		return additionalData;
	}
	public void setAdditionalData(String additionalData) {
		this.additionalData = additionalData;
	}
	
	@Column(name = "transmit_status", length = 50)
	public String getTransmitStatus() {
		return transmitStatus;
	}
	public void setTransmitStatus(String transmitStatus) {
		this.transmitStatus = transmitStatus;
	}
	@Column(name = "dash_response_result", length = 100)
	public String getDashResponseResult() {
		return dashResponseResult;
	}
	public void setDashResponseResult(String dashResponseResult) {
		this.dashResponseResult = dashResponseResult;
	}
	@Column(name = "dash_message", length = 1024)
	public String getDashMessage() {
		return dashMessage;
	}
	public void setDashMessage(String dashMessage) {
		this.dashMessage = dashMessage;
	}
	@Column(name = "dash_reference_number", length = 20)
	public String getDashReferenceNumber() {
		return dashReferenceNumber;
	}
	public void setDashReferenceNumber(String dashReferenceNumber) {
		this.dashReferenceNumber = dashReferenceNumber;
	}
	
	@Column(name = "dash_new_balance", length = 20)
	public String getDashNewBalance() {
		return dashNewBalance;
	}
	public void setDashNewBalance(String dashNewBalance) {
		this.dashNewBalance = dashNewBalance;
	}
	
	@Column(name = "dash_final_amount", length = 20)
	public String getDashFinalAmount() {
		return dashFinalAmount;
	}
	
	public void setDashFinalAmount(String dashFinalAmount) {
		this.dashFinalAmount = dashFinalAmount;
	}
	
	@Column(name = "dash_original_amount", length = 20)
	public String getDashOriginalAmount() {
		return dashOriginalAmount;
	}
	
	public void setDashOriginalAmount(String dashOriginalAmount) {
		this.dashOriginalAmount = dashOriginalAmount;
	}
	
	@Column(name = "tid", length = 15)
	public String getTid() {
		return tid;
	}
	public void setTid(String tid) {
		this.tid = tid;
	}
	
	@Column(name = "serial_no", length = 15)
	public String getSerialNo() {
		return serialNo;
	}
	public void setSerialNo(String serialNo) {
		this.serialNo = serialNo;
	}

	@Column(name = "trans_date_time", length = 16)
	public String getTransDateTime() {
		return transDateTime;
	}
	public void setTransDateTime(String transDateTime) {
		this.transDateTime = transDateTime;
	}
	
	@Column(name = "message_class", length = 30)
	public String getMessageClass() {
		return messageClass;
	}
	public void setMessageClass(String messageClass) {
		this.messageClass = messageClass;
	}
	@Column(name = "dash_response_message", length = 1024)
	public String getDashRespMsg() {
		return dashRespMsg;
	}
	public void setDashRespMsg(String dashRespMsg) {
		this.dashRespMsg = dashRespMsg;
	}
	@Column(name = "job_number", length=10)
	public String getJobNumber() {
		return jobNumber;
	}
	public void setJobNumber(String jobNumber) {
		this.jobNumber = jobNumber;
	}
	
	@Column(name = "driver_id", length=9)
	public String getDriverId() {
		return driverId;
	}
	public void setDriverId(String driverId) {
		this.driverId = driverId;
	}
	
	@Column(name = "dash_txn_status", length = 15)
	public String getDashTxnStatus() {
		return dashTxnStatus;
	}
	public void setDashTxnStatus(String dashTxnStatus) {
		this.dashTxnStatus = dashTxnStatus;
	}

}
