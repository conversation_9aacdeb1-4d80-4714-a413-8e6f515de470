package com.abl.db.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_NOF_TXN_LOG")
public class NofTxnLog implements Serializable {

	public enum Source {
		DCP,
		LES
	}
	
	public enum Status {
		NEW,
		PENDING,	// status set by server, batch will pick up to process to process offline purchase
		PROCESSING,	// status set by batch when it is processing offline purchase
		REVERSING,	// status set by batch when it is (going to) processing reversal. when reversal is cleared, the batch will update the status back to PENDING.
		SUCCESS, 
		FAIL,
	}

	public enum DcpStatus {
		NEW,
		PENDING, // retry declined payment job will pick up to send to dcp for the declined txns
		PROCESSING,	// status set by declined payment batch when it is processing declined txns
		SUCCESS, // already successfully sent to DCP. No retry is needed
		FAIL	// already sent to DCP for x hours, no retry is needed
	}
	
	public static final String MSG_TYPE_REGISTER="nof_register";
	public static final String MSG_TYPE_QUERY_TOKEN="nof_query_token";
	public static final String MSG_TYPE_CFA="nof_cfa";
	public static final String MSG_TYPE_CFA_CANCEL="nof_cfa_cancel";
	public static final String MSG_TYPE_PURCHASE="nof_purchase";
	public static final String MSG_TYPE_OFFLINE_PURCHASE="nof_offline_purchase";
	public static final String MSG_TYPE_PURCHASE_REVERSAL="nof_purchase_reversal";
	public static final String MSG_TYPE_UNKNOWN="nof_unknown";
	
	private Long id;
	private Source source=Source.LES;
	private boolean online=true;	// false=offline
	private Date dateTime;			// date/time txn is received
	private String mti; // MTI of ISO msg
	private String procCode; // proc code of ISO msg
	private Long txnAmt;
	private String stan;
	private String txnTime;			// P-12 time in request msg (hhmmss)
	private String txnDate;			// P-13 date in request msg (mmdd)
	private String tid;
	private String mid;
	private Status status = Status.NEW;
	private String txResult;	// internal error code
	private String respCode;
	private String jobNumber;
	private String taxiNo;
	private String driverId;
	private Long fareAmt;
	private Long gst;
	private Long fareAdmin;
	private String companyCode;
	private String pinpadSn;
	private String rrn;
	private String origRrn;
	private String authCode;
	private boolean voided=false;
	private boolean reversed=false;
	private String msgType;
	private String msgVer;			// only applicable for DCP msg
	private String transId;			// only applicable for DCP msg
	private String guid;
	private String muid;
	private String merchTokenId;
	private String tokenIndex;
	private String tokenExpiry;		// yymm
	private String deviceInfo;
	private String gpsCoord;
	private String origTid;			// register: nets pay tid
	private String origTransDate;	// register: nets pay trans date (mmdd)
	private String origTransTime;	// register: nets pay trans time (hhmmss)
	private String origAuthCode;	// register: nets pay auth code
	private String dataForNets;
	private DcpStatus dcpStatus = DcpStatus.NEW;
	private Date lastSentDcpDateTime;			// last sent to dcp
	private Integer dcpTryCount = 0; // number to retry sent to dcp
	
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_NOF_TXN_LOG")
	@SequenceGenerator(name = "SEQGEN_NOF_TXN_LOG", sequenceName = "TMSQ_NOF_TXN_LOG")
	@Column(name = "id")
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Enumerated(EnumType.STRING)
	@Column(name = "source", length=32)
	public Source getSource() {
		return source;
	}

	public void setSource(Source source) {
		this.source = source;
	}

	@Column(name = "is_online", nullable=false)
	public boolean isOnline() {
		return online;
	}

	public void setOnline(boolean online) {
		this.online = online;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "date_time", nullable=false)
	public Date getDateTime() {
		return dateTime;
	}

	public void setDateTime(Date dateTime) {
		this.dateTime = dateTime;
	}

	@Column(name = "mti", length = 4)
	public String getMti() {
		return mti;
	}

	public void setMti(String mti) {
		this.mti = mti;
	}

	@Column(name = "proc_code", length = 6)
	public String getProcCode() {
		return procCode;
	}

	public void setProcCode(String procCode) {
		this.procCode = procCode;
	}

	@Column(name = "tid", length = 8)
	public String getTid() {
		return tid;
	}

	public void setTid(String tid) {
		this.tid = tid;
	}

	@Column(name = "txn_amount")
	public Long getTxnAmt() {
		return txnAmt;
	}

	public void setTxnAmt(Long txnAmt) {
		this.txnAmt = txnAmt;
	}

	@Column(name = "stan", length = 6)
	public String getStan() {
		return stan;
	}

	public void setStan(String stan) {
		this.stan = stan;
	}

	@Column(name = "txn_time", length=6)
	public String getTxnTime() {
		return txnTime;
	}

	public void setTxnTime(String txnTime) {
		this.txnTime = txnTime;
	}
	
	@Column(name = "txn_date", length=6)
	public String getTxnDate() {
		return txnDate;
	}

	public void setTxnDate(String txnDate) {
		this.txnDate = txnDate;
	}
	
	@Column(name = "mid", length = 15)
	public String getMid() {
		return mid;
	}

	public void setMid(String mid) {
		this.mid = mid;
	}

	@Enumerated(EnumType.STRING)
	@Column(name = "status", length=12, nullable=false)
	public Status getStatus() {
		return status;
	}

	public void setStatus(Status status) {
		this.status = status;
	}

	@Column(name = "tx_result", length=32)
	public String getTxResult() {
		return txResult;
	}

	public void setTxResult(String txResult) {
		this.txResult = txResult;
	}
	
	@Column(name = "resp_code", length = 2)
	public String getRespCode() {
		return respCode;
	}

	public void setRespCode(String respCode) {
		this.respCode = respCode;
	}

	@Column(name = "job_number", length = 10)
	public String getJobNumber() {
		return jobNumber;
	}

	public void setJobNumber(String jobNumber) {
		this.jobNumber = jobNumber;
	}

	@Column(name = "taxi_no", length = 12)
	public String getTaxiNo() {
		return taxiNo;
	}

	public void setTaxiNo(String taxiNo) {
		this.taxiNo = taxiNo;
	}

	@Column(name = "driver_id", length = 9)
	public String getDriverId() {
		return driverId;
	}

	public void setDriverId(String driverId) {
		this.driverId = driverId;
	}

	@Column(name = "fare_amt")
	public Long getFareAmt() {
		return fareAmt;
	}

	public void setFareAmt(Long fareAmt) {
		this.fareAmt = fareAmt;
	}

	@Column(name = "fare_gst")
	public Long getGst() {
		return gst;
	}

	public void setGst(Long gst) {
		this.gst = gst;
	}

	@Column(name = "fare_admin")
	public Long getFareAdmin() {
		return fareAdmin;
	}

	public void setFareAdmin(Long fareAdmin) {
		this.fareAdmin = fareAdmin;
	}


	@Column(name = "company_code", length = 4)
	public String getCompanyCode() {
		return companyCode;
	}

	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}


	@Column(name = "pinpad_sn", length = 20)
	public String getPinpadSn() {
		return pinpadSn;
	}

	public void setPinpadSn(String pinpadSn) {
		this.pinpadSn = pinpadSn;
	}
	
	@Column(name = "rrn", length=24)
	public String getRrn() {
		return rrn;
	}

	public void setRrn(String rrn) {
		this.rrn = rrn;
	}

	@Column(name = "orig_rrn", length=24)
	public String getOrigRrn() {
		return origRrn;
	}

	public void setOrigRrn(String origRrn) {
		this.origRrn = origRrn;
	}

	@Column(name = "auth_code", length=16)
	public String getAuthCode() {
		return authCode;
	}

	public void setAuthCode(String authCode) {
		this.authCode = authCode;
	}
	
	@Column(name = "msg_type", length=64 )
	public String getMsgType() {
		return msgType;
	}

	public void setMsgType(String msgType) {
		this.msgType = msgType;
	}

	@Column(name = "msg_ver", length=16 )
	public String getMsgVer() {
		return msgVer;
	}

	public void setMsgVer(String msgVer) {
		this.msgVer = msgVer;
	}

	@Column(name = "transid", length=64 )
	public String getTransId() {
		return transId;
	}

	public void setTransId(String transId) {
		this.transId = transId;
	}

	@Column(name = "voided", nullable=false)
	public boolean isVoided() {
		return voided;
	}

	public void setVoided(boolean voided) {
		this.voided = voided;
	}

	@Column(name = "reversed", nullable=false)
	public boolean isReversed() {
		return reversed;
	}

	public void setReversed(boolean reversed) {
		this.reversed = reversed;
	}

	@Column(name = "guid")
	public String getGuid() {
		return guid;
	}

	public void setGuid(String guid) {
		this.guid = guid;
	}

	@Column(name = "muid")
	public String getMuid() {
		return muid;
	}

	public void setMuid(String muid) {
		this.muid = muid;
	}

	@Column(name = "merch_token_id", length=64)
	public String getMerchTokenId() {
		return merchTokenId;
	}

	public void setMerchTokenId(String merchTokenId) {
		this.merchTokenId = merchTokenId;
	}

	@Column(name = "token_index", length=2)
	public String getTokenIndex() {
		return tokenIndex;
	}

	public void setTokenIndex(String tokenIndex) {
		this.tokenIndex = tokenIndex;
	}

	@Column(name = "token_expiry", length=8)
	public String getTokenExpiry() {
		return tokenExpiry;
	}

	public void setTokenExpiry(String tokenExpiry) {
		this.tokenExpiry = tokenExpiry;
	}

	@Column(name = "device_info")
	public String getDeviceInfo() {
		return deviceInfo;
	}

	public void setDeviceInfo(String deviceInfo) {
		this.deviceInfo = deviceInfo;
	}

	@Column(name = "gps_coord")
	public String getGpsCoord() {
		return gpsCoord;
	}

	public void setGpsCoord(String gpsCoord) {
		this.gpsCoord = gpsCoord;
	}

	@Column(name = "orig_tid", length=16)
	public String getOrigTid() {
		return origTid;
	}

	public void setOrigTid(String origTid) {
		this.origTid = origTid;
	}

	@Column(name = "orig_trans_date", length=8)
	public String getOrigTransDate() {
		return origTransDate;
	}

	public void setOrigTransDate(String origTransDate) {
		this.origTransDate = origTransDate;
	}

	@Column(name = "orig_trans_time", length=8)
	public String getOrigTransTime() {
		return origTransTime;
	}

	public void setOrigTransTime(String origTransTime) {
		this.origTransTime = origTransTime;
	}

	@Column(name = "orig_auth_code", length=16)
	public String getOrigAuthCode() {
		return origAuthCode;
	}

	public void setOrigAuthCode(String origAuthCode) {
		this.origAuthCode = origAuthCode;
	}

	@Column(name = "data_for_nets", length = 512)
	public String getDataForNets() {
		return dataForNets;
	}

	public void setDataForNets(String dataForNets) {
		this.dataForNets = dataForNets;
	}

	@Enumerated(EnumType.STRING)
	@Column(name = "dcp_status", length=12, nullable=false)
	public DcpStatus getDcpStatus() {
		return dcpStatus;
	}

	public void setDcpStatus(DcpStatus dcpStatus) {
		this.dcpStatus = dcpStatus;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "last_sent_to_dcp", nullable=true)
	public Date getLastSentDcpDateTime() {
		return lastSentDcpDateTime;
	}

	public void setLastSentDcpDateTime(Date lastSentDcpDateTime) {
		this.lastSentDcpDateTime = lastSentDcpDateTime;
	}

	@Column(name = "dcp_try_count")
	public Integer getDcpTryCount() {
		return dcpTryCount;
	}

	public void setDcpTryCount(Integer dcpTryCount) {
		this.dcpTryCount = dcpTryCount;
	}
}
