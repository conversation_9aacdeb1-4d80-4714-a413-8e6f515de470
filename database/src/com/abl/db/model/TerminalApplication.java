package com.abl.db.model;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_TERMINAL_APPLICATION")
@AssociationOverrides({
        @AssociationOverride(name = "pk.tmsTerminal", joinColumns = @JoinColumn(name = "terminal_id")),
        @AssociationOverride(name = "pk.application", joinColumns = @JoinColumn(name = "application_id")),
        @AssociationOverride(name = "pk.release", joinColumns = @JoinColumn(name = "release_id"))})
public class TerminalApplication implements Serializable {

    private TerminalApplicationId pk = new TerminalApplicationId();
    private Date updateDateTime;

    @EmbeddedId
    public TerminalApplicationId getPk() {
        return pk;
    }

    public void setPk(TerminalApplicationId pk) {
        this.pk = pk;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "update_date_time", nullable = false)
    public Date getUpdateDateTime() {
        return updateDateTime;
    }

    public void setUpdateDateTime(Date updateDateTime) {
        this.updateDateTime = updateDateTime;
    }

    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;

        TerminalApplication that = (TerminalApplication) o;

        if (getPk() != null ? !getPk().equals(that.getPk())
                : that.getPk() != null)
            return false;

        return true;
    }

    public int hashCode() {
        return (getPk() != null ? getPk().hashCode() : 0);
    }
}
