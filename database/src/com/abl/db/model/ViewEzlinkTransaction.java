package com.abl.db.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * This is a view, getting duplicate/double debit for ezlink
 * 
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "BATCHB_VW_EZLINK_DOUBLE_DEBIT")
public class ViewEzlinkTransaction {
	
	private String id;	
	private String can;
	private String ptc;
	private Date logDT;
	private Date transDT;
	private String transactionMessage;

	private String taxiNo;
	private String driverId;
	private String jobNo;
	private Long amount;
	
	
	@Id
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	@Column(nullable=false, length=16)
	public String getCan() {
		return can;
	}
	public void setCan(String can) {
		this.can = can;
	}
	@Column(nullable=false, length=6)
	public String getPtc() {
		return ptc;
	}
	public void setPtc(String ptc) {
		this.ptc = ptc;
	}
	
	@Column(nullable=false)
	public Date getLogDT() {
		return logDT;
	}
	public void setLogDT(Date logDT) {
		this.logDT = logDT;
	}
	
	@Column(nullable=false)
	public Date getTransDT() {
		return transDT;
	}
	public void setTransDT(Date transDT) {
		this.transDT = transDT;
	}
	
	@Column(nullable=false, length=266)
	public String getTransactionMessage() {
		return transactionMessage;
	}
	public void setTransactionMessage(String transactionMessage) {
		this.transactionMessage = transactionMessage;
	}
	@Column(nullable=true)
	public String getTaxiNo() {
		return taxiNo;
	}
	public void setTaxiNo(String taxiNo) {
		this.taxiNo = taxiNo;
	}
	
	@Column(nullable=true)
	public String getDriverId() {
		return driverId;
	}
	public void setDriverId(String driverId) {
		this.driverId = driverId;
	}
	@Column(nullable=true)
	public String getJobNo() {
		return jobNo;
	}
	public void setJobNo(String jobNo) {
		this.jobNo = jobNo;
	}
	@Column(nullable=true)
	public Long getAmount() {
		return amount;
	}
	public void setAmount(Long amount) {
		this.amount = amount;
	}
	
	
	
	
}
