package com.abl.db.model;

import javax.persistence.*;
import java.io.Serializable;

@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_PROFILE_FEE_VALUE")
@AssociationOverrides({
        @AssociationOverride(name = "pk.profileFee", joinColumns = @JoinColumn(name = "profile_fee_id")),
        @AssociationOverride(name = "pk.parameterDefinition", joinColumns = @JoinColumn(name = "parameter_definition_id"))})
public class ProfileFeeValue implements Serializable {

    private ProfileFeeValueId pk = new ProfileFeeValueId();
    private String value;

    @EmbeddedId
    public ProfileFeeValueId getPk() {
        return pk;
    }

    public void setPk(ProfileFeeValueId pk) {
        this.pk = pk;
    }

    @Column(name = "fee_value", nullable = true, length = 200)
    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;

        ProfileFeeValue that = (ProfileFeeValue) o;

        if (getPk() != null ? !getPk().equals(that.getPk())
                : that.getPk() != null)
            return false;

        return true;
    }

    public int hashCode() {
        return (getPk() != null ? getPk().hashCode() : 0);
    }

}
