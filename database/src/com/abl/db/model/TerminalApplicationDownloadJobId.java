package com.abl.db.model;

import javax.persistence.Embeddable;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;

import org.hibernate.annotations.ForeignKey;

import java.io.Serializable;

@SuppressWarnings("serial")
@Embeddable
public class TerminalApplicationDownloadJobId implements Serializable {

    private TmsTerminal tmsTerminal;
    private Vehicle vehicle;
    private Release release;
    private ApplicationDownloadJob applicationDownloadJob;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @ForeignKey(name="TMFC_TERM_ID_TERM_APP_DL_JOB") 
    public TmsTerminal getTmsTerminal() {
        return tmsTerminal;
    }

    public void setTmsTerminal(TmsTerminal tmsTerminal) {
        this.tmsTerminal = tmsTerminal;
    }

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @ForeignKey(name="TMFC_VEH_ID_TERM_APP_DL_JOB") 
    public Vehicle getVehicle() {
        return vehicle;
    }

    public void setVehicle(Vehicle vehicle) {
        this.vehicle = vehicle;
    }

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @ForeignKey(name="TMFC_REL_ID_TERM_APP_DL_JOB")
    public Release getRelease() {
        return release;
    }

    public void setRelease(Release release) {
        this.release = release;
    }

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @ForeignKey(name="TMFC_APP_DJOB_ID_TERM_APP_DJOB")
    public ApplicationDownloadJob getApplicationDownloadJob() {
        return applicationDownloadJob;
    }

    public void setApplicationDownloadJob(ApplicationDownloadJob applicationDownloadJob) {
        this.applicationDownloadJob = applicationDownloadJob;
    }

    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;

        TerminalApplicationDownloadJobId that = (TerminalApplicationDownloadJobId) o;

        if (tmsTerminal != null ? !tmsTerminal.equals(that.tmsTerminal)
                : that.tmsTerminal != null)
            return false;
        if (vehicle != null ? !vehicle.equals(that.vehicle)
                : that.vehicle != null)
            return false;
        if (release != null ? !release.equals(that.release)
                : that.release != null)
            return false;
        if (applicationDownloadJob != null ? !applicationDownloadJob
                .equals(that.applicationDownloadJob)
                : that.applicationDownloadJob != null)
            return false;

        return true;
    }

    // todo: how to add for vehicle & release??
    public int hashCode() {
        int result;
        result = (tmsTerminal != null ? tmsTerminal.hashCode() : 0);
        result = 31
                * result
                + (applicationDownloadJob != null ? applicationDownloadJob.hashCode()
                : 0);
        return result;
    }

}
