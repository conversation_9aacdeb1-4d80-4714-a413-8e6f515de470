package com.abl.db.model;

import javax.persistence.*;
import java.io.Serializable;

@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_PROFILE_BIN_VALUE")
@AssociationOverrides({
        @AssociationOverride(name = "pk.profileBinRange", joinColumns = @JoinColumn(name = "profile_bin_range_id")),
        @AssociationOverride(name = "pk.parameterDefinition", joinColumns = @JoinColumn(name = "parameter_definition_id"))})
public class ProfileBinValue implements Serializable {

    private ProfileBinValueId pk = new ProfileBinValueId();
    private String value;

    @EmbeddedId
    public ProfileBinValueId getPk() {
        return pk;
    }

    public void setPk(ProfileBinValueId pk) {
        this.pk = pk;
    }

    @Column(name = "value", nullable = false, length = 200)
    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;

        ProfileBinValue that = (ProfileBinValue) o;

        if (getPk() != null ? !getPk().equals(that.getPk())
                : that.getPk() != null)
            return false;

        return true;
    }

    public int hashCode() {
        return (getPk() != null ? getPk().hashCode() : 0);
    }

}
