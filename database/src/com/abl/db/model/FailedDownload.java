package com.abl.db.model;

import javax.persistence.*;

import org.hibernate.annotations.ForeignKey;
import org.hibernate.annotations.Type;

import java.io.Serializable;
import java.util.Date;

@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_FAILED_DOWNLOAD")
public class FailedDownload implements Serializable {

    private String vehicleNo;
    private String serialNo;
    private Date updateDateTime;
    private String failedReason;
    private Release currentRelease;
    private Release downloadRelease;
    private Boolean deleted = false;

    @Id
    @Column(name = "vehicle_no", nullable = false, length = 20)
    public String getVehicleNo() {
        return vehicleNo;
    }

    public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
    }

    @Column(name = "serial_no", nullable = false, length = 20)
    public String getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    @Column(name = "failed_reason", nullable = false, length = 500)
    public String getFailedReason() {
        return failedReason;
    }

    public void setFailedReason(String failedReason) {
        this.failedReason = failedReason;
    }

    @ManyToOne(fetch = FetchType.LAZY, optional = true)
    @JoinColumn(name = "current_release_id")
    @ForeignKey(name="TMFC_REL_CURR_REL_ID_FAILED_DL") 
    public Release getCurrentRelease() {
        return currentRelease;
    }

    public void setCurrentRelease(Release currentRelease) {
        this.currentRelease = currentRelease;
    }

    @ManyToOne(fetch = FetchType.LAZY, optional = true)
    @JoinColumn(name = "download_release_id")
    @ForeignKey(name="TMFC_REL_DL_REL_ID_FAILED_DL") 
    public Release getDownloadRelease() {
        return downloadRelease;
    }

    public void setDownloadRelease(Release downloadRelease) {
        this.downloadRelease = downloadRelease;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "update_date_time", nullable = false)
    public Date getUpdateDateTime() {
        return updateDateTime;
    }

    public void setUpdateDateTime(Date updateDateTime) {
        this.updateDateTime = updateDateTime;
    }

    @Type(type="boolean")
    @Column(name = "deleted", columnDefinition = "int default 0", nullable = false)
    public Boolean isDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }
}
