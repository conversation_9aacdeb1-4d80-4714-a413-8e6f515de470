package com.abl.db.model;

import javax.persistence.Embeddable;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;

import org.hibernate.annotations.ForeignKey;
import org.springframework.util.ObjectUtils;

import java.io.Serializable;

@SuppressWarnings("serial")
@Embeddable
public class ApplicationBinValueId implements Serializable {
    private ApplicationBinRange applicationBinRange;
    private ParameterDefinition parameterDefinition;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @ForeignKey(name="TMFC_APPBR_ID_APPBV") 
    public ApplicationBinRange getApplicationBinRange() {
        return applicationBinRange;
    }

    public void setApplicationBinRange(ApplicationBinRange applicationBinRange) {
        this.applicationBinRange = applicationBinRange;
    }


    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @ForeignKey(name="TMFC_PARAM_DEF_ID_APPBV")
    public ParameterDefinition getParameterDefinition() {
        return parameterDefinition;
    }

    public void setParameterDefinition(ParameterDefinition parameterDefinition) {
        this.parameterDefinition = parameterDefinition;
    }
    
    
    @Override
		public boolean equals(Object obj) {
			if (obj == this) {
				return true;
			} else if (obj instanceof ApplicationBinValueId) {
				ApplicationBinValueId oth = (ApplicationBinValueId) obj;
				return ObjectUtils.nullSafeEquals(getApplicationBinRange(), oth.getApplicationBinRange())
						&& ObjectUtils.nullSafeEquals(getParameterDefinition(), oth.getParameterDefinition());
			}
			return false;
		}
		
		@Override
		public int hashCode() {
			int hash = ObjectUtils.nullSafeHashCode(getApplicationBinRange());
			hash = 31 * hash + ObjectUtils.nullSafeHashCode(getParameterDefinition());
			return hash;
		}

}
