package com.abl.db.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

/**
 * This is a view, getting terminals application info
 * 
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "TMVW_TERM_APP_SUMMARY")
public class ViewTerminalApplicationSummary {

	private long id;
	private long terminalId;
	private String serialNo;
	private long applicationId;
	private String vehicleId;
	private int appVersion;
	private String padAppVersion;
	private String appName;
	private String appDescription;
	private Date updateDateTime;

	@Id
	@Column(name = "id")
	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	@Column(name = "terminal_id")
	public long getTerminalId() {
		return terminalId;
	}

	public void setTerminalId(long terminalId) {
		this.terminalId = terminalId;
	}

	@Column(name = "serial_no")
	public String getSerialNo() {
		return serialNo;
	}

	public void setSerialNo(String serialNo) {
		this.serialNo = serialNo;
	}

	@Column(name = "application_id")
	public long getApplicationId() {
		return applicationId;
	}

	public void setApplicationId(long applicationId) {
		this.applicationId = applicationId;
	}

	@Column(name = "vehicle_id")
	public String getVehicleId() {
		return vehicleId;
	}

	public void setVehicleId(String vehicleId) {
		this.vehicleId = vehicleId;
	}

	@Column(name = "app_version")
	public int getAppVersion() {
		return appVersion;
	}

	public void setAppVersion(int appVersion) {
		this.appVersion = appVersion;
	}

	@Transient
	public String getPadAppVersion() {
		return String.format("%06d", getAppVersion());
	}

	public void setPadAppVersion(String padAppVersion) {
		this.padAppVersion = padAppVersion;
	}

	@Column(name = "app_name")
	public String getAppName() {
		return appName;
	}

	public void setAppName(String appName) {
		this.appName = appName;
	}

	@Column(name = "description")
	public String getAppDescription() {
		return appDescription;
	}

	public void setAppDescription(String appDescription) {
		this.appDescription = appDescription;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "update_date_time")
	public Date getUpdateDateTime() {
		return updateDateTime;
	}

	public void setUpdateDateTime(Date updateDateTime) {
		this.updateDateTime = updateDateTime;
	}

}
