package com.abl.db.model;

import javax.persistence.*;
import java.util.Date;

@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_QUERY_FARE_TXN_LOG")
public class QueryFareTxnLog {

    public enum Source {
        DCP
    }

    public enum BookingJob {
        NON_BOOKING_JOB(0),
        BOOKING_JOB(1),
        FLAT_FARE_BOOKING_JOB(2);

        private Integer bookingJobCode;

        private BookingJob(Integer bookingJobCode) {
            this.bookingJobCode = bookingJobCode;
        }

        public Integer getBookingJobCode() {
            return bookingJobCode;
        }

        public static BookingJob getBookingJob(Integer bookingJobCode) {
            for (BookingJob bookingJob : values()) {
                if (bookingJob.getBookingJobCode().equals(bookingJobCode)) {
                    return bookingJob;
                }
            }
            return null;
        }

    }

    public enum PaymentMode {
        MASTERPASS(1),
        CABCHARGE(2),
        COF(3),
        <PERSON>Y<PERSON><PERSON>(4),
        NOF(5);

        private Integer paymentModeCode;

        private PaymentMode(Integer paymentModeCode) {
            this.paymentModeCode = paymentModeCode;
        }

        public Integer getPaymentModeCode() {
            return paymentModeCode;
        }

        public static PaymentMode getPaymentMode(Integer paymentModeCode) {
            for (PaymentMode paymentMode : values()) {
                if (paymentMode.getPaymentModeCode().equals(paymentModeCode)) {
                    return paymentMode;
                }
            }
            return null;
        }

    }

    public static final String MSG_TYPE_UNKNOWN = "unknown";
    public static final String MSG_TYPE_QUERY_FARE = "fareRequest";
    public static final String FIELD_ID = "id";
    public static final String FIELD_SOURCE = "source";
    public static final String FIELD_BOOKING_JOB = "booking_job";
    public static final String FIELD_CARD_NUMBER = "card_number";
    public static final String FIELD_PAYMENT_MODE = "payment_mode";
    public static final String FIELD_FARE_AMOUNT = "fare_amount";
    public static final String FIELD_REQUEST_ID = "request_id";
    public static final String FIELD_VEHICLE_NO = "vehicle_no";
    public static final String FIELD_DATE_TIME = "date_time";
    public static final String FIELD_RESP_CODE = "resp_code";

    private Long id;
    private Source source = Source.DCP;
    private String cardNumber;
    private BookingJob bookingJob = BookingJob.BOOKING_JOB;
    private PaymentMode paymentMode = PaymentMode.NOF;
    private String fareAmount;
    private String requestId;
    private String vehicleNo;
    private Date datetime;
    private String respCode;

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_QUERY_FARE_TXN_LOG")
    @SequenceGenerator(name = "SEQGEN_QUERY_FARE_TXN_LOG", sequenceName = "TMSQ_QUERY_FARE_TXN_LOG")
    @Column(name = FIELD_ID)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Enumerated(EnumType.STRING)
    @Column(name = FIELD_SOURCE, length = 32)
    public Source getSource() {
        return source;
    }

    public void setSource(Source source) {
        this.source = source;
    }

    @Column(name = FIELD_CARD_NUMBER, length = 20)
    public String getCardNumber() {
        return cardNumber;
    }

    public void setCardNumber(String cardNumber) {
        this.cardNumber = cardNumber;
    }

    @Enumerated(EnumType.STRING)
    @Column(name = FIELD_BOOKING_JOB, length = 32)
    public BookingJob getBookingJob() {
        return bookingJob;
    }

    public void setBookingJob(BookingJob bookingJob) {
        this.bookingJob = bookingJob;
    }

    @Enumerated(EnumType.STRING)
    @Column(name = FIELD_PAYMENT_MODE, length = 32)
    public PaymentMode getPaymentMode() {
        return paymentMode;
    }

    public void setPaymentMode(PaymentMode paymentMode) {
        this.paymentMode = paymentMode;
    }

    @Column(name = FIELD_FARE_AMOUNT, length = 6)
    public String getFareAmount() {
        return fareAmount;
    }

    public void setFareAmount(String fareAmount) {
        this.fareAmount = fareAmount;
    }

    @Column(name = FIELD_REQUEST_ID, length = 18, nullable = false)
    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    @Column(name = FIELD_VEHICLE_NO, length = 10)
    public String getVehicleNo() {
        return vehicleNo;
    }

    public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = FIELD_DATE_TIME, nullable = false)
    public Date getDatetime() {
        return datetime;
    }

    public void setDatetime(Date datetime) {
        this.datetime = datetime;
    }

    @Column(name = FIELD_RESP_CODE, length = 2)
    public String getRespCode() {
        return respCode;
    }

    public void setRespCode(String respCode) {
        this.respCode = respCode;
    }
}
