package com.abl.db.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * This is a view, getting terminals that has not started the downloading job
 * 
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "TMVW_TERM_BL_NOT_DOWNLOADED")
public class ViewTerminalsBlacklistNotDownloaded implements Serializable {
	private long id;
	private long terminalId;
	private String serialNo;
	private short uploadLog;
	private String modelName;
	private long modelId;
	private String vehicleId;

	@Id
	@Column(name = "id")
	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	@Column(name = "terminal_id")
	public long getTerminalId() {
		return terminalId;
	}

	public void setTerminalId(long terminalId) {
		this.terminalId = terminalId;
	}

	@Column(name = "serial_no")
	public String getSerialNo() {
		return serialNo;
	}

	public void setSerialNo(String serialNo) {
		this.serialNo = serialNo;
	}

	@Column(name = "upload_log")
	public short getUploadLog() {
		return uploadLog;
	}

	public void setUploadLog(short uploadLog) {
		this.uploadLog = uploadLog;
	}

	@Column(name = "model_name")
	public String getModelName() {
		return modelName;
	}

	public void setModelName(String modelName) {
		this.modelName = modelName;
	}

	@Column(name = "model_id")
	public long getModelId() {
		return modelId;
	}

	public void setModelId(long modelId) {
		this.modelId = modelId;
	}

	@Column(name = "vehicle_id")
	public String getVehicleId() {
		return vehicleId;
	}

	public void setVehicleId(String vehicleId) {
		this.vehicleId = vehicleId;
	}

}
