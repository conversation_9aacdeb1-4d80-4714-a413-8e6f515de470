package com.abl.db.model;

import org.apache.log4j.Logger;
import org.hibernate.LazyInitializationException;
import org.hibernate.annotations.Cascade;
import org.hibernate.annotations.ForeignKey;
import org.springframework.security.core.GrantedAuthority;

import javax.persistence.*;

import java.util.Date;
import java.util.Set;

/**
 * Access Profile contains a list of "pages".  If a user has this profile, then
 * he has access to these pages.
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_ACC_PROFILE")
public class AccessProfile implements GrantedAuthority {

    private static final Logger logger = Logger.getLogger(AccessProfile.class);

    private Long id;
    private String name;
    private Date createDate;
    private Date modifyDate;
    private Set<Page> pages;

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_ACCESS_PROFILE")
    @SequenceGenerator(name = "SEQGEN_ACCESS_PROFILE", sequenceName = "TMSQ_ACCESS_PROFILE")
    @Column(name = "id")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "name", nullable = false)
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Transient
    @Override
    public String getAuthority() {
        return String.valueOf(id);
    }

    @ManyToMany(targetEntity = Page.class, cascade = {CascadeType.ALL}, fetch = FetchType.LAZY)
    @Cascade(value = {org.hibernate.annotations.CascadeType.DELETE})
    @ForeignKey(name = "TMFC_ACC_PROFILE_ID_PAGE", inverseName = "TMFC_PAGE_NAME_ACC_PROFILE")
    public Set<Page> getPages() {
        return pages;
    }

    public void setPages(Set<Page> pages) {
        this.pages = pages;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "create_date")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "modify_date")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    public String toString() {
        StringBuffer buffer = new StringBuffer();
        try {
            for (Page page : pages) {
                if (buffer.length() > 0) {
                    buffer.append(",");
                }
                buffer.append(page.getName());
            }
        } catch (LazyInitializationException e) {
            logger.error(e);
            // ignore
        }
        return "AccessProfile(id=" + id + ", name=" + name + ", pages=" + buffer.toString() + ")";
    }
}
