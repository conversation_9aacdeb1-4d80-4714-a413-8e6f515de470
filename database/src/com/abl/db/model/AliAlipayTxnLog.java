package com.abl.db.model;

import org.hibernate.annotations.ForeignKey;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_ALI_ALIPAY_TXN_LOG")
public class AliAlipayTxnLog implements Serializable {
	

	public enum Status {
		NEW,
		SUCCESS,
		PENDING,
		FAIL
	}

	public enum Source {
		HOST,	// for outgoing msg to Alipay, created by aliHost
		BATCH,  // for outgoing msg to Alipay, created by aliBatch
	}
	
	public static final String MSG_TYPE_PREAUTH="order_freeze";
	public static final String MSG_TYPE_PREAUTH_CANCEL="order_unfreeze";
	public static final String MSG_TYPE_PREAUTH_QUERY="order_query";
	public static final String MSG_TYPE_PREAUTH_REVERSAL="order_cancel";
	public static final String MSG_TYPE_PAYMENT="trade_pay";
	public static final String MSG_TYPE_PAYMENT_QUERY="trade_pay_query";
	public static final String MSG_TYPE_UNKNOWN="unknown";
	
	private Long id;
	private Source source=Source.HOST;
	private Date dateTime;
	private String mti; // MTI of ISO msg
	private String procCode; // proc code of ISO msg
	private String txnAmt;	// amount in cents
	private String stan;			// P-11
	private String txnTime;			// P-12 txn time in request msg (hhmmss)
	private String txnDate;			// P-13 txn date in request msg (mmdd)
	private String tid;				// P-41
	private String mid;				// P-42
	private Status status = Status.NEW;
	private String txResult;
	private String responseCode;
	private String rrn;
	private String authCode;
	private String origAuthCode;
	private String msgType;
	private String posEntryMode;
	private String posCondCode;
	private String outOrderNo;
	private String outRequestNo;
	private String orderTitle;
	private String productCode;
	private String payTimeout;
	private String authNo;
	private String operationId;
	private String alipayStatus;
	private String payerUserId;
	private String payerLogonId;
	private String transCurrency;
	private String alipayCode; //code returned from alipay
	private String msg;
	private String alipaySubCode;
	private String subMsg;
	private String operationType;
	private Date gmtCreate;
	private Date gmtTrans;
	private String outTradeNo;
	private String tradeNo;
	private String tradeStatus;

	private AliTxnLog aliTxnLog;	// link to aliTxnLog
	
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_ALI_AP_TXN_LOG")
	@SequenceGenerator(name = "SEQGEN_ALI_AP_TXN_LOG", sequenceName = "TMSQ_ALI_ALIPAY_TXN_LOG")
	@Column(name = "id")
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "date_time")
	public Date getDateTime() {
		return dateTime;
	}

	public void setDateTime(Date dateTime) {
		this.dateTime = dateTime;
	}

	@Column(name = "txn_amount")
	public String getTxnAmt() {
		return txnAmt;
	}

	public void setTxnAmt(String txnAmt) {
		this.txnAmt = txnAmt;
	}
	
	@Enumerated(EnumType.STRING)
	@Column(name = "status", length=12, nullable=false)
	public Status getStatus() {
		return status;
	}

	public void setStatus(Status status) {
		this.status = status;
	}

	@Column(name = "tx_result", length=32)
	public String getTxResult() {
		return txResult;
	}

	public void setTxResult(String txResult) {
		this.txResult = txResult;
	}

	@Column(name = "msg_type", length=20)
	public String getMsgType() {
		return msgType;
	}

	public void setMsgType(String msgType) {
		this.msgType = msgType;
	}

	@Enumerated(EnumType.STRING)
	@Column(name = "source", length=32)
	public Source getSource() {
		return source;
	}

	public void setSource(Source source) {
		this.source = source;
	}

	@Column(name = "mti", length = 4)
	public String getMti() {
		return mti;
	}

	public void setMti(String mti) {
		this.mti = mti;
	}

	@Column(name = "proc_code", length = 6)
	public String getProcCode() {
		return procCode;
	}

	public void setProcCode(String procCode) {
		this.procCode = procCode;
	}

	@Column(name = "stan", length = 6)
	public String getStan() {
		return stan;
	}

	public void setStan(String stan) {
		this.stan = stan;
	}

	@Column(name = "txn_time", length=6)
	public String getTxnTime() {
		return txnTime;
	}

	public void setTxnTime(String txnTime) {
		this.txnTime = txnTime;
	}

	@Column(name = "txn_date", length=4)
	public String getTxnDate() {
		return txnDate;
	}

	public void setTxnDate(String txnDate) {
		this.txnDate = txnDate;
	}

	@Column(name = "tid", length = 8)
	public String getTid() {
		return tid;
	}

	public void setTid(String tid) {
		this.tid = tid;
	}

	@Column(name = "mid", length = 15)
	public String getMid() {
		return mid;
	}

	public void setMid(String mid) {
		this.mid = mid;
	}

	@Column(name = "resp_code", length = 2)
	public String getResponseCode() {
		return responseCode;
	}

	public void setResponseCode(String responseCode) {
		this.responseCode = responseCode;
	}

	@Column(name = "rrn", length=32)
	public String getRrn() {
		return rrn;
	}

	public void setRrn(String rrn) {
		this.rrn = rrn;
	}

	@Column(name = "auth_code", length=16)
	public String getAuthCode() {
		return authCode;
	}

	public void setAuthCode(String authCode) {
		this.authCode = authCode;
	}

	@Column(name = "orig_auth_code", length=16)
	public String getOrigAuthCode() {
		return origAuthCode;
	}

	public void setOrigAuthCode(String origAuthCode) {
		this.origAuthCode = origAuthCode;
	}

	@Column(name = "pos_entry_mode", length=8)
	public String getPosEntryMode() {
		return posEntryMode;
	}

	public void setPosEntryMode(String posEntryMode) {
		this.posEntryMode = posEntryMode;
	}

	@Column(name = "pos_cond_code", length=8)
	public String getPosCondCode() {
		return posCondCode;
	}

	public void setPosCondCode(String posCondCode) {
		this.posCondCode = posCondCode;
	}

	@Column(name = "out_order_no", length=50)
	public String getOutOrderNo() {
		return outOrderNo;
	}

	public void setOutOrderNo(String outOrderNo) {
		this.outOrderNo = outOrderNo;
	}

	@Column(name = "out_request_no", length=50)
	public String getOutRequestNo() {
		return outRequestNo;
	}

	public void setOutRequestNo(String outRequestNo) {
		this.outRequestNo = outRequestNo;
	}

	@Column(name = "order_title", length=100)
	public String getOrderTitle() {
		return orderTitle;
	}

	public void setOrderTitle(String orderTitle) {
		this.orderTitle = orderTitle;
	}

	@Column(name = "product_code", length=50)
	public String getProductCode() {
		return productCode;
	}

	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}

	@Column(name = "pay_timeout", length=10)
	public String getPayTimeout() {
		return payTimeout;
	}

	public void setPayTimeout(String payTimeout) {
		this.payTimeout = payTimeout;
	}

	@Column(name = "auth_no", length=80)
	public String getAuthNo() {
		return authNo;
	}

	public void setAuthNo(String authNo) {
		this.authNo = authNo;
	}

	@Column(name = "operation_id", length=80)
	public String getOperationId() {
		return operationId;
	}

	public void setOperationId(String operationId) {
		this.operationId = operationId;
	}

	@Column(name = "alipay_status", length=30)
	public String getAlipayStatus() {
		return alipayStatus;
	}

	public void setAlipayStatus(String alipayStatus) {
		this.alipayStatus = alipayStatus;
	}

	@Column(name = "payer_user_id", length=50)
	public String getPayerUserId() {
		return payerUserId;
	}

	public void setPayerUserId(String payerUserId) {
		this.payerUserId = payerUserId;
	}

	@Column(name = "payer_logon_id", length=100)
	public String getPayerLogonId() {
		return payerLogonId;
	}

	public void setPayerLogonId(String payerLogonId) {
		this.payerLogonId = payerLogonId;
	}

	@Column(name = "trans_currency", length=10)
	public String getTransCurrency() {
		return transCurrency;
	}

	public void setTransCurrency(String transCurrency) {
		this.transCurrency = transCurrency;
	}

	@Column(name = "alipay_code", length=30)
	public String getAlipayCode() {
		return alipayCode;
	}

	public void setAlipayCode(String alipayCode) {
		this.alipayCode = alipayCode;
	}

	@Column(name = "msg", length=50)
	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	@Column(name = "alipay_sub_code", length=30)
	public String getAlipaySubCode() {
		return alipaySubCode;
	}

	public void setAlipaySubCode(String alipaySubCode) {
		this.alipaySubCode = alipaySubCode;
	}

	@Column(name = "sub_msg", length=100)
	public String getSubMsg() {
		return subMsg;
	}

	public void setSubMsg(String subMsg) {
		this.subMsg = subMsg;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ali_txn_log_id")
	@ForeignKey(name="TMFC_ALI_ALP_TXN_LOG__TXNLOGID")
	public AliTxnLog getAliTxnLog() {
		return aliTxnLog;
	}

	public void setAliTxnLog(AliTxnLog aliTxnLog) {
		this.aliTxnLog = aliTxnLog;
	}

	@Column(name = "operation_type", length=50)
	public String getOperationType() {
		return operationType;
	}

	public void setOperationType(String operationType) {
		this.operationType = operationType;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "gmt_create")
	public Date getGmtCreate() {
		return gmtCreate;
	}

	public void setGmtCreate(Date gmtCreate) {
		this.gmtCreate = gmtCreate;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "gmt_trans")
	public Date getGmtTrans() {
		return gmtTrans;
	}

	public void setGmtTrans(Date gmtTrans) {
		this.gmtTrans = gmtTrans;
	}

	@Column(name = "out_trade_no", length=80)
	public String getOutTradeNo() {
		return outTradeNo;
	}

	public void setOutTradeNo(String outTradeNo) {
		this.outTradeNo = outTradeNo;
	}

	@Column(name = "trade_no", length=80)
	public String getTradeNo() {
		return tradeNo;
	}

	public void setTradeNo(String tradeNo) {
		this.tradeNo = tradeNo;
	}

	@Column(name = "trade_status", length=30)
	public String getTradeStatus() {
		return tradeStatus;
	}

	public void setTradeStatus(String tradeStatus) {
		this.tradeStatus = tradeStatus;
	}
}
