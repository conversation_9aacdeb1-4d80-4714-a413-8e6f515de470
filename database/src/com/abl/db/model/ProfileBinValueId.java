package com.abl.db.model;

import javax.persistence.Embeddable;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;

import org.hibernate.annotations.ForeignKey;

import java.io.Serializable;

@SuppressWarnings("serial")
@Embeddable
public class ProfileBinValueId implements Serializable {

    private ProfileBinRange profileBinRange;
    private ParameterDefinition parameterDefinition;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @ForeignKey(name="TMFC_PROFBR_ID_PROFBV") 
    public ProfileBinRange getProfileBinRange() {
        return profileBinRange;
    }

    public void setProfileBinRange(ProfileBinRange profileBinRange) {
        this.profileBinRange = profileBinRange;
    }

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @ForeignKey(name="TMFC_PARAM_DEF_ID_PROFBV") 
    public ParameterDefinition getParameterDefinition() {
        return parameterDefinition;
    }

    public void setParameterDefinition(ParameterDefinition parameterDefinition) {
        this.parameterDefinition = parameterDefinition;
    }

    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;

        ProfileBinValueId that = (ProfileBinValueId) o;

        if (profileBinRange != null ? !profileBinRange
                .equals(that.profileBinRange) : that.profileBinRange != null)
            return false;
        if (parameterDefinition != null ? !parameterDefinition.equals(that.parameterDefinition)
                : that.parameterDefinition != null)
            return false;

        return true;
    }

    public int hashCode() {
        int result;
        result = (profileBinRange != null ? profileBinRange.hashCode() : 0);
        result = 31 * result
                + (parameterDefinition != null ? parameterDefinition.hashCode() : 0);
        return result;
    }

}
