package com.abl.db.model;

import javax.persistence.*;

import org.hibernate.annotations.ForeignKey;

import java.io.Serializable;
import java.util.Date;

/**
 * Contains log entries of using webadmin
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_ADMIN_LOG")
public class AdminLog implements Serializable {

    private Long id;
    private String user;
    private Page page;
    private Date logDateTime;
    private Boolean success = true;
    private String messageCode;
    private String[] messageParams;
    private String oldData;
    private String newData;
    private Domain domain = null;        // associates this log entry with domain
    // if null, it is not assoc with any domain

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_ADMIN_LOG")
    @SequenceGenerator(name = "SEQGEN_ADMIN_LOG", sequenceName = "TMSQ_ADMIN_LOG")
    @Column(name = "id")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "user_name", length = 32)
    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "page_name")
    @ForeignKey(name="TMFC_PAGE_NAME_ADMIN_LOG") 
    public Page getPage() {
        return page;
    }

    public void setPage(Page page) {
        this.page = page;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "log_date_time", nullable = false)
    public Date getLogDateTime() {
        return logDateTime;
    }

    public void setLogDateTime(Date logDateTime) {
        this.logDateTime = logDateTime;
    }

    @Column(name = "success", nullable = false)
    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    @Column(name = "message_code")
    public String getMessageCode() {
        return messageCode;
    }

    public void setMessageCode(String messageCode) {
        this.messageCode = messageCode;
    }

    @Column(name = "message_params", length = 500)
    public String[] getMessageParams() {
        return messageParams;
    }

    public void setMessageParams(String[] messageParams) {
        this.messageParams = messageParams;
    }

    @Column(name = "old_data")
    public String getOldData() {
        return oldData;
    }

    public void setOldData(String oldData) {
        this.oldData = oldData;
    }

    @Column(name = "new_data", length=500)
    public String getNewData() {
        return newData;
    }

    public void setNewData(String newData) {
        this.newData = newData;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "domain_id")
    @ForeignKey(name="TMFC_DOMAIN_ID_ADMIN_LOG") 
    public Domain getDomain() {
        return domain;
    }

    public void setDomain(Domain domain) {
        this.domain = domain;
    }


}
