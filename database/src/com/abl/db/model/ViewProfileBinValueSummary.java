package com.abl.db.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * This is a view, getting all profile bin value info
 *
 */


@SuppressWarnings("serial")
@Entity
@Table(name = "TMVW_PROFILE_BIN_VALUE_SUMMARY")
public class ViewProfileBinValueSummary {
	private long id;
	private long terminalProfileId;
	private long binRangeId;
	private long appId;
	private String panHigh;
	private int panHighType;
	private String panHighAddionalInfo;
	private String panLow;
	private int panLowType;
	private String panLowAddionalInfo;
	private String binParamName;
	private String binAdditionalInfo;
	private String binParamDesc;
	private String binValue;
	private long paramDefinitionId;
	private int type;
	
	@Id
	public long getId() {
		return id;
	}
	public void setId(long id) {
		this.id = id;
	}
		
	@Column(name = "terminal_profile_id")
	public long getTerminalProfileId() {
		return terminalProfileId;
	}
	public void setTerminalProfileId(long terminalProfileId) {
		this.terminalProfileId = terminalProfileId;
	}
	@Column(name = "profile_bin_range_id")
	public long getBinRangeId() {
		return binRangeId;
	}
	public void setBinRangeId(long binRangeId) {
		this.binRangeId = binRangeId;
	}
	
	@Column(name = "pan_high")
	public String getPanHigh() {
		return panHigh;
	}
	public void setPanHigh(String panHigh) {
		this.panHigh = panHigh;
	}
	
	@Column(name = "pan_high_type")
	public int getPanHighType() {
		return panHighType;
	}
	public void setPanHighType(int panHighType) {
		this.panHighType = panHighType;
	}
	
	@Column(name = "pan_high_info")
	public String getPanHighAddionalInfo() {
		return panHighAddionalInfo;
	}
	public void setPanHighAddionalInfo(String panHighAddionalInfo) {
		this.panHighAddionalInfo = panHighAddionalInfo;
	}
	
	@Column(name = "pan_low")
	public String getPanLow() {
		return panLow;
	}
	public void setPanLow(String panLow) {
		this.panLow = panLow;
	}
	
	@Column(name = "pan_low_type")
	public int getPanLowType() {
		return panLowType;
	}
	public void setPanLowType(int panLowType) {
		this.panLowType = panLowType;
	}
	
	@Column(name = "pan_low_info")
	public String getPanLowAddionalInfo() {
		return panLowAddionalInfo;
	}
	public void setPanLowAddionalInfo(String panLowAddionalInfo) {
		this.panLowAddionalInfo = panLowAddionalInfo;
	}
	
	@Column(name = "bin_param_name")
	public String getBinParamName() {
		return binParamName;
	}
	public void setBinParamName(String binParamName) {
		this.binParamName = binParamName;
	}
	
	@Column(name = "bin_info")
	public String getBinAdditionalInfo() {
		return binAdditionalInfo;
	}
	public void setBinAdditionalInfo(String binAdditionalInfo) {
		this.binAdditionalInfo = binAdditionalInfo;
	}
	
	@Column(name = "bin_description")
	public String getBinParamDesc() {
		return binParamDesc;
	}
	public void setBinParamDesc(String binParamDesc) {
		this.binParamDesc = binParamDesc;
	}
	@Column(name = "bin_value")
	public String getBinValue() {
		return binValue;
	}
	public void setBinValue(String binValue) {
		this.binValue = binValue;
	}
	
	@Column(name = "app_id")
	public long getAppId() {
		return appId;
	}
	public void setAppId(long appId) {
		this.appId = appId;
	}
	
	@Column(name = "param_definition_id")
	public long getParamDefinitionId() {
		return paramDefinitionId;
	}
	public void setParamDefinitionId(long paramDefinitionId) {
		this.paramDefinitionId = paramDefinitionId;
	}
	
	@Column(name="type")
	public int getType() {
		return type;
	}
	public void setType(int type) {
		this.type = type;
	}
}
