package com.abl.db.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * This is a view, getting group release history summary with the model info
 *
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "TMVW_GROUP_REL_HISTORY_SUMMARY")
public class ViewGroupReleaseHistorySummary {
	private long id;
	private long groupId;
	private long releaseId;
	private String groupName;
	private String releaseDesc;
	private String releaseVersion;
	private String modelName;
	private String padReleaseVersion;
	
	@Id
	@Column(name="id")
	public long getId() {
		return id;
	}
	public void setId(long id) {
		this.id = id;
	}
	
	@Column(name="group_id")
	public long getGroupId() {
		return groupId;
	}
	public void setGroupId(long groupId) {
		this.groupId = groupId;
	}
	@Column(name="release_id")
	public long getReleaseId() {
		return releaseId;
	}
	public void setReleaseId(long releaseId) {
		this.releaseId = releaseId;
	}
	@Column(name="group_name")
	public String getGroupName() {
		return groupName;
	}
	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}
	
	@Column(name="release_desc")
	public String getReleaseDesc() {
		return releaseDesc;
	}
	public void setReleaseDesc(String releaseDesc) {
		this.releaseDesc = releaseDesc;
	}
	
	@Column(name="release_version")
	public String getReleaseVersion() {
		return releaseVersion;
	}
	public void setReleaseVersion(String releaseVersion) {
		this.releaseVersion = releaseVersion;
	}
	
	@Column(name="model_name")
	public String getModelName() {
		return modelName;
	}
	public void setModelName(String modelName) {
		this.modelName = modelName;
	}
	
	@Transient
	public String getPadReleaseVersion() {
		return String.format("%06d", Integer.parseInt(getReleaseVersion()));
	}

}
