package com.abl.db.model;

import javax.persistence.*;

import org.hibernate.annotations.ForeignKey;

import java.io.Serializable;

@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_RELEASE_FILE")
public class ReleaseFile implements Serializable {

    private long id;
    private int recordNo;
    private String data;
    private Release release;

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_RELEASE_FILE")
    @SequenceGenerator(name = "SEQGEN_RELEASE_FILE", sequenceName = "TMSQ_RELEASE_FILE")
    @Column(name = "id")
    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    @Column(name = "record_no", nullable = false)
    public int getRecordNo() {
        return recordNo;
    }

    public void setRecordNo(int recordNo) {
        this.recordNo = recordNo;
    }

    @Column(name = "data", nullable = false, length = 4000)
    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "release_id")
    @ForeignKey(name="TMFC_REL_ID_REL_FILE") 
    public Release getRelease() {
        return release;
    }

    public void setRelease(Release release) {
        this.release = release;
    }

}
