package com.abl.db.model;

import javax.persistence.*;
import java.io.Serializable;

@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_APPLICATION_FEE_VALUE")
@AssociationOverrides({
        @AssociationOverride(name = "pk.applicationFee", joinColumns = @JoinColumn(name = "application_fee_id")),
        @AssociationOverride(name = "pk.parameterDefinition", joinColumns = @JoinColumn(name = "parameter_definition_id"))})
public class ApplicationFeeValue implements Serializable {

    private ApplicationFeeValueId pk = new ApplicationFeeValueId();
    private String value;

    @EmbeddedId
    public ApplicationFeeValueId getPk() {
        return pk;
    }

    public void setPk(ApplicationFeeValueId pk) {
        this.pk = pk;
    }

    @Column(name = "fee_value", nullable = true, length = 200)
    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }


    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;

        ApplicationFeeValue that = (ApplicationFeeValue) o;

        if (getPk() != null ? !getPk().equals(that.getPk())
                : that.getPk() != null)
            return false;

        return true;
    }

    public int hashCode() {
        return (getPk() != null ? getPk().hashCode() : 0);
    }

}
