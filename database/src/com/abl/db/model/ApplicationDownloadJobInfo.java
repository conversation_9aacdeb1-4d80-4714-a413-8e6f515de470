package com.abl.db.model;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "TMTB_APP_DOWNLOAD_JOB_INFO")
public class ApplicationDownloadJobInfo implements Serializable {
    private ApplicationDownloadJobInfoPk pk;
    private Date startDate;

    @EmbeddedId
    public ApplicationDownloadJobInfoPk getPk() {
        return pk;
    }

    public void setPk(ApplicationDownloadJobInfoPk pk) {
        this.pk = pk;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "start_date", nullable = true)
    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }
}
