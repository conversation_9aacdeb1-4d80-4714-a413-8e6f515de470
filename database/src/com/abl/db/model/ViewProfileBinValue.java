package com.abl.db.model;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Column;
import javax.persistence.Id;

/**
 * This is a view, getting profile bin view
 *
 */

@SuppressWarnings("serial")
@Entity
@Table(name = "TMVW_PROFILE_BIN_VALUE_VIEW")
public class ViewProfileBinValue {
	
	private long id;
	private long profileBinRangeId;
	private long parameterDefinitionId;
	private String value;
	
	
	@Id
	public long getId() {
		return id;
	}
	
	public void setId(long id) {
		this.id = id;
	}
	
	@Column(name="profile_bin_range_id")
	public long getProfileBinRangeId() {
		return profileBinRangeId;
	}

	public void setProfileBinRangeId(long profileBinRangeId) {
		this.profileBinRangeId = profileBinRangeId;
	}

	@Column(name="parameter_definition_id")
	public long getParameterDefinitionId() {
		return parameterDefinitionId;
	}
	public void setParameterDefinitionId(long parameterDefinitionId) {
		this.parameterDefinitionId = parameterDefinitionId;
	}
	
	@Column(name="value")
	public String getValue() {
		return value;
	}
	public void setValue(String value) {
		this.value = value;
	}
	
	

}
