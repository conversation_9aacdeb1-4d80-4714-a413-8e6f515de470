package com.abl.db.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import java.io.Serializable;
import java.util.Date;

@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_ALIPAY_HOST_TXN_LOG")
public class AlipayHostTxnLog implements Serializable {
	private Long id;
	private String amount;
	private Date createDt;
	private String alipayReqMsg;
	private String alipayRespMsg;
	private String alipayRefNo;
	private String alipayRespResult;
	private String driverId;
	private String jobNumber;
	private String messageType;
	private String serialNo;
	private String transactionId;
	private String transmitStatus;
	private String vehicleId;

	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_ALIPAY_HOST_TXN_LOG")
	@SequenceGenerator(name = "SEQGEN_ALIPAY_HOST_TXN_LOG", sequenceName = "TMSQ_ALIPAY_HOST_TXN_LOG")
	@Column(name = "id")
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "amount", length = 12, nullable = false)
	public String getAmount() {
		return amount;
	}

	public void setAmount(String amount) {
		this.amount = amount;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "create_dt", nullable = false)
	public Date getCreateDt() {
		return createDt;
	}

	public void setCreateDt(Date createDt) {
		this.createDt = createDt;
	}

	@Column(name = "transaction_id", length = 30)
	public String getTransactionId() {
		return transactionId;
	}

	public void setTransactionId(String transactionId) {
		this.transactionId = transactionId;
	}

	@Column(name = "vehicle_id", length = 15)
	public String getVehicleId() {
		return vehicleId;
	}

	public void setVehicleId(String vehicleId) {
		this.vehicleId = vehicleId;
	}

	@Column(name = "transmit_status", length = 50)
	public String getTransmitStatus() {
		return transmitStatus;
	}

	public void setTransmitStatus(String transmitStatus) {
		this.transmitStatus = transmitStatus;
	}

	@Column(name = "serial_no", length = 20)
	public String getSerialNo() {
		return serialNo;
	}

	public void setSerialNo(String serialNo) {
		this.serialNo = serialNo;
	}

	@Column(name = "job_number", length = 10)
	public String getJobNumber() {
		return jobNumber;
	}

	public void setJobNumber(String jobNumber) {
		this.jobNumber = jobNumber;
	}

	@Column(name = "driver_id", length = 9)
	public String getDriverId() {
		return driverId;
	}

	public void setDriverId(String driverId) {
		this.driverId = driverId;
	}

	@Column(name = "alipay_request_message ", length = 2048)
	public String getAlipayReqMsg() {
		return alipayReqMsg;
	}

	public void setAlipayReqMsg(String alipayReqMsg) {
		this.alipayReqMsg = alipayReqMsg;
	}

	@Column(name = "alipay_response_message ", length = 2048)
	public String getAlipayRespMsg() {
		return alipayRespMsg;
	}

	public void setAlipayRespMsg(String alipayRespMsg) {
		this.alipayRespMsg = alipayRespMsg;
	}

	@Column(name = "alipay_reference_number  ", length = 30)
	public String getAlipayRefNo() {
		return alipayRefNo;
	}

	public void setAlipayRefNo(String alipayRefNo) {
		this.alipayRefNo = alipayRefNo;
	}

	@Column(name = "alipay_response_result", length = 500)
	public String getAlipayRespResult() {
		return alipayRespResult;
	}

	public void setAlipayRespResult(String alipayRespResult) {
		this.alipayRespResult = alipayRespResult;
	}

	@Column(name = "message_type", length = 30)
	public String getMessageType() {
		return messageType;
	}

	public void setMessageType(String messageType) {
		this.messageType = messageType;
	}

	
}
