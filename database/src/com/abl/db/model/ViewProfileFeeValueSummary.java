package com.abl.db.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * This is a view, getting all profile fee value info
 *
 */


@SuppressWarnings("serial")
@Entity
@Table(name = "TMVW_PROFILE_FEE_VALUE_SUMMARY")
public class ViewProfileFeeValueSummary {
	private long id;
	private long terminalProfileId;
	private long feeId;
	private long appId;
	private String desc;
	private int descType;
	private String descAddionalInfo;
	
	private String feeParamName;
	private String feeAdditionalInfo;
	private String feeParamDesc;
	private String feeValue;
	private long paramDefinitionId;
	private int type;
	
	@Id
	public long getId() {
		return id;
	}
	public void setId(long id) {
		this.id = id;
	}
		
	@Column(name = "terminal_profile_id")
	public long getTerminalProfileId() {
		return terminalProfileId;
	}
	public void setTerminalProfileId(long terminalProfileId) {
		this.terminalProfileId = terminalProfileId;
	}
	
	@Column(name = "app_id")
	public long getAppId() {
		return appId;
	}
	public void setAppId(long appId) {
		this.appId = appId;
	}
	
	@Column(name = "param_definition_id")
	public long getParamDefinitionId() {
		return paramDefinitionId;
	}
	public void setParamDefinitionId(long paramDefinitionId) {
		this.paramDefinitionId = paramDefinitionId;
	}
	
	@Column(name="type")
	public int getType() {
		return type;
	}
	public void setType(int type) {
		this.type = type;
	}
	
	@Column(name = "fee_id")
	public long getFeeId() {
		return feeId;
	}
	public void setFeeId(long feeId) {
		this.feeId = feeId;
	}
	
	@Column(name = "description")
	public String getDesc() {
		return desc;
	}
	public void setDesc(String desc) {
		this.desc = desc;
	}
	
	@Column(name = "desc_type")
	public int getDescType() {
		return descType;
	}
	public void setDescType(int descType) {
		this.descType = descType;
	}
	@Column(name = "desc_info")
	public String getDescAddionalInfo() {
		return descAddionalInfo;
	}
	public void setDescAddionalInfo(String descAddionalInfo) {
		this.descAddionalInfo = descAddionalInfo;
	}
	
	@Column(name = "fee_param_name")
	public String getFeeParamName() {
		return feeParamName;
	}
	public void setFeeParamName(String feeParamName) {
		this.feeParamName = feeParamName;
	}
	@Column(name = "fee_info")
	public String getFeeAdditionalInfo() {
		return feeAdditionalInfo;
	}
	public void setFeeAdditionalInfo(String feeAdditionalInfo) {
		this.feeAdditionalInfo = feeAdditionalInfo;
	}
	
	@Column(name = "fee_description")
	public String getFeeParamDesc() {
		return feeParamDesc;
	}
	public void setFeeParamDesc(String feeParamDesc) {
		this.feeParamDesc = feeParamDesc;
	}
	
	@Column(name = "fee_value")
	public String getFeeValue() {
		return feeValue;
	}
	public void setFeeValue(String feeValue) {
		this.feeValue = feeValue;
	}
}
