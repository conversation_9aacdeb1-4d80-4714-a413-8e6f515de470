package com.abl.db.model;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Column;
import javax.persistence.Id;

/**
 * This is a view, getting profile fee view
 *
 */

@SuppressWarnings("serial")
@Entity
@Table(name = "TMVW_PROFILE_FEE_VALUE_VIEW")
public class ViewProfileFeeValue {
	
	private long id;
	private long profileFeeId;
	private long parameterDefinitionId;
	private String value;
	
	
	@Id
	public long getId() {
		return id;
	}
	
	public void setId(long id) {
		this.id = id;
	}
	
	@Column(name="profile_fee_id")
	public long getProfileFeeId() {
		return profileFeeId;
	}

	public void setProfileFeeId(long profileFeeId) {
		this.profileFeeId = profileFeeId;
	}

	@Column(name="parameter_definition_id")
	public long getParameterDefinitionId() {
		return parameterDefinitionId;
	}
	public void setParameterDefinitionId(long parameterDefinitionId) {
		this.parameterDefinitionId = parameterDefinitionId;
	}
	
	@Column(name="value")
	public String getValue() {
		return value;
	}
	public void setValue(String value) {
		this.value = value;
	}
	
	

}
