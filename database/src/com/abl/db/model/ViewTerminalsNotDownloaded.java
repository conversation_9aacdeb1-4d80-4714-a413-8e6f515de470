package com.abl.db.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * This is a view, getting terminals that has not started the downloading job
 * 
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "TMVW_TERMINAL_NOT_DOWNLOADED")
public class ViewTerminalsNotDownloaded implements Serializable {
	private long terminalId;
	private String serialNo;
	private long releaseId;
	private long groupId;
	private String vehicleId;

	@Id
	@Column(name = "terminal_id")
	public long getTerminalId() {
		return terminalId;
	}

	public void setTerminalId(long terminalId) {
		this.terminalId = terminalId;
	}

	@Column(name = "serial_no")
	public String getSerialNo() {
		return serialNo;
	}

	public void setSerialNo(String serialNo) {
		this.serialNo = serialNo;
	}


	@Column(name = "release_id")
	public long getReleaseId() {
		return releaseId;
	}

	public void setReleaseId(long releaseId) {
		this.releaseId = releaseId;
	}


	@Column(name = "group_id")
	public long getGroupId() {
		return groupId;
	}

	public void setGroupId(long groupId) {
		this.groupId = groupId;
	}


	@Column(name = "vehicle_id")
	public String getVehicleId() {
		return vehicleId;
	}

	public void setVehicleId(String vehicleId) {
		this.vehicleId = vehicleId;
	}
	

}
