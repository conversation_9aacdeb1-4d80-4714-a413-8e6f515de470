package com.abl.db.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * This is a view, getting terminals that has completed/pending downloading job
 *
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "TMVW_TERM_BL_WITH_DOWNLOAD_JOB")
public class ViewTerminalBlacklistWithDownloadJob {

	private long terminalId;
	private long blacklistVersionId;
	private String vehicleId;
	private String serialNo;
	private String modelName;
	private String modelDescription;
	private long blacklistDownloadId;
	private int terminalBlacklistDownloadStatus;
    private int recordNo;
    private Date startTime;
    private Date lastDownloaded;
//	private int fileSize;
    private int totalRecordNo;
    



	@Id
	@Column(name="terminal_id")
	public long getTerminalId() {
		return terminalId;
	}
	public void setTerminalId(long terminalId) {
		this.terminalId = terminalId;
	}
	
	
	@Column(name="serial_no")
	public String getSerialNo() {
		return serialNo;
	}
	public void setSerialNo(String serialNo) {
		this.serialNo = serialNo;
	}
	
	@Column(name="model")
	public String getModelName() {
		return modelName;
	}
	public void setModelName(String modelName) {
		this.modelName = modelName;
	}
	
	@Column(name="model_description")
	public String getModelDescription() {
		return modelDescription;
	}
	public void setModelDescription(String modelDescription) {
		this.modelDescription = modelDescription;
	}
//	
//	
//	@Column(name="file_size")
//	public int getFileSize() {
//		return fileSize;
//	}
//	public void setFileSize(int fileSize) {
//		this.fileSize = fileSize;
//	}
	
	@Column(name="blacklist_download_id")
	public long getBlacklistDownloadId() {
		return blacklistDownloadId;
	}
	public void setBlacklistDownloadId(long blacklistDownloadId) {
		this.blacklistDownloadId = blacklistDownloadId;
	}
	
	@Column(name="terminal_bl_download_status")
	public int getTerminalBlacklistDownloadStatus() {
		return terminalBlacklistDownloadStatus;
	}
	public void setTerminalBlacklistDownloadStatus(
			int terminalBlacklistDownloadStatus) {
		this.terminalBlacklistDownloadStatus = terminalBlacklistDownloadStatus;
	}
	
    @Column(name = "record_no")
    public int getRecordNo() {
        return recordNo;
    }

    public void setRecordNo(int recordNo) {
        this.recordNo = recordNo;
    }
    
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "start_time", nullable = false) 
	public Date getStartTime() {
		return startTime;
	}
	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
    @Column(name = "last_downloaded", nullable = false) 
	public Date getLastDownloaded() {
		return lastDownloaded;
	}
	public void setLastDownloaded(Date lastDownloaded) {
		this.lastDownloaded = lastDownloaded;
	}
	@Column(name = "vehicle_id")
	public String getVehicleId() {
		return vehicleId;
	}
	public void setVehicleId(String vehicleId) {
		this.vehicleId = vehicleId;
	} 

    @Column(name = "total_record_no")
    public int getTotalRecordNo() {
        return totalRecordNo;
    }

    public void setTotalRecordNo(int totalRecordNo) {
        this.totalRecordNo = totalRecordNo;
    }
    
    @Column(name = "blacklist_version_id")
	public long getBlacklistVersionId() {
		return blacklistVersionId;
	}
	public void setBlacklistVersionId(long blacklistVersionId) {
		this.blacklistVersionId = blacklistVersionId;
	}


}
