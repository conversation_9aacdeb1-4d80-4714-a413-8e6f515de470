package com.abl.db.model;

import javax.persistence.*;

import org.hibernate.annotations.ForeignKey;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_APPLICATION")
public class Application implements Serializable {

    private long id;
    private String name;
    private int version;
    private String description;
    private String padVersion;

    private Set<TerminalApplication> terminalApplication = new HashSet<TerminalApplication>(
            0);

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_APPLICATION")
    @SequenceGenerator(name = "SEQGEN_APPLICATION", sequenceName = "TMSQ_APPLICATION")
    @Column(name = "id")
    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    @Column(name = "name", nullable = false, length = 10)
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Column(name = "version", nullable = false, length = 50)
    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    @Column(name = "description", length = 200)
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "pk.application")
    @ForeignKey(name="TMFC_APP_ID_TMTB_TERM_APP") 
    public Set<TerminalApplication> getTerminalApplication() {
        return terminalApplication;
    }

    public void setTerminalApplication(
            Set<TerminalApplication> terminalApplication) {
        this.terminalApplication = terminalApplication;
    }

    @Transient
    public String getPadVersion() {
        return String.format("%06d", getVersion());
    }

    public void setPadVersion(String padVersion) {
        this.padVersion = padVersion;
    }

}
