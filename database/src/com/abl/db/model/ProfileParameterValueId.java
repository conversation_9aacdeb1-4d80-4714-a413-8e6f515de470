package com.abl.db.model;

import javax.persistence.Embeddable;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;

import org.hibernate.annotations.ForeignKey;

import java.io.Serializable;

@SuppressWarnings("serial")
@Embeddable
public class ProfileParameterValueId implements Serializable {

    private TerminalProfile terminalProfile;
    private ParameterDefinition parameterDefinition;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @ForeignKey(name="TMFC_TERM_PRO_ID_PRO_PARAM_VAL") 
    public TerminalProfile getTerminalProfile() {
        return terminalProfile;
    }

    public void setTerminalProfile(TerminalProfile terminalProfile) {
        this.terminalProfile = terminalProfile;
    }

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    public ParameterDefinition getParameterDefinition() {
        return parameterDefinition;
    }

    public void setParameterDefinition(ParameterDefinition parameterDefinition) {
        this.parameterDefinition = parameterDefinition;
    }

    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;

        ProfileParameterValueId that = (ProfileParameterValueId) o;

        if (terminalProfile != null ? !terminalProfile
                .equals(that.terminalProfile) : that.terminalProfile != null)
            return false;
        if (parameterDefinition != null ? !parameterDefinition
                .equals(that.parameterDefinition) : that.parameterDefinition != null)
            return false;

        return true;
    }

    public int hashCode() {
        int result;
        result = (terminalProfile != null ? terminalProfile.hashCode() : 0);
        result = 31 * result
                + (parameterDefinition != null ? parameterDefinition.hashCode() : 0);
        return result;
    }

}
