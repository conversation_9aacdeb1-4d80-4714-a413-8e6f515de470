package com.abl.db.model;

import javax.persistence.Embeddable;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;

import org.hibernate.annotations.ForeignKey;

import java.io.Serializable;

@SuppressWarnings("serial")
@Embeddable
public class TerminalBlacklistVersionId implements Serializable {

    private TmsTerminal tmsTerminal;
    private BlacklistVersion blacklistVersion;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @ForeignKey(name="TMFC_TERM_ID_TERM_BL_VER")
    public TmsTerminal getTmsTerminal() {
        return tmsTerminal;
    }

    public void setTmsTerminal(TmsTerminal tmsTerminal) {
        this.tmsTerminal = tmsTerminal;
    }

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @ForeignKey(name="TMFC_BL_VER_ID_TERM_BL_VER")
    public BlacklistVersion getBlacklistVersion() {
        return blacklistVersion;
    }

    public void setBlacklistVersion(BlacklistVersion blacklistVersion) {
        this.blacklistVersion = blacklistVersion;
    }

    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;

        TerminalBlacklistVersionId that = (TerminalBlacklistVersionId) o;

        if (tmsTerminal != null ? !tmsTerminal.equals(that.tmsTerminal)
                : that.tmsTerminal != null)
            return false;
        if (blacklistVersion != null ? !blacklistVersion.equals(that.blacklistVersion)
                : that.blacklistVersion != null)
            return false;

        return true;
    }

    public int hashCode() {
        int result;
        result = (tmsTerminal != null ? tmsTerminal.hashCode() : 0);
        result = 31 * result
                + (blacklistVersion != null ? blacklistVersion.hashCode() : 0);
        return result;
    }

}
