package com.abl.db.model;

import org.hibernate.annotations.ForeignKey;

import javax.persistence.Embeddable;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import java.io.Serializable;

@Embeddable
public class ApplicationDownloadJobInfoPk implements Serializable {
    private ApplicationDownloadJob applicationDownloadJob;

    public ApplicationDownloadJobInfoPk() {
    }

    public ApplicationDownloadJobInfoPk(ApplicationDownloadJob applicationDownloadJob) {
        this.applicationDownloadJob = applicationDownloadJob;
    }

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "application_download_job_id")
    @ForeignKey(name="TMFC_APP_DOWNLOAD_JOB_INFO")
    public ApplicationDownloadJob getApplicationDownloadJob() {
        return applicationDownloadJob;
    }

    public void setApplicationDownloadJob(ApplicationDownloadJob applicationDownloadJob) {
        this.applicationDownloadJob = applicationDownloadJob;
    }
}
