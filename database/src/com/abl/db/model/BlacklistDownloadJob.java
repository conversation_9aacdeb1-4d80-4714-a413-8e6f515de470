package com.abl.db.model;

import javax.persistence.*;

import org.hibernate.annotations.ForeignKey;
import org.hibernate.annotations.Type;

import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_BLACKLIST_DOWNLOAD_JOB")
public class BlacklistDownloadJob implements Serializable {

    public static final short ACTIVE = 0;
    public static final short CANCELED = 1;
    public static final short SUSPENDED = 2;

    private long id;
    private int concurrentDownload;
    private String startWindow;
    private String endWindow;
    private short status = ACTIVE;
    private Boolean deleted = false;            // true means deleted.  Most dao methods should not return or modify
    private Date createDateTime;
    private String createdBy;
    private Set<TerminalBlacklistDownloadJob> terminalBlacklistDownloadJob = new HashSet<TerminalBlacklistDownloadJob>(
            0);

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_BLACKLIST_DOWNLOAD_JOB")
    @SequenceGenerator(name = "SEQGEN_BLACKLIST_DOWNLOAD_JOB", sequenceName = "TMSQ_BLACKLIST_DOWNLOAD_JOB")
    @Column(name = "id")
    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    @Column(name = "concurrent_download", nullable = false)
    public int getConcurrentDownload() {
        return concurrentDownload;
    }

    public void setConcurrentDownload(int concurrentDownload) {
        this.concurrentDownload = concurrentDownload;
    }

    @Column(name = "start_window", nullable = false, length = 4)
    public String getStartWindow() {
        return startWindow;
    }

    public void setStartWindow(String startWindow) {
        this.startWindow = startWindow;
    }

    @Column(name = "end_window", nullable = false, length = 4)
    public String getEndWindow() {
        return endWindow;
    }

    public void setEndWindow(String endWindow) {
        this.endWindow = endWindow;
    }

    @Column(name = "status", nullable = false)
    public short getStatus() {
        return status;
    }

    public void setStatus(short status) {
        this.status = status;
    }

    @Type(type="boolean")
    @Column(name = "deleted", columnDefinition = "int default 0", nullable = false)
    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "create_date_time", nullable = false)
    public Date getCreateDateTime() {
        return createDateTime;
    }

    public void setCreateDateTime(Date createDateTime) {
        this.createDateTime = createDateTime;
    }

    @Column(name = "created_by", nullable = false, length = 30)
    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "pk.blacklistDownloadJob")
    @ForeignKey(name="TMFC_BL_DJOB_ID_TERM_BL_DJOB") 
    public Set<TerminalBlacklistDownloadJob> getTerminalBlacklistDownloadJob() {
        return terminalBlacklistDownloadJob;
    }

    public void setTerminalBlacklistDownloadJob(
            Set<TerminalBlacklistDownloadJob> terminalBlacklistDownloadJob) {
        this.terminalBlacklistDownloadJob = terminalBlacklistDownloadJob;
    }

}
