package com.abl.db.model;

import javax.persistence.*;

import org.hibernate.annotations.ForeignKey;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;


@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_PROFILE_FEE")
public class ProfileFee implements Serializable {
    private long id;
    private String desc;
    private short descType;
    private String descAdditionalInfo;
    private TerminalProfile terminalProfile;
    private Set<ProfileFeeValue> profileFeeValue = new HashSet<ProfileFeeValue>(0);

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_PROFILE_FEE")
    @SequenceGenerator(name = "SEQGEN_PROFILE_FEE", sequenceName = "TMSQ_PROFILE_FEE")
    @Column(name = "id")
    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    @Column(name = "description", nullable = true, length = 200)
    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    @Column(name = "desc_type", nullable = false)
    public short getDescType() {
        return descType;
    }

    public void setDescType(short descType) {
        this.descType = descType;
    }

    @Column(name = "desc_additional_info", length = 300)
    public String getDescAdditionalInfo() {
        return descAdditionalInfo;
    }

    public void setDescAdditionalInfo(String descAdditionalInfo) {
        this.descAdditionalInfo = descAdditionalInfo;
    }


    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "terminal_profile_id")
    @ForeignKey(name="TMFC_TERM_PROF_ID_PROFFEE")
    public TerminalProfile getTerminalProfile() {
        return terminalProfile;
    }

    public void setTerminalProfile(TerminalProfile terminalProfile) {
        this.terminalProfile = terminalProfile;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "pk.profileFee")
    @ForeignKey(name="TMFC_PROFF_ID_PROFFV") 
    public Set<ProfileFeeValue> getProfileFeeValue() {
        return profileFeeValue;
    }

    public void setProfileFeeValue(Set<ProfileFeeValue> profileFeeValue) {
        this.profileFeeValue = profileFeeValue;
    }


}
