package com.abl.db.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

/**
 * This is a view, to get terminal profile summary
 * 
 */

@SuppressWarnings("serial")
@Entity
@Table(name = "TMVW_TERM_PROF_SUMMARY_VIEW")
public class ViewTerminalProfileSummary2 implements Serializable {

	private long id;
	private long terminalProfileSummaryId;
	private long groupId;
	private String groupName;
	private long releaseId;
	private String releaseDesc;
	private int releaseVersion;
	private int minReleaseVersion;
	private long appId;
	private String modelName;
	private String appName;
	private int appVersion;
	private long profileId;
	private int profileVersion;
	private String profileName;
	private Date profileCreateDt;
	private Date profileEffectiveDate;
	private String padProfileVersion;
	private String padAppVersion;
	private String padReleaseVersion;
	private String padProfileId;

	@Id
	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	@Column(name = "group_id")
	public long getGroupId() {
		return groupId;
	}

	public void setGroupId(long groupId) {
		this.groupId = groupId;
	}

	@Column(name = "group_name")
	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	@Column(name = "release_id")
	public long getReleaseId() {
		return releaseId;
	}

	public void setReleaseId(long releaseId) {
		this.releaseId = releaseId;
	}

	@Column(name = "release_desc")
	public String getReleaseDesc() {
		return releaseDesc;
	}

	public void setReleaseDesc(String releaseDesc) {
		this.releaseDesc = releaseDesc;
	}

	@Column(name = "release_version")
	public int getReleaseVersion() {
		return releaseVersion;
	}

	public void setReleaseVersion(int releaseVersion) {
		this.releaseVersion = releaseVersion;
	}

	@Column(name = "min_release_version")
	public int getMinReleaseVersion() {
		return minReleaseVersion;
	}

	public void setMinReleaseVersion(int minReleaseVersion) {
		this.minReleaseVersion = minReleaseVersion;
	}

	@Column(name = "model_name")
	public String getModelName() {
		return modelName;
	}

	public void setModelName(String modelName) {
		this.modelName = modelName;
	}
	
	@Column(name = "app_id")
	public long getAppId() {
		return appId;
	}

	public void setAppId(long appId) {
		this.appId = appId;
	}

	@Column(name = "app_name")
	public String getAppName() {
		return appName;
	}

	public void setAppName(String appName) {
		this.appName = appName;
	}

	@Column(name = "app_version")
	public int getAppVersion() {
		return appVersion;
	}

	public void setAppVersion(int appVersion) {
		this.appVersion = appVersion;
	}

	@Column(name = "profile_id")
	public long getProfileId() {
		return profileId;
	}

	public void setProfileId(long profileId) {
		this.profileId = profileId;
	}

	@Column(name = "profile_version")
	public int getProfileVersion() {
		return profileVersion;
	}

	public void setProfileVersion(int profileVersion) {
		this.profileVersion = profileVersion;
	}

	@Column(name = "profile_name")
	public String getProfileName() {
		return profileName;
	}

	public void setProfileName(String profileName) {
		this.profileName = profileName;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "profile_create_dt")
	public Date getProfileCreateDt() {
		return profileCreateDt;
	}

	public void setProfileCreateDt(Date profileCreateDt) {
		this.profileCreateDt = profileCreateDt;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "profile_effective_date")
	public Date getProfileEffectiveDate() {
		return profileEffectiveDate;
	}

	public void setProfileEffectiveDate(Date profileEffectiveDate) {
		this.profileEffectiveDate = profileEffectiveDate;
	}

	@Column(name = "terminal_profile_summary_id")
	public long getTerminalProfileSummaryId() {
		return terminalProfileSummaryId;
	}

	public void setTerminalProfileSummaryId(long terminalProfileSummaryId) {
		this.terminalProfileSummaryId = terminalProfileSummaryId;
	}
	
	@Transient
	public String getPadProfileVersion() {
		return String.format("%06d", getProfileVersion());
	}

	@Transient
	public String getPadAppVersion() {
		return String.format("%06d", getAppVersion());
	}

	@Transient
	public String getPadReleaseVersion() {
		return String.format("%06d", getReleaseVersion());
	}

	@Transient
	public String getPadProfileId() {
		return String.format("%06d", getProfileId());
	}

	

}
