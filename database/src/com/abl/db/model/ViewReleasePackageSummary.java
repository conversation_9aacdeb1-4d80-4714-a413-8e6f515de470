package com.abl.db.model;

import org.hibernate.annotations.Cascade;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * This is a view, to get release package summary
 * 
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "TMVW_RELEASE_PACKAGE_SUMMARY")
public class ViewReleasePackageSummary implements Serializable {

    private long id;
    private int version;
    private int minVersion;
    private String description;
    private int fileSize;
    private Date loadDateTime;
    private String padVersion;
    private String padMinVersion;
    private String modelName;
    private long noTerminals;
    private int noGroups;
    

    @Id
    @Column(name = "releaseId")
    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    @Column(name = "version")
    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    @Column(name = "min_version")
    public int getMinVersion() {
		return minVersion;
	}

	public void setMinVersion(int minVersion) {
		this.minVersion = minVersion;
	}
	
    @Column(name = "description")
    public String getDescription() {
        return description;
    }

    

	public void setDescription(String description) {
        this.description = description;
    }

    @Column(name = "file_size")
    public int getFileSize() {
        return fileSize;
    }

    public void setFileSize(int fileSize) {
        this.fileSize = fileSize;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "load_date_time")
    public Date getLoadDateTime() {
        return loadDateTime;
    }

    public void setLoadDateTime(Date loadDateTime) {
        this.loadDateTime = loadDateTime;
    }

  
    @Transient
    public String getPadVersion() {
        return String.format("%06d", getVersion());
    }

    public void setPadVersion(String padVersion) {
        this.padVersion = padVersion;
    }

	@Transient
	public String getPadMinVersion() {
		return String.format("%06d", getMinVersion());
	}

	@Column(name = "model_name")
	public String getModelName() {
		return modelName;
	}

	public void setModelName(String modelName) {
		this.modelName = modelName;
	}

	@Column(name = "no_terminal")
	public long getNoTerminals() {
		return noTerminals;
	}

	public void setNoTerminals(long noTerminals) {
		this.noTerminals = noTerminals;
	}

	@Column(name = "no_groups")
	public int getNoGroups() {
		return noGroups;
	}

	public void setNoGroups(int noGroups) {
		this.noGroups = noGroups;
	}

	

}
