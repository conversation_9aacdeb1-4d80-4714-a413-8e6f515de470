package com.abl.db.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

/**
 * This is a view, to get vehicle grouping summary
 * 
 */

@SuppressWarnings("serial")
@Entity
@Table(name = "TMVW_VEHICLE_GROUPING_SUMMARY")
public class ViewVehicleGroupingSummary implements Serializable {

	private long id;
	private long groupId;
	private Date assignDate;
	private Date createDate;
	private String groupName;
	private long totalVehicles;
	private long releaseId;
	private String releaseDesc;
	private int fileSize;
	private int version;
	private int minVersion;
	private String padVersion;
	private String padMinVersion;
	private long modelId;
	private String modelName;
	
	

	@Id
	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	@Column(name = "group_id")
	public long getGroupId() {
		return groupId;
	}

	public void setGroupId(long groupId) {
		this.groupId = groupId;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "assign_date")
	public Date getAssignDate() {
		return assignDate;
	}

	public void setAssignDate(Date assignDate) {
		this.assignDate = assignDate;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "create_date")
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "group_name")
	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	@Column(name = "total_vehicles")
	public long getTotalVehicles() {
		return totalVehicles;
	}

	public void setTotalVehicles(long totalVehicles) {
		this.totalVehicles = totalVehicles;
	}

	@Column(name = "release_id")
	public long getReleaseId() {
		return releaseId;
	}

	public void setReleaseId(long releaseId) {
		this.releaseId = releaseId;
	}

	@Column(name = "description")
	public String getReleaseDesc() {
		return releaseDesc;
	}

	public void setReleaseDesc(String releaseDesc) {
		this.releaseDesc = releaseDesc;
	}

	@Column(name = "file_size")
	public int getFileSize() {
		return fileSize;
	}

	public void setFileSize(int fileSize) {
		this.fileSize = fileSize;
	}

	@Column(name = "version")
	public int getVersion() {
		return version;
	}

	public void setVersion(int version) {
		this.version = version;
	}

	@Column(name = "min_version")
	public int getMinVersion() {
		return minVersion;
	}

	public void setMinVersion(int minVersion) {
		this.minVersion = minVersion;
	}

	@Transient
	public String getPadMinVersion() {
		return String.format("%06d", getMinVersion());
	}

	public void setPadVersion(String padVersion) {
		this.padVersion = padVersion;
	}

	@Transient
	public String getPadVersion() {
		return String.format("%06d", getVersion());
	}

	public void setPadMinVersion(String padMinVersion) {
		this.padMinVersion = padMinVersion;
	}

	@Column(name = "model_id")
	public long getModelId() {
		return modelId;
	}

	public void setModelId(long modelId) {
		this.modelId = modelId;
	}

	@Column(name = "model_name")
	public String getModelName() {
		return modelName;
	}

	public void setModelName(String modelName) {
		this.modelName = modelName;
	}

}
