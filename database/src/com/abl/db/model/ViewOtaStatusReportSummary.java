package com.abl.db.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * This is a view, getting OTA Status Report Info
 * 
 */

@SuppressWarnings("serial")
@Entity
@Table(name = "TMVW_OTA_STATUS_REPORT_SUMMARY")
public class ViewOtaStatusReportSummary {
	private String groupId;
	private String groupName;
	private String releaseDesc;
	private int version;
	private String padVersion;
	private int fileSize;
	private int totalVehicles;
	private int completed;
	private int notStarted;
	private int downloading;
	private long timeDiff;
	private int onlineCompleted;
	
	@Id
	@Column(name="group_id")
	public String getGroupId() {
		return groupId;
	}
	public void setGroupId(String groupId) {
		this.groupId = groupId;
	}
	

	@Column(name="group_name")
	public String getGroupName() {
		return groupName;
	}
	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	@Column(name="release_desc")
	public String getReleaseDesc() {
		return releaseDesc;
	}
	public void setReleaseDesc(String releaseDesc) {
		this.releaseDesc = releaseDesc;
	}
	
	@Column(name="version")
	public int getVersion() {
		return version;
	}
	public void setVersion(int version) {
		this.version = version;
	}
	
	@Column(name="file_size")
	public int getFileSize() {
		return fileSize;
	}
	public void setFileSize(int fileSize) {
		this.fileSize = fileSize;
	}
	
	@Column(name="total_vehicles")
	public int getTotalVehicles() {
		return totalVehicles;
	}
	public void setTotalVehicles(int totalVehicles) {
		this.totalVehicles = totalVehicles;
	}
	
	@Column(name="completed")
	public int getCompleted() {
		return completed;
	}
	public void setCompleted(int completed) {
		this.completed = completed;
	}
	
	@Column(name="not_started")
	public int getNotStarted() {
		return notStarted;
	}
	public void setNotStarted(int notStarted) {
		this.notStarted = notStarted;
	}
	
	@Column(name="downloading")
	public int getDownloading() {
		return downloading;
	}
	public void setDownloading(int downloading) {
		this.downloading = downloading;
	}
	
	@Column(name="time_diff")
	public long getTimeDiff() {
		return timeDiff;
	}
	public void setTimeDiff(long timeDiff) {
		this.timeDiff = timeDiff;
	}
	
	@Column(name="online_completed")
	public int getOnlineCompleted() {
		return onlineCompleted;
	}
	public void setOnlineCompleted(int onlineCompleted) {
		this.onlineCompleted = onlineCompleted;
	}
	@Transient
	public String getPadVersion() {
		return String.format("%06d", getVersion());
	}
	
}
