package com.abl.db.model;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Column;
import javax.persistence.Id;

/**
 * This is a view, getting profile parameter view
 *
 */

@SuppressWarnings("serial")
@Entity
@Table(name = "TMVW_PROFILE_PARAM_VALUE_VIEW")
public class ViewProfileParameterValue {
	
	private long id;
	private long terminalProfileId;
	private long parameterDefinitionId;
	private String value;
	
	
	@Id
	public long getId() {
		return id;
	}
	
	public void setId(long id) {
		this.id = id;
	}
	@Column(name="terminal_profile_id")
	public long getTerminalProfileId() {
		return terminalProfileId;
	}
	public void setTerminalProfileId(long terminalProfileId) {
		this.terminalProfileId = terminalProfileId;
	}
	
	@Column(name="parameter_definition_id")
	public long getParameterDefinitionId() {
		return parameterDefinitionId;
	}
	public void setParameterDefinitionId(long parameterDefinitionId) {
		this.parameterDefinitionId = parameterDefinitionId;
	}
	
	@Column(name="value")
	public String getValue() {
		return value;
	}
	public void setValue(String value) {
		this.value = value;
	}
	
	

}
