package com.abl.db.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import org.apache.commons.lang.StringUtils;


@SuppressWarnings("serial")
@Entity
@Table(name = "TMVW_Vehicle")
public class Vehicle implements Serializable {

	private String vehicleId;
	private String vehicleType;
	private String ivdNo;
	private String ivdModelId;
	private String firmwareVersion;
	private String pinpadSerialNo;
	private String ezlinkSamSerialNo;
	private String masterAppVersion;
	private String creditAppVersion;
	private String netsAppVersion;
	private String epinsAppVersion;
	private String ezlinkAppVersion;
	private String fullBlackListRange;
	private String fullBlackListCan;
	private String smallBlacklistRange;
	private String smallBlackListCan;
	
	@Override
	public int hashCode() {
		return getVehicleId() != null ? getVehicleId().toLowerCase().hashCode() : 0;
	}

	@Override
	public boolean equals(Object obj) {
		if (obj == this) {
			return true;
		} else if (obj instanceof Vehicle) {
			Vehicle oth = (Vehicle) obj;
			return StringUtils.equals(oth.getVehicleId(), getVehicleId());
		}
		return false;
	}
	@Id
	@Column(name = "vehicle_id", nullable = false, length = 20)
	public String getVehicleId() {
		return vehicleId;
	}

	public void setVehicleId(String vehicleId) {
		this.vehicleId = vehicleId;
	}

	@Column(name = "vehicle_type", length = 30)
	public String getVehicleType() {
		return vehicleType;
	}

	public void setVehicleType(String vehicleType) {
		this.vehicleType = vehicleType;
	}

	@Column(name = "ivd_no", length = 10)
	public String getIvdNo() {
		return ivdNo;
	}

	public void setIvdNo(String ivdNo) {
		this.ivdNo = ivdNo;
	}

	@Column(name = "ivd_model_id", length = 10)
	public String getIvdModelId() {
		return ivdModelId;
	}

	public void setIvdModelId(String ivdModelId) {
		this.ivdModelId = ivdModelId;
	}

	@Column(name = "firmware_ver", length = 3)
	public String getFirmwareVersion() {
		return firmwareVersion;
	}

	public void setFirmwareVersion(String firmwareVersion) {
		this.firmwareVersion = firmwareVersion;
	}

	@Column(name = "pinpad_serial_no", length = 20)
	public String getPinpadSerialNo() {
		return pinpadSerialNo;
	}

	public void setPinpadSerialNo(String pinpadSerialNo) {
		this.pinpadSerialNo = pinpadSerialNo;
	}

	@Column(name = "ezlink_sam_serial_no", length = 20)
	public String getEzlinkSamSerialNo() {
		return ezlinkSamSerialNo;
	}

	public void setEzlinkSamSerialNo(String ezlinkSamSerialNo) {
		this.ezlinkSamSerialNo = ezlinkSamSerialNo;
	}

	@Column(name = "master_app_version", length = 6)
	public String getMasterAppVersion() {
		return masterAppVersion;
	}

	public void setMasterAppVersion(String masterAppVersion) {
		this.masterAppVersion = masterAppVersion;
	}

	@Column(name = "credit_app_version", length = 6)
	public String getCreditAppVersion() {
		return creditAppVersion;
	}

	public void setCreditAppVersion(String creditAppVersion) {
		this.creditAppVersion = creditAppVersion;
	}

	@Column(name = "nets_app_version", length = 6)
	public String getNetsAppVersion() {
		return netsAppVersion;
	}

	public void setNetsAppVersion(String netsAppVersion) {
		this.netsAppVersion = netsAppVersion;
	}

	@Column(name = "epins_app_version", length = 6)
	public String getEpinsAppVersion() {
		return epinsAppVersion;
	}

	public void setEpinsAppVersion(String epinsAppVersion) {
		this.epinsAppVersion = epinsAppVersion;
	}

	@Column(name = "ezlink_app_version", length = 6)
	public String getEzlinkAppVersion() {
		return ezlinkAppVersion;
	}

	public void setEzlinkAppVersion(String ezlinkAppVersion) {
		this.ezlinkAppVersion = ezlinkAppVersion;
	}

	@Column(name = "full_black_list_range", length = 20)
	public String getFullBlackListRange() {
		return fullBlackListRange;
	}

	public void setFullBlackListRange(String fullBlackListRange) {
		this.fullBlackListRange = fullBlackListRange;
	}

	@Column(name = "full_black_list_can", length = 20)
	public String getFullBlackListCan() {
		return fullBlackListCan;
	}

	public void setFullBlackListCan(String fullBlackListCan) {
		this.fullBlackListCan = fullBlackListCan;
	}

	@Column(name = "small_black_list_range", length = 20)
	public String getSmallBlacklistRange() {
		return smallBlacklistRange;
	}

	public void setSmallBlacklistRange(String smallBlacklistRange) {
		this.smallBlacklistRange = smallBlacklistRange;
	}

	@Column(name = "small_black_list_can", length = 20)
	public String getSmallBlackListCan() {
		return smallBlackListCan;
	}

	public void setSmallBlackListCan(String smallBlackListCan) {
		this.smallBlackListCan = smallBlackListCan;
	}

	public String toString() {
    return "vehicle id=" + vehicleId;
}
}
