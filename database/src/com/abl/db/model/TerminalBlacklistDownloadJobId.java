package com.abl.db.model;

import javax.persistence.Embeddable;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;

import org.hibernate.annotations.ForeignKey;

import java.io.Serializable;

@SuppressWarnings("serial")
@Embeddable
public class TerminalBlacklistDownloadJobId implements Serializable {

    private TmsTerminal tmsTerminal;
    private Vehicle vehicle;
    private BlacklistVersion blacklistVersion;
    private BlacklistDownloadJob blacklistDownloadJob;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @ForeignKey(name="TMFC_TERM_ID_TERM_BL_DL_JOB")
    public TmsTerminal getTmsTerminal() {
        return tmsTerminal;
    }

    public void setTmsTerminal(TmsTerminal tmsTerminal) {
        this.tmsTerminal = tmsTerminal;
    }

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @ForeignKey(name="TMFC_VEH_ID_TERM_BL_DL_JOB")
    public Vehicle getVehicle() {
        return vehicle;
    }

    public void setVehicle(Vehicle vehicle) {
        this.vehicle = vehicle;
    }

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @ForeignKey(name="TMFC_BL_VER_ID_TERM_BL_DL_JOB")
    public BlacklistVersion getBlacklistVersion() {
        return blacklistVersion;
    }

    public void setBlacklistVersion(BlacklistVersion blacklistVersion) {
        this.blacklistVersion = blacklistVersion;
    }

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @ForeignKey(name="TMFC_BL_DJOB_ID_TERM_BL_DJOB")
    public BlacklistDownloadJob getBlacklistDownloadJob() {
        return blacklistDownloadJob;
    }

    public void setBlacklistDownloadJob(BlacklistDownloadJob blacklistDownloadJob) {
        this.blacklistDownloadJob = blacklistDownloadJob;
    }

    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;

        TerminalBlacklistDownloadJobId that = (TerminalBlacklistDownloadJobId) o;

        if (tmsTerminal != null ? !tmsTerminal.equals(that.tmsTerminal)
                : that.tmsTerminal != null)
            return false;
        if (blacklistDownloadJob != null ? !blacklistDownloadJob
                .equals(that.blacklistDownloadJob)
                : that.blacklistDownloadJob != null)
            return false;

        return true;
    }

    public int hashCode() {
        int result;
        result = (tmsTerminal != null ? tmsTerminal.hashCode() : 0);
        result = 31
                * result
                + (blacklistDownloadJob != null ? blacklistDownloadJob.hashCode()
                : 0);
        return result;
    }

}
