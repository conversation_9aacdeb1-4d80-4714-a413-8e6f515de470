package com.abl.db.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

@Entity
@Table(name = "TMTB_SEQUENCE_TABLE")
public class SequenceTable {

	private long id;
	private String name;
	private long value=1;
	
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_ID")
    @SequenceGenerator(name = "SEQGEN_ID", sequenceName = "TMSQ_ID")
    @Column(name = "id")
    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    @Column(name = "name", unique = true)
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	@Column(name = "value")
	public long getValue() {
		return value;
	}

	public void setValue(long value) {
		this.value = value;
	}
    
}
