package com.abl.db.model;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_TERMINAL_BL_VERSION")
@AssociationOverrides({
        @AssociationOverride(name = "pk.tmsTerminal", joinColumns = @JoinColumn(name = "terminal_id")),
        @AssociationOverride(name = "pk.blacklistVersion", joinColumns = @JoinColumn(name = "blacklist_version_id"))})
public class TerminalBlacklistVersion implements Serializable {

    private TerminalBlacklistVersionId pk = new TerminalBlacklistVersionId();
    private Date updateDateTime;

    @EmbeddedId
    public TerminalBlacklistVersionId getPk() {
        return pk;
    }

    public void setPk(TerminalBlacklistVersionId pk) {
        this.pk = pk;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "update_date_time", nullable = false)
    public Date getUpdateDateTime() {
        return updateDateTime;
    }

    public void setUpdateDateTime(Date updateDateTime) {
        this.updateDateTime = updateDateTime;
    }

    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;

        TerminalBlacklistVersion that = (TerminalBlacklistVersion) o;

        if (getPk() != null ? !getPk().equals(that.getPk())
                : that.getPk() != null)
            return false;

        return true;
    }

    public int hashCode() {
        return (getPk() != null ? getPk().hashCode() : 0);
    }
}
