package com.abl.db.model;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_EZL_TXN_LOG")
public class EzlTxnLog implements Serializable {

    public enum Source {
        LES
    }

    public enum Status {
        NEW,
        SUCCESS,
        FAIL
    }

    public static final String MSG_TYPE_BL_CHECK="bl_check";
    public static final String MSG_TYPE_UNKNOWN="unknown";

    private Long id;
    private String mti; // MTI of ISO msg
    private String procCode; // proc code of ISO msg
    private String msgType;
    private Date dateTime;
    private String pan;
    private Long txnAmt;
    private String stan;
    private String rrn;
    private String tid;
    private String mid;
    private String respCode;
    private String txResult;	// internal error code
    private String taxiNumber;
    private String driverId;
    private String jobNumber;
    private Long fareAmt;
    private Long gst;
    private Long fareAdmin;
    private String companyCode;
    private String pinpadSn;
    private String validationValues;
    private EzlTxnLog.Source source=EzlTxnLog.Source.LES;
    private Status status = Status.NEW;
    private String authCode;

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_EZL_TXN_LOG")
    @SequenceGenerator(name = "SEQGEN_EZL_TXN_LOG", sequenceName = "TMSQ_EZL_TXN_LOG")
    @Column(name = "id")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "date_time", nullable=false)
    public Date getDateTime() {
        return dateTime;
    }

    public void setDateTime(Date dateTime) {
        this.dateTime = dateTime;
    }

    @Column(name = "pan", length = 19)
    public String getPan() {
        return pan;
    }

    public void setPan(String pan) {
        this.pan = pan;
    }

    @Column(name = "txn_amount")
    public Long getTxnAmt() {
        return txnAmt;
    }

    public void setTxnAmt(Long txnAmt) {
        this.txnAmt = txnAmt;
    }

    @Column(name = "stan", length = 6)
    public String getStan() {
        return stan;
    }

    public void setStan(String stan) {
        this.stan = stan;
    }

    @Column(name = "rrn", length = 24)
    public String getRrn() {
        return rrn;
    }

    public void setRrn(String rrn) {
        this.rrn = rrn;
    }

    @Column(name = "tid", length = 8)
    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    @Column(name = "mid", length = 15)
    public String getMid() {
        return mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }

    @Column(name = "resp_code", length = 2)
    public String getRespCode() {
        return respCode;
    }

    public void setRespCode(String respCode) {
        this.respCode = respCode;
    }

    @Column(name = "tx_result", length=32)
    public String getTxResult() {
        return txResult;
    }

    public void setTxResult(String txResult) {
        this.txResult = txResult;
    }

    @Column(name = "taxi_no", length = 12)
    public String getTaxiNumber() {
        return taxiNumber;
    }

    public void setTaxiNumber(String taxiNumber) {
        this.taxiNumber = taxiNumber;
    }

    @Column(name = "driver_id", length = 9)
    public String getDriverId() {
        return driverId;
    }

    public void setDriverId(String driverId) {
        this.driverId = driverId;
    }

    @Column(name = "job_number", length = 10)
    public String getJobNumber() {
        return jobNumber;
    }

    public void setJobNumber(String jobNumber) {
        this.jobNumber = jobNumber;
    }

    @Column(name = "fare_gst")
    public Long getGst() {
        return gst;
    }

    public void setGst(Long gst) {
        this.gst = gst;
    }

    @Column(name = "fare_amt")
    public Long getFareAmt() {
        return fareAmt;
    }

    public void setFareAmt(Long fareAmt) {
        this.fareAmt = fareAmt;
    }

    @Column(name = "fare_admin")
    public Long getFareAdmin() {
        return fareAdmin;
    }

    public void setFareAdmin(Long fareAdmin) {
        this.fareAdmin = fareAdmin;
    }

    @Column(name = "company_code", length = 4)
    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    @Column(name = "pinpad_sn", length = 20)
    public String getPinpadSn() {
        return pinpadSn;
    }

    public void setPinpadSn(String pinpadSn) {
        this.pinpadSn = pinpadSn;
    }

    @Column(name = "validation_val", length = 300)
    public String getValidationValues() {
        return validationValues;
    }

    public void setValidationValues(String validationValues) {
        this.validationValues = validationValues;
    }

    @Enumerated(EnumType.STRING)
    @Column(name = "source", length=32)
    public Source getSource() {
        return source;
    }

    public void setSource(Source source) {
        this.source = source;
    }

    @Enumerated(EnumType.STRING)
    @Column(name = "status", length=12, nullable=false)
    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }

    @Column(name = "mti", length = 4)
    public String getMti() {
        return mti;
    }

    public void setMti(String mti) {
        this.mti = mti;
    }

    @Column(name = "proc_code", length = 6)
    public String getProcCode() {
        return procCode;
    }

    public void setProcCode(String procCode) {
        this.procCode = procCode;
    }

    @Column(name = "msg_type", length=20)
    public String getMsgType() {
        return msgType;
    }

    public void setMsgType(String msgType) {
        this.msgType = msgType;
    }

    @Column(name = "auth_code", length = 6)
    public String getAuthCode() {
        return authCode;
    }

    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }
}
