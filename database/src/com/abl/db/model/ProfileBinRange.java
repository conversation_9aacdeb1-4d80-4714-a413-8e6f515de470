package com.abl.db.model;

import javax.persistence.*;

import org.hibernate.annotations.ForeignKey;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;


@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_PROFILE_BIN_RANGE")
public class ProfileBinRange implements Serializable {
    private long id;
    private String panHigh;
    private short panHighType;
    private String panHighAdditionalInfo;
    private String panLow;
    private short panLowType;
    private String panLowAdditionalInfo;
    private TerminalProfile terminalProfile;
    private Set<ProfileBinValue> profileBinValue = new HashSet<ProfileBinValue>(0);

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_PROFILE_BIN_RANGE")
    @SequenceGenerator(name = "SEQGEN_PROFILE_BIN_RANGE", sequenceName = "TMSQ_PROFILE_BIN_RANGE")
    @Column(name = "id")
    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    @Column(name = "pan_high", nullable = false, length = 200)
    public String getPanHigh() {
        return panHigh;
    }

    public void setPanHigh(String panHigh) {
        this.panHigh = panHigh;
    }

    @Column(name = "pan_high_type", nullable = false)
    public short getPanHighType() {
        return panHighType;
    }

    public void setPanHighType(short panHighType) {
        this.panHighType = panHighType;
    }

    @Column(name = "pan_high_additional_info", length = 300)
    public String getPanHighAdditionalInfo() {
        return panHighAdditionalInfo;
    }

    public void setPanHighAdditionalInfo(String panHighAdditionalInfo) {
        this.panHighAdditionalInfo = panHighAdditionalInfo;
    }

    @Column(name = "pan_low", nullable = false, length = 200)
    public String getPanLow() {
        return panLow;
    }

    public void setPanLow(String panLow) {
        this.panLow = panLow;
    }

    @Column(name = "pan_low_type", nullable = false)
    public short getPanLowType() {
        return panLowType;
    }

    public void setPanLowType(short panLowType) {
        this.panLowType = panLowType;
    }

    @Column(name = "pan_low_additional_info", length = 300)
    public String getPanLowAdditionalInfo() {
        return panLowAdditionalInfo;
    }

    public void setPanLowAdditionalInfo(String panLowAdditionalInfo) {
        this.panLowAdditionalInfo = panLowAdditionalInfo;
    }

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "terminal_profile_id")
    @ForeignKey(name="TMFC_TERM_PROF_ID_PROFBR")
    public TerminalProfile getTerminalProfile() {
        return terminalProfile;
    }

    public void setTerminalProfile(TerminalProfile terminalProfile) {
        this.terminalProfile = terminalProfile;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "pk.profileBinRange")
    @ForeignKey(name="TMFC_PROFBR_ID_PROFBV") 
    public Set<ProfileBinValue> getProfileBinValue() {
        return profileBinValue;
    }

    public void setProfileBinValue(Set<ProfileBinValue> profileBinValue) {
        this.profileBinValue = profileBinValue;
    }


}
