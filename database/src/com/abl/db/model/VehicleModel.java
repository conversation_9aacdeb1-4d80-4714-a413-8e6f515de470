package com.abl.db.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@SuppressWarnings("serial")
@Entity
@Table(name = "CN2_V_TMS_VEHICLEMODEL")
public class VehicleModel implements Serializable {
	private String ivdModelId;

	@Id
	@Column(name = "ivd_model_id", nullable = false, length = 10)
	public String getIvdModelId() {
		return ivdModelId;
	}

	public void setIvdModelId(String ivdModelId) {
		this.ivdModelId = ivdModelId;
	}

}
