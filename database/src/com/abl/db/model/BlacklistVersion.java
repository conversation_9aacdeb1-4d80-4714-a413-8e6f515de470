package com.abl.db.model;

import javax.persistence.*;

import org.hibernate.annotations.ForeignKey;

import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_BLACKLIST_VERSION")
public class BlacklistVersion implements Serializable {

    public static final short PENDING = 0;
    public static final short ACTIVE = 1;

    private long id;
    private int version;
    private String padVersion;
    private Date createDateTime;
    private String createdBy;
    private Date scheduleDate;
    private String fullCans;
    private String fullRange;
    private String smallCans;
    private String smallRange;
    private short status = PENDING;

    private Set<TerminalBlacklistVersion> terminalBlacklistVersion = new HashSet<TerminalBlacklistVersion>(
            0);

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_BLACKLIST_VERSION")
    @SequenceGenerator(name = "SEQGEN_BLACKLIST_VERSION", sequenceName = "TMSQ_BLACKLIST_VERSION")
    @Column(name = "id")
    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    @Column(name = "full_cans", length = 19)
    public String getFullCans() {
        return fullCans;
    }

    public void setFullCans(String fullCans) {
        this.fullCans = fullCans;
    }

    @Column(name = "full_range", length = 19)
    public String getFullRange() {
        return fullRange;
    }

    public void setFullRange(String fullRange) {
        this.fullRange = fullRange;
    }

    @Column(name = "small_cans", length = 19)
    public String getSmallCans() {
        return smallCans;
    }

    public void setSmallCans(String smallCans) {
        this.smallCans = smallCans;
    }

    @Column(name = "small_range", length = 19)
    public String getSmallRange() {
        return smallRange;
    }

    public void setSmallRange(String smallRange) {
        this.smallRange = smallRange;
    }

    @Column(name = "version", nullable = false, length = 50, unique = true)
    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "pk.blacklistVersion")
    @ForeignKey(name="TMFC_BL_VER_ID_TERM_BL_VER") 
    public Set<TerminalBlacklistVersion> getTerminalBlacklistVersion() {
        return terminalBlacklistVersion;
    }

    public void setTerminalBlacklistVersion(
            Set<TerminalBlacklistVersion> terminalBlacklistVersion) {
        this.terminalBlacklistVersion = terminalBlacklistVersion;
    }

    @Transient
    public String getPadVersion() {
        return String.format("%06d", getVersion());
    }

    public void setPadVersion(String padVersion) {
        this.padVersion = padVersion;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "create_date_time", nullable = false)
    public Date getCreateDateTime() {
        return createDateTime;
    }

    public void setCreateDateTime(Date createDateTime) {
        this.createDateTime = createDateTime;
    }

    @Column(name = "created_by", nullable = false, length = 30)
    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "schedule_date", nullable = false)
    public Date getScheduleDate() {
        return scheduleDate;
    }

    public void setScheduleDate(Date scheduleDate) {
        this.scheduleDate = scheduleDate;
    }

    @Column(name = "status", nullable = false)
    public short getStatus() {
        return status;
    }

    public void setStatus(short status) {
        this.status = status;
    }
}
