package com.abl.db.model;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.omg.CosNaming.NamingContextExtPackage.StringNameHelper;

import javax.persistence.*;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * Created with IntelliJ IDEA.
 * User: chinyew
 * Date: 21/12/12
 * Time: 2:42 PM
 * To change this template use File | Settings | File Templates.
 */
@Entity
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@Table(name = "BATCHTB_LOYALTY_FILE")
public class LoyaltyFile {

    private Integer id;
    private String filename;
    private Date createDT;
    private Date lastGeneratedDT;

    private Set<Loyalty> trans = new HashSet<Loyalty>();

    @Id
    @GeneratedValue(strategy= GenerationType.AUTO)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(nullable=false, name="filename")
    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }


    @Column(nullable=false, name="create_dt")
    public Date getCreateDT() {
        return createDT;
    }

    public void setCreateDT(Date createDT) {
        this.createDT = createDT;
    }

    @Column(nullable=false, name="last_generated_dt")
    public Date getLastGeneratedDT() {
        return lastGeneratedDT;
    }

    public void setLastGeneratedDT(Date lastGeneratedDT) {
        this.lastGeneratedDT = lastGeneratedDT;
    }

    @OneToMany (mappedBy="loyaltyFile", fetch=FetchType.LAZY)
    public Set<Loyalty> getTrans() {
        return trans;
    }

    public void setTrans(Set<Loyalty> trans) {
        this.trans = trans;
    }
}
