package com.abl.db.model;

import javax.persistence.Embeddable;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;

import org.hibernate.annotations.ForeignKey;

import java.io.Serializable;

@SuppressWarnings("serial")
@Embeddable
public class TerminalApplicationId implements Serializable {

    private TmsTerminal tmsTerminal;
    private Application application;
    private Release release;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @ForeignKey(name="TMFC_TERM_ID_TERM_APP")
    public TmsTerminal getTmsTerminal() {
        return tmsTerminal;
    }

    public void setTmsTerminal(TmsTerminal tmsTerminal) {
        this.tmsTerminal = tmsTerminal;
    }

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @ForeignKey(name="TMFC_APP_ID_TERM_APP")
    public Application getApplication() {
        return application;
    }

    public void setApplication(Application application) {
        this.application = application;
    }

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @ForeignKey(name="TMFC_REL_ID_TERM_APP")
    public Release getRelease() {
        return release;
    }

    public void setRelease(Release release) {
        this.release = release;
    }

    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;

        TerminalApplicationId that = (TerminalApplicationId) o;

        if (tmsTerminal != null ? !tmsTerminal.equals(that.tmsTerminal)
                : that.tmsTerminal != null)
            return false;
        if (application != null ? !application.equals(that.application)
                : that.application != null)
            return false;
        if (release != null ? !release.equals(that.release)
                : that.release != null)
            return false;

        return true;
    }

    public int hashCode() {
        int result;
        result = (tmsTerminal != null ? tmsTerminal.hashCode() : 0);
        result = 31 * result
                + (application != null ? application.hashCode() : 0);
        //todo: how to add release??
        return result;
    }

}
