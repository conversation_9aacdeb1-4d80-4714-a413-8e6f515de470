package com.abl.db.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_PL_ACCOUNT")
public class PlAccount implements Serializable {

	public enum Status {
		ENABLED,
		DISABLED,
		EXPIRED,
		PENDING,
	}

	private Long id;
	private Date createDateTime;
	private Status status=Status.ENABLED;
	private String userId;
	private String ecSetupId;
	private String msgId;	// msgId sent to paylah during ecSetup request

	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_PL_ACCOUNT")
	@SequenceGenerator(name = "SEQGEN_PL_ACCOUNT", sequenceName = "TMSQ_PL_ACCOUNT")
	@Column(name = "id")
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "create_date_time", nullable=false)
	public Date getCreateDateTime() {
		return createDateTime;
	}

	public void setCreateDateTime(Date createDateTime) {
		this.createDateTime = createDateTime;
	}

	@Column(name = "status", nullable=false)
	public Status getStatus() {
		return status;
	}

	public void setStatus(Status status) {
		this.status = status;
	}

	@Column(name = "user_id", nullable=false)
	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	@Column(name = "ec_setup_id", nullable=false)
	public String getEcSetupId() {
		return ecSetupId;
	}

	public void setEcSetupId(String ecSetupId) {
		this.ecSetupId = ecSetupId;
	}

	@Column(name = "msg_id", nullable=false)
	public String getMsgId() {
		return msgId;
	}

	public void setMsgId(String msgId) {
		this.msgId = msgId;
	}

}
