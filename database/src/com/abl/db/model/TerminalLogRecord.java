package com.abl.db.model;

import javax.persistence.*;

import org.hibernate.annotations.ForeignKey;

import java.io.Serializable;
import java.util.Date;

@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_TERMINAL_LOG_RECORD")
public class TerminalLogRecord implements Serializable {
    private long id;
    private TerminalLog terminalLog;
    private int sequenceNo;
    private int length;
    private Date timestamp;
    private String source;
    private String data;
    private Date uploadDateTime;

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_TERMINAL_LOG_RECORD")
    @SequenceGenerator(name = "SEQGEN_TERMINAL_LOG_RECORD", sequenceName = "TMSQ_TERMINAL_LOG_RECORD")
    @Column(name = "id")
    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "terminal_log_id")
    @ForeignKey(name="TMFC_TERM_LOG_ID_TERM_LOG_REC") 
    public TerminalLog getTerminalLog() {
        return terminalLog;
    }

    public void setTerminalLog(TerminalLog terminalLog) {
        this.terminalLog = terminalLog;
    }

    @Column(name = "sequence_no", nullable = false)
    public int getSequenceNo() {
        return sequenceNo;
    }

    public void setSequenceNo(int sequenceNo) {
        this.sequenceNo = sequenceNo;
    }

    @Column(name = "length", nullable = false)
    public int getLength() {
        return length;
    }

    public void setLength(int length) {
        this.length = length;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "timestamp", nullable = false)
    public Date getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    @Column(name = "source", nullable = false, length = 3)
    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    @Column(name = "data", length = 4000)
    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "upload_date_time", nullable = false)
    public Date getUploadDateTime() {
        return uploadDateTime;
    }

    public void setUploadDateTime(Date uploadDateTime) {
        this.uploadDateTime = uploadDateTime;
    }
}
