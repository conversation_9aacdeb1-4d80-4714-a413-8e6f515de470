package com.abl.db.model;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_TERM_APP_DOWNLOAD_JOB")
@AssociationOverrides({
        @AssociationOverride(name = "pk.tmsTerminal", joinColumns = @JoinColumn(name = "terminal_id")),
        @AssociationOverride(name = "pk.vehicle", joinColumns = @JoinColumn(name = "vehicle_id")),
        @AssociationOverride(name = "pk.release", joinColumns = @JoinColumn(name = "release_id")),
        @AssociationOverride(name = "pk.applicationDownloadJob", joinColumns = @JoinColumn(name = "application_download_job_id"))})
public class TerminalApplicationDownloadJob implements Serializable {

    public static final short SUCCESS = 0;
    public static final short IN_PROGRESS = 1;
    public static final short REJECTED = 2;
    public static final short FAIL = 3;
    public static final short NEW = 4;

    private TerminalApplicationDownloadJobId pk = new TerminalApplicationDownloadJobId();
    private Date updateDateTime;
    private short status = NEW;
    private int recordNo;
    private Date firstRequestDateTime;
    private Date lastRequestDateTime;

    @EmbeddedId
    public TerminalApplicationDownloadJobId getPk() {
        return pk;
    }

    public void setPk(TerminalApplicationDownloadJobId pk) {
        this.pk = pk;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "update_date_time", nullable = false)
    public Date getUpdateDateTime() {
        return updateDateTime;
    }

    public void setUpdateDateTime(Date updateDateTime) {
        this.updateDateTime = updateDateTime;
    }

    @Column(name = "status", nullable = false)
    public short getStatus() {
        return status;
    }

    public void setStatus(short status) {
        this.status = status;
    }

    @Column(name = "record_no", nullable = false)
    public int getRecordNo() {
        return recordNo;
    }

    public void setRecordNo(int recordNo) {
        this.recordNo = recordNo;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "first_request_date_time")
    public Date getFirstRequestDateTime() {
        return firstRequestDateTime;
    }

    public void setFirstRequestDateTime(Date firstRequestDateTime) {
        this.firstRequestDateTime = firstRequestDateTime;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "last_request_date_time")
    public Date getLastRequestDateTime() {
        return lastRequestDateTime;
    }

    public void setLastRequestDateTime(Date lastRequestDateTime) {
        this.lastRequestDateTime = lastRequestDateTime;
    }

    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;

        TerminalApplicationDownloadJob that = (TerminalApplicationDownloadJob) o;

        if (getPk() != null ? !getPk().equals(that.getPk())
                : that.getPk() != null)
            return false;

        return true;
    }

    public int hashCode() {
        return (getPk() != null ? getPk().hashCode() : 0);
    }

}
