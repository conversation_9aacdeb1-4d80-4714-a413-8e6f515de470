package com.abl.db.model;

import javax.persistence.*;

import org.hibernate.annotations.ForeignKey;

import java.io.Serializable;
import java.util.Date;

@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_TXN_LOG")
public class TxnLog implements Serializable {

    public enum TxnSource {
        POS,        // from POS
        MTE,        // from MTE
        FTP,        // from FTP
        INTERNAL,    // internal, e.g. card expiry
        DCP;		//for master pass
        
    }

    private Long id;
    private Date txnDateTime;            // date/time txn is recorded
    private String mti;                    // MTI of ISO msg
    private String procCode;            // proc code of ISO msg
    private String tid;                    // tid (tid may not be valid)
    private TxnType txnType;            // txn type (null if unknown trans)
    private TxnSource txnSource = TxnSource.POS;    // source of txn
    private TxnResult txnResult;        // txn result
    private String responseCode;        // response code
    private String transHeader;            // transaction header (8 bytes, in hex)
    private String termSerialNo;        // TmsTerminal Serial Number
    private byte[] data;                // extra data (e.g. batch upload trans)

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_TXN_LOG")
    @SequenceGenerator(name = "SEQGEN_TXN_LOG", sequenceName = "TMSQ_TXN_LOG")
    @Column(name = "id")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "txn_date_time")
    public Date getTxnDateTime() {
        return txnDateTime;
    }

    public void setTxnDateTime(Date txnDateTime) {
        this.txnDateTime = txnDateTime;
    }

    @Column(name = "mti", length = 4)
    public String getMti() {
        return mti;
    }

    public void setMti(String mti) {
        this.mti = mti;
    }

    @Column(name = "proc_code", length = 6)
    public String getProcCode() {
        return procCode;
    }

    public void setProcCode(String procCode) {
        this.procCode = procCode;
    }

    @Column(name = "tid", length = 8)
    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "txn_type")
    @ForeignKey(name="TMFC_TXN_TYPE_CODE_TXN_LOG") 
    public TxnType getTxnType() {
        return txnType;
    }

    public void setTxnType(TxnType txnType) {
        this.txnType = txnType;
    }

    @Column(name = "txn_source")
    public TxnSource getTxnSource() {
        return txnSource;
    }

    public void setTxnSource(TxnSource txnSource) {
        this.txnSource = txnSource;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "txn_result")
    @ForeignKey(name="TMFC_TXN_RESULT_CODE_TXN_LOG") 
    public TxnResult getTxnResult() {
        return txnResult;
    }

    public void setTxnResult(TxnResult txnResult) {
        this.txnResult = txnResult;
    }

    @Column(name = "resp_code", length = 2)
    public String getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(String responseCode) {
        this.responseCode = responseCode;
    }

    @Column(name = "trans_header", length = 8)
    public String getTransHeader() {
        return transHeader;
    }

    public void setTransHeader(String transHeader) {
        this.transHeader = transHeader;
    }

    @Column(name = "term_serial_no", length = 60)
    public String getTermSerialNo() {
        return termSerialNo;
    }

    public void setTermSerialNo(String termSerialNo) {
        this.termSerialNo = termSerialNo;
    }

    @Lob
    @Basic(fetch = FetchType.LAZY)
    public byte[] getData() {
        return data;
    }

    public void setData(byte[] data) {
        this.data = data;
    }
}
