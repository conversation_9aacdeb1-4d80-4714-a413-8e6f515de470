package com.abl.db.model;

import javax.persistence.*;

import org.hibernate.annotations.ForeignKey;
import org.hibernate.annotations.Type;

import java.io.Serializable;
import java.util.Date;

/**
 * Represents grouping of vehicles.
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_GROUPING")
public class Group implements Serializable {

    private Long id;
    private String name;
    private String createdBy;
    private Date createDateTime;
    private Date updateDateTime;
    private Boolean deleted = false;            // true means deleted.  Most dao methods should not return or modify
    private Release release;
    private Date assignDateTime;

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_GROUP")
    @SequenceGenerator(name = "SEQGEN_GROUP", sequenceName = "TMSQ_GROUP")
    @Column(name = "id")
    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "name", nullable = false, unique = true, length = 100)
    public String getName() {
        return name;
    }

    public void setName(String adminName) {
        this.name = adminName;
    }

    @Column(name = "created_by", length = 30, nullable = false)
    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "create_date_time", nullable = false)
    public Date getCreateDateTime() {
        return createDateTime;
    }

    public void setCreateDateTime(Date createDateTime) {
        this.createDateTime = createDateTime;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "update_date_time")
    public Date getUpdateDateTime() {
        return updateDateTime;
    }

    public void setUpdateDateTime(Date updateDateTime) {
        this.updateDateTime = updateDateTime;
    }

    @Type(type="boolean")
    @Column(name = "deleted", columnDefinition = "int default 0", nullable = false)
    public Boolean isDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    @ManyToOne(fetch = FetchType.LAZY, optional = true)
    @JoinColumn(name = "release_id")
    @ForeignKey(name="TMFC_REL_ID_GROUP") 
    public Release getRelease() {
        return release;
    }

    public void setRelease(Release release) {
        this.release = release;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "assign_date_time")
    public Date getAssignDateTime() {
        return assignDateTime;
    }

    public void setAssignDateTime(Date assignDateTime) {
        this.assignDateTime = assignDateTime;
    }

    public String toString() {
        return "Group(id=" + id + ", name=" + name + ")";
    }
}
