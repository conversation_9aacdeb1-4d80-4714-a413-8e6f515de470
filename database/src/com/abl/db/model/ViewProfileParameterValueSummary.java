package com.abl.db.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * This is a view, getting profile parameter value
 * 
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "TMVW_PROF_PARAM_VALUE_SUMMARY")
public class ViewProfileParameterValueSummary implements Serializable {
	private long id;
	private long parameterDefinitionId;
	private long profileId;
	private long applicationId;
	private String additonalInfo;
	private String paramName;
	private String description;
	private int level;
	private int type;
	private String value;
	private String valueRules;
	
	@Id
	public long getId() {
		return id;
	}
	public void setId(long id) {
		this.id = id;
	}
	
	@Column(name = "parameter_definition_id")
	public long getParameterDefinitionId() {
		return parameterDefinitionId;
	}
	public void setParameterDefinitionId(long parameterDefinitionId) {
		this.parameterDefinitionId = parameterDefinitionId;
	}
	
	@Column(name = "terminal_profile_id")
	public long getProfileId() {
		return profileId;
	}
	public void setProfileId(long profileId) {
		this.profileId = profileId;
	}
	
	@Column(name = "application_id")
	public long getApplicationId() {
		return applicationId;
	}
	public void setApplicationId(long applicationId) {
		this.applicationId = applicationId;
	}
	
	@Column(name = "additional_info")
	public String getAdditonalInfo() {
		return additonalInfo;
	}
	public void setAdditonalInfo(String additonalInfo) {
		this.additonalInfo = additonalInfo;
	}
	
	@Column(name = "name")
	public String getParamName() {
		return paramName;
	}
	public void setParamName(String paramName) {
		this.paramName = paramName;
	}
	
	@Column(name = "description")
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	
	@Column(name = "param_level")
	public int getLevel() {
		return level;
	}
	public void setLevel(int level) {
		this.level = level;
	}
	
	@Column(name = "type")
	public int getType() {
		return type;
	}
	public void setType(int type) {
		this.type = type;
	}
	
	@Column(name = "value")
	public String getValue() {
		return value;
	}
	public void setValue(String value) {
		this.value = value;
	}
	
	@Transient
	public String getValueRules() {
		String dataType[] = null;
		String values = this.additonalInfo; 
		String valueRules=null;
		if(this.getType()!=4 && this.getType()!=5 && this.getType()!=6){
			String listValue = values.substring(values.indexOf("(") + 1,
					values.indexOf(")"));
			dataType = listValue.split(",");
			
			if(this.getType()==0){  //number
				int decimal = Integer.parseInt(dataType[3]);
				String initialMaxValue = dataType[2];
				int startDecimal = initialMaxValue.length() - decimal;
				double minValue = Double.parseDouble(dataType[1]);
				double maxValue = Double.parseDouble(initialMaxValue.substring(0,startDecimal)+ "."+ initialMaxValue.substring(startDecimal));
				valueRules = "(Number, min: "+minValue+", max: "+maxValue+", decimal: "+decimal+")";
				
			}else if(this.getType()==3){ //String
				int minLength = Integer.parseInt(dataType[0]);
				int maxLength = Integer.parseInt(dataType[1]);
				valueRules = "(String, min length: "+minLength+", max length: "+maxLength+")";
				
			}
			
		}else{
			if(this.getType()==4){ //date
				valueRules ="(Date, DDMMYY)";
			}else if(this.getType()==5){ //time
				valueRules ="(Time, HHmmss)";
			}else if(this.getType()==6){ //time
				valueRules ="(DateTime, DDMMYYHHmmss)";
			}
			
		}
			
		return valueRules;
	}

	public void setValueRules(String valueRules) {
		this.valueRules = valueRules;
	}
	
	

}
