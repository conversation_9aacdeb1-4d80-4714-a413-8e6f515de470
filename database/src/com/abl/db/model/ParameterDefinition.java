package com.abl.db.model;

import javax.persistence.*;

import org.hibernate.annotations.ForeignKey;
import org.hibernate.annotations.Type;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_PARAMETER_DEFINITION", uniqueConstraints = {@UniqueConstraint(columnNames = {
        "application_id", "name"})})
public class ParameterDefinition implements Serializable {

    public static final short NUMBER = 0;
    public static final short BOOLEAN = 1;
    public static final short OPTION = 2;
    public static final short STRING = 3;
    public static final short DATE = 4;
    public static final short TIME = 5;
    public static final short DATETIME = 6;

    public static final short APP_LEVEL = 0;
    public static final short BIN_LEVEL = 1;
    public static final short FEE_LEVEL = 2;

    private long id;
    private String name;
    private String description;
    private String additionalInfo;
    private short type;
    private short level;
    private String defaultValue;
    private Boolean deleted = false;            // true means deleted.  Most dao methods should not return or modify
    private Application application;
    private String valueRules;
    private Set<ProfileParameterValue> profileParameterValue = new HashSet<ProfileParameterValue>(
            0);

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_PARAMETER_DEFINITION")
    @SequenceGenerator(name = "SEQGEN_PARAMETER_DEFINITION", sequenceName = "TMSQ_PARAMETER_DEFINITION")
    @Column(name = "id")
    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    @Column(name = "name", nullable = false, length = 50)
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Column(name = "description", length = 200)
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Column(name = "additional_info", length = 300)
    public String getAdditionalInfo() {
        return additionalInfo;
    }

    public void setAdditionalInfo(String additionalInfo) {
        this.additionalInfo = additionalInfo;
    }

    @Column(name = "type", nullable = false)
    public short getType() {
        return type;
    }

    public void setType(short type) {
        this.type = type;
    }

    @Column(name = "param_level", nullable = false)
    public short getLevel() {
        return level;
    }

    public void setLevel(short level) {
        this.level = level;
    }

    @Column(name = "default_value", length = 200)
    public String getDefaultValue() {
        return defaultValue;
    }

    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }

    @Type(type="boolean")
    @Column(name = "deleted", columnDefinition = "int default 0", nullable = false)
    public Boolean isDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "pk.parameterDefinition", cascade = CascadeType.ALL)
    @ForeignKey(name="TMFC_PARAMDEF_ID_PRO_PARAM_VAL")
    public Set<ProfileParameterValue> getProfileParameterValue() {
        return profileParameterValue;
    }

    public void setProfileParameterValue(
            Set<ProfileParameterValue> profileParameterValue) {
        this.profileParameterValue = profileParameterValue;
    }

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "application_id")
    @ForeignKey(name="TMFC_APP_ID_PARAM_DEF") 
    public Application getApplication() {
        return application;
    }

    public void setApplication(Application application) {
        this.application = application;
    }

    @Transient
    public String getValueRules() {
        String dataType[] = null;
        String values = this.additionalInfo;
        String valueRules = null;
        if (this.getType() != 4 && this.getType() != 5 && this.getType() != 6) {
            String listValue = values.substring(values.indexOf("(") + 1,
                    values.indexOf(")"));
            dataType = listValue.split(",");

            if (this.getType() == NUMBER) {
                int decimal = Integer.parseInt(dataType[3]);
                String initialMaxValue = dataType[2];
                int startDecimal = initialMaxValue.length() - decimal;
                double minValue = Double.parseDouble(dataType[1]);
                double maxValue = Double.parseDouble(initialMaxValue.substring(0, startDecimal) + "." + initialMaxValue.substring(startDecimal));
                valueRules = "(Number, min: " + minValue + ", max: " + maxValue + ", decimal: " + decimal + ")";

            } else if (this.getType() == STRING) {
                int minLength = Integer.parseInt(dataType[0]);
                int maxLength = Integer.parseInt(dataType[1]);
                valueRules = "(String, min length: " + minLength + ", max length: " + maxLength + ")";

            }

        } else {
            if (this.getType() == DATE) {
                valueRules = "(Date, DDMMYY)";
            } else if (this.getType() == TIME) {
                valueRules = "(Time, HHmmss)";
            }else if (this.getType() == DATETIME) {
                valueRules = "(DateTime, DDMMYYHHmmss)";
            }

        }

        return valueRules;
    }

    public void setValueRules(String valueRules) {
        this.valueRules = valueRules;
    }


}
