package com.abl.db.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * This is a view, getting terminals that has completed/pending downloading job
 *
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "TMVW_TERM_WITH_DOWNLOAD_JOB")
public class ViewTerminalWithDownloadJob {

	private long id;
	private long terminalId;
	private long releaseId;
	private long groupId;
	private String groupName;
	private String serialNo;
	private long appDownloadId;
	private int terminalDownloadStatus;
    private int recordNo;
    private Date startTime;
    private Date lastDownloaded;
    private String vehicleId;
    private int totalRecordNo;


	@Id
	@Column(name="id")
	public long getId() {
		return id;
	}
	public void setId(long id) {
		this.id = id;
	}
	
	@Column(name="terminal_id")
	public long getTerminalId() {
		return terminalId;
	}
	
	public void setTerminalId(long terminalId) {
		this.terminalId = terminalId;
	}
	
	@Column(name="group_id")
	public long getGroupId() {
		return groupId;
	}
	public void setGroupId(long groupId) {
		this.groupId = groupId;
	}
	
	@Column(name="group_name")
	public String getGroupName() {
		return groupName;
	}
	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	@Column(name="serial_no")
	public String getSerialNo() {
		return serialNo;
	}
	public void setSerialNo(String serialNo) {
		this.serialNo = serialNo;
	}
	
	
	@Column(name="app_download_id")
	public long getAppDownloadId() {
		return appDownloadId;
	}
	public void setAppDownloadId(long appDownloadId) {
		this.appDownloadId = appDownloadId;
	}
	
	@Column(name="terminal_download_status")
	public int getTerminalDownloadStatus() {
		return terminalDownloadStatus;
	}
	public void setTerminalDownloadStatus(int terminalDownloadStatus) {
		this.terminalDownloadStatus = terminalDownloadStatus;
	}
	
    @Column(name = "record_no")
    public int getRecordNo() {
        return recordNo;
    }

    public void setRecordNo(int recordNo) {
        this.recordNo = recordNo;
    }
    
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "start_time", nullable = false) 
	public Date getStartTime() {
		return startTime;
	}
	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
    @Column(name = "last_downloaded", nullable = false) 
	public Date getLastDownloaded() {
		return lastDownloaded;
	}
	public void setLastDownloaded(Date lastDownloaded) {
		this.lastDownloaded = lastDownloaded;
	}
	@Column(name = "vehicle_id")
	public String getVehicleId() {
		return vehicleId;
	}
	public void setVehicleId(String vehicleId) {
		this.vehicleId = vehicleId;
	} 

    @Column(name = "total_record_no")
    public int getTotalRecordNo() {
        return totalRecordNo;
    }

    public void setTotalRecordNo(int totalRecordNo) {
        this.totalRecordNo = totalRecordNo;
    }
    
    @Column(name = "release_id")
	public long getReleaseId() {
		return releaseId;
	}
	public void setReleaseId(long releaseId) {
		this.releaseId = releaseId;
	}


}
