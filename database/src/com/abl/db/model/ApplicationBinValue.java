package com.abl.db.model;

import javax.persistence.*;
import java.io.Serializable;

@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_APPLICATION_BIN_VALUE")
@AssociationOverrides({
        @AssociationOverride(name = "pk.applicationBinRange", joinColumns = @JoinColumn(name = "application_bin_range_id")),
        @AssociationOverride(name = "pk.parameterDefinition", joinColumns = @JoinColumn(name = "parameter_definition_id"))})
public class ApplicationBinValue implements Serializable {

    private ApplicationBinValueId pk = new ApplicationBinValueId();
    private String value;

    @EmbeddedId
    public ApplicationBinValueId getPk() {
        return pk;
    }

    public void setPk(ApplicationBinValueId pk) {
        this.pk = pk;
    }

    @Column(name = "value", nullable = false, length = 200)
    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }


    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;

        ApplicationBinValue that = (ApplicationBinValue) o;

        if (getPk() != null ? !getPk().equals(that.getPk())
                : that.getPk() != null)
            return false;

        return true;
    }

    public int hashCode() {
        return (getPk() != null ? getPk().hashCode() : 0);
    }

}
