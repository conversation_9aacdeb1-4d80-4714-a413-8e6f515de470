package com.abl.db.model;

import javax.persistence.*;

import org.hibernate.annotations.ForeignKey;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_APPLICATION_BIN_RANGE")
public class ApplicationBinRange implements Serializable {

    private long id;
    private String panHigh;
    private short panHighType;
    private String panHighAdditionalInfo;
    private String panLow;
    private short panLowType;
    private String panLowAdditionalInfo;
    private Application application;
    private Set<ApplicationBinValue> applicationBinValue = new HashSet<ApplicationBinValue>(0);

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_APPLICATION_BIN_RANGE")
    @SequenceGenerator(name = "SEQGEN_APPLICATION_BIN_RANGE", sequenceName = "TMSQ_APPLICATION_BIN_RANGE")
    @Column(name = "id")
    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    @Column(name = "pan_high", nullable = false, length = 200)
    public String getPanHigh() {
        return panHigh;
    }

    public void setPanHigh(String panHigh) {
        this.panHigh = panHigh;
    }

    @Column(name = "pan_high_type", nullable = false)
    public short getPanHighType() {
        return panHighType;
    }

    public void setPanHighType(short panHighType) {
        this.panHighType = panHighType;
    }

    @Column(name = "pan_high_additional_info", length = 300)
    public String getPanHighAdditionalInfo() {
        return panHighAdditionalInfo;
    }

    public void setPanHighAdditionalInfo(String panHighAdditionalInfo) {
        this.panHighAdditionalInfo = panHighAdditionalInfo;
    }

    @Column(name = "pan_low", nullable = false, length = 200)
    public String getPanLow() {
        return panLow;
    }

    public void setPanLow(String panLow) {
        this.panLow = panLow;
    }

    @Column(name = "pan_low_type", nullable = false)
    public short getPanLowType() {
        return panLowType;
    }

    public void setPanLowType(short panLowType) {
        this.panLowType = panLowType;
    }

    @Column(name = "pan_low_additional_info", length = 300)
    public String getPanLowAdditionalInfo() {
        return panLowAdditionalInfo;
    }

    public void setPanLowAdditionalInfo(String panLowAdditionalInfo) {
        this.panLowAdditionalInfo = panLowAdditionalInfo;
    }

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "application_id")
    @ForeignKey(name="TMFC_APP_ID_APP_BIN_RANGE") 
    public Application getApplication() {
        return application;
    }

    public void setApplication(Application application) {
        this.application = application;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "pk.applicationBinRange")
    @ForeignKey(name="TMFC_APPBR_ID_APPBV") 
    public Set<ApplicationBinValue> getApplicationBinValue() {
        return applicationBinValue;
    }

    public void setApplicationBinValue(Set<ApplicationBinValue> applicationBinValue) {
        this.applicationBinValue = applicationBinValue;
    }

}
