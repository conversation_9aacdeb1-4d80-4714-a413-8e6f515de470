package com.abl.db.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * This is a view, getting terminals that has completed/pending downloading job
 *
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "TMVW_TERM_BL_COMPLETED_JOB")
public class ViewTerminalBlacklistCompletedJob {

	
	private long id;
	private String serialNo;
	private String modelName;   
    private String vehicleId;
    private long blacklistVersionId;
    private Date updateDt;
    private int blacklistVersion;



	@Id
	@Column(name="id")
	public long getId() {
		return id;
	}
	public void setId(long id) {
		this.id = id;
	}
	
	
	@Column(name="serial_no")
	public String getSerialNo() {
		return serialNo;
	}
	public void setSerialNo(String serialNo) {
		this.serialNo = serialNo;
	}
	
	@Column(name="model_name")
	public String getModelName() {
		return modelName;
	}
	public void setModelName(String modelName) {
		this.modelName = modelName;
	}
    
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "update_date_time", nullable = false) 
	public Date getUpdateDt() {
		return updateDt;
	}
	public void setUpdateDt(Date updateDt) {
		this.updateDt = updateDt;
	}
	
	@Column(name = "vehicle_id")
	public String getVehicleId() {
		return vehicleId;
	}
	public void setVehicleId(String vehicleId) {
		this.vehicleId = vehicleId;
	}
	@Column(name="blacklist_version_id")
	public long getBlacklistVersionId() {
		return blacklistVersionId;
	}
	public void setBlacklistVersionId(long blacklistVersionId) {
		this.blacklistVersionId = blacklistVersionId;
	}
	
	@Column(name="blacklist_version")
	public int getBlacklistVersion() {
		return blacklistVersion;
	}
	public void setBlacklistVersion(int blacklistVersion) {
		this.blacklistVersion = blacklistVersion;
	} 

 

}
