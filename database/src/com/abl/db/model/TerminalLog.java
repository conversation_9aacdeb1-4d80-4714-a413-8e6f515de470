package com.abl.db.model;

import javax.persistence.*;

import org.hibernate.annotations.ForeignKey;

import java.io.Serializable;
import java.util.Date;

@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_TERMINAL_LOG", uniqueConstraints = {@UniqueConstraint(columnNames = {
        "terminal_id", "log_date"})})
public class TerminalLog implements Serializable {
    private long id;
    private TmsTerminal tmsTerminal;
    private Date logDate;

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_TERMINAL_LOG")
    @SequenceGenerator(name = "SEQGEN_TERMINAL_LOG", sequenceName = "TMSQ_TERMINAL_LOG")
    @Column(name = "id")
    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "terminal_id")
    @ForeignKey(name="TMFC_TERM_ID_TERM_LOG") 
    public TmsTerminal getTmsTerminal() {
        return tmsTerminal;
    }

    public void setTmsTerminal(TmsTerminal tmsTerminal) {
        this.tmsTerminal = tmsTerminal;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "log_date", nullable = false)
    public Date getLogDate() {
        return logDate;
    }

    public void setLogDate(Date logDate) {
        this.logDate = logDate;
    }

}
