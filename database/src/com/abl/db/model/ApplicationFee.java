package com.abl.db.model;

import javax.persistence.*;

import org.hibernate.annotations.ForeignKey;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_APPLICATION_FEE")
public class ApplicationFee implements Serializable {

    private long id;
    private String desc;
    private short descType;
    private String descAdditionalInfo;
    
    private Application application;
    private Set<ApplicationFeeValue> applicationFeeValue = new HashSet<ApplicationFeeValue>(0);

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_APPLICATION_FEE")
    @SequenceGenerator(name = "SEQGEN_APPLICATION_FEE", sequenceName = "TMSQ_APPLICATION_FEE")
    @Column(name = "id")
    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    @Column(name = "description", nullable = true, length = 200)
    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    @Column(name = "desc_type", nullable = false)
    public short getDescType() {
        return descType;
    }

    public void setDescType(short descType) {
        this.descType = descType;
    }

    @Column(name = "desc_additional_info", length = 300)
    public String getDescAdditionalInfo() {
        return descAdditionalInfo;
    }

    public void setDescAdditionalInfo(String descAdditionalInfo) {
        this.descAdditionalInfo = descAdditionalInfo;
    }

  

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "application_id")
    @ForeignKey(name="TMFC_APP_ID_APP_FEE") 
    public Application getApplication() {
        return application;
    }

    public void setApplication(Application application) {
        this.application = application;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "pk.applicationFee")
    @ForeignKey(name="TMFC_APPF_ID_APPFV") 
    public Set<ApplicationFeeValue> getApplicationFeeValue() {
        return applicationFeeValue;
    }

    public void setApplicationFeeValue(Set<ApplicationFeeValue> applicationFeeValue) {
        this.applicationFeeValue = applicationFeeValue;
    }

}
