package com.abl.db.model;

import javax.persistence.*;

import org.hibernate.annotations.ForeignKey;
import org.hibernate.annotations.Type;

import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_TERMINAL_PROFILE", uniqueConstraints = {@UniqueConstraint(columnNames = {
        "group_id", "application_id", "profile_version"})})
public class TerminalProfile implements Serializable {
    private long id;
    private String profileName;
    private int profileVersion;
    private Group group;
    private Application application;
    private Date createDateTime;
    private Boolean deleted = false;            // true means deleted.  Most dao methods should not return or modify
    private String padProfileId;
    private Date effectiveDate;
    private Set<ProfileParameterValue> profileParameterValue = new HashSet<ProfileParameterValue>(
            0);

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_TERMINAL_PROFILE")
    @SequenceGenerator(name = "SEQGEN_TERMINAL_PROFILE", sequenceName = "TMSQ_TERMINAL_PROFILE")
    @Column(name = "id")
    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    @Column(name = "profile_name", nullable = false, length = 50)
    public String getProfileName() {
        return profileName;
    }

    public void setProfileName(String profileName) {
        this.profileName = profileName;
    }

    @Column(name = "profile_version", nullable = false)
    public int getProfileVersion() {
        return profileVersion;
    }

    public void setProfileVersion(int profileVersion) {
        this.profileVersion = profileVersion;
    }

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "group_id")
    @ForeignKey(name="TMFC_GROUP_ID_TERM_PROF") 
    public Group getGroup() {
        return group;
    }

    public void setGroup(Group group) {
        this.group = group;
    }

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "application_id")
    @ForeignKey(name="TMFC_APP_ID_TERM_PROFILE") 
    public Application getApplication() {
        return application;
    }

    public void setApplication(Application application) {
        this.application = application;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "create_date_time", nullable = false)
    public Date getCreateDateTime() {
        return createDateTime;
    }

    public void setCreateDateTime(Date createDateTime) {
        this.createDateTime = createDateTime;
    }

    @Type(type="boolean")
    @Column(name = "deleted", columnDefinition = "int default 0", nullable = false)
    public Boolean isDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "pk.terminalProfile", cascade = CascadeType.ALL)
    public Set<ProfileParameterValue> getProfileParameterValue() {
        return profileParameterValue;
    }

    public void setProfileParameterValue(
            Set<ProfileParameterValue> profileParameterValue) {
        this.profileParameterValue = profileParameterValue;
    }

    @Transient
    public String getPadProfileId() {
        return padProfileId;
    }

    public void setPadProfileId(String padProfileId) {
        this.padProfileId = padProfileId;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "effective_date")
    public Date getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(Date effectiveDate) {
        this.effectiveDate = effectiveDate;
    }


}
