package com.abl.db.model;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import javax.persistence.*;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * User: chinyew
 * Date: 19/12/12
 * Time: 12:38 PM
 */
@Entity
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@Table(name = "BATCHTB_LOYALTY_TRANSACTIONS")
public class Loyalty {
    private Long id;
    private String tripNo;
    private String cardNo;
    private String completeTime;
    private String vehicleNo;
    private String bookingChannel;
    private String paymentMode;
    private String paymentCardNo;
    private String tripStart;
    private String tripEnd;
    private byte[] receipt;
    private String fare;
    private String admin;
    private String gst;
    private String distance;
    private String prevTransport;
    private String warnCode;
    private String remarks;
    private String tid;
    private String stan;
    private Date createDt;
    private String jobNo;
    private String coyCode;

    private LoyaltyFile loyaltyFile;

    @Id
    @GeneratedValue(strategy= GenerationType.AUTO)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(nullable=true, name="trip_no")
    public String getTripNo() {
        return tripNo;
    }

    public void setTripNo(String tripNo) {
        this.tripNo = tripNo;
    }

    @Column(nullable=true, name="card_no")
    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    @Column(nullable=true, name="complete_time")
    public String getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(String completeTime) {
        this.completeTime = completeTime;
    }

    @Column(nullable=true, name="vehicle_no")
    public String getVehicleNo() {
        return vehicleNo;
    }

    public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
    }

    @Column(nullable=true, name="booking_channel")
    public String getBookingChannel() {
        return bookingChannel;
    }

    public void setBookingChannel(String bookingChannel) {
        this.bookingChannel = bookingChannel;
    }

    @Column(nullable=true, name="payment_mode")
    public String getPaymentMode() {
        return paymentMode;
    }

    public void setPaymentMode(String paymentMode) {
        this.paymentMode = paymentMode;
    }

    @Column(nullable=true, name="payment_card_no")
    public String getPaymentCardNo() {
        return paymentCardNo;
    }

    public void setPaymentCardNo(String paymentCardNo) {
        this.paymentCardNo = paymentCardNo;
    }

    @Column(nullable=true, name="trip_start")
    public String getTripStart() {
        return tripStart;
    }

    public void setTripStart(String tripStart) {
        this.tripStart = tripStart;
    }

    @Column(nullable=true, name="trip_end")
    public String getTripEnd() {
        return tripEnd;
    }

    public void setTripEnd(String tripEnd) {
        this.tripEnd = tripEnd;
    }

    @Column(nullable=true, name="receipt" )
    @Lob
    public byte[] getReceipt() {
        return receipt;
    }

    public void setReceipt(byte[] receipt) {
        this.receipt = receipt;
    }

    @Column(nullable=true, name="fare")
    public String getFare() {
        return fare;
    }

    public void setFare(String fare) {
        this.fare = fare;
    }

    @Column(nullable=true, name="admin")
    public String getAdmin() {
        return admin;
    }

    public void setAdmin(String admin) {
        this.admin = admin;
    }

    @Column(nullable=true, name="gst")
    public String getGst() {
        return gst;
    }

    public void setGst(String gst) {
        this.gst = gst;
    }

    @Column(nullable=true, name="distance")
    public String getDistance() {
        return distance;
    }

    public void setDistance(String distance) {
        this.distance = distance;
    }

    @Column(nullable=true, name="prev_transport_mode")
    public String getPrevTransport() {
        return prevTransport;
    }

    public void setPrevTransport(String prevTransport) {
        this.prevTransport = prevTransport;
    }

    @Column(nullable=true, name="warn_code")
    public String getWarnCode() {
        return warnCode;
    }

    public void setWarnCode(String warnCode) {
        this.warnCode = warnCode;
    }

    @Column(nullable=true, name="remarks")
    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    @Column(nullable=false, name="tid", length=8)
    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    @Column(nullable=false, name="stan")
    public String getStan() {
        return stan;
    }

    public void setStan(String stan) {
        this.stan = stan;
    }

    @Column(nullable=false, name="create_dt")
    public Date getCreateDt() {
        return createDt;
    }

    public void setCreateDt(Date createDt) {
        this.createDt = createDt;
    }

    @ManyToOne
    @JoinColumn(name="loyalty_file_id", nullable=true)
    public LoyaltyFile getLoyaltyFile() {
        return loyaltyFile;
    }

    public void setLoyaltyFile(LoyaltyFile loyaltyFile) {
        this.loyaltyFile = loyaltyFile;
    }


    @Column(nullable=true, name="job_no")
    public String getJobNo() {
        return jobNo;
    }

    public void setJobNo(String jobNo) {
        this.jobNo = jobNo;
    }

    @Column(nullable=true, name="company_code")
    public String getCoyCode() {
        return coyCode;
    }

    public void setCoyCode(String coyCode) {
        this.coyCode = coyCode;
    }
}
