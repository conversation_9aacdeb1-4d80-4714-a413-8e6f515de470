package com.abl.db.model;

import org.hibernate.annotations.ForeignKey;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_EZL_WC_TXN_LOG")
public class EzlWcTxnLog implements Serializable {

	public enum Status {
		NEW,
		SUCCESS,
		FAIL
	}

	public enum Source {
		LES
	}

	public static final String MSG_TYPE_BL_CHECK="bl_check";
	public static final String MSG_TYPE_UNKNOWN="unknown";
	
	private Long id;
	private Source source=Source.LES;
	private Date dateTime;
	private String mti; // MTI of ISO msg
	private String procCode; // proc code of ISO msg
	private Long txnAmt;
	private String stan;			// P-11
	private String txnTime;			// P-12 txn time in request msg (hhmmss)
	private String txnDate;			// P-13 txn date in request msg (mmdd)
	private String tid;				// P-41
	private String mid;				// P-42
	private Status status = Status.NEW;
	private String txResult;
	private String responseCode;
	private String rrn;
	private String posCondCode;
	private String authCode;
	private String msgType;
	private EzlTxnLog ezlTxnLog;
	private String validationValues;	// f60 data in request msg


	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_EZL_WC_TXN_LOG")
	@SequenceGenerator(name = "SEQGEN_EZL_WC_TXN_LOG", sequenceName = "TMSQ_EZL_WC_TXN_LOG")
	@Column(name = "id")
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Enumerated(EnumType.STRING)
	@Column(name = "source", length=32)
	public Source getSource() {
		return source;
	}

	public void setSource(Source source) {
		this.source = source;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "date_time")
	public Date getDateTime() {
		return dateTime;
	}

	public void setDateTime(Date dateTime) {
		this.dateTime = dateTime;
	}

	@Column(name = "mti", length = 4)
	public String getMti() {
		return mti;
	}

	public void setMti(String mti) {
		this.mti = mti;
	}

	@Column(name = "proc_code", length = 6)
	public String getProcCode() {
		return procCode;
	}

	public void setProcCode(String procCode) {
		this.procCode = procCode;
	}

	@Column(name = "tid", length = 8)
	public String getTid() {
		return tid;
	}

	public void setTid(String tid) {
		this.tid = tid;
	}

	@Column(name = "txn_amount")
	public Long getTxnAmt() {
		return txnAmt;
	}

	public void setTxnAmt(Long txnAmt) {
		this.txnAmt = txnAmt;
	}

	@Column(name = "stan", length = 6)
	public String getStan() {
		return stan;
	}

	public void setStan(String stan) {
		this.stan = stan;
	}

	@Column(name = "txn_time", length=6)
	public String getTxnTime() {
		return txnTime;
	}

	public void setTxnTime(String txnTime) {
		this.txnTime = txnTime;
	}
	
	@Column(name = "txn_date", length=4)
	public String getTxnDate() {
		return txnDate;
	}

	public void setTxnDate(String txnDate) {
		this.txnDate = txnDate;
	}

	@Column(name = "mid", length = 15)
	public String getMid() {
		return mid;
	}

	public void setMid(String mid) {
		this.mid = mid;
	}
	
	@Enumerated(EnumType.STRING)
	@Column(name = "status", length=12, nullable=false)
	public Status getStatus() {
		return status;
	}

	public void setStatus(Status status) {
		this.status = status;
	}
	
	@Column(name = "tx_result", length=32)
	public String getTxResult() {
		return txResult;
	}

	public void setTxResult(String txResult) {
		this.txResult = txResult;
	}

	@Column(name = "resp_code", length = 2)
	public String getResponseCode() {
		return responseCode;
	}

	public void setResponseCode(String responseCode) {
		this.responseCode = responseCode;
	}
	
	@Column(name = "rrn", length=32)
	public String getRrn() {
		return rrn;
	}

	public void setRrn(String rrn) {
		this.rrn = rrn;
	}

	@Column(name = "msg_type", length=20)
	public String getMsgType() {
		return msgType;
	}

	public void setMsgType(String msgType) {
		this.msgType = msgType;
	}


	@Column(name = "pos_cond_code", length=8)
	public String getPosCondCode() {
		return posCondCode;
	}

	public void setPosCondCode(String posCondCode) {
		this.posCondCode = posCondCode;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ezl_txn_log_id")
    @ForeignKey(name = "TMFC_LOG_ID_EZL_WC_TXN_LOG")
	public EzlTxnLog getEzlTxnLog() {
		return ezlTxnLog;
	}

	public void setEzlTxnLog(EzlTxnLog ezlTxnLog) {
		this.ezlTxnLog = ezlTxnLog;
	}


	@Column(name = "validation_val", length=300)
	public String getValidationValues() {
		return validationValues;
	}

	public void setValidationValues(String validationValues) {
		this.validationValues = validationValues;
	}

	@Column(name = "auth_code", length = 6)
	public String getAuthCode() {
		return authCode;
	}

	public void setAuthCode(String authCode) {
		this.authCode = authCode;
	}


}
