package com.abl.db.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@SuppressWarnings("serial")
@Entity
@Table(name = "CN2_V_TMS_IVD")
public class Ivd implements Serializable {
	private String firmwareVersion;

	@Id
	@Column(name = "firmware_ver", nullable = false, length = 3)
	public String getFirmwareVersion() {
		return firmwareVersion;
	}

	public void setFirmwareVersion(String firmwareVersion) {
		this.firmwareVersion = firmwareVersion;
	}

}
