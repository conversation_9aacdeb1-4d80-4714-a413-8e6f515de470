package com.abl.db.model;

import javax.persistence.Embeddable;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;

import org.hibernate.annotations.ForeignKey;

import java.io.Serializable;

@SuppressWarnings("serial")
@Embeddable
public class ProfileFeeValueId implements Serializable {

    private ProfileFee profileFee;
    private ParameterDefinition parameterDefinition;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @ForeignKey(name="TMFC_PROFF_ID_PROFFV") 
    public ProfileFee getProfileFee() {
        return profileFee;
    }

    public void setProfileFee(ProfileFee profileFee) {
        this.profileFee = profileFee;
    }

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @ForeignKey(name="TMFC_PARAM_DEF_ID_PROFFV") 
    public ParameterDefinition getParameterDefinition() {
        return parameterDefinition;
    }

    public void setParameterDefinition(ParameterDefinition parameterDefinition) {
        this.parameterDefinition = parameterDefinition;
    }

    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;

        ProfileFeeValueId that = (ProfileFeeValueId) o;

        if (profileFee != null ? !profileFee
                .equals(that.profileFee) : that.profileFee != null)
            return false;
        if (parameterDefinition != null ? !parameterDefinition.equals(that.parameterDefinition)
                : that.parameterDefinition != null)
            return false;

        return true;
    }

    public int hashCode() {
        int result;
        result = (profileFee != null ? profileFee.hashCode() : 0);
        result = 31 * result
                + (parameterDefinition != null ? parameterDefinition.hashCode() : 0);
        return result;
    }

}
