package com.abl.db.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_TXN_RESULT")
public class TxnResult implements Serializable {

    public static final String APPROVED = "0000";                 // aprpoved
    public static final String ERROR = "0001";                    // general error

    // system errors
    // -------------
    public static final String SYSERR = "1000";                   // other system error
    public static final String SYSERR_HSM = "1001";               // hsm error (e.g. unable to communicate with hsm)
    public static final String SYSERR_COMM = "1002";              // communications error


    // request related errors
    // ----------------------
    public static final String ERROR_FORMAT_ERROR = "2001";       // request format error (e.g. missing fields)

    // acquirer/terminal related errors
    // --------------------------------
    public static final String ERROR_INV_TERM = "3001";           // terminal does not exist, or is invalid
    public static final String ERROR_INV_MODEL = "3002";          // Model does not exist, or is invalid
    public static final String ERROR_INV_REQ_DATA = "3003";       // invalid request data (e.g. invalid application name / version)
    public static final String ERROR_NO_PROFILE = "3004";         // terminal profile not found
    public static final String ERROR_NO_DOWNLOAD = "3005";        // No download required
    public static final String ERROR_APPLICATION_NOT_FOUND = "3006";     // application not found
    public static final String ERROR_OUT_OF_TIME_WINDOW = "3007"; // out of download job time window
    public static final String ERROR_JOB_SUSPENDED = "3008";      // all jobs are either cancelled or suspended
    public static final String ERROR_UPLOAD_SUSPENDED = "3009";   // TmsTerminal upload log is suspended

    public static final String ERROR_TMS_ERROR = "3010";          // TMS Error
    public static final String ERROR_NO_GROUP = "3011";           // No group found for this terminal
    public static final String ERROR_PROFILE_NOT_VALID = "3012";  // Profile either deleted or not valid yet
    public static final String ERROR_MIN_VERSION_NOT_MET = "3013";// Minimum version of release is not met
    public static final String ERROR_INV_VEHICLE = "3014";        // Minimum version of release is not met
    public static final String ERROR_INV_FILE_NAME = "3015";      // Invalid file name
    public static final String ERROR_INV_VERSION = "3016";        // Invalid version
    public static final String ERROR_INV_FILE_NO = "3017";        // Invalid file number
    
    //MasterPass
    public static final String FAILED = "4001"; 
    public static final String TIMEOUT= "4002";
    public static final String HTTP_ERR = "4003";
    public static final String SYS_MALFUNCTION = "4004";
   
    public static final String ERROR_ADMIN_FEE_NULL = "4005"; 
    public static final String ERROR_COMP_CODE_NULL= "4006";
    public static final String ERROR_DRIVER_ID_NULL = "4007";
    public static final String ERROR_FARE_AMT_NULL = "4008";
    public static final String ERROR_GST_AMT_NULL = "4009";
    public static final String ERROR_JOB_NO_NULL = "4010";
    public static final String ERROR_TAXI_NO_NULL = "4011";
    public static final String ERROR_TXN_AMT_NULL = "4012";
    public static final String DUPLICATE_TXN = "4013";
    public static final String TXN_NOT_FOUND = "4014";
    
    

    private String code;
    private String description;
    private String responseCode;

    @Id
    @Column(name = "code")
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Column(name = "description", length = 50)
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Column(name = "response_code", length = 3)
    public String getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(String responseCode) {
        this.responseCode = responseCode;
    }
}
