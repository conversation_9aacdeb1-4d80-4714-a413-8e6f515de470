package com.abl.db.model;

import javax.persistence.*;

import org.hibernate.annotations.ForeignKey;

@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_TERMINAL_PROFILE_FILE")
public class TerminalProfileFile {

    private long id;
    private int recordNo;
    private String data;
    private TerminalProfile terminalProfile;

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_TERMINAL_PROFILE_FILE")
    @SequenceGenerator(name = "SEQGEN_TERMINAL_PROFILE_FILE", sequenceName = "TMSQ_TERMINAL_PROFILE_FILE")
    @Column(name = "id")
    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    @Column(name = "record_no", nullable = false)
    public int getRecordNo() {
        return recordNo;
    }

    public void setRecordNo(int recordNo) {
        this.recordNo = recordNo;
    }

    @Column(name = "data", nullable = false, length = 4000)
    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "terminal_profile_id")
    @ForeignKey(name="TMFC_TERM_PRO_ID_TERM_PRO_FILE") 
    public TerminalProfile getTerminalProfile() {
        return terminalProfile;
    }

    public void setTerminalProfile(TerminalProfile terminalProfile) {
        this.terminalProfile = terminalProfile;
    }
}