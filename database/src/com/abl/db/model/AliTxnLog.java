package com.abl.db.model;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_ALI_TXN_LOG")
public class AliTxnLog implements Serializable {

	public enum Source {
		DCP,
		LES
	}

	public enum Status {
		NEW,
		PENDING, // status set by server, batch will pick up to process to process offline payment/ preauth cancel/ preauth reversal
		PROCESSING, // status set by batch when it is processing offline payment/preauth cancel/preauth reversal
		SUCCESS,
		FAIL
	}

	public enum DcpStatus {
		NEW,		// default value
		PENDING, // retry declined payment job will pick up to send to dcp for the declined txns
		PROCESSING,	// status set by declined payment batch when it is processing declined txns
		SUCCESS, // already successfully sent to DCP. No retry is needed
		FAIL	// already sent to DCP for x hours, no retry is needed
	}

	public enum H5Status {
		NEW,		// default value
		PENDING, // retry payment job will pick up to send to H5 for the  payment txns
		PROCESSING,	// status set by payment batch when it is processing payment txns
		SUCCESS, // already successfully sent to H5. No retry is needed
		FAIL	// already sent to H5 for x hours, no retry is needed
	}
	
	public static final String MSG_TYPE_PREAUTH="cdg_ali_preauth";
	public static final String MSG_TYPE_PREAUTH_QUERY="cdg_ali_preauth_query";
	public static final String MSG_TYPE_PREAUTH_REVERSAL="cdg_ali_preauth_reversal";
	public static final String MSG_TYPE_PREAUTH_CANCEL="cdg_ali_preauth_cancel";
	public static final String MSG_TYPE_OFFLINE_PAYMENT="cdg_ali_payment";
	public static final String MSG_TYPE_UNKNOWN="cdg_ali_unknown";
	
	private Long id;
	private Source source= Source.LES;
	private boolean online=true;	// false=offline
	private Date dateTime;			// date/time txn is received
	private String mti; // MTI of ISO msg
	private String procCode; // proc code of ISO msg
	private Long txnAmt;
	private String stan;
	private String txnTime;			// P-12 time in request msg (hhmmss)
	private String txnDate;			// P-13 date in request msg (mmdd)
	private String tid;
	private String mid;
	private Status status = Status.NEW;
	private String txResult;	// internal result code; changed when status is changed
	private String respCode;
	private String jobNumber;
	private String taxiNo;
	private String driverId;
	private Long fareAmt;
	private Long gst;
	private Long fareAdmin;
	private String companyCode;
	private String pinpadSn;
	private String rrn;
	private String authCode;
	private String msgType;
	private String msgVer;			// only applicable for DCP msg
	private String transId;			// only applicable for DCP msg
	private String userId;
	private String appId;
	private String bookingRef; // only applicable for DCP msg
	private String origBookingRef; // // only applicable for DCP msg, to store the booking ref  from dcp during preauth query/reversal/cancellation
	private String remark; // only applicable for DCP msg during preauth cancellation
	private DcpStatus dcpStatus = DcpStatus.NEW;
	private Date lastSentDcpDateTime;			// last sent to dcp
	private Integer dcpTryCount = 0; // number to retry sent to dcp
	private String authNo;
	private Date lastSentToAlipay;			// last date/time txn  sent
	private H5Status h5Status = H5Status.NEW;
	private Date lastSentH5DateTime;			// last sent to dcp
	private Integer h5TryCount = 0; // number to retry sent to dcp
	
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_ALI_TXN_LOG")
	@SequenceGenerator(name = "SEQGEN_ALI_TXN_LOG", sequenceName = "TMSQ_ALI_TXN_LOG")
	@Column(name = "id")
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Enumerated(EnumType.STRING)
	@Column(name = "source", length=32)
	public Source getSource() {
		return source;
	}

	public void setSource(Source source) {
		this.source = source;
	}

	@Column(name = "is_online", nullable=false)
	public boolean isOnline() {
		return online;
	}

	public void setOnline(boolean online) {
		this.online = online;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "date_time", nullable=false)
	public Date getDateTime() {
		return dateTime;
	}

	public void setDateTime(Date dateTime) {
		this.dateTime = dateTime;
	}

	@Column(name = "mti", length = 4)
	public String getMti() {
		return mti;
	}

	public void setMti(String mti) {
		this.mti = mti;
	}

	@Column(name = "proc_code", length = 6)
	public String getProcCode() {
		return procCode;
	}

	public void setProcCode(String procCode) {
		this.procCode = procCode;
	}

	@Column(name = "tid", length = 8)
	public String getTid() {
		return tid;
	}

	public void setTid(String tid) {
		this.tid = tid;
	}

	@Column(name = "txn_amount")
	public Long getTxnAmt() {
		return txnAmt;
	}

	public void setTxnAmt(Long txnAmt) {
		this.txnAmt = txnAmt;
	}

	@Column(name = "stan", length = 6)
	public String getStan() {
		return stan;
	}

	public void setStan(String stan) {
		this.stan = stan;
	}

	@Column(name = "txn_time", length=6)
	public String getTxnTime() {
		return txnTime;
	}

	public void setTxnTime(String txnTime) {
		this.txnTime = txnTime;
	}
	
	@Column(name = "txn_date", length=6)
	public String getTxnDate() {
		return txnDate;
	}

	public void setTxnDate(String txnDate) {
		this.txnDate = txnDate;
	}
	
	@Column(name = "mid", length = 15)
	public String getMid() {
		return mid;
	}

	public void setMid(String mid) {
		this.mid = mid;
	}

	@Enumerated(EnumType.STRING)
	@Column(name = "status", length=12, nullable=false)
	public Status getStatus() {
		return status;
	}

	public void setStatus(Status status) {
		this.status = status;
	}

	@Column(name = "tx_result", length=32)
	public String getTxResult() {
		return txResult;
	}

	public void setTxResult(String txResult) {
		this.txResult = txResult;
	}

	@Column(name = "resp_code", length = 2)
	public String getRespCode() {
		return respCode;
	}

	public void setRespCode(String respCode) {
		this.respCode = respCode;
	}

	@Column(name = "job_number", length = 10)
	public String getJobNumber() {
		return jobNumber;
	}

	public void setJobNumber(String jobNumber) {
		this.jobNumber = jobNumber;
	}

	@Column(name = "taxi_no", length = 12)
	public String getTaxiNo() {
		return taxiNo;
	}

	public void setTaxiNo(String taxiNo) {
		this.taxiNo = taxiNo;
	}

	@Column(name = "driver_id", length = 9)
	public String getDriverId() {
		return driverId;
	}

	public void setDriverId(String driverId) {
		this.driverId = driverId;
	}

	@Column(name = "fare_amt")
	public Long getFareAmt() {
		return fareAmt;
	}

	public void setFareAmt(Long fareAmt) {
		this.fareAmt = fareAmt;
	}

	@Column(name = "fare_gst")
	public Long getGst() {
		return gst;
	}

	public void setGst(Long gst) {
		this.gst = gst;
	}

	@Column(name = "fare_admin")
	public Long getFareAdmin() {
		return fareAdmin;
	}

	public void setFareAdmin(Long fareAdmin) {
		this.fareAdmin = fareAdmin;
	}


	@Column(name = "company_code", length = 4)
	public String getCompanyCode() {
		return companyCode;
	}

	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}


	@Column(name = "pinpad_sn", length = 20)
	public String getPinpadSn() {
		return pinpadSn;
	}

	public void setPinpadSn(String pinpadSn) {
		this.pinpadSn = pinpadSn;
	}
	
	@Column(name = "rrn", length=24)
	public String getRrn() {
		return rrn;
	}

	public void setRrn(String rrn) {
		this.rrn = rrn;
	}

	@Column(name = "auth_code", length=16)
	public String getAuthCode() {
		return authCode;
	}

	public void setAuthCode(String authCode) {
		this.authCode = authCode;
	}
	
	@Column(name = "msg_type", length=64 )
	public String getMsgType() {
		return msgType;
	}

	public void setMsgType(String msgType) {
		this.msgType = msgType;
	}

	@Column(name = "msg_ver", length=16 )
	public String getMsgVer() {
		return msgVer;
	}

	public void setMsgVer(String msgVer) {
		this.msgVer = msgVer;
	}

	@Column(name = "trans_id", length=64 )
	public String getTransId() {
		return transId;
	}

	public void setTransId(String transId) {
		this.transId = transId;
	}

	@Column(name = "user_id")
	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	@Enumerated(EnumType.STRING)
	@Column(name = "dcp_status", length=12, nullable=false)
	public DcpStatus getDcpStatus() {
		return dcpStatus;
	}

	public void setDcpStatus(DcpStatus dcpStatus) {
		this.dcpStatus = dcpStatus;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "last_sent_to_dcp", nullable=true)
	public Date getLastSentDcpDateTime() {
		return lastSentDcpDateTime;
	}

	public void setLastSentDcpDateTime(Date lastSentDcpDateTime) {
		this.lastSentDcpDateTime = lastSentDcpDateTime;
	}

	@Column(name = "dcp_try_count")
	public Integer getDcpTryCount() {
		return dcpTryCount;
	}

	public void setDcpTryCount(Integer dcpTryCount) {
		this.dcpTryCount = dcpTryCount;
	}

	@Column(name = "app_id", length=32 )
	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	@Column(name = "booking_ref", length=50 )
	public String getBookingRef() {
		return bookingRef;
	}

	public void setBookingRef(String bookingRef) {
		this.bookingRef = bookingRef;
	}

	@Column(name = "orig_booking_ref", length=50 )
	public String getOrigBookingRef() {
		return origBookingRef;
	}

	public void setOrigBookingRef(String origBookingRef) {
		this.origBookingRef = origBookingRef;
	}

	@Column(name = "remark", length=100 )
	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	@Column(name = "auth_no", length=80)
	public String getAuthNo() {
		return authNo;
	}

	public void setAuthNo(String authNo) {
		this.authNo = authNo;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "last_sent_to_alipay", nullable=true)
	public Date getLastSentToAlipay() {
		return lastSentToAlipay;
	}

	public void setLastSentToAlipay(Date lastSentToAlipay) {
		this.lastSentToAlipay = lastSentToAlipay;
	}


	@Enumerated(EnumType.STRING)
	@Column(name = "h5_status", length=12, nullable=false)
	public H5Status getH5Status() {
		return h5Status;
	}

	public void setH5Status(H5Status h5Status) {
		this.h5Status = h5Status;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "last_sent_to_h5", nullable=true)
	public Date getLastSentH5DateTime() {
		return lastSentH5DateTime;
	}

	public void setLastSentH5DateTime(Date lastSentH5DateTime) {
		this.lastSentH5DateTime = lastSentH5DateTime;
	}

	@Column(name = "h5_try_count")
	public Integer getH5TryCount() {
		return h5TryCount;
	}

	public void setH5TryCount(Integer h5TryCount) {
		this.h5TryCount = h5TryCount;
	}
}
