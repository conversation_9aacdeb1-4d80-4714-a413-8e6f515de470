package com.abl.db.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

/**
 * This is a view, getting terminal application download summary
 * 
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "TMVW_TERM_APP_DOWNLOAD_SUMMARY")
public class ViewTerminalApplicationDownloadSummary implements Serializable {

	private long id;
	private long appDownloadJobId;
	private String startWindow;
	private String endWindow;
	private int concurrentDownload;
	private Date creationDate;
	private long releaseId;
	private String description;
	private String version;
	private long modelId;
	private String modelName;
	private short jobStatus;
	// private short downloadStatus;
	private int completed;
	private int pending;
	private int notStarted;
	private boolean deleted;
	private String padVersion;
	private long groupId;
	private Date startDate;

	@Id
	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	@Column(name = "job_id")
	public long getAppDownloadJobId() {
		return appDownloadJobId;
	}

	public void setAppDownloadJobId(long appDownloadJobId) {
		this.appDownloadJobId = appDownloadJobId;
	}

	@Column(name = "start_window", nullable = false, length = 4)
	public String getStartWindow() {
		return startWindow;
	}

	public void setStartWindow(String startWindow) {
		this.startWindow = startWindow;
	}

	@Column(name = "end_window", nullable = false, length = 4)
	public String getEndWindow() {
		return endWindow;
	}

	public void setEndWindow(String endWindow) {
		this.endWindow = endWindow;
	}

	@Column(name = "concurrent_download", nullable = false)
	public int getConcurrentDownload() {
		return concurrentDownload;
	}

	public void setConcurrentDownload(int concurrentDownload) {
		this.concurrentDownload = concurrentDownload;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "creation_date")
	public Date getCreationDate() {
		return creationDate;
	}

	public void setCreationDate(Date creationDate) {
		this.creationDate = creationDate;
	}

	@Column(name = "version")
	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	@Column(name = "job_status")
	public short getJobStatus() {
		return jobStatus;
	}

	public void setJobStatus(short jobStatus) {
		this.jobStatus = jobStatus;
	}

	// @Column(name = "download_status")
	// public short getDownloadStatus() {
	// return downloadStatus;
	// }
	//
	// public void setDownloadStatus(short downloadStatus) {
	// this.downloadStatus = downloadStatus;
	// }

	@Column(name = "completed")
	public int getCompleted() {
		return completed;
	}

	public void setCompleted(int completed) {
		this.completed = completed;
	}

	@Column(name = "pending")
	public int getPending() {
		return pending;
	}

	public void setPending(int pending) {
		this.pending = pending;
	}

	@Column(name = "not_started")
	public int getNotStarted() {
		return notStarted;
	}

	public void setNotStarted(int notStarted) {
		this.notStarted = notStarted;
	}

	@Column(name = "model_id")
	public long getModelId() {
		return modelId;
	}

	public void setModelId(long modelId) {
		this.modelId = modelId;
	}

	@Column(name = "model_name", nullable = false, length = 50)
	public String getModelName() {
		return modelName;
	}

	public void setModelName(String modelName) {
		this.modelName = modelName;
	}

	@Column(name = "deleted")
	public boolean isDeleted() {
		return deleted;
	}

	public void setDeleted(boolean deleted) {
		this.deleted = deleted;
	}

	@Column(name = "release_id")
	public long getReleaseId() {
		return releaseId;
	}

	public void setReleaseId(long releaseId) {
		this.releaseId = releaseId;
	}

	@Column(name = "description")
	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	@Column(name = "group_id")
	public long getGroupId() {
		return groupId;
	}

	public void setGroupId(long groupId) {
		this.groupId = groupId;
	}

	@Column(name = "start_date")
	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	@Transient
	public String getPadVersion() {

		return String.format("%06d", Integer.parseInt(getVersion()));
	}

	public void setPadVersion(String padVersion) {
		this.padVersion = padVersion;
	}

}
