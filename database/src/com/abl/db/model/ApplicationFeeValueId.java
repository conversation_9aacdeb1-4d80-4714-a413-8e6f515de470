package com.abl.db.model;

import javax.persistence.Embeddable;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;

import org.hibernate.annotations.ForeignKey;
import org.springframework.util.ObjectUtils;

import java.io.Serializable;

@SuppressWarnings("serial")
@Embeddable
public class ApplicationFeeValueId implements Serializable {
    private ApplicationFee applicationFee;
    private ParameterDefinition parameterDefinition;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @ForeignKey(name="TMFC_APPF_ID_APPFV") 
    public ApplicationFee getApplicationFee() {
        return applicationFee;
    }

    public void setApplicationFee(ApplicationFee applicationFee) {
        this.applicationFee = applicationFee;
    }


    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @ForeignKey(name="TMFC_PARAM_DEF_ID_APPFV")
    public ParameterDefinition getParameterDefinition() {
        return parameterDefinition;
    }

    public void setParameterDefinition(ParameterDefinition parameterDefinition) {
        this.parameterDefinition = parameterDefinition;
    }

    
		@Override
		public boolean equals(Object obj) {
			if (obj == this) {
				return true;
			} else if (obj instanceof ApplicationFeeValueId) {
				ApplicationFeeValueId oth = (ApplicationFeeValueId) obj;
				return ObjectUtils.nullSafeEquals(getApplicationFee(), oth.getApplicationFee())
						&& ObjectUtils.nullSafeEquals(getParameterDefinition(), oth.getParameterDefinition());
			}
			return false;
		}
		
		@Override
		public int hashCode() {
			int hash = ObjectUtils.nullSafeHashCode(getApplicationFee());
			hash = 31 * hash + ObjectUtils.nullSafeHashCode(getParameterDefinition());
			return hash;
		}
}
