package com.abl.db.model;

import javax.persistence.*;

import org.hibernate.annotations.ForeignKey;

import java.io.Serializable;
import java.util.Date;

/**
 * Abstract class containing the common fields for maker/checker
 */
@SuppressWarnings("serial")
@MappedSuperclass
public abstract class Maker<PERSON>he<PERSON> implements Serializable {

    public static final int STATUS_PENDING = 0;
    public static final int STATUS_APPROVED = 1;
    public static final int STATUS_REJECTED = 2;

    private AdminUser maker;
    private AdminUser checker;
    private Domain domain;
    private Date makerDateTime;
    private Date checkerDateTime;
    private String makerComments;
    private String checkerComments;
    private Integer status = STATUS_PENDING;
    private String checkData;

    public abstract boolean verifyCheckData();

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "maker_id")
    @ForeignKey(name="TMFC_MAKER_ID_MCHECKER") 
    public AdminUser getMaker() {
        return maker;
    }

    public void setMaker(AdminUser maker) {
        this.maker = maker;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "checker_id")
    @ForeignKey(name="TMFC_CHECKER_ID_MCHECKER") 
    public AdminUser getChecker() {
        return checker;
    }

    public void setChecker(AdminUser checker) {
        this.checker = checker;
    }

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "domain_id")
    @ForeignKey(name="TMFC_DOMAIN_ID_MCHECKER") 
    public Domain getDomain() {
        return domain;
    }

    public void setDomain(Domain domain) {
        this.domain = domain;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "maker_date_time", nullable = false)
    public Date getMakerDateTime() {
        return makerDateTime;
    }

    public void setMakerDateTime(Date makerDateTime) {
        this.makerDateTime = makerDateTime;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "checker_date_time")
    public Date getCheckerDateTime() {
        return checkerDateTime;
    }

    public void setCheckerDateTime(Date checkerDateTime) {
        this.checkerDateTime = checkerDateTime;
    }

    @Column(name = "maker_comments")
    public String getMakerComments() {
        return makerComments;
    }

    public void setMakerComments(String makerComments) {
        this.makerComments = makerComments;
    }

    @Column(name = "checker_comments")
    public String getCheckerComments() {
        return checkerComments;
    }

    public void setCheckerComments(String checkerComments) {
        this.checkerComments = checkerComments;
    }

    @Column(name = "status", nullable = false)
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Transient
    public boolean isApproved() {
        return (status != null) && (status == STATUS_APPROVED);
    }

    @Transient
    public boolean isRejected() {
        return (status != null) && (status == STATUS_REJECTED);
    }

    @Transient
    public boolean isPending() {
        return (status != null) && (status == STATUS_PENDING);
    }

    @Column(name = "check_data", nullable = false)
    public String getCheckData() {
        return checkData;
    }

    public void setCheckData(String checkData) {
        this.checkData = checkData;
    }

}
