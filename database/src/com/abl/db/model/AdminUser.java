package com.abl.db.model;

import javax.persistence.*;

import org.hibernate.annotations.ForeignKey;
import org.hibernate.annotations.Type;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * User entity
 * <p/>
 * User implements UserDetails for integration with Spring Security framework.
 */
@Entity
@Table(name = "TMTB_ADM_USR")
@SuppressWarnings("serial")
public class AdminUser implements Serializable, Cloneable {

    public static final int STATUS_ACTIVE = 1;
    public static final int STATUS_LOCKED = 0;

    public static final int TYPE_NORMAL = 0;

    private Long id;
    private String login;
    private String name;
    private String password;                // hashed password
    private Date passwordChangeDate = null;    // date when password was last changed by user
    // this field is only null for new users
    private Date passwordExpiry = null;        // password expires at end of this day
    // if null, then password does not expires
    private Boolean passwordReset = true;        // set to true if password has been reset (by superadmin),
    // or user is newly created

    private Boolean administrator = false;    // true if is administrator
    private Integer type = TYPE_NORMAL;        // user type
    private Integer status = STATUS_ACTIVE;    // user account status
    private Date statusDateTime = new Date();    // date/time of status change
    private Date createDateTime = new Date();    // date/time of user creation
    private Date deleteDateTime;            // date/time of user deletion
    private Boolean deleted = false;            // true means user is deleted.  Most dao methods should not return or modify
    // deleted users
    private Boolean passwordLocked = false;    // true means password is locked. This flag should be set when checking
    // loginFailCount
    private Short loginFailCount = 0;
    private Date loginFailDateTime;
    private Date loginDateTime;
    private Date lastLoginDateTime;
    private Domain domain = null;                // links user to domain; if null it means user not linked to any domain
    private String comments;                // comments
    private Set<AccessProfile> accessProfiles;

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_ADMIN_USER")
    @SequenceGenerator(name = "SEQGEN_ADMIN_USER", sequenceName = "TMSQ_ADMIN_USER")
    @Column(name = "id")
    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "login", nullable = false, unique = true, length = 20)
    public String getLogin() {
        return this.login;
    }

    public void setLogin(String adminLogin) {
        this.login = adminLogin;
    }

    @Column(name = "name", nullable = false, length = 80)
    public String getName() {
        return name;
    }

    public void setName(String adminName) {
        this.name = adminName;
    }

    @Column(name = "password", length = 80, nullable = false)
    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    @Column(name = "password_locked", nullable = false)
    public Boolean getPasswordLocked() {
        return passwordLocked;
    }

    public void setPasswordLocked(Boolean passwordLocked) {
        this.passwordLocked = passwordLocked;
    }

    @Column(name = "login_fail_count", nullable = false)
    public Short getLoginFailCount() {
        return loginFailCount;
    }

    public void setLoginFailCount(Short loginFailCount) {
        this.loginFailCount = loginFailCount;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "login_fail_date_time")
    public Date getLoginFailDateTime() {
        return loginFailDateTime;
    }

    public void setLoginFailDateTime(Date loginFailDateTime) {
        this.loginFailDateTime = loginFailDateTime;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "login_date_time")
    public Date getLoginDateTime() {
        return loginDateTime;
    }

    public void setLoginDateTime(Date loginDateTime) {
        this.loginDateTime = loginDateTime;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "last_login_date_time")
    public Date getLastLoginDateTime() {
        return lastLoginDateTime;
    }

    public void setLastLoginDateTime(Date lastLoginDateTime) {
        this.lastLoginDateTime = lastLoginDateTime;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "password_change_date")
    public Date getPasswordChangeDate() {
        return passwordChangeDate;
    }

    public void setPasswordChangeDate(Date passwordChangeDate) {
        this.passwordChangeDate = passwordChangeDate;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "password_expiry")
    public Date getPasswordExpiry() {
        return passwordExpiry;
    }

    public void setPasswordExpiry(Date passwordExpiry) {
        this.passwordExpiry = passwordExpiry;
    }

    @Column(name = "password_reset", nullable = false)
    public Boolean getPasswordReset() {
        return passwordReset;
    }

    public void setPasswordReset(Boolean passwordReset) {
        this.passwordReset = passwordReset;
    }

    @Column(name = "administrator", nullable = false)
    public boolean isAdministrator() {
        return administrator;
    }

    public void setAdministrator(Boolean administrator) {
        this.administrator = administrator;
    }

    @Column(name = "user_type", nullable = false)
    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    @Column(name = "status", nullable = false)
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "status_date_time", nullable = false)
    public Date getStatusDateTime() {
        return statusDateTime;
    }

    public void setStatusDateTime(Date statusDateTime) {
        this.statusDateTime = statusDateTime;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "create_date_time", nullable = false)
    public Date getCreateDateTime() {
        return createDateTime;
    }

    public void setCreateDateTime(Date createDateTime) {
        this.createDateTime = createDateTime;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "delete_date_time")
    public Date getDeleteDateTime() {
        return deleteDateTime;
    }

    public void setDeleteDateTime(Date deleteDateTime) {
        this.deleteDateTime = deleteDateTime;
    }

    @Type(type="boolean")
    @Column(name = "deleted", columnDefinition = "int default 0", nullable = false)
    public Boolean isDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "domain_id")
    @ForeignKey(name="TMFC_DOMAIN_ID_ADM_USR") 
    public Domain getDomain() {
        return domain;
    }

    public void setDomain(Domain domain) {
        this.domain = domain;
    }

    @Column(name = "comments")
    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    @ManyToMany(targetEntity = AccessProfile.class, fetch = FetchType.LAZY)
    @ForeignKey(name = "TMFC_ADM_USR_ID_ACC_PROFILE", inverseName = "TMFC_ACC_PROFILE_ID_ADM_USR")
    public Set<AccessProfile> getAccessProfiles() {
        return accessProfiles;
    }

    public void setAccessProfiles(Set<AccessProfile> accessProfiles) {
        this.accessProfiles = accessProfiles;
    }

    public AdminUser clone() throws CloneNotSupportedException {
        return (AdminUser) super.clone();
    }

    public String toString() {
        return "AdminUser(id=" + id + ", login=" + login + ", name=" + name + ", status=" + status + ")";
    }

}
