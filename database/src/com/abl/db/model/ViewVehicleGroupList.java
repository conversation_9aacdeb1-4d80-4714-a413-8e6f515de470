package com.abl.db.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * This is a view, to get vehicle grouping list
 * 
 */

@SuppressWarnings("serial")
@Entity
@Table(name = "TMVW_VEHICLE_GROUPING_LIST")
public class ViewVehicleGroupList {
	private String vehicleId;
	private long groupId;
	private String groupName;
	private Date assignDateTime;
	private String ivdModelId;
	private String firmwareVersion;
	private String vehicleType;

	
	@Id
	@Column(name = "vehicle_id")
	public String getVehicleId() {
		return vehicleId;
	}
	public void setVehicleId(String vehicleId) {
		this.vehicleId = vehicleId;
	}
	
	@Column(name = "group_id")
	public long getGroupId() {
		return groupId;
	}
	public void setGroupId(long groupId) {
		this.groupId = groupId;
	}
	
	@Column(name = "group_name")
	public String getGroupName() {
		return groupName;
	}
	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
    @Column(name = "assign_date_time")
	public Date getAssignDateTime() {
		return assignDateTime;
	}
	public void setAssignDateTime(Date assignDateTime) {
		this.assignDateTime = assignDateTime;
	}
	
	@Column(name = "ivd_model_id", length = 10)
	public String getIvdModelId() {
		return ivdModelId;
	}
	public void setIvdModelId(String ivdModelId) {
		this.ivdModelId = ivdModelId;
	}
	
	@Column(name = "firmware_ver", length = 3)
	public String getFirmwareVersion() {
		return firmwareVersion;
	}
	public void setFirmwareVersion(String firmwareVersion) {
		this.firmwareVersion = firmwareVersion;
	}
	
	@Column(name = "vehicle_type", length = 30)
	public String getVehicleType() {
		return vehicleType;
	}
	public void setVehicleType(String vehicleType) {
		this.vehicleType = vehicleType;
	}
	
	

}
