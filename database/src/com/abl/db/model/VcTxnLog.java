package com.abl.db.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.ForeignKey;
import org.hibernate.annotations.Index;


@SuppressWarnings("serial")
@Entity
@Table(name = "TMTB_CABC_TXN_LOG")
public class VcTxnLog implements Serializable{
	public enum Status {
		NEW, 
		SUCCESS, 
		FAILED,
		TIMEOUT,
		HTTP_ERR, 
		CONN_ERR, 
		WS_ERR,
		REQ_ERR,	
		PROCCESSING,
		QUEUE
	}
	
	public enum MsgType {
		SALES, 
		OFFLINE_SALES, 
		REVERSAL,
		VOID,
		VOID_REVERSAL,
		UNKNOWN
	}
	
	private Long id;
	private Date txnDateTime; // date/time txn is recorded
	private String mti; // MTI of ISO msg
	private String procCode; // proc code of ISO msg
	private Long txnAmt = 0L;
	private String stan;
	private String tid;
	private String mid;
	private String tripInfo; // field 48 in hex, exclude masked can and card expire 
	private String txnDetail = "NA";	//field 61 in hex
	private Status status = Status.NEW;
	private String responseCode;
	private String maskedCan;
	private String jobNumber = "0000000000";
	private TxnResult txnResult;
	private String dcpTxnState;
	private String dcpCode;
	private String dcpDesc;
	private String expiryDate;
	private String taxiNo = "NA";
	private String driverId = "NA";
	private Long fareAmt = 0L;
	private Long gst = 0L;
	private Long fareAdmin = 0L;
	private String companyCode = "NA";
	private String pinpadSn = "***********";
	private String dcpTxnId;
	private String dcpReqId;
	private String dcpJobNo;
	private String rrn;
	private String authCode;
	private boolean voided = Boolean.FALSE;
	private boolean reversed =Boolean.FALSE;
	private MsgType msgType = MsgType.UNKNOWN;
	private String txnDate;		//request from offline sales MMDD
	private String txnTime;		// request from offline sales HHMMSS
	private Date firstDcpCall;
	private Date lastDcpCall;
	private Integer retry =0;
	
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_CABC_TXN_LOG")
	@SequenceGenerator(name = "SEQGEN_CABC_TXN_LOG", sequenceName = "TMSQ_CABC_TXN_LOG")
	@Column(name = "CABC_TXN_LOG_id", columnDefinition = "int")
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	//@Temporal(TemporalType.TIMESTAMP)
	@Temporal(TemporalType.DATE)
	@Column(name = "txn_date_time")
	@Index(name="TMIX_CABC_TXN_LOG__TXN_DT_TM")
	public Date getTxnDateTime() {
		return txnDateTime;
	}

	public void setTxnDateTime(Date txnDateTime) {
		this.txnDateTime = txnDateTime;
	}

	@Column(name = "mti", length = 4)
	public String getMti() {
		return mti;
	}

	public void setMti(String mti) {
		this.mti = mti;
	}

	@Column(name = "proc_code", length = 6)
	public String getProcCode() {
		return procCode;
	}

	public void setProcCode(String procCode) {
		this.procCode = procCode;
	}

	@Column(name = "tid", length = 8)
	public String getTid() {
		return tid;
	}

	public void setTid(String tid) {
		this.tid = tid;
	}

	@Column(name = "txn_amount", nullable = false)
	public Long getTxnAmt() {
		return txnAmt;
	}

	public void setTxnAmt(Long txnAmt) {
		this.txnAmt = txnAmt;
	}

	@Column(name = "stan", length = 6)
	public String getStan() {
		return stan;
	}

	public void setStan(String stan) {
		this.stan = stan;
	}

	@Column(name = "mid", length = 15)
	public String getMid() {
		return mid;
	}

	public void setMid(String mid) {
		this.mid = mid;
	}

	@Enumerated(EnumType.STRING)
	@Column(name = "status", length=12, nullable = false )
	@Index(name="TMIX_CABC_TXN_LOG__STATUS")
	public Status getStatus() {
		return status;
	}

	public void setStatus(Status status) {
		this.status = status;
	}

	@Column(name = "resp_code", length = 2)
	@Index(name="TMIX_CABC_TXN_LOG__RESP_CODE")
	public String getResponseCode() {
		return responseCode;
	}

	public void setResponseCode(String responseCode) {
		this.responseCode = responseCode;
	}

	@Column(name = "masked_can", length = 19)
	public String getMaskedCan() {
		return maskedCan;
	}

	public void setMaskedCan(String maskedCan) {
		this.maskedCan = maskedCan;
	}

	@Column(name = "job_no", length = 10, nullable = false)
	@Index(name="TMIX_CABC_TXN_LOG__JOB_NO")
	public String getJobNumber() {
		return jobNumber;
	}

	public void setJobNumber(String jobNumber) {
		this.jobNumber = jobNumber;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "txn_result")
	@ForeignKey(name = "TMFC_RESULT_CODE_VC_TXN_LOG")
	public TxnResult getTxnResult() {
		return txnResult;
	}

	public void setTxnResult(TxnResult txnResult) {
		this.txnResult = txnResult;
	}

	
	@Column(name = "expiry_date", length = 10)
	public String getExpiryDate() {
		return expiryDate;
	}

	public void setExpiryDate(String expiryDate) {
		this.expiryDate = expiryDate;
	}

	@Column(name = "taxi_no", length = 12, nullable = false)
	@Index(name="TMIX_CABC_TXN_LOG__TAXI_NO")
	public String getTaxiNo() {
		return taxiNo;
	}

	public void setTaxiNo(String taxiNo) {
		this.taxiNo = taxiNo;
	}

	@Column(name = "driver_id", length = 9, nullable = false)
	public String getDriverId() {
		return driverId;
	}

	public void setDriverId(String driverId) {
		this.driverId = driverId;
	}

	@Column(name = "fare_amt", nullable = false)
	public Long getFareAmt() {
		return fareAmt;
	}

	public void setFareAmt(Long fareAmt) {
		this.fareAmt = fareAmt;
	}

	@Column(name = "fare_gst", nullable = false)
	public Long getGst() {
		return gst;
	}

	public void setGst(Long gst) {
		this.gst = gst;
	}

	@Column(name = "fare_admin", nullable = false)
	public Long getFareAdmin() {
		return fareAdmin;
	}

	public void setFareAdmin(Long fareAdmin) {
		this.fareAdmin = fareAdmin;
	}


	@Column(name = "company_code", length = 4, nullable = false)
	public String getCompanyCode() {
		return companyCode;
	}

	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}


	@Column(name = "pinpad_sn", length = 20, nullable = false)
	@Index(name="TMIX_CABC_TXN_LOG__PINPADP_SN")
	public String getPinpadSn() {
		return pinpadSn;
	}

	public void setPinpadSn(String pinpadSn) {
		this.pinpadSn = pinpadSn;
	}

	@Column(name = "dcp_txn_state")
	public String getDcpTxnState() {
		return dcpTxnState;
	}

	public void setDcpTxnState(String dcpTxnState) {
		this.dcpTxnState = dcpTxnState;
	}

	@Column(name = "dcp_code")
	public String getDcpCode() {
		return dcpCode;
	}

	public void setDcpCode(String dcpCode) {
		this.dcpCode = dcpCode;
	}

	@Column(name = "dcp_desc")
	public String getDcpDesc() {
		return dcpDesc;
	}

	public void setDcpDesc(String dcpDesc) {
		this.dcpDesc = dcpDesc;
	}

	@Column(name = "dcp_txn_id")
	public String getDcpTxnId() {
		return dcpTxnId;
	}

	public void setDcpTxnId(String dcpTxnId) {
		this.dcpTxnId = dcpTxnId;
	}

	@Column(name = "dcp_req_id")
	public String getDcpReqId() {
		return dcpReqId;
	}

	public void setDcpReqId(String dcpReqId) {
		this.dcpReqId = dcpReqId;
	}

	@Column(name = "dcp_job_no")
	public String getDcpJobNo() {
		return dcpJobNo;
	}

	public void setDcpJobNo(String dcpJobNo) {
		this.dcpJobNo = dcpJobNo;
	}

	@Column(name = "rrn")
	public String getRrn() {
		return rrn;
	}

	public void setRrn(String rrn) {
		this.rrn = rrn;
	}

	@Column(name = "auth_code")
	public String getAuthCode() {
		return authCode;
	}

	public void setAuthCode(String authCode) {
		this.authCode = authCode;
	}

	@Column(name = "trip_info", length=999 )
	public String getTripInfo() {
		return tripInfo;
	}

	public void setTripInfo(String data) {
		this.tripInfo = data;
	}

	@Column(name = "txn_detail", length=999, nullable = false )
	public String getTxnDetail() {
		return txnDetail;
	}

	public void setTxnDetail(String privateData) {
		this.txnDetail = privateData;
	}
	
	@Enumerated(EnumType.STRING)
	@Column(name = "msg_type", length=20, nullable = false )
	@Index(name="TMIX_CABC_TXN_LOG__MSG_TYPE")
	public MsgType getMsgType() {
		return msgType;
	}

	public void setMsgType(MsgType msgType) {
		this.msgType = msgType;
	}

	@Column(name = "voided" )
	public boolean isVoided() {
		return voided;
	}

	public void setVoided(boolean voided) {
		this.voided = voided;
	}

	@Column(name = "reversed")
	public boolean isReversed() {
		return reversed;
	}

	public void setReversed(boolean reversed) {
		this.reversed = reversed;
	}

	@Column(name = "txn_date", length=4)
	public String getTxnDate() {
		return txnDate;
	}

	public void setTxnDate(String txnDate) {
		this.txnDate = txnDate;
	}

	@Column(name = "txn_time", length=6)
	public String getTxnTime() {
		return txnTime;
	}

	public void setTxnTime(String txnTime) {
		this.txnTime = txnTime;
	}

	//@Temporal(TemporalType.TIMESTAMP)
	@Temporal(TemporalType.DATE)
	@Column(name = "first_dcp_call")
	public Date getFirstDcpCall() {
		return firstDcpCall;
	}

	public void setFirstDcpCall(Date firstDcpCall) {
		this.firstDcpCall = firstDcpCall;
	}

	//@Temporal(TemporalType.TIMESTAMP)
	@Temporal(TemporalType.DATE)
	@Column(name = "last_dcp_call")
	public Date getLastDcpCall() {
		return lastDcpCall;
	}

	public void setLastDcpCall(Date lastDcpCall) {
		this.lastDcpCall = lastDcpCall;
	}

	@Column(name = "retry" , nullable = false, columnDefinition = "int default 0")
	@Index(name="TMIX_CABC_TXN_LOG__RETRY")
	public Integer getRetry() {
		return retry;
	}

	public void setRetry(Integer retry) {
		this.retry = retry;
	}
}
