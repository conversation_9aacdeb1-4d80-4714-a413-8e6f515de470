package com.abl.db.dao;

import com.abl.db.model.AliTxnLog;

import java.util.Date;
import java.util.List;

public interface AliTxnLogDao extends Dao {

    public AliTxnLog getAliTxnLog(long id);

    /**
     * get AliTxnLog by bookingRef
     * if more than 1 rec, get the latest one
     *
     * @param bookingRef
     * @param msgType
     * @return
     */
    public AliTxnLog getAliTxnLogByBookingRef(String bookingRef, String msgType);

    /**
     * get AliTxnLog by trans id for msg_type= cdg_ali_offline_payment
     *
     * @param transId
     * @return
     */
    public AliTxnLog getPaymentAliTxnLogByTransId(String transId);

    /**
     * get AliTxnLog by bookingRef based on msgType exclude the current ID
     *
     * @param bookingRef
     * @param msgType
     * @param currId
     * @return
     */
    public AliTxnLog getAliTxnLogByBookingRefExcludeCurrentId(String bookingRef, String msgType, Long currId);

    /**
     *
     * to get txn log by mid, tid, stan and resp_code.
     * this is to check duplicate for offline payment
     * @param mid
     * @param tid
     * @param stan
     * @param respCode
     * @return
     */
    public AliTxnLog getOfflinePaymentMidTidStanRespCode(String mid, String tid, String stan, String respCode);

    /**
     *
     * to get payment txns
     * @param h5Status
     * @param retryDt
     * @param maxResults
     * @return
     */
    public List<AliTxnLog> getAlipayDeclinedPaymentTxns(AliTxnLog.H5Status h5Status, Date retryDt, int maxResults);

    /**
     * to update dcp status for the txn
     * @param id
     * @param dcpStatus
     * @return
     */
    public int updateDcpStatus(long id, AliTxnLog.DcpStatus dcpStatus);

    List<AliTxnLog> listPending(String msgType, int offset, int limit,
                                Date txnLogDateTimeNotBefore, Date lastSentToAlipayNotAfter);
    int updateStatus(long id, AliTxnLog.Status status);
    int updateStatus(long id, AliTxnLog.Status status, AliTxnLog.Status whereStatus);
}