package com.abl.db.dao;

import java.util.Date;
import java.util.List;

import com.abl.db.model.VcTxnLog;
import com.abl.db.model.VcTxnLog.MsgType;
import com.abl.db.model.VcTxnLog.Status;

public interface VcTxnLogDao {
	
	public void save(VcTxnLog vcTxnLog);

	public Boolean isOfflineTxnExist(String maskedCan, String jobNumber, String stan);
	
	public List<VcTxnLog>getNewOfflineSalesTxnByDateAndStatus(Status connErrStatus,Date newTxnDt, Date connErrDt, Date timeoutDt, Date lastRetryDt);
	
	public void saveAll(List<VcTxnLog> object);
	
	public VcTxnLog getVcTxnLogs(String maskedCan, String jobNumber, String stan, String msgType);
	
	public List<VcTxnLog> getReversalVcTxnLogs(String maskedCan, String jobNumber, String stan);

	public VcTxnLog getReversedVcTxnLogs(String maskedCan, String jobNumber, MsgType msgType,
			String stan);
	
	public Boolean isVcTxnExist(String maskedCan, String jobNumber, String stan, String msgType);
	
	public VcTxnLog getOriSaleVcTxnForVoid(String rrn, MsgType msgType);
	
	public VcTxnLog getOriSaleVcTxnForVoid(String rrn, String jobNumber, MsgType msgType);
	
	public VcTxnLog getOriVoidVcTxnForReversal(String stan);
	
	public VcTxnLog getOriVoidVcTxnForReversal(String rrn, String jobNumber, MsgType msgType);
	
	public void merge(VcTxnLog vcTxnLog);
	
	public List<VcTxnLog>getAllOfflineSalesTxnByDateAndStatus(Status queueStatus,Status processStatus,Status connErrStatus,Date newTxnDt, Date connErrDt, Date timeoutDt, Date lastRetryDt);
	
}
