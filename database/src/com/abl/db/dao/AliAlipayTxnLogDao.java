package com.abl.db.dao;

import com.abl.db.model.AliAlipayTxnLog;

public interface AliAlipayTxnLogDao extends Dao {

    public AliAlipayTxnLog getAliAlipayTxnLog(long id);

    /**
     * insert into AliAlipayTxnLog
     * 
     * @param txnLog
     */
    public void save(AliAlipayTxnLog txnLog);
    
    /**
     * update AliAlipayTxnLog record
     * 
     * @param txnLog
     */
    public void update(AliAlipayTxnLog txnLog);


    /**
     * get AliAlipayTxnLog based on aliTxnLog id
     * @param id
     * @return
     */
    public AliAlipayTxnLog getAliAlipayTxnLogByAliTxnLogId(long id);

    AliAlipayTxnLog getSuccessfulFreezeByAliTxnLogId(long id);
    int updateAliAlipayStatus(long id, AliAlipayTxnLog.Status status);

    AliAlipayTxnLog getByBookingRef(String bookingRef, String msgType, String ascOrDesc);
}