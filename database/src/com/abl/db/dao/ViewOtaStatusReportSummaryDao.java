package com.abl.db.dao;

import java.util.List;

import com.abl.db.model.ViewOtaStatusReportSummary;

public interface ViewOtaStatusReportSummaryDao extends Dao{
	
	public List<ViewOtaStatusReportSummary> searchSummary(String orderBy, boolean ascending, int offset, int maxResults);
	
	public int countSummary();
	
	public List<ViewOtaStatusReportSummary> searchSelectedGroupSummary(List<String> groupIdList, String orderBy, boolean ascending, int offset, int maxResults);

}
