package com.abl.db.dao;

import com.abl.db.bean.StuckTransactionSearchParam;
import com.abl.db.model.StuckTransaction;
import com.abl.db.model.TmsTerminal;

import java.util.Date;
import java.util.List;

public interface StuckTransactionDao {

    public void save(StuckTransaction stuckTxn);

    public StuckTransaction getStuckTransaction(long id);

    public List<StuckTransaction> getStuckTransactions(StuckTransactionSearchParam searchParam,
                                                       int offset, int maxResults);

    public int countStuckTransactions(StuckTransactionSearchParam searchParam);

    public int countStuckTransactionsByTerminalAndTimestamp(TmsTerminal terminal, Date timestamp);

    public StuckTransaction getStuckTransactionByTerminalAndTimestamp(TmsTerminal terminal, Date timestamp);

    public List<StuckTransaction> getStuckTransactionsWithPendingActions(TmsTerminal terminal, int maxResults);
}
