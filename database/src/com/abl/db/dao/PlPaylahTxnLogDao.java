package com.abl.db.dao;

import com.abl.db.model.PlPaylahTxnLog;

public interface PlPaylahTxnLogDao extends Dao {

    public PlPaylahTxnLog getPlPaylahTxnLog(long id);
    
    /**
     * get PlPaylahTxnLog by plTxnLogId
     * if there are more than 1 recs, get the earliest one
     * 
     * @param id
     * @return
     */
    public PlPaylahTxnLog getPlPaylahTxnLogByPlTxnLogId(long id);
    
    /**
     * get PlPaylahTxnLog by plTxnLogId and msgType
     * if there are more than 1 recs, get the most recent one
     * 
     * @param id
     * @param msgType
     * @return
     */
    public PlPaylahTxnLog getPlPaylahTxnLogByPlTxnLogIdAndMsgType(long id, String msgType);
    
    /**
     * insert into PlPaylahTxnLog
     * 
     * @param txnLog
     */
    public void save(PlPaylahTxnLog txnLog);
    
    /**
     * update PlPaylahTxnLog record
     * 
     * @param txnLog
     */
    public void update(PlPaylahTxnLog txnLog);

}