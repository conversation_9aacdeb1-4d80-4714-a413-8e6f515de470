package com.abl.db.dao;

import java.util.List;

import com.abl.db.model.AdminUser;
import com.abl.db.model.Module;
import com.abl.db.model.Page;

public interface PageDao extends Dao {

	public Page getPage(String name);
	
	public Page getPageFetchModule(String name);
	
	public Page getPageByUrl(String url);
	
	public List<Page> getPages();
	
	public List<Page> getAuthorizedPages(AdminUser appUser);
	
	public List<Page> getAdminPages();
	
	public List<Page> getModulePages(Module module);
	
	public List<Page> getVisiblePages();
}
