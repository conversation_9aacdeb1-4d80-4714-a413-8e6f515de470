package com.abl.db.dao;

import java.util.List;

import com.abl.db.bean.ProfileFeeValueBean;
import com.abl.db.model.ProfileFeeValue;

public interface ProfileFeeValueDao extends Dao{
	
	public void saveAll(List<ProfileFeeValue> object);

    public List<ProfileFeeValue> getProfileFeeValuesByFee(long profileFeeId);

    public List<ProfileFeeValueBean> getProfileFeeValueSummary(long terminalProfileId);
}
