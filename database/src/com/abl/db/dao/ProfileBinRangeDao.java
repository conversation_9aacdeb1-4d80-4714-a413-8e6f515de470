package com.abl.db.dao;

import java.util.List;

import com.abl.db.bean.ProfileBinValueBean;
import com.abl.db.model.ProfileBinRange;

public interface ProfileBinRangeDao extends Dao {
	
	public void saveAll(List<ProfileBinRange> object);

    public List<ProfileBinRange> getProfileBinRangesByProfile(long terminalProfileId);

    public List<ProfileBinValueBean> getAllProfileBinRangesByProfile(long terminalProfileId);
}
