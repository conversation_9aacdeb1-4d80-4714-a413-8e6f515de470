package com.abl.db.dao;

import java.util.Date;
import java.util.List;

import com.abl.db.model.NofTxnLog;

public interface NofTxnLogDao extends Dao {

    public NofTxnLog getNofTxnLog(long id);

    /**
     * get NofTxnLog by rrn
     * if more than 1 rec, get the latest one
     * 
     * @param rrn
     * @return
     */
    public NofTxnLog getNofTxnLogByRrn(String rrn);
    
    /**
     * get NofTxnLog by mid, tid, stan where mti is 0220
     * if more than 1 rec, get the latest one
     * 
     * @param mid
     * @param tid
     * @param stan
     * @return
     */
    public NofTxnLog getPurchaseNofTxnLogByMidTidStan(String mid, String tid, String stan);
    
    /**
     * get NofTxnLog by mid, tid, stan, resp code where mti is 0220
     * if more than 1 rec, get the latest one
     * 
     * @param mid
     * @param tid
     * @param stan
     * @return
     */
    public NofTxnLog getOfflinePurchaseNofTxnLogByMidTidStanRespCode(String mid, String tid, String stan, String respCode);
    
    /**
     * get NofTxnLog by transId, respCode where msgType='nof_offline_purchase'
     * if more than 1 rec, get the latest one
     * 
     * @param transId
     * @param respCode
     * @return
     */
    public NofTxnLog getOfflinePurchaseNofTxnLogByTransIdRespCode(String transId, String respCode);
    
    /**
     * get NofTxnLog by transId where msgType='nof_purchase'
     * if more than 1 rec, get the latest one
     * 
     * @param transId
     * @param respCode
     * @return
     */
    public NofTxnLog getPurchaseNofTxnLogByTransId(String transId);
    
    /**
     * get NofTxnLogs by status order by id
     * 
     * @param status
     * @param maxResults
     * @return
     */
    public List<NofTxnLog> getNofTxnLogs(NofTxnLog.Status status, int maxResults);
    
    /**
     * update NofTxnLog record with id=id
     * set txResult and status
     * 
     * @param id
     * @param txResult
     * @param status
     * @return
     */
    public int updateTxResultAndStatus(long id, String txResult, NofTxnLog.Status status);
    
    /**
     * find NofTxnLog records with status=PROCESSING
     * set status to PENDING
     * 
     * @return
     */
    public int resetPendingStatus();
    
    /**
     * insert into NofTxnLog
     * 
     * @param txnLog
     */
    public void save(NofTxnLog txnLog);

    /**
     *
     * @param dcpStatus
     * @param maxResults
     * @return
     */
    public List<NofTxnLog> getNofDeclinedPaymentTxns(NofTxnLog.DcpStatus dcpStatus, Date retryDt, int maxResults);

    /**
     *
     * @param id
     * @param dcpStatus
     * @return
     */
    public int updateDcpStatus(long id, NofTxnLog.DcpStatus dcpStatus);

}