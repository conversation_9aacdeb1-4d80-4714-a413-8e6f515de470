package com.abl.db.dao;

import java.util.Date;
import java.util.List;

import com.abl.db.model.AdminLog;

public interface AdminLogDao extends Dao {

	public AdminLog getAdminLogFetchAll(long id);
	
	public int countAdminLogs();
	
	public List<AdminLog> searchAdminLogs(String orderBy, boolean ascending, int offset, int maxResults);
	
	public List<AdminLog> searchAdminLogs(String login,Date startDate, Date endDate,String orderBy, boolean ascending, int offset, int maxResults);
	
	public int countAdminLogs(String login, Date startDate, Date endDate);
}
