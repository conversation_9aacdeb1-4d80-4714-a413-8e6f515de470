package com.abl.db.dao;

import java.util.Date;
import java.util.List;

import com.abl.db.model.AlipayHostTxnLog;

public interface AlipayHostTxnLogDao extends Dao {
	
	public List<AlipayHostTxnLog> searchAlipayHostTxnLogs(Date startDate, Date endDate, String taxiNumber, String jobNumber, String driverId, String orderBy, boolean ascending, int offset, int maxResults);
	
	public int countAlipayHostTxnLogs( Date startDate, Date endDate, String taxiNumber, String jobNumber, String driverId);

}
