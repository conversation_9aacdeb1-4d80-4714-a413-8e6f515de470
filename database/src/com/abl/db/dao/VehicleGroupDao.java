package com.abl.db.dao;

import java.io.InputStream;
import java.util.List;

import com.abl.db.model.AdminUser;
import com.abl.db.model.Group;
import com.abl.db.model.VehicleGroup;
import com.abl.db.model.Vehicle;

public interface VehicleGroupDao extends Dao {
	public VehicleGroup getVehicleGroup(String vehicleId);

	public VehicleGroup getVehicleGroupByVehicleId(String vehicleId);

	public VehicleGroup getVehicleGroupById(long id);

	public List<VehicleGroup> getVehicleGroupByGroupId(long id);

	public void saveAll(List<VehicleGroup> vehicleGroup);

	public List<VehicleGroup> uploadVehicle(InputStream inputStream, String groupName,
			AdminUser adminUser) throws Exception;

	public List<VehicleGroup> searchVehicles(String vehicleNo, String orderBy, boolean ascending,
			int offset, int maxResults);

	public int countVehicles(String vehicleNo);

	public List<VehicleGroup> getVehicleGroupByList(List<Vehicle> vehList);

	public List<Vehicle> getExistingVehicles();

	public List<Vehicle> getVehiclesWithoutGroup(List<String> ivdVersion,
			List<String> vehicleType, List<String> vehicleModel, String vehicleNo);

	public List<Vehicle> getVehiclesWithDefaultGroup(List<String> ivdVersion,
			List<String> vehicleType, List<String> vehicleModel, String vehicleNo);

	public List<VehicleGroup> getVehiclesWithGroup(List<String> ivdVersion, List<String> vehicleType,
			List<String> vehicleModel, String vehicleNo);

}
