package com.abl.db.dao;

import java.util.Date;
import java.util.List;

import com.abl.db.model.BlacklistVersion;

public interface BlacklistVersionDao extends Dao {
    public BlacklistVersion getBlacklistVersion(long id);

    public BlacklistVersion getBlacklistVersionByVersion(int version);

    public BlacklistVersion getLatestBlacklistVersion(short status);

    public BlacklistVersion getBlacklistVersion(String fullCans, String fullRange, String smallCans, String smallRange);
    
    public List<BlacklistVersion> getBlacklistVersionByStatus(short status);
    
    public BlacklistVersion getBlacklistVersionByScheduleDate(Date scheduleDate);
    
    public BlacklistVersion getPendingBlacklistVersionByScheduleDateForBatch(Date scheduleDate);
}
