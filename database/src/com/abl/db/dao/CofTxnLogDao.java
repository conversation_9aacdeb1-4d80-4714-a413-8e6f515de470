package com.abl.db.dao;

import java.util.Date;
import java.util.List;

import com.abl.db.model.CofTxnLog;
import com.abl.db.model.CofTxnLog.MsgType;
import com.abl.db.model.CofTxnLog.Status;

public interface CofTxnLogDao extends Dao {

	public void save(CofTxnLog cofTxnLog);

	public Boolean isTxnExist(String maskedCan, String jobNumber, String stan);

	public List<CofTxnLog>getNewOfflineSalesTxnByDateAndStatus(Status connErrStatus,Date newTxnDt, Date connErrDt, Date timeoutDt, Date lastRetryDt);

	public void saveAll(List<CofTxnLog> object);

	public CofTxnLog getCofTxnLogs(String maskedCan, String jobNumber, String stan, String msgType);

	public CofTxnLog getReversedCofTxnLogs(String maskedCan, String jobNumber, MsgType msgType,
			String stan);

	public void merge(CofTxnLog cofTxnLog);

	public List<CofTxnLog>getAllOfflineSalesTxnByDateAndStatus(Status queueStatus,Status processStatus,Status connErrStatus,Date newTxnDt, Date connErrDt, Date timeoutDt, Date lastRetryDt);
}