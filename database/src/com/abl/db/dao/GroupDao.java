package com.abl.db.dao;

import java.util.List;

import com.abl.db.model.Group;

public interface GroupDao extends Dao {

    public Group getGroupByName(String name);
    
    public List<Group> getGroupFetchRelease();
    
    public Group getGroupById(long id);

	public List<Group> getAllGroups();

	public Group getGroupByReleaseId(long releaseId);
	
	public List<Group> getAllGroupsExcludeId(long id);

    public Group getActiveGroupFetchReleaseById(long groupId);
}
