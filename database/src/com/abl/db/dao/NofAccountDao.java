package com.abl.db.dao;

import java.util.List;

import com.abl.db.model.NofAccount;

public interface NofAccountDao extends Dao {

    public NofAccount getNofAccount(long id);

    /**
     * get NofAccount by merchTokenId
     * 
     * @param merchTokenId
     * @return
     */
    public NofAccount getNofAccountByMerchTokenId(String merchTokenId);
    
    /**
     * get list of NofAccount by muid
     * 
     * @param muid
     * @return
     */
    public List<NofAccount> getNofAccountsByGuidMuid(String guid, String muid);
    
    public NofAccount getUndeletedNofAccountsByGuidMuidTokenIndex(String guid, String muid, String tokenIndex);
    
    public void save(NofAccount account);
    
    public void update(NofAccount account);

}