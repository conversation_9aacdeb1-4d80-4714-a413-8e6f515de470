package com.abl.db.dao;

import com.abl.db.model.EzlWcTxnLog;

import java.util.List;

public interface EzlWcTxnLogDao extends Dao {

    public EzlWcTxnLog getEzlWcTxnLog(long id);

    /**
     * get EzlWcTxnLog by EzlTxnLogId
     * if there are more than 1 recs, get the earliest one
     * 
     * @param id
     * @return
     */
    public EzlWcTxnLog getEzlWcTxnLogByEzlTxnLogId(long id);
    
    /**
     * get EzlWcTxnLog with reverseState order by id
     * 
     * @param reversalState
     * @param maxResults
     * @return
     */

    /**
     * insert into EzlWcTxnLog
     * 
     * @param txnLog
     */
    public void save(EzlWcTxnLog txnLog);
    
    /**
     * update EzlWcTxnLog record
     * 
     * @param txnLog
     */
    public void update(EzlWcTxnLog txnLog);

}