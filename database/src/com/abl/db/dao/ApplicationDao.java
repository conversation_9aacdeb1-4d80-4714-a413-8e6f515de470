package com.abl.db.dao;

import com.abl.db.model.Application;

import java.util.List;

public interface ApplicationDao extends Dao {

    public Application getApplication(long id);

    public List<Application> getApplications();

    public List<Application> searchApplicationByNameVersion(String applicationName, int version);

    public void save(Application application);

    public void saveAll(List<Application> application);
}
