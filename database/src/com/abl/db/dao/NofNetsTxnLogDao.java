package com.abl.db.dao;

import java.util.List;

import com.abl.db.model.NofNetsTxnLog;

public interface NofNetsTxnLogDao extends Dao {

    public NofNetsTxnLog getNofNetsTxnLog(long id);

    /**
     * get NofNetsTxnLog by NofTxnLogId
     * if there are more than 1 recs, get the earliest one
     * 
     * @param id
     * @return
     */
    public NofNetsTxnLog getNofNetsTxnLogByNofTxnLogId(long id);
    
    /**
     * get NofNetsTxnLog with reverseState order by id
     * 
     * @param reversalState
     * @param maxResults
     * @return
     */
    public List<NofNetsTxnLog> getNofNetsTxnLogsFetchNofTxnLog(NofNetsTxnLog.ReversalState reversalState, int maxResults);
    
    /**
     * find all NofNetsTxnLog records
     * with status NEW
     * set status to FAIL and reversalState to PENDING
     * 
     * @return num of recs affected by update
     */
    public int resetNewNofNetsTxnLogForReversal();
    
    /**
     * find all NofNetsTxnLog records
     * with reversalState REVERSING
     * set reversalState to PENDING
     * 
     * @return num of recs affected by update
     */
    public int resetReversingNofNetsTxnLogForReversal();
    
    /**
     * insert into NofNetsTxnLog
     * 
     * @param txnLog
     */
    public void save(NofNetsTxnLog txnLog);
    
    /**
     * update NofNetsTxnLog record
     * 
     * @param txnLog
     */
    public void update(NofNetsTxnLog txnLog);

}