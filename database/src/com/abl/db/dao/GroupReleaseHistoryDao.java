package com.abl.db.dao;

import java.util.List;

import com.abl.db.model.GroupReleaseHistory;

public interface GroupReleaseHistoryDao extends Dao{
	
	public List<GroupReleaseHistory> getGroupReleaseHistoryByGroupId(long groupId);
	
	public List<GroupReleaseHistory> searchGroupReleaseHistory(long groupId,long releaseId,
             String orderBy, boolean ascending, int offset, int maxResults) ;
	 
	public int countGroupReleaseHistory(long groupId,long releaseId);
}
