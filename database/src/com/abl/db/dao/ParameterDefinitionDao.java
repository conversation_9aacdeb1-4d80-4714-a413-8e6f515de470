package com.abl.db.dao;

import com.abl.db.model.ParameterDefinition;

import java.util.List;

public interface ParameterDefinitionDao extends Dao {

    public ParameterDefinition getParameterDefinition(long id);

    public List<ParameterDefinition> getParameterDefinitionFetchApplication(long appId);

    public List<ParameterDefinition> getBinParameterDefinitionFetchApplication(long appId);

    public void saveAll(List<ParameterDefinition> object);

    public ParameterDefinition getParameterDefinitionByNameApp(String name, long applicationId);

    public int countParameters(long appId);
    
    public List<ParameterDefinition> getFeeParameterDefinitionFetchApplication(long appId);

}
