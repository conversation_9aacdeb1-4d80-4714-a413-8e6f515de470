package com.abl.db.dao;

import com.abl.db.model.TerminalBlacklistDownloadJob;
import com.abl.db.model.TerminalBlacklistDownloadJobId;

import java.util.List;

public interface TerminalBlacklistDownloadJobDao extends Dao {
    public void save(TerminalBlacklistDownloadJob terminalBlacklistDownloadJob);

    public List<TerminalBlacklistDownloadJob> getNotSuccessTerminalBlacklistDownloadJobs(long blacklistVersionId, long terminalId, String vehicleId);

    public List<TerminalBlacklistDownloadJob> getInProgressTerminalBlacklistDownloadJobs(long terminalId);

    public List<TerminalBlacklistDownloadJob> getInProgressTerminalBlacklistDownloadJobs(String vehicleId);

    public int countTerminalBlacklistDownloadJobs(long jobId, short status, long terminalId);

    public TerminalBlacklistDownloadJob getByPk(TerminalBlacklistDownloadJobId terminalBlacklistDownloadJobId);

    public int releaseOtherJobs(TerminalBlacklistDownloadJob terminalBlacklistDownloadJob);

    public TerminalBlacklistDownloadJob findTerminalBlacklistDownloadJob(long terminalId, String vehicleId, long blacklistVersionId);
}
