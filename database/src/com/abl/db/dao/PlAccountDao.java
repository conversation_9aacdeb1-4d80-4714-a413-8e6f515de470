package com.abl.db.dao;

import com.abl.db.model.PlAccount;

public interface PlAccountDao extends Dao {

    public PlAccount getPlAccount(long id);

    /**
     * get PlAccount by userId
     * 
     * @param userId
     * @return
     */
    public PlAccount getPlAccountByUserId(String userId);
    
    public void saveOrUpdate(PlAccount account);
    
    public void save(PlAccount account);
    
    public void update(PlAccount account);

}