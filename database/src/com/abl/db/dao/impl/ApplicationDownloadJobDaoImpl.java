package com.abl.db.dao.impl;

import com.abl.db.dao.ApplicationDownloadJobDao;
import com.abl.db.model.ApplicationDownloadJob;
import com.abl.utils.DateUtils;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public class ApplicationDownloadJobDaoImpl extends DaoImpl implements
        ApplicationDownloadJobDao {

    @Override
    public ApplicationDownloadJob getApplicationDownloadJob(long id) {
        return getHibernateTemplate().get(ApplicationDownloadJob.class, id);
    }

    @Override
    @SuppressWarnings("unchecked")
    public ApplicationDownloadJob getApplicationDownloadFetchApplication(long id) {
        List<ApplicationDownloadJob> list = getHibernateTemplate().find("from ApplicationDownloadJob appDownload " +
                "left join fetch appDownload.application where appDownload.id=?", id);
        if ((list != null) && (list.size() > 0)) {
            return list.get(0);
        } else {
            return null;
        }
    }

    @Override
    public void save(ApplicationDownloadJob applicationDownloadJob) {
        getHibernateTemplate().saveOrUpdate(applicationDownloadJob);
        if (applicationDownloadJob.getApplicationDownloadJobInfo() != null) {
            getHibernateTemplate().saveOrUpdate(applicationDownloadJob.getApplicationDownloadJobInfo());
        }
    }

    @Override
    public List<ApplicationDownloadJob> getCurrentJobs(long groupId) {
        String currentTime = DateUtils.format(new Date(), "HHmm");
        return getHibernateTemplate().find("from ApplicationDownloadJob job " +
                "where job.deleted=0 and job.group.id=? " +
                "and ((job.startWindow=job.endWindow) " +
                "or (job.startWindow<job.endWindow and job.startWindow<=? and job.endWindow>=?) " +
                "or (job.startWindow>job.endWindow and (job.startWindow<=? or job.endWindow>=?)))",
                groupId,
                currentTime, currentTime,
                currentTime, currentTime);
    }

    @Override
    public List<ApplicationDownloadJob> getApplicationDownloadJobs(long groupId, short status) {
        if (groupId > 0) {
            if (status >= 0) {
                return getHibernateTemplate().find("from ApplicationDownloadJob job " +
                        "join fetch job.group g " +
                        "where job.deleted=? " +
                        "and g.id=? " +
                        "and job.status=?",
                        Boolean.FALSE,
                        groupId,
                        status);
            } else {
                return getHibernateTemplate().find("from ApplicationDownloadJob job " +
                                "join fetch job.group g " +
                                "where job.deleted=? " +
                                "and g.id=?",
                        Boolean.FALSE,
                        groupId);
            }
        } else {
            if (status >= 0) {
                return getHibernateTemplate().find("from ApplicationDownloadJob job " +
                                "join fetch job.group g " +
                                "where job.deleted=? " +
                                "and job.status=?",
                        Boolean.FALSE,
                        status);
            } else {
                return getHibernateTemplate().find("from ApplicationDownloadJob job " +
                                "join fetch job.group g " +
                                "where job.deleted=?",
                        Boolean.FALSE);
            }
        }
    }
    
    @Override
    public ApplicationDownloadJob getApplicationDownloadJobFetchGroup(long id) {
    	List<ApplicationDownloadJob> list = getHibernateTemplate().find("from ApplicationDownloadJob job " +
        		"join fetch job.group grouping where job.id=? ",id);
        
        if ((list != null) && (list.size() > 0)) {
            return list.get(0);
        } else {
            return null;
        }
    }
}
