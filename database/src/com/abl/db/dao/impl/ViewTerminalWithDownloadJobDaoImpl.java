package com.abl.db.dao.impl;

import java.util.List;

import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Repository;

import com.abl.db.dao.ViewTerminalWithDownloadJobDao;
import com.abl.db.model.ViewTerminalWithDownloadJob;

@Repository
public class ViewTerminalWithDownloadJobDaoImpl extends DaoImpl implements ViewTerminalWithDownloadJobDao{
	
	@SuppressWarnings("unchecked")
	public List<ViewTerminalWithDownloadJob> getTerminalWithDownloadJob(long jobId, long groupId) {
		Session session = this.getSession();
		Criteria criteria = session.createCriteria(ViewTerminalWithDownloadJob.class,
				"viewTerminalWithDownloadJob");

//        criteria.add(Restrictions.ne("viewTerminalWithDownloadJob.terminalDownloadStatus",  0));
		criteria.add(Restrictions.eq("viewTerminalWithDownloadJob.groupId", groupId));
		criteria.add(Restrictions.eq("viewTerminalWithDownloadJob.appDownloadId", jobId));
		criteria.addOrder(Order.asc("viewTerminalWithDownloadJob.vehicleId"));
		
		List<ViewTerminalWithDownloadJob> terminalList = criteria.list();
		
		return terminalList;
		
	}

}
