package com.abl.db.dao.impl;

import java.util.List;

import com.abl.db.dao.ViewTerminalWithDownloadedAppDao;
import com.abl.db.model.ViewTerminalWithDownloadedApp;

import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Repository;

@Repository
public class ViewTerminalWithDownloadedAppDaoImpl extends DaoImpl implements
		ViewTerminalWithDownloadedAppDao {

	@SuppressWarnings("unchecked")
	public List<ViewTerminalWithDownloadedApp> getTerminalWithDownloadedApp(
			long groupId) {
		Session session = this.getSession();
		Criteria criteria = session.createCriteria(
				ViewTerminalWithDownloadedApp.class,
				"terminalWithDownloadedApp").setResultTransformer(Criteria.DISTINCT_ROOT_ENTITY);
;

		criteria.add(Restrictions.eq("terminalWithDownloadedApp.groupId",
				groupId));
		criteria.addOrder(Order.asc("terminalWithDownloadedApp.vehicleId"));
		
		List<ViewTerminalWithDownloadedApp> terminalList = criteria.list();

		return terminalList;

	}

}
