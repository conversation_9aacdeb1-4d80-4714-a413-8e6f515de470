package com.abl.db.dao.impl;

import com.abl.db.dao.TxnResultDao;
import com.abl.db.model.TxnResult;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class TxnResultDaoImpl extends DaoImpl implements TxnResultDao {

    @Override
    public TxnResult getTxnResult(String code) {
        return getHibernateTemplate().get(TxnResult.class, code);
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<TxnResult> getTxnResults() {
        return getHibernateTemplate().find("from TxnResult");
    }
}
