package com.abl.db.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.abl.db.dao.ModuleDao;
import com.abl.db.model.Module;

@Repository
public class ModuleDaoImpl extends DaoImpl implements ModuleDao {

	@SuppressWarnings("unchecked")
	@Override
	public List<Module> getModules() {
		return getHibernateTemplate().find("from Module module order by displayOrder");
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Module> getModules(boolean isVisible) {
		return getHibernateTemplate().find("from Module module where module.visible=? order by displayOrder", isVisible);
	}
	
}
