package com.abl.db.dao.impl;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.hibernate.Criteria;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Repository;

import com.abl.db.dao.MpTxnLogDao;
import com.abl.db.model.MpTxnLog;
import com.abl.db.model.MpTxnLog.MsgType;
import com.abl.db.model.MpTxnLog.Status;
import com.abl.db.model.TxnLog;

@Repository
public class MpTxnLogDaoImpl extends DaoImpl implements MpTxnLogDao {

	@Override
	public void save(MpTxnLog mpOfflineSales) {
		getHibernateTemplate().saveOrUpdate(mpOfflineSales);
	}

	@Override
	public void merge(MpTxnLog mpTxnLog) {
		getHibernateTemplate().merge(mpTxnLog);
	}


	@Override
	public Boolean isOfflineTxnExist(String maskedCan, String jobNumber, String stan) {
		Boolean exists = false;
		Session session = this.getSession();
		Criteria criteria = session.createCriteria(MpTxnLog.class)
				.add(Restrictions.eq("maskedCan", maskedCan)).add(Restrictions.eq("jobNumber", jobNumber))
				.add(Restrictions.eq("stan", stan));

		List<MpTxnLog> list = criteria.list();
		if (!list.isEmpty()) {
			exists = true;
		}

		return exists;
	}

	public Boolean isMpTxnExist(String maskedCan, String jobNumber, String stan, String msgType) {
		Boolean exists = false;
		Session session = this.getSession();
		Criteria criteria = session.createCriteria(MpTxnLog.class)
				.add(Restrictions.eq("maskedCan", maskedCan)).add(Restrictions.eq("jobNumber", jobNumber))
				.add(Restrictions.eq("stan", stan))
				.add(Restrictions.eq("msgType", MsgType.valueOf(msgType)));

		List<MpTxnLog> list = criteria.list();
		if (!list.isEmpty()) {
			exists = true;
		}

		return exists;
	}

	@Override
	public List<MpTxnLog> getNewOfflineSalesTxnByDateAndStatus(Status connErrStatus, Date newTxnDt,
			Date connErrDt, Date timeoutDt, Date lastRetryDt) {
		// newTxnDt = current date time - xMinsDelay
		// connErrDt = current date time - xMinsConnErrToRetry (default to 24 hours)
		// timeOutDt = current date time - xmins for retry (default to 30 mins)
		// lastRetryDt = currentDateTime - xminsInterval for retry (default to 10
		// mins)

		Session session = getSession();
		Query query =session
				.createQuery(
						"from MpTxnLog m where m.msgType=:msgType and m.txnDateTime<=:newTxnDt"
								+ " and ((m.status ='NEW' and m.responseCode='00') "
								+ "or (m.status=:connErrStatus and m.firstDcpCall>=:connErrDt and m.lastDcpCall<=:lastRetryDt) "
								+ "or (m.status in ('TIMEOUT', 'HTTP_ERR') and m.firstDcpCall >=:timeoutDt and m.lastDcpCall<=:lastRetryDt)) "
								+ "order by m.id asc");


		query.setParameter("msgType", MsgType.OFFLINE_SALES);
		query.setTimestamp("newTxnDt", newTxnDt);
		query.setParameter("connErrStatus", Status.CONN_ERR);
		query.setTimestamp("connErrDt", connErrDt);
		query.setTimestamp("timeoutDt", timeoutDt);
		query.setTimestamp("lastRetryDt", lastRetryDt);

		List<MpTxnLog> l = query.list();

		logger.debug("list size=" + l.size());
		//		query = session
		//				.createQuery(
		//						"update MpTxnLog set status=:queueStatus where msgType=:msgType and txnDateTime<=:newTxnDt"
		//								+ " and ((status ='NEW' and responseCode='00') "
		//								+ "or (status=:connErrStatus and firstDcpCall>=:connErrDt and lastDcpCall<=:lastRetryDt) "
		//								+ "or (status in ('TIMEOUT', 'HTTP_ERR') and firstDcpCall >=:timeoutDt and lastDcpCall<=:lastRetryDt)) "
		//								+ "order by id desc");
		if(l.size()>0){
			Query query2 = null;
			int startIdx = 0;
			int batchIdx=0;

			Long[] mpTxnIdList = new Long[100];
			Map hmMpTxn = new HashMap<Integer, Long[]>();

			String updateSql ="update MpTxnLog m set m.status=:queueStatus ";
			for(MpTxnLog txn: l) {
				if(startIdx<100) {
					mpTxnIdList[startIdx]=txn.getId();
				} else {	
					if(batchIdx==0) {
						updateSql =updateSql+" where m.id IN (:idList"+batchIdx+")";
					} else {
						updateSql =updateSql+" or m.id IN (:idList"+batchIdx+")";
					}

					hmMpTxn.put(batchIdx,mpTxnIdList);
					startIdx=0;
					mpTxnIdList = new Long[100];
					mpTxnIdList[startIdx]=txn.getId();
					batchIdx++;
				}
				startIdx++;
			}

			if(startIdx>=1 && mpTxnIdList[0]!=null){
				
				Long lastMpList[] = new Long[startIdx];
				
				for(int i=0;i<startIdx;i++) {
					lastMpList[i]=mpTxnIdList[i];
				}
				
				if(batchIdx==0){
					updateSql =updateSql+" where m.id IN (:idList"+batchIdx+")";
					hmMpTxn.put(batchIdx,lastMpList);
				}else{
					updateSql =updateSql+" or m.id IN (:idList"+batchIdx+")";
					hmMpTxn.put(batchIdx,lastMpList);
				}
			}

			updateSql =updateSql+ " order by id asc";

			logger.debug("updateSql:"+updateSql);

			query2 = session.createQuery(updateSql);
			query2.setParameter("queueStatus", Status.QUEUE);			

			Iterator it = hmMpTxn.entrySet().iterator();
			while (it.hasNext()) {
				Map.Entry pair = (Map.Entry)it.next();
				int idx = (Integer) pair.getKey();
				Long[] mptxns = (Long[]) pair.getValue();
				query2.setParameterList("idList"+idx, mptxns);

			}

			int result = query2.executeUpdate();
			logger.debug("update result="+result);
		}

		return l;

	}

	@Override
	public void saveAll(List<MpTxnLog> object) {
		getHibernateTemplate().saveOrUpdateAll(object);
	}

	@Override
	public MpTxnLog getMpTxnLogs(String maskedCan, String jobNumber, String stan, String msgType) {
		Session session = this.getSession();
		MpTxnLog mpTxnLog = null;
		Criteria criteria = session.createCriteria(MpTxnLog.class)
				.add(Restrictions.eq("maskedCan", maskedCan)).add(Restrictions.eq("jobNumber", jobNumber))
				.add(Restrictions.eq("stan", stan))
				.add(Restrictions.eq("msgType", MsgType.valueOf(msgType)));
		List<MpTxnLog> list = criteria.list();
		if (!list.isEmpty()) {
			mpTxnLog = list.get(0);
		}
		return mpTxnLog;
	}

	@Override
	public MpTxnLog getReversedMpTxnLogs(String maskedCan, String jobNumber, MsgType msgType,
			String stan) {
		Session session = this.getSession();
		MpTxnLog mpTxnLog = null;
		Criteria criteria = session.createCriteria(MpTxnLog.class)
				.add(Restrictions.eq("maskedCan", maskedCan)).add(Restrictions.eq("jobNumber", jobNumber))
				.add(Restrictions.eq("msgType", msgType)).add(Restrictions.eq("stan", stan));
		List<MpTxnLog> list = criteria.list();
		if (!list.isEmpty()) {
			mpTxnLog = list.get(0);
		}
		return mpTxnLog;
	}

	@Override
	public MpTxnLog getOriSaleMpTxnForVoid(String rrn, MsgType msgType) {
		Session session = this.getSession();
		MpTxnLog mpTxnLog = null;
		Criteria criteria = session.createCriteria(MpTxnLog.class).add(Restrictions.eq("rrn", rrn))
				.add(Restrictions.eq("msgType", msgType));
		List<MpTxnLog> list = criteria.list();
		if (!list.isEmpty()) {
			mpTxnLog = list.get(0);
		}
		return mpTxnLog;

	}

	@Override
	public MpTxnLog getOriVoidMpTxnForReversal(String stan) {
		Session session = this.getSession();
		MpTxnLog mpTxnLog = null;
		Criteria criteria = session.createCriteria(MpTxnLog.class).add(Restrictions.eq("stan", stan));
		List<MpTxnLog> list = criteria.list();
		if (!list.isEmpty()) {
			mpTxnLog = list.get(0);
		}
		return mpTxnLog;
	}

	@Override
	public List<MpTxnLog> getAllOfflineSalesTxnByDateAndStatus(Status queueStatus,
			Status processStatus, Status connErrStatus, Date newTxnDt, Date connErrDt, Date timeoutDt,
			Date lastRetryDt) {
		// newTxnDt = current date time - xMinsDelay
		// connErrDt = current date time - xMinsConnErrToRetry (default to 24 hours)
		// timeOutDt = current date time - xmins for retry (default to 30 mins)
		// lastRetryDt = currentDateTime - xminsInterval for retry (default to 10
		// mins)
		Session session = getSession();
		Query query = session
				.createQuery(
						"from MpTxnLog m where m.msgType=:msgType and m.txnDateTime<=:newTxnDt"
								+ " and ((m.status ='NEW' and m.responseCode='00') "
								+ "or (m.status=:connErrStatus and m.firstDcpCall>=:connErrDt and m.lastDcpCall<=:lastRetryDt) "
								+ "or (m.status=:processStatus) "
								+ "or (m.status=:queueStatus) "
								+ "or (m.status in ('TIMEOUT', 'HTTP_ERR') and m.firstDcpCall >=:timeoutDt and m.lastDcpCall<=:lastRetryDt)) "
								+ "order by m.id asc");

		query.setParameter("msgType", MsgType.OFFLINE_SALES);
		query.setTimestamp("newTxnDt", newTxnDt);
		query.setParameter("connErrStatus", Status.CONN_ERR);
		query.setTimestamp("connErrDt", connErrDt);
		query.setTimestamp("timeoutDt", timeoutDt);
		query.setTimestamp("lastRetryDt", lastRetryDt);
		query.setParameter("processStatus", Status.PROCCESSING);
		query.setParameter("queueStatus", Status.QUEUE);

		List<MpTxnLog> l = query.list();
		logger.debug("list size=" + l.size());

		//		query = session
		//				.createQuery(
		//						"update MpTxnLog set status=:queueStatus where msgType=:msgType and txnDateTime<=:newTxnDt"
		//								+ " and ((status ='NEW' and responseCode='00') "
		//								+ "or (status=:connErrStatus and firstDcpCall>=:connErrDt and lastDcpCall<=:lastRetryDt) "
		//								+ "or (status=:processStatus) "
		//								+ "or (status=:queueStatus) "
		//								+ "or (status in ('TIMEOUT', 'HTTP_ERR') and firstDcpCall >=:timeoutDt and lastDcpCall<=:lastRetryDt)) "
		//								+ "order by id desc");
		//
		if(l.size()>0) {
			Query query2 = null;
			int startIdx = 0;
			int batchIdx=0;

			Long[] mpTxnIdList = new Long[100];
			Map hmMpTxn = new HashMap<Integer, Long[]>();

			String updateSql ="update MpTxnLog m set m.status=:queueStatus ";
			for(MpTxnLog txn: l) {
				if(startIdx<100) {
					mpTxnIdList[startIdx]=txn.getId();
				} else {	
					if(batchIdx==0) {
						updateSql =updateSql+" where m.id IN (:idList"+batchIdx+")";
					} else {
						updateSql =updateSql+" or m.id IN (:idList"+batchIdx+")";
					}

					hmMpTxn.put(batchIdx,mpTxnIdList);
					startIdx=0;
					mpTxnIdList = new Long[100];
					mpTxnIdList[startIdx]=txn.getId();
					batchIdx++;
				}
				startIdx++;
			}

			if(startIdx>=1 && mpTxnIdList[0]!=null){
				
				Long lastMpList[] = new Long[startIdx];
				
				for(int i=0;i<startIdx;i++) {
					lastMpList[i]=mpTxnIdList[i];
				}
				
				if(batchIdx==0){
					updateSql =updateSql+" where m.id IN (:idList"+batchIdx+")";
					hmMpTxn.put(batchIdx,lastMpList);
				}else{
					updateSql =updateSql+" or m.id IN (:idList"+batchIdx+")";
					hmMpTxn.put(batchIdx,lastMpList);
				}
			}

			updateSql =updateSql+ " order by id asc";

			logger.debug("updateSql:"+updateSql);

			query2 = session.createQuery(updateSql);
			query2.setParameter("queueStatus", Status.QUEUE);			

			Iterator it = hmMpTxn.entrySet().iterator();
			while (it.hasNext()) {
				Map.Entry pair = (Map.Entry)it.next();
				int idx = (Integer) pair.getKey();
				Long[] mptxns = (Long[]) pair.getValue();
				query2.setParameterList("idList"+idx, mptxns);

			}

			int result = query2.executeUpdate();
			logger.debug("update result="+result);
		}
		return l;

	}
}
