package com.abl.db.dao.impl;

import com.abl.db.dao.ProfileFeeDao;
import com.abl.db.model.ProfileFee;
import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class ProfileFeeDaoImpl extends DaoImpl implements ProfileFeeDao {

    @Override
    public void saveAll(List<ProfileFee> object) {
        getHibernateTemplate().saveOrUpdateAll(object);
    }

    @Override
    public List<ProfileFee> getProfileFeesByProfile(long terminalProfileId) {
        Session session = this.getSession();
        Criteria criteria = session.createCriteria(ProfileFee.class, "profileFee");

        if (terminalProfileId > 0) {
            criteria.add(Restrictions.eq("profileFee.terminalProfile.id", terminalProfileId));
        }
        criteria.addOrder(Order.asc("profileFee.terminalProfile.id"));
        criteria.addOrder(Order.asc("profileFee.id"));

        List<ProfileFee> list = criteria.list();
        return list;

    }
}
