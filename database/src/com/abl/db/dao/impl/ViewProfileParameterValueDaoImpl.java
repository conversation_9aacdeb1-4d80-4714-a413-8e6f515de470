package com.abl.db.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.abl.db.dao.ViewProfileParameterValueDao;
import com.abl.db.model.ViewProfileParameterValue;


@Repository
public class ViewProfileParameterValueDaoImpl extends DaoImpl implements ViewProfileParameterValueDao {
	
	@Override
	public void saveAll(List<ViewProfileParameterValue> object) {
		getHibernateTemplate().saveOrUpdateAll(object);
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public ViewProfileParameterValue getProfileParameterValue(long profileId, long paramId) {
		List<ViewProfileParameterValue> list = getHibernateTemplate().find("from ViewProfileParameterValue profileParameterValue " +
				" where profileParameterValue.terminalProfileId= ? and profileParameterValue.parameterDefinitionId =?", profileId, paramId);
		
		if ((list != null)&&(list.size()>0)) {
			return list.get(0);
		} else {
			return null;
		}
	}

}
