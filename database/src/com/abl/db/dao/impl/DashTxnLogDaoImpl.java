package com.abl.db.dao.impl;

import java.util.Date;
import java.util.List;

import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.MatchMode;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Repository;
import com.abl.db.dao.DashTxnLogDao;
import com.abl.db.model.DashTxnLog;

@Repository
public class DashTxnLogDaoImpl extends DaoImpl implements DashTxnLogDao {
	
	@SuppressWarnings("unchecked")
	@Override
	public List<DashTxnLog> searchDashTxnLogs(Date startDate, Date endDate, String taxiNumber, String jobNumber, String driverId, String orderBy, boolean ascending, int offset, int maxResults){
		Session session = this.getSession();
		Criteria criteria = session.createCriteria(DashTxnLog.class, "dashLog");
		
		if (startDate != null) {
			criteria.add(Restrictions.ge("dashLog.logDateTime", startDate));
		}
		
		if (endDate != null) {
			criteria.add(Restrictions.le("dashLog.logDateTime", endDate));
		}
		
		if ((taxiNumber!=null)&&(taxiNumber.length() > 0)) {
			criteria.add(Restrictions.ilike("dashLog.taxiNumber", taxiNumber, MatchMode.ANYWHERE));
		}
		
		if ((jobNumber!=null)&&(jobNumber.length() > 0)) {
			criteria.add(Restrictions.ilike("dashLog.jobNumber", jobNumber, MatchMode.ANYWHERE));
		}
		if ((driverId!=null)&&(driverId.length() > 0)) {
			criteria.add(Restrictions.ilike("dashLog.driverId", driverId, MatchMode.ANYWHERE));
		}

		if (orderBy != null) {
			if (orderBy.indexOf('.')<0) {
				orderBy = "dashLog." + orderBy;
			}
			if (ascending) {
				criteria.addOrder(Order.asc(orderBy));
			} else {
				criteria.addOrder(Order.desc(orderBy));
			}
		}
		
		if (offset > 0) {
			criteria.setFirstResult(offset);
		}
		if (maxResults > 0) {
			criteria.setMaxResults(maxResults);
		}
		
		

		List<DashTxnLog> list = criteria.list();
		return list;
		
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public int countDashTxnLogs( Date startDate, Date endDate, String taxiNumber, String jobNumber, String driverId){
		
		Session session = this.getSession();
		Criteria criteria = session.createCriteria(DashTxnLog.class, "dashLog").setProjection(Projections.rowCount());
		
		if (startDate != null) {
			criteria.add(Restrictions.ge("dashLog.logDateTime", startDate));
		}
		
		if (endDate != null) {
			criteria.add(Restrictions.le("dashLog.logDateTime", endDate));
		}
		
		if ((taxiNumber!=null)&&(taxiNumber.length() > 0)) {
			criteria.add(Restrictions.ilike("dashLog.taxiNumber", taxiNumber, MatchMode.ANYWHERE));
		}
		
		if ((jobNumber!=null)&&(jobNumber.length() > 0)) {
			criteria.add(Restrictions.ilike("dashLog.jobNumber", jobNumber, MatchMode.ANYWHERE));
		}
		if ((driverId!=null)&&(driverId.length() > 0)) {
			criteria.add(Restrictions.ilike("dashLog.driverId", driverId, MatchMode.ANYWHERE));
		}
		
		List<Number> list = criteria.list();
		if ((list != null) && (list.size() > 0)) {
			return list.get(0).intValue();
		} else {
			return 0;
		}
		
	}

}
