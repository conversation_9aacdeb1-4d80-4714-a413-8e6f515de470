package com.abl.db.dao.impl;

import java.util.List;

import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Repository;

import com.abl.db.dao.ViewTerminalBlacklistCompletedJobDao;
import com.abl.db.model.ViewTerminalBlacklistCompletedJob;

@Repository
public class ViewTerminalBlacklistCompletedJobDaoImpl extends DaoImpl implements ViewTerminalBlacklistCompletedJobDao{
	
	@SuppressWarnings("unchecked")
	@Override
	public List<ViewTerminalBlacklistCompletedJob> getTerminalBlacklistCompletedJob(long jobId) {
		Session session = this.getSession();
		Criteria criteria = session.createCriteria(ViewTerminalBlacklistCompletedJob.class,
				"viewTerminalBlacklistCompletedJob");

        criteria.add(Restrictions.ne("viewTerminalBlacklistCompletedJob.terminalBlacklistDownloadJobStatus",  0));
		criteria.add(Restrictions.eq("viewTerminalBlacklistCompletedJob.blacklistJobId", jobId));
		
		List<ViewTerminalBlacklistCompletedJob> terminalList = criteria.list();
		
		return terminalList;
		
	}

	@Override
	public List<ViewTerminalBlacklistCompletedJob> getAllTerminalBlacklistCompletedJob() {
		
		List<ViewTerminalBlacklistCompletedJob> list =getHibernateTemplate().find("from ViewTerminalBlacklistCompletedJob t ");
		return list;

	}
}
