package com.abl.db.dao.impl;

import com.abl.db.dao.BlacklistFileDao;
import com.abl.db.model.BlacklistFile;
import org.apache.jcs.JCS;
import org.apache.log4j.Logger;
import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class BlacklistFileDaoImpl extends DaoImpl implements
        BlacklistFileDao {

    private static final Logger logger = Logger.getLogger(BlacklistFileDaoImpl.class);
    private static JCS blacklistFileCache;
    private static JCS blacklistFileNextRecordCache;

    private static JCS getBlacklistFileCache() {
        if (blacklistFileCache != null) return blacklistFileCache;
        try {
            blacklistFileCache = JCS.getInstance("blacklistFileCache");
        } catch (Exception e) {
            // Handle cache region initialization failure
            logger.warn("blacklistFileCache instance cannot be created");
        }
        return blacklistFileCache;
    }

    private static JCS getBlacklistFileNextRecordCache() {
        if (blacklistFileNextRecordCache != null) return blacklistFileNextRecordCache;
        try {
            blacklistFileNextRecordCache = JCS.getInstance("blacklistFileNextRecordCache");
        } catch (Exception e) {
            // Handle cache region initialization failure
            logger.warn("blacklistFileNextRecordCache instance cannot be created - " + e.getMessage());
        }
        return blacklistFileNextRecordCache;
    }

    @Override
    public BlacklistFile getBlacklistFile(long blacklistVersionId, int recordNo) {
        String key = blacklistVersionId + "|" + recordNo;

        JCS blacklistFileCache = getBlacklistFileCache();
        if (blacklistFileCache != null) {
            Object value = blacklistFileCache.get(key);
            if (value != null) {
                BlacklistFile blacklistFile = (BlacklistFile) value;
                return blacklistFile;
            }
        }

        Session session = this.getSession();
        Criteria criteria = session.createCriteria(BlacklistFile.class, "blacklistFile");

        if (blacklistVersionId > 0) {
            criteria.add(Restrictions.eq("blacklistFile.blacklistVersion.id", blacklistVersionId));
        }

        if (recordNo > 0) {
            criteria.add(Restrictions.eq("blacklistFile.recordNo", recordNo));
        }

        List<BlacklistFile> list = criteria.list();
        if (list == null || list.size() <= 0) return null;

        BlacklistFile blacklistFile = list.get(0);

        try {
            if (blacklistFileCache != null) {
                blacklistFileCache.put(key, blacklistFile);
                logger.debug("cached blacklistFile key=" + key);
            }
        } catch (Exception e) {
            logger.warn("cannot put blacklistFile into cache - " + e.getMessage());
        }

        return blacklistFile;
    }

    @Override
    public int getNextRecordNo(long blacklistVersionId, int recordNo) {
        String key = blacklistVersionId + "|" + recordNo;

        JCS blacklistFileRecordCache = getBlacklistFileNextRecordCache();
        if (blacklistFileRecordCache != null) {
            Object value = blacklistFileRecordCache.get(key);
            if (value != null) {
                Integer nextRecordNo = (Integer) value;
                return nextRecordNo;
            }
        }

        Session session = this.getSession();
        Criteria criteria = session.createCriteria(BlacklistFile.class, "blacklistFile");

        criteria.add(Restrictions.eq("blacklistFile.blacklistVersion.id", blacklistVersionId));
        criteria.add(Restrictions.gt("blacklistFile.recordNo", recordNo));

        criteria.addOrder(Order.asc("blacklistFile.recordNo"));

        criteria.setMaxResults(1);

        List<BlacklistFile> list = criteria.list();
        if (list == null || list.size() <= 0) return 0;

        int nextRecordNo = list.get(0).getRecordNo();

        try {
            if (blacklistFileRecordCache != null) {
                blacklistFileRecordCache.put(key, new Integer(nextRecordNo));
                logger.debug("cached blacklistFileNextRecord key=" + key);
            }
        } catch (Exception e) {
            logger.warn("cannot put blacklistFileNextRecord into cache - " + e.getMessage());
        }

        return nextRecordNo;
    }

    @Override
    public void saveAll(List<BlacklistFile> object) {
        getHibernateTemplate().saveOrUpdateAll(object);
    }

}
