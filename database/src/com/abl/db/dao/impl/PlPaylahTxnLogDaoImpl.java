package com.abl.db.dao.impl;

import com.abl.db.dao.PlPaylahTxnLogDao;
import com.abl.db.model.PlPaylahTxnLog;

import java.util.List;

import org.springframework.stereotype.Repository;

@Repository
public class PlPaylahTxnLogDaoImpl extends DaoImpl implements PlPaylahTxnLogDao {

    @Override
    public PlPaylahTxnLog getPlPaylahTxnLog(long id) {
        return getHibernateTemplate().get(PlPaylahTxnLog.class, id);
    }
    
    @SuppressWarnings("unchecked")
	@Override
    public PlPaylahTxnLog getPlPaylahTxnLogByPlTxnLogId(long id) {
    	List<PlPaylahTxnLog> list = getHibernateTemplate().find("from PlPaylahTxnLog where plTxnLog.id=? order by id", id);
    	if ((list!=null)&&(!list.isEmpty())) {
    		return list.get(0);
    	} else {
    		return null;
    	}
    }
    
    @SuppressWarnings("unchecked")
	@Override
    public PlPaylahTxnLog getPlPaylahTxnLogByPlTxnLogIdAndMsgType(long id, String msgType) {
    	List<PlPaylahTxnLog> list = getHibernateTemplate().find("from PlPaylahTxnLog where plTxnLog.id=? and msgType=? order by id desc", id, msgType);
    	if ((list!=null)&&(!list.isEmpty())) {
    		return list.get(0);
    	} else {
    		return null;
    	}
    }
    
    @Override
    public void save(PlPaylahTxnLog txnLog) {
        getHibernateTemplate().save(txnLog);
    }
    
    @Override
    public void update(PlPaylahTxnLog txnLog) {
        getHibernateTemplate().update(txnLog);
    }
}
