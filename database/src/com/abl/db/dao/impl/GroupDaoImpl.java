package com.abl.db.dao.impl;

import com.abl.db.dao.GroupDao;
import com.abl.db.model.Group;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class GroupDaoImpl extends DaoImpl implements GroupDao {

    @Override
    public Group getGroupByName(String name) {
        List<Group> groups = getHibernateTemplate().find("from Group where upper(name)=?", name.toUpperCase());
        if ((groups != null) && (groups.size() > 0)) {
            return groups.get(0);
        } else {
            return null;
        }
    }
    
    @Override
    public List<Group> getGroupFetchRelease(){
    	List<Group> list =getHibernateTemplate().find("from Group grouping "+
    			"left join fetch grouping.release release "+
    			"left join fetch release.model where grouping.deleted=?",false);
		if ((list != null)&&(list.size()>0)) {
			return list;
		} else {
			return null;
		}
    }
    
    @Override
    public Group getGroupById(long groupId) {
    	List<Group> list = getHibernateTemplate().find("from Group grouping " +
    			"left join fetch grouping.release release "+
    			"left join fetch release.model model "+
    			" where grouping.id=?", groupId);
    	if ((list != null)&&(list.size()>0)) {
    		return list.get(0);
    	} else {
    		return null;
    	}
    }
    
    @Override
    public List<Group> getAllGroups(){
    	return getHibernateTemplate().find("from Group grouping where grouping.deleted=0 order by grouping.name" );
    }
    
	@Override
	public Group getGroupByReleaseId(long releaseId) {
		List<Group> list = getHibernateTemplate()
				.find("from Group grouping "
						+ "left join fetch grouping.release release "+
						" where grouping.deleted =0 and release.id=?",
						releaseId);
		if ((list != null) && (list.size() > 0)) {
			return list.get(0);
		} else {
			return null;

		}
	}
	
	@Override
	public List<Group> getAllGroupsExcludeId(long id) {
		return getHibernateTemplate().find("from Group grouping where grouping.deleted=0 and id<>? order by grouping.name",id );
	}

	@Override
	public Group getActiveGroupFetchReleaseById(long groupId) {
		List<Group> list = getHibernateTemplate().find("from Group grouping " +
				" join fetch grouping.release release "+
				" where grouping.deleted=0 and release.deleted=0 and grouping.id=?", groupId);
		if ((list != null)&&(list.size()>0)) {
			return list.get(0);
		} else {
			return null;
		}
	}
}
