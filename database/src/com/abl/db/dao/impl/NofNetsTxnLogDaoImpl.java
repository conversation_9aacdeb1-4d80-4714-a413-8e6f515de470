package com.abl.db.dao.impl;

import com.abl.db.dao.NofNetsTxnLogDao;
import com.abl.db.model.NofNetsTxnLog;

import java.util.List;

import org.springframework.stereotype.Repository;

@Repository
public class NofNetsTxnLogDaoImpl extends DaoImpl implements NofNetsTxnLogDao {

    @Override
    public NofNetsTxnLog getNofNetsTxnLog(long id) {
        return getHibernateTemplate().get(NofNetsTxnLog.class, id);
    }

    @SuppressWarnings("unchecked")
	@Override
    public NofNetsTxnLog getNofNetsTxnLogByNofTxnLogId(long id) {
    	List<NofNetsTxnLog> list = getHibernateTemplate().find("from NofNetsTxnLog where nofTxnLog.id=? order by id", id);
    	if ((list!=null)&&(!list.isEmpty())) {
    		return list.get(0);
    	} else {
    		return null;
    	}
    }
    
    @SuppressWarnings("unchecked")
	@Override
    public List<NofNetsTxnLog> getNofNetsTxnLogsFetchNofTxnLog(NofNetsTxnLog.ReversalState reversalState, int maxResults) {
    	return getSession().createQuery("select nofNetsTxnLog from NofNetsTxnLog nofNetsTxnLog join fetch nofNetsTxnLog.nofTxnLog "
    			+ "where nofNetsTxnLog.reversalState=:reversalState order by nofNetsTxnLog.id")
        		.setParameter("reversalState", reversalState)
        		.setMaxResults(maxResults)
        		.list();
    }
    
    @Override
    public int resetNewNofNetsTxnLogForReversal() {
    	return getSession().createQuery("update NofNetsTxnLog set status=:failStatus, reversalState=:pendingState where status=:newStatus and source=:source")
    			.setParameter("failStatus", NofNetsTxnLog.Status.FAIL)
    			.setParameter("pendingState", NofNetsTxnLog.ReversalState.PENDING)
    			.setParameter("newStatus", NofNetsTxnLog.Status.NEW)
    			.setParameter("source", NofNetsTxnLog.Source.BATCH)
    			.executeUpdate();
    }
    
    @Override
    public int resetReversingNofNetsTxnLogForReversal() {
    	return getSession().createQuery("update NofNetsTxnLog set reversalState=:pendingState where reversalState=:reversingState")
    			.setParameter("pendingState", NofNetsTxnLog.ReversalState.PENDING)
    			.setParameter("reversingState", NofNetsTxnLog.ReversalState.REVERSING)
    			.executeUpdate();
    }
    
    @Override
    public void save(NofNetsTxnLog txnLog) {
        getHibernateTemplate().save(txnLog);
    }
    
    @Override
    public void update(NofNetsTxnLog txnLog) {
        getHibernateTemplate().update(txnLog);
    }
}
