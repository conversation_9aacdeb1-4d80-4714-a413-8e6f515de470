package com.abl.db.dao.impl;

import org.apache.log4j.Logger;
import org.hibernate.LockOptions;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import com.abl.db.dao.SequenceDao;
import com.abl.db.model.SequenceTable;

@Repository
public class SequenceDaoImpl extends DaoImpl implements SequenceDao {

	private static final Logger logger=Logger.getLogger(SequenceDaoImpl.class);
	
	@Value("${datasource.driverClassName:com.mysql.jdbc.Driver}")
	private String dataClassName;
	
	private boolean sequenceSupported = false;;
	
	@Override
	protected void initDao() throws Exception {
		super.initDao();
		this.sequenceSupported = dataClassName.contains("oracle");
		logger.debug("sequenceSupported=" + sequenceSupported + ", dataClassName=" + dataClassName);
	}

	@Override
	public void createSequence(String sequenceName) {
		if (sequenceSupported) {
			getSession().createSQLQuery("create sequence " + sequenceName).executeUpdate();
		} else {
			SequenceTable sequenceTable = new SequenceTable();
			sequenceTable.setName(sequenceName);
			sequenceTable.setValue(1L);
			save(sequenceTable);
		}
	}
	
	@Override
	public Long nextSequence(String sequenceName) {
		if (sequenceSupported) {
			Number number = (Number)getSession().createSQLQuery("select " + sequenceName + ".nextval from dual")
					.uniqueResult();
			// oracle returns BigDecimal
			return number.longValue();
		} else {
			SequenceTable sequenceTable = (SequenceTable)getSession().createQuery("from SequenceTable where name=:name")
					.setString("name", sequenceName)
					.setLockOptions(LockOptions.UPGRADE)
					.uniqueResult();
			if (sequenceTable == null) {
				return null;
			}
			long value = sequenceTable.getValue();
			sequenceTable.setValue(value+1);
			save(sequenceTable);
			return value;
		}
	}
}
