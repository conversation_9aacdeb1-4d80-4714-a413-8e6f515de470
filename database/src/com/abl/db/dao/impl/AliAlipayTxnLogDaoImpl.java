package com.abl.db.dao.impl;

import com.abl.db.dao.AliAlipayTxnLogDao;
import com.abl.db.model.AliAlipayTxnLog;
import org.hibernate.Criteria;
import org.hibernate.FetchMode;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class AliAlipayTxnLogDaoImpl extends DaoImpl implements AliAlipayTxnLogDao {

    @Override
    public AliAlipayTxnLog getAliAlipayTxnLog(long id) {
        return getHibernateTemplate().get(AliAlipayTxnLog.class, id);
    }

    @Override
    public void save(AliAlipayTxnLog txnLog) {
        getHibernateTemplate().save(txnLog);
    }
    
    @Override
    public void update(AliAlipayTxnLog txnLog) {
        getHibernateTemplate().update(txnLog);
    }

    @SuppressWarnings("unchecked")
    @Override
    public AliAlipayTxnLog getAliAlipayTxnLogByAliTxnLogId(long id) {
        List<AliAlipayTxnLog> list = getHibernateTemplate().find("from AliAlipayTxnLog where aliTxnLog.id=? order by id desc", id);
        if ((list!=null)&&(!list.isEmpty())) {
            return list.get(0);
        } else {
            return null;
        }
    }

    @Override
    public AliAlipayTxnLog getSuccessfulFreezeByAliTxnLogId(long id) {
        List<AliAlipayTxnLog> list = getHibernateTemplate().find(
                "from AliAlipayTxnLog atl where atl.aliTxnLog.id=? and atl.status=? and atl.msgType = ? order by id desc",
                id, AliAlipayTxnLog.Status.SUCCESS, AliAlipayTxnLog.MSG_TYPE_PREAUTH);
        if ((list!=null)&&(!list.isEmpty())) {
            return list.get(0);
        } else {
            return null;
        }
    }

    @Override
    public int updateAliAlipayStatus(long id, AliAlipayTxnLog.Status status){
        return getSession().createQuery(
                "UPDATE AliAlipayTxnLog SET status = :status WHERE(id = :id)"
        )
                .setParameter("status", status)
                .setParameter("id", id)
                .executeUpdate();
    }

    @Override
    public AliAlipayTxnLog getByBookingRef(final String bookingRef, final String msgType, final String ascOrDesc) {
        StringBuilder hqlBuffer = new StringBuilder(
                "FROM AliAlipayTxnLog aa "
                        + "JOIN aa.aliTxnLog atl "
                + "WHERE( "
                        + "atl.bookingRef = ?"
                        + " AND "
                        + "aa.msgType = ? "
                + ")"
        );

        if(ascOrDesc != null){
            hqlBuffer.append(" ORDER BY aa.id " + ascOrDesc);
        }

        Criteria criteria = getSession().createCriteria(AliAlipayTxnLog.class, "aa");
        criteria.setFetchMode("aa.aliTxnLog", FetchMode.JOIN);
        criteria.createAlias("aa.aliTxnLog", "atl");

        criteria.add(Restrictions.eq("aa.msgType", msgType));
        criteria.add(Restrictions.eq("atl.bookingRef", bookingRef));

        if(ascOrDesc != null){
            if(ascOrDesc.equalsIgnoreCase("ASC")){
                criteria.addOrder(Order.asc("aa.id"));
            } else if (ascOrDesc.equalsIgnoreCase("DESC")){
                criteria.addOrder(Order.desc("aa.id"));
            }
        }

//        Query query = getSession().createQuery(hqlBuffer.toString());
//        query.setParameter("bookingRef", bookingRef);
//        query.setParameter("msgType", msgType);
        List<AliAlipayTxnLog> list = criteria.list();
        if(list != null && !list.isEmpty()){
            return list.get(0);
        }
        return null;
    }
}
