package com.abl.db.dao.impl;

import com.abl.db.dao.ProfileBinValueDao;
import com.abl.db.model.ProfileBinValue;
import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class ProfileBinValueDaoImpl extends DaoImpl implements ProfileBinValueDao {
    @Override
    public void saveAll(List<ProfileBinValue> object) {
        getHibernateTemplate().saveOrUpdateAll(object);
    }

    @Override
    public List<ProfileBinValue> getProfileBinValuesByBinRange(long profileBinRangeId) {
        Session session = this.getSession();
        Criteria criteria = session.createCriteria(ProfileBinValue.class, "profileBinValue");

        if (profileBinRangeId > 0) {
            criteria.add(Restrictions.eq("profileBinValue.pk.profileBinRange.id", profileBinRangeId));
        }
        criteria.addOrder(Order.asc("profileBinValue.pk.profileBinRange.id"));
        criteria.addOrder(Order.asc("profileBinValue.pk.parameterDefinition.id"));

        List<ProfileBinValue> list = criteria.list();
        return list;

    }
}
