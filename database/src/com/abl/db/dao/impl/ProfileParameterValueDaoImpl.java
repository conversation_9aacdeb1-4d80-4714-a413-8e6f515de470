package com.abl.db.dao.impl;

import com.abl.db.dao.ProfileParameterValueDao;
import com.abl.db.model.ProfileParameterValue;
import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class ProfileParameterValueDaoImpl extends DaoImpl implements ProfileParameterValueDao {

    @Override
    public void saveAll(List<ProfileParameterValue> object) {
        getHibernateTemplate().saveOrUpdateAll(object);
    }

    @Override
    public List<ProfileParameterValue> getProfileParameterValuesByProfile(long terminalProfileId) {
        Session session = this.getSession();
        Criteria criteria = session.createCriteria(ProfileParameterValue.class, "profileParameterValue");

        if (terminalProfileId > 0) {
            criteria.add(Restrictions.eq("profileParameterValue.pk.terminalProfile.id", terminalProfileId));
        }
        criteria.addOrder(Order.asc("profileParameterValue.pk.terminalProfile.id"));
        criteria.addOrder(Order.asc("profileParameterValue.pk.parameterDefinition.id"));

        List<ProfileParameterValue> list = criteria.list();
        return list;

    }
}
