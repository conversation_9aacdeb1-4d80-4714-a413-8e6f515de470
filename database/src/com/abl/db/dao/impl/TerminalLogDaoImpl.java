package com.abl.db.dao.impl;

import com.abl.db.dao.TerminalLogDao;
import com.abl.db.model.TerminalLog;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public class TerminalLogDaoImpl extends DaoImpl implements TerminalLogDao {

    @Override
    @SuppressWarnings("unchecked")
    public TerminalLog getTerminaLogByTerminalDate(long terminalId, Date date) {
        List<TerminalLog> list = getHibernateTemplate().find("from TerminalLog terminalLog " +
                "where terminalLog.tmsTerminal.id=? and terminalLog.logDate=?",
                terminalId, date);
        if ((list != null) && (list.size() > 0)) {
            return list.get(0);
        } else {
            return null;
        }
    }
}
