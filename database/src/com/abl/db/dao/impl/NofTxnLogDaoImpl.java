package com.abl.db.dao.impl;

import com.abl.db.dao.NofTxnLogDao;
import com.abl.db.model.NofTxnLog;

import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Repository;

@Repository
public class NofTxnLogDaoImpl extends DaoImpl implements NofTxnLogDao {

    @Override
    public NofTxnLog getNofTxnLog(long id) {
        return getHibernateTemplate().get(NofTxnLog.class, id);
    }

    @SuppressWarnings("unchecked")
	@Override
    public NofTxnLog getNofTxnLogByRrn(String rrn) {
    	List<NofTxnLog> list = getHibernateTemplate().find("from NofTxnLog where rrn=? order by id desc", rrn);
    	if ((list != null)&&(!list.isEmpty())) {
    		return list.get(0);
    	} else {
    		return null;
    	}
    }
    
    @SuppressWarnings("unchecked")
	@Override
    public NofTxnLog getPurchaseNofTxnLogByMidTidStan(String mid, String tid, String stan) {
    	List<NofTxnLog> list = getHibernateTemplate().find("from NofTxnLog where mti=? and mid=? and tid=? and stan=? order by id desc", "0200", mid, tid, stan);
    	if ((list != null)&&(!list.isEmpty())) {
    		return list.get(0);
    	} else {
    		return null;
    	}
    }
    
    @SuppressWarnings("unchecked")
	@Override
    public NofTxnLog getOfflinePurchaseNofTxnLogByMidTidStanRespCode(String mid, String tid, String stan, String respCode) {
    	List<NofTxnLog> list = getHibernateTemplate().find("from NofTxnLog where mti=? and mid=? and tid=? and stan=? and respCode=? order by id desc", "0220", mid, tid, stan, respCode);
    	if ((list != null)&&(!list.isEmpty())) {
    		return list.get(0);
    	} else {
    		return null;
    	}
    }
    
    @SuppressWarnings("unchecked")
	@Override
    public NofTxnLog getOfflinePurchaseNofTxnLogByTransIdRespCode(String transId, String respCode) {
    	List<NofTxnLog> list = getHibernateTemplate().find("from NofTxnLog where msgType=? and transId=? and respCode=? order by id desc", NofTxnLog.MSG_TYPE_OFFLINE_PURCHASE, transId, respCode);
    	if ((list != null)&&(!list.isEmpty())) {
    		return list.get(0);
    	} else {
    		return null;
    	}
    }
    
    @SuppressWarnings("unchecked")
	@Override
    public NofTxnLog getPurchaseNofTxnLogByTransId(String transId) {
    	List<NofTxnLog> list = getHibernateTemplate().find("from NofTxnLog where msgType=? and transId=? order by id desc", NofTxnLog.MSG_TYPE_PURCHASE, transId);
    	if ((list != null)&&(!list.isEmpty())) {
    		return list.get(0);
    	} else {
    		return null;
    	}
    }
    
    @SuppressWarnings("unchecked")
    @Override
	public List<NofTxnLog> getNofTxnLogs(NofTxnLog.Status status, int maxResults) {
    	return getSession().createQuery("from NofTxnLog where status=:status order by id")
    		.setParameter("status", status)
    		.setMaxResults(maxResults)
    		.list();
    }
    
    @Override
    public int updateTxResultAndStatus(long id, String txResult, NofTxnLog.Status status) {
    	return getSession().createQuery("update NofTxnLog set txResult=:txResult, status=:status where id=:id")
    			.setString("txResult", txResult)
    			.setParameter("status", status)
    			.setLong("id", id)
    			.executeUpdate();
    }
    
    @Override
    public int resetPendingStatus() {
    	return getSession().createQuery("update NofTxnLog set status=:pendingStatus where status=:processingStatus")
    			.setParameter("pendingStatus", NofTxnLog.Status.PENDING)
    			.setParameter("processingStatus", NofTxnLog.Status.PROCESSING)
    			.executeUpdate();
    }
    
    @Override
    public void save(NofTxnLog txnLog) {
        getHibernateTemplate().save(txnLog);
    }

	@SuppressWarnings("unchecked")
	@Override
	public List<NofTxnLog> getNofDeclinedPaymentTxns(NofTxnLog.DcpStatus dcpStatus, Date retryDt, int maxResults) {
		return getSession().createQuery("from NofTxnLog where dcpStatus=:dcpStatus and lastSentDcpDateTime<=:retryDt order by id")
				.setParameter("dcpStatus", dcpStatus)
				.setTimestamp("retryDt", retryDt)
				.setMaxResults(maxResults)
				.list();
	}

	@Override
	public int updateDcpStatus(long id, NofTxnLog.DcpStatus dcpStatus) {
		return getSession().createQuery("update NofTxnLog set dcpStatus=:dcpStatus where id=:id")
				.setParameter("dcpStatus", dcpStatus)
				.setLong("id", id)
				.executeUpdate();
	}
}
