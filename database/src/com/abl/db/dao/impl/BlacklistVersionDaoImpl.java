package com.abl.db.dao.impl;

import com.abl.db.dao.BlacklistVersionDao;
import com.abl.db.model.BlacklistVersion;
import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public class BlacklistVersionDaoImpl extends DaoImpl implements BlacklistVersionDao {

    @Override
    public BlacklistVersion getBlacklistVersion(long id) {
        return getHibernateTemplate().get(BlacklistVersion.class, id);
    }

    @Override
    public BlacklistVersion getBlacklistVersionByVersion(int version) {
        List<BlacklistVersion> list = getHibernateTemplate().find("from BlacklistVersion blacklistVersion " +
                "where blacklistVersion.version = ?", version);
        if ((list != null) && (list.size() > 0)) {
            return list.get(0);
        } else {
            return null;
        }
    }

    @Override
    public BlacklistVersion getLatestBlacklistVersion(short status) {
        Session session = this.getSession();
        Criteria criteria = session.createCriteria(BlacklistVersion.class, "blacklistVersion");

        if (status >= 0) {
            criteria.add(Restrictions.eq("blacklistVersion.status", status));
        }
        criteria.addOrder(Order.desc("blacklistVersion.version"));

        List<BlacklistVersion> list = criteria.list();
        if ((list != null) && (list.size() > 0)) {
            return list.get(0);
        } else {
            return null;
        }
    }

    @Override
    public BlacklistVersion getBlacklistVersion(String fullCans, String fullRange, String smallCans, String smallRange) {
        Session session = this.getSession();
        Criteria criteria = session.createCriteria(BlacklistVersion.class, "blacklistVersion");

        if (fullCans != null) {
            criteria.add(Restrictions.eq("blacklistVersion.fullCans", fullCans).ignoreCase());
        }
        if (fullRange != null) {
            criteria.add(Restrictions.eq("blacklistVersion.fullRange", fullRange).ignoreCase());
        }
        if (smallCans != null) {
            criteria.add(Restrictions.eq("blacklistVersion.smallCans", smallCans).ignoreCase());
        }
        if (smallRange != null) {
            criteria.add(Restrictions.eq("blacklistVersion.smallRange", smallRange).ignoreCase());
        }

        criteria.addOrder(Order.desc("blacklistVersion.version"));

        List<BlacklistVersion> list = criteria.list();
        if ((list != null) && (list.size() > 0)) {
            return list.get(0);
        } else {
            return null;
        }
    }
    
    @Override
    public List<BlacklistVersion> getBlacklistVersionByStatus(short status){
    	List<BlacklistVersion> list =getHibernateTemplate().find(" from BlacklistVersion blacklistVersion "+
    			"where blacklistVersion.status =? order by blacklistVersion.scheduleDate ", status );
    	if ((list != null)&&(list.size()>0)) {
			return list;
		} else {
			return null;
		}
    }
    
    @Override
    public BlacklistVersion getBlacklistVersionByScheduleDate(Date scheduleDate){
    	Session session = this.getSession();
        Criteria criteria = session.createCriteria(BlacklistVersion.class, "blacklistVersion");
        
        if (scheduleDate != null) {
			criteria.add(Restrictions.eq("blacklistVersion.scheduleDate", scheduleDate));
		}
        
        List<BlacklistVersion> list = criteria.list();
        if ((list != null) && (list.size() > 0)) {
            return list.get(0);
        } else {
            return null;
        }
    	
    }
    
    @Override
    public BlacklistVersion getPendingBlacklistVersionByScheduleDateForBatch(Date scheduleDate){
    	Session session = this.getSession();
        Criteria criteria = session.createCriteria(BlacklistVersion.class, "blacklistVersion");
        
        if (scheduleDate != null) {
			criteria.add(Restrictions.le("blacklistVersion.scheduleDate", scheduleDate));
		}
        criteria.add(Restrictions.eq("blacklistVersion.status", BlacklistVersion.PENDING));
        criteria.addOrder(Order.desc("blacklistVersion.scheduleDate"));
        List<BlacklistVersion> list = criteria.list();
        if ((list != null) && (list.size() > 0)) {
            return list.get(0);
        } else {
            return null;
        }
    	
    }
    
    
}
