package com.abl.db.dao.impl;

import com.abl.db.dao.FailedDownloadDao;
import com.abl.db.model.FailedDownload;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class FailedDownloadDaoImpl extends DaoImpl implements FailedDownloadDao {

    @Override
    public List<FailedDownload> getFailedDownload(String vehicleNo, String serialNo) {
        List<FailedDownload> list = getHibernateTemplate().find("from FailedDownload failedDownload where " +
                "(failedDownload.vehicleNo=? or failedDownload.serialNo=?) and failedDownload.deleted=?",
                vehicleNo, serialNo, false);

        return list;
    }
}
