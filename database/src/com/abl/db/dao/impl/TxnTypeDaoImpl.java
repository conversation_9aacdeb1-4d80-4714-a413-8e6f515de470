package com.abl.db.dao.impl;

import com.abl.db.dao.TxnTypeDao;
import com.abl.db.model.TxnType;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class TxnTypeDaoImpl extends DaoImpl implements TxnTypeDao {

    @Override
    public TxnType getTxnType(String code) {
        return getHibernateTemplate().get(TxnType.class, code);
    }

    @SuppressWarnings("unchecked")
    @Override
    public TxnType getTxnTypeForName(String name) {
        List<TxnType> list = getHibernateTemplate().find("from TxnType where name=?", name);
        if ((list != null) && (list.size() > 0)) {
            return list.get(0);
        } else {
            return null;
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<TxnType> getTxnTypes() {
        return getHibernateTemplate().find("from TxnType");
    }

}
