package com.abl.db.dao.impl;

import com.abl.db.dao.PlAccountDao;
import com.abl.db.model.PlAccount;

import org.springframework.stereotype.Repository;

@Repository
public class PlAccountDaoImpl extends DaoImpl implements PlAccountDao {
	
    @Override
    public PlAccount getPlAccount(long id) {
        return getHibernateTemplate().get(PlAccount.class, id);
    }

	@Override
	public PlAccount getPlAccountByUserId(String userId) {
		return (PlAccount)getSession()
				.createQuery("from PlAccount where userId=:userId")
				.setString("userId", userId)
				.uniqueResult();
	}
	
	@Override
    public void saveOrUpdate(PlAccount account) {
        getHibernateTemplate().saveOrUpdate(account);
    }
	
    @Override
    public void save(PlAccount account) {
        getHibernateTemplate().save(account);
    }

	@Override
	public void update(PlAccount account) {
		getHibernateTemplate().update(account);
	}
}
