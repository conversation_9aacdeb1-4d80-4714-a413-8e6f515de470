package com.abl.db.dao.impl;

import com.abl.db.dao.TerminalBlacklistDownloadJobDao;
import com.abl.db.model.BlacklistDownloadJob;
import com.abl.db.model.TerminalBlacklistDownloadJob;
import com.abl.db.model.TerminalBlacklistDownloadJobId;
import com.abl.utils.DateUtils;
import org.hibernate.Session;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public class TerminalBlacklistDownloadJobDaoImpl extends DaoImpl implements
        TerminalBlacklistDownloadJobDao {

    @Override
    public void save(TerminalBlacklistDownloadJob terminalBlacklistDownloadJob) {
        getHibernateTemplate().saveOrUpdate(terminalBlacklistDownloadJob);
    }

    @Override
    public List<TerminalBlacklistDownloadJob> getNotSuccessTerminalBlacklistDownloadJobs(long blacklistVersionId, long terminalId, String vehicleId) {
        return getHibernateTemplate().find("from TerminalBlacklistDownloadJob termDownload " +
                "where termDownload.status!=0 " +
                "and termDownload.pk.blacklistVersion.id=? and termDownload.pk.tmsTerminal.id=? and termDownload.pk.vehicle.vehicleId=?",
                blacklistVersionId, terminalId, vehicleId.toUpperCase());
    }

    @Override
    public List<TerminalBlacklistDownloadJob> getInProgressTerminalBlacklistDownloadJobs(long terminalId) {
        return getHibernateTemplate().find("from TerminalBlacklistDownloadJob termDownload " +
                "where termDownload.status=1 " +
                "and termDownload.pk.tmsTerminal.id=?",
                terminalId);
    }

    @Override
    public List<TerminalBlacklistDownloadJob> getInProgressTerminalBlacklistDownloadJobs(String vehicleId) {
        return getHibernateTemplate().find("from TerminalBlacklistDownloadJob termDownload " +
                "where termDownload.status=1 " +
                "and termDownload.pk.vehicle.vehicleId=?",
                vehicleId.toUpperCase());
    }

    @Override
    public int countTerminalBlacklistDownloadJobs(long jobId, short status, long terminalId) {
        List<TerminalBlacklistDownloadJob> list = getHibernateTemplate().find("from TerminalBlacklistDownloadJob termDownload " +
                "where termDownload.pk.blacklistDownloadJob.id=? and termDownload.status=? and termDownload.pk.tmsTerminal.id!=?",
                jobId, status, terminalId);
        if (list == null) return 0;
        return list.size();
    }

    @Override
    public TerminalBlacklistDownloadJob getByPk(TerminalBlacklistDownloadJobId terminalBlacklistDownloadJobId) {
        List<TerminalBlacklistDownloadJob> termJobs = getHibernateTemplate().find("from TerminalBlacklistDownloadJob termDownload " +
                "where termDownload.pk.blacklistDownloadJob.id=? and termDownload.pk.tmsTerminal.id=? " +
                "and termDownload.pk.vehicle.vehicleId=? and termDownload.pk.blacklistVersion.id=?",
                terminalBlacklistDownloadJobId.getBlacklistDownloadJob().getId(),
                terminalBlacklistDownloadJobId.getTmsTerminal().getId(),
                terminalBlacklistDownloadJobId.getVehicle().getVehicleId().toUpperCase(),
                terminalBlacklistDownloadJobId.getBlacklistVersion().getId());
        if (termJobs == null || termJobs.size() <= 0) return null;
        return termJobs.get(0);
    }

    @Override
    public int releaseOtherJobs(TerminalBlacklistDownloadJob terminalBlacklistDownloadJob) {
        Session session = this.getSession();

        String hql = "UPDATE TerminalBlacklistDownloadJob termDownload set status = 3 " +
                "WHERE termDownload.pk.blacklistDownloadJob.id != :blacklistDownloadJobId " +
                "and termDownload.pk.tmsTerminal.id = :tmsTerminalId " +
                "and termDownload.pk.vehicle.vehicleId = :vehicleId " +
                "and termDownload.pk.blacklistVersion.id = :blacklistVersionId " +
                "and status = 1";
        org.hibernate.Query query = session.createQuery(hql);
        query.setParameter("blacklistDownloadJobId", terminalBlacklistDownloadJob.getPk().getBlacklistDownloadJob().getId());
        query.setParameter("tmsTerminalId", terminalBlacklistDownloadJob.getPk().getTmsTerminal().getId());
        query.setParameter("vehicleId", terminalBlacklistDownloadJob.getPk().getVehicle().getVehicleId());
        query.setParameter("blacklistVersionId", terminalBlacklistDownloadJob.getPk().getBlacklistVersion().getId());
        int result = query.executeUpdate();
        return result;
    }

    @Override
    public TerminalBlacklistDownloadJob findTerminalBlacklistDownloadJob(long terminalId, String vehicleId, long blacklistVersionId) {
        String currentTime = DateUtils.format(new Date(), "HHmm");
        List<TerminalBlacklistDownloadJob> termJobs = getHibernateTemplate().find("from TerminalBlacklistDownloadJob termDownload " +
                "where termDownload.pk.tmsTerminal.id=? " +
                "and termDownload.pk.vehicle.vehicleId=? " +
                "and termDownload.pk.blacklistVersion.id=? " +
                "and termDownload.status=? " +
                "and termDownload.pk.blacklistDownloadJob.deleted=0 " +
                "and termDownload.pk.blacklistDownloadJob.status=? " +
                "and ((termDownload.pk.blacklistDownloadJob.startWindow=termDownload.pk.blacklistDownloadJob.endWindow) " +
                "or (termDownload.pk.blacklistDownloadJob.startWindow<termDownload.pk.blacklistDownloadJob.endWindow and termDownload.pk.blacklistDownloadJob.startWindow<=? and termDownload.pk.blacklistDownloadJob.endWindow>=?) " +
                "or (termDownload.pk.blacklistDownloadJob.startWindow>termDownload.pk.blacklistDownloadJob.endWindow and (termDownload.pk.blacklistDownloadJob.startWindow<=? or termDownload.pk.blacklistDownloadJob.endWindow>=?)))",
                terminalId,
                vehicleId,
                blacklistVersionId,
                TerminalBlacklistDownloadJob.IN_PROGRESS,
                BlacklistDownloadJob.ACTIVE,
                currentTime, currentTime,
                currentTime, currentTime);
        if (termJobs == null || termJobs.size() <= 0) return null;
        return termJobs.get(0);
    }
}
