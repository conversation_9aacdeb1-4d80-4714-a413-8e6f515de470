package com.abl.db.dao.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import org.hibernate.Criteria;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Repository;

import com.abl.db.dao.VcTxnLogDao;
import com.abl.db.model.VcTxnLog;
import com.abl.db.model.VcTxnLog.MsgType;
import com.abl.db.model.VcTxnLog.Status;

@Repository
public class VcTxnLogDaoImpl extends DaoImpl implements VcTxnLogDao {

	@Override
	public void save(VcTxnLog vcTxnLog) {
		getHibernateTemplate().saveOrUpdate(vcTxnLog);
	}

	@Override
	public Boolean isOfflineTxnExist(String maskedCan, String jobNumber, String stan) {
		Boolean exists = false;
		Session session = this.getSession();
		Criteria criteria = session.createCriteria(VcTxnLog.class)
				.add(Restrictions.eq("maskedCan", maskedCan))
				.add(Restrictions.eq("jobNumber", jobNumber))
				.add(Restrictions.eq("stan", stan));

		@SuppressWarnings("unchecked")
		List<VcTxnLog> list = criteria.list();
		if (!list.isEmpty()) {
			exists = true;
		}

		return exists;
	}

	@Override
	public List<VcTxnLog> getNewOfflineSalesTxnByDateAndStatus(Status connErrStatus, Date newTxnDt, Date connErrDt,
			Date timeoutDt, Date lastRetryDt) {
		Session session = getSession();
		Query newOfflineSaleQuery =session
				.createQuery(
						"from VcTxnLog m where m.msgType=:msgType and m.txnDateTime<=:newTxnDt"
								+ " and ((m.status ='NEW' and m.responseCode='00') "
								+ "or (m.status=:connErrStatus and m.firstDcpCall>=:connErrDt and m.lastDcpCall<=:lastRetryDt) "
								+ "or (m.status in ('TIMEOUT', 'HTTP_ERR') and m.firstDcpCall >=:timeoutDt and m.lastDcpCall<=:lastRetryDt)) "
								+ "order by m.id asc");


		newOfflineSaleQuery.setParameter("msgType", MsgType.OFFLINE_SALES);
		newOfflineSaleQuery.setTimestamp("newTxnDt", newTxnDt);
		newOfflineSaleQuery.setParameter("connErrStatus", Status.CONN_ERR);
		newOfflineSaleQuery.setTimestamp("connErrDt", connErrDt);
		newOfflineSaleQuery.setTimestamp("timeoutDt", timeoutDt);
		newOfflineSaleQuery.setTimestamp("lastRetryDt", lastRetryDt);

		@SuppressWarnings("unchecked")
		List<VcTxnLog> newOfflineSaleList = newOfflineSaleQuery.list();

		logger.debug("virtual cabcharge offline sale list size=" + newOfflineSaleList.size());
		if(newOfflineSaleList.size()>0){
			Query updateStatusToQueueQuery = null;
			int startIdx = 0;
			int batchIdx=0;

			Long[] txnIdList = new Long[100];
			Map<Integer, Long[]> txnMap = new HashMap<Integer, Long[]>();

			String updateSql ="update VcTxnLog m set m.status=:queueStatus ";
			for(VcTxnLog txn: newOfflineSaleList) {
				if(startIdx<100) {
					txnIdList[startIdx]=txn.getId();
				} else {	
					if(batchIdx==0) {
						updateSql =updateSql+" where m.id IN (:idList"+batchIdx+")";
					} else {
						updateSql =updateSql+" or m.id IN (:idList"+batchIdx+")";
					}

					txnMap.put(batchIdx,txnIdList);
					startIdx=0;
					txnIdList = new Long[100];
					txnIdList[startIdx]=txn.getId();
					batchIdx++;
				}
				startIdx++;
			}

			if(startIdx>=1 && txnIdList[0]!=null){
				
				Long lastMpList[] = new Long[startIdx];
				
				for(int i=0;i<startIdx;i++) {
					lastMpList[i]=txnIdList[i];
				}
				
				if(batchIdx==0){
					updateSql =updateSql+" where m.id IN (:idList"+batchIdx+")";
					txnMap.put(batchIdx,lastMpList);
				}else{
					updateSql =updateSql+" or m.id IN (:idList"+batchIdx+")";
					txnMap.put(batchIdx,lastMpList);
				}
			}

			updateSql =updateSql+ " order by id asc";

			logger.debug("updateSql:"+updateSql);

			updateStatusToQueueQuery = session.createQuery(updateSql);
			updateStatusToQueueQuery.setParameter("queueStatus", Status.QUEUE);			

			Iterator<Entry<Integer, Long[]>> it = txnMap.entrySet().iterator();
			while (it.hasNext()) {
				Map.Entry<Integer, Long[]> pair = (Map.Entry<Integer, Long[]>)it.next();
				int idx = (Integer) pair.getKey();
				Long[] txnIds = (Long[]) pair.getValue();
				updateStatusToQueueQuery.setParameterList("idList"+idx, txnIds);

			}

			int result = updateStatusToQueueQuery.executeUpdate();
			logger.debug("update status to QUEUE, result="+result);
		}

		return newOfflineSaleList;
	}

	@Override
	public void saveAll(List<VcTxnLog> object) {
		getHibernateTemplate().saveOrUpdateAll(object);
	}

	@Override
	public VcTxnLog getVcTxnLogs(String maskedCan, String jobNumber, String stan, String msgType) {
		Session session = this.getSession();
		VcTxnLog vcTxnLog = null;
		Criteria criteria = session.createCriteria(VcTxnLog.class)
				.add(Restrictions.eq("maskedCan", maskedCan))
				.add(Restrictions.eq("jobNumber", jobNumber))
				.add(Restrictions.eq("stan", stan))
				.add(Restrictions.eq("msgType", MsgType.valueOf(msgType)));
		@SuppressWarnings("unchecked")
		List<VcTxnLog> list = criteria.list();
		if (!list.isEmpty()) {
			vcTxnLog = list.get(0);
		}
		return vcTxnLog;
	}

	@Override
	public VcTxnLog getReversedVcTxnLogs(String maskedCan, String jobNumber, MsgType msgType, String stan) {
		Session session = this.getSession();
		VcTxnLog vcTxnLog = null;
		Criteria criteria = session.createCriteria(VcTxnLog.class)
				.add(Restrictions.eq("maskedCan", maskedCan))
				.add(Restrictions.eq("jobNumber", jobNumber))
				.add(Restrictions.eq("msgType", msgType))
				.add(Restrictions.eq("stan", stan))
				.add(Restrictions.eq("reversed", Boolean.TRUE));
		@SuppressWarnings("unchecked")
		List<VcTxnLog> list = criteria.list();
		if (!list.isEmpty()) {
			vcTxnLog = list.get(0);
		}
		return vcTxnLog;
	}

	@Override
	public Boolean isVcTxnExist(String maskedCan, String jobNumber, String stan, String msgType) {
		Boolean exists = false;
		Session session = this.getSession();
		Criteria criteria = session.createCriteria(VcTxnLog.class)
				.add(Restrictions.eq("maskedCan", maskedCan))
				.add(Restrictions.eq("jobNumber", jobNumber))
				.add(Restrictions.eq("stan", stan))
				.add(Restrictions.eq("msgType", MsgType.valueOf(msgType)));

		@SuppressWarnings("unchecked")
		List<VcTxnLog> list = criteria.list();
		if (!list.isEmpty()) {
			exists = true;
		}

		return exists;
	}

	@Override
	public VcTxnLog getOriSaleVcTxnForVoid(String rrn, MsgType msgType) {
		Session session = this.getSession();
		VcTxnLog vcTxnLog = null;
		Criteria criteria = session.createCriteria(VcTxnLog.class)
				.add(Restrictions.eq("rrn", rrn))
				.add(Restrictions.eq("msgType", msgType));
		@SuppressWarnings("unchecked")
		List<VcTxnLog> list = criteria.list();
		if (!list.isEmpty()) {
			vcTxnLog = list.get(0);
		}
		return vcTxnLog;
	}

	@Override
	public VcTxnLog getOriVoidVcTxnForReversal(String stan) {
		Session session = this.getSession();
		VcTxnLog vcTxnLog = null;
		Criteria criteria = session.createCriteria(VcTxnLog.class)
				.add(Restrictions.eq("stan", stan));
		@SuppressWarnings("unchecked")
		List<VcTxnLog> list = criteria.list();
		if (!list.isEmpty()) {
			vcTxnLog = list.get(0);
		}
		return vcTxnLog;
	}

	@Override
	public void merge(VcTxnLog vcTxnLog) {
		getHibernateTemplate().merge(vcTxnLog);
	}

	@Override
	public List<VcTxnLog> getAllOfflineSalesTxnByDateAndStatus(Status queueStatus, Status processStatus,
			Status connErrStatus, Date newTxnDt, Date connErrDt, Date timeoutDt, Date lastRetryDt) {
		Session session = getSession();
		Query offlineSalesInQueueQuery = session
				.createQuery(
						"from VcTxnLog m where m.msgType=:msgType and m.txnDateTime<=:newTxnDt"
								+ " and ((m.status ='NEW' and m.responseCode='00') "
								+ "or (m.status=:connErrStatus and m.firstDcpCall>=:connErrDt and m.lastDcpCall<=:lastRetryDt) "
								+ "or (m.status=:processStatus) "
								+ "or (m.status=:queueStatus) "
								+ "or (m.status in ('TIMEOUT', 'HTTP_ERR') and m.firstDcpCall >=:timeoutDt and m.lastDcpCall<=:lastRetryDt)) "
								+ "order by m.id asc");

		offlineSalesInQueueQuery.setParameter("msgType", MsgType.OFFLINE_SALES);
		offlineSalesInQueueQuery.setTimestamp("newTxnDt", newTxnDt);
		offlineSalesInQueueQuery.setParameter("connErrStatus", Status.CONN_ERR);
		offlineSalesInQueueQuery.setTimestamp("connErrDt", connErrDt);
		offlineSalesInQueueQuery.setTimestamp("timeoutDt", timeoutDt);
		offlineSalesInQueueQuery.setTimestamp("lastRetryDt", lastRetryDt);
		offlineSalesInQueueQuery.setParameter("processStatus", Status.PROCCESSING);
		offlineSalesInQueueQuery.setParameter("queueStatus", Status.QUEUE);

		@SuppressWarnings("unchecked")
		List<VcTxnLog> offlineSalesInQueueList = offlineSalesInQueueQuery.list();
		logger.debug("list size=" + offlineSalesInQueueList.size());

		if(offlineSalesInQueueList.size()>0) {
			Query updateStatusQuery = null;
			int startIdx = 0;
			int batchIdx=0;

			Long[] txnIdList = new Long[100];
			HashMap<Integer, Long[]> hmTxn = new HashMap<Integer, Long[]>();

			String updateSql ="update VcTxnLog m set m.status=:queueStatus ";
			for(VcTxnLog txn: offlineSalesInQueueList) {
				if(startIdx<100) {
					txnIdList[startIdx]=txn.getId();
				} else {	
					if(batchIdx==0) {
						updateSql =updateSql+" where m.id IN (:idList"+batchIdx+")";
					} else {
						updateSql =updateSql+" or m.id IN (:idList"+batchIdx+")";
					}

					hmTxn.put(batchIdx,txnIdList);
					startIdx=0;
					txnIdList = new Long[100];
					txnIdList[startIdx]=txn.getId();
					batchIdx++;
				}
				startIdx++;
			}

			if(startIdx>=1 && txnIdList[0]!=null){
				
				Long lastMpList[] = new Long[startIdx];
				
				for(int i=0;i<startIdx;i++) {
					lastMpList[i]=txnIdList[i];
				}
				
				if(batchIdx==0){
					updateSql =updateSql+" where m.id IN (:idList"+batchIdx+")";
					hmTxn.put(batchIdx,lastMpList);
				}else{
					updateSql =updateSql+" or m.id IN (:idList"+batchIdx+")";
					hmTxn.put(batchIdx,lastMpList);
				}
			}

			updateSql =updateSql+ " order by id asc";

			logger.debug("updateSql:"+updateSql);

			updateStatusQuery = session.createQuery(updateSql);
			updateStatusQuery.setParameter("queueStatus", Status.QUEUE);			

			Iterator<Entry<Integer, Long[]>> it = hmTxn.entrySet().iterator();
			while (it.hasNext()) {
				Map.Entry<Integer, Long[]> pair = (Map.Entry<Integer, Long[]>)it.next();
				int idx = (Integer) pair.getKey();
				Long[] txnIds = (Long[]) pair.getValue();
				updateStatusQuery.setParameterList("idList"+idx, txnIds);

			}

			int result = updateStatusQuery.executeUpdate();
			logger.debug("update offline sale status to QUEUE, result="+result);
		}
		return offlineSalesInQueueList;
	}

	@Override
	public VcTxnLog getOriSaleVcTxnForVoid(String rrn, String jobNumber, MsgType msgType) {
		Session session = this.getSession();
		VcTxnLog vcTxnLog = null;
		Criteria criteria = session.createCriteria(VcTxnLog.class)
				.add(Restrictions.eq("rrn", rrn))
				.add(Restrictions.eq("jobNumber", jobNumber))
				.add(Restrictions.eq("msgType", msgType));
		@SuppressWarnings("unchecked")
		List<VcTxnLog> list = criteria.list();
		if (!list.isEmpty()) {
			vcTxnLog = list.get(0);
		}
		return vcTxnLog;
	}

	@Override
	public VcTxnLog getOriVoidVcTxnForReversal(String rrn, String jobNumber, MsgType msgType) {
		Session session = this.getSession();
		VcTxnLog vcTxnLog = null;
		Criteria criteria = session.createCriteria(VcTxnLog.class)
				.add(Restrictions.eq("rrn", rrn))
				.add(Restrictions.eq("jobNumber", jobNumber))
				.add(Restrictions.eq("msgType", msgType));
		@SuppressWarnings("unchecked")
		List<VcTxnLog> list = criteria.list();
		if (!list.isEmpty()) {
			vcTxnLog = list.get(0);
		}
		return vcTxnLog;
	}

	@Override
	public List<VcTxnLog> getReversalVcTxnLogs(String maskedCan, String jobNumber, String stan) {
		Session session = this.getSession();
		Criteria criteria = session.createCriteria(VcTxnLog.class)
				.add(Restrictions.eq("maskedCan", maskedCan))
				.add(Restrictions.eq("jobNumber", jobNumber))
				.add(Restrictions.eq("msgType", MsgType.REVERSAL))
				.add(Restrictions.eq("stan", stan));
		@SuppressWarnings("unchecked")
		List<VcTxnLog> list = criteria.list();
		return list;
	}

}
