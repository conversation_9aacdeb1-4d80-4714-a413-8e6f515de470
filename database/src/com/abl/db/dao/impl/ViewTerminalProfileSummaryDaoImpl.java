package com.abl.db.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.abl.db.dao.ViewTerminalProfileSummaryDao;
import com.abl.db.model.ViewTerminalProfileSummary;

@Repository
public class ViewTerminalProfileSummaryDaoImpl extends DaoImpl implements ViewTerminalProfileSummaryDao {
	
	public List<ViewTerminalProfileSummary> getTerminalProfileSummaryByGroup(long groupId, long releaseId){
		return getHibernateTemplate().find("from ViewTerminalProfileSummary terminalProfileSummary where terminalProfileSummary.groupId=? and terminalProfileSummary.releaseId=?", groupId, releaseId);
	}

}
