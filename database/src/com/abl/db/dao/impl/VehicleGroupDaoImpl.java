package com.abl.db.dao.impl;

import com.abl.db.dao.GroupDao;
import com.abl.db.dao.VehicleDao;
import com.abl.db.dao.VehicleGroupDao;
import com.abl.db.model.AdminUser;
import com.abl.db.model.Group;
import com.abl.db.model.Vehicle;
import com.abl.db.model.VehicleGroup;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.MatchMode;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Repository;

import java.io.BufferedReader;
import java.io.DataInputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Date;

@Repository
public class VehicleGroupDaoImpl extends DaoImpl implements VehicleGroupDao {
	private static final Logger logger = Logger.getLogger(VehicleGroupDaoImpl.class);

	@Autowired
	private VehicleDao vehicleDao;
	@Autowired
	private GroupDao groupDao;

	@SuppressWarnings("unchecked")
	@Override
	public VehicleGroup getVehicleGroup(String vehicleId) {
		List<VehicleGroup> vehicleGroups = getHibernateTemplate().find(
				"from VehicleGroup vehicleGroup join fetch vehicleGroup.group grouping "
						+ " join fetch vehicleGroup.vehicle vehicles " + "where upper(vehicles.vehicleId)=?",
				vehicleId.toUpperCase());
		if ((vehicleGroups != null) && (vehicleGroups.size() > 0)) {
			return vehicleGroups.get(0);
		} else {
			return null;
		}
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<VehicleGroup> getVehicleGroupByList(List<Vehicle> vehList) {

		logger.debug("getting vg..");
		String query = "from VehicleGroup vg join fetch vg.group grouping "
				+ " join fetch vg.vehicle vehicles " + "where vehicles in ";

		int maxBatch = 100;

		int batch = (int) Math.ceil(((double) vehList.size()) / maxBatch);

		Object[] params = new Object[batch];
		String[] paramName = new String[batch];
		List<Vehicle> strList = new ArrayList<Vehicle>();
		logger.debug("vehList.size()" + vehList.size());
		logger.debug("batch.size()" + batch);
		for (int i = 0; i < batch; i++) {
			query += " (:vehicleList" + i + ")";
			paramName[i] = "vehicleList" + i;
			params[i] = vehList.subList(i * maxBatch, Math.min(maxBatch * (i + 1), vehList.size()));

			if (i < batch - 1) {
				query += " or vehicles in ";
			}

		}

		logger.debug("total param name =" + paramName.length);
		logger.debug("total param values =" + params.length);
		int count = 0;
		if (params != null) {

			for (Object o : params) {
				count++;
				logger.debug("#vehicleId:" + o.toString());
			}
		}
		query += " order by vehicles.id asc";

		// List<VehicleGroup> vehicleGroups =
		// getHibernateTemplate().findByNamedParam(query1, "vehicleList", vehList);
		List<VehicleGroup> vehicleGroups = getHibernateTemplate().findByNamedParam(query, paramName,
				params);
		logger.debug("vehicleGroups.size() =" + vehicleGroups.size());
		if (vehicleGroups != null) {
			for (VehicleGroup vg : vehicleGroups) {
				logger.debug("" + vg.getVehicle().getVehicleId());
			}
		}
		return vehicleGroups;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Vehicle> getExistingVehicles() {
		return null;
	}

	@Override
	public VehicleGroup getVehicleGroupByVehicleId(String vehicleId) {
		List<VehicleGroup> vehicleGroups = getHibernateTemplate().find(
				"from VehicleGroup vehicleGroup where upper(vehicleGroup.vehicle.vehicleId)=?",
				vehicleId.toUpperCase());
		if ((vehicleGroups != null) && (vehicleGroups.size() > 0)) {
			return vehicleGroups.get(0);
		} else {
			return null;
		}
	}

	public List<VehicleGroup> getVehicleGroupByGroupId(long id) {
		List<VehicleGroup> vehicleGroups = getHibernateTemplate().find(
				"from VehicleGroup vehicleGroup where vehicleGroup.group.id=?", id);
		if ((vehicleGroups != null) && (vehicleGroups.size() > 0)) {
			return vehicleGroups;

		} else {
			return null;
		}
	}

	public void saveAll(List<VehicleGroup> vehicleGroup) {
		getHibernateTemplate().saveOrUpdateAll(vehicleGroup);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<VehicleGroup> uploadVehicle(InputStream inputStream, String groupName,
			AdminUser adminUser) throws Exception {
		List list = new ArrayList();
		Vehicle vehicle = null;
		List<String> errorList = new ArrayList<String>();
		List<VehicleGroup> duplicateVehicleGroup = new ArrayList<VehicleGroup>();
		List<Vehicle> vehicleList = new ArrayList<Vehicle>();
		List<Vehicle> defaultVehicleGroupList = new ArrayList<Vehicle>();
		DataInputStream dis = new DataInputStream(inputStream);
		BufferedReader reader = new BufferedReader(new InputStreamReader(dis));
		String line;
		// create group
		Group grouping = new Group();
		grouping.setCreateDateTime(new Date());
		grouping.setCreatedBy(adminUser.getLogin());
		grouping.setName(groupName);

		while ((line = reader.readLine()) != null) {
			VehicleGroup vehicleGroup = new VehicleGroup();
			String vehicleId = line.trim();

			if (vehicleId.length() == 0) {
				continue;
			} else {
				vehicle = vehicleDao.getVehicle(vehicleId);
				if (vehicle == null) {
					errorList.add("Unable to find vehicle id: " + vehicleId);

				} else {
					VehicleGroup existedGroup = getVehicleGroup(vehicle.getVehicleId());
					if (existedGroup != null) {
						if (existedGroup.getGroup().getId() == 1) { // default group id=1
							defaultVehicleGroupList.add(vehicle);
						} else {
							duplicateVehicleGroup.add(existedGroup);
						}
					} else {
						vehicleList.add(vehicle);
					}
				}

			}

		}
		list.add(grouping);
		list.add(vehicleList);
		list.add(errorList);
		list.add(duplicateVehicleGroup);
		list.add(defaultVehicleGroupList);
		return list;
	}

	public List<Vehicle> getVehiclesWithoutGroup(List<String> ivdVersion, List<String> vehicleType,
			List<String> vehicleModel, String vehicleNo) {
		List<VehicleGroup> vg = new ArrayList<VehicleGroup>();
		List<Object> paramList = new ArrayList<Object>();
		List<String> paramNameList = new ArrayList<String>();

		String[] paramName = new String[4];
		Object[] params = new Object[4];

		String query = "";

		if (ivdVersion != null && !ivdVersion.isEmpty()) {
			query += " v.firmwareVersion in (:ivdVersion)  and  ";
			paramList.add(ivdVersion);
			paramNameList.add("ivdVersion");

		}
		if (vehicleType != null && !vehicleType.isEmpty()) {
			query += " v.vehicleType in (:vehicleType)  and ";
			paramList.add(vehicleType);
			paramNameList.add("vehicleType");
		}
		if (vehicleModel != null && !vehicleModel.isEmpty()) {
			query += " v.ivdModelId in (:ivdModelId)  and ";
			paramList.add(vehicleModel);
			paramNameList.add("ivdModelId");
		}
		
		if (vehicleNo != null && !vehicleNo.isEmpty()) {
			query += "upper(v.vehicleId) like :vehicleNo  and  ";
			paramList.add(vehicleNo);
			paramNameList.add("vehicleNo");
		}
		params = paramList.toArray(new Object[paramList.size()]);
		paramName = (String[]) paramNameList.toArray(new String[paramNameList.size()]);

		List<Vehicle> list = getHibernateTemplate().findByNamedParam(
				"from Vehicle v " + "where " + query
						+ " not exists (select 'x' from VehicleGroup vg where vg.vehicle =v)", paramName,
				params);

		return list;
	}

	public List<Vehicle> getVehiclesWithDefaultGroup(List<String> ivdVersion,
			List<String> vehicleType, List<String> vehicleModel, String vehicleNo) {
		List<VehicleGroup> vg = new ArrayList<VehicleGroup>();
		String query = "";
		List<Object> paramList = new ArrayList<Object>();
		List<String> paramNameList = new ArrayList<String>();

		String[] paramName = new String[4];
		Object[] params = new Object[4];

		if (ivdVersion != null && !ivdVersion.isEmpty()) {
			query += " v.firmwareVersion in (:ivdVersion)  and ";
			paramList.add(ivdVersion);
			paramNameList.add("ivdVersion");

		}
		if (vehicleType != null && !vehicleType.isEmpty()) {
			query += " v.vehicleType in (:vehicleType)  and ";
			paramList.add(vehicleType);
			paramNameList.add("vehicleType");
		}
		if (vehicleModel != null && !vehicleModel.isEmpty()) {
			query += " v.ivdModelId in (:ivdModelId)  and ";
			paramList.add(vehicleModel);
			paramNameList.add("ivdModelId");
		}

		if (vehicleNo != null && !vehicleNo.isEmpty()) {
			query += " upper(v.vehicleId) like :vehicleNo  and ";
			paramList.add(vehicleNo);
			paramNameList.add("vehicleNo");
		}
		params = paramList.toArray(new Object[paramList.size()]);
		paramName = (String[]) paramNameList.toArray(new String[paramNameList.size()]);

		List<Vehicle> list = getHibernateTemplate()
				.findByNamedParam(
						"from Vehicle v "
								+ "where "
								+ query
								+ " exists (select 'x' from VehicleGroup vg where vg.vehicle =v and vg.group.id=1)",
						paramName, params);

		return list;
	}

	@SuppressWarnings("unchecked")
	public List<VehicleGroup> getVehiclesWithGroup(List<String> ivdVersion, List<String> vehicleType,
			List<String> vehicleModel, String vehicleNo) {
		List<VehicleGroup> vg = new ArrayList<VehicleGroup>();

		List<Object> paramList = new ArrayList<Object>();
		List<String> paramNameList = new ArrayList<String>();

		String[] paramName = new String[4];
		Object[] params = new Object[4];

		String query = "";

		if (ivdVersion != null && !ivdVersion.isEmpty()) {
			query += " v.firmwareVersion in (:ivdVersion) and";
			paramList.add(ivdVersion);
			paramNameList.add("ivdVersion");
			

		}
		if (vehicleType != null && !vehicleType.isEmpty()) {
			
			query += " v.vehicleType in (:vehicleType) and ";
			paramList.add(vehicleType);
			paramNameList.add("vehicleType");
		}
		if (vehicleModel != null && !vehicleModel.isEmpty()) {
			
			query += " v.ivdModelId in (:ivdModelId) and  ";
			paramList.add(vehicleModel);
			paramNameList.add("ivdModelId");
		}
		if (vehicleNo != null && !vehicleNo.isEmpty()) {

			query += " upper(v.vehicleId) like :vehicleNo  and ";
			paramList.add(vehicleNo);
			paramNameList.add("vehicleNo");
		}

		params = paramList.toArray(new Object[paramList.size()]);
		paramName = (String[]) paramNameList.toArray(new String[paramNameList.size()]);

		List<VehicleGroup> list = getHibernateTemplate().findByNamedParam(
				"from VehicleGroup vg join fetch vg.group grouping " + " join fetch vg.vehicle v "
						+ "where "+ query+" vg.group.id<>1 "  + " order by v.id asc", paramName, params);

		return list;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<VehicleGroup> searchVehicles(String vehicleNo, String orderBy, boolean ascending,
			int offset, int maxResults) {
		Session session = this.getSession();
		Criteria criteria = session.createCriteria(VehicleGroup.class, "vehicleGroup");
		criteria.createAlias("vehicle", "vehicle", criteria.LEFT_JOIN);
		criteria.createAlias("group", "grouping", criteria.LEFT_JOIN);

		if (vehicleNo != null && vehicleNo.length() > 0) {
			criteria.add(Restrictions.ilike("vehicle.vehicleId", vehicleNo, MatchMode.ANYWHERE));

		}

		if (orderBy != null) {
			if (orderBy.indexOf('.') < 0) {
				orderBy = "vehicle.vehicleId";
			}
			if (ascending) {
				criteria.addOrder(Order.asc(orderBy));
			} else {
				criteria.addOrder(Order.desc(orderBy));
			}
		}
		if (offset > 0) {
			criteria.setFirstResult(offset);
		}
		if (maxResults > 0) {
			criteria.setMaxResults(maxResults);
		}

		List<VehicleGroup> list = criteria.list();
		return list;

	}

	@SuppressWarnings("unchecked")
	@Override
	public int countVehicles(String vehicleNo) {
		Session session = this.getSession();
		Criteria criteria = session.createCriteria(VehicleGroup.class, "vehicleGroup").setProjection(
				Projections.rowCount());

		if (vehicleNo != null && vehicleNo.length() > 0) {
			criteria.add(Restrictions.ilike("vehicleGroup.vehicle.vehicleId", vehicleNo,
					MatchMode.ANYWHERE));

		}

		List<Number> list = criteria.list();
		if ((list != null) && (list.size() > 0)) {
			return list.get(0).intValue();
		} else {
			return 0;
		}

	}

	@Override
	public VehicleGroup getVehicleGroupById(long id) {
		return getHibernateTemplate().get(VehicleGroup.class, id);
	}

}
