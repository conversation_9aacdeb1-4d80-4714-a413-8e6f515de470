package com.abl.db.dao.impl;

import com.abl.db.dao.ReleaseFileDao;
import com.abl.db.model.ReleaseFile;
import org.apache.jcs.JCS;
import org.apache.log4j.Logger;
import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class ReleaseFileDaoImpl extends DaoImpl implements
        ReleaseFileDao {

    private static final Logger logger = Logger.getLogger(ReleaseFileDaoImpl.class);
    private static JCS releaseFileCache;
    private static JCS releaseFileNextRecordCache;

    private static JCS getReleaseFileCache() {
        if (releaseFileCache != null) return releaseFileCache;
        try {
            releaseFileCache = JCS.getInstance("releaseFileCache");
        } catch (Exception e) {
            // Handle cache region initialization failure
            logger.warn("releaseFileCache instance cannot be created");
        }
        return releaseFileCache;
    }

    private static JCS getReleaseFileNextRecordCache() {
        if (releaseFileNextRecordCache != null) return releaseFileNextRecordCache;
        try {
            releaseFileNextRecordCache = JCS.getInstance("releaseFileNextRecordCache");
        } catch (Exception e) {
            // Handle cache region initialization failure
            logger.warn("releaseFileNextRecordCache instance cannot be created - " + e.getMessage());
        }
        return releaseFileNextRecordCache;
    }

    @Override
    public ReleaseFile getReleaseFile(long releaseId, int recordNo) {
        String key = releaseId + "|" + recordNo;

        JCS releaseFileCache = getReleaseFileCache();
        if (releaseFileCache != null) {
            Object value = releaseFileCache.get(key);
            if (value != null) {
                ReleaseFile releaseFile = (ReleaseFile) value;
                return releaseFile;
            }
        }

        Session session = this.getSession();
        Criteria criteria = session.createCriteria(ReleaseFile.class, "releaseFile");

        if (releaseId > 0) {
            criteria.add(Restrictions.eq("releaseFile.release.id", releaseId));
        }

        if (recordNo > 0) {
            criteria.add(Restrictions.eq("releaseFile.recordNo", recordNo));
        }

        List<ReleaseFile> list = criteria.list();
        if (list == null || list.size() <= 0) return null;

        ReleaseFile releaseFile = list.get(0);

        try {
            if (releaseFileCache != null) {
                releaseFileCache.put(key, releaseFile);
                logger.debug("cached releaseFile key=" + key);
            }
        } catch (Exception e) {
            logger.warn("cannot put releaseFile into cache - " + e.getMessage());
        }

        return releaseFile;
    }

    @Override
    public int getNextRecordNo(long releaseId, int recordNo) {
        String key = releaseId + "|" + recordNo;

        JCS releaseFileNextRecordCache = getReleaseFileNextRecordCache();
        if (releaseFileNextRecordCache != null) {
            Object value = releaseFileNextRecordCache.get(key);
            if (value != null) {
                Integer nextRecordNo = (Integer) value;
                return nextRecordNo;
            }
        }

        Session session = this.getSession();
        Criteria criteria = session.createCriteria(ReleaseFile.class, "releaseFile");

        criteria.add(Restrictions.eq("releaseFile.release.id", releaseId));
        criteria.add(Restrictions.gt("releaseFile.recordNo", recordNo));

        criteria.addOrder(Order.asc("releaseFile.recordNo"));

        criteria.setMaxResults(1);

        List<ReleaseFile> list = criteria.list();
        if (list == null || list.size() <= 0) return 0;

        int nextRecordNo = list.get(0).getRecordNo();

        try {
            if (releaseFileNextRecordCache != null) {
                releaseFileNextRecordCache.put(key, new Integer(nextRecordNo));
                logger.debug("cached releaseFileNextRecord key=" + key);
            }
        } catch (Exception e) {
            logger.warn("cannot put nextRecordNo into cache - " + e.getMessage());
        }

        return nextRecordNo;
    }

    @Override
    public void saveAll(List<ReleaseFile> object) {
        getHibernateTemplate().saveOrUpdateAll(object);
    }

}
