package com.abl.db.dao.impl;

import java.util.List;

import org.hibernate.Session;
import org.springframework.stereotype.Repository;

import com.abl.db.dao.ViewApplicationFeeSummaryDao;
import com.abl.db.model.ViewApplicationFeeSummary;

@Repository
public class ViewApplicationFeeSummaryDaoImpl extends DaoImpl implements ViewApplicationFeeSummaryDao {
	
	@SuppressWarnings("unchecked")
	public List<ViewApplicationFeeSummary> getApplicationFeeSummaryByApp(long appId) {
	return getHibernateTemplate().find("from ViewApplicationFeeSummary appFeeSummary where appId=?", appId);
	}
	
	@SuppressWarnings("unchecked")
	public ViewApplicationFeeSummary getLastApplicationFeeSummary() {
		
//		List<ApplicationBinSummary>  list = getHibernateTemplate().find("from ApplicationBinSummary appBinSummary order by id desc ");
		Session session = this.getSession();
		List<ViewApplicationFeeSummary>  list = session.createQuery("from ViewApplicationFeeSummary order by id desc").setMaxResults(1).list();
		
		if (list!=null && list.size()>0) {
			return list.get(0);
		} else {
			return null;
		}
		
	}

}
