package com.abl.db.dao.impl;

import java.util.List;

import org.hibernate.Session;
import org.springframework.stereotype.Repository;

import com.abl.db.dao.ViewApplicationBinSummaryDao;
import com.abl.db.model.ViewApplicationBinSummary;

@Repository
public class ViewApplicationBinSummaryDaoImpl extends DaoImpl implements ViewApplicationBinSummaryDao {
	
	@SuppressWarnings("unchecked")
	public List<ViewApplicationBinSummary> getApplicationBinSummaryByApp(long appId) {
	return getHibernateTemplate().find("from ViewApplicationBinSummary appBinSummary where appId=?", appId);
	}
	
	@SuppressWarnings("unchecked")
	public ViewApplicationBinSummary getLastApplicationBinSummary() {
		
//		List<ApplicationBinSummary>  list = getHibernateTemplate().find("from ApplicationBinSummary appBinSummary order by id desc ");
		Session session = this.getSession();
		List<ViewApplicationBinSummary>  list = session.createQuery("from ViewApplicationBinSummary order by id desc").setMaxResults(1).list();
		
		if (list!=null && list.size()>0) {
			return list.get(0);
		} else {
			return null;
		}
		
	}

}
