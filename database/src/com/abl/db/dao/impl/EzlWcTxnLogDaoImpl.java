package com.abl.db.dao.impl;

import com.abl.db.dao.EzlWcTxnLogDao;
import com.abl.db.model.EzlWcTxnLog;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class EzlWcTxnLogDaoImpl extends DaoImpl implements EzlWcTxnLogDao {

    @Override
    public EzlWcTxnLog getEzlWcTxnLog(long id) {
        return getHibernateTemplate().get(EzlWcTxnLog.class, id);
    }

    @SuppressWarnings("unchecked")
	@Override
    public EzlWcTxnLog getEzlWcTxnLogByEzlTxnLogId(long id) {
    	List<EzlWcTxnLog> list = getHibernateTemplate().find("from EzlWcTxnLog where ezlTxnLog.id=? order by id", id);
    	if ((list!=null)&&(!list.isEmpty())) {
    		return list.get(0);
    	} else {
    		return null;
    	}
    }

    @Override
    public void save(EzlWcTxnLog txnLog) {
        getHibernateTemplate().save(txnLog);
    }
    
    @Override
    public void update(EzlWcTxnLog txnLog) {
        getHibernateTemplate().update(txnLog);
    }
}
