package com.abl.db.dao.impl;

import com.abl.db.dao.ApplicationDao;
import com.abl.db.model.Application;
import com.abl.db.model.ParameterDefinition;
import com.abl.utils.StringUtils;
import org.apache.log4j.Logger;
import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository("ApplicationDao")
public class ApplicationDaoImpl extends DaoImpl implements ApplicationDao {
    private static final Logger logger = Logger
            .getLogger(ApplicationDaoImpl.class);

    @Override
    public Application getApplication(long id) {
        return getHibernateTemplate().get(Application.class, id);
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<Application> getApplications() {
        return getHibernateTemplate().find(
                "from Application application order by application.name asc");
    }


    @SuppressWarnings("unchecked")
    @Override
    public List<Application> searchApplicationByNameVersion(String applicationName, int version) {
        Session session = this.getSession();
        Criteria criteria = session.createCriteria(Application.class,
                "application");

        if ((applicationName != null) && (applicationName.length() > 0)) {
            criteria.add(Restrictions.eq("application.name", applicationName).ignoreCase());
        }
        if (version >= 0) {
            criteria.add(Restrictions.eq("application.version", version));
        }
        criteria.addOrder(Order.desc("application.version"));

        List<Application> list = criteria.list();

        return list;

    }

    @Override
    public void save(Application application) {
        getHibernateTemplate().saveOrUpdate(application);
    }

    @Override
    public void saveAll(List<Application> object) {
        getHibernateTemplate().saveOrUpdateAll(object);
    }

    @SuppressWarnings("unused")
    private String formatParameterValue(short paramType, String additionalInfo, String value) {
        String formattedValue = null;
        String[] infos = null;
        int decimal = 0;
        switch (paramType) {
            case ParameterDefinition.NUMBER:
                infos = additionalInfo.substring(additionalInfo.indexOf("(") + 1, additionalInfo.indexOf(")")).split(",");
                decimal = Integer.parseInt(infos[3]);
                if (decimal > 0) {
                    int numberOfDecimal = getNumberOfDecimals(value);
                    if (numberOfDecimal == 0) {
                        int length = value.length();
                        int padIndex = length - decimal;
                        logger.debug("length =" + length);
                        logger.debug("padIndex =" + padIndex);

                        if (padIndex > 0) {
                            logger.debug("value 1=" + value.substring(0, padIndex));
                            logger.debug("value 2=" + value.substring(padIndex));
                            value = value.substring(0, padIndex).concat(".").concat(value.substring(padIndex));
                        } else {
                            value = StringUtils.padRight(value, '.', 1);


                            value = StringUtils.padRight(value, '0', decimal - numberOfDecimal);

                        }

                        logger.debug("value after put decimal:" + value);
                        formattedValue = value;

                    }

//    				formattedValue= StringUtils.padRight(value, '0', decimal-numberOfDecimal);

                } else {
                    formattedValue = value;
                }

                break;
            case ParameterDefinition.OPTION:
                formattedValue = value;
                break;
            case ParameterDefinition.STRING:
                formattedValue = value;
                break;
            case ParameterDefinition.DATE:
                formattedValue = value;
                break;
            case ParameterDefinition.TIME:
                formattedValue = value;
                break;
        }

        return formattedValue;

    }

    private int getNumberOfDecimals(String number) {
        boolean isDecimal = Double.parseDouble(number) % 1 == 0;

        if (!isDecimal) {
            int stringLength = number.length();
            int numberOfDecimals = 0;
            char theChar = 'e';
            int counter;

            for (counter = 1; theChar != '.'; counter++) {
                theChar = number.charAt(counter);
            }

            numberOfDecimals = stringLength - counter;

            return numberOfDecimals;
        } else {
            return 0;
        }

    }
}
