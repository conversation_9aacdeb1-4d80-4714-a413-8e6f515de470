package com.abl.db.dao.impl;

import com.abl.db.dao.TerminalApplicationDao;
import com.abl.db.model.Release;
import com.abl.db.model.TerminalApplication;
import com.abl.db.model.TmsTerminal;
import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class TerminalApplicationDaoImpl extends DaoImpl implements TerminalApplicationDao {

    @Override
    public Release getLatestRelease(TmsTerminal terminal) {
        Session session = this.getSession();
        Criteria criteria = session.createCriteria(TerminalApplication.class, "terminalApplication");

        criteria.add(Restrictions.eq("terminalApplication.pk.tmsTerminal", terminal));

        criteria.addOrder(Order.desc("terminalApplication.updateDateTime"));

        List<TerminalApplication> list = criteria.list();
        if (list == null || list.size() <= 0) return null;

        return list.get(0).getPk().getRelease();
    }

    @Override
    public void save(TerminalApplication terminalApplication) {
        getHibernateTemplate().saveOrUpdate(terminalApplication);
    }
}
