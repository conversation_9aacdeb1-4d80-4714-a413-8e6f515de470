package com.abl.db.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.abl.db.dao.ViewTerminalProfileSummary2Dao;
import com.abl.db.model.ViewTerminalProfileSummary2;

@Repository
public class ViewTerminalProfileSummary2DaoImpl extends DaoImpl implements ViewTerminalProfileSummary2Dao {
	
	public List<ViewTerminalProfileSummary2> getTerminalProfileSummaryByGroup(long groupId, long releaseId){
		return getHibernateTemplate().find("from ViewTerminalProfileSummary2 terminalProfileSummary where terminalProfileSummary.groupId=? and terminalProfileSummary.releaseId=? order by terminalProfileSummary.appName, terminalProfileSummary.profileCreateDt, terminalProfileSummary.profileEffectiveDate asc ", groupId, releaseId);
	}

}
