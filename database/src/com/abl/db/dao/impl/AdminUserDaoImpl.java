package com.abl.db.dao.impl;

import java.util.List;

import org.hibernate.Criteria;
import org.hibernate.LockMode;
import org.hibernate.Session;
import org.hibernate.criterion.MatchMode;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Repository;

import com.abl.db.dao.AdminUserDao;
import com.abl.db.model.AdminUser;
import com.abl.db.model.Domain;

@Repository
public class AdminUserDaoImpl extends DaoImpl implements AdminUserDao {

	@Override
	public AdminUser getAdminUser(long id) {
		return getHibernateTemplate().get(AdminUser.class, id);
	}
	
	@Override
	@SuppressWarnings("unchecked")
	public AdminUser getAdminUserFetchDomain(long id) {
		List<AdminUser> list = getHibernateTemplate().find("from AdminUser adminUser " +
				"left join fetch adminUser.domain where adminUser.id=?", id);
		if ((list != null)&&(list.size()>0)) {
			return list.get(0);
		} else {
			return null;
		}
	}
	
	@Override
	public AdminUser getAdminUserForUpdate(long id) {
		return getHibernateTemplate().get(AdminUser.class, id, LockMode.PESSIMISTIC_WRITE);
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public AdminUser getAdminUser(String login) {
		List <AdminUser>list = getHibernateTemplate().find("from AdminUser adminUser " +
				"where adminUser.login=?", login);
		if ((list != null)&&(list.size()>0)) {
			return list.get(0);
		} else {
			return null;
		}
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public AdminUser getAdminUserForUpdate(String login) {
		List <AdminUser>list = getHibernateTemplate().find("from AdminUser adminUser " +
				"where adminUser.login=?", login);
		if ((list != null)&&(list.size()>0)) {
			AdminUser adminUser = list.get(0);
			getHibernateTemplate().refresh(adminUser, LockMode.PESSIMISTIC_WRITE);
			return adminUser;
		} else {
			return null;
		}
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public AdminUser getAdminUserFetchAccessProfiles(long id) {
		List<AdminUser> list = getHibernateTemplate().find("from AdminUser adminUser " +
				"left join fetch adminUser.accessProfiles accessProfiles " +
				"left join fetch accessProfiles.pages " +
				"where adminUser.id=? and adminUser.deleted=?", id, false);
		if ((list != null)&&(list.size()>0)) {
			return list.get(0);
		} else {
			return null;
		}
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public AdminUser getAdminUserFetchAccessProfiles(String login) {
		List<AdminUser> list = getHibernateTemplate().find("from AdminUser adminUser " +
				"left join fetch adminUser.accessProfiles accessProfiles " +
				"left join fetch accessProfiles.pages " +
				"where adminUser.login=? and adminUser.deleted=?", login, false);
		if ((list != null)&&(list.size()>0)) {
			return list.get(0);
		} else {
			return null;
		}
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public int countAdminUsers(String login, String name, Integer status, Domain domain) {
		Session session = this.getSession();
		Criteria criteria = session.createCriteria(AdminUser.class, "adminUser")
							.setProjection(Projections.rowCount());
		
		if ((login!=null)&&(login.length()>0)) {
			criteria.add(Restrictions.ilike("adminUser.login", login ,MatchMode.ANYWHERE));
		}
		if ((name!=null)&&(name.length()>0)) {
			criteria.add(Restrictions.ilike("adminUser.name", name ,MatchMode.ANYWHERE));
		}
		if (status != null) {
			criteria.add(Restrictions.eq("adminUser.status", status));
		}
		if (domain != null) {
			criteria.add(Restrictions.eq("adminUser.domain", domain));
		}
		criteria.add(Restrictions.eq("adminUser.deleted", false));
		List<Number> list = criteria.list();
		if ((list != null) && (list.size() > 0)) {
			return list.get(0).intValue();
		} else {
			return 0;
		}
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public List<AdminUser> searchAdminUsers(String login, String name, Integer status, Domain domain,
			String orderBy, boolean ascending, int offset, int maxResults) {
		
		Session session = this.getSession();
		Criteria criteria = session.createCriteria(AdminUser.class, "adminUser")
								.createCriteria("domain", "domain", Criteria.LEFT_JOIN);
		if ((login != null)&&(login.length() > 0)) {
			criteria.add(Restrictions.ilike("adminUser.login", login ,MatchMode.ANYWHERE));
		}
		if ((name != null)&&(name.length() > 0)) {
			criteria.add(Restrictions.ilike("adminUser.name", name ,MatchMode.ANYWHERE));
		}
		if (status != null) {
			criteria.add(Restrictions.eq("adminUser.status", status));
		}
		if (domain != null) {
			criteria.add(Restrictions.eq("adminUser.domain", domain));
		}
		criteria.add(Restrictions.eq("adminUser.deleted", false));
		if (orderBy != null) {
			if (orderBy.indexOf('.')<0) {
				orderBy = "adminUser." + orderBy;
			}
			if (ascending) {
				criteria.addOrder(Order.asc(orderBy));
			} else {
				criteria.addOrder(Order.desc(orderBy));
			}
		}
		
		if (offset > 0) {
			criteria.setFirstResult(offset);
		}
		if (maxResults > 0) {
			criteria.setMaxResults(maxResults);
		}
		
		List<AdminUser> list = criteria.list();
		return list;
	}

}
