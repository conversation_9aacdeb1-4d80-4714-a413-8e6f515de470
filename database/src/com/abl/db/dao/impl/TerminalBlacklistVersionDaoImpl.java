package com.abl.db.dao.impl;

import com.abl.db.dao.TerminalBlacklistVersionDao;
import com.abl.db.model.TerminalBlacklistVersion;
import org.springframework.stereotype.Repository;

@Repository
public class TerminalBlacklistVersionDaoImpl extends DaoImpl implements TerminalBlacklistVersionDao {

    @Override
    public void save(TerminalBlacklistVersion terminalBlacklistVersion) {
        getHibernateTemplate().saveOrUpdate(terminalBlacklistVersion);
    }
}
