package com.abl.db.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.abl.db.dao.ViewReleasePackageSummaryDao;
import com.abl.db.model.Release;
import com.abl.db.model.ViewReleasePackageSummary;

import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Repository;

@Repository
public class ViewReleasePackageSummaryDaoImpl extends DaoImpl implements ViewReleasePackageSummaryDao {
	 @Override
	   public int countReleases(long releaseId) {
		   Session session = this.getSession();
	        Criteria criteria = session.createCriteria(ViewReleasePackageSummary.class, "viewReleasePackageSummary")
	                .setProjection(Projections.rowCount());
	        if (releaseId>0) {
	            criteria.add(Restrictions.eq("viewReleasePackageSummary.id", releaseId));

	        }
	      
	        List<Number> list = criteria.list();
	        if ((list != null) && (list.size() > 0)) {
	            return list.get(0).intValue();
	        } else {
	            return 0;
	        }
		   
	   }

	   @Override
	   public List<ViewReleasePackageSummary> searchReleases(long releaseId, String orderBy,
	                                             boolean ascending, int offset, int maxResults) {
		   Session session = this.getSession();
	       Criteria criteria = session.createCriteria(ViewReleasePackageSummary.class, "viewReleasePackageSummary");

	        if (releaseId>0) {
	            criteria.add(Restrictions.eq("viewReleasePackageSummary.id", releaseId));

	        }
	       
	        if (orderBy != null) {
	            if (orderBy.indexOf('.') < 0) {
	                orderBy = "viewReleasePackageSummary.loadDateTime";
	            }
	            if (ascending) {
	                criteria.addOrder(Order.asc(orderBy));
	            } else {
	                criteria.addOrder(Order.desc(orderBy));
	            }
	        }
	        if (offset > 0) {
	            criteria.setFirstResult(offset);
	        }
	        if (maxResults > 0) {
	            criteria.setMaxResults(maxResults);
	        }

	        List<ViewReleasePackageSummary> list = criteria.list();
	        return list;
	   }
}
