package com.abl.db.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.abl.db.dao.ViewTerminalApplicationSummaryDao;
import com.abl.db.model.ViewTerminalApplicationSummary;

@Repository
public class ViewTerminalApplicationSummaryDaoImpl extends DaoImpl implements ViewTerminalApplicationSummaryDao {
	
	@Override
	public List<ViewTerminalApplicationSummary> getTerminalApplicationSummary(long terminalId) {
		
		return getHibernateTemplate().find("from ViewTerminalApplicationSummary viewTerminalApplicationSummary where viewTerminalApplicationSummary.terminalId =? order by viewTerminalApplicationSummary.updateDateTime desc",terminalId);
		
	}

}
