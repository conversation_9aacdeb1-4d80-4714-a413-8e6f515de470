package com.abl.db.dao.impl;

import java.util.List;
import org.springframework.stereotype.Repository;
import com.abl.db.model.ApplicationFeeValue;
import com.abl.db.dao.ApplicationFeeValueDao;

@Repository
public class ApplicationFeeValueDaoImpl extends DaoImpl implements ApplicationFeeValueDao{
	@Override
	public void saveAll(List<ApplicationFeeValue> object) {
		getHibernateTemplate().saveOrUpdateAll(object);
	}


}
