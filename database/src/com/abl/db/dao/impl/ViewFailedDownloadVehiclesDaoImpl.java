package com.abl.db.dao.impl;

import java.util.List;

import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Projections;
import org.springframework.stereotype.Repository;

import com.abl.db.dao.ViewFailedDownloadVehiclesDao;
import com.abl.db.model.ViewFailedDownloadVehicles;

@Repository
public class ViewFailedDownloadVehiclesDaoImpl extends DaoImpl implements ViewFailedDownloadVehiclesDao {
	
	public List<ViewFailedDownloadVehicles> searchSummary(String orderBy, boolean ascending, int offset, int maxResults){
		  	Session session = this.getSession();
	        Criteria criteria = session.createCriteria(ViewFailedDownloadVehicles.class, "viewFailedDownloadVehicles");
	      
	        if (orderBy != null) {
	            if (orderBy.indexOf('.') < 0) {
	                orderBy = "viewFailedDownloadVehicles.vehicleId";
	            }
	            if (ascending) {
	                criteria.addOrder(Order.asc(orderBy));
	            } else {
	                criteria.addOrder(Order.desc(orderBy));
	            }
	        }
	        if (offset > 0) {
	            criteria.setFirstResult(offset);
	        }
	        if (maxResults > 0) {
	            criteria.setMaxResults(maxResults);
	        }

	        List<ViewFailedDownloadVehicles> list = criteria.list();
	        return list;
		
	}
	
	public int countSummary(){
		
		Session session = this.getSession();
        Criteria criteria = session.createCriteria(ViewFailedDownloadVehicles.class, "viewFailedDownloadVehicles").setProjection(Projections.rowCount());;
        
        List<Number> list = criteria.list();
        if ((list != null) && (list.size() > 0)) {
            return list.get(0).intValue();
        } else {
            return 0;
        }
        
		
	}

}
