package com.abl.db.dao.impl;

import com.abl.db.bean.ProfileFeeValueBean;
import com.abl.db.dao.ProfileFeeValueDao;
import com.abl.db.model.ProfileFeeValue;
import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

@Repository
public class ProfileFeeValueDaoImpl extends DaoImpl implements ProfileFeeValueDao {
    @Override
    public void saveAll(List<ProfileFeeValue> object) {
        getHibernateTemplate().saveOrUpdateAll(object);
    }

    @Override
    public List<ProfileFeeValue> getProfileFeeValuesByFee(long profileFeeId) {
        Session session = this.getSession();
        Criteria criteria = session.createCriteria(ProfileFeeValue.class, "profileFeeValue");

        if (profileFeeId > 0) {
            criteria.add(Restrictions.eq("profileFeeValue.pk.profileFee.id", profileFeeId));
        }
        criteria.addOrder(Order.asc("profileFeeValue.pk.profileFee.id"));
        criteria.addOrder(Order.asc("profileFeeValue.pk.parameterDefinition.id"));

        List<ProfileFeeValue> list = criteria.list();
        return list;

    }

    @Override
    public List<ProfileFeeValueBean> getProfileFeeValueSummary(long terminalProfileId){
        List<ProfileFeeValueBean> profileFeeValueBeanList = new ArrayList<ProfileFeeValueBean>();
        Session session = this.getSession();
        List<Object[]> records =session.createSQLQuery("select (case when pf.id is null then 0 else pf.id end) as fee_id, pfv.fee_value as fee_value, " +
                "pd.name as fee_param_name "+
                "FROM tmtb_parameter_definition pd left join tmtb_profile_fee_value pfv on pd.id=pfv.parameter_definition_id " +
                "left join tmtb_profile_fee pf on pfv.profile_fee_id  = pf.id " +
                "where pd.param_level=2" +
                "and pf.terminal_profile_id="+ terminalProfileId +" order by pf.id, pd.id asc").list();


        if ((records != null) && (records.size() > 0)) {
            for(int i=0; i<records.size(); i++){
                ProfileFeeValueBean feeValueBean = new ProfileFeeValueBean();
                Object[] feeSummary = records.get(i);
                feeValueBean.setFeeId(new Long(feeSummary[0].toString()));
                feeValueBean.setFeeValue(feeSummary[1].toString());
                feeValueBean.setFeeParamName(feeSummary[2].toString());
                profileFeeValueBeanList.add(feeValueBean);
            }
            return profileFeeValueBeanList;
        } else {
            return null;
        }
    }
}
