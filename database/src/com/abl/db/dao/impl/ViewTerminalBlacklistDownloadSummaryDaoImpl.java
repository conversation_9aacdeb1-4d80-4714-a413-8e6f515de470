package com.abl.db.dao.impl;

import java.util.List;

import org.hibernate.Criteria;
import org.hibernate.Session;

import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Repository;
import com.abl.db.dao.ViewTerminalBlacklistDownloadSummaryDao;
import com.abl.db.model.ViewTerminalBlacklistDownloadSummary;


@Repository
public class ViewTerminalBlacklistDownloadSummaryDaoImpl extends DaoImpl implements ViewTerminalBlacklistDownloadSummaryDao {
	
	@Override
	public ViewTerminalBlacklistDownloadSummary getTerminalBlacklistDownloadSummaryByJobId(long jobId){
		Session session = this.getSession();
        Criteria criteria = session.createCriteria(ViewTerminalBlacklistDownloadSummary.class,
                "termBlacklistDownloadSummary");
        
        if(jobId>0){
        	criteria.add(Restrictions.eq("termBlacklistDownloadSummary.jobId", jobId));
        }

        
        List<ViewTerminalBlacklistDownloadSummary> list = criteria.list();       
      
        if(list!=null && list.size()>0){
        	return list.get(0);
        } else {
        	return null;
        }

	}
	
	public List<ViewTerminalBlacklistDownloadSummary> getAllTerminalBlacklistDownloadSummary(){
		List<ViewTerminalBlacklistDownloadSummary> list =getHibernateTemplate().find(" from ViewTerminalBlacklistDownloadSummary");
    	if ((list != null)&&(list.size()>0)) {
			return list;
		} else {
			return null;
		}
	}
	

}
