package com.abl.db.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.abl.db.dao.ViewTerminalsBlacklistNotDownloadedDao;
import com.abl.db.dao.ViewTerminalsNotDownloadedDao;
import com.abl.db.model.ViewTerminalsBlacklistNotDownloaded;
import com.abl.db.model.ViewTerminalsNotDownloaded;

@Repository
public class ViewTerminalsBlacklistNotDownloadedDaoImpl extends DaoImpl implements ViewTerminalsBlacklistNotDownloadedDao {
	
	@SuppressWarnings("unchecked")
	@Override
	public List<ViewTerminalsBlacklistNotDownloaded> getTerminalBlacklistWithoutDownloadJob(long jobId){

		List<ViewTerminalsBlacklistNotDownloaded> list =getHibernateTemplate().find("from ViewTerminalsBlacklistNotDownloaded t " +
				" where t.jobId =?)", jobId);
		return list;
		
	}
	
	@Override
	public List<ViewTerminalsBlacklistNotDownloaded> getAllTerminalBlacklistWithoutDownloadJob(){
		List<ViewTerminalsBlacklistNotDownloaded> list =getHibernateTemplate().find("from ViewTerminalsBlacklistNotDownloaded t ");
		return list;
	}

}
