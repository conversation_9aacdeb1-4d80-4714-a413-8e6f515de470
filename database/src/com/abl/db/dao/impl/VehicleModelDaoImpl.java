package com.abl.db.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.abl.db.dao.VehicleModelDao;
import com.abl.db.model.VehicleModel;

@Repository
public class VehicleModelDaoImpl extends DaoImpl implements VehicleModelDao {

	public List<VehicleModel> getVehicleModels() {
		return getHibernateTemplate().find("from VehicleModel vehicleModel order by ivdModelId asc");
	}

}
