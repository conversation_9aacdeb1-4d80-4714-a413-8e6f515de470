package com.abl.db.dao.impl;

import java.util.List;

import org.hibernate.Criteria;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.criterion.MatchMode;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Repository;

import com.abl.db.dao.ViewTerminalSummaryDao;
import com.abl.db.model.ViewTerminalSummary;

@Repository
public class ViewTerminalSummaryDaoImpl extends DaoImpl implements ViewTerminalSummaryDao {
	
	@SuppressWarnings("unchecked")
    //@Override
    public List<ViewTerminalSummary> searchTerminals1(String serialNo, String vehicleNo, long modelId,
                                             String orderBy, boolean ascending, int offset, int maxResults) {
        Session session = this.getSession();
        Criteria criteria = session.createCriteria(ViewTerminalSummary.class, "tmsTerminal");

        if (serialNo != null && serialNo.length() > 0) {
            criteria.add(Restrictions.ilike("tmsTerminal.serialNo", serialNo ,MatchMode.ANYWHERE));
        }

        if (vehicleNo != null && vehicleNo.length() > 0) {
            criteria.add(Restrictions.ilike("tmsTerminal.vehicleNo", vehicleNo ,MatchMode.ANYWHERE));
        }
        
        if (modelId>0) {
            criteria.add(Restrictions.eq("tmsTerminal.modelId", modelId));
        }
 
        criteria.add(Restrictions.eq("tmsTerminal.vehicleUnpaired", false));
        if (orderBy != null) {
            if (orderBy.indexOf('.') < 0) {
                orderBy = "tmsTerminal.id";
            }
            if (ascending) {
                criteria.addOrder(Order.asc(orderBy));
            } else {
                criteria.addOrder(Order.desc(orderBy));
            }
        }
        if (offset > 0) {
            criteria.setFirstResult(offset);
        }
        if (maxResults > 0) {
            criteria.setMaxResults(maxResults);
        }

        //System.out.println("is criteria.list() null = " + (criteria.list() == null));
        List<ViewTerminalSummary> list = criteria.list();
        return list;

    }

    @Override
    public List<ViewTerminalSummary> searchTerminals(String serialNo, String vehicleNo, long modelId,
        String orderBy, boolean ascending, int offset, int maxResults) {
        Session session = this.getSession();

        StringBuilder queryStr=new StringBuilder();
        queryStr.append(" SELECT t.id as id, r.description as release_desc,"
            + " (DECODE(1 , DECODE( 1 ,DECODE( (r.version) , NULL , 1 , 0 ) , 1 , 0 ) ,0,(r.version))) as release_version,"
            + " t.vehicle_no as vehicle_no,"
            + " t.serial_no as serial_no,"
            + " t.update_date_time as update_date_time,"
            + " t.upload_log as upload_log,"
            + " m.id as model_id,"
            + " m.model_name as model_name,"
            + " t.vehicle_unpaired as vehicle_unpaired,"
            + " (DECODE(1 , DECODE( 1 ,DECODE( 1 , ("
            + "   SELECT 1 FROM  tmvw_term_with_downloaded_app twdp WHERE twdp.terminal_id  = t.id AND twdp.release_id = r.id AND ROWNUM < 2),1,0 ) , 1 , 0 ) ,'Completed', "
            + "   DECODE( 1 ,DECODE( 1 , (SELECT 1 FROM tmvw_term_with_download_job twdj WHERE twdj.terminal_id  = t.id AND twdj.release_id = r.id AND ROWNUM < 2),1,0 ) , 1 , 0 ) ,'Downloading','Not Started')) as download_status,"
            + " (DECODE(1 , DECODE( 1 ,DECODE( ( SELECT record_no FROM tmtb_term_app_download_job tadj WHERE tadj.release_id  = r.id AND tadj.terminal_id  = t.id AND tadj.status = 1 AND ROWNUM < 2) , NULL , 1 , 0 ) , 1 , 0 ) ,0,"
            + "   (SELECT record_no FROM  tmtb_term_app_download_job tadj WHERE tadj.release_id = r.id AND tadj.terminal_id = t.id AND tadj.status = 1 AND ROWNUM < 2))) as record_no,"
            + " (case when(SELECT MAX(record_no) FROM  tmtb_release_file rf WHERE rf.release_id = r.id) is null then 0 else (SELECT MAX(record_no) FROM tmtb_release_file rf WHERE rf.release_id = r.id) end) as total_record_no, "
            + " t.CURRENT_VERSION AS current_version "
            + " FROM tmtb_tms_terminal t,"
            + "  tmtb_vehicle_group vg,"
            + "  tmtb_model m,"
            + "  tmtb_grouping g,"
            + "  tmtb_release r "
            + " WHERE g.release_id = r.id (+)"
            + "  AND (t.model_id = m.id AND t.vehicle_no = vg.vehicle_id AND vg.group_id = g.id AND t.deleted = 0 AND t.VEHICLE_UNPAIRED=0");
        if (serialNo != null && serialNo.length() > 0) {
            queryStr.append(" AND t.SERIAL_NO LIKE :serialNo");
        }
        if (vehicleNo != null && vehicleNo.length() > 0) {
            queryStr.append(" AND t.vehicle_no LIKE :vehicleNo");
        }
        if (modelId>0) {
            queryStr.append(" AND t.model_id=:modelId");
        }
        queryStr.append(")");
        if (orderBy != null) {
            if (orderBy.indexOf('.') < 0) {
                queryStr.append(" ORDER BY ");
                if (orderBy.equalsIgnoreCase("vehicleNo")) {
                    queryStr.append("vehicle_no");
                } else if (orderBy.equalsIgnoreCase("serialNo")) {
                    queryStr.append("serial_no");
                } else if (orderBy.equalsIgnoreCase("lastUpdated")) {
                    queryStr.append("update_date_time");
                } else {
                    queryStr.append("id");
                }
            }
            if (ascending) {
                queryStr.append(" ASC");
            } else {
                queryStr.append(" DESC");
            }
        }

        Query q=session.createSQLQuery(queryStr.toString()).addEntity(ViewTerminalSummary.class);

        if (serialNo != null && serialNo.length() > 0) {
            q.setParameter("serialNo", serialNo);
            //System.out.println("serialNo:"+serialNo);
        }

        if (vehicleNo != null && vehicleNo.length() > 0) {
            q.setParameter("vehicleNo",vehicleNo);
            //System.out.println("vehicleNo:"+vehicleNo);
        }

        if (modelId>0) {
            q.setParameter("modelId",modelId);
            //System.out.println("modelId:"+modelId);
        }

        if (offset > 0) {
            q.setFirstResult(offset);
        }
        if (maxResults > 0) {
            q.setMaxResults(maxResults);
        }

        //System.out.println("is criteria.list() null = " + (criteria.list() == null));
        List<ViewTerminalSummary> list = q.list();
        return list;

    }


    @SuppressWarnings("unchecked")
    //@Override
    public int countTerminals1(String serialNo, String vehicleNo, long modelId) {
        Session session = this.getSession();
        Criteria criteria = session.createCriteria(ViewTerminalSummary.class, "tmsTerminal")
                .setProjection(Projections.rowCount());

        if (serialNo != null && serialNo.length() > 0) {
            criteria.add(Restrictions.ilike("tmsTerminal.serialNo", serialNo ,MatchMode.ANYWHERE));

        }

        if (vehicleNo != null && vehicleNo.length() > 0) {
            criteria.add(Restrictions.ilike("tmsTerminal.vehicleNo", vehicleNo ,MatchMode.ANYWHERE));

        }
        
        if (modelId>0) {
            criteria.add(Restrictions.eq("tmsTerminal.modelId", modelId));

        }
    
        criteria.add(Restrictions.eq("tmsTerminal.vehicleUnpaired", false));
        List<Number> list = criteria.list();
        if ((list != null) && (list.size() > 0)) {
            return list.get(0).intValue();
        } else {
            return 0;
        }

    }

    @Override
    public int countTerminals(String serialNo, String vehicleNo, long modelId) {
        Session session = this.getSession();
        StringBuilder queryStr=new StringBuilder();
        queryStr.append(" SELECT count(*) AS CNT "
            + " FROM tmtb_tms_terminal t,"
            + "  tmtb_vehicle_group vg,"
            + "  tmtb_model m,"
            + "  tmtb_grouping g,"
            + "  tmtb_release r "
            + " WHERE g.release_id = r.id (+)"
            + "  AND (t.model_id = m.id AND t.vehicle_no = vg.vehicle_id AND vg.group_id = g.id AND t.deleted = 0 AND t.VEHICLE_UNPAIRED=0");
        if (serialNo != null && serialNo.length() > 0) {
            queryStr.append(" AND t.SERIAL_NO LIKE :serialNo");
        }
        if (vehicleNo != null && vehicleNo.length() > 0) {
            queryStr.append(" AND t.vehicle_no LIKE :vehicleNo");
        }
        if (modelId>0) {
            queryStr.append(" AND t.model_id=:modelId");
        }
        queryStr.append(")");

        Query q=session.createSQLQuery(queryStr.toString());

        if (serialNo != null && serialNo.length() > 0) {
            q.setParameter("serialNo", serialNo);
            //System.out.println("serialNo:"+serialNo);
        }

        if (vehicleNo != null && vehicleNo.length() > 0) {
            q.setParameter("vehicleNo",vehicleNo);
            //System.out.println("vehicleNo:"+vehicleNo);
        }

        if (modelId>0) {
            q.setParameter("modelId",modelId);
            //System.out.println("modelId:"+modelId);
        }

        //System.out.println("Query: "+queryStr.toString());

        Long l=((Number)q.uniqueResult()).longValue();
        return l.intValue();
    }

}
