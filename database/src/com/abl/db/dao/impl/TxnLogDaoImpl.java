package com.abl.db.dao.impl;

import com.abl.db.dao.TxnLogDao;
import com.abl.db.model.TxnLog;
import org.hibernate.LockMode;
import org.springframework.stereotype.Repository;

@Repository
public class TxnLogDaoImpl extends DaoImpl implements TxnLogDao {

    @Override
    public TxnLog getTxnLog(long id) {
        return getHibernateTemplate().get(TxnLog.class, id);
    }

    @Override
    public TxnLog getTxnLogForUpdate(long id) {
        return getHibernateTemplate().get(TxnLog.class, id, LockMode.PESSIMISTIC_WRITE);
    }

    @Override
    public void save(TxnLog txnLog) {
        getHibernateTemplate().saveOrUpdate(txnLog);
    }
}
