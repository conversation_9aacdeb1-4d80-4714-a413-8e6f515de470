package com.abl.db.dao.impl;

import java.util.List;

import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Projections;
import org.springframework.stereotype.Repository;

import com.abl.db.dao.AccessProfileDao;
import com.abl.db.model.AccessProfile;

@Repository
public class AccessProfileDaoImpl extends DaoImpl implements AccessProfileDao {

	@Override
	public AccessProfile getAccessProfile(long id) {
		return getHibernateTemplate().get(AccessProfile.class, id);
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public AccessProfile getAccessProfile(String name) {
		List<AccessProfile> list = getHibernateTemplate().find("from AccessProfile accessProfile where accessProfile.name=?", name);
		if ((list != null)&&(list.size()>0)) {
			return (AccessProfile)list.get(0);
		} else {
			return null;
		}
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public AccessProfile getAccessProfileFetchPages(String name) {
		List<AccessProfile> list = getHibernateTemplate().find("from AccessProfile accessProfile join fetch accessProfile.pages page join fetch page.module where accessProfile.name=?", name);
		if ((list != null)&&(list.size()>0)) {
			return (AccessProfile)list.get(0);
		} else {
			return null;
		}
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public AccessProfile getAccessProfileFetchPages(long id) {
		List<AccessProfile> list = getHibernateTemplate().find("from AccessProfile accessProfile " +
				"join fetch accessProfile.pages where accessProfile.id=?", id);
		if ((list != null)&&(list.size()>0)) {
			return (AccessProfile)list.get(0);
		} else {
			return null;
		}
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public List<AccessProfile> getAccessProfiles() {
		return getHibernateTemplate().find("from AccessProfile accessProfile order by accessProfile.name");
	}
	
	@SuppressWarnings({ "unchecked" })
	@Override
	public int countAccessProfiles() {
		Session session = this.getSession();
		Criteria criteria = session.createCriteria(AccessProfile.class)
							.setProjection(Projections.rowCount());
		List<Number> list = criteria.list();
		if ((list != null) && (list.size() > 0)) {
			return list.get(0).intValue();
		} else {
			return 0;
		}
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public List<AccessProfile> searchAccessProfiles(String orderBy, boolean ascending, int offset, int maxResults) {
		Session session = this.getSession();
		Criteria criteria = session.createCriteria(AccessProfile.class);
		if (orderBy != null) {
			if (ascending) {
				criteria.addOrder(Order.asc(orderBy));
			} else {
				criteria.addOrder(Order.desc(orderBy));
			}
		}

		if (offset > 0) {
			criteria.setFirstResult(offset);
		}
		if (maxResults > 0) {
			criteria.setMaxResults(maxResults);
		}
		
		List<AccessProfile> list = criteria.list();
		return list;
	}

	@Override
	public void delete(Object object) {
		AccessProfile accessProfile = (AccessProfile)object;
		accessProfile.setPages(null);
		getHibernateTemplate().delete(object);
	}
	
}
