package com.abl.db.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.abl.db.dao.ApplicationBinRangeDao;
import com.abl.db.model.ApplicationBinRange;

@Repository
public class ApplicationBinRangeDaoImpl extends DaoImpl implements
		ApplicationBinRangeDao {

	public ApplicationBinRange getApplicationBinRange(long id) {
		return getHibernateTemplate().get(ApplicationBinRange.class, id);

	}
	
	@SuppressWarnings("unchecked")
	public List<ApplicationBinRange> getApplicationBinRangeFetchApplication(long appId) {
		return getHibernateTemplate().find("from ApplicationBinRange applicationBinRange " +
				"left join fetch applicationBinRange.application application " +
				"where application.id =?", appId);
	
	}
	
	@Override
	public void saveAll(List<ApplicationBinRange> object) {
		getHibernateTemplate().saveOrUpdateAll(object);
	}

}
