package com.abl.db.dao.impl;

import com.abl.db.dao.BlacklistDownloadJobDao;
import com.abl.db.model.BlacklistDownloadJob;
import com.abl.utils.DateUtils;
import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public class BlacklistDownloadJobDaoImpl extends DaoImpl implements
        BlacklistDownloadJobDao {

    @Override
    public List<BlacklistDownloadJob> getBlacklistDownloadJobs(short status) {
        Session session = this.getSession();
        Criteria criteria = session.createCriteria(BlacklistDownloadJob.class, "blacklistDownloadJob");
        criteria.add(Restrictions.eq("blacklistDownloadJob.deleted", false));
        if (status >= 0) {
            criteria.add(Restrictions.eq("blacklistDownloadJob.status", status));
        }

        List<BlacklistDownloadJob> list = criteria.list();
        return list;

    }

    @Override
    public List<BlacklistDownloadJob> getCurrentJobs() {
        String currentTime = DateUtils.format(new Date(), "HHmm");
        return getHibernateTemplate().find("from BlacklistDownloadJob job " +
                "where job.deleted=0 " +
                "and ((job.startWindow=job.endWindow) " +
                "or (job.startWindow<job.endWindow and job.startWindow<=? and job.endWindow>=?) " +
                "or (job.startWindow>job.endWindow and (job.startWindow<=? or job.endWindow>=?)))",
                currentTime, currentTime,
                currentTime, currentTime);
    }
    
    @Override
    public List<BlacklistDownloadJob> getBlacklistDownloadJob(){
    	List<BlacklistDownloadJob> list =getHibernateTemplate().find(" from BlacklistDownloadJob");
    	if ((list != null)&&(list.size()>0)) {
			return list;
		} else {
			return null;
		}
    }
    
    @Override
    public BlacklistDownloadJob getBlacklistDownloadJobs(long id){
    	return getHibernateTemplate().get(BlacklistDownloadJob.class,id);
    	
    }
    
  
}
