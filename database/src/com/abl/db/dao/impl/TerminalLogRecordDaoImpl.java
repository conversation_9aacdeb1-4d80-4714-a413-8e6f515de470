package com.abl.db.dao.impl;

import com.abl.db.dao.TerminalLogRecordDao;
import com.abl.db.model.TerminalLogRecord;
import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.MatchMode;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public class TerminalLogRecordDaoImpl extends DaoImpl implements TerminalLogRecordDao {

    @Override
    public TerminalLogRecord getTerminaLogRecord(long id) {
        return getHibernateTemplate().get(TerminalLogRecord.class, id);
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<TerminalLogRecord> getTerminaLogRecordByLogSequence(long terminalLogId, int sequenceNo) {
        return getHibernateTemplate().find("from TerminalLogRecord terminalLogRecord " +
                "where terminalLogRecord.terminalLog.id=? and terminalLogRecord.sequenceNo=?",
                terminalLogId, sequenceNo);
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<TerminalLogRecord> getTerminaLogFetchRecordByTerminal(long terminalId, Date startDate, Date endDate, String data, String source) {

//    	List<TerminalLogRecord> list = getHibernateTemplate().find("from TerminalLogRecord logRecord " +
//				"left join fetch logRecord.terminalLog terminalLog "+
//				"left join fetch terminalLog.terminal terminal " +
//				"where terminal.id=?", terminalId);

        Session session = this.getSession();
        Criteria criteria = session.createCriteria(TerminalLogRecord.class, "terminalLogRecord")
                .createCriteria("terminalLog", "terminalLog", Criteria.LEFT_JOIN)
                .createCriteria("tmsTerminal", "terminal", Criteria.LEFT_JOIN);

        if (startDate != null) {
            criteria.add(Restrictions.ge("terminalLog.logDate", startDate));
        }

        if (endDate != null) {
            criteria.add(Restrictions.le("terminalLog.logDate", endDate));
        }

        if ((data != null) && (data.length() > 0)) {
            criteria.add(Restrictions.ilike("terminalLogRecord.data", data ,MatchMode.ANYWHERE));
        }

        if ((source != null) && (source.length() > 0)) {
            criteria.add(Restrictions.ilike("terminalLogRecord.source", source ,MatchMode.ANYWHERE));
        }


        criteria.add(Restrictions.eq("terminal.id", terminalId));
        criteria.addOrder(Order.asc("terminalLog.logDate"));
        criteria.addOrder(Order.asc("terminalLogRecord.sequenceNo"));
        criteria.addOrder(Order.asc("terminalLogRecord.id"));

        List<TerminalLogRecord> list = criteria.list();
        return list;


    }
}
