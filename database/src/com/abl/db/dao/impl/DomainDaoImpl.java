package com.abl.db.dao.impl;

import java.util.List;

import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Repository;

import com.abl.db.dao.DomainDao;
import com.abl.db.model.Domain;

@Repository
public class DomainDaoImpl extends DaoImpl implements DomainDao {

	public Domain getDomain(long id) {
		return getHibernateTemplate().get(Domain.class, id);
	}
	
	@SuppressWarnings("unchecked")
	public List<Domain> getDomains() {
		return getHibernateTemplate().find("from Domain domain where deleted=? order by shortName", false);
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public Domain getDomainByShortName(String shortName) {
		List<Domain> list = getHibernateTemplate().find("from Domain domain where domain.shortName=?", shortName);
		if ((list != null)&&(list.size()>0)) {
			return list.get(0);
		} else {
			return null;
		}
	}

	@SuppressWarnings("unchecked")
	@Override
	public Domain getDomainByName(String name) {
		List<Domain> list = getHibernateTemplate().find("from Domain domain where domain.name=?", name);
		if ((list != null)&&(list.size()>0)) {
			return list.get(0);
		} else {
			return null;
		}
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public int countDomains() {
		Session session = this.getSession();
		Criteria criteria = session.createCriteria(Domain.class)
							.setProjection(Projections.rowCount())
							.add(Restrictions.eq("deleted", false));
		List<Number> list = criteria.list();
		if ((list != null) && (list.size() > 0)) {
			return list.get(0).intValue();
		} else {
			return 0;
		}
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public List<Domain> searchDomains(String orderBy, boolean ascending, int offset, int maxResults) {
		Session session = this.getSession();
		Criteria criteria = session.createCriteria(Domain.class)
							.add(Restrictions.eq("deleted", false));
		if (orderBy != null) {
			if (ascending) {
				criteria.addOrder(Order.asc(orderBy));
			} else {
				criteria.addOrder(Order.desc(orderBy));
			}
		}

		if (offset > 0) {
			criteria.setFirstResult(offset);
		}
		if (maxResults > 0) {
			criteria.setMaxResults(maxResults);
		}
		
		List<Domain> list = criteria.list();
		return list;
		
	}
	
}
