package com.abl.db.dao.impl;

import com.abl.db.dao.ModelDao;
import com.abl.db.model.Model;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class ModelDaoImpl extends DaoImpl implements ModelDao {

    @Override
    public Model getModel(long id) {
        return getHibernateTemplate().get(Model.class, id);
    }

    @Override
    public List<Model> getModels() {
        return getHibernateTemplate().find("from Model model where model.deleted =0 order by model.modelName asc");

    }

    @Override
    public Model getModelByName(String name) {
        return (Model) getSession().createQuery("from Model model where upper(model.modelName) = ? and model.deleted = 0").setString(0, name.toUpperCase()).uniqueResult();
    }
}
