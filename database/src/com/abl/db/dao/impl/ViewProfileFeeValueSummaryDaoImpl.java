package com.abl.db.dao.impl;

import java.util.List;

import org.hibernate.Session;
import org.springframework.stereotype.Repository;

import com.abl.db.model.ViewProfileFeeValueSummary;
import com.abl.db.dao.ViewProfileFeeValueSummaryDao;

@Repository
public class ViewProfileFeeValueSummaryDaoImpl extends DaoImpl implements ViewProfileFeeValueSummaryDao {
	@SuppressWarnings("unchecked")
	public List<ViewProfileFeeValueSummary> getProfileFeeValueSummary(long profileId) {
		return getHibernateTemplate().find("from ViewProfileFeeValueSummary profileFeeSummary where terminalProfileId=?", profileId);
		
	}
	
	@SuppressWarnings("unchecked")
	public ViewProfileFeeValueSummary getLastProfileFeeValueSummary() {

		Session session = this.getSession();
		List<ViewProfileFeeValueSummary>  list = session.createQuery("from ViewProfileFeeValueSummary order by id desc").setMaxResults(1).list();
				
		if(list!=null && list.size()>0){
			return list.get(0);
			
		}else {
			return null;
		}
	}

}
