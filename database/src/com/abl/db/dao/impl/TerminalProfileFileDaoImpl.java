package com.abl.db.dao.impl;

import com.abl.db.dao.TerminalProfileFileDao;
import com.abl.db.model.TerminalProfileFile;
import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class TerminalProfileFileDaoImpl extends DaoImpl implements TerminalProfileFileDao {

    @Override
    public List<TerminalProfileFile> getTerminalProfileFile(long terminalProfileId, int recordNo) {
        Session session = this.getSession();
        Criteria criteria = session.createCriteria(TerminalProfileFile.class, "terminalProfileFile");

        if (terminalProfileId > 0) {
            criteria.add(Restrictions.eq("terminalProfileFile.terminalProfile.id", terminalProfileId));
        }

        if (recordNo > 0) {
            criteria.add(Restrictions.eq("terminalProfileFile.recordNo", recordNo));
        }

        criteria.addOrder(Order.asc("terminalProfileFile.terminalProfile.id"));
        criteria.addOrder(Order.asc("terminalProfileFile.recordNo"));

        List<TerminalProfileFile> list = criteria.list();
        return list;
    }

    @Override
    public int getNextRecordNo(long terminalProfileId, int recordNo) {
        Session session = this.getSession();
        Criteria criteria = session.createCriteria(TerminalProfileFile.class, "terminalProfileFile");

        criteria.add(Restrictions.eq("terminalProfileFile.terminalProfile.id", terminalProfileId));
        criteria.add(Restrictions.gt("terminalProfileFile.recordNo", recordNo));

        criteria.addOrder(Order.asc("terminalProfileFile.recordNo"));

        List<TerminalProfileFile> list = criteria.list();
        if (list == null || list.size() <= 0) return 0;
        return list.get(0).getRecordNo();
    }

    @Override
    public void saveAll(List<TerminalProfileFile> object) {
        getHibernateTemplate().saveOrUpdateAll(object);
    }
}

