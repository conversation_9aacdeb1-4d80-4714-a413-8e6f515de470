package com.abl.db.dao.impl;

import java.util.List;

import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Repository;

import com.abl.db.dao.ViewVehicleGroupingSummaryDao;
import com.abl.db.model.Module;
import com.abl.db.model.ViewVehicleGroupingSummary;

@Repository
public class ViewVehicleGroupingSummaryDaoImpl extends DaoImpl implements ViewVehicleGroupingSummaryDao {
	@SuppressWarnings("unchecked")
	@Override
	public List<ViewVehicleGroupingSummary> getVehicleGroupingSummary() {
		return getHibernateTemplate().find("from ViewVehicleGroupingSummary summary ");
	}
	
	@Override
	public List<ViewVehicleGroupingSummary> getGroupSummaryByGroupAndReleaseId(long groupId, long releaseId, String orderBy, boolean ascending, int offset, int maxResults){
		Session session = this.getSession();
        Criteria criteria = session.createCriteria(ViewVehicleGroupingSummary.class,
                "viewVehicleGroupingSummary");
        
        if(groupId>0){
        	criteria.add(Restrictions.eq("viewVehicleGroupingSummary.groupId", groupId));
        }

        if (releaseId > 0) {
            criteria.add(Restrictions.eq("viewVehicleGroupingSummary.releaseId", releaseId));
        }
        if (orderBy != null) {
            if (orderBy.indexOf('.') < 0) {
                orderBy = "viewVehicleGroupingSummary." + orderBy;
            }
            if (ascending) {
                criteria.addOrder(Order.asc(orderBy));
            } else {
                criteria.addOrder(Order.desc(orderBy));
            }
        }

        if (offset > 0) {
            criteria.setFirstResult(offset);
        }
        if (maxResults > 0) {
            criteria.setMaxResults(maxResults);
        }

        List<ViewVehicleGroupingSummary> list = criteria.list();       
      

        return list;
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public int countGroupSummaryByGroupAndReleaseId(long groupId, long releaseId){
		Session session = this.getSession();
		Criteria criteria = session.createCriteria(ViewVehicleGroupingSummary.class,"viewVehicleGroupingSummary").setProjection(Projections.rowCount());

		if(groupId>0){
			criteria.add(Restrictions.eq("viewVehicleGroupingSummary.groupId", groupId));
		}

		if (releaseId > 0) {
			criteria.add(Restrictions.eq("viewVehicleGroupingSummary.releaseId", releaseId));
		}

		List<Number> list = criteria.list();
		if ((list != null) && (list.size() > 0)) {
			return list.get(0).intValue();
		} else {
			return 0;
		}
	}
	
	public ViewVehicleGroupingSummary getVehicleGroupingSummaryById(long id){
		List<ViewVehicleGroupingSummary> list = getHibernateTemplate().find("from ViewVehicleGroupingSummary summary where summary.id=?",id);
		
		if(list!=null && list.size()>0){
			return list.get(0);
			
		}else {
			return null;
		}
	}

	public ViewVehicleGroupingSummary getVehicleGroupingSummaryByGroupId(long id){
		List<ViewVehicleGroupingSummary> list = getHibernateTemplate().find("from ViewVehicleGroupingSummary summary where summary.groupId=?",id);

		if(list!=null && list.size()>0){
			return list.get(0);

		}else {
			return null;
		}
	}

}



