package com.abl.db.dao.impl;

import java.util.List;

import org.hibernate.Session;
import org.springframework.stereotype.Repository;

import com.abl.db.model.ViewProfileBinValueSummary;
import com.abl.db.dao.ViewProfileBinValueSummaryDao;

@Repository
public class ViewProfileBinValueSummaryDaoImpl extends DaoImpl implements ViewProfileBinValueSummaryDao {
	@SuppressWarnings("unchecked")
	public List<ViewProfileBinValueSummary> getProfileBinValueSummary(long profileId) {
		return getHibernateTemplate().find("from ViewProfileBinValueSummary profileBinSummary where terminalProfileId=?", profileId);
		
	}
	
	@SuppressWarnings("unchecked")
	public ViewProfileBinValueSummary getLastProfileBinValueSummary() {
		
//		List<ProfileBinValueSummary>  list = getHibernateTemplate().find("from ProfileBinValueSummary profileBinSummary order by id desc ");
		Session session = this.getSession();
		List<ViewProfileBinValueSummary>  list = session.createQuery("from ViewProfileBinValueSummary order by id desc").setMaxResults(1).list();
				
		if(list!=null && list.size()>0){
			return list.get(0);
			
		}else {
			return null;
		}
	}

}
