package com.abl.db.dao.impl;

import com.abl.db.bean.StuckTransactionSearchParam;
import com.abl.db.dao.StuckTransactionDao;
import com.abl.db.model.StuckTransaction;
import com.abl.db.model.TmsTerminal;
import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public class StuckTransactionDaoImpl extends DaoImpl implements StuckTransactionDao {

    @Override
    public void save(StuckTransaction stuckTxn) {
        getHibernateTemplate().saveOrUpdate(stuckTxn);
    }

    @Override
    public StuckTransaction getStuckTransaction(long id) {
        return getHibernateTemplate().get(StuckTransaction.class, id);
    }

    @Override
    public List<StuckTransaction> getStuckTransactions(StuckTransactionSearchParam searchParam,
                                                       int offset, int maxResults) {

        Session session = this.getSession();
        Criteria criteria = session.createCriteria(StuckTransaction.class, "txn")
                .createCriteria("tmsTerminal", "terminal", Criteria.LEFT_JOIN);

        updateGetStuckTransactionsCriteria(criteria, searchParam);

        criteria.addOrder(Order.asc("txn.txnDateTime"));

        if (offset > 0) {
            criteria.setFirstResult(offset);
        }
        if (maxResults > 0) {
            criteria.setMaxResults(maxResults);
        }

        List<StuckTransaction> list = criteria.list();
        return list;
    }

    @Override
    public int countStuckTransactions(StuckTransactionSearchParam searchParam) {

        Session session = this.getSession();
        Criteria criteria = session.createCriteria(StuckTransaction.class, "txn")
                .createCriteria("tmsTerminal", "terminal", Criteria.LEFT_JOIN)
                .setProjection(Projections.rowCount());

        updateGetStuckTransactionsCriteria(criteria, searchParam);

        List<Number> list = criteria.list();
        if ((list != null) && (list.size() > 0)) {
            return list.get(0).intValue();
        } else {
            return 0;
        }
    }

    @Override
    public int countStuckTransactionsByTerminalAndTimestamp(TmsTerminal terminal, Date timestamp) {
        Session session = this.getSession();
        Criteria criteria = session.createCriteria(StuckTransaction.class, "txn")
                .createCriteria("tmsTerminal", "terminal", Criteria.LEFT_JOIN)
                .setProjection(Projections.rowCount());

        criteria.add(Restrictions.eq("txn.txnDateTime",timestamp));
        criteria.add(Restrictions.eq("terminal.id", terminal.getId()));

        List<Number> list = criteria.list();
        if ((list != null) && (list.size() > 0)) {
            return list.get(0).intValue();
        } else {
            return 0;
        }
    }

    @Override
    public StuckTransaction getStuckTransactionByTerminalAndTimestamp(TmsTerminal terminal, Date timestamp) {
        Session session = this.getSession();
        Criteria criteria = session.createCriteria(StuckTransaction.class, "txn")
                .createCriteria("tmsTerminal", "terminal", Criteria.LEFT_JOIN);

        criteria.add(Restrictions.eq("txn.txnDateTime",timestamp));
        criteria.add(Restrictions.eq("terminal.id",terminal.getId()));

        criteria.addOrder(Order.asc("txn.txnDateTime"));

        criteria.setMaxResults(1);

        List<StuckTransaction> list = criteria.list();
        if (list!=null && list.size()>0)
            return list.get(0);
        return null;
    }

    @Override
    public List<StuckTransaction> getStuckTransactionsWithPendingActions(TmsTerminal terminal, int maxResults) {
        Session session = this.getSession();
        Criteria criteria = session.createCriteria(StuckTransaction.class, "txn")
                .createCriteria("tmsTerminal", "terminal", Criteria.LEFT_JOIN);

        criteria.add(Restrictions.eq("terminal.id",terminal.getId()));
        criteria.add(Restrictions.eq("txn.state", StuckTransaction.State.PENDING));
        criteria.add(Restrictions.isNotNull("txn.triggerBy"));

        criteria.addOrder(Order.asc("txn.txnDateTime"));
        criteria.setMaxResults(maxResults);

        List<StuckTransaction> list = criteria.list();
        return list;
    }

    /**
     * update Criteria for getStuckTransactions and countStuckTransactions
     *
     * @param criteria
     * @param searchParam
     */
    private void updateGetStuckTransactionsCriteria(Criteria criteria, StuckTransactionSearchParam searchParam) {
        if (searchParam.getSerialNo() != null) {
            criteria.add(Restrictions.ilike("terminal.serialNo", searchParam.getSerialNo()));
        }

        if (searchParam.getVehicleNo() != null) {
            criteria.add(Restrictions.ilike("terminal.vehicleNo", searchParam.getVehicleNo()));
        }

        if (searchParam.getStartDate() != null) {
            criteria.add(Restrictions.ge("txn.txnDateTime", searchParam.getStartDate()));
        }

        if (searchParam.getEndDate() != null) {
            criteria.add(Restrictions.le("txn.txnDateTime", searchParam.getEndDate()));
        }

        if (searchParam.getTriggerBy() != null) {
            criteria.add(Restrictions.eq("txn.triggerBy", searchParam.getTriggerBy()));
        }

        if (searchParam.getJobNumber() != null) {
            criteria.add(Restrictions.eq("txn.jobNumber", searchParam.getJobNumber()));
        }

        if (searchParam.getTxnType() != null) {
            criteria.add(Restrictions.eq("txn.txnType", searchParam.getTxnType()));
        }

        if (searchParam.getState() != null) {
            criteria.add(Restrictions.eq("txn.state", searchParam.getState()));
        }
    }
}
