package com.abl.db.dao.impl;

import java.util.List;

import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Repository;

import com.abl.db.dao.ViewTerminalBlacklistWithDownloadJobDao;
import com.abl.db.model.ViewTerminalBlacklistWithDownloadJob;

@Repository
public class ViewTerminalBlacklistWithDownloadJobDaoImpl extends DaoImpl implements ViewTerminalBlacklistWithDownloadJobDao{
	
	@SuppressWarnings("unchecked")
	public List<ViewTerminalBlacklistWithDownloadJob> getTerminalBlacklistWithDownloadJob(long jobId) {
		Session session = this.getSession();
		Criteria criteria = session.createCriteria(ViewTerminalBlacklistWithDownloadJob.class,
				"viewTerminalBlacklistWithDownloadJob");

//        criteria.add(Restrictions.ne("viewTerminalBlacklistWithDownloadJob.terminalBlacklistDownloadStatus",  0));
		criteria.add(Restrictions.eq("viewTerminalBlacklistWithDownloadJob.blacklistDownloadId", jobId));
		
		List<ViewTerminalBlacklistWithDownloadJob> terminalList = criteria.list();
		
		return terminalList;
		
	}

}
