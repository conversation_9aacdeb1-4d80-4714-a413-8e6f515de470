package com.abl.db.dao.impl;

import java.util.Date;
import java.util.List;

import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.MatchMode;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Projections;
import org.springframework.stereotype.Repository;
import org.hibernate.criterion.Restrictions;

import com.abl.db.dao.AdminLogDao;
import com.abl.db.model.AdminLog;

@Repository
public class AdminLogDaoImpl extends DaoImpl implements AdminLogDao {

	@SuppressWarnings("unchecked")
	@Override
	public AdminLog getAdminLogFetchAll(long id) {
//		List<AdminLog> list = getHibernateTemplate().find("from AdminLog adminLog " +
//				"left join fetch adminLog.adminUser " +
//				"join fetch adminLog.page where adminLog.id=?", id);
//		if ((list != null)&&(list.size()>0)) {
//			return list.get(0);
//		} else {
//			return null;
//		}
		
		List<AdminLog> list = getHibernateTemplate().find("from AdminLog adminLog " +
														"join fetch adminLog.page where adminLog.id=?", id);
		if ((list != null)&&(list.size()>0)) {
			return list.get(0);
		} else {
			return null;
		}
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public int countAdminLogs() {
		Session session = this.getSession();
		Criteria criteria = session.createCriteria(AdminLog.class, "adminLog")
							.setProjection(Projections.rowCount());
		List<Number> list = criteria.list();
		if ((list != null) && (list.size() > 0)) {
			return list.get(0).intValue();
		} else {
			return 0;
		}
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public List<AdminLog> searchAdminLogs(String orderBy, boolean ascending, int offset, int maxResults) {
		Session session = this.getSession();
		Criteria criteria = session.createCriteria(AdminLog.class, "adminLog")
									.createCriteria("adminLog.page", "page", Criteria.LEFT_JOIN);
		if (orderBy != null) {
			if (orderBy.indexOf('.')<0) {
				orderBy = "adminLog." + orderBy;
			}
			if (ascending) {
				criteria.addOrder(Order.asc(orderBy));
			} else {
				criteria.addOrder(Order.desc(orderBy));
			}
		}
		
		if (offset > 0) {
			criteria.setFirstResult(offset);
		}
		if (maxResults > 0) {
			criteria.setMaxResults(maxResults);
		}
		
		

		List<AdminLog> list = criteria.list();
		return list;
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public List<AdminLog> searchAdminLogs(String login, Date startDate, Date endDate, String orderBy, boolean ascending, int offset, int maxResults) {
		Session session = this.getSession();
		Criteria criteria = session.createCriteria(AdminLog.class, "adminLog")
									.createCriteria("adminLog.page", "page", Criteria.LEFT_JOIN);
		if (orderBy != null) {
			if (orderBy.indexOf('.')<0) {
				orderBy = "adminLog." + orderBy;
			}
			if (ascending) {
				criteria.addOrder(Order.asc(orderBy));
			} else {
				criteria.addOrder(Order.desc(orderBy));
			}
		}
		
		if (offset > 0) {
			criteria.setFirstResult(offset);
		}
		if (maxResults > 0) {
			criteria.setMaxResults(maxResults);
		}
		
		if ((login != null)&&(login.length() > 0)) {
			criteria.add(Restrictions.ilike("adminLog.user", login, MatchMode.ANYWHERE));
		}
		
		if (startDate != null) {
			criteria.add(Restrictions.ge("adminLog.logDateTime", startDate));
		}
		
		if (endDate != null) {
			criteria.add(Restrictions.le("adminLog.logDateTime", endDate));
		}


		List<AdminLog> list = criteria.list();
		return list;
	}
	
	
	@SuppressWarnings("unchecked")
	@Override
	public int countAdminLogs(String login, Date startDate, Date endDate) {
		Session session = this.getSession();
		Criteria criteria = session.createCriteria(AdminLog.class, "adminLog")
							.setProjection(Projections.rowCount());
		
		if ((login != null)&&(login.length() > 0)) {
			criteria.add(Restrictions.ilike("adminLog.user", login ,MatchMode.ANYWHERE));
		}
		
		if (startDate != null) {
			criteria.add(Restrictions.ge("adminLog.logDateTime", startDate));
		}
		
		if (endDate != null) {
			criteria.add(Restrictions.le("adminLog.logDateTime", endDate));
		}
		List<Number> list = criteria.list();
		if ((list != null) && (list.size() > 0)) {
			return list.get(0).intValue();
		} else {
			return 0;
		}
	}
	
}
