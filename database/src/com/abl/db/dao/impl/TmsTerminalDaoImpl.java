package com.abl.db.dao.impl;

import com.abl.db.dao.TmsTerminalDao;
import com.abl.db.model.TmsTerminal;

import org.hibernate.Criteria;
import org.hibernate.LockMode;
import org.hibernate.Session;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Restrictions;
import org.hibernate.criterion.MatchMode;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class TmsTerminalDaoImpl extends DaoImpl implements TmsTerminalDao {

    public TmsTerminal getTerminal(long id) {
        return getHibernateTemplate().get(TmsTerminal.class, id);
    }

    @Override
    @SuppressWarnings("unchecked")
    public TmsTerminal getTerminalFetchModel(long id) {
        List<TmsTerminal> list = getHibernateTemplate().find("from TmsTerminal terminal " +
                "left join fetch terminal.model where terminal.id=?", id);
        if ((list != null) && (list.size() > 0)) {
            return list.get(0);
        } else {
            return null;
        }

    }

    @Override
    public void save(TmsTerminal terminal) {
        getHibernateTemplate().saveOrUpdate(terminal);
    }

    @Override
    public void saveAll(List<TmsTerminal> object) {
        getHibernateTemplate().saveOrUpdateAll(object);
    }


    @SuppressWarnings("unchecked")
    @Override
    public List<TmsTerminal> searchTerminals(String serialNo, String vehicleNo, long modelId,
                                             String orderBy, boolean ascending, int offset, int maxResults) {
        Session session = this.getSession();
        Criteria criteria = session.createCriteria(TmsTerminal.class, "tmsTerminal");
        criteria.createAlias("model", "model", criteria.LEFT_JOIN);

        if (serialNo != null && serialNo.length() > 0) {
            criteria.add(Restrictions.ilike("tmsTerminal.serialNo", serialNo ,MatchMode.ANYWHERE));

        }

        if (vehicleNo != null && vehicleNo.length() > 0) {
            criteria.add(Restrictions.ilike("tmsTerminal.vehicleNo", vehicleNo ,MatchMode.ANYWHERE));

        }
        
        if (modelId>0) {
            criteria.add(Restrictions.eq("model.id", modelId));

        }
        criteria.add(Restrictions.eq("tmsTerminal.deleted", false));
        //System.out.println("in search Terminals..");
        if (orderBy != null) {
            if (orderBy.indexOf('.') < 0) {
                orderBy = "tmsTerminal.id";
            }
            if (ascending) {
                criteria.addOrder(Order.asc(orderBy));
            } else {
                criteria.addOrder(Order.desc(orderBy));
            }
        }
        if (offset > 0) {
            criteria.setFirstResult(offset);
        }
        if (maxResults > 0) {
            criteria.setMaxResults(maxResults);
        }

        //System.out.println("is criteria.list() null = " + (criteria.list() == null));
        List<TmsTerminal> list = criteria.list();
        return list;

    }

    @SuppressWarnings("unchecked")
    @Override
    public int countTerminals(String serialNo, String vehicleNo, long modelId) {
        Session session = this.getSession();
        Criteria criteria = session.createCriteria(TmsTerminal.class, "tmsTerminal")
                .setProjection(Projections.rowCount());
        criteria.createAlias("model", "model", criteria.LEFT_JOIN);

        if (serialNo != null && serialNo.length() > 0) {
            criteria.add(Restrictions.ilike("tmsTerminal.serialNo", serialNo ,MatchMode.ANYWHERE));

        }

        if (vehicleNo != null && vehicleNo.length() > 0) {
            criteria.add(Restrictions.ilike("tmsTerminal.vehicleNo", vehicleNo ,MatchMode.ANYWHERE));

        }
        
        if (modelId>0) {
            criteria.add(Restrictions.eq("model.id", modelId));

        }
        criteria.add(Restrictions.eq("tmsTerminal.deleted", false));

        List<Number> list = criteria.list();
        if ((list != null) && (list.size() > 0)) {
            return list.get(0).intValue();
        } else {
            return 0;
        }

    }

    @Override
    public TmsTerminal getTerminalBySerialNo(String serialNo) {
        return (TmsTerminal) getSession().createQuery("from TmsTerminal where serial_no = ? and deleted = 0").setString(0, serialNo).uniqueResult();
    }

    @SuppressWarnings("unchecked")
    @Override
    public TmsTerminal getTerminalForUpdate(String serialNo) {
        Session session = this.getSession();
        Criteria criteria = session.createCriteria(TmsTerminal.class)
//								.setLockMode(LockMode.PESSIMISTIC_WRITE)
                .add(Restrictions.eq("deleted", false))
                .add(Restrictions.ilike("serialNo", serialNo ,MatchMode.ANYWHERE));

        List<TmsTerminal> list = criteria.list();
        if ((list != null) && (list.size() > 0)) {
            TmsTerminal terminal = list.get(0);

            //TODO: criteria.setLockMode is currently (Nov 2010) not working.  It is an outstanding Hibernate bug.
            //      so, we add a lock here.  For some reasons, this will not work if setLockMode is used at criteria object.
            getHibernateTemplate().refresh(terminal, LockMode.PESSIMISTIC_WRITE);

            return terminal;
        } else {
            return null;
        }
    }
    
    @Override
    public int unpairOtherTerminalsForVehicleNo(String vehicleNo, String serialNo){

        Session session = this.getSession();

        String hql = "UPDATE TmsTerminal set vehicle_unpaired = 1 "  +
                "WHERE deleted = 0 and vehicle_unpaired = 0 and serial_no != :serial_no and vehicle_no = :vehicle_no";
        org.hibernate.Query query = session.createQuery(hql);
        query.setParameter("serial_no", serialNo);
        query.setParameter("vehicle_no", vehicleNo.toUpperCase());
        int result = query.executeUpdate();
        return result;

//         Criteria criteria = session.createCriteria(TmsTerminal.class)
//	         .add(Restrictions.eq("deleted", false))
//	         .add(Restrictions.eq("vehicleUnpaired", false))
//	         .add(Restrictions.ne("serialNo", serialNo))
//	         .add(Restrictions.eq("vehicleNo", vehicleNo).ignoreCase());
//         List<TmsTerminal> list = criteria.list();
//         if ((list != null) && (list.size() > 0)) {
//             return list.get(0);
//         } else {
//             return null;
//         }
    }
}
