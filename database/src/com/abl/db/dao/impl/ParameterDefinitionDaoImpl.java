package com.abl.db.dao.impl;

import com.abl.db.dao.ParameterDefinitionDao;
import com.abl.db.model.ParameterDefinition;
import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class ParameterDefinitionDaoImpl extends DaoImpl implements
        ParameterDefinitionDao {

    @Override
    public ParameterDefinition getParameterDefinition(long id) {
        return getHibernateTemplate().get(ParameterDefinition.class, id);
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<ParameterDefinition> getParameterDefinitionFetchApplication(long appId) {
        List<ParameterDefinition> list = getHibernateTemplate().find("from ParameterDefinition paramDef " +
                "left join fetch paramDef.application application " +
                "where paramDef.level =0 and application.id = ?  order by paramDef.id", appId);
        return list;
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<ParameterDefinition> getBinParameterDefinitionFetchApplication(long appId) {
        List<ParameterDefinition> list = getHibernateTemplate().find("from ParameterDefinition paramDef " +
                "left join fetch paramDef.application application " +
                "where paramDef.level =1 and application.id = ?", appId);
        return list;
    }

    @Override
    public void saveAll(List<ParameterDefinition> object) {
        getHibernateTemplate().saveOrUpdateAll(object);
    }

    @SuppressWarnings("unchecked")
    @Override
    public ParameterDefinition getParameterDefinitionByNameApp(String name, long applicationId) {
        List<ParameterDefinition> list = getHibernateTemplate().find("from ParameterDefinition paramDef " +
                "left join fetch paramDef.application application " +
                "where paramDef.name =? and application.id =?", name, applicationId);
        if ((list != null) && (list.size() > 0)) {
            return list.get(0);
        } else {
            return null;
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public int countParameters(long appId) {
        Session session = this.getSession();
        Criteria criteria = session.createCriteria(ParameterDefinition.class, "parameterDefinition")
                .createCriteria("application", "application", Criteria.LEFT_JOIN).setProjection(Projections.rowCount());

        if (appId > 0) {
            criteria.add(Restrictions.eq("application.id", appId));
        }

        criteria.add(Restrictions.eq("parameterDefinition.deleted", false));

        List<Number> list = criteria.list();
        if ((list != null) && (list.size() > 0)) {
            return list.get(0).intValue();
        } else {
            return 0;
        }
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public List<ParameterDefinition> getFeeParameterDefinitionFetchApplication(long appId) {
        List<ParameterDefinition> list = getHibernateTemplate().find("from ParameterDefinition paramDef " +
                "left join fetch paramDef.application application " +
                "where paramDef.level =2 and application.id = ?", appId);
        return list;
    }

}
