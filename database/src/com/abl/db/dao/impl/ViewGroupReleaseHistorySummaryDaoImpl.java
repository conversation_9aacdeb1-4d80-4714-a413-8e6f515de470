package com.abl.db.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.abl.db.dao.ViewGroupReleaseHistorySummaryDao;
import com.abl.db.model.ViewGroupReleaseHistorySummary;

@Repository
public class ViewGroupReleaseHistorySummaryDaoImpl extends DaoImpl implements ViewGroupReleaseHistorySummaryDao {
	
	@Override
	public List<ViewGroupReleaseHistorySummary> getGroupReleaseHistoryByGroupRelease(long groupId, long releaseId){
		return getHibernateTemplate().find("from ViewGroupReleaseHistorySummary summary where groupId=? and releaseId=?", groupId, releaseId);
	}

}
