package com.abl.db.dao.impl;

import java.util.List;

import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Repository;

import com.abl.db.dao.ViewOtaStatusReportSummaryDao;
import com.abl.db.model.ViewOtaStatusReportSummary;

@Repository
public class ViewOtaStatusReportSummaryDaoImpl extends DaoImpl implements ViewOtaStatusReportSummaryDao {

	public List<ViewOtaStatusReportSummary> searchSummary(String orderBy, boolean ascending, int offset, int maxResults){
		  	Session session = this.getSession();
	        Criteria criteria = session.createCriteria(ViewOtaStatusReportSummary.class, "viewOtaStatusReportSummary");
	      
	        if (orderBy != null) {
	            if (orderBy.indexOf('.') < 0) {
	                orderBy = "viewOtaStatusReportSummary.groupName";
	            }
	            if (ascending) {
	                criteria.addOrder(Order.asc(orderBy));
	            } else {
	                criteria.addOrder(Order.desc(orderBy));
	            }
	        }
	        if (offset > 0) {
	            criteria.setFirstResult(offset);
	        }
	        if (maxResults > 0) {
	            criteria.setMaxResults(maxResults);
	        }

	        //System.out.println("is criteria.list() null = " + (criteria.list() == null));
	        List<ViewOtaStatusReportSummary> list = criteria.list();
	        return list;
		
	}
	
	public int countSummary(){
		
		Session session = this.getSession();
        Criteria criteria = session.createCriteria(ViewOtaStatusReportSummary.class, "viewOtaStatusReportSummary").setProjection(Projections.rowCount());;
        
        List<Number> list = criteria.list();
        if ((list != null) && (list.size() > 0)) {
            return list.get(0).intValue();
        } else {
            return 0;
        }
        
		
	}
	
	public List<ViewOtaStatusReportSummary> searchSelectedGroupSummary(List<String> groupIdList, String orderBy, boolean ascending, int offset, int maxResults){
		Session session = this.getSession();
        Criteria criteria = session.createCriteria(ViewOtaStatusReportSummary.class, "viewOtaStatusReportSummary");
      
        if(groupIdList!=null && groupIdList.size()>0){
        	criteria.add(Restrictions.in("viewOtaStatusReportSummary.groupId", groupIdList));
        }
        if (orderBy != null) {
            if (orderBy.indexOf('.') < 0) {
                orderBy = "viewOtaStatusReportSummary.groupName";
            }
            if (ascending) {
                criteria.addOrder(Order.asc(orderBy));
            } else {
                criteria.addOrder(Order.desc(orderBy));
            }
        }
        if (offset > 0) {
            criteria.setFirstResult(offset);
        }
        if (maxResults > 0) {
            criteria.setMaxResults(maxResults);
        }

       
        List<ViewOtaStatusReportSummary> list = criteria.list();
        return list;
	}

}
