package com.abl.db.dao.impl;

import com.abl.db.dao.NofAccountDao;
import com.abl.db.model.NofAccount;

import java.util.List;

import org.springframework.stereotype.Repository;

@Repository
public class NofAccountDaoImpl extends DaoImpl implements NofAccountDao {

    @Override
    public NofAccount getNofAccount(long id) {
        return getHibernateTemplate().get(NofAccount.class, id);
    }

	@Override
	public NofAccount getNofAccountByMerchTokenId(String merchTokenId) {
		@SuppressWarnings("unchecked")
		List<NofAccount> list = getHibernateTemplate().find("from NofAccount where merchTokenId=?", merchTokenId);
		if ((list!=null)&&(!list.isEmpty())) {
			return list.get(0);
		} else {
			return null;
		}
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<NofAccount> getNofAccountsByGuidMuid(String guid, String muid) {
		return getHibernateTemplate().find("from NofAccount where guid=? and muid=?", guid, muid);
	}
	
	@Override
	public NofAccount getUndeletedNofAccountsByGuidMuidTokenIndex(String guid, String muid, String tokenIndex) {
		return (NofAccount)getSession().createQuery("from NofAccount where guid=:guid and muid=:muid and tokenIndex=:tokenIndex and deleteDateTime is null")
			.setString("guid", guid)
			.setString("muid", muid)
			.setString("tokenIndex", tokenIndex)
			.uniqueResult();
	}
	
    @Override
    public void save(NofAccount account) {
        getHibernateTemplate().save(account);
    }

	@Override
	public void update(NofAccount account) {
		getHibernateTemplate().update(account);
	}
}
