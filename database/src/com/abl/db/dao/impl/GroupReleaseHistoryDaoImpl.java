package com.abl.db.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;
import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Restrictions;

import com.abl.db.dao.GroupReleaseHistoryDao;
import com.abl.db.model.GroupReleaseHistory;

@Repository
public class GroupReleaseHistoryDaoImpl extends DaoImpl implements GroupReleaseHistoryDao{
	
	@SuppressWarnings("unchecked")
	@Override
	public List<GroupReleaseHistory> getGroupReleaseHistoryByGroupId(long groupId){
		List<GroupReleaseHistory> list = getHibernateTemplate().find("from GroupReleaseHistory groupReleaseHistory " +
				"left join fetch groupReleaseHistory.release where groupReleaseHistory.release.deleted=0 "+
				"and groupReleaseHistory.group.id=? order by groupReleaseHistory.release.id asc", groupId);
		if ((list != null)&&(list.size()>0)) {
			return  list;
		} else {
			return null;
		}
	}
	
	@SuppressWarnings("unchecked")
    @Override
    public List<GroupReleaseHistory> searchGroupReleaseHistory(long groupId,long releaseId,
                                             String orderBy, boolean ascending, int offset, int maxResults) {
        Session session = this.getSession();
        Criteria criteria = session.createCriteria(GroupReleaseHistory.class, "groupRelHistory");
        criteria.createAlias("group", "group", criteria.LEFT_JOIN);
        criteria.createAlias("release", "release", criteria.LEFT_JOIN);

       
        
        if (groupId>0) {
            criteria.add(Restrictions.eq("groupRelHistory.group.id", groupId));

        }
        
        if (releaseId>0) {
            criteria.add(Restrictions.eq("groupRelHistory.release.id", releaseId));

        }
       
        if (orderBy != null) {
            if (orderBy.indexOf('.') < 0) {
                orderBy = "groupRelHistory.id";
            }
            if (ascending) {
                criteria.addOrder(Order.asc(orderBy));
            } else {
                criteria.addOrder(Order.desc(orderBy));
            }
        }
        if (offset > 0) {
            criteria.setFirstResult(offset);
        }
        if (maxResults > 0) {
            criteria.setMaxResults(maxResults);
        }

   
        List<GroupReleaseHistory> list = criteria.list();
        return list;

    }
	
	@SuppressWarnings("unchecked")
    @Override
    public int countGroupReleaseHistory(long groupId,long releaseId) {
        Session session = this.getSession();
        Criteria criteria = session.createCriteria(GroupReleaseHistory.class, "groupRelHistory")
                .setProjection(Projections.rowCount());
        criteria.createAlias("group", "group", criteria.LEFT_JOIN);
        criteria.createAlias("release", "release", criteria.LEFT_JOIN);
       
        
        if (groupId>0) {
            criteria.add(Restrictions.eq("groupRelHistory.group.id", groupId));

        }
        if (releaseId>0) {
            criteria.add(Restrictions.eq("groupRelHistory.release.id", releaseId));

        }
        
        List<Number> list = criteria.list();
        if ((list != null) && (list.size() > 0)) {
            return list.get(0).intValue();
        } else {
            return 0;
        }

    }

}
