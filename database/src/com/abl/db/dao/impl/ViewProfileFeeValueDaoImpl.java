package com.abl.db.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.abl.db.dao.ViewProfileFeeValueDao;
import com.abl.db.model.ViewProfileFeeValue;

@Repository
public class ViewProfileFeeValueDaoImpl extends DaoImpl implements ViewProfileFeeValueDao {
	
	@Override
	public void saveAll(List<ViewProfileFeeValue> object) {
		getHibernateTemplate().saveOrUpdateAll(object);
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public ViewProfileFeeValue getProfileFeeValue(long feeId, long paramId) {
		List<ViewProfileFeeValue> list = getHibernateTemplate().find("from ViewProfileFeeValue profileFeeValue " +
				" where profileFeeValue.profileFeeId= ? and profileFeeValue.parameterDefinitionId =?", feeId, paramId);
		
		if ((list != null)&&(list.size()>0)) {
			return list.get(0);
		} else {
			return null;
		}
	}

}
