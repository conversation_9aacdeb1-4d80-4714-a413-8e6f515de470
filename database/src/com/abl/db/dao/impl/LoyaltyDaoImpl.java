package com.abl.db.dao.impl;

import com.abl.db.dao.LoyaltyDao;
import com.abl.db.model.Loyalty;
import com.abl.db.model.LoyaltyFile;
import org.apache.log4j.Logger;
import org.hibernate.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: chinyew
 * Date: 21/12/12
 * Time: 3:20 PM
 * To change this template use File | Settings | File Templates.
 */
@Repository
public class LoyaltyDaoImpl extends DaoImpl implements LoyaltyDao {

    Logger logger = Logger.getLogger(LoyaltyDaoImpl.class);

    @Override
    public void save(Loyalty loyalty) {
        getHibernateTemplate().saveOrUpdate(loyalty);
    }

    @Override
    public void save(LoyaltyFile loyaltyFile) {
        getHibernateTemplate().saveOrUpdate(loyaltyFile);
    }

    public List<LoyaltyFile> getLoyaltyFiles(Date from, Date to) {
        Query query = getSession().createQuery("from LoyaltyFile where createDT >= :from and createDT <= :to");
        query.setParameter("from", from);
        query.setParameter("to", to);

        List l = query.list();
        return l;
    }

    public List<Loyalty> getUnprocessedLoyalty() {

        Query query = getSession().createQuery("from Loyalty where loyaltyFile is null order by createDt desc");
        List l = query.setMaxResults(1000).list();

        return l;
    }


    public boolean checkDuplicate(String tid, String completeTime, String tripNo) {

        //logger.debug("Before session");
        Query query = getSession().createQuery("from Loyalty where tid = :tid and completeTime = :completeTime and tripNo = :tripNo");
        query.setParameter("tid", tid);
        query.setParameter("completeTime", completeTime);
        query.setParameter("tripNo", tripNo);

        //logger.debug("Before query" );
        List l = query.setMaxResults(1).list();
        //logger.debug("After query");

        if ( l == null || l.isEmpty() )
            return false;

        return true;

    }
}
