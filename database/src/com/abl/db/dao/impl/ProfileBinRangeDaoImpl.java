package com.abl.db.dao.impl;

import com.abl.db.bean.ProfileBinValueBean;
import com.abl.db.dao.ProfileBinRangeDao;
import com.abl.db.model.ProfileBinRange;
import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

@Repository
public class ProfileBinRangeDaoImpl extends DaoImpl implements ProfileBinRangeDao {

    @Override
    public void saveAll(List<ProfileBinRange> object) {
        getHibernateTemplate().saveOrUpdateAll(object);
    }

    @Override
    public List<ProfileBinRange> getProfileBinRangesByProfile(long terminalProfileId) {
        Session session = this.getSession();
        Criteria criteria = session.createCriteria(ProfileBinRange.class, "profileBinRange");

        if (terminalProfileId > 0) {
            criteria.add(Restrictions.eq("profileBinRange.terminalProfile.id", terminalProfileId));
        }
        criteria.addOrder(Order.asc("profileBinRange.terminalProfile.id"));
        criteria.addOrder(Order.asc("profileBinRange.id"));

        List<ProfileBinRange> list = criteria.list();
        return list;

    }
    @Override
    public List<ProfileBinValueBean> getAllProfileBinRangesByProfile(long terminalProfileId){
        List<ProfileBinValueBean> profileBinValueBeansList = new ArrayList<ProfileBinValueBean>();
        Session session = this.getSession();
        List<Object[]> records =session.createSQLQuery("select  pbr.id as profile_bin_range_id,  pbr.terminal_profile_id as terminal_profile_id, pd.application_id as app_id, "+
        "pd.id as param_definition_id, pbr.pan_high as pan_high,  pbr.pan_low as pan_low, pbv.value as bin_value, "+
                "pd.name as bin_param_name from  tmtb_profile_bin_range pbr "+
                "join tmtb_profile_bin_value pbv on pbr.id= pbv.profile_bin_range_id "+
        "join tmtb_parameter_definition pd on pbv.parameter_definition_id  = pd.id where pbr.terminal_profile_id="+terminalProfileId + " order by pbr.id asc").list();


        if ((records != null) && (records.size() > 0)) {
            for(int i=0; i<records.size(); i++){
                ProfileBinValueBean pbvBean = new ProfileBinValueBean();
                Object[] binRangeDetails = records.get(i);
                pbvBean.setProfileBinRangeId(new Long(binRangeDetails[0].toString()));
                pbvBean.setTerminalProfileId(new Long(binRangeDetails[1].toString()));
                pbvBean.setAppId(new Long(binRangeDetails[2].toString()));
                pbvBean.setParamDefinitionId(new Long(binRangeDetails[3].toString()));
                pbvBean.setPanHigh((binRangeDetails[4].toString()));
                pbvBean.setPanLow((binRangeDetails[5].toString()));
                pbvBean.setBinValue((binRangeDetails[6].toString()));
                pbvBean.setBinParamName((binRangeDetails[7].toString()));
                profileBinValueBeansList.add(pbvBean);
            }
            return profileBinValueBeansList;
        } else {
            return null;
        }
    }
}
