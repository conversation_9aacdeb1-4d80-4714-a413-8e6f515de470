package com.abl.db.dao.impl;

import java.util.List;

import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.MatchMode;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Repository;

import com.abl.db.dao.VehicleDao;
import com.abl.db.dao.ViewVehicleGroupListDao;
import com.abl.db.model.Vehicle;
import com.abl.db.model.ViewVehicleGroupList;

@Repository
public class ViewVehicleGroupListDaoImpl extends DaoImpl implements ViewVehicleGroupListDao {

	 @SuppressWarnings("unchecked")
	    @Override
	    public List<ViewVehicleGroupList> searchVehicles(String vehicleNo, long groupId, String orderBy, boolean ascending, int offset, int maxResults) {
	    	Session session = this.getSession();
	        Criteria criteria = session.createCriteria(ViewVehicleGroupList.class, "vehicle");
	        if (vehicleNo != null && vehicleNo.length() > 0) {
	            criteria.add(Restrictions.ilike("vehicle.vehicleId", vehicleNo ,MatchMode.ANYWHERE));

	        }
	        
	        if (groupId > 0) {
	            criteria.add(Restrictions.eq("vehicle.groupId", groupId));
	
	        }

	        if (orderBy != null) {
	            if (orderBy.indexOf('.') < 0) {
	                orderBy = "vehicle.vehicleId";
	            }
	            if (ascending) {
	                criteria.addOrder(Order.asc(orderBy));
	            } else {
	                criteria.addOrder(Order.desc(orderBy));
	            }
	        }
	        if (offset > 0) {
	            criteria.setFirstResult(offset);
	        }
	        if (maxResults > 0) {
	            criteria.setMaxResults(maxResults);
	        }

	        List<ViewVehicleGroupList> list = criteria.list();
	        return list;
	    }
	    
	    @SuppressWarnings("unchecked")
	    @Override
	    public int countVehicles(String vehicleNo, long groupId) {
	        Session session = this.getSession();
	        Criteria criteria = session.createCriteria(ViewVehicleGroupList.class, "vehicle")
	                .setProjection(Projections.rowCount());

	        if (vehicleNo != null && vehicleNo.length() > 0) {
	            criteria.add(Restrictions.ilike("vehicle.vehicleId", vehicleNo ,MatchMode.ANYWHERE));

	        }

	        if (groupId > 0) {
	            criteria.add(Restrictions.eq("vehicle.groupId", groupId));
	
	        }
	        

	        List<Number> list = criteria.list();
	        if ((list != null) && (list.size() > 0)) {
	            return list.get(0).intValue();
	        } else {
	            return 0;
	        }

	    }
	    
}
