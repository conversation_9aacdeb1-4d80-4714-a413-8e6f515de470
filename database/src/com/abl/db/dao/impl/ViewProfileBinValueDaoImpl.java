package com.abl.db.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.abl.db.dao.ViewProfileBinValueDao;
import com.abl.db.model.ViewProfileBinValue;

@Repository
public class ViewProfileBinValueDaoImpl extends DaoImpl implements ViewProfileBinValueDao {
	
	@Override
	public void saveAll(List<ViewProfileBinValue> object) {
		getHibernateTemplate().saveOrUpdateAll(object);
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public ViewProfileBinValue getProfileBinValue(long binRangeId, long paramId) {
		List<ViewProfileBinValue> list = getHibernateTemplate().find("from ViewProfileBinValue profileBinValue " +
				" where profileBinValue.profileBinRangeId= ? and profileBinValue.parameterDefinitionId =?", binRangeId, paramId);
		
		if ((list != null)&&(list.size()>0)) {
			return list.get(0);
		} else {
			return null;
		}
	}

}
