package com.abl.db.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.abl.db.dao.VehicleTypeDao;
import com.abl.db.model.VehicleType;

@Repository
public class VehicleTypeDaoImpl extends DaoImpl implements VehicleTypeDao {

	public List<VehicleType> getVehicleTypes() {
		return getHibernateTemplate().find("from VehicleType vType order by vehicleType asc ");
	}

}
