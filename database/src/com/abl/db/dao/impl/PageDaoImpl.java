package com.abl.db.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.abl.db.dao.PageDao;
import com.abl.db.model.AdminUser;
import com.abl.db.model.Module;
import com.abl.db.model.Page;

@Repository
public class PageDaoImpl extends DaoImpl implements PageDao {

	@Override
	public Page getPage(String name) {
		return getHibernateTemplate().get(Page.class, name);
	}
	
	@Override
	@SuppressWarnings("unchecked")
	public Page getPageFetchModule(String name) {
		List<Page> list = getHibernateTemplate().find("from Page page left join fetch page.module where page.name=?", name);
		if ((list!=null)&&(list.size()>0)) {
			return list.get(0);
		} else {
			return null;
		}
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public Page getPageByUrl(String url) {
		List<Page> list = getHibernateTemplate().find("from Page page where page.url=?", url);
		if ((list != null)&&(list.size()>0)) {
			Page page = (Page)list.get(0);
			return page;
		} else {
			return null;
		}
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public List<Page> getPages() {
		return getHibernateTemplate().find("from Page page " +
				"join fetch page.module module " +
				"order by page.displayOrder");
	}
	
	@SuppressWarnings("unchecked")
	public List<Page> getAuthorizedPages(AdminUser adminUser) {
		if (adminUser == null) {
			return null;
		}
		if (adminUser.getDomain() == null) {
			return getHibernateTemplate().find("select distinct page " +
					"from AdminUser adminUser " +
					"join adminUser.accessProfiles accessProfile " +
					"join accessProfile.pages page " +
					"join fetch page.module module " +
					"where adminUser.id=? and page.domainFlag=?", adminUser.getId(), false);
		} else {
			return getHibernateTemplate().find("select distinct page " +
					"from AdminUser adminUser " +
					"join adminUser.accessProfiles accessProfile " +
					"join accessProfile.pages page " +
					"join fetch page.module module " +
					"where adminUser.id=?", adminUser.getId());
		}
	}
	
	@SuppressWarnings("unchecked")
	public List<Page> getAdminPages() {
		return getHibernateTemplate().find("select page " +
				"from Page page " +
				"join fetch page.module module " +
				"where page.adminFlag=? ", true);
	}
	
	@SuppressWarnings("unchecked")
	public List<Page> getModulePages(Module module) {
		return getHibernateTemplate().find("select page " +
				"from Page page " +
				"join page.module module " +
				"where module.name=? " +
				"order by page.displayOrder", module.getName());
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public List<Page> getVisiblePages() {
		return getHibernateTemplate().find("from Page page " +
				"join fetch page.module module where page.commonFlag =0 " +
				"order by page.name");
	}

}
