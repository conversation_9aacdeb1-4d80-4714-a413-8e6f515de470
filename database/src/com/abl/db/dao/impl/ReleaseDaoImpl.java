package com.abl.db.dao.impl;

import com.abl.db.dao.ApplicationDao;
import com.abl.db.dao.ModelDao;
import com.abl.db.dao.ReleaseDao;
import com.abl.db.model.AdminUser;
import com.abl.db.model.Application;
import com.abl.db.model.ApplicationBinRange;
import com.abl.db.model.ApplicationBinValue;
import com.abl.db.model.ApplicationBinValueId;
import com.abl.db.model.ApplicationFee;
import com.abl.db.model.ApplicationFeeValue;
import com.abl.db.model.ApplicationFeeValueId;
import com.abl.db.model.Model;
import com.abl.db.model.ParameterDefinition;
import com.abl.db.model.Release;
import com.abl.db.model.ReleaseFile;
import com.abl.utils.ByteUtils;
import com.abl.utils.StringUtils;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Repository;

import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.DataInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStreamReader;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Repository("ReleaseDao")
public class ReleaseDaoImpl extends DaoImpl implements ReleaseDao {
	private static final Logger logger = Logger.getLogger(ReleaseDaoImpl.class);

	int packetSize;

	public int getPacketSize() {
		return packetSize;
	}

	public void setPacketSize(int packetSize) {
		this.packetSize = packetSize;
	}

	@Autowired
	private ModelDao modelDao;

	@Autowired
	private ApplicationDao applicationDao;

	@Override
	public Release getRelease(long id) {
		return getHibernateTemplate().get(Release.class, id);
	}

	@Override
	public Release getReleaseByVersion(int version) {
		List<Release> list = getHibernateTemplate().find(
				"from Release release " + "where deleted=0 and release.version = ?", version);
		if ((list != null) && (list.size() > 0)) {
			return list.get(0);
		} else {
			return null;
		}
	}

	@Override
	public boolean hasModel(long releaseId, Model model) {
		Release release = getRelease(releaseId);
		if (release == null)
			return false;

		Set<Model> models = release.getModel();
		if (models == null || models.isEmpty() || model == null)
			return false;

		return models.contains(model);
	}

	@Override
	public Release getLatestRelease() {
		List<Release> list = getHibernateTemplate().find(
				"from Release release " + "where deleted=0 order by version desc");
		if ((list != null) && (list.size() > 0)) {
			return list.get(0);
		} else {
			return null;
		}
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public List loadReleasePackage(AdminUser adminUser, String filePath) throws Exception {
		List list = new ArrayList();
		File directory = new File(filePath);
		File files[] = directory.listFiles();

		Arrays.sort(files, new Comparator() {
			public int compare(final Object o1, final Object o2) {
				String s1 = ((File) o1).getName().toLowerCase();
				String s2 = ((File) o2).getName().toLowerCase();
				final int s1Dot = s1.lastIndexOf('.');
				final int s2Dot = s2.lastIndexOf('.');
				//
				if ((s1Dot == -1) == (s2Dot == -1)) { // both or neither
					s1 = s1.substring(s1Dot + 1);
					s2 = s2.substring(s2Dot + 1);
					return s1.compareTo(s2);
				} else if (s1Dot == -1) { // only s2 has an extension, so s1 goes first
					return -1;
				} else { // only s1 has an extension, so s1 goes second
					return 1;
				}
			}
		});

		Application appForBinary = null;
		ReleaseFile releaseFile = null;
		Release release = null;
		List<ReleaseFile> releaseFileList = new ArrayList<ReleaseFile>();
		List<Application> applicationList = new ArrayList<Application>();
		List<ParameterDefinition> paramDefList = new ArrayList<ParameterDefinition>();
		List<ParameterDefinition> subParamDefList = null;
		List<ApplicationBinRange> appBinRangeList = new ArrayList<ApplicationBinRange>();
		List<ApplicationBinValue> appBinValueList = new ArrayList<ApplicationBinValue>();
		List<ApplicationFee> appFeeList = new ArrayList<ApplicationFee>();
		List<ApplicationFeeValue> appFeeValueList = new ArrayList<ApplicationFeeValue>();
		List<String> errorList = new ArrayList<String>();
		int binaryFileSize = 0;
		for (File f : files) {
			int pos = f.getName().lastIndexOf('.');
			String ext = f.getName().substring(pos + 1);
			if (ext.equalsIgnoreCase("zip")) {
				binaryFileSize = (int) f.length();
			}
		}
		// TODO:: must have 5 definition files and 1 release file - all .txt
		for (File f : files) {
			FileInputStream fstream = null;
			try {
				fstream = new FileInputStream(f);
			} catch (FileNotFoundException e) {
				e.printStackTrace();
			}
			DataInputStream dis = new DataInputStream(fstream);
			BufferedReader reader = new BufferedReader(new InputStreamReader(dis));
			String line;
			logger.debug("file name to process:" + f.getName());
			if (f.getName().equalsIgnoreCase("release.txt")) {
				logger.debug("processing release file..");

				int lineNo = 0;
				String fileType;
				String releaseVersionStr;
				String minReleaseVersion;
				String modelString;
				String description = null;

				while ((line = reader.readLine()) != null) {
					line = line.trim();
					if (line.length() == 0) {
						break;
					}
					lineNo++;
					if (lineNo == 1) {
						if (!line.equalsIgnoreCase("release")) {
							errorList.add("Invalid key word for release file");
							break;
						}
						release = new Release();

					} else if (lineNo == 2) {

						releaseVersionStr = line;
						if (releaseVersionStr.length() < 6) {
							errorList.add("Invalid release version: " + releaseVersionStr);
							break;
						}

						Release releaseVer = getLatestRelease();
						int releaseVersion = Integer.parseInt(line);
						if (releaseVer != null) {
							if (releaseVersion <= releaseVer.getVersion()) {
								errorList.add("Invalid release version. Current version:"
										+ releaseVer.getPadVersion() + ", version uploaded:" + releaseVersionStr);
								break;
							}
						}

						releaseVersion = Integer.parseInt(line);
						release.setVersion(releaseVersion);

					} else if (lineNo == 3) {
						minReleaseVersion = line;
						if (minReleaseVersion.length() < 6) {
							errorList.add("Invalid min release version: " + minReleaseVersion);
							break;
						}

						release.setMinVersion(Integer.parseInt(minReleaseVersion));

					} else if (lineNo == 4) {
						modelString = line;
						logger.debug("model:" + modelString);
						String[] modelList = modelString.split("\\,");
						Model model = null;
						Set<Model> models = null;
						if (modelList != null && modelList.length > 0) {
							models = new HashSet<Model>();
							for (String modelName : modelList) {
								model = modelDao.getModelByName(modelName);
								if (model != null) {
									models.add(model);
								} else {
									errorList.add("Model: " + modelName + " not found!");
									break;
								}
							}

						}
						logger.debug("set model to release..");
						release.setModel(models);
					} else {
						if (description != null && description.length() > 0) {
							description = description.concat(line);
						} else {
							description = line;
						}

					}
				}

				release.setDescription(description);
				release.setCreatedBy(adminUser.getLogin());
				release.setFileSize(binaryFileSize);
				release.setLoadDateTime(new Date());
				logger.debug("release version:" + release.getVersion());
				logger.debug("binaryFileSize:" + binaryFileSize);
				logger.debug("description:" + release.getDescription());

			}

		}
		for (File f : files) {
			subParamDefList = new ArrayList<ParameterDefinition>();
			int pos = f.getName().lastIndexOf('.');
			String ext = f.getName().substring(pos + 1);

			if (ext.equalsIgnoreCase("txt") && !(f.getName().equalsIgnoreCase("release.txt"))) {
				FileInputStream fstream = null;
				try {
					fstream = new FileInputStream(f);
				} catch (FileNotFoundException e) {
					e.printStackTrace();
				}
				DataInputStream dis = new DataInputStream(fstream);
				BufferedReader reader = new BufferedReader(new InputStreamReader(dis));
				String line;
				// for param definition file
				int lineNo = 0;
				String applicationName = null;
				int applicationVersion = 0;
				String applicationDesc;
				int noOfParam = 0;
				int noOfBin = 0;
				int appInfoLineCnt = 4;
				String panHighAdditionalInfo = null;
				short panHighType = 0;
				String panLowAdditionalInfo = null;
				short panLowType = 0;
				int noOfBinDataFields = 0;
				int noOfFeeDataFields = 0;
				String descAdditionalInfo = null;
				short finalFeeType = 0;
				int noOfFee = 0;
				int noOfBinParam = 0;
				boolean hasFeeParam = false;
				int noOfFeeParam = 0;

				List<Application> appList = new ArrayList<Application>();

				Application app = null;
				ApplicationBinRange abr = null;
				ApplicationFee af = null;
				int cntBin = 0;
				int cntFee = 0;
				logger.debug("proccesing " + f.getName());
				try {
					while ((line = reader.readLine()) != null) {
						line = line.trim();
						if (line.length() == 0) {
							break;
						}
						lineNo++;
						String[] dataFields = line.split("\\|");
						// logger.debug("@lineNo:" + lineNo + ", " + line);
						// logger.debug("#errorList size:"+errorList.size());
						if (lineNo == 1) {
							applicationName = line;
							if (applicationName.length() > 10) { // column length for name is
																										// 10
								logger.debug("application name("+applicationName+") is >10");
								errorList.add("The application name(" + applicationName
										+ ") is too long. Maximum length is 10 (" + f.getName() + ")");
								break;
							}

						} else if (lineNo == 2) {
							applicationVersion = Integer.parseInt(line);

						} else if (lineNo == 3) {
							applicationDesc = line;
							logger.debug("application:" + applicationDesc + ", version:" + applicationVersion);
							appList = applicationDao.searchApplicationByNameVersion(applicationName,
									applicationVersion);

							if (appList == null || appList.size() == 0) {
								logger.debug("is a new application");
								app = new Application();
								app.setName(applicationName);
								app.setVersion(applicationVersion);
								app.setDescription(applicationDesc);

							} else {
								app = appList.get(0);
							}

						} else if (lineNo == 4) {
							noOfParam = Integer.parseInt(line);

						}
						// process paramDefinition File
						if (appList == null || appList.size() == 0) {
							if (lineNo > 4 && lineNo <= noOfParam + 4) {
								if ((dataFields != null) && (dataFields.length == 4)) {
									ParameterDefinition paramDef = new ParameterDefinition();
									String paramName = dataFields[0];
									String paramDesc = dataFields[1];
									short type;

									String typeStr = dataFields[2];
									String realTypeStr = typeStr;
									int index = typeStr.indexOf("(");

									if (index > 0) {
										realTypeStr = typeStr.substring(0, typeStr.lastIndexOf('(')).trim();
									}

									if (realTypeStr.equalsIgnoreCase("number")) {
										type = 0;

									} else if (realTypeStr.equalsIgnoreCase("boolean")) {
										type = 1;
									} else if (realTypeStr.equalsIgnoreCase("option")) {
										type = 2;
									} else if (realTypeStr.equalsIgnoreCase("string")) {
										type = 3;
									} else if (realTypeStr.equalsIgnoreCase("date")) {
										type = 4;
									} else if (realTypeStr.equalsIgnoreCase("time")) {
										type = 5;
									} else if (realTypeStr.equalsIgnoreCase("datetime")) {
										type = 6;
									} else {
										type = 7;
									}

									if (type == 7) {
										errorList.add("Invalid parameter type:" + realTypeStr + " (" + f.getName()
												+ ")");
										break;
									}

									String defaultValue = formatParameterValue(type, typeStr, dataFields[3]);
									// logger.debug("param name:" + paramName + ", " + paramDesc);
									paramDef.setName(paramName);
									paramDef.setDescription(paramDesc);
									paramDef.setType(type);
									paramDef.setLevel((short) 0);
									paramDef.setDefaultValue(defaultValue);
									paramDef.setAdditionalInfo(typeStr);
									paramDef.setApplication(app);
									// logger.debug("adding paramDef: " + paramName + "app:" +
									// app.getName());
									paramDefList.add(paramDef);
									subParamDefList.add(paramDef);
									// logger.debug("paramDefList.size()" + paramDefList.size());

								} else {
									errorList.add("Invalid record! File: " + f.getName() + ", line #:" + lineNo);
								}

							} else if ((lineNo - 1) == (noOfParam + appInfoLineCnt)) {
								noOfBin = Integer.parseInt(line);
								logger.debug("noOfBin:" + noOfBin);

							} else if (lineNo == (noOfParam + appInfoLineCnt + 2)) { // 2
																																				// lines
																																				// of
																																				// noofBin
																																				// and
																																				// BinDesc
																																				// logger.debug("lineNo:"
																																				// +
																																				// lineNo
																																				// +
																																				// ", line:"
																																				// +
																																				// line);
								logger.debug("noOfBin:" + noOfBin);
								if (noOfBin > 0) { // only check for bin if noOfBin>0
									if ((dataFields != null)) {
										noOfBinDataFields = dataFields.length;
										boolean isPanHighLow = true;
										int cnt = 0;
										String paramName = null;
										String paramDesc = null;
										String realBinTypeStr;
										String binTypeStr = null;
										short binType = 0;
										ParameterDefinition paramDef = null;
										boolean isFeeDesc = true;
										logger.debug("dataFields.length =" + dataFields.length);
										for (int i = 0; i < (dataFields.length); i++) {
											if (i > 5) {
												isPanHighLow = false;
											}
											cnt++;
											if (cnt == 1) {
												if (!isPanHighLow) {
													paramDef = new ParameterDefinition();
													paramName = dataFields[i];
													paramDef.setName(paramName);
													// logger.debug("$$ paramName:" + paramName);
												}

											} else if (cnt == 2) {
												if (!isPanHighLow) {
													paramDesc = dataFields[i];
													paramDef.setDescription(paramDesc);
													// logger.debug("$$ paramDesc:" + paramDesc);
												}
											} else if (cnt == 3) { // the 3rd records is the
												binTypeStr = dataFields[i];
												realBinTypeStr = binTypeStr;
												int index = binTypeStr.indexOf("(");
												if (index > 0) {
													realBinTypeStr = binTypeStr.substring(0, binTypeStr.lastIndexOf('('))
															.trim();
												}

												if (realBinTypeStr.equalsIgnoreCase("number")) {
													binType = 0;

												} else if (realBinTypeStr.equalsIgnoreCase("boolean")) {
													binType = 1;
												} else if (realBinTypeStr.equalsIgnoreCase("option")) {
													binType = 2;
												} else if (realBinTypeStr.equalsIgnoreCase("string")) {
													binType = 3;
												} else if (realBinTypeStr.equalsIgnoreCase("date")) {
													binType = 4;
												} else if (realBinTypeStr.equalsIgnoreCase("time")) {
													binType = 5;
												} else if (realBinTypeStr.equalsIgnoreCase("datetime")) {
													binType = 6;
												} else {
													binType = 7;
												}

												if (binType == 7) {
													errorList.add("Invalid bin type:" + realBinTypeStr + " (" + f.getName()
															+ ")");
													break;
												}
												cnt = 0;
												// logger.debug("in cnt 3, isPanHighLow is " +
												// isPanHighLow);
												if (!isPanHighLow) {
													paramDef.setType(binType);
													paramDef.setAdditionalInfo(binTypeStr);
													paramDef.setApplication(app);
													paramDef.setLevel((short) 1);
													paramDefList.add(paramDef);
													subParamDefList.add(paramDef);
												} else {
													if (i == 2) {
														panLowAdditionalInfo = binTypeStr;
														panLowType = binType;

													} else {
														panHighAdditionalInfo = binTypeStr;
														panHighType = binType;
													}

												}

											}

										}

									}
								}

							} else if (lineNo > (noOfParam + 2 + appInfoLineCnt)
									&& lineNo <= (noOfParam + noOfBin + appInfoLineCnt + 2)) {
								if (noOfBin > 0) { // only if have bin
								// logger.debug("have bin, lineNO = " + lineNo);
									if (lineNo == (noOfParam + appInfoLineCnt + 2 + 1)) { 
										cntBin = 0;

									} else {
										cntBin++;
									}
									String panLow = dataFields[0];
									String panHigh = dataFields[1];
									 logger.debug("panHigh:" + panHigh+", panLow:"+panLow);
									// logger.debug("cntBin =" + cntBin);
									// logger.debug("applicationBinRangelist.size() = " +
									// appBinRangeList.size());
									errorList = validatePanLowHigh(panLow, panHigh, panLowAdditionalInfo,
											panHighAdditionalInfo, errorList);
									abr = new ApplicationBinRange();
									abr.setPanHigh(panHigh);
									abr.setPanLow(panLow);
									abr.setApplication(app);
									abr.setPanHighType(panHighType);
									abr.setPanHighAdditionalInfo(panHighAdditionalInfo);
									abr.setPanLowAdditionalInfo(panLowAdditionalInfo);
									abr.setPanLowType(panLowType);

									noOfBinParam = subParamDefList.size() - noOfParam; 
									 logger.debug("noOfBinParam =" + noOfBinParam);
									for (int bin = 0; bin < noOfBinParam; bin++) {

										ApplicationBinValueId appValue = new ApplicationBinValueId();
										appValue.setApplicationBinRange(abr);
										appValue.setParameterDefinition(subParamDefList.get(noOfParam + bin));
									// the 1st 2 datafields is pan high and low, thus start with 3rd column
										int defaultBin = 2; 
										
										short paramType = subParamDefList.get(noOfParam + bin).getType();
										String additionalInfo = subParamDefList.get(noOfParam + bin)
												.getAdditionalInfo();
										
										String defaultValue = formatParameterValue(paramType, additionalInfo,
												dataFields[defaultBin + bin]);
										// logger.debug("default value=" + defaultValue);

										ApplicationBinValue abv = new ApplicationBinValue();
										abv.setValue(defaultValue);
										abv.setPk(appValue);

										Set<ApplicationBinValue> appBinValue = new HashSet<ApplicationBinValue>();
										appBinValue.add(abv);

										abr.setApplicationBinValue(appBinValue);
										appBinRangeList.add(abr);
										appBinValueList.addAll(appBinValue);
									}
								}

							} else if (lineNo == (noOfBin + 1 + noOfParam + appInfoLineCnt + 2)) {
								noOfFee = Integer.parseInt(line);
								logger.debug("no of fee:"+noOfFee);
							} else if (lineNo == (noOfBin + 2 + noOfParam + appInfoLineCnt + 2)) { 
								hasFeeParam = true;
								logger.debug("------------Fee ----------------");
								logger.debug("lineNo:" + lineNo + ", line:" + line);
								if ((dataFields != null)) {
									noOfFeeDataFields = dataFields.length;
									boolean isFeeDesc = true;
									int cnt = 0;
									String paramName = null;
									String paramDesc = null;
									String realFeeTypeStr;
									String feeTypeStr = null;
									short feeType = 0;
									ParameterDefinition paramDef = null;
									// logger.debug("dataFields.length =" + dataFields.length);
									for (int i = 0; i < (dataFields.length); i++) {

										// logger.debug("i=" + i + "," + dataFields[i]);
										if (i > 2) {
											isFeeDesc = false;
										}
										cnt++;
										if (cnt == 1) {
											if (!isFeeDesc) {
												paramDef = new ParameterDefinition();
												paramName = dataFields[i];
												paramDef.setName(paramName);
												// logger.debug("param name for fee:" + paramName);
											}

										} else if (cnt == 2) {
											if (!isFeeDesc) {
												paramDesc = dataFields[i];
												paramDef.setDescription(paramDesc);
												// logger.debug("param desc for fee:" + paramDesc);
											}
										} else if (cnt == 3) { // the 3rd records is the
											feeTypeStr = dataFields[i];
											realFeeTypeStr = feeTypeStr;
											int index = feeTypeStr.indexOf("(");
											if (index > 0) {
												realFeeTypeStr = feeTypeStr.substring(0, feeTypeStr.lastIndexOf('('))
														.trim();
											}

											if (realFeeTypeStr.equalsIgnoreCase("number")) {
												feeType = 0;

											} else if (realFeeTypeStr.equalsIgnoreCase("boolean")) {
												feeType = 1;
											} else if (realFeeTypeStr.equalsIgnoreCase("option")) {
												feeType = 2;
											} else if (realFeeTypeStr.equalsIgnoreCase("string")) {
												feeType = 3;
											} else if (realFeeTypeStr.equalsIgnoreCase("date")) {
												feeType = 4;
											} else if (realFeeTypeStr.equalsIgnoreCase("time")) {
												feeType = 5;
											} else if (realFeeTypeStr.equalsIgnoreCase("datetime")) {
												feeType = 6;
											} else {
												feeType = 7;
											}

											if (feeType == 7) {
												errorList.add("Invalid fee type:" + realFeeTypeStr + " (" + f.getName()
														+ ")");
												break;
											}
											cnt = 0;
											// logger.debug("isFeeDesc = " + isFeeDesc);
											if (!isFeeDesc) {
												paramDef.setType(feeType);
												paramDef.setAdditionalInfo(feeTypeStr);
												paramDef.setApplication(app);
												paramDef.setLevel((short) 2);
												paramDefList.add(paramDef);
												subParamDefList.add(paramDef);
											} else {
												if (i == 2) {
													descAdditionalInfo = feeTypeStr;
													finalFeeType = feeType;

												}

											}

										}

									}
									/** no need to set a empty default value.
									logger.debug("---no default fee value ---");
									if (noOfFee == 0 && hasFeeParam) { // have default fee param,
																											// set null value to
																											// default
																											// fee value
										logger.debug("have default fee param, but no default fee value");
										String desc = "";
										logger.debug("finalFeeType:" + finalFeeType);
										logger.debug("finalFeeType:" + descAdditionalInfo);
										af = new ApplicationFee();

										af.setDesc(desc);

										af.setApplication(app);
										af.setDescType(finalFeeType);
										af.setDescAdditionalInfo(descAdditionalInfo);

										noOfFeeParam = subParamDefList.size() - noOfParam - noOfBinParam; 
									
										 logger.debug("noOfFeeParam =" + noOfFeeParam);

										for (int fee = 0; fee < noOfFeeParam; fee++) {

											ApplicationFeeValueId appValue = new ApplicationFeeValueId();
											appValue.setApplicationFee(af);
											appValue.setParameterDefinition(subParamDefList.get(noOfBinParam + noOfParam
													+ fee));
											// the 1st datafield is fee descriptions
											int defaultFee = 1; 

											short paramType = subParamDefList.get(noOfParam + noOfBinParam + fee)
													.getType();
											String additionalInfo = subParamDefList.get(noOfBinParam + noOfParam + fee)
													.getAdditionalInfo();
									
											ApplicationFeeValue afv = new ApplicationFeeValue();
											afv.setValue(null);
											afv.setPk(appValue);

											Set<ApplicationFeeValue> appFeeValue = new HashSet<ApplicationFeeValue>();
											appFeeValue.add(afv);

											af.setApplicationFeeValue(appFeeValue);
											appFeeList.add(af);
											appFeeValueList.addAll(appFeeValue);
										}

									}
									**/
								}

							} else if (lineNo > (noOfBin + 2 + noOfParam + 2 + appInfoLineCnt)
									&& lineNo <= (noOfFee + noOfParam + 2 + noOfBin + appInfoLineCnt + 2)) {
								// logger.debug("--- checking fee--- line no =" + lineNo);
								if (lineNo == (noOfFee + 2 + noOfParam + noOfBin + appInfoLineCnt + 2 + 1)) { // 2
																																															// lines
																																															// of
																																															// records
																																															// are
																																															// the
																																															// noOfFee
																																															// and
																																															// feeDesc
									cntFee = 0;

								} else {
									cntFee++;
								}
								String desc = dataFields[0];

								af = new ApplicationFee();

								af.setDesc(desc);

								af.setApplication(app);
								af.setDescType(finalFeeType);
								af.setDescAdditionalInfo(descAdditionalInfo);

								noOfFeeParam = subParamDefList.size() - noOfParam - noOfBinParam; 
																													
								 logger.debug("noOfFeeParam =" + noOfFeeParam);
								for (int fee = 0; fee < noOfFeeParam; fee++) {

									ApplicationFeeValueId appValue = new ApplicationFeeValueId();
									appValue.setApplicationFee(af);
									appValue.setParameterDefinition(subParamDefList.get(noOfBinParam + noOfParam
											+ fee));
									int defaultFee = 1; // the 1st datafield is fee descriptions
									// thus start with
									// 2nd column

									// logger.debug("##no of param =" + subParamDefList.size());

									short paramType = subParamDefList.get(noOfParam + noOfBinParam + fee).getType();
									String additionalInfo = subParamDefList.get(noOfBinParam + noOfParam + fee)
											.getAdditionalInfo();
									// logger.debug("paramType:" + paramType);
									// logger.debug("additionalInfo:" + additionalInfo);
									// logger.debug("value:" + dataFields[defaultFee + fee]);

									String defaultValue = formatParameterValue(paramType, additionalInfo,
											dataFields[defaultFee + fee]);
									// logger.debug("default value=" + defaultValue);

									ApplicationFeeValue afv = new ApplicationFeeValue();
									afv.setValue(defaultValue);
									afv.setPk(appValue);

									Set<ApplicationFeeValue> appFeeValue = new HashSet<ApplicationFeeValue>();
									appFeeValue.add(afv);

									af.setApplicationFeeValue(appFeeValue);
									appFeeList.add(af);
									appFeeValueList.addAll(appFeeValue);
								}

							}
						}
					}

					applicationList.add(app);
				} catch (Exception e) {
					logger.debug("error:" + e);
				}
			} else if (ext.equalsIgnoreCase("zip")) {
				if (release != null) {
					logger.debug("process ZIP.." + release.getDescription());
					ByteArrayOutputStream chunk;
					FileInputStream fis;

					int fileSize = (int) f.length();
					int nChunks = 0, read = 0, readLength = packetSize;
					byte[] byteChunk;

					int record = 0;
					logger.debug("file size:" + fileSize);

					try {

						fis = new FileInputStream(f);
						while (fileSize > 0) {
							if (fileSize <= packetSize) {
								readLength = fileSize;
							}

							record++;
							byteChunk = new byte[readLength];
							read = fis.read(byteChunk, 0, readLength);
							fileSize -= read;
							assert (read == byteChunk.length);
							nChunks++;
							chunk = new ByteArrayOutputStream();
							chunk.write(byteChunk);

							releaseFile = new ReleaseFile();
							releaseFile.setRelease(release);
							releaseFile.setRecordNo(record);
							releaseFile.setData(ByteUtils.bytesToHex(byteChunk));
							releaseFileList.add(releaseFile);

							chunk.flush();
							chunk.close();
							byteChunk = null;
							chunk = null;

						}
						fis.close();
						fis = null;

					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			}
		}

		if (errorList == null || errorList.size() == 0) {
			errorList = validateDefaultValue(paramDefList, appBinValueList, appFeeValueList	);

		}

		Set<Application> applications = null;

		if (applicationList != null) {
			applications = new HashSet<Application>();
			for (Application app1 : applicationList) {
				applications.add(app1);

			}
			release.setApplication(applications);

		}
		list.add(release);
		list.add(applicationList);
		list.add(paramDefList);
		list.add(appBinRangeList);
		list.add(appBinValueList);
		list.add(errorList);
		list.add(releaseFileList);
		list.add(appFeeList);
		list.add(appFeeValueList);

		return list;
	}

	private List<String> validateDefaultValue(List<ParameterDefinition> paramDefList,
			List<ApplicationBinValue> appBinValueList, List<ApplicationFeeValue> appFeeValueList) {
		List<String> error = new ArrayList<String>();
		double minValue = 0;
		int minLength = 0;
		int maxLength = 0;
		String initialMaxValue;
		int decimal = 0;
		int startDecimal = 0;
		double maxValue = 0;
		logger.debug("validate default value....");
		for (ParameterDefinition param : paramDefList) {
			String dataType[] = null;
			String values = param.getAdditionalInfo();
			String defaultValue = param.getDefaultValue();
			String paramName = param.getName();
			logger.debug("param name:" + paramName + ", level:" + param.getLevel());
			if (param.getLevel() == 0) {
				if (param.getType() != 4 && param.getType() != 5 && param.getType() != 6) {
					String listValue = values.substring(values.indexOf("(") + 1, values.indexOf(")"));
					dataType = listValue.split(",");
				}

				if (((defaultValue == null) || (defaultValue.length() == 0))) { // only
																																				// check
																																				// for
																																				// parameter
																																				// level
					error.add("Default value for parameter: " + paramName + " cannot be empty");
				} else {
					if (param.getType() == 0) {
						minValue = Double.parseDouble(dataType[1]);
						initialMaxValue = dataType[2];
						decimal = Integer.parseInt(dataType[3]);
						startDecimal = initialMaxValue.length() - decimal;
						maxValue = Double.parseDouble(initialMaxValue.substring(0, startDecimal) + "."
								+ initialMaxValue.substring(startDecimal));

						if (!defaultValue.matches("-?\\d+(.\\d+)?")) {
							error.add("Invalid default value for parameter: " + paramName);
						} else {
							if (Double.parseDouble(defaultValue) >= minValue
									&& Double.parseDouble(defaultValue) <= maxValue) {
								if (decimal > 0) {
									int numberOfDecimal = getNumberOfDecimals(defaultValue);
									if (numberOfDecimal > decimal) {
										error.add("Invalid decimal points for parameter: " + paramName);

									}
								}

							} else {
								error.add("Invalid default value for parameter: " + paramName);
							}
						}

					} else if (param.getType() == 3) {
						int valueLength = defaultValue.length();
						minLength = Integer.parseInt(dataType[0]);
						maxLength = Integer.parseInt(dataType[1]);
						if (valueLength < minLength || valueLength > maxLength) {
							error.add("Invalid default value for parameter: " + paramName);
						}

					} else if (param.getType() == 4) {

						if (!isValidDate(defaultValue, "ddMMyy")) {
							error.add("Invalid default value for parameter: " + paramName);

						}

					} else if (param.getType() == 5) {
						if (!isValidTime(defaultValue)) {
							error.add("Invalid default value for parameter: " + paramName);
						}

					} else if (param.getType() == 6) {
						if (defaultValue.trim().length() == 12) { // ddMMyyHHmmss
							if (!isValidDate(defaultValue.substring(0, 6), "ddMMyy")) {
								error.add("Invalid default value for parameter: " + paramName);
							}
							if (!isValidTime(defaultValue.substring(6))) {
								error.add("Invalid default value for parameter: " + paramName);

							}
						} else {
							error.add("Invalid default value for parameter: " + paramName);
						}

					}
				}
			}

		}

		for (ApplicationBinValue appBinValue : appBinValueList) {
			String dataType[] = null;
			ParameterDefinition param = appBinValue.getPk().getParameterDefinition();
			String defaultValue = appBinValue.getValue();
			String values = param.getAdditionalInfo();
			String paramName = param.getName();
			if (param.getType() != 4 && param.getType() != 5 && param.getType() != 6) {
				String listValue = values.substring(values.indexOf("(") + 1, values.indexOf(")"));
				dataType = listValue.split(",");
			}
			if ((defaultValue == null) || (defaultValue.length() == 0)) {
				error.add("Default value for parameter: " + paramName + " cannot be empty");
			} else {
				if (param.getType() == 0) {
					minValue = Double.parseDouble(dataType[1]);
					initialMaxValue = dataType[2];
					decimal = Integer.parseInt(dataType[3]);
					startDecimal = initialMaxValue.length() - decimal;
					maxValue = Double.parseDouble(initialMaxValue.substring(0, startDecimal) + "."
							+ initialMaxValue.substring(startDecimal));

					if (!defaultValue.matches("-?\\d+(.\\d+)?")) {
						error.add("Invalid default value for parameter: " + paramName);
					} else {
						if (Double.parseDouble(defaultValue) >= minValue
								&& Double.parseDouble(defaultValue) <= maxValue) {
							if (decimal > 0) {
								int numberOfDecimal = getNumberOfDecimals(defaultValue);
								if (numberOfDecimal > decimal) {
									error.add("Invalid decimal points for parameter: " + paramName);

								}
							}

						} else {
							error.add("Invalid default value for parameter: " + paramName);
						}
					}

				} else if (param.getType() == 3) {
					int valueLength = defaultValue.length();
					minLength = Integer.parseInt(dataType[0]);
					maxLength = Integer.parseInt(dataType[1]);
					if (valueLength < minLength || valueLength > maxLength) {
						error.add("Invalid default value for parameter: " + paramName);
					}

				} else if (param.getType() == 4) {

					if (!isValidDate(defaultValue, "ddMMyy")) {
						error.add("Invalid default value for parameter: " + paramName);

					}

				} else if (param.getType() == 5) {
					if (!isValidTime(defaultValue)) {
						error.add("Invalid default value for parameter: " + paramName);
					}

				} else if (param.getType() == 6) {
					if (defaultValue.trim().length() == 12) { // ddMMyyHHmmss
						if (!isValidDate(defaultValue.substring(0, 6), "ddMMyy")) {
							error.add("Invalid default value for parameter: " + paramName);
						}
						if (!isValidTime(defaultValue.substring(6))) {
							error.add("Invalid default value for parameter: " + paramName);

						}
					} else {
						error.add("Invalid default value for parameter: " + paramName);
					}

				}
			}

		}
		if (appFeeValueList != null) {
			logger.debug("validate default value....");
			logger.debug("appFeeValueList size =" + appFeeValueList.size());
		}
		for (ApplicationFeeValue appFeeValue : appFeeValueList) {
			String dataType[] = null;
			ParameterDefinition param = appFeeValue.getPk().getParameterDefinition();
			String defaultValue = appFeeValue.getValue();
			String values = param.getAdditionalInfo();
			String paramName = param.getName();
			logger.debug("paramName=" + paramName);
			if (param.getType() != 4 && param.getType() != 5 && param.getType() != 6) {
				String listValue = values.substring(values.indexOf("(") + 1, values.indexOf(")"));
				dataType = listValue.split(",");
			}
			if ((defaultValue == null) || (defaultValue.length() == 0)) {
				// error.add("Default value for parameter: " + paramName +
				// " cannot be empty"); //default value for fee can be empty
			} else {
				if (param.getType() == 0) {
					minValue = Double.parseDouble(dataType[1]);
					initialMaxValue = dataType[2];
					decimal = Integer.parseInt(dataType[3]);
					startDecimal = initialMaxValue.length() - decimal;
					maxValue = Double.parseDouble(initialMaxValue.substring(0, startDecimal) + "."
							+ initialMaxValue.substring(startDecimal));

					if (!defaultValue.matches("-?\\d+(.\\d+)?")) {
						error.add("Invalid default value for parameter: " + paramName);
					} else {
						if (Double.parseDouble(defaultValue) >= minValue
								&& Double.parseDouble(defaultValue) <= maxValue) {
							if (decimal > 0) {
								int numberOfDecimal = getNumberOfDecimals(defaultValue);
								if (numberOfDecimal > decimal) {
									error.add("Invalid decimal points for parameter: " + paramName);

								}
							}

						} else {
							error.add("Invalid default value for parameter: " + paramName);
						}
					}

				} else if (param.getType() == 3) {
					int valueLength = defaultValue.length();
					minLength = Integer.parseInt(dataType[0]);
					maxLength = Integer.parseInt(dataType[1]);
					if (valueLength < minLength || valueLength > maxLength) {
						error.add("Invalid default value for parameter: " + paramName);
					}

				} else if (param.getType() == 4) {

					if (!isValidDate(defaultValue, "ddMMyy")) {
						error.add("Invalid default value for parameter: " + paramName);

					}

				} else if (param.getType() == 5) {
					if (!isValidTime(defaultValue)) {
						error.add("Invalid default value for parameter: " + paramName);
					}

				} else if (param.getType() == 6) {
					if (defaultValue != null && defaultValue.trim().length() > 0) {
						if (defaultValue.trim().length() == 12) { // ddMMyyHHmmss
							if (!isValidDate(defaultValue.substring(0, 6), "ddMMyy")) {
								error.add("Invalid default value for parameter: " + paramName);
							}
							if (!isValidTime(defaultValue.substring(6))) {
								error.add("Invalid default value for parameter: " + paramName);

							}
						} else {
							error.add("Invalid default value for parameter: " + paramName);
						}
					}

				}
			}

		}

		return error;
	}

	private boolean isValidDate(String date, String pattern) {
		SimpleDateFormat sdf = new SimpleDateFormat(pattern);

		Date checkDate = null;

		try {

			checkDate = sdf.parse(date);

		} catch (ParseException e) {
			return false;
		}

		if (!sdf.format(checkDate).equals(date)) {
			return false;

		}

		return true;

	}

	private boolean isValidTime(String time) {

		if (time.matches("^([0-1][0-9]|2[0-3])([0-5][0-9])([0-5][0-9])$")) {
			return true;
		} else {
			return false;
		}

	}

	@SuppressWarnings("unused")
	private String formatParameterValue(short paramType, String additionalInfo, String value) {
		String formattedValue = null;
		String[] infos = null;
		int decimal = 0;
		switch (paramType) {
		case ParameterDefinition.NUMBER:
			infos = additionalInfo
					.substring(additionalInfo.indexOf("(") + 1, additionalInfo.indexOf(")")).split(",");
			decimal = Integer.parseInt(infos[3]);
			if (decimal > 0) {
				int numberOfDecimal = getNumberOfDecimals(value);
				if (numberOfDecimal == 0) {
					int length = value.length();
					int padIndex = length - decimal;
					logger.debug("length =" + length);
					logger.debug("padIndex =" + padIndex);

					if (padIndex > 0) {
						logger.debug("value 1=" + value.substring(0, padIndex));
						logger.debug("value 2=" + value.substring(padIndex));
						value = value.substring(0, padIndex).concat(".").concat(value.substring(padIndex));
					} else {
						value = StringUtils.padRight(value, '.', 1);

						value = StringUtils.padRight(value, '0', decimal - numberOfDecimal);

					}

					logger.debug("value after put decimal:" + value);
					formattedValue = value;

				}

				// formattedValue= StringUtils.padRight(value, '0',
				// decimal-numberOfDecimal);

			} else {
				formattedValue = value;
			}

			break;
		case ParameterDefinition.OPTION:
			formattedValue = value;
			break;
		case ParameterDefinition.STRING:
			formattedValue = value;
			break;
		case ParameterDefinition.DATE:
			formattedValue = value;
			break;
		case ParameterDefinition.TIME:
			formattedValue = value;
			break;
		case ParameterDefinition.DATETIME:
			formattedValue = value;
			break;
		}

		return formattedValue;

	}

	private int getNumberOfDecimals(String number) {
		boolean isDecimal = Double.parseDouble(number) % 1 == 0;

		if (!isDecimal) {
			int stringLength = number.length();
			int numberOfDecimals = 0;
			char theChar = 'e';
			int counter;

			for (counter = 1; theChar != '.'; counter++) {
				theChar = number.charAt(counter);
			}

			numberOfDecimals = stringLength - counter;

			return numberOfDecimals;
		} else {
			return 0;
		}

	}

	private List<String> validatePanLowHigh(String panLow, String panHigh,
			String panLowAdditionalInfo, String panHighAdditionalInfo, List<String> error) {
		// List<String> error = new ArrayList<String>();
		double minValue = 0;
		String initialMaxValue;
		int startDecimal = 0;
		int decimal = 0;
		double maxValue = 0;

		if (panLow == null || panLow.length() == 0) {
			error.add("Pan Low can not be empty value");

		} else if (panHigh == null || panHigh.length() == 0) {
			error.add("Pan Low can not be empty value");

		} else if (Double.parseDouble(panLow) > Double.parseDouble(panHigh)) {
			error.add("Invalid Pan Low! Pan Low cannot be greater than Pan High");
		} else {
			String listPanHighValue = panHighAdditionalInfo.substring(
					panHighAdditionalInfo.indexOf("(") + 1, panHighAdditionalInfo.indexOf(")"));
			String panHighInfo[] = listPanHighValue.split(",");
			minValue = Double.parseDouble(panHighInfo[1]);
			initialMaxValue = panHighInfo[2];
			decimal = Integer.parseInt(panHighInfo[3]);
			startDecimal = initialMaxValue.length() - decimal;
			maxValue = Double.parseDouble(initialMaxValue.substring(0, startDecimal) + "."
					+ initialMaxValue.substring(startDecimal));

			if (!panHigh.matches("-?\\d+(.\\d+)?")) {
				error.add("invalid Pan High");

			} else if (Double.parseDouble(panHigh) < minValue || Double.parseDouble(panHigh) > maxValue) {

				error.add("invalid Pan High range");
			}

			String listValuePL = panLowAdditionalInfo.substring(panLowAdditionalInfo.indexOf("(") + 1,
					panLowAdditionalInfo.indexOf(")"));
			String dataTypePL[] = listValuePL.split(",");

			minValue = Double.parseDouble(dataTypePL[1]);
			initialMaxValue = dataTypePL[2];
			decimal = Integer.parseInt(dataTypePL[3]);
			startDecimal = initialMaxValue.length() - decimal;
			maxValue = Double.parseDouble(initialMaxValue.substring(0, startDecimal) + "."
					+ initialMaxValue.substring(startDecimal));

			if (!panLow.matches("-?\\d+(.\\d+)?")) {
				error.add("invalid Pan Low");
			} else if (Double.parseDouble(panHigh) < minValue || Double.parseDouble(panHigh) > maxValue) {

				error.add("invalid Pan Low range");
			}

		}

		return error;

	}

	@Override
	public List<Release> getAllReleases() {
		return getHibernateTemplate().find(
				"from Release release " + "where deleted=0 order by description, version asc");

	}

	@Override
	public Release getAllReleasesFetchApplication(long id) {
		List<Release> list = getHibernateTemplate().find(
				"from Release release " + "join fetch release.application relApp "
						+ "where release.deleted=0 and release.id =?", id);
		if ((list != null) && (list.size() > 0)) {
			return (Release) list.get(0);
		} else {
			return null;
		}
	}

}
