package com.abl.db.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.abl.db.dao.ViewTerminalsNotDownloadedDao;
import com.abl.db.model.ViewTerminalsNotDownloaded;

@Repository
public class ViewTerminalsNotDownloadedDaoImpl extends DaoImpl implements ViewTerminalsNotDownloadedDao {
	
	@SuppressWarnings("unchecked")
	public List<ViewTerminalsNotDownloaded> getTerminalWithoutDownloadJob(long groupId){

		List<ViewTerminalsNotDownloaded> list =getHibernateTemplate().find("from ViewTerminalsNotDownloaded t " +
				" where t.groupId =? order by t.vehicleId asc ", groupId);
		return list;
		
	}

}
