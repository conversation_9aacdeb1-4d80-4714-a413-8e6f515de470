package com.abl.db.dao.impl;

import com.abl.db.bean.TermProfileAppBean;
import com.abl.db.dao.TerminalProfileDao;
import com.abl.db.model.TerminalProfile;
import com.abl.utils.DateUtils;
import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Repository
public class TerminalProfileDaoImpl extends DaoImpl implements
		TerminalProfileDao {

	@Override
	public TerminalProfile getTerminalProfile(long id) {
		return getHibernateTemplate().get(TerminalProfile.class, id);
	}

	@Override
	public void save(TerminalProfile terminalProfile) {
		getHibernateTemplate().saveOrUpdate(terminalProfile);

	}

	@SuppressWarnings("unchecked")
	@Override
	public TerminalProfile getTerminalProfileFetchApplication(long id) {
		List<TerminalProfile> list = getHibernateTemplate()
				.find("from TerminalProfile terminalProfile  "
						+ "left join fetch terminalProfile.application application "
						+ "left join fetch terminalProfile.group grouping "
						+ "where terminalProfile.id =?", id);
		if ((list != null) && (list.size() > 0)) {
			return list.get(0);
		} else {
			return null;
		}

	}

	@Override
	public void saveAll(List<TerminalProfile> object) {
		getHibernateTemplate().saveOrUpdateAll(object);
	}

	@SuppressWarnings("unchecked")
	@Override
	public TerminalProfile getTerminalProfileFetchApplication(long appId,
			long groupId) {
		List<TerminalProfile> list = getHibernateTemplate()
				.find("from TerminalProfile terminalProfile  "
						+ "left join fetch terminalProfile.application application "
						+ "left join fetch terminalProfile.group grouping "
						+ "where application.id = ? and grouping.id =? order by terminalProfile.profileVersion desc ",
						appId, groupId);

		if ((list != null) && (list.size() > 0)) {
			return list.get(0);
		} else {
			return null;
		}

	}
@SuppressWarnings("unchecked")
    @Override
    public TerminalProfile getLatestActiveTerminalProfile(long appId, long groupId) {
        Session session = this.getSession();
        Criteria criteria = session.createCriteria(TerminalProfile.class, "terminalProfile");
        criteria.createAlias("application", "application", criteria.LEFT_JOIN);
        criteria.createAlias("group", "grouping", criteria.LEFT_JOIN);
        if (appId != -1) {
            criteria.add(Restrictions.eq("application.id", appId));
        }

        if (groupId != -1) {
            criteria.add(Restrictions.eq("grouping.id", groupId));
        }
        Date nextDay = DateUtils.add(DateUtils.getStartOfDay(new Date()), Calendar.DAY_OF_YEAR, 1);
        criteria.add(Restrictions.lt("terminalProfile.effectiveDate", nextDay));
        criteria.add(Restrictions.eq("terminalProfile.deleted", false));
        criteria.addOrder(Order.desc("terminalProfile.profileVersion"));

        List<TerminalProfile> list = criteria.list();
        if (list != null && list.size() > 0) return list.get(0);

        return null;
    }

	@SuppressWarnings("unchecked")
	@Override
	public List<TermProfileAppBean> getTerminalProfileFetchApplicationRelease(long groupId, long releaseId) {
		List<TermProfileAppBean> termProfileAppBeansList = new ArrayList<TermProfileAppBean>();
		Session session = this.getSession();
		List<Object[]> records =session.createSQLQuery("select g.id as group_id, g.name as group_name, tp.id as terminal_profile_id, ra.tmtb_release_id as release_id, a.id as app_id, a.name as app_name, a.version as app_version " +
				"from tmtb_terminal_profile tp " +
				"join tmtb_grouping g on tp.group_id= g.id " +
				"join tmtb_application a on tp.application_id=a.id " +
				"join tmtb_release_tmtb_application ra on ra.application_id  = a.id " +
				"where tp.deleted=0 and g.deleted =0 and tp.group_id="+ groupId+" and ra.tmtb_release_id="+releaseId).list();


		if ((records != null) && (records.size() > 0)) {
			for(int i=0; i<records.size(); i++){
				TermProfileAppBean appBean = new TermProfileAppBean();
				Object[] termProfileApp = records.get(i);
				appBean.setGroupId(new Long(termProfileApp[0].toString()));
				appBean.setGroupName(termProfileApp[1].toString());
				appBean.setTerminalProfileId(new Long(termProfileApp[2].toString()));
				appBean.setReleaseId(new Long(termProfileApp[3].toString()));
				appBean.setAppId(new Long(termProfileApp[4].toString()));
				appBean.setAppName((termProfileApp[5].toString()));
				appBean.setAppVersion(new Integer(termProfileApp[6].toString()));
				termProfileAppBeansList.add(appBean);
			}
			return termProfileAppBeansList;
		} else {
			return null;
		}


	}

}
