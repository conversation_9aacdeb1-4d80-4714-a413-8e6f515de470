package com.abl.db.dao.impl;

import com.abl.db.dao.DbDao;

import java.util.Collection;

import org.springframework.stereotype.Repository;

import java.util.Collection;

@Repository
public class DbDaoImpl extends DaoImpl implements DbDao {

    @Override
    public int executeSql(String sql) {
        return getSession().createSQLQuery(sql).executeUpdate();
    }
    
    @Override
    public void saveOrUpdate(Collection<Object> entities) {
    	getHibernateTemplate().saveOrUpdateAll(entities);
    }
}