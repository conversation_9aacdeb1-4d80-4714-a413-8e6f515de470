package com.abl.db.dao.impl;

import com.abl.db.dao.PlTxnLogDao;
import com.abl.db.model.PlTxnLog;

import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Repository;

@Repository
public class PlTxnLogDaoImpl extends DaoImpl implements PlTxnLogDao {

    @Override
    public PlTxnLog getPlTxnLog(long id) {
        return getHibernateTemplate().get(PlTxnLog.class, id);
    }

    @SuppressWarnings("unchecked")
	@Override
    public PlTxnLog getPlTxnLogByRrn(String rrn) {
    	List<PlTxnLog> list = getHibernateTemplate().find("from PlTxnLog where rrn=? order by id desc", rrn);
    	if ((list != null)&&(!list.isEmpty())) {
    		return list.get(0);
    	} else {
    		return null;
    	}
    }
    
    @SuppressWarnings("unchecked")
	@Override
    public PlTxnLog getOfflineSalePlTxnLogByMidTidStanRespCode(String mid, String tid, String stan, String respCode) {
    	List<PlTxnLog> list = getHibernateTemplate().find("from PlTxnLog where mti=? and mid=? and tid=? and stan=? and respCode=? order by id desc", "0220", mid, tid, stan, respCode);
    	if ((list != null)&&(!list.isEmpty())) {
    		return list.get(0);
    	} else {
    		return null;
    	}
    }
    
    @SuppressWarnings("unchecked")
	@Override
    public PlTxnLog getOfflinePurchasePlTxnLogByTransIdRespCode(String transId, String respCode) {
    	List<PlTxnLog> list = getHibernateTemplate().find("from PlTxnLog where msgType=? and transId=? and respCode=? order by id desc", PlTxnLog.MSG_TYPE_OFFLINE_SALE, transId, respCode);
    	if ((list != null)&&(!list.isEmpty())) {
    		return list.get(0);
    	} else {
    		return null;
    	}
    }
    
    @SuppressWarnings("unchecked")
	@Override
    public PlTxnLog getSalePlTxnLogByMidTidStan(String mid, String tid, String stan) {
    	List<PlTxnLog> list = getHibernateTemplate().find("from PlTxnLog where mti=? and mid=? and tid=? and stan=? order by id desc", "0200", mid, tid, stan);
    	if ((list != null)&&(!list.isEmpty())) {
    		return list.get(0);
    	} else {
    		return null;
    	}
    }
    
    @SuppressWarnings("unchecked")
	@Override
    public PlTxnLog getSalePlTxnLogByTransId(String transId) {
    	List<PlTxnLog> list = getHibernateTemplate().find("from PlTxnLog where msgType=? and transId=? order by id desc", PlTxnLog.MSG_TYPE_SALE, transId);
    	if ((list != null)&&(!list.isEmpty())) {
    		return list.get(0);
    	} else {
    		return null;
    	}
    }
    
    @SuppressWarnings("unchecked")
    @Override
	public List<PlTxnLog> getPlTxnLogs(PlTxnLog.Status status, int maxResults) {
    	return getSession().createQuery("from PlTxnLog where status=:status order by id")
    		.setParameter("status", status)
    		.setMaxResults(maxResults)
    		.list();
    }
    
    @Override
    public int updateTxResultAndStatus(long id, String txResult, PlTxnLog.Status status) {
    	return getSession().createQuery("update PlTxnLog set txResult=:txResult, status=:status where id=:id")
    			.setString("txResult", txResult)
    			.setParameter("status", status)
    			.setLong("id", id)
    			.executeUpdate();
    }

	@Override
	public int updateTxResultAndStatusAndDcpStatus(long id, String txResult, PlTxnLog.Status status, PlTxnLog.DcpStatus dcpStatus) {
		return getSession().createQuery("update PlTxnLog set txResult=:txResult, status=:status,  dcpStatus=:dcpStatus where id=:id")
				.setString("txResult", txResult)
				.setParameter("status", status)
				.setParameter("dcpStatus", dcpStatus)
				.setLong("id", id)
				.executeUpdate();
	}
    
    @Override
    public int resetPendingStatus() {
    	return getSession().createQuery("update PlTxnLog set status=:pendingStatus where status=:processingStatus")
    			.setParameter("pendingStatus", PlTxnLog.Status.PENDING)
    			.setParameter("processingStatus", PlTxnLog.Status.PROCESSING)
    			.executeUpdate();
    }
    
    @Override
    public int resetEnquiringStatus() {
    	return getSession().createQuery("update PlTxnLog set status=:enquiringStatus where status=:processingStatus")
    			.setParameter("enquiringStatus", PlTxnLog.Status.ENQUIRING)
    			.setParameter("processingStatus", PlTxnLog.Status.PROCESSING2)
    			.executeUpdate();
    }
    
    @Override
    public void save(PlTxnLog txnLog) {
        getHibernateTemplate().save(txnLog);
    }


	@SuppressWarnings("unchecked")
	@Override
	public List<PlTxnLog> getPaylahDeclinedPaymentTxns(PlTxnLog.DcpStatus dcpStatus, Date retryDt, int maxResults) {
		return getSession().createQuery("from PlTxnLog where dcpStatus=:dcpStatus and lastSentDcpDateTime<=:retryDt order by id")
				.setParameter("dcpStatus", dcpStatus)
				.setTimestamp("retryDt", retryDt)
				.setMaxResults(maxResults)
				.list();
	}

	@Override
	public int updateDcpStatus(long id, PlTxnLog.DcpStatus dcpStatus) {
		return getSession().createQuery("update PlTxnLog set dcpStatus=:dcpStatus where id=:id")
				.setParameter("dcpStatus", dcpStatus)
				.setLong("id", id)
				.executeUpdate();
	}
}
