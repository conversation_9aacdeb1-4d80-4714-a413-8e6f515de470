package com.abl.db.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.abl.db.dao.ApplicationBinValueDao;
import com.abl.db.model.ApplicationBinValue;
@Repository
public class ApplicationBinValueDaoImpl extends DaoImpl implements ApplicationBinValueDao {
	
	public ApplicationBinValue getApplicationBinValue(long id){
		return getHibernateTemplate().get(ApplicationBinValue.class, id);
		
	}
	
	@Override
	public void saveAll(List<ApplicationBinValue> object) {
		getHibernateTemplate().saveOrUpdateAll(object);
	}

}
