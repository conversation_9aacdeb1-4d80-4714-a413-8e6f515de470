package com.abl.db.dao.impl;

import com.abl.db.dao.EzlTxnLogDao;
import com.abl.db.dao.NofTxnLogDao;
import com.abl.db.model.EzlTxnLog;
import com.abl.db.model.NofTxnLog;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public class EzlTxnLogDaoImpl extends DaoImpl implements EzlTxnLogDao {

    @Override
    public EzlTxnLog getEzlTxnLog(long id) {
        return getHibernateTemplate().get(EzlTxnLog.class, id);
    }
}
