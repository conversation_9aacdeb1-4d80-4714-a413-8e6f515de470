package com.abl.db.dao.impl;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

import org.hibernate.Query;
import org.springframework.stereotype.Repository;

import com.abl.db.model.EzlinkTransaction;
import com.abl.db.model.ViewEzlinkTransaction;
import com.abl.db.dao.EzlinkTransactionDao;
import com.abl.utils.DateUtils;

@Repository
public class EzlinkTransactionDaoImpl extends EzlinkDaoImpl implements EzlinkTransactionDao {
	
	public List<ViewEzlinkTransaction> searchDuplicateEzlinkTxn( String can, String orderBy, boolean ascending, int offset, int maxResults){
		Calendar fromDate = Calendar.getInstance();
		Date todayStart = DateUtils.getStartOfDay(new Date());
		fromDate.setTime(todayStart);
		fromDate.add(Calendar.MONTH, -1);
		Date fromDate2 = fromDate.getTime();
	
		Query query = getSession().createQuery("from ViewEzlinkTransaction where logdt >=:startDate and can =:can order by ptc desc");
		query.setParameter("startDate", fromDate2);
		query.setParameter("can", can);
		List l = query.list();
		return l;
	}

public int countDuplicateEzlinkTxn(String can){

	Calendar fromDate = Calendar.getInstance();
	Date todayStart = DateUtils.getStartOfDay(new Date());
	fromDate.setTime(todayStart);
	fromDate.add(Calendar.MONTH, -1); 
	Date fromDate2 = fromDate.getTime();
	
	Query query = getSession().createQuery("from ViewEzlinkTransaction where logdt >=:startDate and can =:can order by ptc desc");
	query.setParameter("startDate", fromDate2);
	query.setParameter("can", can);

	List l = query.list();

	if(l!=null && l.size()>0){
		return l.size();
	}else{
		return 0;
	}
}
}
