package com.abl.db.dao.impl;

import com.abl.db.dao.Dao;

import java.util.Collection;

import org.hibernate.LockMode;
import org.hibernate.SessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.orm.hibernate3.support.HibernateDaoSupport;

public abstract class <PERSON>oImpl extends HibernateDaoSupport implements Dao {

    @Autowired
    @Qualifier(value="sessionFactory")
    public void ablSetSessionFactory(SessionFactory sessionFactory) {
        setSessionFactory(sessionFactory);
    }

    @Override
    public void save(Object object) {
        getHibernateTemplate().saveOrUpdate(object);
    }

    @Override
    public void delete(Object object) {
        getHibernateTemplate().delete(object);
    }

    @Override
    public void refresh(Object object) {
        getHibernateTemplate().refresh(object);
    }

    @Override
    public void refreshForUpdate(Object object) {
        getHibernateTemplate().refresh(object, LockMode.PESSIMISTIC_WRITE);
    }
    
}
