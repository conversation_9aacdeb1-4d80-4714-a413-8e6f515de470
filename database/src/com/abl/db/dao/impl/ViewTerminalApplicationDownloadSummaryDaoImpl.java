package com.abl.db.dao.impl;

import com.abl.db.dao.ViewTerminalApplicationDownloadSummaryDao;
import com.abl.db.model.ViewTerminalApplicationDownloadSummary;
import java.util.List;
import org.hibernate.Criteria;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Repository;

@Repository
public class ViewTerminalApplicationDownloadSummaryDaoImpl extends DaoImpl implements
    ViewTerminalApplicationDownloadSummaryDao {

  @Override
  public List<ViewTerminalApplicationDownloadSummary> getAllTerminalApplicationDownloadSummary1(
      long groupId, short jobStatus) {
    Session session = this.getSession();
    Criteria criteria = session.createCriteria(ViewTerminalApplicationDownloadSummary.class,
        "termAppDownloadSummary");

    if (groupId > 0) {
      criteria.add(Restrictions.eq("termAppDownloadSummary.groupId", groupId));
    }

    if (jobStatus >= 0) {
      criteria.add(Restrictions.eq("termAppDownloadSummary.jobStatus", jobStatus));
    }

    criteria.add(Restrictions.eq("termAppDownloadSummary.deleted", false));

    List<ViewTerminalApplicationDownloadSummary> list = criteria.list();

    return list;
  }


  @Override
  public List<ViewTerminalApplicationDownloadSummary> getAllTerminalApplicationDownloadSummary(
      long groupId, short jobStatus) {
    Session session = this.getSession();

    short js = (jobStatus >= 0 ? jobStatus : 0);
    String jobStatusStr = (jobStatus >= 0 ? " AND tadj.STATUS=" + js : "");

    Query q = session.createSQLQuery(
            "SELECT  ROWNUM AS ID,tadj.id AS JOB_ID, tadj.START_WINDOW AS START_WINDOW, tadj.END_WINDOW AS END_WINDOW, tadj.CONCURRENT_DOWNLOAD,"
                +
                "tadj.CREATE_DATE_TIME AS CREATION_DATE," +
                "tr.ID AS RELEASE_ID, tr.DESCRIPTION ,  tr.VERSION , tm.ID AS MODEL_ID, tm.MODEL_NAME ,tadj.STATUS AS JOB_STATUS,tg.ID AS GROUP_ID,"
                +
                "(CASE WHEN" +
                "          ( SELECT COUNT(DISTINCT ta.terminal_id)" +
                "          FROM  TMVW_TERM_APP_LATEST ta, TMTB_TMS_TERMINAL ttt, tmtb_release r2, tmtb_grouping g2, tmtb_vehicle_group vg "
                +
                "          WHERE ta.release_id  = r2.id AND r2.id  = g2.release_id " +
                "          AND g2.id  = tg.id AND ta.terminal_id  = ttt.id" +
                "          AND ttt.vehicle_no  = vg.vehicle_id AND vg.group_id  = g2.id) IS NULL THEN 0 "
                +
                "          ELSE (SELECT COUNT(DISTINCT ta.terminal_id)" +
                "          FROM  TMVW_TERM_APP_LATEST ta," +
                "          tmtb_tms_terminal t,tmtb_release r2, tmtb_grouping g2, tmtb_vehicle_group vg"
                +
                "          WHERE ta.release_id  = r2.id AND r2.id  = g2.release_id" +
                "          AND g2.id  = tg.id AND ta.terminal_id  = t.id" +
                "          AND t.vehicle_no  = vg.vehicle_id AND vg.group_id  = g2.id" +
                "          AND   t.vehicle_unpaired=0 " +
                ") END) AS COMPLETED," +
                "    (CASE WHEN (SELECT COUNT(*)" +
                "          FROM  tmtb_term_app_download_job d," +
                "          tmtb_grouping g2" +
                "          WHERE d.application_download_job_id  = tadj.id" +
                " AND d.status  = 1" +
                " AND d.release_id  = tr.id" +
                " AND tr.deleted  = 0 " +
                " AND tr.id  = g2.release_id " +
                " AND g2.id  = tg.id " +
                " ) is null then 0 else (SELECT COUNT(*) " +
                "FROM  tmtb_term_app_download_job d, " +
                " tmtb_grouping g2 " +
                "WHERE d.application_download_job_id  = tadj.id " +
                " AND d.status  = 1" +
                " AND d.release_id  = tr.id" +
                " AND tr.deleted  = 0" +
                " AND tr.id  = g2.release_id" +
                " AND g2.id  = tg.id" +
                " )END) AS PENDING," +
                " (CASE WHEN ( SELECT COUNT(*) " +
                "FROM  tmvw_terminal_not_downloaded td " +
                "WHERE td.group_id  = tadj.group_id " +
                " )is null then 0 else (SELECT COUNT(*) " +
                "FROM  tmvw_terminal_not_downloaded td  " +
                "WHERE td.group_id  = tadj.group_id " +
                ")end )as not_started, " +
                " tadj.deleted as deleted, tadji.start_date " +
                "FROM TMTB_APPLICATION_DOWNLOAD_JOB tadj , TMTB_GROUPING tg, TMTB_RELEASE tr, TMTB_RELEASE_TMTB_MODEL trtm, TMTB_MODEL tm, TMTB_APP_DOWNLOAD_JOB_INFO tadji "
                +
                "WHERE tadj.GROUP_ID = :group_id AND tadj.GROUP_ID=tg.ID AND tg.RELEASE_ID = tr.ID AND trtm.TMTB_RELEASE_ID = tr.ID AND "
                +
                "trtm.MODEL_ID = tm.ID AND tadj.deleted = :deleted AND tadji.application_download_job_id (+) = tadj.id"
                + jobStatusStr).
        addEntity(ViewTerminalApplicationDownloadSummary.class).
        setParameter("group_id", groupId).
        setParameter("deleted", false);

    List<ViewTerminalApplicationDownloadSummary> list = q.list();

    return list;
  }
}
