package com.abl.db.dao.impl;

import com.abl.db.dao.AliTxnLogDao;
import com.abl.db.model.AliTxnLog;
import org.hibernate.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Repository
public class AliTxnLogDaoImpl extends DaoImpl implements AliTxnLogDao {

    @Override
    public AliTxnLog getAliTxnLog(long id) {
        return getHibernateTemplate().get(AliTxnLog.class, id);
    }

    @SuppressWarnings("unchecked")
    @Override
    public AliTxnLog getAliTxnLogByBookingRef(String bookingRef, String msgType) {
        List<AliTxnLog> list = getHibernateTemplate().find("from AliTxnLog where bookingRef=? and msgType=? order by id desc", bookingRef, msgType);

        if ((list != null)&&(!list.isEmpty())) {
            return list.get(0);
        } else {
            return null;
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public AliTxnLog getPaymentAliTxnLogByTransId(String transId) {
        List<AliTxnLog> list = getHibernateTemplate().find("from AliTxnLog where msgType=? and transId=? order by id desc", AliTxnLog.MSG_TYPE_OFFLINE_PAYMENT, transId);
        if ((list != null)&&(!list.isEmpty())) {
            return list.get(0);
        } else {
            return null;
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public AliTxnLog getAliTxnLogByBookingRefExcludeCurrentId(String bookingRef, String msgType, Long currId) {
        List<AliTxnLog> list = getHibernateTemplate().find("from AliTxnLog where bookingRef=?  and msgType=? and id<>? order by id desc", bookingRef, msgType, currId);
        if ((list != null)&&(!list.isEmpty())) {
            return list.get(0);
        } else {
            return null;
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public AliTxnLog getOfflinePaymentMidTidStanRespCode(String mid, String tid, String stan, String respCode) {
        List<AliTxnLog> list = getHibernateTemplate().find("from AliTxnLog where mti=? and mid=? and tid=? and stan=? and respCode=? order by id desc", "0220", mid, tid, stan, respCode);
        if ((list != null)&&(!list.isEmpty())) {
            return list.get(0);
        } else {
            return null;
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<AliTxnLog> getAlipayDeclinedPaymentTxns(AliTxnLog.H5Status h5Status, Date retryDt, int maxResults) {
        return getSession().createQuery("from AliTxnLog where h5Status=:h5Status and lastSentH5DateTime<=:retryDt order by id")
                .setParameter("h5Status", h5Status)
                .setTimestamp("retryDt", retryDt)
                .setMaxResults(maxResults)
                .list();
    }

    @Override
    public int updateDcpStatus(long id, AliTxnLog.DcpStatus dcpStatus) {
        return getSession().createQuery("update AliTxnLog set dcpStatus=:dcpStatus where id=:id")
                .setParameter("dcpStatus", dcpStatus)
                .setLong("id", id)
                .executeUpdate();
    }

    @Override
    public List<AliTxnLog> listPending(final String msgType, final int offset, final int limit,
                                       final Date txnLogDateTimeNotBefore, final Date lastSentToAlipayNotAfter){
        /*
        select * from tmtb_ali_txn_log where
        msg_type='cdg_ali_payment' and status='PENDING'
        and orig_booking_ref in (select booking_ref from tmtb_ali_txn_log where msg_type='cdg_ali_preauth')
        order by id desc;
         */
        StringBuilder buffer = new StringBuilder(
                "FROM AliTxnLog aliTxnLog "
                + "WHERE( " +
                        "aliTxnLog.msgType = :msgType "
                        + "AND "
                        + "aliTxnLog.status = :status "
                        + "AND "
                        + "aliTxnLog.origBookingRef IN ("
                                + "SELECT bookingRef FROM AliTxnLog WHERE msg_type = '" + AliTxnLog.MSG_TYPE_PREAUTH + "'"
                        + ")"
        );

        if(txnLogDateTimeNotBefore != null){
            buffer.append(
                    "AND (aliTxnLog.dateTime >= :dcpMessageReceivedNotBefore OR aliTxnLog.dateTime IS NULL)"
            );
        }

        if(lastSentToAlipayNotAfter != null){
        	buffer.append(
        			"AND (aliTxnLog.lastSentToAlipay < :alipayMessageReceivedNotBefore OR aliTxnLog.lastSentToAlipay IS NULL)"
	        );
        }

        buffer.append(") ORDER BY aliTxnLog.id ASC ");

        Query query = getSession().createQuery(
                buffer.toString()
        )
                .setParameter("msgType", msgType)
                .setParameter("status", AliTxnLog.Status.PENDING);

        if(txnLogDateTimeNotBefore != null){
            query.setParameter("dcpMessageReceivedNotBefore", txnLogDateTimeNotBefore);
        }

        if(lastSentToAlipayNotAfter != null){
        	query.setParameter("alipayMessageReceivedNotBefore", lastSentToAlipayNotAfter);
        }

        if(offset > 0){
            query.setFirstResult(offset);
        }

        if(limit > 0){
            query.setMaxResults(limit);
        }

        List<AliTxnLog> list = query.list();
        if (list !=null && !list.isEmpty()) {
            return list;
        } else {
            return null;
        }
    }


    public int updateStatus(final long id, AliTxnLog.Status status) {
        return updateStatus(id, status, null);
    }

    @Override
    public int updateStatus(final long id, AliTxnLog.Status status, AliTxnLog.Status whereStatus) {

        StringBuilder buffer = new StringBuilder("UPDATE AliTxnLog SET status=:status WHERE(id=:id");

        if(whereStatus != null){
            buffer.append(" AND status=:whereStatus");
        }

        buffer.append(")");

        Query query = getSession().createQuery(
                buffer.toString()
        );
        query.setParameter("id", id);
        query.setParameter("status", status);
        if(whereStatus != null){
            query.setParameter("whereStatus", whereStatus);
        }

        return query.executeUpdate();
    }
}
