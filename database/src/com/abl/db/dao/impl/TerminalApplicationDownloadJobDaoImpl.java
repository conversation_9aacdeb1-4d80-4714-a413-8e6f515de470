package com.abl.db.dao.impl;

import com.abl.db.dao.TerminalApplicationDownloadJobDao;
import com.abl.db.model.ApplicationDownloadJob;
import com.abl.db.model.TerminalApplicationDownloadJob;
import com.abl.db.model.TerminalApplicationDownloadJobId;
import com.abl.utils.DateUtils;
import org.hibernate.Session;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public class TerminalApplicationDownloadJobDaoImpl extends DaoImpl implements
        TerminalApplicationDownloadJobDao {
    @SuppressWarnings("unchecked")
    @Override
    public List<TerminalApplicationDownloadJob> getTerminalApplicationDownloadJobs() {
        return getHibernateTemplate().find("from TerminalApplicationDownloadJob termDownload");
    }

    @Override
    public void save(TerminalApplicationDownloadJob terminalApplicationDownloadJob) {
        getHibernateTemplate().saveOrUpdate(terminalApplicationDownloadJob);
    }

    @Override
    public List<TerminalApplicationDownloadJob> getNotSuccessTerminalApplicationDownloadJobs(int releaseVersion, long terminalId, String vehicleId) {
        return getHibernateTemplate().find("from TerminalApplicationDownloadJob termDownload " +
                "where termDownload.status!=0 " +
                "and termDownload.pk.release.version=? and termDownload.pk.tmsTerminal.id=? and termDownload.pk.vehicle.vehicleId=?",
                releaseVersion, terminalId, vehicleId);
    }

    @Override
    public List<TerminalApplicationDownloadJob> getInProgressTerminalApplicationDownloadJobs(long terminalId) {
        return getHibernateTemplate().find("from TerminalApplicationDownloadJob termDownload " +
                "where termDownload.status=1 " +
                "and termDownload.pk.tmsTerminal.id=?",
                terminalId);
    }

    @Override
    public List<TerminalApplicationDownloadJob> getInProgressTerminalApplicationDownloadJobs(String vehicleId) {
        return getHibernateTemplate().find("from TerminalApplicationDownloadJob termDownload " +
                "where termDownload.status=1 " +
                "and termDownload.pk.vehicle.vehicleId=?",
                vehicleId);
    }

    @Override
    public int countTerminalApplicationDownloadJobs(long jobId, short status, long terminalId) {
        List<TerminalApplicationDownloadJob> list = getHibernateTemplate().find("from TerminalApplicationDownloadJob termDownload " +
                "where termDownload.pk.applicationDownloadJob.id=? and termDownload.status=? and termDownload.pk.tmsTerminal.id!=?",
                jobId, status, terminalId);
        if (list == null) return 0;
        return list.size();
    }

    @Override
    public TerminalApplicationDownloadJob getByPk(TerminalApplicationDownloadJobId terminalApplicationDownloadJobId) {
        List<TerminalApplicationDownloadJob> termJobs = getHibernateTemplate().find("from TerminalApplicationDownloadJob termDownload " +
                "where termDownload.pk.applicationDownloadJob.id=? and termDownload.pk.tmsTerminal.id=? " +
                "and termDownload.pk.vehicle.vehicleId=? and termDownload.pk.release.id=?",
                terminalApplicationDownloadJobId.getApplicationDownloadJob().getId(),
                terminalApplicationDownloadJobId.getTmsTerminal().getId(),
                terminalApplicationDownloadJobId.getVehicle().getVehicleId(),
                terminalApplicationDownloadJobId.getRelease().getId());
        if (termJobs == null || termJobs.size() <= 0) return null;
        return termJobs.get(0);
    }

    @Override
    public int releaseOtherJobs(TerminalApplicationDownloadJob terminalApplicationDownloadJob) {
        Session session = this.getSession();

        String hql = "UPDATE TerminalApplicationDownloadJob termDownload set status = 3 " +
                "WHERE termDownload.pk.applicationDownloadJob.id != :applicationDownloadJobId " +
                "and termDownload.pk.tmsTerminal.id = :tmsTerminalId " +
                "and termDownload.pk.vehicle.vehicleId = :vehicleId " +
                "and termDownload.pk.release.id = :releaseId " +
                "and status = 1";
        org.hibernate.Query query = session.createQuery(hql);
        query.setParameter("applicationDownloadJobId", terminalApplicationDownloadJob.getPk().getApplicationDownloadJob().getId());
        query.setParameter("tmsTerminalId", terminalApplicationDownloadJob.getPk().getTmsTerminal().getId());
        query.setParameter("vehicleId", terminalApplicationDownloadJob.getPk().getVehicle().getVehicleId());
        query.setParameter("releaseId", terminalApplicationDownloadJob.getPk().getRelease().getId());
        int result = query.executeUpdate();
        return result;
    }

    public TerminalApplicationDownloadJob findTerminalApplicationDownloadJob(long groupId, long terminalId, String vehicleId, long releaseId) {
        String currentTime = DateUtils.format(new Date(), "HHmm");
        List<TerminalApplicationDownloadJob> termJobs = getHibernateTemplate().find("from TerminalApplicationDownloadJob termDownload " +
                "where termDownload.pk.tmsTerminal.id=? " +
                "and termDownload.pk.vehicle.vehicleId=? " +
                "and termDownload.pk.release.id=? " +
                "and termDownload.status=? " +
                "and termDownload.pk.applicationDownloadJob.deleted=0 " +
                "and termDownload.pk.applicationDownloadJob.status=? " +
                "and termDownload.pk.applicationDownloadJob.group.id=? " +
                "and ((termDownload.pk.applicationDownloadJob.startWindow=termDownload.pk.applicationDownloadJob.endWindow) " +
                "or (termDownload.pk.applicationDownloadJob.startWindow<termDownload.pk.applicationDownloadJob.endWindow and termDownload.pk.applicationDownloadJob.startWindow<=? and termDownload.pk.applicationDownloadJob.endWindow>=?) " +
                "or (termDownload.pk.applicationDownloadJob.startWindow>termDownload.pk.applicationDownloadJob.endWindow and (termDownload.pk.applicationDownloadJob.startWindow<=? or termDownload.pk.applicationDownloadJob.endWindow>=?)))",
                terminalId,
                vehicleId,
                releaseId,
                TerminalApplicationDownloadJob.IN_PROGRESS,
                ApplicationDownloadJob.ACTIVE,
                groupId,
                currentTime, currentTime,
                currentTime, currentTime);
        if (termJobs == null || termJobs.size() <= 0) return null;
        return termJobs.get(0);
    }
}
