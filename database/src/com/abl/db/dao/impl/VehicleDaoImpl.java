package com.abl.db.dao.impl;

import com.abl.db.dao.VehicleDao;
import com.abl.db.model.TmsTerminal;
import com.abl.db.model.Vehicle;
import com.abl.db.model.VehicleGroup;

import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.MatchMode;
import org.hibernate.criterion.Restrictions;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Projections;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class VehicleDaoImpl extends DaoImpl implements VehicleDao {

	@SuppressWarnings("unchecked")
	@Override
	public Vehicle getVehicle(String vehicleId) {
		List<Vehicle> vehicles = getHibernateTemplate().find("from Vehicle where upper(vehicleId)=?",
				vehicleId.toUpperCase());
		if ((vehicles != null) && (vehicles.size() > 0)) {
			return vehicles.get(0);
		} else {
			return null;
		}
	}

	@Override
	public List<Vehicle> getVehiclesByMultipleCriteria(List<String> ivdVersion,
			List<String> vehicleType, List<String> vehicleModel, String vehicleNo) {
		Session session = this.getSession();
		Criteria criteria = session.createCriteria(Vehicle.class, "vehicle");

		if (vehicleNo != null && vehicleNo.length() > 0) {
			criteria.add(Restrictions.ilike("vehicleId", vehicleNo, MatchMode.ANYWHERE));

		}
		if (ivdVersion != null && ivdVersion.size() > 0) {
			criteria.add(Restrictions.in("firmwareVersion", ivdVersion));

		}
		if (vehicleType != null && vehicleType.size() > 0) {
			criteria.add(Restrictions.in("vehicleType", vehicleType));

		}

		if (vehicleModel != null && vehicleModel.size() > 0) {
			criteria.add(Restrictions.in("ivdModelId", vehicleModel));

		}
		criteria.addOrder(Order.asc("vehicleId"));
		List<Vehicle> list = criteria.list();
		return list;
	}

	@Override
	public int countVehiclesByMultipleCriteria(List<String> ivdVersion, List<String> vehicleType,
			List<String> vehicleModel, String vehicleNo) {
		Session session = this.getSession();
	
		Criteria criteria = session.createCriteria(Vehicle.class, "vehicle").setProjection(
				Projections.rowCount());

		if (vehicleNo != null && vehicleNo.length() > 0) {
			criteria.add(Restrictions.ilike("vehicleId", vehicleNo, MatchMode.ANYWHERE));

		}
		if (ivdVersion != null && ivdVersion.size() > 0) {
			criteria.add(Restrictions.in("firmwareVersion", ivdVersion));

		}
		if (vehicleType != null && vehicleType.size() > 0) {
			criteria.add(Restrictions.in("vehicleType", vehicleType));

		}

		if (vehicleModel != null && vehicleModel.size() > 0) {
			criteria.add(Restrictions.in("ivdModelId", vehicleModel));

		}
		List<Number> list = criteria.list();
		if ((list != null) && (list.size() > 0)) {
			return list.get(0).intValue();
		} else {
			return 0;
		}

	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Vehicle> searchVehicles(String vehicleNo, String orderBy, boolean ascending,
			int offset, int maxResults) {
		Session session = this.getSession();
		Criteria criteria = session.createCriteria(Vehicle.class, "vehicle");

		if (vehicleNo != null && vehicleNo.length() > 0) {
			criteria.add(Restrictions.ilike("vehicle.vehicleId", vehicleNo, MatchMode.ANYWHERE));

		}

		if (orderBy != null) {
			if (orderBy.indexOf('.') < 0) {
				orderBy = "vehicle.vehicleId";
			}
			if (ascending) {
				criteria.addOrder(Order.asc(orderBy));
			} else {
				criteria.addOrder(Order.desc(orderBy));
			}
		}
		if (offset > 0) {
			criteria.setFirstResult(offset);
		}
		if (maxResults > 0) {
			criteria.setMaxResults(maxResults);
		}

		List<Vehicle> list = criteria.list();
		return list;
	}

	@SuppressWarnings("unchecked")
	@Override
	public int countVehicles(String vehicleNo) {
		Session session = this.getSession();
		Criteria criteria = session.createCriteria(Vehicle.class, "vehicle").setProjection(
				Projections.rowCount());

		if (vehicleNo != null && vehicleNo.length() > 0) {
			criteria.add(Restrictions.ilike("vehicle.vehicleId", vehicleNo, MatchMode.ANYWHERE));

		}

		List<Number> list = criteria.list();
		if ((list != null) && (list.size() > 0)) {
			return list.get(0).intValue();
		} else {
			return 0;
		}

	}

}
