package com.abl.db.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.abl.db.dao.ViewProfileParameterValueSummaryDao;
import com.abl.db.model.ViewProfileParameterValueSummary;

@Repository
public class ViewProfileParameterValueSummaryDaoImpl extends DaoImpl implements ViewProfileParameterValueSummaryDao {
	
	@SuppressWarnings("unchecked")
	public List<ViewProfileParameterValueSummary> getProfileParameterValueSummary(long profileId) {
		return getHibernateTemplate().find("from ViewProfileParameterValueSummary profileSummary where profileSummary.profileId=?", profileId);
	}

}
