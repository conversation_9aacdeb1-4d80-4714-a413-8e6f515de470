package com.abl.db.dao;

import java.util.List;

import com.abl.db.model.AdminUser;
import com.abl.db.model.Domain;

public interface AdminUserDao extends Dao {

	public AdminUser getAdminUser(long id);
	
	public AdminUser getAdminUserFetchDomain(long id);
	
	public AdminUser getAdminUserForUpdate(long id);
	
	public AdminUser getAdminUser(String login);
	
	public AdminUser getAdminUserForUpdate(String login);
	
	public AdminUser getAdminUserFetchAccessProfiles(long id);
	
	public AdminUser getAdminUserFetchAccessProfiles(String login);
	
	public int countAdminUsers(String login, String name, Integer status, Domain bank);
	
	public List<AdminUser> searchAdminUsers(String login, String name, Integer status, Domain bank, String orderBy, boolean ascending, int offset, int maxResults);
}
