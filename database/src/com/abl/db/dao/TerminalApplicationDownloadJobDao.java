package com.abl.db.dao;

import com.abl.db.model.TerminalApplicationDownloadJob;
import com.abl.db.model.TerminalApplicationDownloadJobId;

import java.util.List;

public interface TerminalApplicationDownloadJobDao extends Dao {

    public List<TerminalApplicationDownloadJob> getTerminalApplicationDownloadJobs();

    public void save(TerminalApplicationDownloadJob terminalApplicationDownloadJob);

    public List<TerminalApplicationDownloadJob> getNotSuccessTerminalApplicationDownloadJobs(int releaseVersion, long terminalId, String vehicleId);

    public List<TerminalApplicationDownloadJob> getInProgressTerminalApplicationDownloadJobs(long terminalId);

    public List<TerminalApplicationDownloadJob> getInProgressTerminalApplicationDownloadJobs(String vehicleId);

    public int countTerminalApplicationDownloadJobs(long jobId, short status, long terminalId);

    public TerminalApplicationDownloadJob getByPk(TerminalApplicationDownloadJobId terminalApplicationDownloadJobId);

    public int releaseOtherJobs(TerminalApplicationDownloadJob terminalApplicationDownloadJob);

    public TerminalApplicationDownloadJob findTerminalApplicationDownloadJob(long groupId, long terminalId, String vehicleId, long releaseId);
}
