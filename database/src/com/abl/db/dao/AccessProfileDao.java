package com.abl.db.dao;

import java.util.List;

import com.abl.db.model.AccessProfile;

public interface AccessProfileDao extends Dao {

	public AccessProfile getAccessProfile(long id);
	
	public AccessProfile getAccessProfile(String name);
	
	public AccessProfile getAccessProfileFetchPages(String name);
	
	public AccessProfile getAccessProfileFetchPages(long id);
	
	public List<AccessProfile> getAccessProfiles();
	
	public int countAccessProfiles();
	
	public List<AccessProfile> searchAccessProfiles(String orderBy, boolean ascending, int offset, int maxResults);
}
