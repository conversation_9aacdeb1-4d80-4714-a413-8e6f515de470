package com.abl.db.dao;

import java.util.Date;
import java.util.List;

import com.abl.db.model.MpTxnLog;
import com.abl.db.model.MpTxnLog.MsgType;
import com.abl.db.model.MpTxnLog.Status;

public interface MpTxnLogDao extends Dao {

	public void save(MpTxnLog mpOfflineSales);

	public Boolean isOfflineTxnExist(String maskedCan, String jobNumber, String stan);
	
	public List<MpTxnLog>getNewOfflineSalesTxnByDateAndStatus(Status connErrStatus,Date newTxnDt, Date connErrDt, Date timeoutDt, Date lastRetryDt);
	
	public void saveAll(List<MpTxnLog> object);
	
	public MpTxnLog getMpTxnLogs(String maskedCan, String jobNumber, String stan, String msgType);

	public MpTxnLog getReversedMpTxnLogs(String maskedCan, String jobNumber, MsgType msgType,
			String stan);
	
	public Boolean isMpTxnExist(String maskedCan, String jobNumber, String stan, String msgType);
	
	public MpTxnLog getOriSaleMpTxnForVoid(String rrn, MsgType msgType);
	
	public MpTxnLog getOriVoidMpTxnForReversal(String stan);
	
	public void merge(MpTxnLog mpTxnLog);
	
	public List<MpTxnLog>getAllOfflineSalesTxnByDateAndStatus(Status queueStatus,Status processStatus,Status connErrStatus,Date newTxnDt, Date connErrDt, Date timeoutDt, Date lastRetryDt);
	
//	public void updateMpTxnInQueue(List<MpTxnLog> txnLogs, Status queueStatus);
}