package com.abl.db.dao;

import com.abl.db.model.TmsTerminal;

import java.util.List;

public interface TmsTerminalDao extends Dao {

    public TmsTerminal getTerminal(long id);

    public TmsTerminal getTerminalFetchModel(long id);

    public void save(TmsTerminal terminal);

    public void saveAll(List<TmsTerminal> object);

    public List<TmsTerminal> searchTerminals(String serialNo, String vehicleNo, long modelId, String orderBy,
                                             boolean ascending, int offset, int maxResults);

    public int countTerminals(String serialNo, String vehicleNo,long modelId);

    public TmsTerminal getTerminalBySerialNo(String serialNo);

    public TmsTerminal getTerminalForUpdate(String serialNo);
    
    public int unpairOtherTerminalsForVehicleNo(String vehicleNo,String serialNo);
}
