package com.abl.db.dao;

import java.util.Date;
import java.util.List;

import com.abl.db.model.AlipayTxnLog;

public interface AlipayTxnLogDao extends Dao {
	
	public List<AlipayTxnLog> searchAlipayTxnLogs(Date startDate, Date endDate, String taxiNumber, String jobNumber, String driverId, String orderBy, boolean ascending, int offset, int maxResults);
	
	public int countAlipayTxnLogs( Date startDate, Date endDate, String taxiNumber, String jobNumber, String driverId);

}
