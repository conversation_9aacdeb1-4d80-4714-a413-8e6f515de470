package com.abl.db.dao;

import com.abl.db.model.TerminalLogRecord;

import java.util.Date;
import java.util.List;

public interface TerminalLogRecordDao extends Dao {
    public TerminalLogRecord getTerminaLogRecord(long id);

    public List<TerminalLogRecord> getTerminaLogRecordByLogSequence(long terminalLogId, int sequenceNo);

    public List<TerminalLogRecord> getTerminaLogFetchRecordByTerminal(long terminalId, Date startDate, Date endDate, String data, String source);
}
