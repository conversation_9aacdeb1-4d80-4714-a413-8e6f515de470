package com.abl.db.dao;

import java.util.Date;
import java.util.List;

import com.abl.db.model.DashSingtelLog;

public interface DashSingtelLogDao extends Dao {
	
	public List<DashSingtelLog> searchDashSingtelLogs(Date startDate, Date endDate, String taxiNumber, String jobNumber, String driverId, String orderBy, boolean ascending, int offset, int maxResults);
	
	public int countDashSingtelLogs( Date startDate, Date endDate, String taxiNumber, String jobNumber, String driverId);

}
