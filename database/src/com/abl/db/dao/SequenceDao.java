package com.abl.db.dao;

public interface SequenceDao extends Dao {

	/**
	 * create db sequence with name sequenceName
	 * throws exception if sequence creation fail
	 * 
	 * @param sequenceName
	 */
	public void createSequence(String sequenceName);
	
	/**
	 * get next value from db sequence
	 * throws exception if db sequence is not found
	 * 
	 * @param sequenceName
	 * @return
	 */
	public Long nextSequence(String sequenceName);

}