package com.abl.db.dao;

import java.util.List;

import com.abl.db.model.Domain;

public interface DomainDao extends Dao {

	public Domain getDomain(long id);
	
	public List<Domain> getDomains();
	
	public Domain getDomainByShortName(String shortName);
	
	public Domain getDomainByName(String name);
	
	public int countDomains();
	
	public List<Domain> searchDomains(String orderBy, boolean ascending, int offset, int maxResults);
}
