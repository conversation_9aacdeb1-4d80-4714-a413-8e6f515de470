package com.abl.db.bean;

public class TermProfileAppBean {
    private long groupId;
    private String groupName;
    private long terminalProfileId;
    private long appId;
    private long releaseId;
    private int appVersion;
    private String padAppVersion;
    private String appName;

    public long getGroupId() {
        return groupId;
    }

    public void setGroupId(long groupId) {
        this.groupId = groupId;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public long getTerminalProfileId() {
        return terminalProfileId;
    }

    public void setTerminalProfileId(long terminalProfileId) {
        this.terminalProfileId = terminalProfileId;
    }

    public long getAppId() {
        return appId;
    }

    public void setAppId(long appId) {
        this.appId = appId;
    }

    public long getReleaseId() {
        return releaseId;
    }

    public void setReleaseId(long releaseId) {
        this.releaseId = releaseId;
    }

    public int getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(int appVersion) {
        this.appVersion = appVersion;
    }

    public String getPadAppVersion() {
        return padAppVersion;
    }

    public void setPadAppVersion(String padAppVersion) {
        this.padAppVersion = padAppVersion;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }
}
