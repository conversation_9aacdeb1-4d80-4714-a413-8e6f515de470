package com.abl.db.bean;

import com.abl.db.model.StuckTransaction;

import java.util.Date;

/**
 * used by search methods in StuckTransactionService/Dao
 */
public class StuckTransactionSearchParam {
    private String serialNo;
    private String vehicleNo;
    private Date startDate;
    private Date endDate;
    private String triggerBy;
    private String jobNumber;
    private StuckTransaction.TxnType txnType;
    private StuckTransaction.State state;

    public String getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }

    public String getVehicleNo() {
        return vehicleNo;
    }

    public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getTriggerBy() {
        return triggerBy;
    }

    public void setTriggerBy(String triggerBy) {
        this.triggerBy = triggerBy;
    }

    public String getJobNumber() {
        return jobNumber;
    }

    public void setJobNumber(String jobNumber) {
        this.jobNumber = jobNumber;
    }

    public StuckTransaction.TxnType getTxnType() {
        return txnType;
    }

    public void setTxnType(StuckTransaction.TxnType txnType) {
        this.txnType = txnType;
    }

    public StuckTransaction.State getState() {
        return state;
    }

    public void setState(StuckTransaction.State state) {
        this.state = state;
    }
}
