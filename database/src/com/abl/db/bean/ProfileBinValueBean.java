package com.abl.db.bean;

public class ProfileBinValueBean {
    private long profileBinRangeId;
    private long terminalProfileId;
    private long appId;
    private long paramDefinitionId;
    private String panHigh;
    private String panLow;
    private String binParamName;
    private String binValue;

    public long getProfileBinRangeId() {
        return profileBinRangeId;
    }

    public void setProfileBinRangeId(long profileBinRangeId) {
        this.profileBinRangeId = profileBinRangeId;
    }

    public long getTerminalProfileId() {
        return terminalProfileId;
    }

    public void setTerminalProfileId(long terminalProfileId) {
        this.terminalProfileId = terminalProfileId;
    }

    public long getAppId() {
        return appId;
    }

    public void setAppId(long appId) {
        this.appId = appId;
    }

    public long getParamDefinitionId() {
        return paramDefinitionId;
    }

    public void setParamDefinitionId(long paramDefinitionId) {
        this.paramDefinitionId = paramDefinitionId;
    }

    public String getPanHigh() {
        return panHigh;
    }

    public void setPanHigh(String panHigh) {
        this.panHigh = panHigh;
    }

    public String getPanLow() {
        return panLow;
    }

    public void setPanLow(String panLow) {
        this.panLow = panLow;
    }

    public String getBinParamName() {
        return binParamName;
    }

    public void setBinParamName(String binParamName) {
        this.binParamName = binParamName;
    }

    public String getBinValue() {
        return binValue;
    }

    public void setBinValue(String binValue) {
        this.binValue = binValue;
    }
}
