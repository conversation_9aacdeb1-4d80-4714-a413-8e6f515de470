package com.abl.qr;

import org.apache.commons.configuration.ConfigurationException;
import org.apache.commons.configuration.PropertiesConfiguration;
import org.apache.commons.configuration.reloading.FileChangedReloadingStrategy;
import org.apache.commons.dbcp2.BasicDataSource;
import org.apache.log4j.Logger;
import org.jpos.q2.Q2;

import javax.sql.DataSource;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 10/4/17.
 */
public class QRHost {
    public static final String VERSION="1.0.0";

    private static Logger logger=Logger.getLogger(QRHost.class);

    private Q2 q2;
    private static BasicDataSource dataSource;
    public static PropertiesConfiguration config;

    public QRHost(String file) {
        try {
            config=new PropertiesConfiguration(file);
            config.setReloadingStrategy(new FileChangedReloadingStrategy());
        } catch (ConfigurationException e) {
            logger.error(e.toString());
        }
        q2=new Q2(config.getString("q2.deploy.dir","deploy"));

        try {
            dataSource = new BasicDataSource();
            Class.forName(config.getString("db.driver",""));
            dataSource.setUsername(config.getString("db.user"));
            dataSource.setPassword(config.getString("db.pass"));
            dataSource.setUrl(config.getString("db.url"));
            dataSource.setTestOnBorrow(config.getBoolean("db.test.on.borrow",true));
            dataSource.setValidationQuery(config.getString("db.validation.query","select 1 from dual"));
            dataSource.setInitialSize(config.getInt("db.initial.size",0));
            dataSource.setMaxTotal(config.getInt("db.max.active",8));
            dataSource.setMaxIdle(config.getInt("db.max.idle",8));
            dataSource.setMinIdle(config.getInt("db.min.idle",0));
            dataSource.setMaxWaitMillis(config.getLong("db.max.wait.milliseconds",15000));
        } catch (ClassNotFoundException e) {
            logger.error("Unable to load DB Driver "+e.toString());
        }
    }

    public static void main(String[] args) {
        if (args.length<1) {
            System.out.println("Usage: java DashHost properties_file");
            System.exit(1);
        }

        final QRHost qrHost=new QRHost(args[0]);
        logger.debug("Starting qrHost.");
        qrHost.start();

        Runtime.getRuntime().addShutdownHook(new Thread() {
            public void run() {
                logger.debug("In shutdown hook.");
                qrHost.stop();
            }
        });
    }

    public void start() {
        q2.start();
    }

    public void stop() {
        q2.stop();
    }

    public static DataSource getDataSource() {
        return dataSource;
    }
}
