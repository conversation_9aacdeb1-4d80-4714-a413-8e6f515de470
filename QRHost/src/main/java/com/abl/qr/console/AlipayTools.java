package com.abl.qr.console;

import com.abl.qr.QRHost;
import com.abl.qr.alipay.request.CancelReq;
import com.abl.qr.alipay.request.PreCreateReq;
import com.abl.qr.alipay.request.QueryReq;
import com.abl.qr.alipay.response.CancelRes;
import com.abl.qr.alipay.response.PreCreateRes;
import com.abl.qr.alipay.response.QueryRes;
import com.abl.qr.utils.AlipayUtils;
import com.abl.qr.utils.Utils;
import org.apache.commons.cli.*;
import org.apache.commons.configuration.PropertiesConfiguration;

/**
 * Created by k<PERSON><PERSON><PERSON><PERSON> on 7/5/17.
 */
public class AlipayTools {
    public static void main(String[] args) {
        Option help = new Option( "h", "help", false,"print this message" );
        Option version = new Option( "v", "version",false ,"print the version information and exit" );
        Option action = new Option( "act", "action",true ,"action to perform [precreate|cancel|query]" );
        Option amount = new Option( "amt", "amount",true ,"amount" );
        Option ref = new Option( "ref", "reference",true ,"reference" );
        Option config = new Option( "c", "config",true,"config file path");

        Options options = new Options();

        options.addOption( help );
        options.addOption( action );
        options.addOption( version );
        options.addOption( config );
        options.addOption( amount );
        options.addOption( ref );

        // create the parser
        CommandLineParser parser = new DefaultParser();
        // parse the command line arguments
        try {
            CommandLine line = parser.parse(options, args);
            String configFile = null;
            String actionStr = null;
            String amtStr = null;
            String refStr = null;

            if (line.hasOption("v")) {
                System.out.printf("Version: " + QRHost.VERSION + "\n");
                System.exit(0);
            }
            if (line.hasOption("h")) {
                // automatically generate the help statement
                printHelp(options);
                System.exit(0);
            }
            if (line.hasOption("c")) {
                System.out.printf("Config File: " + line.getOptionValue("c") + "\n");
                configFile = line.getOptionValue("c");
            }
            if (line.hasOption("act")) {
                System.out.printf("Action: " + line.getOptionValue("act") + "\n");
                actionStr = line.getOptionValue("act");
            }
            if (line.hasOption("amt")) {
                System.out.printf("Amount: " + line.getOptionValue("amt") + "\n");
                amtStr = line.getOptionValue("amt");
            }
            if (line.hasOption("ref")) {
                System.out.printf("Reference: " + line.getOptionValue("ref") + "\n");
                refStr = line.getOptionValue("ref");
            }

            if (actionStr==null || actionStr.isEmpty() || configFile==null || configFile.isEmpty()) {
                HelpFormatter formatter = new HelpFormatter();
                formatter.printHelp("Alipay Console Tools", options);
                System.exit(0);
            }

            PropertiesConfiguration properties=new PropertiesConfiguration(configFile);

            if (actionStr.equals("precreate")) {
                PreCreateReq preCreate=new PreCreateReq();
                preCreate.setCurrency(properties.getString("alipay.currency","SGD"));
                preCreate.setTransCurrency(properties.getString("alipay.currency","SGD"));
                preCreate.setPartner(properties.getString("alipay.partner.id"));
                //preCreate.setSellerId(properties.getString("alipay.partner.id")); // same as partner?
                preCreate.setInputCharset(properties.getString("alipay.charset","UTF-8"));
                preCreate.setOutTradeNo(refStr);
                preCreate.setNotifyUrl(properties.getString("alipay.notify.url"));
                preCreate.setPassbackParameters(refStr);
                preCreate.setService(properties.getString("alipay.precreate.service","alipay.acquire.precreate"));
                preCreate.setSubject(properties.getString("alipay.subject", "Comfort Taxi"));
                preCreate.setProductCode(properties.getString("alipay.product.code","OVERSEAS_MBARCODE_PAY"));
                Long currTime=System.currentTimeMillis();
                preCreate.setTimestamp(currTime.toString());
                preCreate.setTotalFee(amtStr);

                String preSignString=preCreate.generatePreSignString();
                String md5Signature=properties.getString("alipay.md5.signature");
                System.out.println("presign string:\n"+preSignString);

                //byte sign[]=AlipayUtils.MD5Sign(preSignString.getBytes(), md5Signature.getBytes());
                String sign1=AlipayUtils.MD5Sign1(preSignString+md5Signature, "MD5");
                preCreate.setSignType(properties.getString("alipay.sign.type","MD5"));
                System.out.println("Sign:"+sign1);
                preCreate.setSign(sign1);

                PreCreateRes result= (PreCreateRes) AlipayUtils.postRequest(properties, preCreate.getNameValuePair(), PreCreateRes.class);
                //String xmlString=AlipayUtils.getObjectXML(preCreate);
                //System.out.println("XML:"+xmlString);

                //String result= (String) AlipayUtils.postRequest(properties,xmlString, String.class);
                System.out.println("Receive : "+result);

            } else if (actionStr.equals("query")) {
                QueryReq queryReq=new QueryReq();
                queryReq.setInputCharset(properties.getString("alipay.charset","UTF-8"));
                queryReq.setService(properties.getString("alipay.query.service","alipay.acquire.overseas.query"));
                queryReq.setPartner(properties.getString("alipay.partner.id"));
                queryReq.setPartnerTransId(refStr);

                String preSignString=queryReq.generatePreSignString();
                String md5Signature=properties.getString("alipay.md5.signature");
                System.out.println("presign string:\n"+preSignString);

                String sign1=AlipayUtils.MD5Sign1(preSignString+md5Signature, "MD5");
                queryReq.setSignType(properties.getString("alipay.sign.type","MD5"));
                System.out.println("Sign:"+sign1);
                queryReq.setSign(sign1);

                QueryRes result= (QueryRes) AlipayUtils.postRequest(properties, queryReq.getNameValuePair(), QueryRes.class);
                System.out.println("Receive : "+result);

            } else if (actionStr.equals("cancel")) {

                CancelReq cancelReq=new CancelReq();

                cancelReq.setInputCharset(properties.getString("alipay.charset","UTF-8"));
                cancelReq.setService(properties.getString("alipay.cancel.service","alipay.acquire.cancel"));
                cancelReq.setPartner(properties.getString("alipay.partner.id"));
                cancelReq.setOutTradeNo(refStr);
                Long currTime=System.currentTimeMillis();
                cancelReq.setTimestamp(currTime.toString());

                String preSignString=cancelReq.generatePreSignString();
                String md5Signature=properties.getString("alipay.md5.signature");
                System.out.println("presign string:\n"+preSignString);

                String sign1=AlipayUtils.MD5Sign1(preSignString+md5Signature, "MD5");
                cancelReq.setSignType(properties.getString("alipay.sign.type","MD5"));
                System.out.println("Sign:"+sign1);
                cancelReq.setSign(sign1);

                CancelRes result= (CancelRes) AlipayUtils.postRequest(properties, cancelReq.getNameValuePair(), CancelRes.class);
                System.out.println("Receive : "+result);

            } else {
                printHelp(options);
            }

        } catch (ParseException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void printHelp(Options options) {
        HelpFormatter formatter = new HelpFormatter();
        formatter.printHelp("Alipay Console Tools", options);
    }
}
