package com.abl.qr.console;

import com.abl.qr.QRHost;
import com.abl.qr.netsqr.NETSQRPostObject;
import com.abl.qr.netsqr.QRData;
import com.abl.qr.netsqr.request.OrderReq;
import com.abl.qr.utils.NETSQRUtils;
import com.abl.qr.utils.Utils;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.apache.commons.cli.*;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.configuration.PropertiesConfiguration;

import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.security.PublicKey;

/**
 * Created by kohtzewei on 17/5/17.
 */
public class NETSQRTools {
    public static void main(String[] args) {
        Option help = new Option("h", "help", false, "print this message");
        Option version = new Option("v", "version", false, "print the version information and exit");
        Option action = new Option("act", "action", true, "action to perform [register|cancel|query]");
        Option amount = new Option("amt", "amount", true, "amount");
        Option ref = new Option("ref", "reference", true, "reference");
        Option config = new Option("c", "config", true, "config file path");

        Options options = new Options();

        options.addOption(help);
        options.addOption(action);
        options.addOption(version);
        options.addOption(config);
        options.addOption(amount);
        options.addOption(ref);

        // create the parser
        CommandLineParser parser = new DefaultParser();
        // parse the command line arguments
        try {
            CommandLine line = parser.parse(options, args);
            String configFile = null;
            String actionStr = null;
            String amtStr = null;
            String refStr = null;

            if (line.hasOption("v")) {
                System.out.printf("Version: " + QRHost.VERSION + "\n");
                System.exit(0);
            }
            if (line.hasOption("h")) {
                // automatically generate the help statement
                printHelp(options);
                System.exit(0);
            }
            if (line.hasOption("c")) {
                System.out.printf("Config File: " + line.getOptionValue("c") + "\n");
                configFile = line.getOptionValue("c");
            }
            if (line.hasOption("act")) {
                System.out.printf("Action: " + line.getOptionValue("act") + "\n");
                actionStr = line.getOptionValue("act");
            }
            if (line.hasOption("amt")) {
                System.out.printf("Amount: " + line.getOptionValue("amt") + "\n");
                amtStr = line.getOptionValue("amt");
            }
            if (line.hasOption("ref")) {
                System.out.printf("Reference: " + line.getOptionValue("ref") + "\n");
                refStr = line.getOptionValue("ref");
            }

            if (actionStr == null || actionStr.isEmpty() || configFile == null || configFile.isEmpty()) {
                HelpFormatter formatter = new HelpFormatter();
                formatter.printHelp("NETSQR Console Tools", options);
                System.exit(0);
            }

            PropertiesConfiguration properties = new PropertiesConfiguration(configFile);

            if (actionStr.equals("register")) {
                OrderReq orderReq=new OrderReq();
                orderReq.setMti("0200");
                orderReq.setProcessCode("990000");
                orderReq.setAmount(amtStr);
                orderReq.setStan("000001");
                orderReq.setTransactionDate("0523");
                orderReq.setTransactionTime("180000");
                orderReq.setEntryMode("00");
                orderReq.setConditionCode("83");
                orderReq.setInstitutionCode(properties.getString("nets.qr.institution.code"));
                orderReq.setRetrievalRef(refStr);
                String mid=properties.getString("nets.qr.mid");
                orderReq.setHostMid(mid);
                String tid=properties.getString("nets.qr.tid");
                orderReq.setHostTid(tid);
                orderReq.setAcceptorName("Comfort");
                orderReq.setInvoiceRef(refStr);

                QRData qrData=new QRData();
                qrData.setAmt(amtStr);
                qrData.setTid(tid);
                qrData.setMerchId(mid);
                qrData.setMerchantName("Comfort");
                orderReq.setTxnIdentifier(qrData.toQRString());
                System.out.println(qrData.toQRString());

                OrderReq.AdditionalData ad=new OrderReq.AdditionalData();
                ad.setPosId(tid);
                ad.setSourceAmount(amtStr);
                ad.setSourceCurrency("SGD");
                ad.setTargetCurrency("SGD");
                Gson gson=new GsonBuilder().disableHtmlEscaping().create();

                orderReq.setNpxData(gson.toJson(ad));
                orderReq.setCallbackUrl("https://d.glo.asia/WebNotify/log");

                orderReq.sign(properties.getString("nets.qr.secret"));

                SecretKey secretKey= NETSQRUtils.genSessionKey();
                //byte key[]="c40ca158-539a-4648-9d68-290307c7".getBytes();
                //SecretKeySpec secretKeySpec = new SecretKeySpec(key, "AES");
                System.out.println(Utils.getByteString(secretKey.getEncoded(),false));

                //System.out.println(Utils.getByteString(secretKey.getEncoded(),false));
                System.out.println("------RSA enc secret key------");
                PublicKey pk = NETSQRUtils.getPublicKey("./conf/comfort_public.pem");
                org.apache.commons.codec.binary.Base64 base64=new Base64();


                //String encSessionKey=base64.encodeAsString(NETSQRUtils.encryptSessionKey(secretKey.getEncoded(),pk));
                String encSessionKey=new String(base64.encode(NETSQRUtils.encryptSessionKey(secretKey.getEncoded(),pk)));
                System.out.println(encSessionKey);

                String orderJson=gson.toJson(orderReq);
                System.out.println("------Order JSON------");
                System.out.println(orderJson);
                System.out.println("------AES enc of payload------");
                String payload=Utils.getByteString(NETSQRUtils.encryptPayload(orderJson.getBytes(),secretKey),false).toUpperCase();
                System.out.println(payload);

                NETSQRPostObject object=new NETSQRPostObject();
                object.setKey1(encSessionKey);
                object.setKey2(payload);

                String postString=gson.toJson(object);
                System.out.println("------JSON to post------");
                System.out.println(postString);

                String url=properties.getString("nets.qr.register.url");
                System.out.println("------Post URL------");
                System.out.println(url);

                Object o=NETSQRUtils.postRequest(properties, postString, url,null, null );

            } else if (actionStr.equals("query")) {

            } else if (actionStr.equals("cancel")) {

            } else {
                printHelp(options);
            }

        } catch (ParseException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void printHelp(Options options) {
        HelpFormatter formatter = new HelpFormatter();
        formatter.printHelp("Alipay Console Tools", options);
    }
}
