package com.abl.qr.alipay.request;

import lombok.Getter;
import lombok.Setter;
import org.apache.http.message.BasicNameValuePair;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 7/5/17.
 */

@XmlRootElement( name="alipay" )
@XmlAccessorType(XmlAccessType.FIELD)
public class PreCreateReq {
    @XmlElement( name = "service")
    @Getter @Setter
    private String service;

    @XmlElement( name = "partner")
    @Getter @Setter
    private String partner;

    @XmlElement( name = "_input_charset")
    @Getter @Setter
    private String inputCharset;

    @XmlElement( name = "sign_type")
    @Getter @Setter
    private String signType;

    @XmlElement( name = "sign")
    @Getter @Setter
    private String sign;

    @XmlElement( name = "timestamp")
    @Getter @Setter
    private String timestamp;

    @XmlElement( name = "notify_url")
    @Getter @Setter
    private String notifyUrl;

    @XmlElement( name = "terminal_timestamp")
    @Getter @Setter
    private String terminalTimestamp;

    @XmlElement( name = "out_trade_no")
    @Getter @Setter
    private String outTradeNo;

    @XmlElement( name = "subject")
    @Getter @Setter
    private String subject;

    @XmlElement( name = "product_code")
    @Getter @Setter
    private String productCode;

    @XmlElement( name = "total_fee")
    @Getter @Setter
    private String totalFee;

    @XmlElement( name = "seller_id")
    @Getter @Setter
    private String sellerId;

    @XmlElement( name = "currency")
    @Getter @Setter
    private String currency;

    @XmlElement( name = "trans_currency")
    @Getter @Setter
    private String transCurrency;

    @XmlElement( name = "passback_parameters")
    @Getter @Setter
    private String passbackParameters;

    // This field is use to send Comfort private date as the secondary_merchant_id field
    @XmlElement( name = "extend_params")
    @Getter @Setter
    private String extendParams;

    public String generatePreSignString() {
        StringBuilder stringBuilder=new StringBuilder();
        if (inputCharset!=null && !inputCharset.isEmpty()) {
            stringBuilder.append("_input_charset=" + inputCharset);
        }

        if (currency!=null && !currency.isEmpty()) {
            if (stringBuilder.length()>0) stringBuilder.append("&");
            stringBuilder.append("currency=" + currency);
        }

        if (extendParams!=null && !extendParams.isEmpty()) {
            if (stringBuilder.length()>0) stringBuilder.append("&");
            stringBuilder.append("extend_params=" + extendParams);
        }

        if (notifyUrl!=null && !notifyUrl.isEmpty()) {
            if (stringBuilder.length()>0) stringBuilder.append("&");
            stringBuilder.append("notify_url=" + notifyUrl);
        }

        if (outTradeNo!=null && !outTradeNo.isEmpty()) {
            if (stringBuilder.length()>0) stringBuilder.append("&");
            stringBuilder.append("out_trade_no=" + outTradeNo);
        }

        if (partner!=null && !partner.isEmpty()) {
            if (stringBuilder.length()>0) stringBuilder.append("&");
            stringBuilder.append("partner=" + partner);
        }

        if (passbackParameters!=null && !passbackParameters.isEmpty()) {
            if (stringBuilder.length()>0) stringBuilder.append("&");
            stringBuilder.append("passback_parameters=" + passbackParameters);
        }

        if (productCode!=null && !productCode.isEmpty()) {
            if (stringBuilder.length()>0) stringBuilder.append("&");
            stringBuilder.append("product_code=" + productCode);
        }

        if (sellerId!=null && !sellerId.isEmpty()) {
            if (stringBuilder.length()>0) stringBuilder.append("&");
            stringBuilder.append("seller_id=" + sellerId);
        }

        if (service!=null && !service.isEmpty()) {
            if (stringBuilder.length()>0) stringBuilder.append("&");
            stringBuilder.append("service=" + service);
        }

        if (subject!=null && !subject.isEmpty()) {
            if (stringBuilder.length()>0) stringBuilder.append("&");
            stringBuilder.append("subject=" + subject);
        }

        if (terminalTimestamp!=null && !terminalTimestamp.isEmpty()) {
            if (stringBuilder.length()>0) stringBuilder.append("&");
            stringBuilder.append("terminal_timestamp=" + terminalTimestamp);
        }

        if (timestamp!=null && !timestamp.isEmpty()) {
            if (stringBuilder.length()>0) stringBuilder.append("&");
            stringBuilder.append("timestamp=" + timestamp);
        }

        if (totalFee!=null && !totalFee.isEmpty()) {
            if (stringBuilder.length()>0) stringBuilder.append("&");
            stringBuilder.append("total_fee=" + totalFee);
        }

        if (transCurrency!=null && !transCurrency.isEmpty()) {
            if (stringBuilder.length()>0) stringBuilder.append("&");
            stringBuilder.append("trans_currency=" + transCurrency);
        }

        return stringBuilder.toString();
    }

    public List<BasicNameValuePair> getNameValuePair() {
        List<BasicNameValuePair> list=new ArrayList<BasicNameValuePair>();

        if (inputCharset!=null && !inputCharset.isEmpty()) {
            list.add(new BasicNameValuePair("_input_charset",inputCharset));
        }

        if (currency!=null && !currency.isEmpty()) {
            list.add(new BasicNameValuePair("currency", currency));
        }

        if (extendParams!=null && !extendParams.isEmpty()) {
            list.add(new BasicNameValuePair("extend_params" , extendParams));
        }

        if (notifyUrl!=null && !notifyUrl.isEmpty()) {
            list.add(new BasicNameValuePair("notify_url", notifyUrl));
        }

        if (outTradeNo!=null && !outTradeNo.isEmpty()) {
            list.add(new BasicNameValuePair("out_trade_no", outTradeNo));
        }

        if (partner!=null && !partner.isEmpty()) {
            list.add(new BasicNameValuePair("partner", partner));
        }

        if (passbackParameters!=null && !passbackParameters.isEmpty()) {
            list.add(new BasicNameValuePair("passback_parameters", passbackParameters));
        }

        if (productCode!=null && !productCode.isEmpty()) {
            list.add(new BasicNameValuePair("product_code", productCode));
        }

        if (sellerId!=null && !sellerId.isEmpty()) {
            list.add(new BasicNameValuePair("seller_id", sellerId));
        }

        if (service!=null && !service.isEmpty()) {
            list.add(new BasicNameValuePair("service", service));
        }

        if (sign!=null && !sign.isEmpty()) {
            list.add(new BasicNameValuePair("sign", sign));
        }

        if (signType!=null && !signType.isEmpty()) {
            list.add(new BasicNameValuePair("sign_type", signType));
        }

        if (subject!=null && !subject.isEmpty()) {
            list.add(new BasicNameValuePair("subject", subject));
        }

        if (terminalTimestamp!=null && !terminalTimestamp.isEmpty()) {
            list.add(new BasicNameValuePair("terminal_timestamp", terminalTimestamp));
        }

        if (timestamp!=null && !timestamp.isEmpty()) {
            list.add(new BasicNameValuePair("timestamp", timestamp));
        }

        if (totalFee!=null && !totalFee.isEmpty()) {
            list.add(new BasicNameValuePair("total_fee", totalFee));
        }

        if (transCurrency!=null && !transCurrency.isEmpty()) {
            list.add(new BasicNameValuePair("trans_currency", transCurrency));
        }

        return list;
    }


    @Override
    public String toString() {
        return "PreCreateReq{" +
                "service='" + service + '\'' +
                ", partner='" + partner + '\'' +
                ", inputCharset='" + inputCharset + '\'' +
                ", signType='" + signType + '\'' +
                ", sign='" + sign + '\'' +
                ", timestamp='" + timestamp + '\'' +
                ", notifyUrl='" + notifyUrl + '\'' +
                ", terminalTimestamp='" + terminalTimestamp + '\'' +
                ", outTradeNo='" + outTradeNo + '\'' +
                ", subject='" + subject + '\'' +
                ", productCode='" + productCode + '\'' +
                ", totalFee='" + totalFee + '\'' +
                ", sellerId='" + sellerId + '\'' +
                ", currency='" + currency + '\'' +
                ", transCurrency='" + transCurrency + '\'' +
                ", passbackParameters='" + passbackParameters + '\'' +
                ", extendParams='" + extendParams + '\'' +
                '}';
    }
}
