package com.abl.qr.alipay.response;

import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.*;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 8/5/17.
 */

@XmlRootElement( name="alipay" )
@XmlAccessorType(XmlAccessType.FIELD)
public class QueryRes {
    @XmlElement( name = "sign_type")
    @Getter @Setter
    private String signType;

    @XmlElement( name = "sign")
    @Getter @Setter
    private String sign;

    @XmlElement( name = "is_success")
    @Getter @Setter
    private String isSuccess;

    @XmlElementWrapper(name="request")
    @XmlElement( name = "param")
    private List<ReqParam> request;

    @XmlElement
    @Getter @Setter
    private Response response;

    @XmlAccessorType(XmlAccessType.FIELD)
    public static class Response {
        @XmlElement( name="alipay" )
        @Getter @Setter
        private Response.QueryResponse alipayResponse;

        @XmlAccessorType(XmlAccessType.FIELD)
        public static class QueryResponse {
            @XmlElement( name = "alipay_trans_status")
            @Getter @Setter
            private String alipayTransStatus;

            @XmlElement( name = "alipay_buyer_login_id")
            @Getter @Setter
            private String alipayBuyerLoginId;

            @XmlElement( name = "alipay_buyer_user_id")
            @Getter @Setter
            private String alipayBuyerUserId;

            @XmlElement( name = "out_trade_no")
            @Getter @Setter
            private String outTradeNo;

            @XmlElement( name = "partner_trans_id")
            @Getter @Setter
            private String partnerTransId;

            @XmlElement( name = "alipay_trans_id")
            @Getter @Setter
            private String alipayTransId;

            @XmlElement( name = "alipay_pay_time")
            @Getter @Setter
            private String alipayPayTime;

            @XmlElement( name = "currency")
            @Getter @Setter
            private String currency;

            @XmlElement( name = "trans_amount")
            @Getter @Setter
            private String transAmount;

            @XmlElement( name = "exchange_rate")
            @Getter @Setter
            private String exchangeRate;

            @XmlElement( name = "trans_amount_cny")
            @Getter @Setter
            private String transAmountCny;

                @XmlElement( name = "result_code")
            @Getter @Setter
            private String resultCode;

            @XmlElement( name = "detail_error_code")
            @Getter @Setter
            private String detailErrorCode;

            @XmlElement( name = "detail_error_des")
            @Getter @Setter
            private String detailErrorDes;

            @Override
            public String toString() {
                return "QueryResponse{" +
                        "alipayTransStatus='" + alipayTransStatus + '\'' +
                        ", alipayBuyerLoginId='" + alipayBuyerLoginId + '\'' +
                        ", alipayBuyerUserId='" + alipayBuyerUserId + '\'' +
                        ", outTradeNo='" + outTradeNo + '\'' +
                        ", partnerTransId='" + partnerTransId + '\'' +
                        ", alipayTransId='" + alipayTransId + '\'' +
                        ", alipayPayTime='" + alipayPayTime + '\'' +
                        ", currency='" + currency + '\'' +
                        ", transAmount='" + transAmount + '\'' +
                        ", exchangeRate='" + exchangeRate + '\'' +
                        ", transAmountCny='" + transAmountCny + '\'' +
                        ", resultCode='" + resultCode + '\'' +
                        ", detailErrorCode='" + detailErrorCode + '\'' +
                        ", detailErrorDes='" + detailErrorDes + '\'' +
                        '}';
            }
        }

        @Override
        public String toString() {
            return "Response{" +
                    "alipayResponse=" + alipayResponse +
                    '}';
        }
    }

    @Override
    public String toString() {
        return "QueryRes{" +
                "signType='" + signType + '\'' +
                ", sign='" + sign + '\'' +
                ", isSuccess='" + isSuccess + '\'' +
                ", request=" + request +
                ", response=" + response +
                '}';
    }
}
