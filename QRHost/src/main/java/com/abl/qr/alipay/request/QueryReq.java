package com.abl.qr.alipay.request;

import lombok.Getter;
import lombok.Setter;
import org.apache.http.message.BasicNameValuePair;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 8/5/17.
 */

@XmlRootElement( name="alipay" )
@XmlAccessorType(XmlAccessType.FIELD)
public class QueryReq {
    @XmlElement( name = "service")
    @Getter @Setter
    private String service;
    @XmlElement( name = "partner")
    @Getter @Setter
    private String partner;
    @XmlElement( name = "_input_charset")
    @Getter @Setter
    private String inputCharset;
    @XmlElement( name = "sign_type")
    @Getter @Setter
    private String signType;
    @XmlElement( name = "sign")
    @Getter @Setter
    private String sign;
    @XmlElement( name = "partner_trans_id")
    @Getter @Setter
    private String partnerTransId;

    public String generatePreSignString() {
        StringBuilder stringBuilder=new StringBuilder();
        if (inputCharset!=null && !inputCharset.isEmpty()) {
            stringBuilder.append("_input_charset=" + inputCharset);
        }

        if (partner!=null && !partner.isEmpty()) {
            if (stringBuilder.length()>0) stringBuilder.append("&");
            stringBuilder.append("partner=" + partner);
        }

        if (partnerTransId!=null && !partnerTransId.isEmpty()) {
            if (stringBuilder.length()>0) stringBuilder.append("&");
            stringBuilder.append("partner_trans_id=" + partnerTransId);
        }

        if (service!=null && !service.isEmpty()) {
            if (stringBuilder.length()>0) stringBuilder.append("&");
            stringBuilder.append("service=" + service);
        }

        /*if (sign!=null && !sign.isEmpty()) {
            if (stringBuilder.length()>0) stringBuilder.append("&");
            stringBuilder.append("sign=" + sign);
        }

        if (signType!=null && !signType.isEmpty()) {
            if (stringBuilder.length()>0) stringBuilder.append("&");
            stringBuilder.append("sign_type=" + signType);
        }*/

        return stringBuilder.toString();
    }

    public List<BasicNameValuePair> getNameValuePair() {
        List<BasicNameValuePair> list=new ArrayList<BasicNameValuePair>();

        if (inputCharset!=null && !inputCharset.isEmpty()) {
            list.add(new BasicNameValuePair("_input_charset",inputCharset));
        }

        if (partnerTransId!=null && !partnerTransId.isEmpty()) {
            list.add(new BasicNameValuePair("partner_trans_id", partnerTransId));
        }

        if (partner!=null && !partner.isEmpty()) {
            list.add(new BasicNameValuePair("partner", partner));
        }

        if (service!=null && !service.isEmpty()) {
            list.add(new BasicNameValuePair("service", service));
        }

        if (sign!=null && !sign.isEmpty()) {
            list.add(new BasicNameValuePair("sign", sign));
        }

        if (signType!=null && !signType.isEmpty()) {
            list.add(new BasicNameValuePair("sign_type", signType));
        }

        return list;
    }

    @Override
    public String toString() {
        return "QueryReq{" +
                "service='" + service + '\'' +
                ", partner='" + partner + '\'' +
                ", inputCharset='" + inputCharset + '\'' +
                ", signType='" + signType + '\'' +
                ", sign='" + sign + '\'' +
                ", partnerTransId='" + partnerTransId + '\'' +
                '}';
    }
}
