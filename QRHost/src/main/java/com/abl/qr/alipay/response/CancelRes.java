package com.abl.qr.alipay.response;

import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.*;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 8/5/17.
 */
@XmlRootElement( name="alipay" )
@XmlAccessorType(XmlAccessType.FIELD)
public class CancelRes {
    @XmlElement( name = "sign_type")
    @Getter @Setter
    private String signType;
    @XmlElement( name = "sign")
    @Getter @Setter
    private String sign;

    @XmlElement( name = "is_success")
    @Getter @Setter
    private String isSuccess;

    @XmlElement( name = "error")
    @Getter @Setter
    private String error;


    @XmlElementWrapper(name="request")
    @XmlElement( name = "param")
    private List<ReqParam> request;


    @XmlElement
    @Getter @Setter
    private Response response;

    @XmlAccessorType(XmlAccessType.FIELD)
    public static class Response {
        @XmlElement(name = "alipay")
        @Getter
        @Setter
        private Response.QueryResponse alipayResponse;

        @XmlAccessorType(XmlAccessType.FIELD)
        public static class QueryResponse {
            @XmlElement( name = "result_code")
            @Getter @Setter
            private String resultCode;

            @XmlElement( name = "trade_no")
            @Getter @Setter
            private String tradeNo;

            @XmlElement( name = "out_trade_no")
            @Getter @Setter
            private String outTradeNo;

            @XmlElement( name = "retry_flag")
            @Getter @Setter
            private String retryFlag;

            @XmlElement( name = "action")
            @Getter @Setter
            private String action;

            @XmlElement( name = "detail_error_code")
            @Getter @Setter
            private String detailErrorCode;

            @XmlElement( name = "detail_error_des")
            @Getter @Setter
            private String detailErrorDes;

            @XmlElement( name = "display_message")
            @Getter @Setter
            private String displayMessage;


            @Override
            public String toString() {
                return "QueryResponse{" +
                        "resultCode='" + resultCode + '\'' +
                        ", tradeNo='" + tradeNo + '\'' +
                        ", outTradeNo='" + outTradeNo + '\'' +
                        ", retryFlag='" + retryFlag + '\'' +
                        ", action='" + action + '\'' +
                        ", detailErrorCode='" + detailErrorCode + '\'' +
                        ", detailErrorDes='" + detailErrorDes + '\'' +
                        ", displayMessage='" + displayMessage + '\'' +
                        '}';
            }
        }

        @Override
        public String toString() {
            return "Response{" +
                    "alipayResponse=" + alipayResponse +
                    '}';
        }
    }

    @Override
    public String toString() {
        return "CancelRes{" +
                "signType='" + signType + '\'' +
                ", sign='" + sign + '\'' +
                ", isSuccess='" + isSuccess + '\'' +
                ", error='" + error + '\'' +
                ", request=" + request +
                ", response=" + response +
                '}';
    }
}
