package com.abl.qr.alipay.request;

import lombok.Getter;
import lombok.Setter;
import org.apache.http.message.BasicNameValuePair;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 8/5/17.
 */
@XmlRootElement( name="alipay" )
@XmlAccessorType(XmlAccessType.FIELD)
public class CancelReq {
    @XmlElement( name = "service")
    @Getter @Setter
    private String service;
    @XmlElement( name = "partner")
    @Getter @Setter
    private String partner;
    @XmlElement( name = "_input_charset")
    @Getter @Setter
    private String inputCharset;
    @XmlElement( name = "sign_type")
    @Getter @Setter
    private String signType;
    @XmlElement( name = "sign")
    @Getter @Setter
    private String sign;
    @XmlElement( name = "timestamp")
    @Getter @Setter
    private String timestamp;
    @XmlElement( name = "terminal_timestamp")
    @Getter @Setter
    private String terminalTimestamp;
    @XmlElement( name = "out_trade_no")
    @Getter @Setter
    private String outTradeNo;

    public String generatePreSignString() {
        StringBuilder stringBuilder=new StringBuilder();
        if (inputCharset!=null && !inputCharset.isEmpty()) {
            stringBuilder.append("_input_charset=" + inputCharset);
        }

        if (outTradeNo!=null && !outTradeNo.isEmpty()) {
            if (stringBuilder.length()>0) stringBuilder.append("&");
            stringBuilder.append("out_trade_no=" + outTradeNo);
        }

        if (partner!=null && !partner.isEmpty()) {
            if (stringBuilder.length()>0) stringBuilder.append("&");
            stringBuilder.append("partner=" + partner);
        }

        if (service!=null && !service.isEmpty()) {
            if (stringBuilder.length()>0) stringBuilder.append("&");
            stringBuilder.append("service=" + service);
        }

        /*if (sign!=null && !sign.isEmpty()) {
            if (stringBuilder.length()>0) stringBuilder.append("&");
            stringBuilder.append("sign=" + sign);
        }

        if (signType!=null && !signType.isEmpty()) {
            if (stringBuilder.length()>0) stringBuilder.append("&");
            stringBuilder.append("sign_type=" + signType);
        }*/

        if (terminalTimestamp!=null && !terminalTimestamp.isEmpty()) {
            if (stringBuilder.length()>0) stringBuilder.append("&");
            stringBuilder.append("terminal_timestamp=" + terminalTimestamp);
        }

        if (timestamp!=null && !timestamp.isEmpty()) {
            if (stringBuilder.length()>0) stringBuilder.append("&");
            stringBuilder.append("timestamp=" + timestamp);
        }

        return stringBuilder.toString();
    }

    public List<BasicNameValuePair> getNameValuePair() {
        List<BasicNameValuePair> list=new ArrayList<BasicNameValuePair>();

        if (inputCharset!=null && !inputCharset.isEmpty()) {
            list.add(new BasicNameValuePair("_input_charset",inputCharset));
        }

        if (outTradeNo!=null && !outTradeNo.isEmpty()) {
            list.add(new BasicNameValuePair("out_trade_no", outTradeNo));
        }

        if (partner!=null && !partner.isEmpty()) {
            list.add(new BasicNameValuePair("partner", partner));
        }

        if (service!=null && !service.isEmpty()) {
            list.add(new BasicNameValuePair("service", service));
        }

        if (sign!=null && !sign.isEmpty()) {
            list.add(new BasicNameValuePair("sign", sign));
        }

        if (signType!=null && !signType.isEmpty()) {
            list.add(new BasicNameValuePair("sign_type", signType));
        }

        if (terminalTimestamp!=null && !terminalTimestamp.isEmpty()) {
            list.add(new BasicNameValuePair("terminal_timestamp", terminalTimestamp));
        }

        if (timestamp!=null && !timestamp.isEmpty()) {
            list.add(new BasicNameValuePair("timestamp", timestamp));
        }

        return list;
    }

    @Override
    public String toString() {
        return "CancelReq{" +
                "service='" + service + '\'' +
                ", partner='" + partner + '\'' +
                ", inputCharset='" + inputCharset + '\'' +
                ", signType='" + signType + '\'' +
                ", sign='" + sign + '\'' +
                ", timestamp='" + timestamp + '\'' +
                ", terminalTimestamp='" + terminalTimestamp + '\'' +
                ", outTradeNo='" + outTradeNo + '\'' +
                '}';
    }
}
