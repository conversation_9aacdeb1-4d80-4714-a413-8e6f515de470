package com.abl.qr.alipay;

import com.abl.qr.utils.Utils;
import lombok.Getter;
import lombok.Setter;

import java.io.UnsupportedEncodingException;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 15/5/17.
 */
public class AlipayResult {
    @Getter @Setter
    private String extraCommonParameter;
    @Getter @Setter
    private String subject;
    @Getter @Setter
    private String tradeNo;
    @Getter @Setter
    private String buyerEmail;
    @Getter @Setter
    private String gmtCreate;
    @Getter @Setter
    private String notifyType;
    @Getter @Setter
    private String quantity;
    @Getter @Setter
    private String outTradeNo;
    @Getter @Setter
    private String notifyTime;
    @Getter @Setter
    private String sellerId;
    @Getter @Setter
    private String tradeStatus;
    @Getter @Setter
    private String totalFee;
    @Getter @Setter
    private String gmtPayment;
    @Getter @Setter
    private String sellerEmail;
    @Getter @Setter
    private String gmtClose;
    @Getter @Setter
    private String price;
    @Getter @Setter
    private String buyerId;
    @Getter @Setter
    private String notifyId;
    @Getter @Setter
    private String signType;
    @Getter @Setter
    private String sign;

    public static AlipayResult parseNotification(String result) throws UnsupportedEncodingException {
        Map<String, String> map=Utils.splitQuery(result);

        AlipayResult alipayResult=new AlipayResult();
        alipayResult.parse(map);
        return alipayResult;
    }

    public void parse(Map<String, String> m) {
        if (m.containsKey("extra_common_param")) extraCommonParameter=m.get("extra_common_param");

        if (m.containsKey("subject")) subject=m.get("subject");

        if (m.containsKey("trade_no")) tradeNo=m.get("trade_no");

        if (m.containsKey("buyer_email")) buyerEmail=m.get("buyer_email");

        if (m.containsKey("gmt_create")) gmtCreate=m.get("gmt_create");

        if (m.containsKey("notify_type")) notifyType=m.get("notify_type");

        if (m.containsKey("quantity")) quantity=m.get("quantity");

        if (m.containsKey("out_trade_no")) outTradeNo=m.get("out_trade_no");

        if (m.containsKey("notify_time")) notifyTime=m.get("notify_time");

        if (m.containsKey("seller_id")) sellerId=m.get("seller_id");

        if (m.containsKey("trade_status")) tradeStatus=m.get("trade_status");

        if (m.containsKey("total_fee")) totalFee=m.get("total_fee");

        if (m.containsKey("gmt_payment")) gmtPayment=m.get("gmt_payment");

        if (m.containsKey("seller_email")) sellerEmail=m.get("seller_email");

        if (m.containsKey("gmt_close")) gmtClose=m.get("gmt_close");

        if (m.containsKey("price")) price=m.get("price");

        if (m.containsKey("buyer_id")) buyerId=m.get("buyer_id");

        if (m.containsKey("notify_id")) notifyId=m.get("notify_id");

        if (m.containsKey("sign_type")) signType=m.get("sign_type");

        if (m.containsKey("sign")) sign=m.get("sign");
    }

    @Override
    public String toString() {
        return "AlipayResult{" +
                "extraCommonParameter='" + extraCommonParameter + '\'' +
                ", subject='" + subject + '\'' +
                ", tradeNo='" + tradeNo + '\'' +
                ", buyerEmail='" + buyerEmail + '\'' +
                ", gmtCreate='" + gmtCreate + '\'' +
                ", notifyType='" + notifyType + '\'' +
                ", quantity='" + quantity + '\'' +
                ", outTradeNo='" + outTradeNo + '\'' +
                ", notifyTime='" + notifyTime + '\'' +
                ", sellerId='" + sellerId + '\'' +
                ", tradeStatus='" + tradeStatus + '\'' +
                ", totalFee='" + totalFee + '\'' +
                ", gmtPayment='" + gmtPayment + '\'' +
                ", sellerEmail='" + sellerEmail + '\'' +
                ", gmtClose='" + gmtClose + '\'' +
                ", price='" + price + '\'' +
                ", buyerId='" + buyerId + '\'' +
                ", notifyId='" + notifyId + '\'' +
                ", signType='" + signType + '\'' +
                ", sign='" + sign + '\'' +
                '}';
    }
}
