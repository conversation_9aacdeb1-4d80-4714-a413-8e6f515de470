package com.abl.qr.alipay.request;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 7/6/17.
 */
public class BusinessExtensionParameters {
    @SerializedName("SECONDARY_MERCHANT_ID")
    @Getter @Setter
    private String secMerchantId;

    @SerializedName("SECONDARY_MERCHANT_NAME")
    @Getter @Setter
    private String secMerchantName;

    @SerializedName("SECONDARY_MERCHANT_INDUSTRY")
    @Getter @Setter
    private String secMerchantIndustry;

    @SerializedName("STORE_ID")
    @Getter @Setter
    private String storeId;

    @SerializedName("STORE_NAME")
    @Getter @Setter
    private String storeName;

    @SerializedName("TERMINAL_ID")
    @Getter @Setter
    private String terminalId;
}
