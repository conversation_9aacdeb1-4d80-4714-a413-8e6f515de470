package com.abl.qr.alipay.response;

import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlValue;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 14/5/17.
 */

@XmlAccessorType(XmlAccessType.FIELD)
public class ReqParam {
    @XmlAttribute(name = "name")
    @Getter
    @Setter
    private String name;
    @XmlValue
    @Getter @Setter
    private String value;

    @Override
    public String toString() {
        return "ReqParam{" +
                "name='" + name + '\'' +
                ", value='" + value + '\'' +
                '}';
    }
}
