package com.abl.qr.alipay.response;

import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.*;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 8/5/17.
 */

@XmlRootElement( name="alipay" )
@XmlAccessorType(XmlAccessType.FIELD)
public class PreCreateRes {
    @XmlElement( name = "sign_type")
    @Getter @Setter
    private String signType;
    @XmlElement( name = "sign")
    @Getter @Setter
    private String sign;

    @XmlElement( name = "is_success")
    @Getter @Setter
    private String isSuccess;

    @XmlElement( name = "error")
    @Getter @Setter
    private String error;

    @XmlElement( name = "result_code")
    @Getter @Setter
    private String resultCode;

    @XmlElement( name = "detail_error_code")
    @Getter @Setter
    private String detailErrorCode;

    @XmlElement( name = "detail_error_des")
    @Getter @Setter
    private String detailErrorDes;

    @XmlElementWrapper(name="request")
    @XmlElement( name = "param")
    @Getter @Setter
    private List<ReqParam> request;

    @XmlElement
    @Getter @Setter
    private Response response;

    @Override
    public String toString() {
        return "PreCreateRes{" +
                "signType='" + signType + '\'' +
                ", sign='" + sign + '\'' +
                ", isSuccess='" + isSuccess + '\'' +
                ", error='" + error + '\'' +
                ", resultCode='" + resultCode + '\'' +
                ", detailErrorCode='" + detailErrorCode + '\'' +
                ", detailErrorDes='" + detailErrorDes + '\'' +
                ", request='" + request + '\'' +
                ", alipayResponse='" + response + '\'' +
                '}';
    }

    @XmlAccessorType(XmlAccessType.FIELD)
    public static class Response {
        @XmlElement( name="alipay" )
        @Getter @Setter
        private AlipayResponse alipayResponse;

        @XmlAccessorType(XmlAccessType.FIELD)
        public static class AlipayResponse {
            @XmlElement(name = "out_trade_no")
            @Getter
            @Setter
            private String outTradeNo;

            @XmlElement(name = "voucher_type")
            @Getter
            @Setter
            private String voucherType;

            @XmlElement(name = "qr_code")
            @Getter
            @Setter
            private String qrCode;

            @XmlElement(name = "big_pic_url")
            @Getter
            @Setter
            private String bigPicUrl;

            @XmlElement(name = "pic_url")
            @Getter
            @Setter
            private String picUrl;

            @XmlElement(name = "small_pic_url")
            @Getter
            @Setter
            private String smallPicUrl;

            @Override
            public String toString() {
                return "Response{" +
                        "outTradeNo='" + outTradeNo + '\'' +
                        ", voucherType='" + voucherType + '\'' +
                        ", qrCode='" + qrCode + '\'' +
                        ", bigPicUrl='" + bigPicUrl + '\'' +
                        ", picUrl='" + picUrl + '\'' +
                        ", smallPicUrl='" + smallPicUrl + '\'' +
                        '}';
            }
        }

        @Override
        public String toString() {
            return "Response{" +
                    "r=" + alipayResponse +
                    '}';
        }
    }

    /*
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class ReqParam {
        @XmlAttribute(name = "name")
        @Getter @Setter
        private String name;
        @XmlValue
        @Getter @Setter
        private String value;

        @Override
        public String toString() {
            return "ReqParam{" +
                    "name='" + name + '\'' +
                    ", value='" + value + '\'' +
                    '}';
        }
    }*/
}
