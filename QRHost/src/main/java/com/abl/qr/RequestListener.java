package com.abl.qr;

import com.abl.qr.utils.ContextUtils;
import org.apache.log4j.Logger;
import org.jpos.core.Configurable;
import org.jpos.core.Configuration;
import org.jpos.core.ConfigurationException;
import org.jpos.iso.ISOMsg;
import org.jpos.iso.ISORequestListener;
import org.jpos.iso.ISOSource;
import org.jpos.space.Space;
import org.jpos.space.SpaceFactory;
import org.jpos.transaction.Context;

/**
 * Receives incoming request, sets up the context, and hands it to the transaction manager
 */
public class RequestListener implements ISORequestListener, Configurable {
    public static Logger logger= Logger.getLogger(RequestListener.class);

    private Configuration cfg;
    private String queue;
    private String space;

    private Long timeout;

    @Override
    public void setConfiguration(Configuration cfg) throws ConfigurationException {
        this.cfg = cfg;
        logger.debug("Setting up queue.");
        queue = cfg.get("queue");
        space = cfg.get("space");
        timeout = cfg.getLong("timeout");
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    @Override
    public boolean process(ISOSource isoSource, ISOMsg isoMsg) {
        logger.debug("processing");

        Context context = new Context();
        ContextUtils.put(context, ContextUtils.Item.ISO_SOURCE, isoSource);
        ContextUtils.put(context, ContextUtils.Item.REQUEST, isoMsg);

        // put context into space
        Space sp = SpaceFactory.getSpace(space);
        sp.out(queue, context, timeout);

        return true;
    }


}
