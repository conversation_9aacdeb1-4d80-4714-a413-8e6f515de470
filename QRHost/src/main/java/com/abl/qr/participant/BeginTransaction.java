package com.abl.qr.participant;


import com.abl.qr.TxnDetails;
import com.abl.qr.db.AlipayTmlTxnLog;
import com.abl.qr.utils.ContextUtils;
import com.abl.qr.utils.NETSQRUtils;
import org.apache.log4j.Logger;
import org.apache.log4j.MDC;
import org.jpos.transaction.Context;

import java.util.Date;

/**
 * Begins transaction
 * <p/>
 * This should be the first participant.
 */
public class BeginTransaction extends AbstractParticipant {

    private static final Logger logger = Logger.getLogger(BeginTransaction.class);

    @Override
    public void abort(long id, Context context) {
        logger.error("abort");
    }

    @Override
    public void commit(long id, Context context) {
        logger.debug("commit");
    }

    @Override
    public int prepare(long id, Context context) {
        // setup the log4j diagnostic context, so each logging message is tagged with a
        // different id to make it easier to trace
        MDC.put("id", id);

        logger.debug("prepare");

        TxnDetails txnDetails = new TxnDetails();
        ContextUtils.put(context, ContextUtils.Item.TXN_DETAILS, txnDetails);
        AlipayTmlTxnLog txnLog =new AlipayTmlTxnLog();
        ContextUtils.put(context, ContextUtils.Item.TXN_LOG, txnLog);
        // set tms date/time
        // note: millisec is set to 0 bec ISO msg date/time fields do not have millisec
        Date date = NETSQRUtils.removeMillisec(new Date());
        txnDetails.setTxnDateTime(date);


        return PREPARED | READONLY | NO_JOIN;
    }

}
