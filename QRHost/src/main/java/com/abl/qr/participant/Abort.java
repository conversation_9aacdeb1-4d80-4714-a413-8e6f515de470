package com.abl.qr.participant;

import com.abl.qr.QRHost;
import com.abl.qr.db.AlipayTmlTxnLog;
import com.abl.qr.db.DBUtils;
import com.abl.qr.utils.ContextUtils;
import com.abl.qr.utils.TxnResult;
import org.apache.log4j.Logger;
import org.jpos.iso.ISOMsg;
import org.jpos.transaction.Context;

/**
 * This participant simply performs abort
 */
public class Abort extends AbstractParticipant {

    private static final Logger logger = Logger.getLogger(Abort.class);

    @Override
    public void abort(long id, Context context) {
        logger.error("abort");
    }

    @Override
    public void commit(long id, Context context) {
        logger.debug("commit");
    }

    @Override
    public int prepare(long id, Context context) {
        logger.debug("prepare");

        ISOMsg request = (ISOMsg) context.get(ContextUtils.Item.REQUEST);
        AlipayTmlTxnLog txnLog=(AlipayTmlTxnLog)context.get(ContextUtils.Item.TXN_LOG);
        try {
            txnLog.setMTI(request.getMTI());
            txnLog.setProcCode(request.getString(3));
            txnLog.setSTAN(request.getString(11));

            DBUtils.logAlipayTmlTransactions(QRHost.getDataSource(), txnLog);
        } catch (Exception e) {
            logger.error(e.toString());
        }
        ContextUtils.putIfDoesNotExist(context, ContextUtils.Item.RESULT_CODE, TxnResult.ERROR);
        return ABORTED;
    }

}
