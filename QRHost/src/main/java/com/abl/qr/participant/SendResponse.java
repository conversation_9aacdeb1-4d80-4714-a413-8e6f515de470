package com.abl.qr.participant;

import com.abl.qr.utils.ContextUtils;
import com.abl.qr.utils.TxnResult;
import org.apache.log4j.Logger;
import org.jpos.core.Configuration;
import org.jpos.core.ConfigurationException;
import org.jpos.iso.ISOMsg;
import org.jpos.iso.ISOSource;
import org.jpos.transaction.AbortParticipant;
import org.jpos.transaction.Context;

import java.io.Serializable;

public class SendResponse extends AbstractParticipant implements AbortParticipant {

    private static final Logger logger = Logger.getLogger(SendResponse.class);

    private boolean sendOnError = true;    // true means to send alipayResponse even when an error has occurred

    @Override
    public void setConfiguration(Configuration cfg)
            throws ConfigurationException {
        super.setConfiguration(cfg);

        String cfgSendOnError = cfg.get("sendOnError", null);
        if (cfgSendOnError != null) {
            sendOnError = cfgSendOnError.equalsIgnoreCase("true");
        }
    }

    @Override
    public int prepareForAbort(long id, Serializable context) {
        logger.error("prepareForAbort");
        return PREPARED | READONLY;
    }

    @Override
    public void abort(long id, Context context) {
        logger.error("abort");

        if (!sendOnError) {
            logger.error("not sending alipayResponse on error");
            return;
        }

        ISOMsg response = (ISOMsg) context.get(ContextUtils.Item.RESPONSE);
        if (response == null) {
            // no alipayResponse
            logger.warn("no alipayResponse found");
            return;
        }

        ISOSource isoSource = (ISOSource) context.get(ContextUtils.Item.ISO_SOURCE);
        try {
            isoSource.send(response);
        } catch (Exception e) {
            logger.error("unable to send alipayResponse", e);
        }
    }

    @Override
    public void commit(long id, Context context) {
        logger.debug("commit");
    }

    @Override
    public int prepare(long id, Context context) {
        logger.debug("prepare");

        ISOMsg response = (ISOMsg) context.get(ContextUtils.Item.RESPONSE);
        if (response == null) {
            // no alipayResponse, assume this is intentional
            // e.g. for batch uploads, only respond at the last message of each batch
            ContextUtils.putIfDoesNotExist(context, ContextUtils.Item.RESULT_CODE, TxnResult.APPROVED);
            return PREPARED;
        }

        ISOSource isoSource = (ISOSource) context.get(ContextUtils.Item.ISO_SOURCE);
        try {
            isoSource.send(response);
        } catch (Exception e) {
            logger.error("unable to send alipayResponse", e);
            ContextUtils.putIfDoesNotExist(context, ContextUtils.Item.RESULT_CODE, TxnResult.SYSERR);
            return ABORTED;
        }

        return PREPARED;
    }
}
