package com.abl.qr.participant;

import com.abl.qr.QRHost;
import com.abl.qr.db.AlipayTmlTxnLog;
import com.abl.qr.db.DBUtils;
import com.abl.qr.utils.ContextUtils;
import org.apache.log4j.Logger;
import org.jpos.transaction.Context;

public class InsertTxnLog extends AbstractParticipant {

    private static final Logger logger = Logger.getLogger(InsertTxnLog.class);

    @Override
    public void abort(long id, Context context) {
        logger.error("abort");
    }

    @Override
    public void commit(long id, Context context) {
        logger.debug("commit");
    }

    @Override
    public int prepare(long id, Context context) {
        logger.debug("prepare");

        //ISOMsg msg=(ISOMsg)ContextUtils.get(context, ContextUtils.Item.REQUEST);
        //TxnDetails txnDetails = (TxnDetails) context.get(ContextUtils.Item.TXN_DETAILS);
        AlipayTmlTxnLog txnLog=(AlipayTmlTxnLog)context.get(ContextUtils.Item.TXN_LOG);
        txnLog.setRespCode("  ");

        DBUtils.logAlipayTmlTransactions(QRHost.getDataSource(), txnLog);

        return PREPARED;
    }


}
