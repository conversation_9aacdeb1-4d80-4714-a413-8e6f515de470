package com.abl.qr.participant;

import com.abl.qr.QRHost;
import com.abl.qr.TxnDetails;
import com.abl.qr.alipay.request.BusinessExtensionParameters;
import com.abl.qr.alipay.request.PreCreateReq;
import com.abl.qr.alipay.response.PreCreateRes;
import com.abl.qr.db.AlipayHostTxnLog;
import com.abl.qr.db.AlipayTmlTxnLog;
import com.abl.qr.db.DBUtils;
import com.abl.qr.utils.*;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.apache.commons.configuration.PropertiesConfiguration;
import org.apache.log4j.Logger;
import org.jpos.iso.ISOMsg;
import org.jpos.transaction.Context;

import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * User: k<PERSON><PERSON><PERSON><PERSON>
 *
 * Register the QR Data with NETS. Upon successful registration, send confirmation to PIN pad.
 */
public class RegisterQRPayment extends AbstractParticipant {
    public static Logger logger= Logger.getLogger(RegisterQRPayment.class);

    public static Gson gson;
    static {
        gson=new Gson();
    }

    @Override
    public void abort(long id, Context context) {
        logger.debug("abort");
    }

    @Override
    public void commit(long id, Context context) {
        logger.debug("commit");
    }

    @Override
    public int prepare(long id, Context context) {
        // Prepare request to SingTel Dash host
        logger.debug("prepare");
        ISOMsg msg=(ISOMsg) ContextUtils.get(context, ContextUtils.Item.REQUEST);
        TxnDetails txnDetails = (TxnDetails) context.get(ContextUtils.Item.TXN_DETAILS);
        AlipayTmlTxnLog txnLog=(AlipayTmlTxnLog)context.get(ContextUtils.Item.TXN_LOG);
        ISOMsg isoResp=(ISOMsg)ContextUtils.get(context, ContextUtils.Item.RESPONSE);

        String procCode=txnLog.getProcCode();
        String amount= Utils.centsToDollar(msg.getString(4));
        String STAN=txnLog.getSTAN();
        String transactionId=txnDetails.getTransactionId()+AlipayUtils.TXN_REF_DELIMITER+STAN;
        String merchantParam=msg.getString(61).trim();
        if (merchantParam==null || merchantParam.length()<53) {
            logger.warn("Unable to extract private data! ");
            merchantParam=null;
        }

        logger.debug("processing code:"+procCode+  " - " +ProcessingCode.ALIPAY_REGISTER );

        if (procCode.equals(ProcessingCode.ALIPAY_REGISTER.toString())) {
            logger.debug("processing alipay register");
            if (processAlipayRegister(transactionId,amount,QRHost.config, isoResp, txnLog, context, merchantParam)<0) {
                return ABORTED;
            }
        } else {
            logger.error("Unknown processing code "+procCode);
            return ABORTED;
        }
        return PREPARED;
    }

    private int processAlipayRegister(String refStr, String amtStr,
                                      PropertiesConfiguration properties, ISOMsg isoResp,
                                      AlipayTmlTxnLog txnLog, Context context, String merchantParam) {

        logger.debug("Alipay Precreate with trade no:"+refStr);

        AlipayHostTxnLog alipayHostTxnLog=new AlipayHostTxnLog();
        alipayHostTxnLog.setMessageType("precreate");

        try {
            PreCreateReq preCreate=new PreCreateReq();
            preCreate.setCurrency(properties.getString("alipay.currency","SGD"));
            preCreate.setTransCurrency(properties.getString("alipay.currency","SGD"));
            preCreate.setPartner(properties.getString("alipay.partner.id"));
            preCreate.setInputCharset(properties.getString("alipay.charset","UTF-8"));
            preCreate.setOutTradeNo(refStr);
            preCreate.setNotifyUrl(properties.getString("alipay.notify.url"));
            preCreate.setPassbackParameters(refStr);
            preCreate.setService(properties.getString("alipay.precreate.service","alipay.acquire.precreate"));
            preCreate.setSubject(properties.getString("alipay.subject", "Comfort Taxi"));
            preCreate.setProductCode(properties.getString("alipay.product.code","OVERSEAS_MBARCODE_PAY"));

            BusinessExtensionParameters bep=new BusinessExtensionParameters();
            if (merchantParam!=null) {
                logger.debug("merchantParam:"+merchantParam);

                String secMerchantName=merchantParam.substring(0,12)+merchantParam.substring(21,21+10);
                String sedMerchantId=merchantParam.substring(12,12+9)+merchantParam.substring(31,31+22);

                bep.setSecMerchantId(sedMerchantId);
                bep.setSecMerchantName(secMerchantName);
                preCreate.setExtendParams(gson.toJson(bep));
            }
            Long currTime=System.currentTimeMillis();
            preCreate.setTimestamp(currTime.toString());
            preCreate.setTotalFee(amtStr);

            String preSignString=preCreate.generatePreSignString();
            String md5Signature=properties.getString("alipay.md5.signature");

            String sign1= AlipayUtils.MD5Sign1(preSignString+md5Signature, "MD5");
            preCreate.setSignType(properties.getString("alipay.sign.type","MD5"));
            preCreate.setSign(sign1);

            PreCreateRes result= (PreCreateRes) AlipayUtils.postRequest(properties, preCreate.getNameValuePair(), PreCreateRes.class);

            // Log to db
            alipayHostTxnLog.setAlipayReferenceNumber(refStr);
            alipayHostTxnLog.setAmount(Integer.toString(txnLog.getAmount()));
            alipayHostTxnLog.setVehicleId(txnLog.getTaxiNumber());
            alipayHostTxnLog.setJobId(txnLog.getJobNumber());
            alipayHostTxnLog.setDriverId(txnLog.getDriverId());
            alipayHostTxnLog.setSerialNo(txnLog.getPinpadSerial());
            //alipayHostTxnLog.setTransDateTime(Utils.format(new Date(),"ddMMyy HHmmss"));
            alipayHostTxnLog.setAlipayRequestMessage(preCreate.toString());

            if (result!=null) {
                alipayHostTxnLog.setTransmitStatus("OK");
                alipayHostTxnLog.setAlipayResponseResult(result.getResultCode());
                alipayHostTxnLog.setAlipayResponseMessage(result.toString());

                if (result.getIsSuccess().equals("T") && (result.getResponse().getAlipayResponse().getQrCode()!=null)) {
                    String authCode= Utils.format(new Date(), "HHmmss");

                    logger.debug("Setting alipayResponse to be APPROVED");
                    ContextUtils.put(context, ContextUtils.Item.RESULT_CODE, TxnResult.APPROVED);
                    isoResp.set(48, result.getResponse().getAlipayResponse().getQrCode());
                    txnLog.setApprovalCode(authCode);
                    txnLog.setQrData(result.getResponse().getAlipayResponse().getQrCode());
                    txnLog.setRespCode(TxnResult.APPROVED);

                    alipayHostTxnLog.setAlipayReferenceNumber(result.getResponse().getAlipayResponse().getOutTradeNo());
                } else {
                    logger.error("Error alipayResponse for register payment for "+refStr+":"+result.getDetailErrorCode());
                    ContextUtils.put(context, ContextUtils.Item.RESULT_CODE, TxnResult.REGISTER_ERR);
                    txnLog.setRespCode(TxnResult.REGISTER_ERR);
                }

                DBUtils.logAlipayHostTxnLog(QRHost.getDataSource(), alipayHostTxnLog);

            } else {
                alipayHostTxnLog.setAlipayResponseResult("No result");
                // Precreate failed
                ContextUtils.put(context, ContextUtils.Item.RESULT_CODE, TxnResult.REGISTER_ERR);
                txnLog.setRespCode(TxnResult.REGISTER_ERR);
                logger.error("Unable to precreate, result is null!");

                DBUtils.logAlipayHostTxnLog(QRHost.getDataSource(), alipayHostTxnLog);

                return -1;
            }
        } catch (Exception e) {
            // Log to db
            alipayHostTxnLog.setAlipayReferenceNumber(refStr);
            alipayHostTxnLog.setAmount(Integer.toString(txnLog.getAmount()));
            alipayHostTxnLog.setVehicleId(txnLog.getTaxiNumber());
            alipayHostTxnLog.setJobId(txnLog.getJobNumber());
            alipayHostTxnLog.setDriverId(txnLog.getDriverId());
            alipayHostTxnLog.setSerialNo(txnLog.getPinpadSerial());

            alipayHostTxnLog.setTransmitStatus("ERROR:"+e.toString());

            ContextUtils.put(context, ContextUtils.Item.RESULT_CODE, TxnResult.SYSERR);
            txnLog.setRespCode(TxnResult.SYSERR);
            logger.error("Error sending precreate",e);
            return -1;
        } finally {

        }

        return 0;
    }
}
