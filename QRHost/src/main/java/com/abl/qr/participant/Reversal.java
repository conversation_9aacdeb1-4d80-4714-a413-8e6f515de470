package com.abl.qr.participant;

import com.abl.qr.QRHost;
import com.abl.qr.TxnDetails;
import com.abl.qr.db.AlipayTmlTxnLog;
import com.abl.qr.db.DBUtils;
import com.abl.qr.utils.*;
import org.apache.log4j.Logger;
import org.jpos.iso.ISOMsg;
import org.jpos.transaction.Context;

import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * User: kohtz<PERSON>ei
 * Date: 6/10/13
 * Time: 11:14 PM
 * To change this template use File | Settings | File Templates.
 */
public class Reversal extends AbstractParticipant {
    public static Logger logger= Logger.getLogger(Reversal.class);

    @Override
    public void abort(long id, Context context) {
        //To change body of implemented methods use File | Settings | File Templates.
    }

    @Override
    public void commit(long id, Context context) {
        //To change body of implemented methods use File | Settings | File Templates.
    }

    @Override
    public int prepare(long id, Context context) {
        ISOMsg msg=(ISOMsg) ContextUtils.get(context, ContextUtils.Item.REQUEST);
        TxnDetails txnDetails = (TxnDetails) context.get(ContextUtils.Item.TXN_DETAILS);
        AlipayTmlTxnLog txnLog=(AlipayTmlTxnLog)context.get(ContextUtils.Item.TXN_LOG);
        ISOMsg isoResp=(ISOMsg)ContextUtils.get(context, ContextUtils.Item.RESPONSE);

        String authCode= ThreadSafeSimpleDateFormat.getInstance("HHmmss").format(new Date());

        String procCode=msg.getString(3);

        boolean doReversal=QRHost.config.getBoolean("alipay.send.reversal",true);
        logger.debug("doReversal:"+doReversal);

        // When checking the response, need to take the last STAN of the previous transction
        // used to register the alipay txn
        if (doReversal) {
            String STAN = DBUtils.getSTANofLastAlipayPrecreate(QRHost.getDataSource(), txnLog.getDriverId(), txnLog.getJobNumber(), txnLog.getTaxiNumber());

            if (STAN == null) {
                logger.warn("Unable to find STAN of last alipay precreate txn from tml log! " + txnLog.getDriverId() +
                        "," + txnLog.getJobNumber() + "," + txnLog.getTaxiNumber());
                // Set STAN back to 0
                STAN = "";
            }

            String transactionId = txnDetails.getTransactionId() + AlipayUtils.TXN_REF_DELIMITER + STAN;


            if (procCode.equals(ProcessingCode.ALIPAY_REGISTER.toString())) {
                logger.debug("Processing Alipay Reversal for Register " + transactionId);
                AlipayUtils.doCancelTransaction(transactionId, txnLog, QRHost.config);
            } else if (procCode.equals(ProcessingCode.ALIPAY_PAYMENT.toString())) {
                logger.debug("Processing Alipay Reversal for Payment  " + transactionId);
                AlipayUtils.doCancelTransaction(transactionId, txnLog, QRHost.config);
            } else {
                logger.debug("Unknown processing code");
            }
        } else {
            logger.warn("Reversal turned off! Not sending reversal to alipay for "+txnDetails.getTransactionId());
        }

        try {
            // Todo: Log message here

            isoResp.set(38, authCode);
        } catch (Exception e) {
            logger.error(e.toString());
        }

        // set as approved regardless of what happen
        ContextUtils.put(context, ContextUtils.Item.RESULT_CODE, TxnResult.APPROVED);
        txnLog.setRespCode(TxnResult.APPROVED);

        txnLog.setApprovalCode(authCode);

        return PREPARED;  //To change body of implemented methods use File | Settings | File Templates.
    }
}
