package com.abl.qr.participant;

import com.abl.qr.QRHost;
import com.abl.qr.db.AlipayTmlTxnLog;
import com.abl.qr.db.DBUtils;
import com.abl.qr.utils.ContextUtils;
import org.apache.log4j.Logger;
import org.jpos.iso.ISOMsg;
import org.jpos.transaction.AbortParticipant;
import org.jpos.transaction.Context;

import java.io.Serializable;
import java.util.Date;

public class SaveTxn extends AbstractParticipant implements AbortParticipant {

    private static final Logger logger = Logger.getLogger(SaveTxn.class);

    @Override
    public int prepareForAbort(long id, Serializable context) {
        logger.error("prepareForAbort");
        return PREPARED | READONLY;
    }

    @Override
    public void abort(long id, Context context) {
        logger.error("abort");
    }

    @Override
    public void commit(long id, Context context) {
        logger.debug("commit");
    }

    @Override
    public int prepare(long id, Context context) {
        logger.debug("prepare");

        ISOMsg msg=(ISOMsg) ContextUtils.get(context, ContextUtils.Item.RESPONSE);
        AlipayTmlTxnLog txnLog=(AlipayTmlTxnLog)context.get(ContextUtils.Item.TXN_LOG);
        try {
            txnLog.setMTI(msg.getMTI());
            txnLog.setLogDateTime(new Date());
            txnLog.setRespDateTime(new Date());
            DBUtils.logAlipayTmlTransactions(QRHost.getDataSource(), txnLog);
        } catch (Exception e) {
            logger.error(e.toString());
        }

        return PREPARED;
    }
}
