package com.abl.qr.participant;


import com.abl.qr.TxnDetails;
import com.abl.qr.db.AlipayTmlTxnLog;
import com.abl.qr.utils.ContextUtils;
import com.abl.qr.utils.NETSQRUtils;
import com.abl.qr.utils.TxnResult;
import org.apache.log4j.Logger;
import org.jpos.iso.ISOException;
import org.jpos.iso.ISOMsg;
import org.jpos.transaction.Context;

/**
 * Created with IntelliJ IDEA.
 * User: kohtzewei
 * Date: 5/10/13
 * Time: 4:22 PM
 * To change this template use File | Settings | File Templates.
 */
public class CreateResponse extends AbstractParticipant  {
    public static Logger logger = Logger.getLogger(CreateResponse.class);

    @Override
    public void abort(long id, Context context) {
        logger.debug("abort");
    }

    @Override
    public void commit(long id, Context context) {
        logger.debug("commit");
    }

    @Override
    public int prepare(long id, Context context) {
        ISOMsg request = (ISOMsg) context.get(ContextUtils.Item.REQUEST);
        ISOMsg response;
        try {
            response = createResponse(request);
        } catch (ISOException e) {
            logger.error("unable to create alipayResponse", e);
            ContextUtils.put(context, ContextUtils.Item.RESULT_CODE, TxnResult.SYSERR);
            return ABORTED;
        }

        ContextUtils.put(context, ContextUtils.Item.RESPONSE, response);

        try {
            prepareISOMsg(response, context);
            ContextUtils.put(context, ContextUtils.Item.RESULT_CODE, TxnResult.PROCESSING);
        } catch (Exception e) {
            logger.error("unable to prepare alipayResponse", e);
            ContextUtils.put(context, ContextUtils.Item.RESULT_CODE, TxnResult.SYSERR);
            return ABORTED;
        }

        return PREPARED;  //To change body of implemented methods use File | Settings | File Templates.
    }

    private ISOMsg createResponse(ISOMsg request) throws ISOException {
        ISOMsg response = new ISOMsg();
        response.setMTI(request.getMTI());
        response.setHeader(request.getHeader());
        response.setPackager(request.getPackager());
        response.setDirection(request.getDirection());
        response.setResponseMTI();
        return response;
    }

    private void prepareISOMsg(ISOMsg isoMsg, Context context) throws Exception {

        ISOMsg request = (ISOMsg) context.get(ContextUtils.Item.REQUEST);
        TxnDetails txnDetails = (TxnDetails) context.get(ContextUtils.Item.TXN_DETAILS);
        AlipayTmlTxnLog txnLog=(AlipayTmlTxnLog)context.get(ContextUtils.Item.TXN_LOG);

        String rrn= NETSQRUtils.generateRRN();
        String STAN=request.getString(11).trim();
        String TID=request.getString(41).trim();
        String procCode=request.getString(3);
        String amount=request.getString(4);
        isoMsg.set(3, procCode);
        isoMsg.set(4, amount);
        isoMsg.set(41, TID);
        isoMsg.set(42, request.getString(42));
        isoMsg.set(11, request.getString(11));
        isoMsg.set(12, NETSQRUtils.format(txnDetails.getTxnDateTime(),"HHmmss")); //HHMMSS
        isoMsg.set(13, NETSQRUtils.format(txnDetails.getTxnDateTime(),"MMdd")); //MMDD
        isoMsg.set(61, request.getString(61));
        isoMsg.set(37, rrn); //Default RRN

        if (request.getString(61).length()<73)
            throw new Exception("Private data file length error!");

        txnDetails.setPrivateDataFile(request.getString(61));
        //txnDetails.setTransactionId(NETSQRUtils.CDG_TXN_PREFIX+txnDetails.get("TaxiNumber").trim()+"_"+txnDetails.get("JobNumber"));

        // 17 Aug 2017: Requested by Comfort to change to the following:
        // Taxinumber(12) ||driverID(9) ||jobnumber(10) ||FareAmt(6) || GST(6) || Admin(6) ||CompanyCode(4)
        txnDetails.setTransactionId(txnDetails.get("TaxiNumber")+txnDetails.get("DriverId")+txnDetails.get("JobNumber")+txnDetails.get("FareAmount")+txnDetails.get("FareGST")+txnDetails.get("FareAdmin")+txnDetails.get("CompanyCode"));

        txnDetails.setTid(request.getString(41).trim());

        txnLog.setCompanyCode(txnDetails.get("CompanyCode"));
        txnLog.setDriverId(txnDetails.get("DriverId"));
        txnLog.setFareAmount(txnDetails.get("FareAmount"));
        txnLog.setFareAdmin(txnDetails.get("FareAdmin"));
        txnLog.setFareGst(txnDetails.get("FareGST"));
        txnLog.setPinpadSerial(txnDetails.get("PPSerialNumber"));
        txnLog.setJobNumber(txnDetails.get("JobNumber"));
        txnLog.setTaxiNumber(txnDetails.get("TaxiNumber"));
        txnLog.setMTI(request.getMTI());
        txnLog.setTID(TID);
        txnLog.setSTAN(STAN);
        txnLog.setProcCode(procCode);
        try {
            int amtInt=Integer.parseInt(amount);
            txnLog.setAmount(amtInt);
        } catch (NumberFormatException e) {
            logger.error("Unable to convert "+amount+" to int.");
            logger.error(e.toString());
        }
        txnLog.setLogDateTime(txnDetails.getTxnDateTime());
        txnLog.setRrn(rrn);
        txnLog.setApprovalCode("      ");

    }

}
