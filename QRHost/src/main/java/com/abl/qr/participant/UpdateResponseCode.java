package com.abl.qr.participant;

import com.abl.qr.utils.ContextUtils;
import com.abl.qr.utils.TxnResult;
import org.apache.log4j.Logger;
import org.jpos.iso.ISOException;
import org.jpos.iso.ISOMsg;
import org.jpos.transaction.AbortParticipant;
import org.jpos.transaction.Context;

import java.io.Serializable;

public class UpdateResponseCode extends AbstractParticipant implements AbortParticipant {

    private static final Logger logger = Logger.getLogger(UpdateResponseCode.class);

    @Override
    public int prepareForAbort(long id, Serializable context) {
        logger.error("prepareForAbort");
        return PREPARED | READONLY;
    }

    @Override
    public void abort(long id, Context context) {
        logger.error("abort");

        String resultCode = (String) context.get(ContextUtils.Item.RESULT_CODE);
        if (resultCode == null) {
            logger.error("resultCode == null, setting resultCode to ERROR");
            resultCode = TxnResult.ERROR;
        }

        setResponseCode(context, resultCode);
    }

    @Override
    public void commit(long id, Context context) {
        logger.debug("commit");
    }

    @Override
    public int prepare(long id, Context context) {
        logger.debug("prepare");

        String resultCode = (String) context.get(ContextUtils.Item.RESULT_CODE);
        if (resultCode == null) {
            resultCode = TxnResult.APPROVED;
        }

        if (!setResponseCode(context, resultCode)) {
            return ABORTED;
        }

        return PREPARED;
    }

    protected boolean setResponseCode(Context context, String resultCode) {
        ISOMsg response = (ISOMsg) context.get(ContextUtils.Item.RESPONSE);
        if (response == null) {
            logger.warn("no alipayResponse found");
            return true;
        }

        try {
            logger.debug("Setting alipayResponse "+resultCode);
            response.set(39, resultCode);
        } catch (Exception e) {
            logger.error("unable to set alipayResponse code", e);
            ContextUtils.put(context, ContextUtils.Item.RESULT_CODE, TxnResult.SYSERR);
            return false;
        }

        return true;
    }

}
