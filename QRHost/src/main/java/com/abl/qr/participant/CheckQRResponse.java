package com.abl.qr.participant;


import com.abl.qr.QRHost;
import com.abl.qr.TxnDetails;
import com.abl.qr.alipay.AlipayResult;
import com.abl.qr.alipay.request.QueryReq;
import com.abl.qr.alipay.response.QueryRes;
import com.abl.qr.db.AlipayHostTxnLog;
import com.abl.qr.db.AlipayTmlTxnLog;
import com.abl.qr.db.DBUtils;
import com.abl.qr.utils.*;
import org.apache.log4j.Logger;
import org.jpos.iso.ISOMsg;
import org.jpos.transaction.Context;

import javax.jms.JMSException;
import java.io.UnsupportedEncodingException;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * User: kohtzewei
 * Date: 6/10/13
 * Time: 4:54 PM
 * To change this template use File | Settings | File Templates.
 */
public class CheckQRResponse extends AbstractParticipant {
    public static Logger logger= Logger.getLogger(CheckQRResponse.class);

    @Override
    public void abort(long id, Context context) {
        //To change body of implemented methods use File | Settings | File Templates.
    }

    @Override
    public void commit(long id, Context context) {
        //To change body of implemented methods use File | Settings | File Templates.
    }

    @Override
    public int prepare(long id, Context context) {
        ISOMsg msg=(ISOMsg) ContextUtils.get(context, ContextUtils.Item.REQUEST);
        TxnDetails txnDetails = (TxnDetails) context.get(ContextUtils.Item.TXN_DETAILS);
        ISOMsg isoResp=(ISOMsg)ContextUtils.get(context, ContextUtils.Item.RESPONSE);
        AlipayTmlTxnLog txnLog=(AlipayTmlTxnLog)context.get(ContextUtils.Item.TXN_LOG);

        String procCode=msg.getString(3);

        // When checking the response, need to take the last STAN of the previous transction
        // used to register the alipay txn
        String STAN=DBUtils.getSTANofLastAlipayPrecreate(QRHost.getDataSource(), txnLog.getDriverId(),txnLog.getJobNumber(), txnLog.getTaxiNumber());

        if (STAN==null) {
            logger.warn("Unable to find STAN of last alipay precreate txn from tml log! "+txnLog.getDriverId()+
                    ","+txnLog.getJobNumber()+","+txnLog.getTaxiNumber());
            // Set STAN back to 0
            STAN="";
        }
        String transactionId=txnDetails.getTransactionId()+AlipayUtils.TXN_REF_DELIMITER+STAN;

        logger.debug("processing code:"+procCode+  " - " +ProcessingCode.ALIPAY_PAYMENT + " transactionID:"+transactionId );

        if (procCode.equals(ProcessingCode.ALIPAY_PAYMENT.toString())) {
            logger.debug("Checking alipay host");
            if (processAlipay(transactionId, isoResp, txnLog, context)<0) {
                return ABORTED;
            }
        //} else if (procCode.equals(ProcessingCode.NETSQR_PAYMENT.toString())) {
            //processNETSQR();
        } else {
            logger.error("Unknown Processing code in CheckQRResponse - "+procCode);

            return ABORTED;
        }

        return PREPARED;  //To change body of implemented methods use File | Settings | File Templates.
    }

    /**
     * There is a possiblity that the post request to Alipay takes a longer time to get a response, in such case the
     * total elapse time may exceed and the terminal may have timed out.
     *
     * @param transactionId
     * @param isoResp
     * @param txnLog
     * @param context
     * @return
     */
    private int processAlipay(String transactionId, ISOMsg isoResp, AlipayTmlTxnLog txnLog, Context context) {
        MQClient mqClient=new MQClient(QRHost.config);
        String authCode=NETSQRUtils.format(new Date(),"HHmmss");

        int tryCount=0;
        int maxTry=QRHost.config.getInt("mq.alipay.try.count",4);
        long timeout=QRHost.config.getLong("mq.max.wait.in.seconds",5)*1000;

        long startTime=System.currentTimeMillis();
        String mqtopic=QRHost.config.getString("mq.alipay.topic","");

        boolean postReversal=false, toExit=false;

        do {
            tryCount++;
            logger.debug(tryCount+"/"+maxTry+" - Connecting to MQ topic:"+mqtopic);

            postReversal=false; // Start with false. If during the check that need to do reversal, then try.
            QRMessage resp=null;
            try {
                mqClient.connect(mqtopic);
                resp = mqClient.checkResponse(transactionId.trim(), timeout);
            } catch (JMSException e) {
                logger.error("JMS Exception while processing "+transactionId,e);
                try {
                    logger.debug("Waiting for timeout "+timeout/1000 +"s");
                    Thread.sleep(timeout);
                } catch (InterruptedException e1) {
                }
            } finally {
                mqClient.close();
            }

            if (resp != null) {
                toExit = true; // Already got the response from post back url via MQ, set toExit
                try {
                    AlipayResult alipayResult = AlipayResult.parseNotification(resp.getMessage());

                    logger.debug("TradeStatus:" + alipayResult.getTradeStatus());
                    toExit = true;

                    if (alipayResult.getTradeNo() != null) {
                        isoResp.set(38, authCode);
                        String rrn = alipayResult.getTradeNo();
                        if (rrn == null) {
                            rrn = "000000000000";
                        } else {
                            if (rrn.length() > 12)
                                rrn = rrn.substring(rrn.length() - 12);
                        }
                        String marketingMessage = getEmptyField48();
                        isoResp.set(37, rrn);
                        isoResp.set(48, marketingMessage);
                        ContextUtils.put(context, ContextUtils.Item.RESULT_CODE, TxnResult.APPROVED);
                        txnLog.setRespCode(TxnResult.APPROVED);
                        txnLog.setApprovalCode(authCode);
                        txnLog.setRrn(rrn);
                    } else {
                        ContextUtils.put(context, ContextUtils.Item.RESULT_CODE, TxnResult.HOST_DECLINED);
                        txnLog.setRespCode(TxnResult.HOST_DECLINED);
                    }
                } catch (UnsupportedEncodingException e) {
                    logger.error("Error in parsing response test while processing "+transactionId,e);
                    logger.error("setting to postReversal");

                    ContextUtils.put(context, ContextUtils.Item.RESULT_CODE, TxnResult.SYSERR);
                    txnLog.setRespCode(TxnResult.SYSERR);

                    postReversal=true;
                }
            } else {
                // try to poll for result
                AlipayHostTxnLog alipayHostTxnLog = new AlipayHostTxnLog();
                alipayHostTxnLog.setMessageType("query");

                try {
                    // Do a query to check status first.
                    QueryReq queryReq = new QueryReq();
                    queryReq.setInputCharset(QRHost.config.getString("alipay.charset", "UTF-8"));
                    queryReq.setService(QRHost.config.getString("alipay.query.service", "alipay.acquire.overseas.query"));
                    queryReq.setPartner(QRHost.config.getString("alipay.partner.id"));
                    queryReq.setPartnerTransId(transactionId);

                    String preSignString = queryReq.generatePreSignString();
                    String md5Signature = QRHost.config.getString("alipay.md5.signature");

                    String sign1 = AlipayUtils.MD5Sign1(preSignString + md5Signature, "MD5");
                    queryReq.setSignType(QRHost.config.getString("alipay.sign.type", "MD5"));
                    queryReq.setSign(sign1);

                    QueryRes resp1 = (QueryRes) AlipayUtils.postRequest(QRHost.config, queryReq.getNameValuePair(), QueryRes.class);

                    logger.debug("Alipay Query Response: " + resp1);

                    // Log to db
                    alipayHostTxnLog.setVehicleId(txnLog.getTaxiNumber());
                    alipayHostTxnLog.setJobId(txnLog.getJobNumber());
                    alipayHostTxnLog.setDriverId(txnLog.getDriverId());
                    alipayHostTxnLog.setSerialNo(txnLog.getPinpadSerial());
                    alipayHostTxnLog.setAlipayRequestMessage(queryReq.toString());
                    alipayHostTxnLog.setAmount(Integer.toString(txnLog.getAmount()));
                    alipayHostTxnLog.setAlipayReferenceNumber(transactionId);

                    if (resp1 != null) {
                        alipayHostTxnLog.setTransmitStatus("OK");
                        alipayHostTxnLog.setAlipayResponseMessage(resp1.toString());

                        if (resp1.getResponse().getAlipayResponse().getAlipayTransStatus() != null) {
                            alipayHostTxnLog.setAlipayResponseResult(resp1.getResponse().getAlipayResponse().getAlipayTransStatus());
                        }

                        if ((resp1.getIsSuccess().equalsIgnoreCase("T")) &&
                                (resp1.getResponse().getAlipayResponse().getAlipayTransStatus() != null) &&
                                (resp1.getResponse().getAlipayResponse().getAlipayTransStatus().equals("TRADE_SUCCESS"))) {
                            ContextUtils.put(context, ContextUtils.Item.RESULT_CODE, TxnResult.APPROVED);
                            txnLog.setRespCode(TxnResult.APPROVED);

                            isoResp.set(38, authCode);
                            logger.debug("getting resposponse trans ID");
                            String rrn = resp1.getResponse().getAlipayResponse().getAlipayTransId();
                            if (rrn == null) {
                                rrn = "000000000000";
                            } else {
                                if (rrn.length() > 12)
                                    rrn = rrn.substring(rrn.length() - 12);
                            }
                            isoResp.set(37, rrn);
                            isoResp.set(48, getEmptyField48());
                            txnLog.setApprovalCode(authCode);
                            txnLog.setRrn(rrn);
                            logger.debug("Success, setting toExit to true");
                            toExit = true;
                        } else if ((resp1.getIsSuccess().equalsIgnoreCase("T")) &&
                                (resp1.getResponse().getAlipayResponse().getAlipayTransStatus() != null) &&
                                (resp1.getResponse().getAlipayResponse().getAlipayTransStatus().equals("WAIT_BUYER_PAY"))) {
                            logger.debug("Waiting for buyer to pay.");
                            // Still waiting for buyer to pay.. retry.
                        } else if ((resp1.getIsSuccess().equalsIgnoreCase("T")) &&
                                (resp1.getResponse().getAlipayResponse().getAlipayTransStatus() == null) &&
                                (resp1.getResponse().getAlipayResponse().getDetailErrorCode() != null) &&
                                (resp1.getResponse().getAlipayResponse().getDetailErrorCode().equals("TRADE_NOT_EXIST"))) {
                            logger.debug("TRADE_NOT_EXIST Waiting for buyer to pay.");
                            // Still waiting for buyer to pay.. retry.
                        } else if (resp1.getResponse().getAlipayResponse().getResultCode() != null && resp1.getResponse().getAlipayResponse().getResultCode().equals("FAIL")) {
                            // Failed, can exit, no need to do reversal
                            ContextUtils.put(context, ContextUtils.Item.RESULT_CODE, TxnResult.HOST_DECLINED);
                            txnLog.setRespCode(TxnResult.HOST_DECLINED);
                            toExit = true;
                        } else {
                            // Take it as fail? or retry?
                            if (resp1.getResponse().getAlipayResponse() != null && resp1.getResponse().getAlipayResponse().getDetailErrorCode() != null)
                                logger.debug("Got response:" + resp1.getResponse().getAlipayResponse().getDetailErrorCode());
                            logger.debug("Continue to wait and check for resp..");
                        }
                    } else {
                        // no response, continue..
                        postReversal = true; // As there is no resposne, set post Reversal flag as true.
                        logger.debug("No response for query");
                    }

                    DBUtils.logAlipayHostTxnLog(QRHost.getDataSource(), alipayHostTxnLog);

                } catch (Exception e) {
                    postReversal = true;
                    logger.error("Exception while processing " + transactionId, e);
                    ContextUtils.put(context, ContextUtils.Item.RESULT_CODE, TxnResult.SYSERR);
                    txnLog.setRespCode(TxnResult.SYSERR);

                    alipayHostTxnLog.setAlipayResponseResult("Error:" + e.toString());
                    DBUtils.logAlipayHostTxnLog(QRHost.getDataSource(), alipayHostTxnLog);
                    toExit = true; // Exit if there are some system error.
                }
            }

            if (!toExit && ((System.currentTimeMillis()-startTime)>29*1000)) {
                logger.warn(tryCount+"/"+maxTry+" total elapse time taken > 29 seconds. ");
                toExit=true;
            }
        } while ((tryCount<maxTry) && (!toExit));

        if (tryCount>=maxTry) {
            // If it is still processing at end of retry
            String result=(String)ContextUtils.get(context, ContextUtils.Item.RESULT_CODE);
            if (result.equals(TxnResult.PROCESSING)) {
                postReversal=true;
            }
        }

        if (postReversal) {
            logger.debug("postReversal is set. Reversing "+transactionId);
            AlipayUtils.doCancelTransaction(transactionId, txnLog, QRHost.config);
        }

        return 0;
    }

    private int processAlipayOld(String transactionId, ISOMsg isoResp, AlipayTmlTxnLog txnLog, Context context) {
        MQClient mqClient=new MQClient(QRHost.config);
        String authCode=NETSQRUtils.format(new Date(),"HHmmss");
        long timeout=QRHost.config.getLong("mq.max.wait.in.seconds",29)*1000;
        String mqtopic=QRHost.config.getString("mq.alipay.topic","");
        try {
            logger.debug("Connecting to MQ topic:"+mqtopic);
            mqClient.connect(mqtopic);

            QRMessage resp=mqClient.checkResponse(transactionId.trim(), timeout);
            if (resp!=null) {
                AlipayResult alipayResult=AlipayResult.parseNotification(resp.getMessage());

                logger.debug("TradeStatus:"+alipayResult.getTradeStatus());

                if (alipayResult.getTradeNo()!=null) {
                    isoResp.set(38, authCode);
                    String rrn=alipayResult.getTradeNo();
                    if (rrn==null) {
                        rrn="000000000000";
                    } else {
                        if (rrn.length()>12)
                            rrn=rrn.substring(rrn.length()-12);
                    }
                    String marketingMessage=getEmptyField48();
                    isoResp.set(37, rrn);
                    isoResp.set(48, marketingMessage);
                    ContextUtils.put(context, ContextUtils.Item.RESULT_CODE, TxnResult.APPROVED);
                    txnLog.setRespCode(TxnResult.APPROVED);
                    txnLog.setApprovalCode(authCode);
                    txnLog.setRrn(rrn);
                } else {
                    ContextUtils.put(context, ContextUtils.Item.RESULT_CODE, TxnResult.HOST_DECLINED);
                    txnLog.setRespCode(TxnResult.HOST_DECLINED);
                }
            } else {
                // if time out, call get status message?
                logger.debug("Response is null!");
                boolean postReversal=false;

                AlipayHostTxnLog alipayHostTxnLog=new AlipayHostTxnLog();
                alipayHostTxnLog.setMessageType("query");

                try {
                    // Do a query to check status first.
                    QueryReq queryReq=new QueryReq();
                    queryReq.setInputCharset(QRHost.config.getString("alipay.charset","UTF-8"));
                    queryReq.setService(QRHost.config.getString("alipay.query.service","alipay.acquire.overseas.query"));
                    queryReq.setPartner(QRHost.config.getString("alipay.partner.id"));
                    queryReq.setPartnerTransId(transactionId);

                    String preSignString=queryReq.generatePreSignString();
                    String md5Signature=QRHost.config.getString("alipay.md5.signature");

                    String sign1=AlipayUtils.MD5Sign1(preSignString+md5Signature, "MD5");
                    queryReq.setSignType(QRHost.config.getString("alipay.sign.type","MD5"));
                    queryReq.setSign(sign1);

                    QueryRes resp1= (QueryRes) AlipayUtils.postRequest(QRHost.config, queryReq.getNameValuePair(), QueryRes.class);

                    logger.debug("Alipay Query Response: "+resp1);

                    // Log to db
                    alipayHostTxnLog.setVehicleId(txnLog.getTaxiNumber());
                    alipayHostTxnLog.setJobId(txnLog.getJobNumber());
                    alipayHostTxnLog.setDriverId(txnLog.getDriverId());
                    alipayHostTxnLog.setSerialNo(txnLog.getPinpadSerial());
                    //alipayHostTxnLog.setTransDateTime(Utils.format(new Date(),"ddMMyy HHmmss"));
                    alipayHostTxnLog.setAlipayRequestMessage(queryReq.toString());
                    alipayHostTxnLog.setAmount(Integer.toString(txnLog.getAmount()));
                    alipayHostTxnLog.setAlipayReferenceNumber(transactionId);

                    if (resp1!=null) {
                        alipayHostTxnLog.setTransmitStatus("OK");
                        alipayHostTxnLog.setAlipayResponseMessage(resp1.toString());

                        if (resp1.getResponse().getAlipayResponse().getAlipayTransStatus()!=null) {
                            alipayHostTxnLog.setAlipayResponseResult(resp1.getResponse().getAlipayResponse().getAlipayTransStatus());
                        }

                        if ((resp1.getIsSuccess().equalsIgnoreCase("T")) &&
                                (resp1.getResponse().getAlipayResponse().getAlipayTransStatus()!=null) &&
                                (resp1.getResponse().getAlipayResponse().getAlipayTransStatus().equals("TRADE_SUCCESS"))) {
                            ContextUtils.put(context, ContextUtils.Item.RESULT_CODE, TxnResult.APPROVED);
                            txnLog.setRespCode(TxnResult.APPROVED);

                            isoResp.set(38, authCode);
                            logger.debug("getting response trans ID");
                            String rrn=resp1.getResponse().getAlipayResponse().getAlipayTransId();
                            if (rrn==null) {
                                rrn="000000000000";
                            } else {
                                if (rrn.length()>12)
                                    rrn=rrn.substring(rrn.length()-12);
                            }
                            isoResp.set(37, rrn);
                            isoResp.set(48, getEmptyField48());
                            txnLog.setApprovalCode(authCode);
                            txnLog.setRrn(rrn);
                        } else {
                            postReversal=true;
                            ContextUtils.put(context, ContextUtils.Item.RESULT_CODE, TxnResult.HOST_DECLINED);
                            txnLog.setRespCode(TxnResult.HOST_DECLINED);
                        }
                    } else {
                        // no response, reverse
                        postReversal=true;
                        ContextUtils.put(context, ContextUtils.Item.RESULT_CODE, TxnResult.HOST_DECLINED);
                        txnLog.setRespCode(TxnResult.HOST_DECLINED);

                        logger.debug("No response for query");
                    }

                    DBUtils.logAlipayHostTxnLog(QRHost.getDataSource(), alipayHostTxnLog);

                } catch (Exception e) {
                    postReversal=true;
                    logger.error(e.toString());
                    ContextUtils.put(context, ContextUtils.Item.RESULT_CODE, TxnResult.SYSERR);
                    txnLog.setRespCode(TxnResult.SYSERR);

                    alipayHostTxnLog.setAlipayResponseResult("Error:"+e.toString());
                    DBUtils.logAlipayHostTxnLog(QRHost.getDataSource(), alipayHostTxnLog);
                }

                if (postReversal) {
                    AlipayUtils.doCancelTransaction(transactionId, txnLog, QRHost.config);
                }
            }

        } catch (JMSException e) {
            logger.error("Error in prepare, JMS Exception",e);
            AlipayUtils.doCancelTransaction(transactionId, txnLog, QRHost.config);
            return -1;
        } catch (UnsupportedEncodingException e) {
            logger.error("Error in parsing response test",e);
            AlipayUtils.doCancelTransaction(transactionId, txnLog, QRHost.config);
            return -1;
        } finally {
            mqClient.close();
        }

        return 0;
    }

    private int processNETSQR() {
        return 0;
    }

    private String getEmptyField48() {
        byte b[]=new byte[65];
        for (byte b1 : b) {
            b1=' ';
        }
        return new String(b);
    }
}
