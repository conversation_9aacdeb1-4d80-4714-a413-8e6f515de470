package com.abl.qr;


import org.apache.log4j.Logger;

import java.io.Serializable;
import java.util.*;

/**
 * stores transaction details
 *
     Taxi Number	Ans-12
     Driver ID	Ans-9
     Job Number	Ans-10
     Fare Amount	Ans-6
     Fare GST	Ans-6
     Fare Admin	Ans-6
     Company Code	Ans-4
     PIN Pad Serial Num	Ans-20
 */

@SuppressWarnings("serial")
public class TxnDetails implements Serializable {
    public static Logger logger= Logger.getLogger(TxnDetails.class);

    private static LinkedHashMap<String, Integer> def;

    private Hashtable table;
    private String mti;
    private String procCode;
    private String systemTraceAuditNumber;
    private Date txnDateTime;
    private String transactionId;
    private String tid;
    private byte[] data;

    static {
        def=new LinkedHashMap<String, Integer>();
        def.put("TaxiNumber",12);
        def.put("DriverId",9);
        def.put("JobNumber",10);
        def.put("FareAmount",6);
        def.put("FareGST",6);
        def.put("FareAdmin",6);
        def.put("CompanyCode",4);
        def.put("PPSerialNumber",20);
    }

    public TxnDetails() {
        table=new Hashtable();
    }

    public String getMti() {
        return mti;
    }

    public void setMti(String mti) {
        this.mti = mti;
    }

    public String getProcCode() {
        return procCode;
    }

    public void setProcCode(String procCode) {
        this.procCode = procCode;
    }

    public String getSystemTraceAuditNumber() {
        return systemTraceAuditNumber;
    }

    public void setSystemTraceAuditNumber(String systemTraceAuditNumber) {
        this.systemTraceAuditNumber = systemTraceAuditNumber;
    }

    public Date getTxnDateTime() {
        return txnDateTime;
    }

    public void setTxnDateTime(Date txnDateTime) {
        this.txnDateTime = txnDateTime;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public byte[] getData() {
        return data;
    }

    public void setData(byte[] data) {
        this.data = data;
    }

    public void setPrivateDataFile(String data) {
        if (data.length()<73) {
            logger.error("Length of private data field is lesser than 73! Skipping");
            return;
        }
        Iterator i=def.entrySet().iterator();
        int idx=0;
        while (i.hasNext()) {
            Map.Entry entry=((Map.Entry)i.next());
            int offset=(Integer)entry.getValue();
            String result=data.substring(idx, idx+offset);
            logger.debug("Key:"+entry.getKey()+" Value:"+entry.getValue()+" offset:"+offset+" data:"+result);
            if ("DriverId".equals(entry.getKey())) {
                // Mask driver ID:
                table.put(entry.getKey(),"XXXXX"+result.substring(5));
            } else {
                table.put(entry.getKey(), result);
            }
            idx+=offset;
        }
    }

    public String get(String name) {
        Object o=table.get(name);
        //logger.debug("Getting result for name:"+name+" value:"+(o==null?"null":o.toString()));
        if (o==null)
            return null;
        return (table.get(name)).toString();
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    @Override
    public String toString() {
        return "TxnDetails{" +
                "table=" + table +
                ", mti='" + mti + '\'' +
                ", procCode='" + procCode + '\'' +
                ", systemTraceAuditNumber='" + systemTraceAuditNumber + '\'' +
                ", txnDateTime=" + txnDateTime +
                ", tid='" + tid + '\'' +
                ", data=" + Arrays.toString(data) +
                '}';
    }
}
