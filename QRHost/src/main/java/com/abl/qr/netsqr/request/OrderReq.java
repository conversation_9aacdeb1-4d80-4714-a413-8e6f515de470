package com.abl.qr.netsqr.request;

import com.abl.qr.utils.NETSQRUtils;
import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import org.apache.log4j.Logger;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 19/5/17.
 */
public class OrderReq {
    public static Logger logger=Logger.getLogger(OrderReq.class);

    @SerializedName("mti")
    @Getter @Setter
    private String mti;
    @SerializedName("process_code")
    @Getter @Setter
    private String processCode;
    @SerializedName("amount")
    @Getter @Setter
    private String amount;
    @SerializedName("transmission_time")
    @Getter @Setter
    private String transmissionTime;
    @SerializedName("stan")
    @Getter @Setter
    private String stan;
    @SerializedName("transaction_time")
    @Getter @Setter
    private String transactionTime;
    @SerializedName("transaction_date")
    @Getter @Setter
    private String transactionDate;
    @SerializedName("entry_mode")
    @Getter @Setter
    private String entryMode;
    @SerializedName("condition_code")
    @Getter @Setter
    private String conditionCode;
    @SerializedName("institution_code")
    @Getter @Setter
    private String institutionCode;
    @SerializedName("retrieval_ref")
    @Getter @Setter
    private String retrievalRef;
    @SerializedName("host_tid")
    @Getter @Setter
    private String hostTid;
    @SerializedName("host_mid")
    @Getter @Setter
    private String hostMid;
    @SerializedName("acceptor_name")
    @Getter @Setter
    private String acceptorName;
    @SerializedName("txn_identifier")
    @Getter @Setter
    private String txnIdentifier;
    @SerializedName("npx_data")
    @Getter @Setter
    private String npxData;
    @SerializedName("invoice_ref")
    @Getter @Setter
    private String invoiceRef;
    @SerializedName("user_data")
    @Getter @Setter
    private String userData;
    @SerializedName("callback_url")
    @Getter @Setter
    private String callbackUrl;
    @SerializedName("sig")
    @Getter @Setter
    private String sig;

    public String getSignString() {
        // Generation String
        // [mti][process_code][stan][transaction_time][transaction_date][entry_mode][condition_code][host_tid][host_mid][apisecret]

        StringBuilder sb=new StringBuilder();
        if (mti!=null) sb.append(mti);
        if (processCode!=null) sb.append(processCode);
        if (stan!=null) sb.append(stan);
        if (transactionTime!=null) sb.append(transactionTime);
        if (transactionDate!=null) sb.append(transactionDate);
        if (entryMode!=null) sb.append(entryMode);
        if (conditionCode!=null) sb.append(conditionCode);
        if (hostTid!=null) sb.append(hostTid);
        if (hostMid!=null) sb.append(hostMid);
        //if (retrievalRef!=null) sb.append(retrievalRef);
        //if (institutionCode!=null) sb.append(institutionCode);

        return sb.toString();
    }

    public void sign(String secret) {
        logger.debug("String to sign:"+getSignString());
        logger.debug("Secret:"+secret);
        String toSign=getSignString()+secret;
        logger.debug("Final String:"+toSign);

        sig= NETSQRUtils.genSignature(toSign);
    }

    public static class AdditionalData {
        @SerializedName("E103")
        @Getter @Setter
        private String posId;
        @SerializedName("E201")
        @Getter @Setter
        private String sourceAmount;
        @SerializedName("E202")
        @Getter @Setter
        private String sourceCurrency;
        @SerializedName("E204")
        @Getter @Setter
        private String targetCurrency;
    }
}
