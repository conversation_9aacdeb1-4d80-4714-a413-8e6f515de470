package com.abl.qr.netsqr;

import com.abl.qr.utils.Utils;
import lombok.Getter;
import lombok.Setter;

import java.util.Random;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 23/5/17.
 */
public class QRData {
    @Getter @Setter
    private String header="NETSQPAY";
    @Getter @Setter
    private String version="0";
    @Getter @Setter
    private String tid;
    @Getter @Setter
    private String merchId;
    @Getter @Setter
    private String stan;
    @Getter @Setter
    private String timeStamp;
    @Getter @Setter
    private String op="0";
    @Getter @Setter
    private String channel="0";
    @Getter @Setter
    private String amt;
    @Getter @Setter
    private String paymentAccept="0001";
    @Getter @Setter
    private String currency="0";
    @Getter @Setter
    private String otrs;
    @Getter @Setter
    private String merchantName;
    @Getter @Setter
    private String checksum;

    public QRData() {
        timeStamp= Long.toHexString(System.currentTimeMillis()/1000 + 8*60*60- 9131*86400);
        byte[] b = new byte[4];
        new Random().nextBytes(b);
        otrs= Utils.getByteString(b,false);
    }

    public String toQRString() {
        StringBuilder sb=new StringBuilder();
        sb.append(header);
        sb.append(version);
        if (tid!=null) sb.append(String.format("%8s", tid).replace(' ', '#'));
        else sb.append("########");
        if (merchId!=null) sb.append(String.format("%15s", merchId).replace(' ', '#'));
        else sb.append("###############");
        if (stan!=null) sb.append(String.format("%6s", stan).replace(' ', '0'));
        else sb.append("000000");
        sb.append(String.format("%8s", timeStamp).replace(' ','0').toUpperCase());
        sb.append(op);
        sb.append(channel);
        if (amt!=null) sb.append(String.format("%8s", amt).replace(' ', '0'));
        else sb.append("00000000");
        sb.append(paymentAccept);
        sb.append(currency);
        sb.append(otrs);
        if (merchantName!=null) sb.append(String.format("%17s", merchantName));
        else sb.append("                 ");

        byte crc[]=Utils.crc16(sb.toString().getBytes());
        sb.append(Utils.getByteString(crc,false));

        return sb.toString();
    }

}
