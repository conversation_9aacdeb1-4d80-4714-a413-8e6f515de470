package com.abl.qr.utils;


import com.abl.qr.QRHost;
import com.abl.qr.alipay.request.CancelReq;
import com.abl.qr.db.AlipayHostTxnLog;
import com.abl.qr.db.AlipayTmlTxnLog;
import org.apache.commons.configuration.PropertiesConfiguration;

import java.util.Date;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;


public class RetryTask extends TimerTask {
    @Override
    public void run(){

    }

    public void scheduleTask(CancelReq cancelReq, String transactionId, AlipayTmlTxnLog txnLog, AlipayHostTxnLog alipayHostTxnLog, int totalRetry ) throws Exception
    {
        long retryInterval = Long.valueOf(QRHost.config.getString("cancel.retry.interval.seconds","3"));
        TimeUnit.SECONDS.sleep(retryInterval);
        AlipayUtils.doResendCancelTransaction(cancelReq, transactionId, txnLog, alipayHostTxnLog, totalRetry+1, QRHost.config);
    }
}
