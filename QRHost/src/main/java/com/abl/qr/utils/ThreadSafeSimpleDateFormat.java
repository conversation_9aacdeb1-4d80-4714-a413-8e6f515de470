package com.abl.qr.utils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Hashtable;
import java.util.Map;

/**
 * Thread-safe version of SimpleDateFormat (SimpleDateFormat is NOT thread-safe)
 * <p/>
 * You may not need to use this class directly if you are using DateUtils methods.
 */
public class ThreadSafeSimpleDateFormat {

    private static Map<String, ThreadSafeSimpleDateFormat> store = new Hashtable<String, ThreadSafeSimpleDateFormat>();

    private DateFormat df;

    public static ThreadSafeSimpleDateFormat getInstance(String format) {
        synchronized (store) {
            if (store.containsKey(format)) {
                return store.get(format);
            } else {
                ThreadSafeSimpleDateFormat tsdf = new ThreadSafeSimpleDateFormat(format);
                store.put(format, tsdf);
                return tsdf;
            }
        }
    }

    private ThreadSafeSimpleDateFormat(String format) {
        df = new SimpleDateFormat(format);
    }

    /**
     * synchronized version of SimpleDateFormat.format()
     *
     * @param date
     * @return date in desired string format
     */
    public synchronized String format(Date date) {
        return df.format(date);
    }

    /**
     * synchronized version of SimpleDateFormat.parse()
     *
     * @param string
     * @return date value
     * @throws ParseException
     */
    public synchronized Date parse(String string) throws ParseException {
        return df.parse(string);
    }
}
