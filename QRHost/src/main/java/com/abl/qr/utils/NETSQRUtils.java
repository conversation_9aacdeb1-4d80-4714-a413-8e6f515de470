package com.abl.qr.utils;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.nimbusds.jose.jwk.RSAKey;
import com.nimbusds.jose.util.X509CertUtils;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.configuration.PropertiesConfiguration;
import org.apache.commons.io.FileUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.InputStreamEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.*;
import javax.crypto.spec.IvParameterSpec;
import java.io.*;
import java.security.*;
import java.security.cert.X509Certificate;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.X509EncodedKeySpec;
import java.util.Calendar;
import java.util.Date;
import java.util.Random;

/**
 * Created with IntelliJ IDEA.
 * User: kohtzewei
 * Date: 5/10/13
 * Time: 5:05 PM
 * To change this template use File | Settings | File Templates.
 *
 * This utils contains methods to assist in the NETS QR functions
 */
public class NETSQRUtils {
    public static final Logger logger= Logger.getLogger(NETSQRUtils.class);

    public static final String CDG_TXN_PREFIX="CDG_";

    /**
     * returns date with millisecond field set to 0
     *
     * @param date
     * @return date value
     */
    public static Date removeMillisec(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * Format date to String according to pattern.
     * This is a thin layer over SimpleDateFormat.format()
     *
     * @param date
     * @param pattern
     * @return string
     */
    public static String format(Date date, String pattern) {
        ThreadSafeSimpleDateFormat sdf = ThreadSafeSimpleDateFormat.getInstance(pattern);
        return sdf.format(date);
    }

    /*
    public static Object postRequest(PropertiesConfiguration config, String jsonString, String respClassStr, AlipayHostTxnLog log) throws Exception {
        return postRequest(config, jsonString, respClassStr, null, log);
    }
    */


    public static Object postRequest(PropertiesConfiguration config, String jsonString, String url, String respClassStr, Class c) throws Exception {
        Object result=null;

        int connectTimeout=config.getInt("netsqr.qr.connect.timeout",10000);
        int socketTimeout=config.getInt("netsqr.qr.socket.timeout",10000);
        int requetsTimeout=config.getInt("netsqr.qr.request.timeout",10000);
        HttpClientBuilder builder = HttpClientBuilder.create();
        RequestConfig rc = RequestConfig.custom().setConnectTimeout(connectTimeout).
                setConnectionRequestTimeout(requetsTimeout).setSocketTimeout(socketTimeout).build();

        builder.setDefaultRequestConfig(rc);

        HttpClient client=builder.build();

        //String url=config.getString("dash.registerPayment.url","/pos/RegisterPayment");
        //String url=config.getString("nets.qr."+respClassStr+".url","/pos/RegisterPayment");
        //logger.debug("reqClassStr:"+respClassStr+" Url: "+url);
        HttpPost post=new HttpPost(url);

        post.setHeader("KeyId", config.getString("nets.qr.key.id"));

        InputStream inputStream=new ByteArrayInputStream(jsonString.getBytes());
        InputStreamEntity inputStreamEntity=new InputStreamEntity(inputStream);
        inputStreamEntity.setContentType("application/json");

        post.setEntity(inputStreamEntity);

        HttpResponse netsResposne = null;
        try {
            netsResposne = client.execute(post);
        } catch (IOException e) {
            logger.error("Exception getting response",e);
            throw e;
        }

        if (netsResposne!=null) {
            int statusCode=netsResposne.getStatusLine().getStatusCode();
            logger.debug("Http status: "+statusCode);
            if (statusCode!=200) {

                throw new Exception("Post Err:"+statusCode);
            }
            HttpEntity resEntity = netsResposne.getEntity();

            if (resEntity != null) {
                logger.debug("Response content length: " + resEntity.getContentLength());

                String resp=new String(EntityUtils.toByteArray(resEntity));
                logger.debug("Response: "+resp);


            } else {
                logger.error("No NETS response from post to "+url+" data:"+jsonString);
                //log.setTransmitStatus("No resp");
            }

        }
        inputStream.close();

        return result;
    }

    public static Object toObject(String resp, Class c, String objName) {
        logger.debug("Trying to parse json: " + resp + " to " + c.toString() + " objName:" + objName);
        try {
            Gson gson=new Gson();
            JsonParser parser = new JsonParser();
            JsonObject object = parser.parse(resp.trim()).getAsJsonObject();
            logger.debug("Object = "+object);
            JsonObject jo = object.getAsJsonObject(objName);
            logger.debug("fromJson:"+jo);

            return gson.fromJson(jo, c);
        } catch (Exception e) {
            logger.error("Unable to parse json: "+resp+" to "+c.toString()+" objName:"+objName);
            logger.error(e.toString());
        }
        return null;
    }

    /*
    public static ResponseVO toResponseVO(String resp, String objName) {
        Gson gson=new Gson();
        JsonParser parser = new JsonParser();

        try {
            JsonObject object = parser.parse(resp.trim()).getAsJsonObject();
            logger.debug("Object = "+object);
            JsonObject jo = object.getAsJsonObject(objName);
            logger.debug("fromJson:"+jo);
            JsonObject responseVO=jo.getAsJsonObject("responseVO");
            return gson.fromJson(responseVO,ResponseVO.class);
        } catch (JsonSyntaxException e) {
            logger.error(e.toString());
        }
        ResponseVO errResp=new ResponseVO();
        errResp.setStatus("99");

        return errResp;
    }
    */

    public static byte[] encipher(byte[] publicKeyBytes, byte[] input) throws NoSuchPaddingException, NoSuchAlgorithmException, NoSuchProviderException, InvalidKeySpecException, InvalidKeyException, BadPaddingException, IllegalBlockSizeException {
        //Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());
        //Cipher asymmetricCipher = Cipher.getInstance("RSA/None/PKCS1Padding", "BC");
        Cipher asymmetricCipher = Cipher.getInstance("RSA");

        X509EncodedKeySpec publicKeySpec = new X509EncodedKeySpec(publicKeyBytes);

        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        Key key = keyFactory.generatePublic(publicKeySpec);

        asymmetricCipher.init(Cipher.ENCRYPT_MODE, key);

        //asymmetricCipher.init(Cipher.ENCRYPT_MODE, pubKey, random);
        byte[] cipherText = asymmetricCipher.doFinal(input);

        return cipherText;
    }

    public static String generateRRN() {
        long aStart=100000000000L;
        long aEnd=999999999999L;

        return generateRandom(aStart, aEnd,12);
    }

    public static String generateAuthCode() {
        long aStart=100000L;
        long aEnd=999999L;

        return generateRandom(aStart, aEnd, 6);
    }

    public static String generateRandom(long aStart, long aEnd, int noOfDigit) {
        Random aRandom=new Random();
        //get the range, casting to long to avoid overflow problems
        long range = aEnd - aStart + 1;
        // compute a fraction of the range, 0 <= frac < range
        long randomNumber = (long)(range * aRandom.nextDouble());
        //System.out.println(" fraction... "+randomNumber);
        return String.format("%0"+noOfDigit+"d",randomNumber);
    }

    /*
    public static ResponseVO sendReversal(String transactionId, DashSingTelLog log, String reason) {
        String credential= DashHost.config.getString("dash.st-credential", "");
        String base64PK=DashHost.config.getString("dash.public.key","");
        //String authCode= DateUtils.format(new Date(), "HHmmss");
        ResponseVO resp= null;
        try {
            DashRSAHelper rsaHelper=DashRSAHelper.getInstance(base64PK);

            byte[] encCredential=rsaHelper.encipher(credential.getBytes());

            ReversePosPayment r=new ReversePosPayment();
            r.setTransactionId(transactionId);
            r.setReasonNote(reason);
            r.setHeader("stCredential", JSONPostUtils.encodeBase64(encCredential));
            r.setHeader("ipAddress", DashHost.config.getString("dash.pos.server.ip", ""));

            //Map<String, CancelPosPayment> m=new HashMap<String, CancelPosPayment>();
            //m.put(CancelPosPayment.DASH_CLASS_NAME,r);

            Map<String, ReversePosPayment> m=new HashMap<String, ReversePosPayment>();
            m.put(ReversePosPayment.DASH_CLASS_NAME,r);

            GsonBuilder gsonBuilder=new GsonBuilder();
            Gson gson=gsonBuilder.create();
            String x=gson.toJson(m);
            logger.debug("Raw Request : "+x);

            log.setMessageClass(ReversePosPayment.DASH_CLASS_NAME);

            resp=(ResponseVO) NETSQRUtils.postRequest(DashHost.config,
                    x, ReversePosPaymentResp.DASH_CLASS_NAME, log);
        } catch (Exception e) {
            logger.error(e.toString());
        }
        return resp;
    }
    */

    /*
    public static ResponseVO sendCancelRegisteredPosPayment(String transactionId, DashSingTelLog log) {
        String credential= DashHost.config.getString("dash.st-credential", "");
        String base64PK=DashHost.config.getString("dash.public.key","");
        //String authCode= DateUtils.format(new Date(), "HHmmss");
        ResponseVO resp= null;
        try {
            DashRSAHelper rsaHelper=DashRSAHelper.getInstance(base64PK);

            byte[] encCredential=rsaHelper.encipher(credential.getBytes());

            CancelRegisteredPosPayment r=new CancelRegisteredPosPayment();
            r.setTransactionId(transactionId);
            r.setHeader("stCredential", JSONPostUtils.encodeBase64(encCredential));
            r.setHeader("ipAddress", DashHost.config.getString("dash.pos.server.ip", ""));

            Map<String, CancelRegisteredPosPayment> m=new HashMap<String, CancelRegisteredPosPayment>();
            m.put(CancelRegisteredPosPayment.DASH_CLASS_NAME,r);


            GsonBuilder gsonBuilder=new GsonBuilder();
            Gson gson=gsonBuilder.create();
            String x=gson.toJson(m);
            logger.debug("Raw Request : "+x);

            log.setMessageClass(CancelRegisteredPosPayment.DASH_CLASS_NAME);

            resp=(ResponseVO) NETSQRUtils.postRequest(DashHost.config,
                    x, CancelRegisteredPosPayment.DASH_CLASS_NAME, log);
        } catch (Exception e) {
            logger.error(e.toString());
        }
        return resp;
    }
    */

    public static RSAPublicKey getRSAPublicKey(String filename)
            throws Exception {

        byte[] encodedCert = FileUtils.readFileToByteArray(new File(filename));

        //X509EncodedKeySpec spec =
        //        new X509EncodedKeySpec(encodedCert);
        //KeyFactory kf = KeyFactory.getInstance("RSA");

        //return kf.generatePublic(spec);

        X509Certificate cert = X509CertUtils.parse(encodedCert);
        if (cert!=null) {
            return (RSAPublicKey)cert.getPublicKey();
        }

        return null;
    }

    public static PublicKey getPublicKey(String filename)
            throws Exception {

        byte[] encodedCert = FileUtils.readFileToByteArray(new File(filename));

        X509Certificate cert = X509CertUtils.parse(encodedCert);
        if (cert!=null) {
            return cert.getPublicKey();
        }

        return null;
    }

    public static String genSignature(String text) {
        String signature=null;
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");

            md.update(text.getBytes("UTF-8")); // Change this to "UTF-16" if needed
            byte[] digest = md.digest();

            String s=Utils.getByteString(digest,false).toUpperCase();
            Base64 base64=new Base64();
            signature=base64.encodeAsString(s.getBytes());
        } catch (NoSuchAlgorithmException e) {
            logger.error("Error generating signature",e);
        } catch (UnsupportedEncodingException e) {
            logger.error("Error generating signature",e);
        }
        return signature;
    }

    public static byte[] encipher(PublicKey key, byte[] input) throws NoSuchPaddingException, NoSuchAlgorithmException, NoSuchProviderException, InvalidKeySpecException, InvalidKeyException, BadPaddingException, IllegalBlockSizeException {
        //Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());
        //Cipher asymmetricCipher = Cipher.getInstance("RSA/None/PKCS1Padding", "BC");
        Cipher asymmetricCipher = Cipher.getInstance("RSA");

        asymmetricCipher.init(Cipher.ENCRYPT_MODE, key);

        //asymmetricCipher.init(Cipher.ENCRYPT_MODE, pubKey, random);
        byte[] cipherText = asymmetricCipher.doFinal(input);

        return cipherText;
    }

    public static SecretKey genSessionKey() throws NoSuchAlgorithmException {
        KeyGenerator kg = KeyGenerator.getInstance("AES");
        kg.init(256);
        SecretKey sk = kg.generateKey();
        //return sk.getEncoded();
        return sk;
    }


    public static RSAKey getRSAKeyfromPublicKey(RSAPublicKey publicKey) {
        RSAKey jwk = new RSAKey.Builder(publicKey).build();
        return jwk;
    }

    private static BouncyCastleProvider bcp;
    static {
        bcp=new org.bouncycastle.jce.provider.BouncyCastleProvider();
        Security.addProvider(bcp);
    }

    public static byte[] encryptSessionKey(byte[] data, PublicKey key) {
        try {
            final Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
            //final Cipher cipher = Cipher.getInstance("RSA");
            cipher.init(Cipher.ENCRYPT_MODE, key);
            byte[] cipherText = cipher.doFinal(data);

            return cipherText;
        }
        catch (Exception e) {
            logger.error("Error encrypting Session Key");
        }
        return null;
    }

    public static byte[] encryptPayload(byte[] data, SecretKey sk) {
        try {
            final Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            byte iv[]={0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00};
            cipher.init(Cipher.ENCRYPT_MODE, sk,new IvParameterSpec(iv));
            byte[] cipherText = cipher.doFinal(data);

            return cipherText;
        }
        catch (Exception e) {
            logger.error("Error encrypting Session Key");
        }
        return null;
    }
}
