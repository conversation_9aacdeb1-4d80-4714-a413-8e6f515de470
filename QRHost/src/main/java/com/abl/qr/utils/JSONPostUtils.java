package com.abl.qr.utils;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.configuration.PropertiesConfiguration;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLContexts;
import org.apache.http.entity.InputStreamEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.X509EncodedKeySpec;

/**
 * Created with IntelliJ IDEA.
 * User: kohtzewei
 * Date: 14/10/13
 * Time: 4:50 PM
 * To change this template use File | Settings | File Templates.
 */
public class JSONPostUtils {

    public static final Logger logger= Logger.getLogger(JSONPostUtils.class);

    public static byte[] decodePublicKey(String encodedBytes) throws IOException {
        Base64 decoder=new Base64();
        byte[] decodedBytes = decoder.decode(encodedBytes);
        return decodedBytes;
    }

    public static String encodeBase64(byte[] data) {
        //BASE64Encoder encoder=new BASE64Encoder();
        Base64 encoder=new Base64();
        return encoder.encodeAsString(data);
    }

    public static Object postRequest(PropertiesConfiguration config, String jsonString, Class c) throws Exception {

        HttpClientBuilder builder = HttpClientBuilder.create();
        RequestConfig rc = RequestConfig.custom().setConnectTimeout(10*1000).
                setConnectionRequestTimeout(10*1000).setSocketTimeout(10*1000).build();
        builder.setDefaultRequestConfig(rc);


        X509TrustManager tm = new X509TrustManager() {


            @Override
            public void checkClientTrusted(java.security.cert.X509Certificate[] x509Certificates, String s) throws java.security.cert.CertificateException {
                //To change body of implemented methods use File | Settings | File Templates.
            }

            @Override
            public void checkServerTrusted(java.security.cert.X509Certificate[] x509Certificates, String s) throws java.security.cert.CertificateException {
                //To change body of implemented methods use File | Settings | File Templates.
            }

            @Override
            public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                //return new java.security.cert.X509Certificate[0];  //To change body of implemented methods use File | Settings | File Templates.
                return null;
            }

        };

        SSLContext sslContext = SSLContexts.custom()
                .useTLS()
                .useSSL()
                .build();
        sslContext.init(null, new TrustManager[]{tm},null);
        SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);

        builder.setSSLSocketFactory(sslsf);

        //HttpClientContext clientContext = HttpClientContext.create();


        HttpClient client=builder.build();

        HttpPost post=new HttpPost(config.getString("dash.registerPayment.url","/pos/RegisterPayment"));
        //post.set
        //ContentType.create("application/json");

        //post.addHeader("counterMsisdn",config.getString("dash.counter.msisdn",""));
        //post.addHeader("counterPin",config.getString("dash.counter.pin",""));

        InputStream inputStream=new ByteArrayInputStream(jsonString.getBytes());//init your own inputstream
        InputStreamEntity inputStreamEntity=new InputStreamEntity(inputStream);
        inputStreamEntity.setContentType("application/json");
        post.setEntity(inputStreamEntity);

        HttpResponse response = client.execute(post);
        //System.out.println(alipayResponse);
        if (response!=null) {
            int statusCode=response.getStatusLine().getStatusCode();
            logger.debug("Http status: "+statusCode);
            if (statusCode!=200) {
                throw new Exception("HTTP alipayResponse error "+statusCode);
            }
            HttpEntity resEntity = response.getEntity();

            if (resEntity != null) {
                logger.debug("Response content length: " + resEntity.getContentLength());

                String resp=new String(EntityUtils.toByteArray(resEntity));
                logger.debug("Response: "+resp);

                Object o=toObject(resp, c, "registerPosPaymentRs");
                logger.debug("Object received: "+o);
                return o;
            }
        }

        throw new Exception("Post to SingTel Error");

    }

    public static Object toObject(String resp, Class c, String objName) {
        Gson gson=new Gson();
        JsonParser parser = new JsonParser();
        //JsonObject object1 = parser.parse(resp).getAsJsonObject().getAsJsonObject(objName);

        JsonObject object = parser.parse(resp).getAsJsonObject();
        logger.debug("Object = "+object);
        JsonObject example = object.getAsJsonObject(objName);
        logger.debug("fromJson:"+example);
        return gson.fromJson(example, c);
        //return gson.fromJson(resp, c);
    }
    /*
    public static byte[] encipher(byte[] publicKeyBytes, byte[] input) throws NoSuchPaddingException, NoSuchAlgorithmException, NoSuchProviderException, InvalidKeySpecException, InvalidKeyException, BadPaddingException, IllegalBlockSizeException {
        // Use RSA/NONE/NoPadding as algorithm and BouncyCastle as crypto provider
        //Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());
        //Cipher asymmetricCipher = Cipher.getInstance("RSA/None/PKCS1Padding", "BC");
        Cipher asymmetricCipher = Cipher.getInstance("RSA");

        X509EncodedKeySpec publicKeySpec = new X509EncodedKeySpec(publicKeyBytes);
        //RSAPublicKeySpec keySpec = new RSAPublicKeySpec(m, e);

        //logger.debug("Public key: "+publicKeySpec.toString());
        //KeyFactory keyFactory;
        //keyFactory = KeyFactory.getInstance(publicKeySpec.getFormat());

        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        //RSAPublicKeySpec pub = keyFactory.getKeySpec(publicKeySpec,RSAPublicKeySpec.class);
        Key key = keyFactory.generatePublic(publicKeySpec);

        asymmetricCipher.init(Cipher.ENCRYPT_MODE, key);

        //asymmetricCipher.init(Cipher.ENCRYPT_MODE, pubKey, random);
        byte[] cipherText = asymmetricCipher.doFinal(input);
        logger.debug("ct1:"+ ByteUtils.bytesToHex(cipherText));

        byte[] cipherText1 = asymmetricCipher.doFinal(input);
        logger.debug("ct2:"+ ByteUtils.bytesToHex(cipherText1));
        return cipherText;
    }
    */



}
