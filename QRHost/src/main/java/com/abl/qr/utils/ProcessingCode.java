package com.abl.qr.utils;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 15/5/17.
 */
public enum ProcessingCode {
    ALIPAY_REGISTER("520000"),
    ALIPAY_PAYMENT("530000"),
    NETSQR_REGISTER("540000"),
    NETSQR_PAYMENT("550000")
    ;

    private final String code;

    /**
     * @param code
     */
    private ProcessingCode(final String code) {
        this.code = code;
    }

    /* (non-Javadoc)
     * @see java.lang.Enum#toString()
     */
    @Override
    public String toString() {
        return code;
    }
}
