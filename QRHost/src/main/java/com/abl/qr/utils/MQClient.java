package com.abl.qr.utils;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.apache.activemq.ActiveMQConnectionFactory;
import org.apache.commons.configuration.PropertiesConfiguration;
import org.apache.log4j.Logger;

import javax.jms.*;

/**
 * Created with IntelliJ IDEA.
 * User: koh<PERSON>ew<PERSON>
 * Date: 6/10/13
 * Time: 5:03 PM
 * To change this template use File | Settings | File Templates.
 */
public class MQClient implements ExceptionListener {
    public static Logger logger= Logger.getLogger(MQClient.class);

    private ActiveMQConnectionFactory connectionFactory;
    private Connection connection;
    private Session session;
    private PropertiesConfiguration config;
    private Destination destination;
    private MessageConsumer consumer;

    public MQClient(PropertiesConfiguration config) {
        this.config=config;
    }

    public void connect(String topic) throws JMSException {
        String connectStr=config.getString("mq.url","failover:(tcp://localhost:61616)?timeout=3000");
        connectionFactory = new ActiveMQConnectionFactory(connectStr);
        // Create a Connection
        connection = connectionFactory.createConnection();
        connection.start();

        connection.setExceptionListener(this);

        // Create a Session
        session = connection.createSession(false, Session.AUTO_ACKNOWLEDGE);

        destination = session.createTopic(topic);

        // Create a MessageConsumer from the Session to the Topic or Queue
        consumer = session.createConsumer(destination);
    }

    public QRMessage checkResponse(String transactionId, long maxWaitSetting) throws JMSException {
        //NETSQRNotifyMessage response=null;

        //long maxWaitSetting=config.getLong("netsqr.qr.alipayResponse.wait.time",40000);
        long maxWait=maxWaitSetting;
        boolean matched=false;
        long startTime=System.currentTimeMillis();

        // loop receiving till either finding what i want or time out.
        // timeout (maxWait) will reduces every time
        String recv="";
        QRMessage qrMessage=null;
        while ((!matched) && (maxWait>0)) {
            logger.debug("Waiting for "+maxWait);
            Message message = consumer.receive(maxWait);
            long waited=System.currentTimeMillis()-startTime;
            maxWait=maxWaitSetting-waited;
            logger.debug("new maxWait:" + maxWait+" waited:"+waited+" maxWaitSetting:"+maxWaitSetting);

            if (message instanceof TextMessage) {
                recv=((TextMessage) message).getText();
                // check the alipayResponse
                logger.debug("recv:"+recv);

                Gson gson = new GsonBuilder().create();
                qrMessage = gson.fromJson(recv, QRMessage.class);

                if (qrMessage!=null && qrMessage.getReference().equals(transactionId)) {
                    logger.debug("Found matching transaction ID");
                    matched=true;
                } else {
                    qrMessage=null;
                }
            }
        }

        return qrMessage;
    }

    public void close() {
        try {
            if (consumer!=null)
                consumer.close();
            if (session!=null)
                session.close();
            if (connection!=null) {
                connection.stop();
                connection.close();
            }
        } catch (JMSException e) {
            logger.error(e.toString());
        }
    }

    @Override
    public void onException(JMSException e) {
        logger.error(e.toString());
    }
}
