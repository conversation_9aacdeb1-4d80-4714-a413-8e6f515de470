package com.abl.qr.utils;

import org.apache.commons.configuration.PropertiesConfiguration;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.InputStreamEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.bind.Unmarshaller;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;

/**
 * Created with IntelliJ IDEA.
 * User: kohtz<PERSON>ei
 * Date: 1/10/13
 * Time: 11:41 PM
 *
 *
 */

public class XMLPostUtils {
    public static Logger logger= Logger.getLogger(XMLPostUtils.class);

    public static Object postRequest(PropertiesConfiguration config, String xmlString, Class c) throws Exception {

        HttpClientBuilder builder = HttpClientBuilder.create();
        RequestConfig rc = RequestConfig.custom().setConnectTimeout(10*1000).
                setConnectionRequestTimeout(10*1000).setSocketTimeout(10*1000).build();
        builder.setDefaultRequestConfig(rc);

        HttpClient client=builder.build();

        HttpPost post=new HttpPost(config.getString("dash.registerPayment.url","/pos/RegisterPayment"));

        //post.setRequestHeader(
        //        "Content-type", "text/xml; charset=ISO-8859-1");

        post.addHeader("st-credential",config.getString("dash.st-credential",""));
        post.addHeader("ipAddress",config.getString("dash.pos.server.ip",""));
        post.addHeader("counterMsisdn",config.getString("dash.counter.msisdn",""));
        post.addHeader("counterPin",config.getString("dash.counter.pin",""));

        InputStream inputStream=new ByteArrayInputStream(xmlString.getBytes());//init your own inputstream
        InputStreamEntity inputStreamEntity=new InputStreamEntity(inputStream);
        post.setEntity(inputStreamEntity);

        HttpResponse response = client.execute(post);
        //System.out.println(alipayResponse);
        if (response!=null) {
            int statusCode=response.getStatusLine().getStatusCode();
            logger.debug("Http status: "+statusCode);
            if (statusCode!=200) {
                throw new Exception("HTTP alipayResponse error "+statusCode);
            }
            HttpEntity resEntity = response.getEntity();

            if (resEntity != null) {
                logger.debug("Response content length: " + resEntity.getContentLength());

                String resp=new String(EntityUtils.toByteArray(resEntity));
                logger.debug("Response: "+resp);

                Object o=toObject(resp, c);
                logger.debug("Object received: "+o);
                return o;
            }
        }

        throw new Exception("Post to SingTel Error");

    }

    public static String toXML(Object o) throws JAXBException {
        JAXBContext jaxbContext = JAXBContext.newInstance(o.getClass());
        Marshaller jaxbMarshaller = jaxbContext.createMarshaller();

        jaxbMarshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);

        ByteArrayOutputStream baos=new ByteArrayOutputStream();
        jaxbMarshaller.marshal(o, baos);

        return baos.toString();
    }

    public static Object toObject(String xml, Class c) throws JAXBException {
        JAXBContext jc = JAXBContext.newInstance(c);
        Unmarshaller u = jc.createUnmarshaller();

        ByteArrayInputStream baos=new ByteArrayInputStream(xml.getBytes());

        Object o=u.unmarshal(baos);

        return o;
    }
}
