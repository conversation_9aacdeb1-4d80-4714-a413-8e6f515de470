package com.abl.qr.utils;

import com.abl.qr.QRHost;
import com.abl.qr.alipay.request.CancelReq;
import com.abl.qr.alipay.response.CancelRes;
import com.abl.qr.db.AlipayHostTxnLog;
import com.abl.qr.db.AlipayTmlTxnLog;
import com.abl.qr.db.DBUtils;
import org.apache.commons.configuration.PropertiesConfiguration;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHost;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLContexts;
import org.apache.http.entity.InputStreamEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.protocol.HTTP;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;
import org.bouncycastle.crypto.digests.MD5Digest;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.Marshaller;
import javax.xml.bind.Unmarshaller;
import java.io.*;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by kohtzewei on 7/5/17.
 *
 * Alipay specific utils
 */
public class AlipayUtils {
    public static Logger logger=Logger.getLogger(AlipayUtils.class);

    public static final String TXN_REF_DELIMITER="-";
    public static final String FAIL = "FAIL";
    public static final String SYSTEM_ERROR="SYSTEM_ERROR";
    /**
     * Utility class to convert object to xml string
     *
     * @param object
     * @return
     * @throws Exception
     */

    public static String getObjectXML(Object object) throws Exception {
        JAXBContext context = JAXBContext.newInstance(object.getClass());
        Marshaller m = context.createMarshaller();
        m.setProperty(Marshaller.JAXB_FRAGMENT, Boolean.TRUE);
        StringWriter sw=new StringWriter();
        m.marshal(object, sw);
        return sw.toString();
    }


    public static Object getObjectFromXMLString(String xmlStr, Class cls) throws Exception {
        JAXBContext jaxbContext = JAXBContext.newInstance(cls);
        Unmarshaller jaxbUnmarshaller = jaxbContext.createUnmarshaller();

        Object rp = jaxbUnmarshaller.unmarshal(new StringReader(xmlStr));
        logger.debug(rp);

        return rp;
    }

    public static Object postRequest(PropertiesConfiguration config, List<BasicNameValuePair> list, Class c) throws Exception {

        HttpClientBuilder builder = HttpClientBuilder.create();
        RequestConfig rc = RequestConfig.custom().setConnectTimeout(10*1000).
                setConnectionRequestTimeout(10*1000).setSocketTimeout(10*1000).build();
        builder.setDefaultRequestConfig(rc);

        HttpClient client=builder.build();

        String hostURL=config.getString("alipay.host",null);
        if (hostURL==null || hostURL.isEmpty()) {
            throw new Exception("Alipay Host URL cannot be null or empty");
        }

        HttpPost post=new HttpPost(hostURL);

        if (config.getString("proxy.server",null)!=null) {
            logger.debug("Using proxy:"+config.getString("proxy.server"));
            HttpHost proxy = new HttpHost(config.getString("proxy.server"), config.getInt("proxy.port",8080), "http");
            RequestConfig httpConfig = RequestConfig.custom()
                    .setProxy(proxy)
                    .build();

            post.setConfig(httpConfig);
        }

        logger.debug(new UrlEncodedFormEntity(list));
        post.setEntity(new UrlEncodedFormEntity(list));

        HttpResponse response = client.execute(post);
        if (response!=null) {
            int statusCode=response.getStatusLine().getStatusCode();
            logger.debug("Http status: "+statusCode);
            if (statusCode!=200) {
                throw new Exception("HTTP alipayResponse error "+statusCode);
            }
            HttpEntity resEntity = response.getEntity();

            if (resEntity != null) {
                logger.debug("Response content length: " + resEntity.getContentLength());

                String resp=new String(EntityUtils.toByteArray(resEntity));
                logger.debug("Response: "+resp);

                //return resp;
                Object o=getObjectFromXMLString(resp, c);
                logger.debug("Object received: "+o);

                return o;
            } else {
                logger.debug("Response from Alipay is null");
            }
        }

        throw new Exception("Post to Alipay Error");
    }

    public static String MD5Sign1(String message, String algorithm)
        throws Exception {

        MessageDigest digest = MessageDigest.getInstance(algorithm);
        byte[] hashedBytes = digest.digest(message.getBytes("UTF-8"));

        return Utils.getByteString(hashedBytes,false);
    }

    public static void doCancelTransaction(String refStr, AlipayTmlTxnLog txnLog, PropertiesConfiguration properties) {
        AlipayHostTxnLog alipayHostTxnLog=new AlipayHostTxnLog();
        alipayHostTxnLog.setMessageType("cancel");
        Boolean resend = Boolean.FALSE;
        CancelReq cancelReq = null;

        try {
            cancelReq=new CancelReq();

            cancelReq.setInputCharset(properties.getString("alipay.charset","UTF-8"));
            cancelReq.setService(properties.getString("alipay.cancel.service","alipay.acquire.cancel"));
            cancelReq.setPartner(properties.getString("alipay.partner.id"));
            cancelReq.setOutTradeNo(refStr);
            Long currTime=System.currentTimeMillis();
            cancelReq.setTimestamp(currTime.toString());

            String preSignString=cancelReq.generatePreSignString();
            String md5Signature=properties.getString("alipay.md5.signature");

            String sign1=AlipayUtils.MD5Sign1(preSignString+md5Signature, "MD5");
            cancelReq.setSignType(properties.getString("alipay.sign.type","MD5"));
            cancelReq.setSign(sign1);

            CancelRes result= (CancelRes) AlipayUtils.postRequest(properties, cancelReq.getNameValuePair(), CancelRes.class);

            // Log to db
            alipayHostTxnLog.setAlipayReferenceNumber(refStr);
            alipayHostTxnLog.setAmount(Integer.toString(txnLog.getAmount()));
            alipayHostTxnLog.setVehicleId(txnLog.getTaxiNumber());
            alipayHostTxnLog.setJobId(txnLog.getJobNumber());
            alipayHostTxnLog.setDriverId(txnLog.getDriverId());
            alipayHostTxnLog.setSerialNo(txnLog.getPinpadSerial());
            //alipayHostTxnLog.setTransDateTime(Utils.format(new Date(),"ddMMyy HHmmss"));
            alipayHostTxnLog.setAlipayRequestMessage(cancelReq.toString());

            if (result!=null) {
                logger.debug("Receive : " + result);
                alipayHostTxnLog.setTransmitStatus("OK");
                alipayHostTxnLog.setAlipayResponseMessage(result.toString());
                if (result.getResponse().getAlipayResponse().getResultCode()!=null) {
                    alipayHostTxnLog.setAlipayResponseResult(result.getResponse().getAlipayResponse().getResultCode());
                    if(result.getResponse().getAlipayResponse().getResultCode().equals(FAIL) && result.getResponse().getAlipayResponse().getDetailErrorCode().equals(SYSTEM_ERROR)){
                        resend= Boolean.TRUE;
                    }
                }
            } else {
                logger.error("Unable to cancel Alipay transaction ID: " + refStr);
                resend= Boolean.TRUE;
            }

            DBUtils.logAlipayHostTxnLog(QRHost.getDataSource(), alipayHostTxnLog);




        } catch (Exception e) {
            logger.error("Error in Alipay doCancelTransaction",e);

            alipayHostTxnLog.setAlipayReferenceNumber(refStr);
            alipayHostTxnLog.setAmount(Integer.toString(txnLog.getAmount()));
            alipayHostTxnLog.setVehicleId(txnLog.getTaxiNumber());
            alipayHostTxnLog.setJobId(txnLog.getJobNumber());
            alipayHostTxnLog.setDriverId(txnLog.getDriverId());
            alipayHostTxnLog.setSerialNo(txnLog.getPinpadSerial());
            //alipayHostTxnLog.setTransDateTime(Utils.format(new Date(),"ddMMyy HHmmss"));
            alipayHostTxnLog.setAlipayRequestMessage("");
            alipayHostTxnLog.setTransmitStatus("Error");
            alipayHostTxnLog.setAlipayResponseResult(e.toString());

            DBUtils.logAlipayHostTxnLog(QRHost.getDataSource(), alipayHostTxnLog);
            resend= Boolean.TRUE;

        }
        logger.debug("resend:"+resend);
        // check if system error from Alipay then resend
        if(resend) {
            if(null != cancelReq) {
                try {
                    RetryTask task = new RetryTask();
                    task.scheduleTask(cancelReq, refStr, txnLog, alipayHostTxnLog, 0);
                } catch (Exception e){
                    logger.error("unable to schedule retry:", e);
                }
            }else {
                logger.info("Cancel request is null, unable to resend reversal");
            }
        }
    }

    public static void doResendCancelTransaction(CancelReq cancelReq, String refStr, AlipayTmlTxnLog txnLog, AlipayHostTxnLog alipayHostTxnLog, int noOfRetry, PropertiesConfiguration properties) {
        Boolean resend = Boolean.FALSE;
        CancelRes result= null;
        try {
            result= (CancelRes) AlipayUtils.postRequest(properties, cancelReq.getNameValuePair(), CancelRes.class);

            if (result!=null) {
                alipayHostTxnLog.setTransmitStatus("OK");
                alipayHostTxnLog.setAlipayResponseMessage(result.toString());
                if (result.getResponse().getAlipayResponse().getResultCode()!=null) {

                    alipayHostTxnLog.setAlipayResponseResult(result.getResponse().getAlipayResponse().getResultCode());
                    if(result.getResponse().getAlipayResponse().getResultCode().equals(FAIL) && result.getResponse().getAlipayResponse().getDetailErrorCode().equals(SYSTEM_ERROR)){
                        resend= Boolean.TRUE;
                    }
                }
            } else {
                logger.error("Unable to cancel Alipay transaction ID: " + refStr);
            }

            DBUtils.logAlipayHostTxnLog(QRHost.getDataSource(), alipayHostTxnLog);



        } catch (Exception e) {
            logger.error("Error in Alipay doCancelTransaction",e);
            alipayHostTxnLog.setAlipayReferenceNumber(refStr);
            alipayHostTxnLog.setAmount(Integer.toString(txnLog.getAmount()));
            alipayHostTxnLog.setVehicleId(txnLog.getTaxiNumber());
            alipayHostTxnLog.setJobId(txnLog.getJobNumber());
            alipayHostTxnLog.setDriverId(txnLog.getDriverId());
            alipayHostTxnLog.setSerialNo(txnLog.getPinpadSerial());
            //alipayHostTxnLog.setTransDateTime(Utils.format(new Date(),"ddMMyy HHmmss"));
            alipayHostTxnLog.setAlipayRequestMessage("");
            alipayHostTxnLog.setTransmitStatus("Error");
            alipayHostTxnLog.setAlipayResponseResult(e.toString());

            DBUtils.logAlipayHostTxnLog(QRHost.getDataSource(), alipayHostTxnLog);
            resend= Boolean.TRUE;

        }

        if(resend) {
            int maxRetry = Integer.valueOf(properties.getString("cancel.retry.times"));
            if(null != cancelReq) {
                if (noOfRetry < maxRetry) {

                    try {
                        RetryTask task = new RetryTask();
                        task.scheduleTask(cancelReq, refStr, txnLog, alipayHostTxnLog, noOfRetry);
                    } catch (Exception e) {
                        logger.error("unable to schedule retry:", e);
                    }

                } else {
                    logger.info("Maximum retry for reversal reached, number of retry sent: " + noOfRetry + ", max retry: " + maxRetry + ">>" + (null == result ? cancelReq.toString() : result.toString()));
                }
            } else {
                logger.info("Cancel request is null, unable to resend reversal");
            }
        }
    }
}
