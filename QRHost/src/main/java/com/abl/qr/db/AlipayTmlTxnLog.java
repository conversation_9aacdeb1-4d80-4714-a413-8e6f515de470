package com.abl.qr.db;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * User: koh<PERSON><PERSON><PERSON>
 * Date: 5/12/13
 * Time: 9:14 AM
 * To change this template use File | Settings | File Templates.
 */
public class AlipayTmlTxnLog {
    private int id;
    private int amount;
    private String approvalCode;
    private String companyCode;
    private String driverId;
    private String fareAdmin;
    private String fareAmount;
    private String fareGst;
    private String jobNumber;
    private Date logDateTime;
    private String MTI;
    private String pinpadSerial;
    private String procCode;
    private String respCode;
    private Date respDateTime;
    private String rrn;
    private String STAN;
    private String taxiNumber;
    private String TID;

    @Getter @Setter
    private String qrData;

    public AlipayTmlTxnLog() {
        //respDateTime=new Date();
        logDateTime=new Date();
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getAmount() {
        return amount;
    }

    public void setAmount(int amount) {
        this.amount = amount;
    }

    public String getApprovalCode() {
        return approvalCode;
    }

    public void setApprovalCode(String approvalCode) {
        this.approvalCode = approvalCode;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getDriverId() {
        return driverId;
    }

    public void setDriverId(String driverId) {
        this.driverId = driverId;
    }

    public String getFareAdmin() {
        return fareAdmin;
    }

    public void setFareAdmin(String fareAdmin) {
        this.fareAdmin = fareAdmin;
    }

    public String getFareAmount() {
        return fareAmount;
    }

    public void setFareAmount(String fareAmount) {
        this.fareAmount = fareAmount;
    }

    public String getFareGst() {
        return fareGst;
    }

    public void setFareGst(String fareGst) {
        this.fareGst = fareGst;
    }

    public String getJobNumber() {
        return jobNumber;
    }

    public void setJobNumber(String jobNumber) {
        this.jobNumber = jobNumber;
    }

    public Date getLogDateTime() {
        return logDateTime;
    }

    public void setLogDateTime(Date logDateTime) {
        this.logDateTime = logDateTime;
    }

    public String getMTI() {
        return MTI;
    }

    public void setMTI(String MTI) {
        this.MTI = MTI;
    }

    public String getPinpadSerial() {
        return pinpadSerial.trim();
    }

    public void setPinpadSerial(String pinpadSerial) {
        this.pinpadSerial = pinpadSerial;
    }

    public String getProcCode() {
        return procCode;
    }

    public void setProcCode(String procCode) {
        this.procCode = procCode;
    }

    public String getRespCode() {
        return respCode;
    }

    public void setRespCode(String respCode) {
        this.respCode = respCode;
    }

    public Date getRespDateTime() {
        return respDateTime;
    }

    public void setRespDateTime(Date respDateTime) {
        this.respDateTime = respDateTime;
    }

    public String getRrn() {
        return rrn;
    }

    public void setRrn(String rrn) {
        this.rrn = rrn;
    }

    public String getSTAN() {
        return STAN;
    }

    public void setSTAN(String STAN) {
        this.STAN = STAN;
    }

    public String getTID() {
        return TID;
    }

    public void setTID(String TID) {
        this.TID = TID;
    }

    public String getTaxiNumber() {
        return taxiNumber;
    }

    public void setTaxiNumber(String taxiNumber) {
        this.taxiNumber = taxiNumber;
    }

    @Override
    public String toString() {
        return "AlipayTmlTxnLog{" +
                "id=" + id +
                ", amount=" + amount +
                ", approvalCode='" + approvalCode + '\'' +
                ", companyCode='" + companyCode + '\'' +
                ", driverId='" + driverId + '\'' +
                ", fareAdmin='" + fareAdmin + '\'' +
                ", fareAmount='" + fareAmount + '\'' +
                ", fareGst='" + fareGst + '\'' +
                ", jobNumber='" + jobNumber + '\'' +
                ", logDateTime=" + logDateTime +
                ", MTI='" + MTI + '\'' +
                ", pinpadSerial='" + pinpadSerial + '\'' +
                ", procCode='" + procCode + '\'' +
                ", respCode='" + respCode + '\'' +
                ", respDateTime=" + respDateTime +
                ", rrn='" + rrn + '\'' +
                ", STAN='" + STAN + '\'' +
                ", taxiNumber='" + taxiNumber + '\'' +
                ", TID='" + TID + '\'' +
                ", qrData='" + qrData + '\'' +
                '}';
    }
}
