package com.abl.qr.db;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;


/**
 * Created with IntelliJ IDEA.
 * User: koh<PERSON><PERSON><PERSON>
 * Date: 3/12/13
 * Time: 10:23 PM
 * To change this template use File | Settings | File Templates.
 */
public class AlipayHostTxnLog {
    @Getter @Setter
    private int id;
    @Getter @Setter
    private String amount;
    @Getter @Setter
    private String AlipayReferenceNumber;
    @Getter @Setter
    private String AlipayRequestMessage;
    @Getter @Setter
    private String AlipayResponseMessage;
    @Getter @Setter
    private String AlipayResponseResult;
    @Getter @Setter
    private String messageType;
    //@Getter @Setter
    //private String transDateTime;
    @Getter @Setter
    private String transactionId;
    @Getter @Setter
    private String transmitStatus;
    @Getter @Setter
    private String vehicleId;
    @Getter @Setter
    private String jobId;
    @Getter @Setter
    private String driverId;
    @Getter @Setter
    private String serialNo;

    public AlipayHostTxnLog() {
    }

    @Override
    public String toString() {
        return "AlipayHostTxnLog{" +
                "id=" + id +
                ", amount='" + amount + '\'' +
                ", AlipayReferenceNumber='" + AlipayReferenceNumber + '\'' +
                ", AlipayRequestMessage='" + AlipayRequestMessage + '\'' +
                ", AlipayResponseMessage='" + AlipayResponseMessage + '\'' +
                ", AlipayResponseResult='" + AlipayResponseResult + '\'' +
                ", messageType='" + messageType + '\'' +
                //", transDateTime='" + transDateTime + '\'' +
                ", transactionId='" + transactionId + '\'' +
                ", transmitStatus='" + transmitStatus + '\'' +
                ", vehicleId='" + vehicleId + '\'' +
                ", jobId='" + jobId + '\'' +
                ", driverId='" + driverId + '\'' +
                ", serialNo='" + serialNo + '\'' +
                '}';
    }
}
