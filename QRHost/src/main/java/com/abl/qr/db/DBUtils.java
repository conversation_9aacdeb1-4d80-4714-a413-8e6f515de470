package com.abl.qr.db;

import org.apache.log4j.Logger;

import javax.sql.DataSource;
import java.sql.*;

/**
 * Created with IntelliJ IDEA.
 * User: koh<PERSON><PERSON><PERSON>
 * Date: 3/12/13
 * Time: 7:20 PM
 * To change this template use File | Settings | File Templates.
 */
public class DBUtils {
    public static final Logger logger= Logger.getLogger(DBUtils.class);

    public static final String ALIPAY_QR_TABLE ="TMTB_ALIPAY_HOST_TXN_LOG";
    public static final String QR_TXN_TABLE ="TMTB_ALIPAY_TML_TXN_LOG";

    /**
     * This helper function is to log the request and response message to <PERSON>pay host.
     *
     * Called by RegisterPOSPayment, CancelPOSPayment, GetPOSStatus, NotifyPOS (Servlet)
     *
     * @return
     * 1 - OK
     * 0 - No record inserted
     * -1 - Error
     */
    public static int logAlipayHostTxnLog(DataSource dataSource, AlipayHostTxnLog data) {
        Connection conn = null;
        PreparedStatement stmt = null;

        int totalCnt=0;

        logger.debug("Logging to db:" +data);
        String insert="INSERT INTO "+ ALIPAY_QR_TABLE +" (AMOUNT,CREATE_DT,ALIPAY_REQUEST_MESSAGE,"+
                "ALIPAY_RESPONSE_MESSAGE,ALIPAY_REFERENCE_NUMBER,ALIPAY_RESPONSE_RESULT,"+
                "DRIVER_ID,JOB_NUMBER,MESSAGE_TYPE,SERIAL_NO,TRANSACTION_ID,TRANSMIT_STATUS,VEHICLE_ID,ID) "+
                "VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,TMSQ_ALIPAY_HOST_TXN_LOG.NEXTVAL)";
        //logger.debug("insert sql:"+insert);
        try {
            conn = dataSource.getConnection();
            stmt = conn.prepareStatement(insert);

            stmt.setString(1, data.getAmount());
            stmt.setTimestamp(2, new Timestamp(System.currentTimeMillis()));
            stmt.setString(3, data.getAlipayRequestMessage());
            stmt.setString(4, data.getAlipayResponseMessage());
            stmt.setString(5, data.getAlipayReferenceNumber());
            stmt.setString(6, data.getAlipayResponseResult());
            stmt.setString(7, data.getDriverId());
            stmt.setString(8, data.getJobId());
            stmt.setString(9, data.getMessageType());
            stmt.setString(10, data.getSerialNo());
            //stmt.setString(11, data.getTransDateTime());
            stmt.setString(11, data.getTransactionId());
            stmt.setString(12, data.getTransmitStatus());
            stmt.setString(13, data.getVehicleId());

            totalCnt=stmt.executeUpdate();

        } catch(SQLException e) {
            totalCnt=-1;

            logger.error(e.toString());
        } finally {
            //try { if (rset != null) rset.close(); } catch(Exception e) { }
            try { if (stmt != null) stmt.close(); } catch(Exception e) { }
            try { if (conn != null) conn.close(); } catch(Exception e) { }
        }
        return totalCnt;
        //return 0;
    }

    /**
     * This helper function is used to log the Alipay QR transactions between QRHost and PIN pad in to DB.
     * Log the transaction status. Eg, Sales approved, TO, reversed, etc.
     *
     * @return
     */
    public static int logAlipayTmlTransactions(DataSource dataSource, AlipayTmlTxnLog log) {
        Connection conn = null;
        PreparedStatement stmt = null;

        int totalCnt=0;

        logger.debug("Logging to db:" +log);
        String insert="INSERT INTO "+ QR_TXN_TABLE +" (AMOUNT,APPROVAL_CODE,COMPANY_CODE,DRIVER_ID,FARE_ADMIN,"+
                "FARE_AMOUNT,FARE_GST,JOB_NUMBER,LOG_DATE_TIME,MTI,PINPAD_SN,PROC_CODE,RESP_CODE,RESPONSE_DATE_TIME,"+
                "RRN,STAN,TAXI_NUMBER,TID,ID) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,TMSQ_ALIPAY_TML_TXN_LOG.NEXTVAL)";
        try {
            conn = dataSource.getConnection();
            stmt = conn.prepareStatement(insert);
            stmt.setInt(1, log.getAmount());
            stmt.setString(2, log.getApprovalCode());
            stmt.setString(3, log.getCompanyCode());
            stmt.setString(4, log.getDriverId());
            stmt.setString(5, log.getFareAdmin());
            stmt.setString(6, log.getFareAmount());
            stmt.setString(7, log.getFareGst());
            stmt.setString(8, log.getJobNumber());
            stmt.setTimestamp(9, new Timestamp(log.getLogDateTime().getTime()));
            stmt.setString(10, log.getMTI());
            stmt.setString(11, log.getPinpadSerial());
            stmt.setString(12, log.getProcCode());
            stmt.setString(13, log.getRespCode());
            if (log.getRespDateTime()!=null)
                stmt.setTimestamp(14, new Timestamp(log.getRespDateTime().getTime()));
            else
                stmt.setTimestamp(14, null);
            stmt.setString(15, log.getRrn());
            stmt.setString(16, log.getSTAN());
            stmt.setString(17, log.getTaxiNumber());
            stmt.setString(18, log.getTID());

            totalCnt=stmt.executeUpdate();

        } catch(SQLException e) {
            totalCnt=-1;

            logger.error(e.toString());
        } finally {
            try { if (stmt != null) stmt.close(); } catch(Exception e) { }
            try { if (conn != null) conn.close(); } catch(Exception e) { }
        }
        return totalCnt;
        //return 0;
    }

    /**
     * Returns the last STAN of the alipay pre-create
     *
     * @param driverID
     * @param jobNo
     * @param taxiNo
     * @return STAN
     */
    public static String getSTANofLastAlipayPrecreate(DataSource dataSource, String driverID, String jobNo, String taxiNo) {
        String stan=null;

        //String query="SELECT STAN FROM "+QR_TXN_TABLE+" WHERE PROC_CODE='520000' AND MTI='0100' AND DRIVER_ID=? AND JOB_NUMBER=? AND " +
        //        "TAXI_NUMBER=? AND ROWNUM <= 1 ORDER BY LOG_DATE_TIME DESC";
        String query="SELECT * FROM (SELECT STAN FROM "+QR_TXN_TABLE+" WHERE PROC_CODE='520000' AND MTI='0100' AND DRIVER_ID=? AND JOB_NUMBER=? AND TAXI_NUMBER=? ORDER BY LOG_DATE_TIME DESC) WHERE ROWNUM<=1";
        Connection conn = null;
        PreparedStatement stmt = null;

        int totalCnt=0;

        logger.debug("Query from db:" +driverID+","+jobNo+","+taxiNo);

        try {
            conn = dataSource.getConnection();
            stmt = conn.prepareStatement(query);
            stmt.setString(1, driverID);
            stmt.setString(2, jobNo);
            stmt.setString(3, taxiNo);

            ResultSet rs=stmt.executeQuery();

            if  (rs!=null && rs.next()) {
                stan=rs.getString("STAN");
            }

        } catch(SQLException e) {
            logger.error(e.toString());
        } finally {
            try { if (stmt != null) stmt.close(); } catch(Exception e) { }
            try { if (conn != null) conn.close(); } catch(Exception e) { }
        }

        return stan;
    }
}
