package com.abl.qr;

import org.apache.log4j.Logger;
import org.jpos.util.LogEvent;
import org.jpos.util.LogListener;

import java.io.ByteArrayOutputStream;
import java.io.PrintStream;

/**
 * capture logging events from JPos and log using log4j
 */
public class JposLogListener implements LogListener {

    private static final Logger logger = Logger.getLogger(JposLogListener.class);

    @Override
    public LogEvent log(LogEvent logEvent) {

        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        PrintStream ps = new PrintStream(baos);
        logEvent.dump(ps, "");

        logger.info(baos.toString());

        return logEvent;
    }

}
