jpos.packager=conf/abl.xml

q2.deploy.dir=./conf/q2


# comfort UAT
db.url=
db.user=
db.pass=
db.driver=oracle.jdbc.driver.OracleDriver

mq.max.wait.in.seconds=8
mq.alipay.try.count=3

alipay.connect.timeout=5000
alipay.socket.timeout=5000
alipay.request.timeout=5000
alipay.alipayResponse.wait.time=30000
alipay.host=https://openapi.alipaydev.com/gateway.do

alipay.product.code=OVERSEAS_MBARCODE_PAY
alipay.currency=SGD
alipay.charset=utf-8
alipay.notify.url=https://payment-alipay-uat.cdgtaxi.com.sg/WebNotify/notify
alipay.precreate.service=alipay.acquire.precreate
alipay.subject=Comfort Taxi
alipay.sign.type=MD5

alipay.query.service=alipay.acquire.overseas.query
alipay.cancel.service=alipay.acquire.cancel

alipay.send.reversal=true

# Merchant <EMAIL> - Cross-border Online Payment(WAP)
# alipay.partner.id=2088621876741862
# alipay.md5.signature=gfje4xzsg6sldklxrvhn1wnb9yvbedy2

# <NAME_EMAIL> - Cross-border Online Payment(WAP)
alipay.partner.id=2088621926114040
alipay.md5.signature=1rn2n7k3d04b4zkycntozkchcjd66qo4

proxy.server=************
proxy.port=80

mq.url=tcp://localhost:61616
mq.alipay.topic=alipay.notify

cancel.retry.times=3
cancel.retry.interval.seconds=3