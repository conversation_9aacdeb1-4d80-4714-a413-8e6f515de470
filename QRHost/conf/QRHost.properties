jpos.packager=conf/abl.xml

q2.deploy.dir=./conf/q2


db.url=
db.user=
db.pass=
db.driver=oracle.jdbc.driver.OracleDriver

alipay.connect.timeout=5000
alipay.socket.timeout=5000
alipay.request.timeout=5000
alipay.alipayResponse.wait.time=30000
alipay.host=https://openapi.alipaydev.com/gateway.do
alipay.send.reversal=true

# Following is to replace the alipay.alipayResponse.wait.time (in seconds, default 29 seconds)
mq.max.wait.in.seconds=29

alipay.product.code=OVERSEAS_MBARCODE_PAY
alipay.currency=SGD
alipay.charset=UTF-8
alipay.notify.url=https://d.glo.asia/notify/alipay
alipay.service=alipay.acquire.precreate
alipay.subject=Comfort Taxi
alipay.sign.type=MD5

# Merchant <EMAIL> - Cross-border Online Payment(WAP)
#alipay.partner.id=2088101181803054
#alipay.md5.signature=try71v5o21266f4uvbo9h1b81dg4jmtt

alipay.partner.id=2088021966388155
alipay.md5.signature=yq9ud70vvpmnsxk7u75g27jfwg02nenhalipay.host

# Merchant <EMAIL>  - New Cross-border Online Payment(PC/WAP)
#alipay.partner.id=2088101181811460
#alipay.md5.signature=ue9jzyd3dflq0k1iq4xnbrqtw95pcu8g

mq.alipay.topic=alipay.notify
cancel.retry.times=3
cancel.retry.interval.seconds=3