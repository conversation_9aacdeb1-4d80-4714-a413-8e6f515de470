log4j.rootLogger=DEBUG, R

log4j.logger.org.apache.commons=WARN
log4j.logger.org.apache.commons.beanutils=WARN
log4j.logger.org.apache.http=WARN

#### First appender writes to console
log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
# Pattern to output the caller's file name and line number.
log4j.appender.stdout.layout.ConversionPattern=%d{dd/MM/yyyy HH:mm:ss,SSS} [%t] (%F:%L) - %m%n

#### Second appender writes to a file
#log4j.appender.R=org.apache.log4j.FileAppender
log4j.appender.R=org.apache.log4j.RollingFileAppender
log4j.appender.R.File=./logs/QRHost.log

# Control the maximum log file size
log4j.appender.R.MaxFileSize=1024KB
# Archive log files (one backup file here)
log4j.appender.R.MaxBackupIndex=10
#log4j.appender.R.Append=false
log4j.appender.R.layout=org.apache.log4j.PatternLayout
# log4j.appender.R.layout.ConversionPattern=%p %t %c - %m%n
log4j.appender.R.layout.ConversionPattern=%d{dd/MM/yyyy HH:mm:ss,SSS} [%t] (%F:%L) - %m%n
