<server name="Q-server" class="org.jpos.q2.iso.QServer" logger="Q2" realm="Q2.host.server">

    <attr name="port" type="java.lang.Integer">5659</attr>
	<attr name="minSessions" type="java.lang.Integer">500</attr>  <!-- Min -->
	<attr name="maxSessions" type="java.lang.Integer">5000</attr> <!-- Max -->

    <channel name="server.channel"
            class="org.jpos.iso.channel.NACChannel"
            packager="org.jpos.iso.packager.GenericPackager" header="6000028000"
            logger="Q2" realm="Q2.host.server">
        <property name="packager-config" value="conf/abl.xml" />
    </channel>

    <request-listener class="com.abl.qr.RequestListener">
        <property name="space" value="transient:tmsServer:space/qrHost" />
        <property name="queue" value="qrHost" />
        <property name="timeout" value="10000" />
    </request-listener>
</server>
