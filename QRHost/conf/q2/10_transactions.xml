<txnmgr name="TxnMgr" logger="Q2" class="org.jpos.transaction.TransactionManager">

    <property name="space" value="transient:tmsServer:space/qrHost" />
    <property name="queue" value="qrHost" />
    <property name="sessions" value="100" />

	<participant class="com.abl.qr.participant.BeginTransaction"/>

    <participant class="com.abl.qr.participant.SelectTxn">
        <property name="REGISTER_PAYMENT"   value="CreateResponse InsertTxnLog RegisterPayment PrepareResponse SaveTxn SendResponse" />
        <property name="REQUEST_PAYMENT"    value="CreateResponse InsertTxnLog CheckQRResponse PrepareResponse SaveTxn SendResponse" />
        <property name="REVERSAL"           value="CreateResponse InsertTxnLog Reversal PrepareResponse SaveTxn SendResponse"/>
        <property name="UNKNOWN"			value="Unknown PrepareResponse SaveTxn SendResponse" />
    </participant>

    <group name="InsertTxnLog">
        <participant class="com.abl.qr.participant.InsertTxnLog"/>
    </group>

    <group name="CreateResponse">
        <participant class="com.abl.qr.participant.CreateResponse"/>
    </group>

    <group name="Reversal">
        <participant class="com.abl.qr.participant.Reversal"/>
    </group>

    <group name="RegisterPayment">
        <participant class="com.abl.qr.participant.RegisterQRPayment"/>
    </group>

    <group name="CheckQRResponse">
        <participant class="com.abl.qr.participant.CheckQRResponse"/>
    </group>

    <group name="PrepareResponse">
        <participant class="com.abl.qr.participant.UpdateResponseCode"/>
    </group>

    <group name="SaveTxn">
        <participant class="com.abl.qr.participant.SaveTxn"/>
    </group>

    <group name="SendResponse">
        <participant class="com.abl.qr.participant.SendResponse"/>
    </group>

    <group name="Unknown">
        <participant class="com.abl.qr.participant.Abort"/>
    </group>
</txnmgr>
