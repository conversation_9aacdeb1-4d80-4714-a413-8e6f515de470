 create table TMTB_ALIPAY_HOST_TXN_LOG (
        id number(19,0) not null,
        amount varchar2(12 char) not null,
        create_dt date not null,
        alipay_request_message varchar2(2048 char),
        alipay_response_message varchar2(2048 char),
        alipay_reference_number varchar2(64 char),
        alipay_response_result varchar2(500 char),
        driver_id varchar2(9 char),
        job_number varchar2(10 char),
        message_type varchar2(30 char),
        serial_no varchar2(20 char),
        transaction_id varchar2(30 char),
        transmit_status varchar2(50 char),
        vehicle_id varchar2(15 char),
        primary key (id)
    );

    create table TMTB_ALIPAY_TML_TXN_LOG (
        id number(19,0) not null,
        amount number(19,0),
        approval_code varchar2(6 char),
        company_code varchar2(4 char),
        driver_id varchar2(9 char),
        fare_admin varchar2(6 char),
        fare_amount varchar2(6 char),
        fare_gst varchar2(6 char),
        job_number varchar2(10 char),
        log_date_time date,
        mti varchar2(4 char),
        pinpad_sn varchar2(20 char),
        proc_code varchar2(6 char),
        resp_code varchar2(2 char),
        response_date_time date,
        rrn varchar2(12 char),
        stan varchar2(6 char),
        taxi_number varchar2(12 char),
        tid varchar2(8 char),
        primary key (id)
    );


CREATE SEQUENCE TMSQ_ALIPAY_TML_TXN_LOG
 START WITH     1
 INCREMENT BY   1
 NOCACHE
 NOCYCLE;

CREATE SEQUENCE TMSQ_ALIPAY_HOST_TXN_LOG
 START WITH     1
 INCREMENT BY   1
 NOCACHE
 NOCYCLE;