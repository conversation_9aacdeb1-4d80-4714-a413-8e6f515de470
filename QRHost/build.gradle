version '1.6'

apply plugin: 'java'
apply plugin: 'idea'
apply plugin: 'application'

mainClassName = ""

sourceCompatibility = 1.6

task initSourceFolders { // add << before { to prevent executing during configuration phase
    sourceSets*.java.srcDirs*.each { it.mkdirs() }
    sourceSets*.resources.srcDirs*.each { it.mkdirs() }
}

repositories {
    maven { url 'http://download.oracle.com/maven' }
    flatDir {
        dirs 'libs'
    }
    mavenCentral()
}

dependencies {
    testCompile group: 'junit', name: 'junit', version: '4.11'

    compile 'log4j:log4j:1.2.16+'
    compile 'commons-configuration:commons-configuration:1.10+'
    compile group: 'com.google.guava', name: 'guava', version: '19.0'
    // https://mvnrepository.com/artifact/commons-net/commons-net
    compile group: 'commons-net', name: 'commons-net', version: '3.5'
    // https://mvnrepository.com/artifact/commons-io/commons-io
    compile group: 'commons-io', name: 'commons-io', version: '2.5'
    // https://mvnrepository.com/artifact/org.apache.commons/commons-collections4
    compile group: 'org.apache.commons', name: 'commons-collections4', version: '4.1'
    // https://mvnrepository.com/artifact/org.jpos/jpos
    compile group: 'org.jpos', name: 'jpos', version: '2.0.10'
    // https://mvnrepository.com/artifact/com.google.code.gson/gson
    compile group: 'com.google.code.gson', name: 'gson', version: '2.8.0'
    // https://mvnrepository.com/artifact/org.apache.commons/commons-dbcp2
    compile group: 'org.apache.commons', name: 'commons-dbcp2', version: '2.1.1'
    // https://mvnrepository.com/artifact/org.apache.activemq/activemq-client
    compile group: 'org.apache.activemq', name: 'activemq-client', version: '5.14.4'
    // https://mvnrepository.com/artifact/org.apache.httpcomponents/httpclient
    compile group: 'org.apache.httpcomponents', name: 'httpclient', version: '4.5.3'
    // https://mvnrepository.com/artifact/javax.xml.bind/jaxb-api
    compile group: 'javax.xml.bind', name: 'jaxb-api', version: '2.3.0-b170201.1204'
    // https://mvnrepository.com/artifact/com.squareup.okhttp/okhttp
    compile group: 'com.squareup.okhttp', name: 'okhttp', version: '2.7.5'
    // https://mvnrepository.com/artifact/org.projectlombok/lombok
    compile group: 'org.projectlombok', name: 'lombok', version: '1.16.16'
    // https://mvnrepository.com/artifact/commons-codec/commons-codec
    compile group: 'commons-codec', name: 'commons-codec', version: '1.10'
    // https://mvnrepository.com/artifact/commons-cli/commons-cli
    compile group: 'commons-cli', name: 'commons-cli', version: '1.3.1'
    // https://mvnrepository.com/artifact/org.bouncycastle/bcprov-jdk15on
    compile group: 'org.bouncycastle', name: 'bcprov-jdk15on', version: '1.56'
    // https://mvnrepository.com/artifact/commons-beanutils/commons-beanutils
    compile group: 'commons-beanutils', name: 'commons-beanutils', version: '1.9.3'
    // https://mvnrepository.com/artifact/com.nimbusds/nimbus-jose-jwt
    compile group: 'com.nimbusds', name: 'nimbus-jose-jwt', version: '4.37.1'

    compile fileTree(dir: 'libs', include: '*.jar')
}

task copyToDeploy(type: Copy) {
    from zipTree('build/distributions/Comfort-QRHost-1.5.zip')
    into 'deploy'

    new File("deploy/Comfort-QRHost-1.5/log").mkdirs()
}

copyToDeploy.dependsOn distZip

task tJar(type: Jar, dependsOn: compileJava) {
    from sourceSets.main.output.classesDir
    include 'com/abl/qr/db/*'
    include 'com/abl/qr/utils/Utils*'
    include 'com/abl/qr/utils/ThreadSafeSimpleDateFormat*'
    //Jar.includeEmptyDirs = false
    archiveName = 'QRHost-dblib-1.0.jar'
}
