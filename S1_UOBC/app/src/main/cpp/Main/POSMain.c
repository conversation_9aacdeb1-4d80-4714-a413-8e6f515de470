/*******************************************************************************

*******************************************************************************/

#include <string.h>
#include <stdio.h>
#include <ctosapi.h>
#include <ctoserr.h>
#include <stdlib.h>
#include <stdarg.h>
#include <emv_cl.h>

#include "..\Includes\POSTypedef.h"
#include "..\Debug\Debug.h"

#include "..\FileModule\myFileFunc.h"
#include "..\DataBase\DataBaseFunc.h"

#include "..\Includes\showbmp.h"
#include "..\Includes\POSMain.h"
#include "..\Includes\POSTrans.h"
#include "..\Includes\POSHost.h"
#include "..\Includes\POSSale.h"
#include "..\Includes\POSConfig.h"
#include "..\Includes\epad.h"

#include "..\ui\Display.h"
#include "..\Includes\Dmenu.h"
#include "..\Includes\POSVoid.h"
#include "..\powrfail\POSPOWRFAIL.h"
#include "..\Comm\V5Comm.h"
#include "..\Includes\CardUtil.h"

#include "..\Includes\MultiApLib.h"
#include "..\Aptrans\MultiAptrans.h"
#include "..\Includes\CfgExpress.h"
#include "..\Includes\Wub_lib.h"
#include "..\Aptrans\MultiShareECR.h"
#include "..\Aptrans\MultiShareCOM.h"
#include "..\TMS\TMS.h"
#include "..\Ctls\POSCtls.h"
#include "..\Includes\POSSetting.h"
#include "..\Ctls\PosWave.h"
#include "..\Erm\PosErm.h"
#include "..\Aptrans\MultiShareEMV.h"
#include "..\PCI100\PCI100.h"
#include "..\Htle\htlesrc.h"
#include "..\Includes\epad.h"
#include "..\Includes\CTOSInput.h"
#include "../Aptrans/MultiAptrans.h"

#include <ctos_qrcode.h>
#include <stdbool.h>
#include <android_jni_log.h>

//#define UNUSE
//#define NEWICON
extern USHORT CTOS_VirtualFunctionKeyHit(BYTE *key);
extern USHORT CTOS_VirtualFunctionKeySet( CTOS_VIRTUAL_FUNCTION_KEY_SET_PARA* pPara, BYTE FuncKeyCount);
unsigned long gulInitStartTime = 0;
CTOS_VIRTUAL_FUNCTION_KEY_SET_PARA *nPtr = NULL;
BYTE funckey_count = 0;
BYTE byIsIdleReversal = 0;
CTOS_VIRTUAL_FUNCTION_KEY_SET_PARA stKeyPara [4] = 
{
    {0,400, 80, 480},  //F1
    {80, 400, 160, 480}, //F2
    {160, 400, 240, 480}, //F3
    {240, 400, 320, 480} //F4
};

extern USHORT GPRSCONNETSTATUS;

static BYTE stgFirstIdleKey = 0x00;
static BYTE gIdleEventSC_MSR = 0x00;
extern BOOL fGPRSConnectOK;
extern USHORT usHWSupport;
static BYTE stgIdleEvent= 0;

int g_inHK2Mode = 0;

void vdCTOSS_ClearTouchPanelTest(void)
{
	BYTE str[256],size = 5;
	ULONG rb;
	BYTE count = 0;

	int pen_up = 0;
	int pen_down = 0;
	int bXpen_up = 0, bXpen_down = 0;
	int bXCoordinate = 0, bYCoordinate = 0;
	int temp_iX = 0,temp_iY = 0;
	USHORT ret;
    BYTE key;

	//if (strTCT.byTerminalType == 4)
	if ((strTCT.byTerminalType%2) == 0)
	{
		do{
			ret = CTOS_VirtualFunctionKeyHit(&key);
			vdDebug_LogPrintf("CTOS_VirtualFunctionKeyHit.key=[%d][%c]",key,key);
			if (key == d_KBD_INVALID)
			{
				CTOS_KBDHit(&key);
				break;
			}

		}while(key != d_KBD_INVALID);
	}
	
}

BYTE chGetFirstIdleKey(void)
{
//	vdDebug_LogPrintf("chGetFirstIdleKey=%d", stgFirstIdleKey);
    return stgFirstIdleKey;
}

void vdSetFirstIdleKey(BYTE bFirstKey)
{
//	vdDebug_LogPrintf("vdSetFirstIdleKey=%d", bFirstKey);
    stgFirstIdleKey = bFirstKey;
}

BYTE chGetIdleEvent(void)
{
    return stgIdleEvent;
}

void vdSetIdleEvent(BYTE bIdleEvent)
{
    stgIdleEvent = bIdleEvent;
}


BYTE chGetIdleEventSC_MSR(void)
{
    return gIdleEventSC_MSR;
}

void vdSetIdleEventSC_MSR(BYTE bIdleEventSC_MSR)
{
    gIdleEventSC_MSR = bIdleEventSC_MSR;
}


int inCTOS_ValidFirstIdleKey(void)
{
    if((chGetFirstIdleKey() >= d_KBD_1) && (chGetFirstIdleKey() <= d_KBD_9))
        return d_OK;
    else
        return d_NO;
}

int inCTOS_ECRTask(void)
{
	vdDebug_LogPrintf("inCTOS_ECRTask [%0ld]", strTCT.fECR);
	return 0;
    if (strTCT.fECR) // tct
    {
		BYTE bInBuf[40];
		BYTE bOutBuf[40];
		BYTE *ptr = NULL;
		USHORT usInLen = 0;
		USHORT usOutLen = 0;
		USHORT usResult;

		BYTE ipc[d_MAX_IPC_BUFFER] , str[d_MAX_IPC_BUFFER];
		int ipc_len;
		BYTE bret = d_NO;
		BYTE outbuf[d_MAX_IPC_BUFFER];
		USHORT out_len = 0;
		pid_t my_pid;

	    // inMultiAP_ECRGetMainroutine(); 
	    
		memset(bOutBuf, 0x00, sizeof(bOutBuf)); 
		usResult = inMultiAP_RunIPCCmdTypesEx("com.persistent.app", d_IPC_CMD_ECR_Event, bInBuf, usInLen, bOutBuf, &usOutLen);
		vdDebug_LogPrintf("usResult=[%d],usResult=[%x]",usResult,usResult);
		if (d_OK == usResult)
		{
			// if (memcmp(&bOutBuf[2], SHARE_CTLS_NONEMVCARD, 1) == 0)
			if (bOutBuf[2] != 0x00)
			{				
				vdSetECRTransactionFlg(1);
				// patrick add code 20141205 start		
				// inSendECRResponseFlag = 0;
				put_env_int("ECRRESP",0);//hubing enhance ECR
				ipc_len = inMultiAP_HandleIPC(&bOutBuf[2] , 1, outbuf, &out_len);//Do IPC request
		
				// patrick add code 20141205 start
				// inSendECRResponseFlag = get_env_int("ECRRESP");//hubing enhance ECR
				// if (inSendECRResponseFlag == 1) 
				//	return d_OK;
				// patrick add code 20141205 end
		
				if (out_len == 0)
				{
					out_len = 0;
					outbuf[out_len++] = d_FAIL;
					outbuf[out_len] = 0x00;
				}
		
				inTCTSave(1);
				
				bret= inMultiAP_Database_BatchDelete();
				vdDebug_LogPrintf("inMultiAP_Database_BatchDelete,bret=[%d]", bret);
				if(d_OK != bret)
				{
					vdSetErrorMessage("MultiAP BatchDelete ERR");
				}
				vdDebug_LogPrintf("Resp PAN:%s",srTransRec.szPAN);
				vdDebug_LogPrintf("Resp DE39:%s",srTransRec.szRespCode);
				vdDebug_LogPrintf("Resp RREF:%s",srTransRec.szRRN);
				vdDebug_LogPrintf("Resp ExpDate:[%02X%02X]",srTransRec.szExpireDate[0], srTransRec.szExpireDate[1]);
				vdDebug_LogPrintf("Resp MID:%s",srTransRec.szTID);
				vdDebug_LogPrintf("Resp TID:%s",srTransRec.szMID);
				vdDebug_LogPrintf("Inv Num:[%02X%02X%02X]",srTransRec.szInvoiceNo[0], srTransRec.szInvoiceNo[1], srTransRec.szInvoiceNo[2]);
			
				bret = inMultiAP_Database_BatchInsert(&srTransRec);
				vdDebug_LogPrintf("inMultiAP_Database_BatchInsert=[%d]", bret);
				if(d_OK != bret)
				{
					vdSetErrorMessage("MultiAP BatchInsert ERR");
				}
		
				vdSetECRTransactionFlg(0);
				memset( &srTransRec, 0x00, sizeof(TRANS_DATA_TABLE));
				
				//bret = inMultiAP_SendChild(outbuf,out_len);
				//hubing enhance ECR
				// bret = inMultiAP_SendAP_IPCCmd("com.persistent.app", outbuf,out_len);
				
				memset(bOutBuf, 0x00, sizeof(bOutBuf)); 
				usResult = inMultiAP_RunIPCCmdTypesEx("com.persistent.app", d_IPC_CMD_ECR_SendResponse, bInBuf, usInLen, bOutBuf, &usOutLen);
				// patrick add code 20141205 start
				return d_OK;
			}
		}
    }
	
	if (strTCT.byPinPadMode == 1) //for External pinpad&CTLS&Signature
    {
	    inMultiAP_EXTGetMainroutine(); 
    }
	
    return SUCCESS;
}

int inCTOSS_JumpToSleepMode(void)
{  
	char szMessage[30+1];
	int inSleepTime;
	static int BatteryCharge = 0;
#if 1
	if (inCTOSS_CheckBatteryChargeStatus() == d_OK)
	{
		//vdDebug_LogPrintf("BatteryCharge=[%d]",BatteryCharge);
		if (BatteryCharge == 1)
		{
			vdDebug_LogPrintf("CTOS_LCDSetContrast to [%0xFF]");
			if ((strTCT.byTerminalType%2) != 0)
				CTOS_LCDSetContrast(0xFF);
			BatteryCharge = 0;
		}
	}
	else
	{
		//vdDebug_LogPrintf("BatteryCharge=[%d]",BatteryCharge);
		if (BatteryCharge == 0 || chGetIdleEvent() == 1)
		{
			vdDebug_LogPrintf("CTOS_LCDSetContrast to [%0x50]");
			if ((strTCT.byTerminalType%2) != 0)
    			CTOS_LCDSetContrast(0x50);
			BatteryCharge = 1;
		}
	}
#endif
	if (chGetIdleEvent() == 1)
	{	
		memset(szMessage,0x00,sizeof(szMessage));
		inCTOSS_GetEnvDB("SLEEPTIME", szMessage);
		if (strlen(szMessage) > 0)
			inSleepTime = atoi(szMessage);
		else
			inSleepTime = 90;
				
		vdDebug_LogPrintf("inCTOSS_JumpToSleepMode,inSleepTime=[%d]", inSleepTime);
		CTOS_TimeOutSet(TIMER_ID_3, inSleepTime*100);
		vdSetIdleEvent(0);
		CTOS_TimeOutSet(TIMER_ID_4, 2*100);//for auto d_BKLIT_LCD&d_BKLIT_KBD off
	}
	if(CTOS_TimeOutCheck(TIMER_ID_3) == d_YES)
	{
		vdSetIdleEvent(1);
		vdCTOS_uiIDLESleepMode();
	}
#if 0	
	if(CTOS_TimeOutCheck(TIMER_ID_4) == d_YES)//for auto d_BKLIT_LCD&d_BKLIT_KBD off
	{
		vdSetIdleEvent(1);
		vdDebug_LogPrintf("auto d_BKLIT_LCD&d_BKLIT_KBD off...");
		CTOS_BackLightSet (d_BKLIT_LCD, d_OFF);
		CTOS_BackLightSet (d_BKLIT_KBD, d_OFF);
	}
#endif	
    return SUCCESS;
}


int inCTOS_IdleRunningTestFun4(void)
{    
    return SUCCESS;
}

int inCTOS_IdleRunningTestFun5(void)
{
    return SUCCESS;
}

int inCTOS_IdleRunningTestFun6(void)
{
    return SUCCESS;
}

int inCTOS_IdleRunningTestFun7(void)
{
    return SUCCESS;
}

int inCTOS_IdleRunningTestFun8(void)
{
    return SUCCESS;
}

int inCTOS_AutoReversal(void)
{
	unsigned long ulTimeGap;
	BOOL isUiUpdated = 0;
	vdDebug_LogPrintf("inAutoReversalTime[%ld]", strTCT.inAutoReversalTime);
	if(strTCT.inAutoReversalTime == 0)
		return 0;
	vdDebug_LogPrintf("inCTOS_AutoReversal,[%ld],gulInitStartTime[%ld]", CTOS_TickGet(), gulInitStartTime);
	if(gulInitStartTime == 0)
        gulInitStartTime = CTOS_TickGet();
	ulTimeGap = CTOS_TickGet() - gulInitStartTime;	//(read_ticks() - gulInitStartTime);
    vdDebug_LogPrintf("inCTOS_AutoReversal,ulTimeGap[%ld]", ulTimeGap);
	if (ulTimeGap < (strTCT.inAutoReversalTime * 100) )//6000 = 60 seconds, ONE_MIN_BY_TICKS
		return(d_OK);

	{
		//select all
		int inNum, inNumOfHost, inResult, inSendLen, inReceLen;
		int inMerNum, inNumOfMit;
		inNumOfHost = inHDTNumRecord();
		CHAR szFileName[d_BUFF_SIZE];
		BYTE uszSendData[2048 + 1], uszReceData[2048 + 1];
//		TRANS_DATA_TABLE srTransRec;

//		memset(&srTransRec, 0, sizeof(TRANS_DATA_TABLE));

		vdDebug_LogPrintf("check reversal by host and merchant; inNumOfHost=[%d]-----",inNumOfHost);
		for(inNum =1 ;inNum <= inNumOfHost; inNum++)
		{
			if(inHDTRead(inNum) == d_OK)
			{
				if (memcmp(strHDT.szHostLabel, "EFTSEC", 6) == 0)
				{
					continue;
				}

				DebugAddINT("summary host Index",inNum);
				inMMTReadNumofRecords(inNum,&inNumOfMit);
				for(inMerNum =0 ;inMerNum < inNumOfMit; inMerNum++)
				{
//					vdCTOS_TxnsBeginInit();
					memset(&srTransRec, 0, sizeof(TRANS_DATA_TABLE));
					memcpy(&strMMT[0],&strMMT[inMerNum],sizeof(STRUCT_MMT));
					srTransRec.MITid = strMMT[0].MITid;
					srTransRec.HDTid = inNum;
					strHDT.inHostIndex = inNum;

					memset(szFileName,0,sizeof(szFileName));
					sprintf(szFileName, "%s%02d%02drev"
							, strHDT.szHostLabel
							, strHDT.inHostIndex
							, srTransRec.MITid);
					vdDebug_LogPrintf("check reversal file[%s]", szFileName);
					if((inResult = inMyFile_CheckFileExist(szFileName)) < 0)
					{
						vdDebug_LogPrintf("inMyFile_CheckFileExist <0");
						continue;
					}
					else
					{
                        inSendLen = inResult;
						vdDebug_LogPrintf("Got reversal file[%s],inSendLen=%d", szFileName, inSendLen);
						if((inResult = inMyFile_ReversalRead(&uszSendData[0],sizeof(uszSendData))) == ST_SUCCESS)
						{
							isUiUpdated = 1;
							byIsIdleReversal = 1;
//							ProcessingUI();
							//ProcessingUIEx();
							DebugAddHEX("Reversal orig", uszSendData, inSendLen);
							inCTOSS_ISOEngCheckEncrypt(srTransRec.HDTid, uszSendData, &inSendLen);
							DebugAddHEX("Reversal Encrypt", uszSendData, inSendLen);
							inCPTRead(srTransRec.HDTid);
							vdDebug_LogPrintf("szPriTxnHostIP[%s], inPriTxnHostPortNum=[%d]", strCPT.szPriTxnHostIP, strCPT.inPriTxnHostPortNum);
							//TODO: init com
							if (inCTOS_InitComm(strCPT.inCommunicationMode) != d_OK)
							{
								vdDebug_LogPrintf("COMM INIT ERR");
								continue;
							}
							inResult = inCTOS_CheckInitComm(strCPT.inCommunicationMode);
							if (inResult != d_OK)
							{
								vdDebug_LogPrintf("COMM INIT ERR");
								continue;
							}
							if (srCommFuncPoint.inCheckComm(&srTransRec) != d_OK)
							{
								inCTOS_inDisconnect();
								vdDebug_LogPrintf("COMM inCheckComm ERR");
								continue;
							}
							if (srCommFuncPoint.inConnect(&srTransRec) != ST_SUCCESS)
							{
								vdDebug_LogPrintf("inSnedReversalToHost inConnect Err");
								inCTOS_inDisconnect();
								continue;
							}
							if (strTCT.fPrintISOMessage == VS_TRUE){
								inPrintISOPacket(VS_TRUE , uszSendData, inSendLen);
							}

							if ((inReceLen = inSendAndReceiveFormComm(&srTransRec,
																	  (unsigned char *)uszSendData,
																	  inSendLen,
																	  (unsigned char *)uszReceData)) <= ST_SUCCESS)
							{
								vdDebug_LogPrintf("inSnedReversalToHost Send Err");
								srTransRec.shTransResult = TRANS_COMM_ERROR;
								continue;
							}
							vdSetISOEngTransDataAddress(&srTransRec);
							inResult = inCTOSS_UnPackIsodataEx1(srTransRec.HDTid,
																(unsigned char *)uszSendData,
																inSendLen,
																(unsigned char *)uszReceData,
																&inReceLen);

							if (strTCT.fPrintISOMessage == VS_TRUE){
								inPrintISOPacket(VS_FALSE , uszReceData, inReceLen);
							}
							if (inResult != ST_SUCCESS)
							{
								vdDebug_LogPrintf("inSnedReversalToHost inCTOSS_UnPackIsodata Err");
								continue;
							}
							else
							{
								if (memcmp(srTransRec.szRespCode, "00", 2))
								{
									vdDebug_LogPrintf(". inSnedReversalToHost Resp Err %02x%02x",srTransRec.szRespCode[0],srTransRec.szRespCode[1]);
									inCTOS_inDisconnect();

									continue;
								}
								else
								{
									inResult = CTOS_FileDelete(szFileName);
									if (inResult != d_OK)
									{
										vdDebug_LogPrintf(". inSnedReversalToHost %04x",inResult);
										inCTOS_inDisconnect();
										continue;
									}
									else
										vdDebug_LogPrintf("rev. file deelted succesfully after send rev to host");

								}
							}

						}
					}
				}
			}
		}



	}

    vdDebug_LogPrintf("isUiUpdated[%d] ", isUiUpdated);
	if(isUiUpdated)
	{
		//go back main menu
		vdDebug_LogPrintf("go back main menu ");
		vdSetErrorMessage("");

		vdCTOS_TransEndReset();
//		usCTOSS_RestartActivity(" ");
	}

	gulInitStartTime = CTOS_TickGet();
	byIsIdleReversal = 0;
	return SUCCESS;
}

extern int inEthernet_GetTerminalIP(char *szTerminalIP);

int inCTOS_ConfigureCommDevice(void)
{
    BOOL BolDetachLANChange=TRUE;
    BYTE strOut[30],strtemp[17];
	USHORT  usRtn;
	BOOL fPortable;
	BOOL fPCI;
	USHORT mkHWSupport;

	if(BolDetachLANChange==TRUE)
	{
		//inCPTRead(1);
		usRtn = CTOS_HWSettingGet(&fPortable,&fPCI,&mkHWSupport);
		vdDebug_LogPrintf("CTOS_HWSettingGet,usRtn=[%x],fPortable[%d],fPCI=[%d],mkHWSupport=[%04x]", usRtn,fPortable,fPCI,mkHWSupport);
		if (usRtn != d_OK)
		{
			return SUCCESS;
		}
		
		vdDebug_LogPrintf("mkHWSupport & d_MK_HW_GPRS=[%04x]", mkHWSupport & d_MK_HW_GPRS);
		if ((mkHWSupport & d_MK_HW_ETHERNET) != d_MK_HW_ETHERNET)
		{
			return SUCCESS;
		}
	
		srTransRec.usTerminalCommunicationMode = ETHERNET_MODE;
		//vduiClearBelow(3);
		if ((strTCT.byTerminalType%2) == 0)
		{
			clearLine(12);
			clearLine(13);
			CTOS_LCDTPrintXY (1,12, "Please Wait 	");
		}
		else
		{
			clearLine(7);
			clearLine(8);
			CTOS_LCDTPrintXY (1,7, "Please Wait 	");
		}

		if ((strTCT.byTerminalType%2) == 0)
			CTOS_LCDTPrintXY(1, 13, "Init LAN... 	");
		else
			CTOS_LCDTPrintXY(1, 8, "Init LAN... 	");

		if (inCTOS_InitComm(srTransRec.usTerminalCommunicationMode) != d_OK) 
		{
		   
			vdDisplayErrorMsg(1, 8, "COMM INIT ERR");
			return SUCCESS;
		}
		
		srCommFuncPoint.inInitComm(&srTransRec, srTransRec.usTerminalCommunicationMode);
		srCommFuncPoint.inGetCommConfig(&srTransRec);
		srCommFuncPoint.inSetCommConfig(&srTransRec);		
	
	}		   

	//if(strCPT.inCommunicationMode == ETHERNET_MODE)
	{
	   if(strTCP.fDHCPEnable != IPCONFIG_STATIC)
	   {
			inEthernet_GetTerminalIP(strtemp);

			// set static IP			
			strTCP.fDHCPEnable = IPCONFIG_STATIC;
			strcpy(srEthernetRec.strLocalIP, strtemp);
			srCommFuncPoint.inSetCommConfig(&srTransRec); 

			// set DHCP
			strTCP.fDHCPEnable = IPCONFIG_DHCP;
			srCommFuncPoint.inSetCommConfig(&srTransRec); 

		   //vduiClearBelow(3);
		   if ((strTCT.byTerminalType%2) == 0)
			{
			clearLine(12);
			clearLine(13);
		   setLCDPrint(12, DISPLAY_POSITION_LEFT, "DHCP TERMINAL IP");
		   setLCDPrint(13, DISPLAY_POSITION_LEFT, strtemp);
		   	}
		   else
		   	{
		   	clearLine(7);
			clearLine(8);
		   setLCDPrint(7, DISPLAY_POSITION_LEFT, "DHCP TERMINAL IP");
		   setLCDPrint(8, DISPLAY_POSITION_LEFT, strtemp);
		   	}
	   }
	}

	return SUCCESS;
}

int inCTOS_ConfigureCTLSReader(void)
{
	// patrick add code 20140903
	inTCTRead(1);
	if (strTCT.fFirstInit)
	{	
		if ((strTCT.byTerminalType%2) == 0)
		{
			if (strTCT.inThemesType == 0)
			{
				vdCTOSS_CombineMenuBMP("BG_SPRING.BMP");
				vdBackUpWhiteBMP("SPRING.BMP","WHITE.BMP");
				vdBackUpWhiteBMP("SPRINGTOUCH.BMP","menutouch.bmp");
				vdBackUpWhiteBMP("SPRINGITEM.BMP","menuitem.bmp");
			}
			if (strTCT.inThemesType == 1)
			{
				vdCTOSS_CombineMenuBMP("BG_SUMMER.BMP");
				vdBackUpWhiteBMP("SUMMER.BMP","WHITE.BMP");
				vdBackUpWhiteBMP("SUMMERTOUCH.BMP","menutouch.bmp");
				vdBackUpWhiteBMP("SUMMERITEM.BMP","menuitem.bmp");
			}
		}
		inCTOS_ConfigureCommDevice();
		
		if (NO_CTLS != inCTOSS_GetCtlsMode())
		{
			vdCTOS_InitWaveData();
		}
		
		strTCT.fFirstInit = 0;
		inTCTSave(1);

		if (NO_CTLS != inCTOSS_GetCtlsMode() && (strTCT.byPinPadMode == 1))
		{
			CTOS_Delay(1000);
			CTOS_LCDTPrintXY(1, 8, "CONFIGURE READER OK");
			CTOS_Beep();
    		CTOS_Delay(1500);
			CTOS_LCDTClearDisplay();
		}
	}

	return SUCCESS;
}

int inCTOS_DisplayIdleBMP(void)
{
	if(strTCT.byPinPadMode == 1)
	{
		return SUCCESS;
	}
	//if (!strTCT.fECR) // tct
    	//vdDisplayAnimateBmp(0, 32, "Insert1.bmp", "Insert2.bmp", "Insert3.bmp", NULL, NULL);
	vdDisplayAnimateBmp(0, 32, "Insert1.bmp", NULL, NULL, NULL, NULL);
//	vdDisplayAnimateBmp(0, 30, "Insert1.bmp", "Insert2.bmp", "Insert3.bmp", NULL, NULL);
//	vdDisplayAnimateBmp(0,0, "Print1.bmp", "Print2.bmp", "Print3.bmp", NULL, NULL);

    return SUCCESS;
}

int inCTOS_IdleDisplayDateAndTime(void)
{
    CTOS_RTC SetRTC;
    BYTE szCurrentTime[50];
	BYTE szCurrentDate[50];
	static unsigned long linux_time = 0;
	unsigned long linux_crttime;
	static int gIdleDisplayTime = 0;
	
	if(strTCT.byPinPadMode == 1)
	{
		return SUCCESS;
	}
	
	linux_crttime = CTOS_TickGet();


    memset(szCurrentTime, 0x00, sizeof(szCurrentTime));
	memset(szCurrentDate, 0x00, sizeof(szCurrentDate));
    CTOS_RTCGet(&SetRTC);
//	sprintf(szCurrentTime,"%d-%02d-%02d  %02d:%02d",SetRTC.bYear, SetRTC.bMonth, SetRTC.bDay, SetRTC.bHour,SetRTC.bMinute);
//	sprintf(szCurrentTime,"%02d-%02d %02d:%02d",SetRTC.bMonth, SetRTC.bDay, SetRTC.bHour,SetRTC.bMinute);
	sprintf(szCurrentDate,"%d-%02d-%02d",SetRTC.bYear, SetRTC.bMonth, SetRTC.bDay);
	sprintf(szCurrentTime,"%02d:%02d",SetRTC.bHour,SetRTC.bMinute);
//    CTOS_LCDTPrintXY (13,1, szCurrentTime);
    CTOS_LCDTSelectFontSize(d_FONT_16x16);

		if (linux_crttime > linux_time + 300)
		{
			linux_time = CTOS_TickGet();
			if (gIdleDisplayTime == 0)
			{
				gIdleDisplayTime = 1;			
				//CTOS_LCDTPrintXY (1, 1, "            ");
				CTOS_LCDTPrintAligned(1, "                ", d_LCD_ALIGNLEFT);
			}
			else
				gIdleDisplayTime = 0;
		}
		if (gIdleDisplayTime == 1) 
			//CTOS_LCDTPrintXY (1, 1, szCurrentTime);
			CTOS_LCDTPrintAligned(1, szCurrentTime, d_LCD_ALIGNLEFT);
		else
			//CTOS_LCDTPrintXY (1, 1, szCurrentDate);
			CTOS_LCDTPrintAligned(1, szCurrentDate, d_LCD_ALIGNLEFT);

    CTOS_LCDTSelectFontSize(d_FONT_16x30);

    return SUCCESS;
}

int inCTOS_DisplayComTypeICO(void)
{    
#define SMALL_ICON_LEN	24
#define SMALL_ICON_GAP	2
#define FIRST_SMALL_ICON	1
#define SMALL_ICON_LEN2		5


    USHORT usRet;
    USHORT usRet1;
    DWORD pdwStatus ;
    BYTE  bPercentage ;
    static int inLoop = 0;
    static int inGPRSSignal = 0;
	static USHORT usNetworkType = 0;
	static USHORT usEthType = 1;
	static BYTE szNetworkName[128+1];
	static BYTE szWIFISSID[50];
	static unsigned long linux_time = 0;
	unsigned long linux_crttime;
	static int gIdleDisplayIcon = 0;
	char szMessage[30+1];
	int inCommBack;
	int inPriCommMode;
	int inSecCommMode;
	STRUCT_SHARLS_COM Sharls_COMData;
	int inResult = -1;
    static int inWifiSignal = 0;

	memset(&Sharls_COMData, 0x00, sizeof(STRUCT_SHARLS_COM));
	
	linux_crttime = CTOS_TickGet();
	if(strTCT.byPinPadMode == 1)
	{
		return SUCCESS;
	}

    if(strTCT.fDemo)
    {
        displaybmpEx(300, 90, "DEMO.bmp");
    }

#ifdef UNUSE
	//display bluetooth
	{
		displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*5, 0, "BTNone.bmp");
	}
	//display vifi
	{
		displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*4, 0, "VFNone.bmp");
	}
	//display GPS
	{
		displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*5-10, 16, "GPSNone.bmp");
	}
#endif
	
	//display ECR
	if(strTCT.fECR)
    {
        displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*3-10, 16, "ECR.bmp");
    }
	else
	{
        displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*3-10, 16, "None.bmp");
    }

	//display ERM
	if(strTCT.byERMMode != 0)
    {
        displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*4-10, 16, "ERM.bmp");
    }
	else
	{
        displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*4-10, 16, "None.bmp");
    }
	//for External pinpad&CTLS&Signature
	if(strTCT.byPinPadType == 3 || strTCT.byPinPadMode == 1)
	{
		displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*5-10, 16, "EXT.bmp");
	}
	else
	{
		displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*5-10, 16, "None.bmp");
	}
	//display Transaction COMMS mode
	{
		memset(szMessage,0x00,sizeof(szMessage));
		inCTOSS_GetEnv("PRICOMMMODE", szMessage);
		if (strlen(szMessage) > 0)
			inPriCommMode = atoi(szMessage);
		else
			inPriCommMode = 1;
		
		memset(szMessage,0x00,sizeof(szMessage));
		inCTOSS_GetEnv("SECCOMMMODE", szMessage);
		if (strlen(szMessage) > 0)
			inSecCommMode = atoi(szMessage);
		else
			inSecCommMode = 0;
		
		memset(szMessage,0x00,sizeof(szMessage));
		inCTOSS_GetEnv("COMMBACK", szMessage);
		if (strlen(szMessage) > 0)
			inCommBack = atoi(szMessage);
		else
			inCommBack = 0;
		if (inSecCommMode == inPriCommMode)
			inSecCommMode = NULL_MODE;
		displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*2-10, 16, "No2.bmp");
		switch(inSecCommMode)
		{
			case DIAL_UP_MODE:
				displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*2-5, 16, "Modem.bmp");
				break;
			case ETHERNET_MODE:
				displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*2-5, 16, "LAN.bmp");
				break;
			case GPRS_MODE:
				displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*2-5, 16, "G.bmp");
				break;
			case WIFI_MODE:
				displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*2-5, 16, "VF4.bmp");
				break;
			case COM1_MODE:
			case COM2_MODE:
			case USB_MODE:
			case NULL_MODE:
			default:
				displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*2-5, 16, "None.bmp");
        		break;
		}
		displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)-SMALL_ICON_GAP-5, 16, "12.bmp");
		displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)-5, 16, "No1.bmp");
		if (inCommBack == 0)
		{
			switch(inPriCommMode)
			{
				case DIAL_UP_MODE:
					displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP), 16, "Modem.bmp");
					break;
				case ETHERNET_MODE:
					displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP), 16, "LAN.bmp");
					break;
				case GPRS_MODE:
					displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP), 16, "G.bmp");
					break;
				case WIFI_MODE:
					displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP), 16, "VF4.bmp");
					break;
				case COM1_MODE:
				case COM2_MODE:
				case USB_MODE:
				case NULL_MODE:
				default:
					displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP), 16, "None.bmp");
            		break;
			}
		}
		else
		{
			switch(inPriCommMode)
			{
				case DIAL_UP_MODE:
					displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP), 16, "ModemFail.bmp");
					break;
				case ETHERNET_MODE:
					displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP), 16, "LANFail.bmp");
					break;
				case GPRS_MODE:
					displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP), 16, "GFail.bmp");
					break;
				case WIFI_MODE:
					displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP), 16, "VFFail.bmp");
					break;
				case COM1_MODE:
				case COM2_MODE:
				case USB_MODE:
				case NULL_MODE:
				default:
					displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP), 16, "None.bmp");
            		break;
			}
		}
	}

    pdwStatus = 0x00;
    usRet = CTOS_BatteryStatus(&pdwStatus);
    //vdDebug_LogPrintf("**CTOS_BatteryStatus [%X] [%02X]**", usRet, pdwStatus);
    usRet1 = CTOS_BatteryGetCapacity(&bPercentage);
    //vdDebug_LogPrintf("**CTOS_BatteryGetCapacity [%X] [%d]**", usRet1, bPercentage);

    if(d_BATTERY_NOT_EXIST == usRet1 || d_BATTERY_NOT_SUPPORT == usRet1)
    {
		displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP), 0, "Plugged.bmp");
    }
    else if(d_OK == usRet1)
    {
    	#if 0
        if (bPercentage <= 10)
        {
            if(d_OK == usRet && (pdwStatus & d_MK_BATTERY_CHARGE))
            {
            }
            else
            {        
				/CTOS_SystemReset();
            }
        }
		else if (bPercentage <= 20)
		#endif
		if (bPercentage <= 1)
		{
            if(d_OK == usRet && (pdwStatus & d_MK_BATTERY_CHARGE))
            {
            }
            else
            {        
				vdDisplayAnimateBmp(245, 150, "Low_battery.bmp", NULL, NULL, NULL, NULL);
				CTOS_Beep();
            }
		}

        if(bPercentage <= 25)
        {
            if(d_OK == usRet && (pdwStatus & d_MK_BATTERY_CHARGE))
            {
				displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP), 0, "25N.bmp");
            }
            else
            {
				displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP), 0, "25.bmp");
            }
        }
        else if(bPercentage <= 50)
        {
            if(d_OK == usRet && (pdwStatus & d_MK_BATTERY_CHARGE))
            {
				displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP), 0, "50N.bmp");
            }
            else
            {
				displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP), 0, "50.bmp");
            }
        }
        else if(bPercentage <= 75)
        {
            if(d_OK == usRet && (pdwStatus & d_MK_BATTERY_CHARGE))
            {
				displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP), 0, "75N.bmp");
            }
            else
            {
				displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP), 0, "75.bmp");
            }
            
        }
        else if(bPercentage <= 100)
        {
			displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP), 0, "100.bmp");
        }
    }

	if (linux_crttime > linux_time + 500 || inLoop == 0)
	{
		inLoop = 1;
		linux_time = CTOS_TickGet();
//		inGPRSSignal = inCTOSS_GetGPRSSignalEx(&usNetworkType, szNetworkName,&usEthType);
		inGPRSSignal = inCTOSS_GetGPRSSignalEx1(&usNetworkType, szNetworkName, &usEthType, &Sharls_COMData);
		inWifiSignal = Sharls_COMData.inReserved2; 

		vdDebug_LogPrintf("**inCTOSS_GetGPRSSignal inGPRSSignal=[%d]**usEthType=[%d]", inGPRSSignal,usEthType);
		vdDebug_LogPrintf("**usNetworkType [%2x][%2x][%d]**", d_MOBILE_NETWORK_UMTS , usNetworkType, (d_MOBILE_NETWORK_UMTS & usNetworkType));
		CTOS_LCDTSelectFontSize(d_FONT_16x16);
		//CTOS_LCDTPrintXY (1, 2, "                    ");
		CTOS_LCDTPrintAligned(2, "                    ", d_LCD_ALIGNLEFT);
		CTOS_LCDTSelectFontSize(d_FONT_16x30);

		memset(szWIFISSID,0x00,sizeof(szWIFISSID));
		if (Sharls_COMData.usWIFIType == 1)
			strcpy(szWIFISSID,Sharls_COMData.szWIFISSID);

		if (gIdleDisplayIcon == 0)
		{
			gIdleDisplayIcon = 1;			
		}
		else
			gIdleDisplayIcon = 0;
		 
	}

	
	//display modem icon
	if ((usHWSupport & d_MK_HW_MODEM) == d_MK_HW_MODEM)
	{
		displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*2, 0, "Modem.bmp");
	}
	else
		displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*2, 0, "ModemNone.bmp");

	////display wifi icon
	if ((usHWSupport & d_MK_HW_WIFI) == d_MK_HW_WIFI)
	{
		if(0 == inWifiSignal)
			displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*4, 0, "VFNone.bmp");
		else if(1 == inWifiSignal)
			displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*4, 0, "VF1.bmp");
		else if(2 == inWifiSignal)
			displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*4, 0, "VF2.bmp");
		else if(3 == inWifiSignal)
			displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*4, 0, "VF3.bmp");
		else if(4 == inWifiSignal)
			displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*4, 0, "VF4.bmp");
		else
		{
		displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*4, 0, "VF4.bmp");
	}
	}
	else
	{
		if (0 == usHWSupport)
			displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*4, 0, "VFInit.bmp");
		else
			displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*4, 0, "VFNone.bmp");
	}
//	vdDebug_LogPrintf("d_MK_HW_WIFI[%d], signal[%d]", usHWSupport & d_MK_HW_WIFI, Sharls_COMData.inReserved2);

	//vdDebug_LogPrintf("**gIdleDisplayIcon[%d]**",gIdleDisplayIcon);
	//display ETHERNET icon
	if ((usHWSupport & d_MK_HW_ETHERNET) == d_MK_HW_ETHERNET)
	{
		if(usEthType == 1)
		{	
			displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*3, 0, "LAN.bmp");
		}
		else
		{
			displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*3, 0, "None.bmp");
		}
	}
	else
		displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*3, 0, "LANNone.bmp");

	// display wifi icon
	if ((usHWSupport & d_MK_HW_WIFI) == d_MK_HW_WIFI)
	{
		if (strlen(szWIFISSID) > 0)
		{
			if (gIdleDisplayIcon == 1)
			{
				CTOS_LCDTSelectFontSize(d_FONT_16x16);
				//CTOS_LCDTPrintXY (1, 2, szNetworkName);
				CTOS_LCDTPrintAligned(2, szWIFISSID, d_LCD_ALIGNLEFT);
				CTOS_LCDTSelectFontSize(d_FONT_16x30);
			}
		}
		else
			gIdleDisplayIcon = 0;
	}
	else
		gIdleDisplayIcon = 0;

	//display GPRS icon
	if ((usHWSupport & d_MK_HW_GPRS) == d_MK_HW_GPRS)
	{
	if(inGPRSSignal > 0)
	{
		if (gIdleDisplayIcon == 0)
		{
			CTOS_LCDTSelectFontSize(d_FONT_16x16);
			//CTOS_LCDTPrintXY (1, 2, szNetworkName);
			CTOS_LCDTPrintAligned(2, szNetworkName, d_LCD_ALIGNLEFT);
			CTOS_LCDTSelectFontSize(d_FONT_16x30);
		}

		if (fGPRSConnectOK == TRUE)
		{
#if 0			
		if (d_MOBILE_NETWORK_UMTS & usNetworkType)
			displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*6, 0, "3G.bmp");
		else if (d_MOBILE_NETWORK_GPRS & usNetworkType)
			displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*6, 0, "G.bmp");
		else if (d_MOBILE_NETWORK_GPRS & usNetworkType)
			displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*6, 0, "G.bmp");
		else
			displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*6, 0, "GNone.bmp");
#endif
		if ((d_GSM_NETWORK_WCDMA == usNetworkType) || (d_GSM_NETWORK_HSDPA == usNetworkType))
			displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*6, 0, "3G.bmp");
		else if ((d_MOBILE_NETWORK_GPRS == usNetworkType) || (d_GSM_NETWORK_EGPRS == usNetworkType))
			displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*6, 0, "G.bmp");
		else
			displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*6, 0, "GNone.bmp");
		}
		else
			displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*6, 0, "GNone.bmp");
		
        if(1 == inGPRSSignal)
			displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*7, 0, "G1.bmp");
        else if(2 == inGPRSSignal)
			displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*7, 0, "G2.bmp");
        else if(3 == inGPRSSignal)
			displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*7, 0, "G3.bmp");
        else if(4 == inGPRSSignal)
			displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*7, 0, "G4.bmp");
        else
    	{
			displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*7, 0, "None.bmp");
    	}

	}
	else
	{
		gIdleDisplayIcon = 1;
		displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*7, 0, "None.bmp");
		displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*6, 0, "None.bmp");
	//	CTOS_LCDTSelectFontSize(d_FONT_16x16);
	//	CTOS_LCDTPrintXY (1, 2, "No Service");
	//	CTOS_LCDTSelectFontSize(d_FONT_16x30);
	}
	}
	else
	{
		gIdleDisplayIcon = 1;
		if (0 == usHWSupport)
			displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*6, 0, "GInit.bmp");
		else
			displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*6, 0, "GNone.bmp");
	}

	inResult = inCTOSS_GetRemovableStorageStatus();
	if (inResult == 1)
		displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP), 32, "SDMount.bmp");
	else if (inResult == 2)
		displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*2, 32, "PenDriveMount.bmp");
	else if (inResult == 3)
	{
		displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP), 32, "SDMount.bmp");
		displaybmpEx(319-(SMALL_ICON_LEN+SMALL_ICON_GAP)*2, 32, "PenDriveMount.bmp");
	}

    return SUCCESS;
}

typedef struct 
{
	int  (*inCTOS_IdleRunningFun)(void);
} IDLE_FUNC;

IDLE_FUNC g_IdleRunningFun[] = 
{
//	inCTOS_ConfigureCTLSReader,
//    inCTOS_DisplayIdleBMP,
//    inCTOS_DisplayComTypeICO,
//    inCTOS_IdleDisplayDateAndTime,
    inCTOS_ECRTask,
	inCTOS_AutoReversal,
//    inCTOSS_JumpToSleepMode,
//    vdCTOSS_RecoverRAM,
    inCTOS_IdleRunningTestFun6,
    inCTOS_IdleRunningTestFun7,
    inCTOS_IdleRunningTestFun8,
    NULL        
};

int inCTOSS_InitAP(void)
{
	USHORT ret;
	char szDatabaseName[100+1];
	strcpy(szDatabaseName, "DMENGTHAI.S3DB");
	
	ret = CTOS_SetFunKeyPassword(strTCT.szFunKeyPW, 1);
	vdDebug_LogPrintf("CTOS_SetFunKeyPassword=[%s],ret=[%d]", strTCT.szFunKeyPW,ret);
	
	ret = CTOS_SetPMEnterPassword(strTCT.szPMpassword, 1);	
	vdDebug_LogPrintf("CTOS_SetPMEnterPassword=[%s],ret=[%d]", strTCT.szPMpassword,ret);

		/**********************
			CTOS_PrinterSetHeatLevel 
			default value is 2. 
			= 0 : Ultra light. 	= 1 : Very light. 
			= 2 : Light. 		= 3 : Medium. 
			= 4 : Dark. 		= 5 : Very dark. 
			= 6 : Ultra dark.
		***********************/
	ret = CTOS_PrinterSetHeatLevel(strTCT.inPrinterHeatLevel);	
	vdDebug_LogPrintf("CTOS_PrinterSetHeatLevel=[%d],ret=[%d]", strTCT.inPrinterHeatLevel,ret);

	if (strTCT.inThemesType == 1)
	{
		CTOS_LCDTTFSelect("tahoma.ttf", 0);
                CTOS_LCDTTFSwichDisplayMode(d_TTF_MODE_VIETNAM);

                //CTOS_PrinterTTFSelect("tahoma.ttf", 0);
                //inCTOSS_SetERMFontType("tahoma.ttf", 0);
				CTOS_PrinterTTFSelect("cousinebd.ttf", 0);
				inCTOSS_SetERMFontType("cousinebd.ttf", 0);
                CTOS_PrinterTTFSwichDisplayMode(d_TTF_MODE_VIETNAM);

		inSetLanguageDatabase(szDatabaseName);

		inCTOSS_SetALLApFont("tahoma.ttf");
	}
        else
        {
                // patrick test 20150115 start
                CTOS_LCDTTFSelect(d_FONT_DEFAULT_TTF, 0);
                CTOS_LCDTTFSwichDisplayMode(d_TTF_MODE_DEFAULT);					 

                CTOS_PrinterTTFSelect(d_FONT_DEFAULT_TTF, 0);
                inCTOSS_SetERMFontType(d_FONT_DEFAULT_TTF, 0);
                CTOS_PrinterTTFSwichDisplayMode(d_TTF_MODE_DEFAULT);

                inSetLanguageDatabase(d_FONT_DEFAULT_TTF);
				inCTOSS_SetALLApFont(d_FONT_DEFAULT_TTF);
        }

	return SUCCESS;
}
int inCTOS_InitECR(void)
{
	vdDebug_LogPrintf("**inCTOS_InitECR**");
	if (strTCT.fECR) // tct
	{
		usCTOSS_ECRInitialize();
	}
	vdDebug_LogPrintf("**inCTOS_InitECR exit**");
	return SUCCESS;
}

int inCTOS_IdleEventProcess(void)
{
#define CTOS_ILDE_TIMER  60

    DWORD dwWait=0, dwWakeup=0;
    USHORT ret;
    USHORT byMSR_status;
    BYTE key;
    BYTE Tkey;
    BYTE bySC_status;
    BYTE babuff[128];
    BYTE sT1Buf[128] ,sT2Buf[128] ,sT3Buf[128];
    USHORT bT1Len=128 ,bT2Len=128 ,bT3Len=128;
    int i = 0;
    int i1=0, i2=0;
    BYTE baTemp[128];
	char szErrorMessage[30];

    CTOS_SCStatus(d_SC_USER, &bySC_status);
    if(bySC_status & d_MK_SC_PRESENT)
    {
        //inSetTextMode();
        vdRemoveCard();
    }
    
    CTOS_TimeOutSet(TIMER_ID_4, CTOS_ILDE_TIMER);  //Only IDLE loop use TIMER_ID_4, please don't use this TIMER_ID_4 in other place in application
    vduiKeyboardBackLight(VS_FALSE);
	vdSetIdleEvent(1);

    vdDebug_LogPrintf("**strTCT.fECR[%d]**", strTCT.fECR);
	if (strTCT.fECR) // tct
	{
		usCTOSS_ECRInitialize();	
	}
	
	inCTOSS_InitAP();

	memset(szErrorMessage,0x00,sizeof(szErrorMessage));
	sprintf(szErrorMessage,"%d",strCPT.inCommunicationMode);
	inCTOSS_PutEnv("PRICOMMMODE", szErrorMessage);
	inCTOSS_PutEnv("COMMBACK", "0");
	memset(szErrorMessage,0x00,sizeof(szErrorMessage));
	sprintf(szErrorMessage,"%d",strCPT.inSecCommunicationMode);
	inCTOSS_PutEnv("SECCOMMMODE", szErrorMessage);

	if (strTCT.byTerminalType == 4)
	{			
		BYTE Tkey = 0;	
		Tkey = (sizeof(stKeyPara) / sizeof(CTOS_VIRTUAL_FUNCTION_KEY_SET_PARA))%0x100;
		
		ret = CTOS_VirtualFunctionKeySet(stKeyPara, Tkey);			
	}

	if(inCTOSS_CheckMemoryStatus() != d_OK)
		{
			CTOS_SystemReset();
		}
	
	while(1)
    {
		//Open the Back Light in the LCD Display //
		//vduiLightOn();
		//return idle delay problem, move to head
		if(CTOS_TimeOutCheck(TIMER_ID_4) == d_YES)
        {
            i = 0;
            while(g_IdleRunningFun[i].inCTOS_IdleRunningFun != NULL)
                g_IdleRunningFun[i++].inCTOS_IdleRunningFun();
        }

        dwWait = d_EVENT_KBD | d_EVENT_MSR | d_EVENT_SC;
        //System waits the device the be set acts.   //System waits for target devices to be triggered and will not return until any target device is triggered //
		
		// patrick ECR clear
		//if (strTCT.fECR) // tct
	    //    dwWait = d_EVENT_MSR | d_EVENT_SC;
		if(strTCT.byPinPadMode == 1)
		{
			USHORT usTk1Len, usTk2Len, usTk3Len;
			BYTE szTk1Buf[TRACK_I_BYTES], szTk2Buf[TRACK_II_BYTES], szTk3Buf[TRACK_III_BYTES];
			usTk1Len = TRACK_I_BYTES ;
			usTk2Len = TRACK_II_BYTES ;
			usTk3Len = TRACK_III_BYTES ;
			
			dwWait = 0; // Disable the keybaord event. because of PinPad.
			CTOS_MSRRead(szTk1Buf, &usTk1Len, szTk2Buf, &usTk2Len, szTk3Buf, &usTk3Len);

			CTOS_KBDBufFlush();
		}

		if (strTCT.byTerminalType == 4)
		{
			do{
				ret = CTOS_VirtualFunctionKeyHit(&key);
				if (key == d_KBD_INVALID)
				{
					CTOS_KBDHit(&key);
					break;
				}

				 CTOS_KBDBufPut(key);				
			}while(key != d_KBD_INVALID);

			if (key != d_KBD_INVALID) // patrick, please do not remove this part of code
			{
				CTOS_KBDBufPut(key);
			}			
		}

        ret = CTOS_SystemWait(20, dwWait, &dwWakeup);

        vdSetFirstIdleKey(0x00);
        CTOS_SCStatus(d_SC_USER, &bySC_status);
        if ((dwWakeup & d_EVENT_KBD) == d_EVENT_KBD)
        {
            vduiKeyboardBackLight(VS_TRUE);
            //Detect if any key is pressed and get which key, but not get actual key.
            //If you get actual key, use to "CTOS_KBDGet" function//
			
            CTOS_KBDGet(&key);
			vduiLightOn();

            switch(key)
            {
                case d_KBD_00:     
					vdCTOS_uiIDLEPowerOff();
                    break;
				
                case d_KBD_F1:     
					ret = inCTOS_PromptPassword();
					if (d_OK != ret)
					{
						//CTOS_LCDTClearDisplay();
						break;						
					}
	
                    inF1KeyEvent();
                    break;
                case d_KBD_F2:                     

					inF2KeyEvent();
                    break;
                case d_KBD_F3:                     
					inF3KeyEvent();
                    break;

                case d_KBD_F4:                    
					inF4KeyEvent();
                    break;

                case d_KBD_ENTER:                    
					inEnterKeyEvent();
                    //CTOS_LCDTClearDisplay();
                    break;

                case d_KBD_CANCEL:
                    break;

                case d_KBD_0:
					ret = inCTOS_PromptPassword();
					if (d_OK != ret)
					{
						//CTOS_LCDTClearDisplay();
						break;						
					}
                    inF1KeyEvent();
                    //CTOS_LCDTClearDisplay();
                    break;
                case d_KBD_1:
					vdCTOSS_EFT_TestHardcodeKey();
					break;
                case d_KBD_2:
                case d_KBD_3:
                case d_KBD_4:
                case d_KBD_5:
                case d_KBD_6:
                case d_KBD_7:
                case d_KBD_8:
                case d_KBD_9:
                    vdSetFirstIdleKey(key);
                    inCTOS_SALE();
                    vdSetFirstIdleKey(0x00);
                    //CTOS_LCDTClearDisplay();
                    break;
                    
                default:
	                break;
            }

			clearLine(1);
			clearLine(2);
            vduiKeyboardBackLight(VS_FALSE);
            vdSetIdleEvent(1);
        }
        else if ((dwWakeup & d_EVENT_MSR) == d_EVENT_MSR)
        {
        	if(strTCT.byPinPadMode == 1)
        	{
				continue;
        	}
			
            //Open the Back Light in the LCD Display //
            vduiKeyboardBackLight(VS_TRUE);
            vduiLightOn();
            vdCTOS_SetTransEntryMode(CARD_ENTRY_MSR);
			vdSetIdleEventSC_MSR(1);
            
            //inCTOS_WAVE_SALE();
            inCTOS_SALE();
            //CTOS_LCDTClearDisplay();
			clearLine(1);
			clearLine(2);
	        vduiKeyboardBackLight(VS_FALSE);
			vdSetIdleEventSC_MSR(0);
			vdSetIdleEvent(1);
            
            continue;
            
        }
        else if (((dwWakeup & d_EVENT_SC) == d_EVENT_SC) || (bySC_status & d_MK_SC_PRESENT))
        {
        	if(strTCT.byPinPadMode == 1)
				continue;
			
            //Open the Back Light in the LCD Display //
            vduiKeyboardBackLight(VS_TRUE);
            vduiLightOn();
            vdCTOS_SetTransEntryMode(CARD_ENTRY_ICC);
			vdSetIdleEventSC_MSR(1);
            
            //inCTOS_WAVE_SALE();
            inCTOS_SALE();
            //CTOS_LCDTClearDisplay();
			clearLine(1);
			clearLine(2);
            vduiKeyboardBackLight(VS_FALSE);
			vdSetIdleEventSC_MSR(0);
			vdSetIdleEvent(1);
            
            continue;
        }
	#if 0
        else if(CTOS_TimeOutCheck(TIMER_ID_4) == d_YES)
        {
            i = 0;
            while(g_IdleRunningFun[i].inCTOS_IdleRunningFun != NULL)
                g_IdleRunningFun[i++].inCTOS_IdleRunningFun();
            
            continue;
        }
     #endif   
    }
}


int inGetHawker2Mode(void)
{		
	return g_inHK2Mode;
}

void vdSetHawker2Mode(int inHKMode)
{
	g_inHK2Mode = inHKMode;
}



int main(int argc,char *argv[])
{
	//inSetTextMode();

	//inCTOSS_ProcessCfgExpress();
    
    inTCTRead(1);

    inCPTRead(1);

    inCSTRead(1);

    inTCPRead(1);

	inTCTEXRead(1);
	inTXNRead(1);
	inTMSEXRead(1);
	
	vdDebug_LogPrintf("end  inTCTEXRead");

		
	while (1)
	{
		inMultiAP_GetMainroutine();	
		return 0;
	}

	/*check if need TMS update*/
    inCTOSS_TMSRestoreTxnData();

    vdThreadRunAppColdInit();

	g_inHK2Mode = get_env_int("HKMODE");
	vdSetHawker2Mode(g_inHK2Mode);

	
	if (get_env_int("AUTORECOVER") != 8)
	{
	inMultiAP_ForkTask();
	if(d_OK == inMultiAP_CheckSubAPStatus())
	{
		//for CUP testing
		vdCTOSS_EFT_TestHardcodeKey();
		
		vdDebug_LogPrintf("inWaitTime =[%d]",strTCT.inWaitTime);
		if (strTCT.inWaitTime <= 0)
			strTCT.inWaitTime = 100;
		
		while (1)
		{
			inMultiAP_GetMainroutine();			
			//CTOS_Delay(strTCT.inWaitTime);
		}
	}
	else
	{
		inMultiAP_ForkSharlsAp();
	}
	}
	else
	{
		inMultiAP_ForkTaskEx();
	}
	/*check if it is external pinpad V3P*/
	if ((strTCT.byPinPadType == 3 && strTCT.byPinPadPort == 9)
		||(strTCT.byPinPadType == 1 && strTCT.byPinPadPort == 9))
	{
		if (strTCT.byRS232DebugPort == 8)
		{
			strTCT.byRS232DebugPort = 0;
			inTCTSave(1);

			CTOS_LCDTClearDisplay();
		    vduiDisplayStringCenter(3,"USB HOST MODE");
		    vduiDisplayStringCenter(4,"USB DEBUG ENABLE");
		    vduiDisplayStringCenter(5,"TERMINAL REBOOT");

			CTOS_Delay(2000);
			CTOS_SystemReset();
		}
		CTOS_USBSelectMode(d_USB_HOST_MODE);
	}
	
    //CTOS_LCDTClearDisplay();
    if (get_env_int("AUTORECOVER") != 8)
    {
	inCTOS_DisplayIdleBMP();
	CTOS_Delay(1000);

	inCTLOS_Getpowrfail();
    }
	put_env_int("AUTORECOVER", 0);
	
    //inCTOSS_CheckIfPendingTMSDownload();
//for CUP testing
	vdCTOSS_EFT_TestHardcodeKey();


    	
    inCTOS_IdleEventProcess();

}

int inIdleRunningFunctions(void)
{
	int	i = 0;
	i = 0;

	vdDebug_LogPrintf("inIdleRunningFunctions");

//	inCTOS_ECRTask();
	while(g_IdleRunningFun[i].inCTOS_IdleRunningFun != NULL)
		g_IdleRunningFun[i++].inCTOS_IdleRunningFun();
	
	return d_OK;
}

