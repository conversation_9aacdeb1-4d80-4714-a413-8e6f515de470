#include <string.h>
#include <stdio.h>
#include <stdlib.h>
#include <stdarg.h>
#include <ctosapi.h>
#include <vwdleapi.h>
#include <sqlite3.h>

#include "..\Includes\POSTypedef.h"
#include "..\Debug\Debug.h"

#include "..\Includes\POSMain.h"
#include "..\Includes\POSTrans.h"
#include "..\Includes\POSHost.h"
#include "..\Includes\POSSale.h"
#include "..\Includes\POSbatch.h"
#include "..\Includes\POSRefund.h"
#include "..\ui\Display.h"
#include "..\Includes\V5IsoFunc.h"
#include "..\Accum\Accum.h"
#include "..\print\Print.h"
#include "..\Comm\V5Comm.h"
#include "..\Includes\MultiApLib.h"
#include "..\Aptrans\MultiAptrans.h"
#include "..\Includes\Wub_lib.h"
#include "..\Database\DatabaseFunc.h"
#include "..\ApTrans\MultiShareEMV.h"
#include "..\Includes\CardUtil.h"
#include "..\Includes\POSSetting.h"
#include "..\PCI100\COMMS.h"
#include "..\erm\Poserm.h"

#include "POSCtls.h"
#include "POSWave.h"
#include "../PinPad/pinpad.h"
#include "../UIapi.h"
#include "../Includes/POSHost.h"

int inWaveTransType = 0;
int ginDoingHouseKeeping =0;

void vdSetHouseKeeping(int type)
{
	ginDoingHouseKeeping = type;
}

void vdCTOSS_SetWaveTransType(int type)
{
	inWaveTransType = type;
}

int inCTOSS_GetWaveTransType(void)
{
	return inWaveTransType;
}

int inCTOS_MultiAPReloadWaveData(void)
{
	if (srTransRec.usChipDataLen > 0)
	{			
		vdCTOSS_WaveGetEMVData(srTransRec.baChipData, srTransRec.usChipDataLen);
	}
		
	if (srTransRec.usAdditionalDataLen > 0)
	{			
		vdCTOSS_WaveGetEMVData(srTransRec.baAdditionalData, srTransRec.usAdditionalDataLen);
	}
	
	return (d_OK);
}

int inCTOS_WaveFlowProcess(void)
{
    int inRet = d_NO;

    USHORT ushEMVtagLen;
    BYTE   EMVtagVal[64];
    BYTE   szStr[64];
	int inEVENT_ID = 0;

	memset(&stRCDataAnalyze,0x00,sizeof(EMVCL_RC_DATA_ANALYZE));

	vdDiners_EmptyBatch();
    
    inRet = inCTOSS_CheckMemoryStatus();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_GetTxnPassword();
    if(d_OK != inRet)
        return inRet;

	inRet = inCTOS_GetTxnBaseAmount();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_GetTxnTipAmount();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_UpdateTxnTotalAmount();
    if(d_OK != inRet)
        return inRet;

	//inRet = inCTOS_GetDistance();	
    
    inRet = inCTOS_WaveGetCardFields();
    if(d_OK != inRet)
        return inRet;
	
    inRet = inCTOS_SelectHost();
    if(d_OK != inRet)
        return inRet;

    if (inMultiAP_CheckMainAPStatus() == d_OK)
    {
    	if (srTransRec.byTransType == REFUND)
			inEVENT_ID = d_IPC_CMD_WAVE_REFUND;
		else
			inEVENT_ID = d_IPC_CMD_WAVE_SALE;
		
        inRet = inCTOS_MultiAPSaveData(inEVENT_ID);
        if(d_OK != inRet)
            return inRet;
    }
    else
    {
        if (0 == fGetAppRunBySelf() && inMultiAP_CheckSubAPStatus() == d_OK)
        {
			
			inRet = inCTOS_MultiAPGetData();
            if(d_OK != inRet)
                return inRet;
            
            inRet = inCTOS_MultiAPReloadTable();
            if(d_OK != inRet)
                return inRet;

			inRet = inCTOS_MultiAPReloadWaveData();
            if(d_OK != inRet)
                return inRet;
        }
		
		vdDebug_LogPrintf("strHDT.szAPName[%s]",strHDT.szAPName);
		
		if (strcmp (strHDT.szAPName,"V5S_Amex") == 0)
		{

			vdDebug_LogPrintf("strHDT.szAPName[%s]",strHDT.szAPName);

			if (srTransRec.byTransType == REFUND)
				inEVENT_ID = d_IPC_CMD_WAVE_REFUND;
			else
				inEVENT_ID = d_IPC_CMD_WAVE_SALE;
					
			inRet = inCTOS_MultiAPSaveData(inEVENT_ID);
			if(d_OK != inRet)
				return inRet;
		}
		
        inRet = inCTOS_MultiAPCheckAllowd();
        if(d_OK != inRet)
            return inRet;
    }

	vdDebug_LogPrintf("inCTOS_CustComputeAndDispTotal");

	inRet = inCTOS_CustComputeAndDispTotal();
    if(d_OK != inRet)
        return inRet;
	
    inRet = inCTOS_CheckAndSelectMutipleMID();
    if(d_OK != inRet)
        return inRet;

/*    inRet = inCTOS_PreConnect();
    if(d_OK != inRet)
        return inRet;*/

    inRet = inCTOS_CheckIssuerEnable();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_CheckTranAllowd();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_CheckMustSettle();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_GetInvoice();
    if(d_OK != inRet)
        return inRet;

	if (srTransRec.HDTid == AMEX_HOST_INDEX)
	{
	    inRet = inCTOS_Get4DBC();
	    if(d_OK != inRet)
	        return inRet;
	}
	else
	{		
	    inRet = inCTOS_GetCVV2();
	    if(d_OK != inRet)
	        return inRet;
	}

    //inRet = inCTOS_CustComputeAndDispTotal();
    //if(d_OK != inRet)
    //    return inRet;
	
    inRet = inCTOS_EMVProcessing();
    if(d_OK != inRet)
        return inRet;  

	inRet = GetPIN_With_3DESDUKPT(0);
    if(d_OK != inRet)
        return inRet;
    if(inCTOS_DCC_EligibilityCheck() == 1)
    {
        inRet = inCTOS_DCC_SingleRateRequest();
        if(d_OK != inRet)
            return inRet;
    }

    inRet = inCTOS_PreConnect();
	if(d_OK != inRet)
		return inRet;

	//inCTOSS_MDTDoUpdateFinalFare();
    inRet = inBuildAndSendIsoData();
    if(d_OK != inRet)
    {
        NotApprovedUI();
        return inRet;
    }

	//inRet = inCTOS_EMVTCUploadComfort();	
	//vdDebug_LogPrintf("inCTOS_EMVTCUploadComfort inRet[%d]", inRet);
	//if(d_OK != inRet)
	//	return inRet;

    inRet = inCTOS_SaveBatchTxn();
    if(d_OK != inRet)
        return inRet;

//    inRet = inCTOS_UpdateAccumTotal();
//    if(d_OK != inRet)
//        return inRet;

	// patrick add code 20141205 start
    inRet = inMultiAP_ECRSendSuccessResponse();
    //if(d_OK != inRet)
    //    return inRet;

	inRet = inCTOS_EMVTCUploadComfort();	
	vdDebug_LogPrintf("inCTOS_EMVTCUploadComfort inRet[%d]", inRet);
	if(d_OK != inRet)
		return inRet;
/*	
	inRet = ushCTOS_ePadSignature();
    if(d_OK != inRet)
        return inRet;

	inRet = inCTOSS_ERM_ReceiptRecvVia();
	if(d_OK != inRet)
        return inRet;
	
	if (isCheckTerminalMP200() == d_OK)
	{
		vdCTOSS_DisplayStatus(d_OK);
	}
	
    inRet = ushCTOS_printReceipt();
	vdDebug_LogPrintf("inRet =[%d]",inRet);
	
//	inRet = inCTOS_AdviceTransUpload();
//	 if(d_OK != inRet)
//        return inRet;

//    inRet = inCTOS_EMVTCUpload();
//    if(d_OK != inRet)
//        return inRet;
	
	vdDebug_LogPrintf("inRet =[%d]",inRet);
    if(d_OK != inRet)
        return inRet;
    else
        vdSetErrorMessage("");

	inRet = inMultiAP_MDBSendSuccessResponse();
	if(d_OK != inRet)
        return inRet;
*/
    return d_OK;
}

void vSetFont(void)
{
	CTOS_LCDSelectModeEx(d_LCD_TEXT_MODE, d_TRUE);
	CTOS_LCDFontSelectMode(d_FONT_TTF_MODE);
	CTOS_LCDTTFSelect((BYTE *) "ca_default.ttf", 0);
	CTOS_LCDTSelectFontSize(0x2432);
}

void vdInitCDGTrans(CDG_TRANS *psrCDGTrans, PAYMENT_INFO *psrPmtInfo){
	char szAmount[20];
	extern unsigned char gucUserCN2PaymentMode;
	vdDebug_LogPrintf("=====vdInitCDGTrans=====");
	vdGetCDGTransaction(psrCDGTrans);	// get GstMessage value - needed to print receipt because NETS app got the value during UpdateFare (UpdateFare is done in NETS app not here)

	sprintf(szAmount, "%06ld", psrPmtInfo->lnAmount);
	memcpy(psrCDGTrans->szFareAmt, szAmount, 6);
	sprintf(szAmount, "%06d", 0);
	memcpy(psrCDGTrans->szAdminAmt, szAmount, 6);
	memcpy(psrCDGTrans->szGSTAmt, szAmount, 6);

	sprintf(szAmount, "%012d", 0);
	memcpy(psrCDGTrans->szTotalFare, szAmount, 12);

	memcpy(psrCDGTrans->szJobNumber, psrPmtInfo->szJobNumber, 10);
	memcpy(psrCDGTrans->szTaxiNumber, psrPmtInfo->szTaxiNumber, 12);
	memcpy(psrCDGTrans->szDriverID, psrPmtInfo->szDriverId, 9);
	memset(psrCDGTrans->szCompanyCode, 0x20, 6);
	memcpy(psrCDGTrans->szCompanyCode, psrPmtInfo->szCompanyCode, 4);

	psrCDGTrans->ucBookingJob = psrPmtInfo->chBookingJob;

#ifdef DO_VIRTUAL_CABCHARGE
	psrCDGTrans->ucUseCN2PaymentMode = psrPmtInfo->chUseCN2PaymentMode;
	gucUserCN2PaymentMode = psrPmtInfo->chUseCN2PaymentMode;
	memcpy(psrCDGTrans->szTripInformation, psrPmtInfo->szTripInformation, 200);
	memcpy(psrCDGTrans->szPaymentInfo, psrPmtInfo->szPaymentInfo, 20);
#endif

	memcpy(psrCDGTrans->szPrivateField, psrPmtInfo->szPrivateField, 102);	//for NOF & Paylah

	vdSetCDGTransaction(psrCDGTrans);

	//for void
	//for DE63
	memcpy(srTransRec.IPPPlan,psrPmtInfo->szJobNumber, 10);
	inCTOSS_StoreBatchFieldData(&srBatchFlexiData, FLEXI_PAYMENT_INFO, &srPmtInfo, sizeof(srPmtInfo));
	inCTOSS_StoreBatchFieldData(&srBatchFlexiData, FLEXI_PAYMENT_ADDIONAL_INFO, &srPmtAddionalInfo, sizeof(srPmtAddionalInfo));
}


int inCTOS_WAVE_SALE(BOOL isEcr)
{
    int inRet = d_NO;
	CDG_TRANS srMyCDGTrans;

	
	//vdSetAppRunBySelf(1);
	
	vSetFont();
    //CTOS_LCDTClearDisplay();

    vdCTOS_TxnsBeginInit();

	vdCTOS_SetTransType(SALE);

	vdCTOSS_SetWaveTransType(1);

	vdCTOSS_GetAmt();

	inRet = inCTOSS_CLMOpenAndGetVersion();
	if(d_OK != inRet)
        return inRet;

	inRet = inCTOSS_ERM_CheckSlipImage();
	if(d_OK != inRet)
        return inRet;
	
	//display title
    vdDispTransTitle(SALE);
	memset(&srMyCDGTrans,0,sizeof(CDG_TRANS));
	vdInitCDGTrans(&srMyCDGTrans, &srPmtInfo);
	
    inRet = inCTOS_WaveFlowProcess();

	vdCTOSS_CLMClose();

    inCTOS_inDisconnect();

	
	vdDebug_LogPrintf("inRet =[%d]",inRet);

	if(d_OK == inRet)
	inCTOSS_UploadReceipt();

	if (isCheckTerminalMP200() == d_OK)
	{
		//CTOS_KBDBufFlush();
		if (d_OK != inRet)
		vdCTOSS_DisplayStatus(inRet);
		//WaitKey(5);
	}
	
    vdCTOS_TransEndReset();

    return inRet;
}



int inCTOS_WAVE_REFUND(void)
{
    int inRet = d_NO;

    //CTOS_LCDTClearDisplay();

	if (0 == inCTOSS_GetCtlsMode())
	{
		inRet = inCTOS_REFUND();
	}
	else
		{

		    vdCTOS_TxnsBeginInit();

			vdCTOS_SetTransType(REFUND);
			vdCTOSS_SetWaveTransType(1);

			vdCTOSS_GetAmt();

			inRet = inCTOSS_CLMOpenAndGetVersion();
			if(d_OK != inRet)
		        return inRet;
			//display title
		    vdDispTransTitle(REFUND);

		    inRet = inCTOS_WaveFlowProcess();

			vdCTOSS_CLMClose();

		    inCTOS_inDisconnect();
			
			
			vdDebug_LogPrintf("inRet =[%d]",inRet);
			
			if(d_OK == inRet)
			inCTOSS_UploadReceipt();
			

			if (isCheckTerminalMP200() == d_OK)
			{
				//CTOS_KBDBufFlush();
				if (d_OK != inRet)
				vdCTOSS_DisplayStatus(inRet);
				//WaitKey(5);
			}
	
		    vdCTOS_TransEndReset();
		}
    return inRet;
}
#if 0
void vdCTOS_InitWaveData(void)
{
	BYTE bInBuf[250];
	BYTE bOutBuf[250];
	BYTE *ptr = NULL;
	USHORT usInLen = 0;
	USHORT usOutLen = 0;
	USHORT usResult;
	int inCTLSmode = 0;

	
	inCTLSmode = inCTOSS_GetCtlsMode();
	if (NO_CTLS == inCTLSmode)
		return;

	//Open the Back Light in the LCD Display //
	vduiKeyboardBackLight(VS_TRUE);
	vduiLightOn();
	CTOS_LCDTClearDisplay();
    vdDispTitleString((BYTE *)"CONTACTLESS");
	setLCDPrint(5, DISPLAY_POSITION_LEFT, "CONFIGURE READER...");
	
	memset(bOutBuf, 0x00, sizeof(bOutBuf));
	memset(bInBuf, 0x00, sizeof(bInBuf));

	
	vdDebug_LogPrintf("d_IPC_CMD_EMV_WAVESETTAGS usInLen[%d],inCTLSmode=[%d] ",usInLen,inCTLSmode);

	if (CTLS_INTERNAL == inCTLSmode || CTLS_EXTERNAL == inCTLSmode)
	{
		usResult = inMultiAP_RunIPCCmdTypes("com.Source.SHARLS_EMV.SHARLS_EMV", d_IPC_CMD_EMV_WAVESETTAGS, bInBuf, usInLen, bOutBuf, &usOutLen);
	}

	if (CTLS_V3_SHARECTLS == inCTLSmode)
	{
		usResult = inMultiAP_RunIPCCmdTypes("com.Source.SHARLS_CTLS.SHARLS_CTLS", d_IPC_CMD_EMV_WAVESETTAGS, bInBuf, usInLen, bOutBuf, &usOutLen);
	}
	
}
#endif

void vdCTOS_InitWaveData(void)
{
	BYTE bInBuf[250];
	BYTE bOutBuf[250];
	BYTE *ptr = NULL;
	USHORT usInLen = 0;
	USHORT usOutLen = 0;
	USHORT usResult;
	int inCTLSmode = 0;
	
	inCTLSmode = inCTOSS_GetCtlsMode();
	if (NO_CTLS == inCTLSmode)
		return;

	//Open the Back Light in the LCD Display //
	vduiKeyboardBackLight(VS_TRUE);
	vduiLightOn();
	CTOS_LCDTClearDisplay();
    vdDispTitleString((BYTE *)"CONTACTLESS");
	setLCDPrint(5, DISPLAY_POSITION_LEFT, "CONFIGURE READER...");
	
	memset(bOutBuf, 0x00, sizeof(bOutBuf));
	memset(bInBuf, 0x00, sizeof(bInBuf));
	
	vdDebug_LogPrintf("d_IPC_CMD_EMV_WAVESETTAGS usInLen[%d],inCTLSmode=[%d] ",usInLen,inCTLSmode);

	if(strTCT.byPinPadMode == 0)
	{
		if(strTCT.byPinPadType == 3 && CTLS_V3_SHARECTLS == inCTLSmode)
		{
			char szV3PSendBuf[1024+1];
			int inOffSet = 0;
			int status;
			char szRecvBuf[1024+1];
			int inRecvlen = 0;
			unsigned char *pszPtr;
			
			vdCTOSS_SetV3PRS232Port(strTCT.byPinPadPort);
			inCTOSS_V3PRS232Open(strTCT.inPPBaudRate, 'N', 8, 1);

			// send STX INJECT_KEY ETX LRC
			memset(szV3PSendBuf, 0x00, sizeof(szV3PSendBuf));
			inOffSet = 0;
			szV3PSendBuf[inOffSet] = STX;
			inOffSet += 1;
			memcpy(&szV3PSendBuf[inOffSet], "INIT_WAVE", strlen("INIT_WAVE"));
			inOffSet += strlen("INIT_WAVE");
			szV3PSendBuf[inOffSet] = ETX;
			inOffSet += 1;			
			szV3PSendBuf[inOffSet] = (char) wub_lrc(&(szV3PSendBuf[1]), inOffSet-1);
			inOffSet += 1;
			inCTOSS_V3PRS232SendBuf(szV3PSendBuf, inOffSet);	
			status = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, (d_CEPAS_READ_BALANCE_CARD_PRESENT_TIMEOUT));						

			//inCTOSS_USBHostCloseEx();
			inCTOSS_V3PRS232Close();
			return ;
		}
	}

	if (CTLS_INTERNAL == inCTLSmode || CTLS_EXTERNAL == inCTLSmode)
	{
		usResult = inMultiAP_RunIPCCmdTypes("com.Source.SHARLS_EMV.SHARLS_EMV", d_IPC_CMD_EMV_WAVESETTAGS, bInBuf, usInLen, bOutBuf, &usOutLen);
	}

	if (CTLS_V3_SHARECTLS == inCTLSmode || CTLS_V3_INT_SHARECTLS == inCTLSmode)
	{
		usResult = inMultiAP_RunIPCCmdTypes("com.Source.SHARLS_CTLS.SHARLS_CTLS", d_IPC_CMD_EMV_WAVESETTAGS, bInBuf, usInLen, bOutBuf, &usOutLen);
	}	

}

int inDoDinersUpload(void)
{
	int inRet =  -1;

    vdCTOS_TxnsBeginInit();

	// process upload offline for DINERS now
	vdCTOS_SetTransType(SALE_OFFLINE);	
	inCTOS_SelectDinersAppHost();

	inRet = inCTOS_PreConnect();
    if(d_OK != inRet)
        return ST_ERROR;

	inCTOS_CheckAndSelectMutipleMID();
	if (inProcessNPXOfflineSaleUpload(&srTransRec) != ST_SUCCESS)
	{
		inCTOS_inDisconnect();
		return ST_ERROR;
	}

	inCTOS_inDisconnect();

	return d_OK;
}

int inDoDinersTCUpload(void)
{
	int inRet =  -1;

    vdCTOS_TxnsBeginInit();

	// process upload offline for DINERS now
	vdCTOS_SetTransType(TC_UPLOAD);	
	inCTOS_SelectDinersAppHost();

	inRet = inCTOS_PreConnect();
    if(d_OK != inRet)
        return ST_ERROR;

	inCTOS_CheckAndSelectMutipleMID();
	if (inProcessNPXTCUpload(&srTransRec) != ST_SUCCESS)
	{
		inCTOS_inDisconnect();
		return ST_ERROR;
	}

	inCTOS_inDisconnect();

	return d_OK;
}

int inDinersReversal(void)
{
	int inRet =  -1;

    vdCTOS_TxnsBeginInit();

	// process reversal for Alipay now
	vdCTOS_SetTransType(REVERSAL);	
	inCTOS_SelectDinersAppHost();

	inRet = inCTOS_PreConnect();
    if(d_OK != inRet)
        return ST_ERROR;

	inCTOS_CheckAndSelectMutipleMID();
	vdSetSaveReversal(1);
	if (inProcessReversal(&srTransRec) != ST_SUCCESS)
	{
		vdSetSaveReversal(0);				
		//NotApprovedUI();
		inCTOS_inDisconnect();
		return ST_ERROR;
	}

	vdSetSaveReversal(0);
	inCTOS_inDisconnect();

	return d_OK;
}

int inDoAppUpdate(void)
{
    int inRet =  -1;

    //Call main to do file copy
    inCallJAVA_CopyFileUI();
    return d_OK;
}

int inDinersSendPendingTrans(void)
{
	 int inRet = d_NO;
	vdDebug_LogPrintf("inDinersSendPendingTrans....");
	ginDoingHouseKeeping =1;
	//refer vx820
	//inRunFunction(pobTran, CREDIT_GET_PENDING_REV);
	//inRunFunction(pobTran, CREDIT_GET_PENDING_OFFLINE);
	//inRunFunction(pobTran, CREDIT_GET_PENDING_TCUPLOAD);
	inRet = inDinersReversal();
	vdDebug_LogPrintf("inDinersReversal....inRet=[%d],ginDoingHouseKeeping=[%d]",inRet,ginDoingHouseKeeping);
	if (ginDoingHouseKeeping ==0)
		return d_NO;
	//if (inRet == d_OK)
	{
		inRet = inDoDinersUpload();
		vdDebug_LogPrintf("inDoDinersUpload....inRet=[%d],ginDoingHouseKeeping=[%d]",inRet,ginDoingHouseKeeping);
		if (ginDoingHouseKeeping ==0)
			return d_NO;
		//if (inRet == d_OK)
			inRet = inDoDinersTCUpload();
		vdDebug_LogPrintf("inDoDinersTCUpload....inRet=[%d],ginDoingHouseKeeping=[%d]",inRet,ginDoingHouseKeeping);
		if (ginDoingHouseKeeping ==0)
			return d_NO;
	}

	return inRet;
}


