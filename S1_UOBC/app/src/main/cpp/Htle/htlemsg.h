/**************************************************************************
********************************************************************************/
#ifndef HTLEMSG_H
#define HTLEMSG_H

#define HLE_MSG_ID							0x66DB

#define EDIT_HLE_MSG						0x66DB0001
#define TMK_DOWNLOAD_MSG					0x66DB0002
#define TEST_MSG							0x66DB0003
#define HTLE_INSERT_MSG						0x66DB0004
#define ENTER_PIN_MSG						0x66DB0005
#define TRYAGAIN_PIN_MSG					0x66DB0006
#define LASTPIN_TRYAGAIN_MSG				0x66DB0007
#define PIN_BLOCK_MSG						0x66DB0008
#define HTLE_APPL_BLOCKED					0x66DB0009
#define HTLE_INCORR_LEN						0x66DB000A
#define HTLE_APPL_FAILED					0x66DB000B
#define HTLE_FUNC_NO_SUP					0x66DB000C
#define HTLE_NO_APPL						0x66DB000D
#define HTLE_ERR_P1_P2						0x66DB000E
#define HTLE_WAIT_MSG						0x66DB000F
#define HTLE_AUTH_FAIL						0x66DB0010
#define HTLE_MEM_FAIL						0x66DB0011
#define HTLE_CON_FAIL						0x66DB0012
#define HTLE_CLA_ERR						0x66DB0013
#define HTLE_DE_ERR							0x66DB0014
#define HTLE_KCV_ERR						0x66DB0015
#define HTLE_KEY_ERR						0x66DB0016
#define HTLE_SEC_ERR						0x66DB0017
#define HTLE_INVAL_DATA					0x66DB0018
#define HTLE_CARD_ERR						0x66DB0019
#define HTLE_KEK_ERR						0x66DB001A
#define HTLE_NO_SESS						0x66DB001B
#define HTLE_NO_KEY							0x66DB001C
#define HTLE_NO_AID							0x66DB001D
#define TWK_DOWNLOAD_MSG					0x66DB001E

#endif /* SCBMSG_H */



