#include <string.h>
#include <stdio.h>
#include <ctosapi.h>
#include <stdlib.h>
#include <stdarg.h>
#include <time.h>


/** These two files are necessary for calling CTOS API **/
#include "../Includes/POSbatch.h"
#include "../Includes/POSTypedef.h"

#include "../FileModule/myFileFunc.h"
#include "../POWRFAIL/POSPOWRFAIL.h"
#include "..\debug\debug.h"
#include "..\Database\DatabaseFunc.h"
#include "..\Includes\myEZLib.h"
#include "..\ui\Display.h"
#include "..\Includes\POSHost.h"
#include "..\Includes\Wub_lib.h"
#include "..\debug\debug.h"
#include "../Ctls/POSCtls.h"
#include "../Includes/V5IsoFunc.h"
#include "tms.h"

#define DAYS  (60*60*24)
#define HOURS (60*60)
#define MINS  (60)


#define DDMMYY 		0x0001
#define MMDDYY 		0x0002
#define MMYY   		0x0004
#define YYMM   		0x0008
#define YYMMDD 		0x0010
#define YYYYMMDD 	0x0020

#define DATE_SIZE              6            /* Date in MMDDYY format        */
#define TIME_SIZE              6            /* Time in HHMMSS format        */
#define CENTURY_SIZE           2            /* as in 20th or 19th */

#define NULL_CH	'\0'


static char gszDLTime[10];

extern void vdCTOSS_GetTermSerialNO(char *pszSN);

static void vdDisplayAt(int x, int y, const char *m)
{
	CTOS_LCDTClearDisplay();
	CTOS_LCDTPrintXY(x, y, (unsigned char *)m);
}


static void vdAddDateTimebySecOff(char* szNewTime, int off, int flag)
{
    time_t now_time;      
	time_t end_time;               

	struct tm tmtime;
    struct tm* tmp_time = &tmtime;

	strptime(szNewTime,"%Y%m%d%H%M%S",tmp_time);
    now_time = mktime(tmp_time);
    vdDebug_LogPrintf("%ld\n",now_time);
    
    memset(tmp_time, 0x00, sizeof(tmp_time));
    
	end_time=now_time+ off*flag;
	tmp_time = localtime(&end_time);

    strftime(szNewTime, 24, "%Y%m%d%H%M%S", tmp_time);    
}



static void vdVSAbortProc (int inErrCode)
{
    char szLocal[22 + 1];	

    CTOS_Beep();					
	memset(szLocal,0x00, sizeof(szLocal));
    sprintf(szLocal, "Internal error: #%03d", inErrCode);
    vdDisplayAt(1, 1, szLocal);		
    CTOS_Beep();					
    exit(1);						
}

/*********************************************************************************************
* @func void | vdGetDate |
*				Gets the date.  The varible pchDate should be allocated
*                  outside.
*
* @parm char * | pchDate |
*       Buffer to receive the date string
*
* @parm int |inMode|
*		Format of the date DDMMYY, MMDDYY, YYYYMMDD, YYMMDD(default)
*
* @end
**********************************************************************************************/
static void vdGetDate (char *pchDate, int inMode)
{
	char Buffer[15+1];
	int inDateLen;
	CTOS_RTC SetRTC;
    char szDate[4+1];
    char szTime[6+1];
    char szBuf[2+1];

	if (CTOS_RTCGet(&SetRTC) != d_OK)					
		vdVSAbortProc(125);						

	switch(inMode)
	{
		case DDMMYY:
			sprintf(pchDate, "%02d", SetRTC.bDay);			
			sprintf(&pchDate[2], "%02d", SetRTC.bMonth);	
			sprintf(&pchDate[4], "%02d", SetRTC.bYear);		
			inDateLen = DATE_SIZE;
			break;

		case MMDDYY:
			sprintf(pchDate, "%02d", SetRTC.bMonth);		
			sprintf(&pchDate[2], "%02d", SetRTC.bDay);		
			sprintf(&pchDate[4], "%02d", SetRTC.bYear);
			inDateLen = DATE_SIZE;
			break;

		case YYYYMMDD:
			sprintf(pchDate, "%04d", SetRTC.bYear + 2000);
			sprintf(&pchDate[4], "%02d", SetRTC.bMonth);
			sprintf(&pchDate[6], "%02d", SetRTC.bDay);
			inDateLen = DATE_SIZE+CENTURY_SIZE;
			break;

		case YYMMDD:
		default:
			sprintf(pchDate, "%02d", SetRTC.bYear);
			sprintf(&pchDate[2], "%02d", SetRTC.bMonth);
			sprintf(&pchDate[4], "%02d", SetRTC.bDay);
			inDateLen = DATE_SIZE;
	}

	pchDate[inDateLen] = NULL_CH;
	vdDebug_LogPrintf("vdGetDate pchDate[%s]", pchDate);
	return;
}


void vdCTOSS_GetTermSerialNO(char *pszSN)
{
	USHORT ushRet = 0;
	BYTE baBuf[17 + 1];
	int i = 0;
	int len = 0;
	char szSN[16+1];
	
	int inSNLen = 0;
 
	if (NULL == pszSN)
		return;

	memset(baBuf,0x00,sizeof(baBuf));
	memset(szSN,0x00,sizeof(szSN));
	
	CTOS_GetFactorySN(baBuf);
	baBuf[16] = 0x00;


	len = strlen(baBuf);
	vdDebug_LogPrintf("trim factory SN[%s], len[%d]", baBuf, len);

	/*remove the last digit for LRC*/
	memcpy(szSN, baBuf, len-1);	 //from now, 2020,06.22, copy whole serial number

	len = strlen(szSN);
	vdDebug_LogPrintf("szSN[%s], len[%d]", szSN, len);

	//refer TMS, remove LRC, pack 0000 in front, size 16 bytes
	memset(pszSN,'0',16-len);
	memcpy(&pszSN[16-len],szSN,len);

	vdDebug_LogPrintf("pszSN[%s]", pszSN);
	
 return;
}

int inSchedule(void)
{
	int inStartTime = 0;
	int inEndTime = 0;
	
	int inDayGap = 0;
	int inRetryTimes = 0;
	int inRetryDelay = 0;

	int inStrlen=0;
	int inDLTime = 0;
	int inFirstDL = 0;

	char szStartHours[10];
	char szStartMins[10];
	
	char szEndHours[10];
	char szEndMins[10];

	char szDLHours[10];
	char szDLMins[10];
	char szDLSecs[10];

	char szDLYear[10];
	char szDLMonth[10];
	char szDLDate[10];

	char szDLDateTime[20];
	
	BYTE szLastSN[2+1];
	int inTermSN = 0;

	BYTE szTermSN[50+1];	

	CTOS_RTC SetRTC;
	int inYear, inMonth, inDate,inStartHours, inStartMins,inEndHours, inEndMins ,inDateGap, inDLHours, inDLMins, inDLSecs;
	char szTemp[10];
	char u1t_Mins[10];
	char u1t_EndDate[10];
	int inDLYear, inDLMonth, inDLDate;

	if(strSKD.inDayGap == 0)
	{		
        vdDebug_LogPrintf("Skip inSchedule");
		return d_NO;
	}

	memset(szTemp,0x00,sizeof(szTemp));
	memcpy(szTemp,strSKD.szLastDLDate,2);
	inYear = atoi(szTemp);
	memset(szTemp,0x00,sizeof(szTemp));
	memcpy(szTemp,&strSKD.szLastDLDate[2],2);
	inMonth = atoi(szTemp);
	memset(szTemp,0x00,sizeof(szTemp));
	memcpy(szTemp,&strSKD.szLastDLDate[4],2);
	inDate = atoi(szTemp);


	inStartTime = atoi(strSKD.szStartTime);
	inEndTime = atoi(strSKD.szEndTime);

    inDayGap = strSKD.inDayGap;
	inRetryTimes = strSKD.inRetryTimes;
	inRetryDelay = strSKD.inRetryDelay;
	inFirstDL = strSKD.inFirstTimeDL;
	

    vdDebug_LogPrintf("inStartTime[%d]", inStartTime);
    vdDebug_LogPrintf("inEndTime[%d]", inEndTime);
    vdDebug_LogPrintf("inDayGap[%d]", inDayGap);
    vdDebug_LogPrintf("inRetryTimes[%d]", inRetryTimes);
    vdDebug_LogPrintf("inRetryDelay[%d]", inRetryDelay);

	memset(szTermSN, 0x00, sizeof(szTermSN));
	memset(szLastSN, 0x00, sizeof(szLastSN));

	vdCTOSS_GetTermSerialNO(szTermSN);
	
    vdDebug_LogPrintf("szTermSN[%s]", szTermSN);
	
	inStrlen=strlen(szTermSN);
	strcpy(szLastSN, &szTermSN[inStrlen-2]);

	
    vdDebug_LogPrintf("szLastSN[%s]", szLastSN);
	
	inTermSN=atoi(szLastSN);
	
    vdDebug_LogPrintf("inTermSN[%d]", inTermSN);

	
	
	inDLTime = (inEndTime - inStartTime)/4 * (inTermSN) / 100 + inStartTime;

	
    vdDebug_LogPrintf("inDLTime[%d]", inDLTime);

	memset(gszDLTime, 0x00, sizeof(gszDLTime));
	
    //sprintf(szDLTime, "%04d", inDLTime);
    sprintf(gszDLTime, "%04d", inDLTime);

    vdDebug_LogPrintf("gszDLTime[%s]", gszDLTime);
	
	CTOS_RTCGet(&SetRTC);
	vdDebug_LogPrintf("bYear=[%d].bMonth=[%d].bDay=[%d].",SetRTC.bYear,SetRTC.bMonth,SetRTC.bDay);
	vdDebug_LogPrintf("inYear=[%d].inMonth=[%d].inDate=[%d].",inYear,inMonth,inDate);
	
	vdDebug_LogPrintf("strCTS.szLastDLDate[%s]", strSKD.szLastDLDate);
	
	inDateGap = inCTOSS_CheckIntervialDateFrom2013((SetRTC.bYear+2000), SetRTC.bMonth, SetRTC.bDay) - inCTOSS_CheckIntervialDateFrom2013((inYear+2000), inMonth, inDate);
	vdDebug_LogPrintf("inDateGap=[%d],strSKD.szLastDLDate=[%s]",inDateGap,strSKD.szLastDLDate);

	if((inDateGap < strSKD.inDayGap) && (inFirstDL == 0))
		return d_NO;


	 memset(szStartHours,0x00,sizeof(szStartHours));
	 memset(szStartMins,0x00,sizeof(szStartMins));	 
	 memset(szEndHours,0x00,sizeof(szEndHours));
	 memset(szEndMins,0x00,sizeof(szEndMins));
	
	 
	 memcpy(szStartHours,strSKD.szStartTime,2);
	 memcpy(szStartMins,&(strSKD.szStartTime[2]),2);
	 memcpy(szEndHours,strSKD.szEndTime,2); 
	 memcpy(szEndMins,&(strSKD.szEndTime[2]),2);


	 inStartHours = atoi(szStartHours);
	 inStartMins = atoi(szStartMins); 
	 inEndHours = atoi(szEndHours);
	 inEndMins = atoi(szEndMins);
	

	 memset(szDLDateTime, 0x00, sizeof(szDLDateTime));
	 
	 memset(szDLHours,0x00,sizeof(szDLHours));
	 memset(szDLMins,0x00,sizeof(szDLMins));
	 memset(szDLSecs,0x00,sizeof(szDLSecs));

	 memset(szDLYear,0x00,sizeof(szDLYear));
	 memset(szDLMonth,0x00,sizeof(szDLMonth));
	 memset(szDLDate,0x00,sizeof(szDLDate));

	 if(inFirstDL == 1)
	 {
	     sprintf(szDLDateTime, "%02d%02d%02d%02d%02d%02d", SetRTC.bYear,SetRTC.bMonth,SetRTC.bDay, SetRTC.bHour,SetRTC.bMinute,0);
         memcpy(&szDLDateTime[6], gszDLTime, 2);
		 memcpy(&szDLDateTime[8], &gszDLTime[2], 2);
		 
         strcpy(strSKD.szNextDLTime, szDLDateTime);

	 }
	 else
	 {
         strcpy(szDLDateTime, strSKD.szNextDLTime);

	 }
	
	memcpy(szDLYear,&szDLDateTime[0],2); 
	memcpy(szDLMonth, &szDLDateTime[2], 2);
	memcpy(szDLDate, &szDLDateTime[4], 2);

	 memcpy(szDLHours,&szDLDateTime[6],2); 
	 memcpy(szDLMins, &szDLDateTime[8], 2);
	 memcpy(szDLSecs, &szDLDateTime[10], 2);

	inDLYear = atoi(szDLYear);
	 inDLMonth = atoi(szDLMonth);
	 inDLDate = atoi(szDLDate);

	 inDLHours = atoi(szDLHours);
	 inDLMins = atoi(szDLMins);
	 inDLSecs = atoi(szDLSecs);
	 
	 vdDebug_LogPrintf("DownloadDate[%02d:%02d:%02d],DownloadTime[%02d:%02d:%02d]",inDLYear,inDLMonth,inDLDate, inDLHours, inDLMins, inDLSecs);
	 vdDebug_LogPrintf("CurrentTime[%02d:%02d:%02d],CurrentTime[%02d:%02d:%02d]",SetRTC.bYear, SetRTC.bMonth, SetRTC.bDay, SetRTC.bHour, SetRTC.bMinute, SetRTC.bSecond);

	if ((inDLYear == SetRTC.bYear)&&(inDLMonth==SetRTC.bMonth)&&(inDLDate == SetRTC.bDay))
	{
		vdDebug_LogPrintf("Date is match");
	}
	else
		return d_NO;

	 //if((inDLHours == SetRTC.bHour)&&(inDLMins==SetRTC.bMinute)&&(inDLSecs == SetRTC.bSecond))
	 // if idle check charls_com status, then will over 1 seconds, will can't call below
	 if((inDLHours == SetRTC.bHour)&&(inDLMins==SetRTC.bMinute))
	 {

	     //sprintf(strCTS.szLastDLDate,"%02d%02d%02d",SetRTC.bYear,SetRTC.bMonth,SetRTC.bDay);
	     //sprintf(strCTS.szLastDLTime,"%02d%02d%02d",SetRTC.bHour,SetRTC.bMinute,SetRTC.bSecond);
		 strSKD.inFirstTimeDL = 0;
		 vdDebug_LogPrintf("DOWNLOADING");

		 //strCTS.inCurRetryTimes = 0;
		 //inUpdateNextScheduleConnectTime(0);
		 inSKDSave(1);
		 //inCTOS_LogFileUpload();

		 return d_OK;
	 }
	 //else
	 //{
	 //	inUpdateNextScheduleConnectTime(0);
	 //}

	 


    return d_NO;
}



int inUpdateNextScheduleConnectTime(int Success)
{
	int inCurrentRetry = 0;
	int inMaxRetry = 0;
	int inRetryDelay = 0;
	int inDayGap = 0;
	int inTermLoadBal = 0;
	int inSetNextScheduleDayFlag = 0;
	char nextConnectDateTime[20] = {0}; 
	char tmpConnectDateTime[20] = {0};
	char scheduleStart[8] = {0};
	char scheduleEnd[8] = {0};
	char currentDateTime[20] = {0};
	CTOS_RTC SetRTC;
        
    CTOS_RTCGet(&SetRTC);

	inCurrentRetry = strSKD.inCurRetryTimes;
	inMaxRetry = strSKD.inRetryTimes;
	inRetryDelay = strSKD.inRetryDelay;
	inDayGap = strSKD.inDayGap;
	inTermLoadBal = strSKD.inTermLoadBal;


	if(1 == Success)
	{
		strSKD.inCurRetryTimes = 0;
		sprintf(strSKD.szLastDLDate,"%02d%02d%02d",SetRTC.bYear,SetRTC.bMonth,SetRTC.bDay);
		sprintf(strSKD.szLastDLTime,"%02d%02d%02d",SetRTC.bHour,SetRTC.bMinute,SetRTC.bSecond);

		memcpy(tmpConnectDateTime, "20", 2); 
		
		vdDebug_LogPrintf("strCTS.szNextDLTime[%s]", strSKD.szNextDLTime);
	    memcpy(&tmpConnectDateTime[2], strSKD.szNextDLTime, 12);
	    memset(nextConnectDateTime, 0x00, sizeof(nextConnectDateTime)); 
	    memcpy(nextConnectDateTime, tmpConnectDateTime, 14);
				
		vdDebug_LogPrintf("nextConnectDateTime[%s]", nextConnectDateTime);
		vdAddDateTimebySecOff(nextConnectDateTime, inDayGap, DAYS);
		
		vdDebug_LogPrintf("nextConnectDateTime[%s]", nextConnectDateTime);

						
	    memcpy(&nextConnectDateTime[8], gszDLTime, 4);
		
		if(memcmp(currentDateTime, &nextConnectDateTime[2], 6) > 0)
			memcpy(&nextConnectDateTime[2], currentDateTime, 6);

		strSKD.inCurRetryTimes = 0;
		
		memcpy(strSKD.szNextDLTime, &nextConnectDateTime[2], 12);	
		inSKDSave(1);
	
		vdDebug_LogPrintf("Download Success");

		return d_OK;
	}

	vdGetDate(currentDateTime, YYMMDD);
	


	memcpy(scheduleStart, strSKD.szStartTime, 4);
	memcpy(scheduleEnd, strSKD.szEndTime, 4);
	memcpy(nextConnectDateTime, strSKD.szNextDLTime, 12);

	memcpy(tmpConnectDateTime, "20", 2); 
	memcpy(&tmpConnectDateTime[2], nextConnectDateTime, 12);
	memset(nextConnectDateTime, 0x00, sizeof(nextConnectDateTime)); 
	memcpy(nextConnectDateTime, tmpConnectDateTime, 14);
	
	vdDebug_LogPrintf("nextConnectDateTime[%s]inCurrentRetry[%d]inMaxRetry[%d]inRetryDelay[%d]", nextConnectDateTime, inCurrentRetry, inMaxRetry, inRetryDelay);
	if((inCurrentRetry < inMaxRetry) && 0 == Success)
	{
		vdAddDateTimebySecOff(nextConnectDateTime, inRetryDelay, MINS);
		if(memcmp(&nextConnectDateTime[8], scheduleEnd, 4) > 0)
		{
			inSetNextScheduleDayFlag = 1;
		}
		else
		{
			if(memcmp(currentDateTime, &nextConnectDateTime[2], 6) > 0)
				memcpy(&nextConnectDateTime[2], currentDateTime, 6);
			inCurrentRetry ++;
			
			strSKD.inCurRetryTimes = inCurrentRetry;	
			memcpy(strSKD.szNextDLTime, &nextConnectDateTime[2], 12);
			
			inSKDSave(1);
		}
	}
	else
		inSetNextScheduleDayFlag = 1;

	if(inSetNextScheduleDayFlag)
	{
		
		memcpy(&nextConnectDateTime[2], strSKD.szNextDLTime, 12);	
		vdAddDateTimebySecOff(nextConnectDateTime, inDayGap, DAYS);
		
				
	    memcpy(&nextConnectDateTime[8], gszDLTime, 4);
		

		if(memcmp(currentDateTime, &nextConnectDateTime[2], 6) > 0)
			memcpy(&nextConnectDateTime[2], currentDateTime, 6);

		strSKD.inCurRetryTimes = 0;
		memcpy(strSKD.szNextDLTime, &nextConnectDateTime[2], 12);	
		inSKDSave(1);
	}

	vdDebug_LogPrintf("inUpdateNextScheduleConnectTime[%s]inCurrentRetry[%d]", nextConnectDateTime, inCurrentRetry);
}

