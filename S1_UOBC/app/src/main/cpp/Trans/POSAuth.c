/*******************************************************************************

*******************************************************************************/

#include <string.h>
#include <stdio.h>
#include <ctosapi.h>
#include <stdlib.h>
#include <stdarg.h>
#include <typedef.h>


#include "..\Includes\POSTypedef.h"
#include "..\Includes\POSMain.h"
#include "..\Includes\POSTrans.h"
#include "..\Includes\POSHost.h"
#include "..\Includes\POSSale.h"
#include "..\Includes\POSbatch.h"
#include "..\ui\Display.h"
#include "..\Includes\V5IsoFunc.h"
#include "..\comm\V5comm.h"
#include "..\print\print.h"
#include "..\Includes\MultiApLib.h"
#include "..\Aptrans\MultiAptrans.h"

#include "../Database/DatabaseFunc.h"
#include "..\erm\Poserm.h"
#include "../Includes/POSSetting.h"
#include "..\Debug\Debug.h"

int inCTOS_PreAuthFlowProcess(void)
{
    int inRet = d_NO;


    vdCTOS_SetTransType(PRE_AUTH);
    
    vdDispTransTitle(PRE_AUTH);
    
    inRet = inCTOSS_CheckMemoryStatus();
    if(d_OK != inRet)
        return inRet;
    
    inRet = inCTOS_GetTxnPassword();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_GetCardFields();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_SelectHost();
    if(d_OK != inRet)
        return inRet;

    if (inMultiAP_CheckMainAPStatus() == d_OK)
    {
        inRet = inCTOS_MultiAPSaveData(d_IPC_CMD_PRE_AUTH);
        if(d_OK != inRet)
            return inRet;
    }
    else
    {
		if (0 == fGetAppRunBySelf() && inMultiAP_CheckSubAPStatus() == d_OK)
        {
            inRet = inCTOS_MultiAPGetData();
            if(d_OK != inRet)
                return inRet;
            
            inRet = inCTOS_MultiAPReloadTable();
            if(d_OK != inRet)
                return inRet;
        }
        inRet = inCTOS_MultiAPCheckAllowd();
        if(d_OK != inRet)
            return inRet;
    }

    inRet = inCTOS_CheckAndSelectMutipleMID();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_PreConnect();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_CheckIssuerEnable();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_CheckTranAllowd();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_CheckMustSettle();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_GetInvoice();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_GetTxnBaseAmount();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_UpdateTxnTotalAmount();
    if(d_OK != inRet)
        return inRet;

	if (srTransRec.HDTid == AMEX_HOST_INDEX)
	{
	    inRet = inCTOS_Get4DBC();
	    if(d_OK != inRet)
	        return inRet;
	}
	else
	{		
	    inRet = inCTOS_GetCVV2();
	    if(d_OK != inRet)
	        return inRet;
	}

    inRet = inCTOS_CustComputeAndDispTotal();
    if(d_OK != inRet)
        return inRet;
    
    inRet = inCTOS_EMVProcessing();
    if(d_OK != inRet)
        return inRet; 

	inRet = GetPIN_With_3DESDUKPT(0);
    if(d_OK != inRet)
        return inRet;

    inRet = inBuildAndSendIsoData();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_SaveBatchTxn();
    if(d_OK != inRet)
        return inRet;

	inRet = ushCTOS_ePadSignature();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOSS_ERM_ReceiptRecvVia();
    if(d_OK != inRet)
        return inRet;

	if (isCheckTerminalMP200() == d_OK)
	{
		vdCTOSS_DisplayStatus(d_OK);
	}
	
    inRet = ushCTOS_printReceipt();
    if(d_OK != inRet)
        return inRet;

	if (srTransRec.HDTid == AMEX_HOST_INDEX)
	{				
		inRet = inCTOS_AdviceTransUpload();
		 if(d_OK != inRet)
			return inRet;
	}	

    inRet = inCTOS_EMVTCUpload();
    if(d_OK != inRet)
        return inRet;
    else
        vdSetErrorMessage("");      
    return d_OK;
}

int inCTOS_PREAUTH(void)
{
    int inRet = d_NO;

	if (strTCT.byTerminalType == 8) //UPT1000F
	{
      CTOS_LCDTClearDisplay();
    }
    
    vdCTOS_TxnsBeginInit();

	inRet = inCTOSS_ERM_CheckSlipImage();
	if(d_OK != inRet)
        return inRet;
    
    inRet = inCTOS_PreAuthFlowProcess();

    inCTOS_inDisconnect();

	if(d_OK == inRet)
	inCTOSS_UploadReceipt();

	if (isCheckTerminalMP200() == d_OK)
	{
		if (d_OK != inRet)
		vdCTOSS_DisplayStatus(inRet);
	}

    vdCTOS_TransEndReset();

    return inRet;
}

//auto topup set srTransPara->byEntryMode to manual
int inCTOS_PreAuthAutoTopupFlowProcess(TRANS_DATA_TABLE *srTransPara)
{
    int inRet = d_NO;

    vdCTOS_SetTransType(PREAUTH_TOPUP);
    vdDispTransTitle(PREAUTH_TOPUP);
    usCTOSS_ClearAllShowTittle("AUTH TOPUP");
    {
        memcpy(&srTransRec, srTransPara, sizeof(TRANS_DATA_TABLE));
        DebugAddHEX("fill txn data, topup amt:", srTransRec.szBaseAmount, 6);
        srTransRec.byEntryMode = CARD_ENTRY_MANUAL;
    }
    inRet = inCTOS_LoadCDTandIIT();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_VoidSelectHost();//if not load, strHDT.inHostIndex is not correct, effect batch/accum
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_PreConnect();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_GetInvoice();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_UpdateTxnTotalAmount();//use pass in base amount to update total amount
    if(d_OK != inRet)
        return inRet;

    inRet = inBuildAndSendIsoData();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_SaveBatchTxn();
    if(d_OK != inRet)
        return inRet;

    inRet = ushCTOS_ePadSignature();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOSS_ERM_ReceiptRecvVia();
    if(d_OK != inRet)
        return inRet;

    if (isCheckTerminalMP200() == d_OK)
    {
        vdCTOSS_DisplayStatus(d_OK);
    }

    inRet = ushCTOS_printReceipt();
    if(d_OK != inRet)
        return inRet;
    inRet = inCTOS_EMVTCUpload();
    if(d_OK != inRet)
        return inRet;
    else
        vdSetErrorMessage("");
    return d_OK;
}

int inCTOS_PreAuthAutoVoidFlowProcess(TRANS_DATA_TABLE *srTransPara)
{
    int inRet = d_NO;

    vdCTOS_SetTransType(VOID_PREAUTH);
    vdDispTransTitle(VOID_PREAUTH);
    usCTOSS_ClearAllShowTittle("AUTH REVERSAL");
    {


        memcpy(&srTransRec, srTransPara, sizeof(TRANS_DATA_TABLE));
        DebugAddHEX("fill txn data, auth reversal amt:", srTransRec.szBaseAmount, 6);
        DebugAddHEX("fill txn data, szOrgInvoiceNo:", srTransRec.szOrgInvoiceNo, 3);
        DebugAddHEX("fill txn data, szInvoiceNo:", srTransRec.szInvoiceNo, 3);
        memcpy(srTransRec.szInvoiceNo, srTransRec.szOrgInvoiceNo, 3);//to retrive auth record and update it to auth reversal status

        inRet = inCTOS_BatchSearchEx();
        if(d_OK != inRet)
            return inRet;
//        srTransRec.byEntryMode = CARD_ENTRY_MANUAL;
    }
    inRet = inCTOS_LoadCDTandIIT();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_VoidSelectHost();//if not load, strHDT.inHostIndex is not correct, effect batch/accum
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_PreConnect();
    if(d_OK != inRet)
        return inRet;

/*    inRet = inCTOS_GetInvoice();
    if(d_OK != inRet)
        return inRet;*/

    inRet = inCTOS_UpdateTxnTotalAmount();//use pass in base amount to update total amount
    if(d_OK != inRet)
        return inRet;

    inRet = inBuildAndSendIsoData();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_SaveBatchTxn();
    if(d_OK != inRet)
        return inRet;
//No need to sign for Auth reversal
/*    inRet = ushCTOS_ePadSignature();
    if(d_OK != inRet)
        return inRet;*/

    inRet = inCTOSS_ERM_ReceiptRecvVia();
    if(d_OK != inRet)
        return inRet;

    if (isCheckTerminalMP200() == d_OK)
    {
        vdCTOSS_DisplayStatus(d_OK);
    }

    inRet = ushCTOS_printReceipt();
    if(d_OK != inRet)
        return inRet;
    inRet = inCTOS_EMVTCUpload();
    if(d_OK != inRet)
        return inRet;
    else
        vdSetErrorMessage("");
    return d_OK;
}
int inCTOS_PreAuthTopupFlowProcess(void)
{
    int inRet = d_NO;


    vdCTOS_SetTransType(PREAUTH_TOPUP);

    vdDispTransTitle(PREAUTH_TOPUP);

    inRet = inCTOSS_CheckMemoryStatus();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_GetTxnPassword();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_CheckAndSelectMutipleMID();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_PreConnect();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_CheckIssuerEnable();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_CheckTranAllowd();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_CheckMustSettle();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_GetInvoice();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_GetTxnBaseAmount();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_UpdateTxnTotalAmount();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_GetCVV2();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_CustComputeAndDispTotal();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_EMVProcessing();
    if(d_OK != inRet)
        return inRet;

    inRet = GetPIN_With_3DESDUKPT(0);
    if(d_OK != inRet)
        return inRet;

    inRet = inBuildAndSendIsoData();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_SaveBatchTxn();//no update accum file for both auth and auth top up txn
    if(d_OK != inRet)
        return inRet;

    inRet = ushCTOS_ePadSignature();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOSS_ERM_ReceiptRecvVia();
    if(d_OK != inRet)
        return inRet;

    if (isCheckTerminalMP200() == d_OK)
    {
        vdCTOSS_DisplayStatus(d_OK);
    }

    inRet = ushCTOS_printReceipt();
    if(d_OK != inRet)
        return inRet;
    inRet = inCTOS_EMVTCUpload();
    if(d_OK != inRet)
        return inRet;
    else
        vdSetErrorMessage("");
    return d_OK;
}
int inCTOS_PREAUTH_TOPUP(void)
{
    int inRet = d_NO;

    if (strTCT.byTerminalType == 8) //UPT1000F
    {
        CTOS_LCDTClearDisplay();
    }

    vdCTOS_TxnsBeginInit();

    inRet = inCTOSS_ERM_CheckSlipImage();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_PreAuthTopupFlowProcess();

    inCTOS_inDisconnect();

    if(d_OK == inRet)
        inCTOSS_UploadReceipt();

    if (isCheckTerminalMP200() == d_OK)
    {
        if (d_OK != inRet)
            vdCTOSS_DisplayStatus(inRet);
    }

    vdCTOS_TransEndReset();

    return inRet;
}
