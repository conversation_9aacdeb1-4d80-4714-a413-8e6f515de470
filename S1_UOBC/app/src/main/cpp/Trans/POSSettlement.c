/*******************************************************************************

*******************************************************************************/

#include <string.h>
#include <stdio.h>
#include <ctosapi.h>
#include <stdlib.h>
#include <stdarg.h>
#include <typedef.h>
#include <EMVAPLib.h>
#include <EMVLib.h>


#include "..\Includes\POSTypedef.h"

#include "../Accum/Accum.h"
#include "..\Includes\POSMain.h"
#include "..\Includes\POSTrans.h"
#include "..\Includes\POSHost.h"
#include "..\Includes\POSSale.h"
#include "..\Includes\POSbatch.h"
#include "..\ui\Display.h"
#include "..\Includes\V5IsoFunc.h"
#include "..\print\Print.h"
#include "..\Includes\POSHost.h"
#include "..\Comm\V5Comm.h"
#include "..\Includes\POSSetting.h"
#include "..\Includes\MultiApLib.h"
#include "..\Aptrans\MultiAptrans.h"
#include "..\FileModule\myFileFunc.h"
#include "..\Database\DatabaseFunc.h"
#include "../Debug/Debug.h"
#include "..\Includes\Wub_lib.h"
#include "..\Includes\myEZLib.h"
#include "..\TMS\TMS.h"
#include "../Batch/POSbatch.h"
#include "../Includes/POSSetting.h"

BOOL g_fSettleOK = FALSE;
BOOL fECRSelectAll;

void vdECRBuildSettlementTotal(void);
int inGetAcqrHostIndex(int Index);

int inCTOS_SettlementFlowProcess(void)
{
    int inRet = d_NO;
    int key = 0 ;

	int inAcqIdx1=0;
	int inAcqIdx2=0;

	inAcqIdx1=inGetAcqrHostIndex(1);
	inAcqIdx2=inGetAcqrHostIndex(2);
		
    vdCTOS_SetTransType(SETTLE);
    
    //display title
    vdDispTransTitle(SETTLE);

    vdDebug_LogPrintf("inCTOS_SettlementFlowProcess");
	
    inRet = inCTOS_GetTxnPassword();
    if(d_OK != inRet)
        return inRet;

	if(strTCT.fECR == TRUE && fGetECRTransactionFlg() == TRUE)
	{
		vdDebug_LogPrintf("srTransRec.szHostLabel=[%s]", srTransRec.szHostLabel);
		if(memcmp(srTransRec.szHostLabel, "ALL", 3) == 0)
		{
			fECRSelectAll=TRUE;
		    key=1;
		}
		else
			key=2;
	}
    else
        key = inCTOS_SelectAllHostOrOneHost("Settle", "Settle all", "Settle one host");
	
    if(key == 1)
    {
        //settle all

        inRet = inCTOS_SettleAllHostsEx();

		//inRet = inMultiAP_ECRSendSuccessResponse();

        return d_OK;
    }
    else if(key <= 0)
    {
        return d_OK;
    }
    else{
        //settle a host
    }
    inRet = inCTOS_SettlementSelectAndLoadHost();
    if(d_OK != inRet)
        return inRet;

    if (inMultiAP_CheckMainAPStatus() == d_OK)
    {
        inRet = inCTOS_MultiAPSaveData(d_IPC_CMD_SETTLEMENT);
        if(d_OK != inRet)
            return inRet;
    }
    else
    {
        if (0 == fGetAppRunBySelf() && inMultiAP_CheckSubAPStatus() == d_OK)
        {
            inRet = inCTOS_MultiAPGetData();
            if(d_OK != inRet)
                return inRet;

            inRet = inCTOS_MultiAPReloadHost();
            if(d_OK != inRet)
                return inRet;
        }
		else
	    {
	        inRet = inCTOS_MultiAPSaveData(d_IPC_CMD_SETTLEMENT);
	        if(d_OK != inRet)
	            return inRet;
	    }
    }


    inRet = inCTOS_CheckAndSelectMutipleMID();
    if(d_OK != inRet)
        return inRet;

	inRet = inCTOS_ChkBatchEmpty();
    if(d_OK != inRet)
        return inRet;

	inRet = inCTOS_DisplayBatchTotalEx();
    if(d_OK != inRet)
        return inRet;

	vdECRBuildSettlementTotal();

    inRet = inCTOS_PreConnect();
    if(d_OK != inRet)
        return inRet;

    inRet = inBuildAndSendIsoData();
    if(d_OK != inRet)
    {
		ushCTOS_printErrorReceipt();
		return inRet;
    }

    if (strTCT.fECR)
    {
        if(srTransRec.HDTid == inAcqIdx1)
            put_env_int("ACQ01_SETTLED_APPROVED", 1);
        else if(srTransRec.HDTid == inAcqIdx2)
            put_env_int("ACQ02_SETTLED_APPROVED", 1);
    }
	
    inRet = inMultiAP_ECRSendSuccessResponse();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_PrintSettleReport();
    if(d_OK != inRet)
        return inRet; 

    inRet = inCTOS_SettlementClearBathAndAccum();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_inDisconnect();
    if(d_OK != inRet)
        return inRet;
    else
        vdSetErrorMessage("");
    
    

    return ST_SUCCESS;
}

int inCTOS_SETTLEMENT(void)
{
    int inRet = d_NO;
    
    CTOS_LCDTClearDisplay();
    
    vdCTOS_TxnsBeginInit();
    
    inRet = inCTOS_SettlementFlowProcess();
	
	if(d_OK == inRet)
	    inCTOSS_UploadReceipt();

    //if(d_OK == inRet)
//        inCTOSS_SettlementCheckTMSDownloadRequest();
    vdCTOS_TransEndReset();

 /*   if(strCTMS.fTMSCallAfterSettle)
        inCTOS_CTMSUPDATE();*/
    return inRet;
}



int inCTOS_SettleAMerchant(void)
{
	int inRet = d_NO;
	char szDisplayMsg[50];

	int inAcqIdx1=0;
	int inAcqIdx2=0;

	inAcqIdx1=inGetAcqrHostIndex(1);
	inAcqIdx2=inGetAcqrHostIndex(2);

	memset(szDisplayMsg,0x00,sizeof(szDisplayMsg));
	sprintf(szDisplayMsg,"%s",strMMT[0].szMerchantName);
	CTOS_LCDTPrintXY(1, 8, "                   ");
	CTOS_LCDTPrintXY(1, 8, szDisplayMsg);
	vdDebug_LogPrintf("inCTOS_SettleAMerchant--hostname=[%s]-merchant=[%s]--",strHDT.szHostLabel,strMMT[0].szMerchantName);
	inRet = inCTOS_ChkBatchEmpty();
    if(d_OK != inRet)
    {
//		if (strTCT.TaxiMode)
	    	g_fSettleOK = TRUE;	// no batch != settle fails
        return inRet;
    }

	vdECRBuildSettlementTotal();
	
    inRet = inCTOS_PreConnect();
    if(d_OK != inRet)
        return inRet;

    inRet = inBuildAndSendIsoData();
    if(d_OK != inRet)
    {
        ushCTOS_printErrorReceipt();
        return inRet;
    }

    if (strTCT.fECR)
    {
        if(srTransRec.HDTid == inAcqIdx1)
            put_env_int("ACQ01_SETTLED_APPROVED", 1);
        else if(srTransRec.HDTid == inAcqIdx2)
            put_env_int("ACQ02_SETTLED_APPROVED", 1);
    }

//	if (strTCT.TaxiMode)
		g_fSettleOK = TRUE;

    inRet = inCTOS_PrintSettleReport();
    if(d_OK != inRet)
        return inRet; 

    inRet = inCTOS_SettlementClearBathAndAccum();
    if(d_OK != inRet)
        return inRet;

	// RSA update EFTSec working key
    //inRet = inCTOSS_TWKRSAFlow();
    //if(d_OK != inRet)
       // return inRet;

    inRet = inCTOS_inDisconnect();
    if(d_OK != inRet)
        return inRet;
    else
        vdSetErrorMessage("");

	inCTOSS_UploadReceipt();
	
	return ST_SUCCESS;
}



int inCTOS_SettleAHost(int *settleSucc)
{
	int inRet = d_NO;
	int inNumOfMit = 0,inNum;
	char szErrMsg[30+1];

	memset(szErrMsg,0x00,sizeof(szErrMsg));
	sprintf(szErrMsg,"SETTLE %s",strHDT.szHostLabel);
	CTOS_LCDTPrintXY(1, 8, "                   ");
	CTOS_LCDTPrintXY(1, 8, szErrMsg);
	inMMTReadNumofRecords(strHDT.inHostIndex,&inNumOfMit);
	vdDebug_LogPrintf("inNumOfMit=[%d]-----",inNumOfMit);
	for(inNum =0 ;inNum < inNumOfMit; inNum++)
	{
		memcpy(&strMMT[0],&strMMT[inNum],sizeof(STRUCT_MMT));
		srTransRec.MITid = strMMT[0].MITid;
	    strcpy(srTransRec.szTID, strMMT[0].szTID);
	    strcpy(srTransRec.szMID, strMMT[0].szMID);
	    memcpy(srTransRec.szBatchNo, strMMT[0].szBatchNo, 4);
	    strcpy(srTransRec.szHostLabel, strHDT.szHostLabel);
		
//		if (strTCT.TaxiMode)
			g_fSettleOK = FALSE;

	    inRet = inCTOS_SettleAMerchant();
	    if(g_fSettleOK != TRUE) {
            *settleSucc = 0;
	    } else{
            *settleSucc = 1;
	    }
		if (d_OK != inRet)
		{
			inCTOS_inDisconnect();
			memset(szErrMsg,0x00,sizeof(szErrMsg));
		    if (inGetErrorMessage(szErrMsg) > 0)
		    {
//		        vdDisplayErrorMsg(1, 8, szErrMsg);
                vdDisplayErrorMsgEx(1, 8, szErrMsg);
		    }
			vdSetErrorMessage("");

			if (1 == strTCT.TaxiMode && g_fSettleOK == FALSE)
				vdTaxi_PrintSettleErrorStatus();
		}
    }

	return ST_SUCCESS;
}

#ifdef HOST_ONE_BY_ONE
int inCTOS_SettleAllHosts(void)
{
    int inRet = d_NO;
	int inNumOfHost = 0,inNum;
	char szBcd[INVOICE_BCD_SIZE+1];
	char szErrMsg[30+1];

    vdCTOS_SetTransType(SETTLE);
    
    //display title
    vdDispTransTitle(SETTLE);

    inRet = inCTOS_GetTxnPassword();
    if(d_OK != inRet)
        return inRet;

	if (inMultiAP_CheckSubAPStatus() != d_OK)//only 1 APP or main APP
	{
	    inNumOfHost = inHDTNumRecord();
		vdDebug_LogPrintf("inNumOfHost=[%d]-----",inNumOfHost);
		for(inNum =1 ;inNum <= inNumOfHost; inNum++)
		{
			if(inHDTRead(inNum) == d_OK)
			{
				inCPTRead(inNum);
				srTransRec.HDTid = inNum;
        		strcpy(srTransRec.szHostLabel, strHDT.szHostLabel);
				memset(szBcd, 0x00, sizeof(szBcd));
			    memcpy(szBcd, strHDT.szTraceNo, INVOICE_BCD_SIZE);    
			    inBcdAddOne(szBcd, strHDT.szTraceNo, INVOICE_BCD_SIZE);
			    srTransRec.ulTraceNum = wub_bcd_2_long(strHDT.szTraceNo,3);
				
				if (inMultiAP_CheckMainAPStatus() == d_OK)
				{
					//multi AP
			        inRet = inCTOS_MultiAPSaveData(d_IPC_CMD_SETTLE_ALL);
			        if (d_OK == inRet)//Current AP
					{
						inRet = inCTOS_SettleAHost();	
					}
					memset(szErrMsg,0x00,sizeof(szErrMsg));
				    if (inGetErrorMessage(szErrMsg) > 0)
				    {
				        vdDisplayErrorMsg(1, 8, szErrMsg);
				    }
					vdSetErrorMessage("");
					
				}
				else
				{
					// only one AP
					inRet = inCTOS_SettleAHost();
		                return inRet;
				}
			}
		}
	}
	else// Sub APP
    {
        inRet = inCTOS_MultiAPGetData();
        if(d_OK != inRet)
            return inRet;

        inRet = inCTOS_MultiAPReloadHost();
        if(d_OK != inRet)
            return inRet;

		inRet = inCTOS_SettleAHost();
            return inRet;
    }

    return ST_SUCCESS;
}
#endif
int inCTOS_SettleAllHosts(void)
{
    int inRet = d_NO;
    int inNumOfHost = 0,inNum;
    char szBcd[INVOICE_BCD_SIZE+1];
    char szErrMsg[30+1];
    char szAPName[100];
    int inAPPID;
    int SettleSucc = 0;
    BYTE fPrintMultiCurrencyReport = 1;
    memset(szAPName,0x00,sizeof(szAPName));
    inMultiAP_CurrentAPNamePID(szAPName, &inAPPID);

    inNumOfHost = inHDTNumRecord();
    vdDebug_LogPrintf("inNumOfHost=[%d]-----",inNumOfHost);
    for(inNum =1 ;inNum <= inNumOfHost; inNum++)
    {
        if(inHDTRead(inNum) == d_OK)
        {
            vdDebug_LogPrintf("inCTOS_SettleAllHosts szAPName=[%s]-[%s]----inNum[%d]",szAPName,strHDT.szAPName,inNum);
            if (strcmp(szAPName, strHDT.szAPName)!=0)
            {
                continue;
            }
            if (memcmp(strHDT.szHostLabel, "EFTSEC", 6) == 0)
            {
                continue;
            }

            inCPTRead(inNum);
            srTransRec.HDTid = inNum;
            strcpy(srTransRec.szHostLabel, strHDT.szHostLabel);
            memset(szBcd, 0x00, sizeof(szBcd));
            memcpy(szBcd, strHDT.szTraceNo, INVOICE_BCD_SIZE);
            inBcdAddOne(szBcd, strHDT.szTraceNo, INVOICE_BCD_SIZE);
            srTransRec.ulTraceNum = wub_bcd_2_long(strHDT.szTraceNo,3);

            // only one AP
            usCTOSS_ClearAllShowTittle("SETTLE ALL");
            inCTOS_SettleAHost(&SettleSucc);
            if(SettleSucc == FALSE)
                fPrintMultiCurrencyReport = 0;
        }
    }

    //after settle all ok
    if(fPrintMultiCurrencyReport)  {
        //vdCTOS_PrintMultiCurrencyReport();
    }

    vdDebug_LogPrintf("end inCTOS_SettleAllHosts-----");
    return ST_SUCCESS;
}

int inCTOS_SettleAllHostsEx(void)
{
    int inRet = d_NO;
	int inNumOfHost = 0,inNum;
	char szBcd[INVOICE_BCD_SIZE+1];
	char szErrMsg[30+1];
	char szAPName[100];
	int inAPPID;

    int settleSucc = 0;
    BYTE fPrintMultiCurrencyReport = 1;

	memset(szAPName,0x00,sizeof(szAPName));
	inMultiAP_CurrentAPNamePID(szAPName, &inAPPID);

    inNumOfHost = inHDTNumRecord();
	vdDebug_LogPrintf("inNumOfHost=[%d]-----",inNumOfHost);

	// clear MultiCurrencyAccum 20200915 start
	if(fPrintMultiCurrencyReport)
	{
		memset(&gStMultiCurrencyAccum, 0x00, sizeof(ACCUM_REC));
		inRet = inCTOS_SaveMultiCurrencyAccumTotal(&gStMultiCurrencyAccum);
		vdDebug_LogPrintf("inCTOS_SaveMultiCurrencyAccumTotal=[%d]", inRet);
	}
	// clear MultiCurrencyAccum 20200915 end	
	
	for(inNum =1 ;inNum <= inNumOfHost; inNum++)
	{
		if(inHDTRead(inNum) == d_OK)
		{
			vdDebug_LogPrintf("inCTOS_SettleAllHosts szAPName=[%s]-[%s]----inNum[%d]",szAPName,strHDT.szAPName,inNum);
			if (strcmp(szAPName, strHDT.szAPName)!=0)
			{
				continue;
			}
			if ((memcmp(strHDT.szHostLabel, "EFTSEC", 6) == 0) || (memcmp(strHDT.szHostLabel, "GB-DCC", 6) == 0))
			{
				continue;
			}

			inCPTRead(inNum);
			srTransRec.HDTid = inNum;
    		strcpy(srTransRec.szHostLabel, strHDT.szHostLabel);
			memset(szBcd, 0x00, sizeof(szBcd));
		    memcpy(szBcd, strHDT.szTraceNo, INVOICE_BCD_SIZE);
		    //TODO: need to do here? already increase inside inBuildAndSendIsoData
//		    inBcdAddOne(szBcd, strHDT.szTraceNo, INVOICE_BCD_SIZE);
		    srTransRec.ulTraceNum = wub_bcd_2_long(strHDT.szTraceNo,3);
			
			// only one AP
            usCTOSS_ClearAllShowTittle("SETTLE ALL");
			inCTOS_SettleAHost(&settleSucc);
		}
	}

	inMultiAP_ECRSendSuccessResponse();
	
	//after settle all ok
	if(fPrintMultiCurrencyReport) {
		inCTOS_PrintSettleReport();
        if(strDCC.fDccEnable)
            vdCTOS_PrintMultiCurrencyReport(&gStMultiCurrencyAccum);
	}

	vdDebug_LogPrintf("end inCTOS_SettleAllHosts-----");
    return ST_SUCCESS;
}


int inCTOS_SettleAllOperation(void)
{
    int inRet = d_NO;
	int inNumOfHost = 0,inNum;
	char szBcd[INVOICE_BCD_SIZE+1];
	char szErrMsg[30+1];
	char szAPName[100];
	int inAPPID;

	int inResult = ST_SUCCESS; //Auto-settlement: return result if a settlement failed or not -- jzg

    vdCTOS_SetTransType(SETTLE);
    
    //display title
    vdDispTransTitle(SETTLE);

    inRet = inCTOS_GetTxnPassword();
    if(d_OK != inRet)
        return inRet;

	if (inMultiAP_CheckMainAPStatus() == d_OK)
	{
		inResult = inCTOS_SettleAllHosts();
		inCTOS_MultiAPALLAppEventID(d_IPC_CMD_SETTLE_ALL);
	}
	else
	{
		inCTOS_SettleAllHosts();
	}

    return ST_SUCCESS;
}



int inCTOS_SETTLE_ALL(void)
{
    int inRet = d_NO;
    
    CTOS_LCDTClearDisplay();
    
    vdCTOS_TxnsBeginInit();
    
    inRet = inCTOS_SettleAllOperation();
	vdDebug_LogPrintf("end inCTOS_SettleAllOperation-----inRet[%d]",inRet);
//    if(d_OK == inRet)
//        inCTOSS_SettlementCheckTMSDownloadRequest();

    vdCTOS_TransEndReset();

    return inRet;
}


int inCTOS_BATCH_TOTAL_Process(void)
{
    int inRet;

    vdCTOS_SetTransType(BATCH_TOTAL);
    
    //display title
    vdDispTransTitle(BATCH_TOTAL);
    
    inRet = inCTOS_GetTxnPassword();
    if(d_OK != inRet)
       return inRet;

    inRet = inCTOS_SelectHostSetting();
    if (inRet == -1)
        return -1;

    if (inMultiAP_CheckMainAPStatus() == d_OK)
    {
        inRet = inCTOS_MultiAPSaveData(d_IPC_CMD_BATCH_TOTAL);
        if(d_OK != inRet)
            return inRet;
    }
    else
    {
		if (0 == fGetAppRunBySelf() && inMultiAP_CheckSubAPStatus() == d_OK)
        {
            inRet = inCTOS_MultiAPGetData();
            if(d_OK != inRet)
                return inRet;

            inRet = inCTOS_MultiAPReloadHost();
            if(d_OK != inRet)
                return inRet;
        }
		else
	    {
	        inRet = inCTOS_MultiAPSaveData(d_IPC_CMD_BATCH_TOTAL);
	        if(d_OK != inRet)
	            return inRet;
	    }
    }

    inRet = inCTOS_CheckAndSelectMutipleMID();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_ChkBatchEmpty();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_DisplayBatchTotal();
    if(d_OK != inRet)
        return inRet;
	
    inRet = inMultiAP_ECRSendSuccessResponse();
    if(d_OK != inRet)
        return inRet;

    vdSetErrorMessage("");

    return d_OK;
    
}

int inCTOS_BATCH_REVIEW_Process(void)
{
    int inRet;

    vdCTOS_SetTransType(BATCH_REVIEW);
    
    //display title
    vdDispTransTitle(BATCH_REVIEW);

    inRet = inCTOS_GetTxnPassword();
    if(d_OK != inRet)
       return inRet;

    inRet = inCTOS_SelectHostSetting();
    if (inRet == -1)
        return -1;

    if (inMultiAP_CheckMainAPStatus() == d_OK)
    {
        inRet = inCTOS_MultiAPSaveData(d_IPC_CMD_BATCH_REVIEW);
        if(d_OK != inRet)
            return inRet;
    }
    else
    {
		if (0 == fGetAppRunBySelf() && inMultiAP_CheckSubAPStatus() == d_OK)
        {
            inRet = inCTOS_MultiAPGetData();
            if(d_OK != inRet)
                return inRet;

            inRet = inCTOS_MultiAPReloadHost();
            if(d_OK != inRet)
                return inRet;
        }
		else
	    {
	        inRet = inCTOS_MultiAPSaveData(d_IPC_CMD_BATCH_REVIEW);
	        if(d_OK != inRet)
	            return inRet;
	    }
    }

    inRet = inCTOS_CheckAndSelectMutipleMID();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_ChkBatchEmpty();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_BatchReviewFlow();    
    if(d_OK != inRet)
        return inRet;

    vdSetErrorMessage("");
    
    return d_OK;
    
}

int inCTOS_BATCH_TOTAL(void)
{
    int inRet;
    
    CTOS_LCDTClearDisplay();
    
    vdCTOS_TxnsBeginInit(); 

    vdDispTransTitle(BATCH_TOTAL);

    inCTOS_BATCH_TOTAL_Process();
    
    vdCTOS_TransEndReset();

    return d_OK;
    
}

int inCTOS_BATCH_REVIEW(void)
{
    int inRet;
    
    CTOS_LCDTClearDisplay();
    
    vdCTOS_TxnsBeginInit(); 

    vdDispTransTitle(BATCH_REVIEW);
    
    inCTOS_BATCH_REVIEW_Process();
    
    vdCTOS_TransEndReset();
    
    return d_OK;
    
}

int inCTOS_SelectAllHostOrOneHost(char *pHeader, char *pMenu1, char *pMenu2)
{
    BYTE key;
    BYTE bHeaderAttr = 0x01+0x04, iCol = 1;
    char szHeaderString[50];
    char szChoiceMsg[200 + 1];
    BYTE  x = 1;
    memset(szChoiceMsg, 0, sizeof(szChoiceMsg));
    strcpy(szHeaderString, pHeader);

    memset(szChoiceMsg, 0, sizeof(szChoiceMsg));
    strcat((char *)szChoiceMsg, (char *)pMenu1);
    strcat((char *)szChoiceMsg, (char *)" \n");
    strcat((char *)szChoiceMsg, (char *)pMenu2);
    strcat((char *)szChoiceMsg, (char *)" \n");

    key = MenuDisplay(szHeaderString, strlen(szHeaderString), bHeaderAttr, iCol, x, szChoiceMsg, TRUE);
    vdDebug_LogPrintf("inCTOS_SelectAllHostOrOneHost key[%d]",key);
    if (key == 0xFF)
    {
        //vdDisplayErrorMsg(1, 8, "WRONG INPUT!!!");
        return -1;
    }
    if(key > 0)
    {
        if(key == 1)
        {
            //settle all
        }
        else if (key == 2)
        {
            //settle a host
        }
        else
        {
            key = 0;
        }
    }
    return key;
}

void vdECRBuildSettlementTotal(void)
{
    int inTranCardType=0, inResult;
	ACCUM_REC srAccumRec;

	char szECRTemplate[15+1];
	char temp[128+1];

    int inAcqIdx1=0;
	int inAcqIdx2=0;

	inAcqIdx1=inGetAcqrHostIndex(1);
	inAcqIdx2=inGetAcqrHostIndex(2);
	
    vdDebug_LogPrintf("vdECRBuildSettlementTotal");
	
    memset(&srAccumRec, 0x00, sizeof (ACCUM_REC));
    if ((inResult = inCTOS_ReadAccumTotal(&srAccumRec)) == ST_ERROR) {
        vdDebug_LogPrintf("[vdUpdateAmountTotal]---Read Total Rec. error");
        return;
    }

    inTranCardType=0;
		
    vdDebug_LogPrintf("[strTCT.fECR:%d, fGetECRTransactionFlg():%d, inTranCardType:%d", strTCT.fECR, fGetECRTransactionFlg(), inTranCardType);
	if(strTCT.fECR == TRUE && fGetECRTransactionFlg() == TRUE)
	{
		memset(temp, 0x00, sizeof(temp));
									
		/* SALE */
		memset(szECRTemplate, 0x00, sizeof (szECRTemplate));
		sprintf(szECRTemplate, "%03d", (srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usSaleCount));
		strcpy(temp, szECRTemplate);
		memset(szECRTemplate, 0x00, sizeof (szECRTemplate));
		sprintf(szECRTemplate, "%012.0f", (srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount));
		strcat(temp, szECRTemplate);

		//vdDebug_PrintOnPaper("1-[%s]", temp);
					
		/* VOID */
		//memset(buff, 0x00, sizeof(buff));
		memset(szECRTemplate, 0x00, sizeof (szECRTemplate));
		sprintf(szECRTemplate, "%03d", (srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usVoidSaleCount));
		strcat(temp, szECRTemplate);
		memset(szECRTemplate, 0x00, sizeof (szECRTemplate));
		sprintf(szECRTemplate, "%012.0f", (srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulVoidSaleTotalAmount));
		strcat(temp, szECRTemplate);
				
		/* REFUND */
		memset(szECRTemplate, 0x00, sizeof (szECRTemplate));
		sprintf(szECRTemplate, "%03d", (srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usRefundCount));
		strcat(temp, szECRTemplate);
		memset(szECRTemplate, 0x00, sizeof (szECRTemplate));
		sprintf(szECRTemplate, "%012.0f", (srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulRefundTotalAmount));
		//vdDebug_PrintOnPaper("refundamt-[%s]", szECRTemplate);
		strcat(temp, szECRTemplate);

		//vdDebug_PrintOnPaper("2-[%s]", temp);
		strcat(temp, srTransRec.szTID); /* +8 */
		strcat(temp, srTransRec.szMID); /* +15 */

		memset(szECRTemplate, 0x00, sizeof (szECRTemplate));
		wub_hex_2_str(srTransRec.szBatchNo,szECRTemplate,3);
		strcat(temp, szECRTemplate);

		vdDebug_LogPrintf("vdECRBuildSettlementTotal:%s", temp);

		/*
		46
		+8 - TID
		+15 - MID
		+6 - Batch Number
		*/
#if 0		
		switch(srTransRec.HDTid)
		{	
			case HOST_UOB_VMJ:
				put_env_int("ACQ01_SETTLED", 1);
				put_env_int("ACQ01_SETTLED_APPROVED", 0);
				put_env("ACQ01_SETTLED_DATA", temp, strlen(temp)); /*46+8+15+6*/
				break;
				
			case HOST_AMEX:  
				put_env_int("ACQ02_SETTLED", 1);
				put_env_int("ACQ02_SETTLED_APPROVED", 0);
				put_env("ACQ01_SETTLED_DATA", temp, strlen(temp)); /*46+8+15+6*/
				break;				
		}
#endif
		if(srTransRec.HDTid == inAcqIdx1)
		{
			put_env_int("ACQ01_SETTLED", 1);
			put_env_int("ACQ01_SETTLED_APPROVED", 0);
			put_env("ACQ01_SETTLED_DATA", temp, strlen(temp)); /*46+8+15+6*/
		}
		else if(srTransRec.HDTid == inAcqIdx2)
		{
			put_env_int("ACQ02_SETTLED", 1);
			put_env_int("ACQ02_SETTLED_APPROVED", 0);
			put_env("ACQ01_SETTLED_DATA", temp, strlen(temp)); /*46+8+15+6*/
		}
		
	}
	//end ECR data
}

int inGetAcqrHostIndex(int Index)
{
	char szAcquirerIndex[24+1];
    int inAcqIdx=0;
	
	memset(szAcquirerIndex, 0, sizeof(szAcquirerIndex));
	sprintf(szAcquirerIndex, "ACQ%02d_HOST_IDX", Index); /*ex. ACQ01_HOST_IDX*/
    inAcqIdx=get_env_int(szAcquirerIndex);
 
	vdDebug_LogPrintf("szAcquirerIndex: %s, inAcqIdx: %d",szAcquirerIndex, inAcqIdx);

    return inAcqIdx;	
}

