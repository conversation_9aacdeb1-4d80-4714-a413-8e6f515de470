/*******************************************************************************

*******************************************************************************/

#include <string.h>
#include <stdio.h>
#include <stdlib.h>
#include <ctosapi.h>
#include <EMVAPLib.h>
#include <EMVLib.h>
#include <emv_cl.h>
#include <vwdleapi.h>

#include <sys/stat.h>   
#include "..\Includes\POSTypedef.h"
#include "..\FileModule\myFileFunc.h"

#include "..\Includes\msg.h"
#include "..\Includes\CTOSInput.h"
#include "..\ui\display.h"

#include "..\Debug\Debug.h"

#include "..\Includes\CTOSInput.h"

#include "..\comm\v5Comm.h"
#include "..\Accum\Accum.h"
#include "..\DataBase\DataBaseFunc.h"
#include "..\POWRFAIL\POSPOWRFAIL.h"

#include "..\Includes\POSMain.h"
#include "..\Includes\POSTrans.h"
#include "..\Includes\POSHost.h"
#include "..\Includes\POSSale.h"
#include "..\Comm\V5Comm.h"
#include "..\debug\debug.h"
#include "..\Includes\Wub_lib.h"
#include "..\Includes\CardUtil.h"
#include "..\Debug\Debug.h"
#include "..\Database\DatabaseFunc.h"
#include "..\Includes\myEZLib.h"
#include "..\ApTrans\MultiShareEMV.h"
#include "..\Includes\MultiApLib.h"
#include "..\Includes\V5IsoFunc.h"
#include "..\Ctls\POSCtls.h"
#include "..\Ctls\PosWave.h"
#include "..\Aptrans\MultiAptrans.h"
#include "..\print\print.h"
#include "..\pinpad\Pinpad.h"
#include "..\Includes\POSSetting.h"
#include "..\Htle\htlesrc.h"
#include "..\External\External.h"
#include "../Includes/POSTypedef.h"

// patrck fix code 20141208
extern EMVCL_RC_DATA_ANALYZE stRCDataAnalyze;
extern char card_holder_pin[16+1];

static BOOL fGetCardNO = 0;
int inFallbackToMSR = FAIL;
static char szBaseAmount[6+1];
int ginLoadAllTxnTable = 0;
BOOL fECRTxnFlg = 0;

extern int ing_KeyPressed;
#define     AMEX_POP_FIELD44_FOR_CER

int FlagAmexField44 = 0;
extern BOOL fECRSelectAll;

void vdSetECRTransactionFlg(BOOL flg)
{
    int flag = flg;
	put_env_int("ECRTRANS",flag);
    fECRTxnFlg = flg;	
}

BOOL fGetECRTransactionFlg(void)
{
	fECRTxnFlg = get_env_int("ECRTRANS");
    return fECRTxnFlg;
}

BOOL fGetECRTransactionFlgEx(void)
{
    return fECRTxnFlg;
}


extern int isdigit ( int c );

/*
USHORT shCTOS_GetNum(IN  USHORT usY, IN  USHORT usLeftRight, OUT BYTE *baBuf, OUT  USHORT *usStrLen, USHORT usMinLen, USHORT usMaxLen, USHORT usByPassAllow, USHORT usTimeOutMS)
{
    
    BYTE    bDisplayStr[MAX_CHAR_PER_LINE+1];
    BYTE    bKey = 0x00;
    BYTE    bInputStrData[128];
    USHORT  usInputStrLen;

    usInputStrLen = 0;
    memset(bInputStrData, 0x00, sizeof(bInputStrData));
    
    if(usTimeOutMS > 0)
        CTOS_TimeOutSet (TIMER_ID_1 , usTimeOutMS);

    vdDebug_LogPrintf("start [%d] data[%s]", strlen(baBuf), baBuf);
    if(strlen(baBuf) > 0 )
    {
        memset(bDisplayStr, 0x00, sizeof(bDisplayStr));
        memset(bDisplayStr, 0x20, usMaxLen*2);
        usInputStrLen = strlen(baBuf);
        strcpy(bInputStrData, baBuf);
        if(0x01 == usLeftRight)
        {
            strcpy(&bDisplayStr[(usMaxLen-strlen(bInputStrData))*2], bInputStrData);
            //CTOS_LCDTPrintXY(MAX_CHAR_PER_LINE - usMaxLen*2, usY, bDisplayStr);
			CTOS_LCDTPrintAligned(usY,bDisplayStr,d_LCD_ALIGNRIGHT);
        }
        else
        {
            memcpy(bDisplayStr, bInputStrData, strlen(bInputStrData));
            //CTOS_LCDTPrintXY(1, usY, bDisplayStr);
			CTOS_LCDTPrintAligned(usY,bDisplayStr,d_LCD_ALIGNLEFT);
        }
    }
    
    while(1)
    {
//        vduiLightOn(); // patrick remark for flash light always
        if(CTOS_TimeOutCheck(TIMER_ID_1 )  == d_YES)
        {
            *usStrLen = 0;
            baBuf[0] = 0x00;
            return d_KBD_CANCEL ;
        }
        
        CTOS_KBDHit(&bKey);

        switch(bKey)
        {
        case d_KBD_DOT:
            break;
        case d_KBD_CLEAR:
            if (usInputStrLen)
            {
                usInputStrLen--;
                bInputStrData[usInputStrLen] = 0x00;
                
                memset(bDisplayStr, 0x00, sizeof(bDisplayStr));
                memset(bDisplayStr, 0x20, usMaxLen*2);
                if(0x01 == usLeftRight)
                {
                    strcpy(&bDisplayStr[(usMaxLen-strlen(bInputStrData))*2], bInputStrData);
                    //CTOS_LCDTPrintXY(MAX_CHAR_PER_LINE - usMaxLen*2, usY, bDisplayStr);
					CTOS_LCDTPrintAligned(usY,bDisplayStr,d_LCD_ALIGNRIGHT);
                }
                else
                {
                    memcpy(bDisplayStr, bInputStrData, strlen(bInputStrData));
                    //CTOS_LCDTPrintXY(1, usY, bDisplayStr);
					CTOS_LCDTPrintAligned(usY,bDisplayStr,d_LCD_ALIGNLEFT);
                }

				if(1 == fGetCardNO && usInputStrLen == 0)
				{
					*usStrLen = 0;
	            	baBuf[0] = 0x00;
	            	return d_KBD_CANCEL ;
				}
            }
            break;
        case d_KBD_CANCEL:
            *usStrLen = 0;
            baBuf[0] = 0x00;
            return d_KBD_CANCEL ;
        case '1':
        case '2':
        case '3':
        case '4':
        case '5':
        case '6':
        case '7':
        case '8':
        case '9':
        case '0':
            if (usInputStrLen < usMaxLen )
            {
                bInputStrData[usInputStrLen++] = bKey;

                memset(bDisplayStr, 0x00, sizeof(bDisplayStr));
                memset(bDisplayStr, 0x20, usMaxLen*2);
                if(0x01 == usLeftRight)
                {
                    strcpy(&bDisplayStr[(usMaxLen-strlen(bInputStrData))*2], bInputStrData);
                    //CTOS_LCDTPrintXY(MAX_CHAR_PER_LINE - usMaxLen*2, usY, bDisplayStr);
					CTOS_LCDTPrintAligned(usY,bDisplayStr,d_LCD_ALIGNRIGHT);
                }
                else
                {
                    memcpy(bDisplayStr, bInputStrData, strlen(bInputStrData));
                    //CTOS_LCDTPrintXY(1, usY, bDisplayStr);
					CTOS_LCDTPrintAligned(usY,bDisplayStr,d_LCD_ALIGNLEFT);
                }
            }
            break;
        case d_KBD_ENTER:
            if(usInputStrLen >= usMinLen && usInputStrLen <= usMaxLen)
            {   
                *usStrLen = usInputStrLen;
                strcpy(baBuf, bInputStrData);
                return *usStrLen;
            }
            else if(usByPassAllow && 0 == usInputStrLen)
            {   
                *usStrLen = usInputStrLen;
                baBuf[0] = 0x00;
                return *usStrLen;
            }
            break;
        default :
            break;
        }
    }

    return 0;
}
*/

/*
USHORT shCTOS_GetExpDate(IN  USHORT usY, IN  USHORT usLeftRight, OUT BYTE *baBuf, OUT  USHORT *usStrLen, USHORT usMinLen, USHORT usMaxLen, USHORT usTimeOutMS)
{
    
    BYTE    bDisplayStr[MAX_CHAR_PER_LINE+1];
    BYTE    bKey = 0x00;
    BYTE    bInputStrData[20];
    BYTE    bInputFormatStr[20];
    USHORT  usInputStrLen;

    usInputStrLen = 0;
    memset(bInputStrData, 0x00, sizeof(bInputStrData));
    
    if(usTimeOutMS > 0)
        CTOS_TimeOutSet (TIMER_ID_1 , usTimeOutMS);
    
    while(1)
    {
        //vduiLightOn();
        if(CTOS_TimeOutCheck(TIMER_ID_1 )  == d_YES)
        {
            *usStrLen = 0;
            baBuf[0] = 0x00;
            return d_KBD_CANCEL ;
        }
        
        CTOS_KBDHit(&bKey);

        switch(bKey)
        {
        case d_KBD_DOT:
            break;
        case d_KBD_CLEAR:
            if (usInputStrLen)
            {
                usInputStrLen--;
                bInputStrData[usInputStrLen] = 0x00;

                memset(bInputFormatStr, 0x00, sizeof(bInputFormatStr));
                if(usInputStrLen >= 2)
                {
                    memcpy(bInputFormatStr, bInputStrData, 2);
                    strcat(bInputFormatStr, "/");
                    if(usInputStrLen > 2)
                        strcat(bInputFormatStr, &bInputStrData[2]);
                }
                else
                {
                    strcpy(bInputFormatStr, bInputStrData);
                }

                memset(bDisplayStr, 0x00, sizeof(bDisplayStr));
                memset(bDisplayStr, 0x20, (usMaxLen+1)*2);
                
                if(0x01 == usLeftRight)
                {
                    strcpy(&bDisplayStr[(usMaxLen+1-strlen(bInputFormatStr))*2], bInputFormatStr);
                    //CTOS_LCDTPrintXY(MAX_CHAR_PER_LINE - (usMaxLen+1)*2, usY, bDisplayStr);
					CTOS_LCDTPrintAligned(usY,bDisplayStr,d_LCD_ALIGNRIGHT);
                }
                else
                {
                    memcpy(bDisplayStr, bInputFormatStr, strlen(bInputFormatStr));
                    //CTOS_LCDTPrintXY(1, usY, bDisplayStr);
					CTOS_LCDTPrintAligned(usY,bDisplayStr,d_LCD_ALIGNLEFT);
                }
            }
            break;
        case d_KBD_CANCEL:
            *usStrLen = 0;
            baBuf[0] = 0x00;
            return d_KBD_CANCEL ;
        case '1':
        case '2':
        case '3':
        case '4':
        case '5':
        case '6':
        case '7':
        case '8':
        case '9':
        case '0':
            if (usInputStrLen < usMaxLen )
            {
                bInputStrData[usInputStrLen++] = bKey;

                memset(bInputFormatStr, 0x00, sizeof(bInputFormatStr));
                if(usInputStrLen >= 2)
                {
                    memcpy(bInputFormatStr, bInputStrData, 2);
                    strcat(bInputFormatStr, "/");
                    if(usInputStrLen > 2)
                        strcat(bInputFormatStr, &bInputStrData[2]);
                }
                else
                {
                    strcpy(bInputFormatStr, bInputStrData);
                }

                memset(bDisplayStr, 0x00, sizeof(bDisplayStr));
                memset(bDisplayStr, 0x20, (usMaxLen+1)*2);
                if(0x01 == usLeftRight)
                {
                    strcpy(&bDisplayStr[(usMaxLen+1-strlen(bInputFormatStr))*2], bInputFormatStr);
                    //CTOS_LCDTPrintXY(MAX_CHAR_PER_LINE - (usMaxLen+1)*2, usY, bDisplayStr);
					CTOS_LCDTPrintAligned(usY,bDisplayStr,d_LCD_ALIGNRIGHT);
                }
                else
                {
                    memcpy(bDisplayStr, bInputFormatStr, strlen(bInputFormatStr));
                    //CTOS_LCDTPrintXY(1, usY, bDisplayStr);
					CTOS_LCDTPrintAligned(usY,bDisplayStr,d_LCD_ALIGNLEFT);
                }
            }
            break;
        case d_KBD_ENTER:
            if(usInputStrLen >= usMinLen && usInputStrLen <= usMaxLen)
            {   
                *usStrLen = usInputStrLen;
                strcpy(baBuf, bInputStrData);
                return *usStrLen;
            }
            break;
        default :
            break;
        }
    }

    return 0;
}
*/
	
USHORT getExpDate( OUT BYTE *baBuf)
{
    BYTE    szMonth[3];    
    USHORT  usRet;
    USHORT  usLens;
    USHORT  usMinLen = 4;
    USHORT  usMaxLen = 4;
    USHORT usInputLine = 8;
    BYTE fReEnter = 0;
    while(1)
    {
        if(fReEnter)
        {
            usCTOSS_ClearAllExceptTittle();
            setLCDPrint(7, DISPLAY_POSITION_LEFT, "EXPIRY DATE(MM/YY):");
        }
        usRet = shCTOS_GetExpDate(usInputLine, 0x01, baBuf, &usLens, usMinLen, usMaxLen, d_INPUT_TIMEOUT);
        vdDebug_LogPrintf("exp baBuf[%s],usRet[%d]", baBuf, usRet);
        if (usRet == d_KBD_CANCEL )
            return (d_EDM_USER_CANCEL);
        memset(szMonth, 0x00, sizeof(szMonth));
        memcpy(szMonth, baBuf, 2);
        if(atol(szMonth) > 12 || 0 == atol(szMonth) || (strlen(baBuf) != 4))
        {
            baBuf[0] = 0x00;
            clearLine(8);
            vdDisplayErrorMsg(1, 8, "INVALID FORMAT"); 
            clearLine(8);
            fReEnter = 1;
            continue;
        }
        else
        {
            return (d_OK);
        }
    }
}





#if 1
USHORT getPIN(OUT BYTE *baBuf)
{
    USHORT usRet;
    USHORT usLens;
    USHORT usMinLen = 0;
    USHORT usMaxLen = 12;
    USHORT usInputLine = 8;
    
    while(1)
    {
        usRet = shCTOS_GetPin(usInputLine, 0x01, baBuf, &usLens, usMinLen, usMaxLen, 0, d_INPUT_TIMEOUT);
        if (usRet == d_KBD_CANCEL )
            return (d_EDM_USER_CANCEL);
        if (usRet >= usMinLen && usRet <= usMaxLen)
        {
            return (d_OK);       
        }

        baBuf[0] = 0x00;
    }
}
#endif

USHORT getCardNO(OUT BYTE *baBuf)
{
    USHORT usRet;
    USHORT usLens;
    USHORT usMinLen = 14;
    USHORT usMaxLen = 19;
    USHORT usInputLine = 8;
    
    while(1)
    {
        usRet = shCTOS_GetNum(usInputLine, 0x01, baBuf, &usLens, usMinLen, usMaxLen, 0, d_INPUT_TIMEOUT);
        if (usRet == d_KBD_CANCEL )
            return (d_EDM_USER_CANCEL);
        if (usRet >= usMinLen && usRet <= usMaxLen)
        {
            return (d_OK);       
        }

        baBuf[0] = 0x00;
    }
}
    

unsigned char WaitKey(short Sec)
{
    unsigned char c;
    long mlsec;
    
    mlsec=Sec*100;
    BOOL isKey;
    CTOS_TimeOutSet(TIMER_ID_3,mlsec);
    while(1)//loop for time out
    {
        CTOS_KBDInKey(&isKey);
        if (isKey){ //If isKey is TRUE, represent key be pressed //
            
            vduiLightOn();
            CTOS_KBDGet(&c);
            return c;   
        }
        else if (CTOS_TimeOutCheck(TIMER_ID_3) == d_YES)
        {      
            return d_KBD_CANCEL;
        }
    }
}


void vduiApiAmount(unsigned char *ou, unsigned char *ascamt, unsigned char len)
{
    /*~~~~~~~~~~~~~~~~~~~~~~~*/
    unsigned int    ii, jj, tt;
    unsigned char   ch;
    unsigned char   aa;
    unsigned char   buf[3];
    /*~~~~~~~~~~~~~~~~~~~~~~~*/

    jj = 0;
    tt = 0;
    ii = 0;
    
    
    ou[jj++] = strCST.szCurSymbol[0];
    ou[jj++] = strCST.szCurSymbol[1];
    ou[jj++] = strCST.szCurSymbol[2];
    
    for(ii = 0; ii < len; ii++)
    {
        ch = ascamt[ii];
        if((tt == 0) && (ch == 'C'))
        {
            tt = 1;
        }
        else if((tt == 0) && (ch == 'D'))
        {
            tt = 1;
            ou[jj++] = '-';
        }
        else if(ch<0x30 && ch>0x39 )
        {
            break;
        }
    }


    len = ii;
    aa = 0;
    for(ii = tt; (ii + 3) < len; ii++)
    {
        ch = ascamt[ii];
        if((ch == '0') && (aa == 0))
        {
            continue;
        }

        if(ch>0x29 && ch<0x40 )//if(isdigit(ch) /* && (ch !='0') */ )
        {
            aa = 1;
            ou[jj++] = ch;
        }
    }

    tt = ii;
    len = len - ii;
    buf[0] = '0', buf[1] = '0', buf[2] = '0';
    for(ii = 0; ii < len; ii++)
    {
        buf[3 - len + ii] = ascamt[tt++];
    }

    ou[jj++] = buf[0];
    ou[jj++] = '.';
    ou[jj++] = buf[1];
    ou[jj++] = buf[2];
    ou[jj++] = '\0';
}

void vduiApiPoint(unsigned char *ou, unsigned char *ascamt, unsigned char len)
{
    /*~~~~~~~~~~~~~~~~~~~~~~~*/
    unsigned int    ii, jj, tt;
    unsigned char   ch;
    unsigned char   aa;
    unsigned char   buf[3];
    /*~~~~~~~~~~~~~~~~~~~~~~~*/

    jj = 0;
    tt = 0;

    ou[jj++] = 'P';
    ou[jj++] = 'T';
    ou[jj++] = 'S';
    ou[jj++] = ' ';
    for(ii = 0; ii < len; ii++)
    {
        ch = ascamt[ii];
        if((tt == 0) && (ch == 'C'))
        {
            tt = 1;
        }
        else if((tt == 0) && (ch == 'D'))
        {
            tt = 1;
            ou[jj++] = '-';
        }
        else if(ch<0x30 && ch>0x39 )
        {
            break;
        }
    }


    len = ii;
    aa = 0;
    for(ii = tt; (ii + 3) < len; ii++)
    {
        ch = ascamt[ii];
        if((ch == '0') && (aa == 0))
        {
            continue;
        }

        if(ch>0x29 && ch<0x40 )
        {
            aa = 1;
            ou[jj++] = ch;
        }
    }

    tt = ii;
    len = len - ii;
    buf[0] = '0', buf[1] = '0', buf[2] = '0';
    for(ii = 0; ii < len; ii++)
    {
        buf[3 - len + ii] = ascamt[tt++];
    }

    ou[jj++] = buf[0];
    ou[jj++] = '.';
    ou[jj++] = buf[1];
    ou[jj++] = buf[2];
    ou[jj++] = '\0';
}

//mode  1=amount , 2=string, 3=IP  4=password, 5=Point
unsigned char struiApiGetStringSub
(
    unsigned char   *strDisplay,
    short   x,
    short   y,
    unsigned char   *ou,
    unsigned char   mode,
    short   minlen,
    short   maxlen
)
{
    
    unsigned char srDestIP[MAX_CHAR_PER_LINE+1];
    unsigned char amtdis[MAX_CHAR_PER_LINE+1];
    unsigned char c;
    int n;
    int i;
    
    memset(srDestIP,0x00,sizeof(srDestIP));
    n= 0;   

    vduiClearBelow(y);

    if(mode == MODE_AMOUNT)
    {
        vduiClearBelow(8);
        vduiApiAmount(amtdis, srDestIP, n);                     
        //CTOS_LCDTPrintXY(MAX_CHAR_PER_LINE-strlen(amtdis)*2,8,amtdis);
		CTOS_LCDTPrintAligned(8,amtdis,d_LCD_ALIGNRIGHT);
        
    }
    else if(mode == MODE_POINT)
    {
        vduiClearBelow(8);
        vduiApiPoint(amtdis, srDestIP, n);                      
        //CTOS_LCDTPrintXY(MAX_CHAR_PER_LINE-strlen(amtdis)*2,8,amtdis);
		CTOS_LCDTPrintAligned(8,amtdis,d_LCD_ALIGNRIGHT);
        
    }
    
    while(1)
    {
        
        //CTOS_LCDTPrintXY(x,y,strDisplay);
		CTOS_LCDTPrintAligned(y,strDisplay,d_LCD_ALIGNLEFT);
        c=WaitKey(30);//CTOS_KBDGet(&c);
        vduiLightOn();
        
        if (c == d_KBD_ENTER)
        {
            if((n>=minlen) && (n<=maxlen))
            {
                ou[n]=0;
                memcpy(ou,srDestIP,n+1);    
                if(mode==MODE_FOODITEM && atoi(ou)==0)
                {
                    return d_KBD_CANCEL;
                }
                
                if(mode==MODE_AMOUNT && atoi(ou)==0)
                {
                    i = atoi(ou);
                    memset(srDestIP,0x00,sizeof(srDestIP));
                    n=0;
                    vduiWarningSound();
                }
                else
                    return d_KBD_ENTER;
            }
            
        }       
        else if((c == d_KBD_CANCEL) && (n==0))          
        {
            memset(srDestIP,0x00,sizeof(srDestIP));         
            return d_KBD_CANCEL;
        }           

        else
        {
            if (c==d_KBD_CLEAR)
            {   
                if(n>0) 
                {
                    n--;
                    srDestIP[n]='\0';                   
                }       
            }
            else if((c == d_KBD_CANCEL))            
            {
                memset(srDestIP,0x00,sizeof(srDestIP));
                n=0;                
            }   
            else if (((c == d_KBD_DOT) & (mode==MODE_IPADDRESS)) || ((c == d_KBD_DOWN) & (mode==MODE_IPADDRESS)))
            {
                srDestIP[n]='.';
                n++;    
            }
            else if (c == d_KBD_DOT || c == d_KBD_F3  || c == d_KBD_F4 || c == d_KBD_00) 
            {
                ;
            }
            else if(c == d_KBD_UP || c == d_KBD_DOWN)
            {
                return c;
            }
            else if(n<maxlen)
            {
                srDestIP[n]=c;
                n++;    
            }
            else
            {

            }

            if(mode == MODE_AMOUNT)
            {
                vduiClearBelow(8);
                vduiApiAmount(amtdis, srDestIP, n);                     
                //CTOS_LCDTPrintXY(MAX_CHAR_PER_LINE-strlen(amtdis)*2,8,amtdis);
				CTOS_LCDTPrintAligned(8,amtdis,d_LCD_ALIGNRIGHT);
                
            }
            else if(mode == MODE_POINT)
            {
                vduiClearBelow(8);
                vduiApiPoint(amtdis, srDestIP, n);                      
                //CTOS_LCDTPrintXY(MAX_CHAR_PER_LINE-strlen(amtdis)*2,8,amtdis);
				CTOS_LCDTPrintAligned(8,amtdis,d_LCD_ALIGNRIGHT);
                
            }
            else if(mode == MODE_PASSWORD)
            {
                for(i=0;i<n;i++)
                    amtdis[i]='*';
                amtdis[n]=0;
                vduiClearBelow(8);
                //CTOS_LCDTPrintXY(MAX_CHAR_PER_LINE-n*2,8,amtdis);
				CTOS_LCDTPrintAligned(8,amtdis,d_LCD_ALIGNRIGHT);
            }
            else
            {   
                vduiClearBelow(8);
                //CTOS_LCDTPrintXY(MAX_CHAR_PER_LINE-n*2,8,srDestIP);
				CTOS_LCDTPrintAligned(8,srDestIP,d_LCD_ALIGNRIGHT);
            }
        }
        
    }

}

void vdCTOSS_GetMemoryStatus(char *Funname)
{
    ULONG ulUsedDiskSize = 0;
    ULONG ulTotalDiskSize = 0;
    ULONG ulUsedRamSize = 0;
    ULONG ulTotalRamSize = 0;

    ULONG ulAvailableRamSize = 0;
    ULONG ulAvailDiskSize = 0;
    
    UCHAR szUsedDiskSize[50];
    UCHAR szTotalDiskSize[50];
    UCHAR szUsedRamSize[50];
    UCHAR szTotalRamSize[50];
    
    UCHAR szAvailableRamSize[50];
    UCHAR szAvailableDiskSize[50];

    memset(szUsedDiskSize,0,sizeof(szUsedDiskSize));
    memset(szTotalDiskSize,0,sizeof(szTotalDiskSize));
    memset(szUsedRamSize,0,sizeof(szUsedRamSize));
    memset(szTotalRamSize,0,sizeof(szTotalRamSize));
    memset(szAvailableRamSize,0,sizeof(szAvailableRamSize));
    memset(szAvailableDiskSize,0,sizeof(szAvailableDiskSize));
    
    usCTOSS_SystemMemoryStatus( &ulUsedDiskSize , &ulTotalDiskSize, &ulUsedRamSize, &ulTotalRamSize );
    ulAvailableRamSize = ulTotalRamSize - ulUsedRamSize;
    ulAvailDiskSize = ulTotalDiskSize - ulUsedDiskSize;
    
    sprintf(szTotalDiskSize,"%s:%ld","Total disk",ulTotalDiskSize); 
    sprintf(szUsedDiskSize,"%s:%ld","Used   disk",ulUsedDiskSize);
    sprintf(szAvailableDiskSize,"%s:%ld","Avail disk",ulAvailDiskSize);
    
    sprintf(szTotalRamSize,"%s:%ld","Total RAM",ulTotalRamSize);    
    sprintf(szUsedRamSize,"%s:%ld","Used   RAM",ulUsedRamSize);
    sprintf(szAvailableRamSize,"%s:%ld","Avail RAM",ulAvailableRamSize);
    vdDebug_LogPrintf("[%s][%ld],[%ld][%ld][%ld]",Funname,ulUsedDiskSize,ulTotalRamSize,ulUsedRamSize,ulAvailableRamSize);

    CTOS_LCDTClearDisplay();
    
    CTOS_LCDTPrintXY(1, 1, szTotalDiskSize);
    CTOS_LCDTPrintXY(1, 2, szUsedDiskSize);
    CTOS_LCDTPrintXY(1, 3, szAvailableDiskSize);
    
    CTOS_LCDTPrintXY(1, 5, szTotalRamSize);
    CTOS_LCDTPrintXY(1, 6, szUsedRamSize);
    CTOS_LCDTPrintXY(1, 7, szAvailableRamSize);
    WaitKey(60);
    
}

int inCTOSS_CheckMemoryStatus()
{
#define SAFE_LIMIT_SIZE 5000

    ULONG ulUsedDiskSize = 0;
    ULONG ulTotalDiskSize = 0;
    ULONG ulUsedRamSize = 0;
    ULONG ulTotalRamSize = 0;

    ULONG ulAvailableRamSize = 0;
    ULONG ulAvailDiskSize = 0;
    
    UCHAR szUsedDiskSize[50];
    UCHAR szTotalDiskSize[50];
    UCHAR szUsedRamSize[50];
    UCHAR szTotalRamSize[50];
    
    UCHAR szAvailableRamSize[50];
    UCHAR szAvailableDiskSize[50];
    
    if (0 == fGetAppRunBySelf() && inMultiAP_CheckSubAPStatus() == d_OK)
        return d_OK;

    memset(szUsedDiskSize,0,sizeof(szUsedDiskSize));
    memset(szTotalDiskSize,0,sizeof(szTotalDiskSize));
    memset(szUsedRamSize,0,sizeof(szUsedRamSize));
    memset(szTotalRamSize,0,sizeof(szTotalRamSize));
    memset(szAvailableRamSize,0,sizeof(szAvailableRamSize));
    memset(szAvailableDiskSize,0,sizeof(szAvailableDiskSize));
    
    usCTOSS_SystemMemoryStatus( &ulUsedDiskSize , &ulTotalDiskSize, &ulUsedRamSize, &ulTotalRamSize );
    vdDebug_LogPrintf("[%ld],[%ld][%ld][%ld]",ulUsedDiskSize,ulTotalDiskSize,ulUsedRamSize,ulTotalRamSize);
    ulAvailableRamSize = ulTotalRamSize - ulUsedRamSize;
    ulAvailDiskSize = ulTotalDiskSize - ulUsedDiskSize;
    
    sprintf(szTotalDiskSize,"%s:%ld","Total disk",ulTotalDiskSize); 
    sprintf(szUsedDiskSize,"%s:%ld","Used   disk",ulUsedDiskSize);
    sprintf(szAvailableDiskSize,"%s:%ld","Avail disk",ulAvailDiskSize);
    
    sprintf(szTotalRamSize,"%s:%ld","Total RAM",ulTotalRamSize);    
    sprintf(szUsedRamSize,"%s:%ld","Used   RAM",ulUsedRamSize);
    sprintf(szAvailableRamSize,"%s:%ld","Avail RAM",ulAvailableRamSize);
    vdDebug_LogPrintf("ulAvailDiskSize[%ld],ulAvailableRamSize[%ld]",ulAvailDiskSize,ulAvailableRamSize);

    if (ulAvailDiskSize < SAFE_LIMIT_SIZE)
    {
        CTOS_LCDTClearDisplay();
        CTOS_LCDTPrintXY(1, 7, "Settle  soon");
        vdDisplayErrorMsg(1, 8,  "Insufficient Memory");
        return FAIL;
    }

    if (ulAvailableRamSize < SAFE_LIMIT_SIZE)
    {
        CTOS_LCDTClearDisplay();
		if (strTCT.TaxiMode)
        	vdSetErrorMessage("Please Try Again(IR)");
		else
        	vdSetErrorMessage("Insufficient RAM");
        return FAIL;
    }
    
    return d_OK;
    
}

void vdCTOS_SyncHostDateTime()
{
    CTOS_RTC SetRTC;
    char szDate[4+1];
    char szTime[6+1];
    char szBuf[2+1];
    BYTE szDateTime[30+1];

    vdDebug_LogPrintf("saturn vdCTOS_SyncHostDateTime");

    if(srTransRec.byOffline == CN_TRUE)
        return;
    //CTOS_RTCGet(&SetRTC);

    //vdDebug_LogPrintf("sys year[%02x],Date[%02x][%02x]time[%02x][%02x][%02x]",SetRTC.bYear,SetRTC.bMonth,SetRTC.bDay,SetRTC.bHour,SetRTC.bMinute,SetRTC.bSecond);

    //vdDebug_LogPrintf("year[%02X],time[%02x:%02x:%02x]date[%02x][%02x]",SetRTC.bYear,srTransRec.szTime[0],srTransRec.szTime[1],srTransRec.szTime[2],srTransRec.szDate[0],srTransRec.szDate[1]);

    memset(szDate,0,sizeof(szDate));
    memset(szTime,0,sizeof(szTime));

    wub_hex_2_str(srTransRec.szDate, szDate, 2);
    wub_hex_2_str(srTransRec.szTime, szTime, 3);
    vdDebug_LogPrintf("saturn date %s", szDate);
    vdDebug_LogPrintf("saturn time %s", szTime);
    if((strlen(szDate)<=0) ||(strlen(szTime)<=0))
        return;

    memset(szDateTime, 0x00, sizeof(szDateTime));
    strncpy(szDateTime,&szDate[0],2);
    strcat(szDateTime,"|");

    strncat(szDateTime,&szDate[2],2);
    strcat(szDateTime,"|");

//if year is "x", app will get the year setting of the terminal
    strcat(szDateTime,"x|");

    strncat(szDateTime,&szTime[0],2);
    strcat(szDateTime,"|");

    strncat(szDateTime,&szTime[2],2);
    strcat(szDateTime,"|");

    strncat(szDateTime,&szTime[4],2);

    //CTOS_RTCSet(&SetRTC);
    vdDebug_LogPrintf("saturn szDatetime %s", szDateTime);
    usSetDateTime(szDateTime);

    return;
}

int file_exist (char *filename)
{
  struct stat buffer;   
  return (stat (filename, &buffer) == 0);
}

int inCLearTablesStructure(void){

memset(&strCDT,0x00, sizeof(STRUCT_CDT));
memset(&strIIT,0x00, sizeof(STRUCT_IIT));
memset(&strEMVT,0x00, sizeof(STRUCT_EMVT));
memset(&strHDT,0x00, sizeof(STRUCT_HDT));
    return d_OK;
}

void vdCTOS_TxnsBeginInit(void)
{
    BYTE bEntryMode = 0;
	int inRet = d_NO;

//	inCallJAVA_onIdlePause();

    if(d_OK == inCTOS_ValidFirstIdleKey())
        bEntryMode = srTransRec.byEntryMode;
    ginLoadAllTxnTable = 0;
	memset( &srTransRec, 0x00, sizeof(TRANS_DATA_TABLE));
	memset(card_holder_pin,0,sizeof(card_holder_pin));
	memset( &srTransFlexiData, 0x00, sizeof(TRANS_FLEXI_DATA_TABLE));

	memset( &strMMT, 0x00, sizeof(STRUCT_MMT)*10);

	//fix first transaction set fallback time, then quickly do second transaction will allow swipe chip card
	memset(strTCT.szFallbackTime,0x00,sizeof(strTCT.szFallbackTime));
	inFallbackToMSR = FAIL;

	fECRSelectAll=FALSE;
		
//Clear Previous Transaction Data
    inCLearTablesStructure();
    if(0 != bEntryMode)
        srTransRec.byEntryMode = bEntryMode;

    vdSetErrorMessage("");
	vdCTOSS_SetWaveTransType(0);

	if (strTCT.TaxiMode)
		inCSTRead(2);//get currency symbol for display and printing

	vdCTOSS_SetPinByPass(FALSE);

    vdDebug_LogPrintf("clear sign bmp");

    inDeleteFile(DB_SIGN_BMP);
    inDeleteGzipFile(DB_SIGN_BMP_GZ);
    vdDebug_LogPrintf("x.vdCTOS_TxnsBeginInit check if main app");
//add for ECR only MianAP get the ECR command, if Sub AP will cannot error
	//if (inMultiAP_CheckMainAPStatus() == d_OK)
	//if (inMultiAP_CheckSubAPStatus() != d_OK)//only 1 APP or main APP
//    if(1)//To handle ecr indenpendly as sub app

    if (inMultiAP_CheckMainAPStatus() == d_OK)
	{
	    vdDebug_LogPrintf("vdCTOS_TxnsBeginInit check ECR on?[%d]", strTCT.fECR);
	    if (strTCT.fECR) // tct
	    {
	        vdDebug_LogPrintf("vdCTOS_TxnsBeginInit check Database exist");
	    	if (file_exist (DB_MULTIAP))
	    	{
	    	    vdDebug_LogPrintf("vdCTOS_TxnsBeginInit Read ECR Data");
	    		inRet = inCTOS_MultiAPGetData();
                vdSetSendEcrResponseFlg(0);
				memcpy(szBaseAmount, srTransRec.szBaseAmount, 6);	
				if(srTransRec.szStoreID[0] == 'Y')
					srTransRec.fPrintRetrieval=1;
				else
					srTransRec.fPrintRetrieval=0;
				vdDebug_LogPrintf("srTransRec.fPrintRetrieval[%d]", srTransRec.fPrintRetrieval);
                vdDebug_LogPrintf("vdCTOS_TxnsBeginInit Read ECR Data ret[%d]", inRet);
	    		if(d_OK != inRet)
	    			return ;
	    	}
	    }

	} else{
        vdDebug_LogPrintf("x.inMultiAP_CheckSubAPStatus is dOK");
	}
    vdDebug_LogPrintf("vdCTOS_TxnsBeginInit exit");
}

void vdCTOS_TransEndReset(void)
{
    USHORT usTk1Len, usTk2Len, usTk3Len;
    BYTE szTk1Buf[TRACK_I_BYTES], szTk2Buf[TRACK_II_BYTES], szTk3Buf[TRACK_III_BYTES];
    char szErrMsg[30+1];
    int beepCnt = 5;//5
    usTk1Len = TRACK_I_BYTES ;
    usTk2Len = TRACK_II_BYTES ;
    usTk3Len = TRACK_III_BYTES ;
    vdDebug_LogPrintf("vdCTOS_TransEndReset1,ginLoadAllTxnTable[%d],strTCT.fSendReversalImmediately[%d]", ginLoadAllTxnTable, strTCT.fSendReversalImmediately);
    //inMultiAP_ECRSendFailedResponse();

    vdDebug_LogPrintf("vdCTOS_TransEndReset");

    vdDebug_LogPrintf("vdCTOS_TransEndReset 2");
//	vdCTOSS_PrinterStart(100);
//	CTOS_PrinterBufferOutput((BYTE *)"vdCTOS_TransEndReset", 3);
//	vdCTOSS_PrinterEnd();

	// patrick this part need add transaction status UI. So thet can avoid thread running and result incorrect.
//COMFORT NO NEED PRINT, SO REMRK IT
/*
	// patrick for android it is took time get EMV data so either remark this part of code or adds flag
	// patrick to print contactless EMV data 20151019
//    inMultiAP_Database_EMVTransferDataWrite(srTransRec.usChipDataLen, srTransRec.baChipData);
    if(CARD_ENTRY_ICC == srTransRec.byEntryMode)
	{
		usCTOSS_EMV_MultiDataGet(PRINT_EMV_TAGS_LIST, &srTransRec.usChipDataLen, srTransRec.baChipData);
	    inMultiAP_Database_EMVTransferDataWrite(srTransRec.usChipDataLen, srTransRec.baChipData);
	}
	else
		inMultiAP_Database_EMVTransferDataWrite(srTransRec.usChipDataLen, srTransRec.baChipData);
		
    DebugAddHEX("PRINT_EMV_TAGS_LIST",srTransRec.baChipData,srTransRec.usChipDataLen);
	// patrick to print contactless EMV data 20151019
*/
	
    vdDebug_LogPrintf("vdCTOS_TransEndReset ECR?[%d]", fGetECRTransactionFlg());
	//usCTOSS_BackToProgress("TEST");

//	CTOS_LCDTClearDisplay();
   // char szTxnTittleBuf[30] = {0};
   // szGetTransTitle(srTransRec.byTransType, szTxnTittleBuf);
   // vdDebug_LogPrintf("usCTOSS_ClearAllShowTittle");
    //usCTOSS_ClearAllShowTittle(szTxnTittleBuf);
	// patrick fix code 20141209
	//vduiClearBelow(2);

	memset(&stRCDataAnalyze,0x00,sizeof(EMVCL_RC_DATA_ANALYZE));



    //if((strTCT.fSendReversalImmediately) && (ginLoadAllTxnTable == 1))
    //inProcessReversalEx(&srTransRec);

	
    vdDebug_LogPrintf("check vdRemoveCard");
    if(CARD_ENTRY_ICC == srTransRec.byEntryMode)
        vdRemoveCard();
    else if(CARD_ENTRY_EASY_ICC == srTransRec.byEntryMode)
		vdRemoveCard();
    else
        CTOS_MSRRead(szTk1Buf, &usTk1Len, szTk2Buf, &usTk2Len, szTk3Buf, &usTk3Len);

    if(fGetECRTransactionFlg() != TRUE)
    {
        memset( &srTransRec, 0x00, sizeof(TRANS_DATA_TABLE));
    }
    
    CTOS_KBDBufFlush();

    inCTLOS_Updatepowrfail(PFR_IDLE_STATE);
    vdSetErrorMessage("");
	vdCTOSS_SetWaveTransType(0);
	
	ushCTOS_ClearePadSignature();
      //Clear Previous Transaction Data
    inCLearTablesStructure();
	  memset(card_holder_pin,0,sizeof(card_holder_pin));
  	//fECRTxnFlg = 0; fix error not response  problme

	if ((strTCT.byTerminalType%2) == 0)
		vdCTOSS_ClearTouchPanelTest();
    //usCTOSS_RestartActivity();
//	inCallJAVA_onIdleResume();
    return;
}

void vdCTOS_SetTransEntryMode(BYTE bEntryMode)
{
    srTransRec.byEntryMode = bEntryMode;
}

void vdCTOS_SetTransType(BYTE bTxnType)
{
    srTransRec.byTransType = bTxnType;
    inCTLOS_Updatepowrfail(PFR_IDLE_STATE);
}


short shCTOS_SetMagstripCardTrackData(BYTE* baTk1Buf, USHORT usTk1Len, BYTE* baTk2Buf, USHORT usTk2Len, BYTE* baTk3Buf, USHORT usTk3Len) //Invalid card reading fix -- jzg
{
	short shRet = d_OK;

    if (usTk1Len > 0)
    {
		DebugAddHEX("szTrack1Data", baTk1Buf, usTk1Len);	
	    memcpy(srTransRec.szTrack1Data, &baTk1Buf[1], (usTk1Len -3));
    	srTransRec.usTrack1Len = (usTk1Len - 3);// REMOVE %, ? AND LRC
    }
	
    if (usTk2Len > 0)
    {
		DebugAddHEX("szTrack2Data", baTk2Buf, usTk2Len);	
		memcpy(srTransRec.szTrack2Data, &baTk2Buf[1], (usTk2Len - 3));
		srTransRec.usTrack2Len = (usTk2Len - 3);// REMOVE %, ? AND LRC
    }

    if (usTk3Len > 0)
    {
		DebugAddHEX("szTrack3Data", baTk3Buf, usTk3Len);		
		memcpy(srTransRec.szTrack3Data, baTk3Buf, usTk3Len);
		srTransRec.usTrack3Len = usTk3Len;
    }

    vdCTOS_SetTransEntryMode(CARD_ENTRY_MSR);

    //Fix for card without Track 1
    //vdGetCardHolderName(baTk1Buf, usTk1Len);
    if (usTk1Len > 0)
       vdAnalysisTrack1( &baTk1Buf[1], usTk1Len); 
    if (usTk2Len > 0)
    //Fix for card without Track 2
		shRet = shAnalysisTrack2(&baTk2Buf[1], usTk2Len); //Invalid card reading fix -- jzg
	else
	{
		vdDebug_LogPrintf("JEFF::T2 INVALID! [%d]", usTk2Len);
		return INVALID_CARD;
	}
	return shRet;
}

void vdCTOS_ResetMagstripCardData(void)
{
    memset(srTransRec.szTrack1Data, 0x00, sizeof(srTransRec.szTrack1Data));
    srTransRec.usTrack1Len=0;

    memset(srTransRec.szTrack2Data, 0x00, sizeof(srTransRec.szTrack2Data));
    srTransRec.usTrack2Len=0;

    memset(srTransRec.szTrack3Data, 0x00, sizeof(srTransRec.szTrack3Data));
    srTransRec.usTrack3Len=0;

    memset(srTransRec.szCardholderName, 0x00, sizeof(srTransRec.szCardholderName));

    memset(srTransRec.szPAN, 0x00, sizeof(srTransRec.szPAN));
    srTransRec.byPanLen = 0;
    memset(srTransRec.szExpireDate, 0x00, sizeof(srTransRec.szExpireDate));
    memset(srTransRec.szServiceCode, 0x00, sizeof(srTransRec.szServiceCode));

    srTransRec.byEntryMode = 0;
}

int inCTOS_CheckEMVFallbackTimeAllow(char* szStartTime, char* szEndTime, int inAllowTime)
{
    char szTempBuf[20];
    int inHH1, inHH2, inMM1, inMM2, inSS1, inSS2, inGap;

    if ((strlen(szStartTime) == 0) || (strlen(szStartTime) == 0)) 
        return (d_OK);

    if(0 == atoi(szStartTime))
        return (d_OK);

    memset(szTempBuf, 0, sizeof(szTempBuf));
    memcpy(szTempBuf, &szStartTime[0], 2);
    inHH1 = atoi(szTempBuf);

    memset(szTempBuf, 0, sizeof(szTempBuf));
    memcpy(szTempBuf, &szStartTime[2], 2);
    inMM1 = atoi(szTempBuf);

    memset(szTempBuf, 0, sizeof(szTempBuf));
    memcpy(szTempBuf, &szStartTime[4], 2);
    inSS1 = atoi(szTempBuf);

    memset(szTempBuf, 0, sizeof(szTempBuf));
    memcpy(szTempBuf, &szEndTime[0], 2);
    inHH2 = atoi(szTempBuf);

    memset(szTempBuf, 0, sizeof(szTempBuf));
    memcpy(szTempBuf, &szEndTime[2], 2);
    inMM2 = atoi(szTempBuf);

    memset(szTempBuf, 0, sizeof(szTempBuf));
    memcpy(szTempBuf, &szEndTime[4], 2);
    inSS2 = atoi(szTempBuf);

    inGap = ((inHH2*3600)+(inMM2*60)+inSS2) - ((inHH1*3600)+(inMM1*60)+inSS1);

    if (inGap < 0 )
        return (d_OK);

    if(inGap > inAllowTime)
        return (d_NO);

    return d_OK;;

}

int inCTOS_CheckEMVFallback(void)
{
    BYTE szFallbackStartTime[20];
    BYTE szCurrentTime[20];
    int inRet;
    CTOS_RTC SetRTC;

	vdDebug_LogPrintf("fChkServiceCode=[%d]",strCDT.fChkServiceCode);
	vdDebug_LogPrintf("szServiceCode=[%c]",srTransRec.szServiceCode[0]);
	vdDebug_LogPrintf("inFallbackToMSR=[%d],srTransRec.byEntryMode=[%d]",inFallbackToMSR,srTransRec.byEntryMode);
    if (strCDT.fChkServiceCode)
    {
        if(((srTransRec.szServiceCode[0] == '2') || (srTransRec.szServiceCode[0] == '6'))
            && (CARD_ENTRY_ICC != srTransRec.byEntryMode))
        {
            if(inFallbackToMSR == SUCCESS)
            {
                CTOS_RTCGet(&SetRTC);
                sprintf(szCurrentTime,"%02d%02d%02d",SetRTC.bHour,SetRTC.bMinute,SetRTC.bSecond);
                inRet = inCTOS_CheckEMVFallbackTimeAllow(strTCT.szFallbackTime, szCurrentTime, strTCT.inFallbackTimeGap);

                inFallbackToMSR = FAIL;
                memset(strTCT.szFallbackTime,0x00,sizeof(strTCT.szFallbackTime));
                
                if(d_OK != inRet)
                    return FAIL;
                else
                    vdCTOS_SetTransEntryMode(CARD_ENTRY_FALLBACK);
            }
            else
                return FAIL;
        }
    }

    return d_OK;;
}

int inCTOS_CheckIssuerEnable(void)
{
    int inEnable = 0;

    inEnable = strIIT.inCheckHost;
    vdDebug_LogPrintf("inCTOS_CheckIssuerEnable: [%d]", inEnable);

    if(0 == inEnable)
    {
       vdSetErrorMessage("TRANS NOT ALLWD,ISSUER");
       return(ST_ERROR);
    }
    else
        return(ST_SUCCESS);
}


int inCTOS_CheckTranAllowd(void)
{
    int inEnable = 0;

    vdDebug_LogPrintf("srTransRec.byEntryMode[%d]",srTransRec.byEntryMode);
    if (inMultiAP_CheckSubAPStatus() == d_OK)
        return d_OK;

    inEnable = strPIT.fTxnEnable;
    vdDebug_LogPrintf("inCTOS_CheckTranAllowd: [%d]", inEnable);
    
    if(0 == inEnable)
    {
       vdSetErrorMessage("TRANS NOT ALLWD,PIT");
       return(ST_ERROR);
    }
    else
        return(ST_SUCCESS);
}

int inCTOS_CheckCUPTranAllowd(void)
{
    int inEnable = 0;

	vdDebug_LogPrintf("inCTOS_CheckCUPTranAllowd: HDTid[%d]", srTransRec.HDTid);
	if (fChkCUPHost(srTransRec.HDTid))
		return d_OK;
	else
	{
		vdSetErrorMessage("TRANS NOT ALLWD");
		return d_NO;
	}
}

int inCTOS_CheckCUPTranNoAllowd(void)
{
    int inEnable = 0;

	vdDebug_LogPrintf("inCTOS_CheckCUPTranNoAllowd: HDTid[%d]", srTransRec.HDTid);
	if (fChkCUPHost(srTransRec.HDTid))
	{
		vdSetErrorMessage("TRANS NOT ALLWD");
		return d_NO;
	}
	else
	{
		return d_OK;
	}
}



int inCTOS_CheckMustSettle(void)
{
    int inEnable = 0;

    inEnable = strMMT[0].fMustSettFlag;
    
    if(1 == inEnable)
    {
       vdSetErrorMessage("MUST SETTLE");
       return(ST_ERROR);
    }
    else
        return(ST_SUCCESS);
}

void vdCTOS_FormatPAN(char *szFmt,char* szInPAN,char* szOutPAN)
{
    char szCurrentPAN[20];
    int inFmtIdx = 0;
    int inPANIdx = 0;
    int inFmtPANSize;
    
    inFmtPANSize = strlen(szFmt);
    if (strlen(szFmt) == 0) 
    {
      strncpy(szOutPAN,szInPAN,inFmtPANSize);
      return;
    }

    memset(szCurrentPAN, 0x00, sizeof(szCurrentPAN));
    memcpy(szCurrentPAN,szInPAN,strlen(szInPAN));

    while(szFmt[inFmtIdx]!= 0x00)
    {
      if(szFmt[inFmtIdx] == 'N' || szFmt[inFmtIdx] == 'n')
      {
          szOutPAN[inFmtIdx] = szCurrentPAN[inPANIdx]; 
          inFmtIdx++;
          inPANIdx++;
      }
      else if (szFmt[inFmtIdx] == 'X' || szFmt[inFmtIdx] == 'x' ||szFmt[inFmtIdx] == '*')   
      {
                     
          memcpy(&szOutPAN[inFmtIdx],&szFmt[inFmtIdx],1);
          inFmtIdx++;
          inPANIdx++;
      }
      else if (!isdigit(szFmt[inFmtIdx]))
      {
          szOutPAN[inFmtIdx] = szFmt[inFmtIdx];
          inFmtIdx++;
      }
    }

    while(szCurrentPAN[inPANIdx]!= 0x00)
    {
     szOutPAN[inFmtIdx] = szCurrentPAN[inPANIdx]; 
     inFmtIdx++;
     inPANIdx++;
    }

    return;
}

//start base on issuer IIT and mask card pan and expire data
int ntocs( char *dest_buf, char *src_buf)
{
   int i;

   // DCS #1045 : Included following 2 lines
	if ( (dest_buf == NULL) || (src_buf == NULL) ) 
	   return -1;

      /* get length of current string add one for count */
   i = strlen(src_buf)+1;

       /* max counted string length is 255 */
   if ((i > 255) || (i == 0))
       dest_buf[0] = 0x00;
   else
   {
       strcpy(dest_buf+1, src_buf);
       dest_buf[0] = i;
   }

   /* return the count length */
   return (dest_buf [0]);

}


int str2int(char *buffer)
{
   int sign=0;
	int num;
	
			/* incoming string must not be longer than 40 characters */
   char  temp[42];
     
   // DCS #1045 : Included following 2 lines
   if ( buffer == NULL) 
	   return -1;

  if (strlen(buffer) > 40)
     return (0);

  if (*buffer == '-')
     sign= '-';

			/* SVC_2INT requires counted strings */
  ntocs(temp, buffer);
 
  //num = SVC_2INT (temp);
  num = atol (temp);

  return(sign?-num:num);

}


int chars2int (char * s_buffer, int i_num)
{
    char  temp[6];
    i_num = MIN (i_num, 5);

    if (i_num < 0)
    {
         return 0;
    }
    temp[i_num] = 0x00;
    memcpy (temp, s_buffer, i_num);
    return (str2int (temp));
}

int append_char (char *string, char c)
{
    int i;

        /* get the current length of the string, this is the 
            position for the new character.
        */
    i = strlen (string);
    
        /* now place the passed character at the end of the 
            string.  since the length is the number of characters
            in the string, then the pointer plus the number of characters
            is the current NULL position.  The user may pass an empty
            string.  In this case, the apended character will be in the 
            first position.
        */
    *(string + i++) = c;
        /* Now add a NULL after the newly appended character. */

	if (c != 0)
	*(string + i) = 0x00;
    
	/* i is the position of the NULL which is also the new string
    length.  Return i.
    */
    return (strlen (string));
}

int pad (char *pdest_buf, char *psrc_buf, char pad_char, int pad_size,
                   int align)
{
   int ch_left, ch_right;
   int num_pad;
   char *d_ptr, *s_ptr;

         /* pad _size cannot be negative */

   if (pad_size < 0)
      pad_size = 0;
      
         /* determine how many characters to add */
         /* ensure we need to add characters */

   if ( 0 > (num_pad = pad_size - (int) strlen (psrc_buf)))
      num_pad = 0;


		 /* the source and destination buffer may be the same
		 *  buffer.  if they are different, copy the source
		 *  to the destination and do not molest the source
		 *
		 *	02/18/92  jwm
		 */
		 
   if ( psrc_buf != pdest_buf)
   {
         /* initialized destination and copy source 
		 *  2/18/92 jwm
		 */
      memcpy (pdest_buf, psrc_buf, strlen (psrc_buf)+1);
   }												

         /* determine the number of characters to pad on */
         /* each end.                                    */

   switch (align)
   {
   
      case  RIGHT:
      {
         ch_left = num_pad;
         ch_right = 0;
         break;
      }
      
      case CENTER:
      {
         ch_left = num_pad / 2;
         ch_right = num_pad - ch_left;
         break;
      }

      case LEFT:
      default:
      {
         ch_left = 0;
         ch_right = num_pad;
         break;
      }
   }
         /* pad the front of the string */

   if (ch_left)
   {
       s_ptr = psrc_buf + strlen(psrc_buf);
       d_ptr = pdest_buf + strlen(psrc_buf) + ch_left;
       while ( psrc_buf <= s_ptr)     /* copy string to destination */
          *d_ptr-- = *s_ptr--;
       while (ch_left--)			  /* add pad characters before string */
          *d_ptr-- = pad_char; 											   
   }

         /* pad the end of the string */

   while (ch_right --)
   {
      append_char (pdest_buf, pad_char);
   }

   return (num_pad);
}

void vdCTOSS_PrintFormatPAN(char* pchPAN,char* pchFmtPAN,int inFmtPANSize,int page)
{
    char szFmt[FORMATTED_PAN_SIZE + 1];
    char szScratch[FORMATTED_PAN_SIZE + 1];
    char szCurrentPAN[FORMATTED_PAN_SIZE + 1];
    char chLastPad = ' ';
    VS_BOOL fReverseMode = VS_FALSE;
    VS_BOOL fMerchFormat = VS_FALSE;
    int inBytesLeft;
    int inLen;
    int inLastDigits;
    int inFmtIdx = 0;
    int inPANIdx = 0;
    int i = 0;

    /* Remove 1 from count for NULL terminator, assume caller uses sizeof */
    inFmtPANSize--;


    memset(szFmt, 0x00, sizeof(szFmt));
	
	if ( page == d_FIRST_PAGE )
	{
//		strcpy(szFmt, strIIT.szMaskMerchantCopy);
        strcpy(szFmt, strIIT.szMaskCustomerCopy);
    }
	else if ( page == d_SECOND_PAGE )
	{
//		strcpy(szFmt, strIIT.szMaskCustomerCopy);
        strcpy(szFmt, strIIT.szMaskMerchantCopy);
    }
	else	
		strcpy(szFmt, strIIT.szPANFormat);

	if (inFmtPANSize == EXP_DATE_SIZE)
	{
		strcpy(szFmt, strIIT.szMaskExpireDate);
	}	

	if (strlen(pchPAN) >= 19 && srTransRec.HDTid == DINERS_HOST_INDEX)
	{
		strcpy(szFmt, "***************NNNN");
	}

	vdDebug_LogPrintf("szFmt = [%d] [%s] [%s]",inFmtPANSize, szFmt,pchPAN);

    fMerchFormat = strIIT.fMerchPANFormat;
    

    /* First check on reverse mode, only indicated by first byte */
    memset(szCurrentPAN, 0x00, sizeof(szCurrentPAN));
    if (szFmt[inFmtIdx] == 'R' || szFmt[inFmtIdx] == 'r') {
        fReverseMode = VS_TRUE;
        inFmtIdx++;  /* Move past the 'r' */
        inBytesLeft = inLen = strlen(pchPAN);
        inBytesLeft--;  /* array index starts at 0 */
        for (i = 0; i < inLen && i < FORMATTED_PAN_SIZE; i++,inBytesLeft--) {
            /* Copy the PAN backwards */
            szCurrentPAN[i] = *(pchPAN + inBytesLeft);
        }
    } /* end if reverse mode */
    else
        strncpy(szCurrentPAN, pchPAN, FORMATTED_PAN_SIZE); /* end else normal mode */

	//LOG_PRINTFF((0X08L, "szCurrentPAN = [%s]",szCurrentPAN));

    /* While rcpt.acctnum not full && ! end of PAN  && ! end of Format string */
    while (szFmt[inFmtIdx] != NULL_CH && szCurrentPAN[inPANIdx] != NULL_CH && strlen(pchFmtPAN) < inFmtPANSize) {
        if (szFmt[inFmtIdx] == 'n' || szFmt[inFmtIdx] == 'N') {
            /* They want this one digit in full view */
            append_char(pchFmtPAN, szCurrentPAN[inPANIdx]);
            inFmtIdx++;
            inPANIdx++;
            if (szFmt[inFmtIdx] == NULL_CH)
                inFmtIdx--;
        } /* end if n or N, print PAN digit */
        else if (szFmt[inFmtIdx] == 'X' || szFmt[inFmtIdx] == 'x' || szFmt[inFmtIdx] == '*') {
            /* They want one pad character */
            append_char(pchFmtPAN, szFmt[inFmtIdx]);
            chLastPad = szFmt[inFmtIdx];
            inFmtIdx++;
            inPANIdx++;
            if (szFmt[inFmtIdx] == NULL_CH) {
                /* No more formatting so print the rest w/ pads */
                inBytesLeft = strlen(&szCurrentPAN[inPANIdx]);
                memset(szScratch, 0x00, sizeof(szScratch));
                pad(szScratch, szScratch, chLastPad, inBytesLeft, RIGHT);
                strcat(pchFmtPAN, szScratch);
            }
        } /* end if pad character (x,X or * ) */
        else if (!isdigit(szFmt[inFmtIdx])) {
            /* They want a format separator */
            append_char(pchFmtPAN, szFmt[inFmtIdx]);
            inFmtIdx++;
        } /* end if not 0-9 */
        else if (szFmt[inFmtIdx] == '0') {
            /* Zero is not a valid value */
            inFmtIdx++;
        } /* end if zero */
        else {
            /* We must have 1-9 in szFmt[inFmtIdx] */
            inBytesLeft = strlen(&szCurrentPAN[inPANIdx]);
            inLastDigits = chars2int(&szFmt[inFmtIdx], 1);

            /* if we are reverse mode the last digits are in the front.
             if we are not in reverse then if what we have left in the PAN
             is less than or equal to the LastDigit PAN Format indicator
             if either is true then print a PAN digit in the open */
            if ((fReverseMode && inPANIdx < inLastDigits) || (!fReverseMode && inBytesLeft <= inLastDigits)) {
                append_char(pchFmtPAN, szCurrentPAN[inPANIdx]);
                inFmtIdx++;
                inPANIdx++;
                if (fReverseMode && (inPANIdx < inLastDigits || szFmt[inFmtIdx] == NULL_CH)) {
                    /* Still have more PAN digits to print */
                    inFmtIdx--;
                }
                if (szFmt[inFmtIdx] == NULL_CH) {
                    /* No more formatting so print the rest of the PAN */
                    strcat(pchFmtPAN, &szCurrentPAN[inPANIdx]);
                }
            } /* if last x digits then print the PAN in the open */
            else {
                /* If we are not at the end of PAN then print the last pad char */
                if (chLastPad != ' ')
                    append_char(pchFmtPAN, chLastPad);
                inFmtIdx++;
                inPANIdx++;
                /* if that was our last format character then use it again */
                if (szFmt[inFmtIdx] == NULL_CH)
                    inFmtIdx--;
            } /* end if PAN remaining greater than last digit indicator */
        } /* end else, must be 1-9 */
    } /* end while print buffer not full, not end of Format string or PAN */

    if (fReverseMode) {
        memset(szScratch, 0x00, sizeof(szScratch));
        inBytesLeft = inLen = strlen(pchFmtPAN);
        inBytesLeft--;  /* array index starts at 0 */
        for (i = 0; i < inLen && i < FORMATTED_PAN_SIZE; i++,--inBytesLeft) {
            /* Copy the Formatted PAN back so that it is forward */
            szScratch[i] = *(pchFmtPAN + inBytesLeft);
        }
        strncpy(pchFmtPAN, szScratch, FORMATTED_PAN_SIZE);
    } /* end if reverse mode */

	vdDebug_LogPrintf("pchFmtPAN = [%s]",pchFmtPAN);

    return;
} /* end func vdFormatPAN() */
//end base on issuer IIT and mask card pan and expire data


int inGetIssuerRecord(int inIssuerNumber) 
{
    int inRec = 1;
    do 
    {
        //if (inIITRead(inRec) != d_OK) //for improve transaction speed 
		if (inIITRead(inIssuerNumber) != d_OK)
        {
            return(d_NO);
        }
        inRec++;
    } while (inIssuerNumber != strIIT.inIssuerNumber);

    return(d_OK);
}


int inCTOS_DisplayCardTitle(USHORT usCardTypeLine, USHORT usPANLine)
{
    char szStr[50 + 1]; 
    USHORT EMVtagLen;
    BYTE   EMVtagVal[64];
    BYTE szTemp1[30+1];

    memset(szStr,0x00,sizeof(szStr));
    memset(EMVtagVal,0x00,sizeof(EMVtagVal));
  
    if (srTransRec.byEntryMode == CARD_ENTRY_ICC)
    {    
        EMVtagLen = 0;
        
        if(EMVtagLen > 0)
        {
            sprintf(szStr, "%s", EMVtagVal);
            vdDebug_LogPrintf("Card label(str): %s",szStr);       
        }
    }

    if(0 == strlen(szStr))
    {
        sprintf(szStr, "%s", strCDT.szCardLabel);
        vdDebug_LogPrintf("Card label: %s",strCDT.szCardLabel);
    }
        
    memset(szTemp1,0,sizeof(szTemp1));
    vdCTOS_FormatPAN(strIIT.szPANFormat, srTransRec.szPAN, szTemp1);

    if(0>= usCardTypeLine || 0 >= usPANLine)
    {
        usCardTypeLine = 3;
        usPANLine = 4;
    }

	if (!strTCT.TaxiMode)
	{
	    CTOS_LCDTPrintXY(1, usCardTypeLine, szStr);
	    CTOS_LCDTPrintXY(1, usPANLine, szTemp1);
	}
	
    return(d_OK);
}

short inCTOS_LoadCDTIndex(void)
{
	signed int inRetVal;
	short   shStatus;
	int     i=0, selectedRDTIndex;
	BYTE    shSuitableRDTIndex[10];
	int inIssuer = 0;
	int inNumberOfMatches = 0;
	int inRecNumArray[4];
	char szChoiceMsg[100 + 1];
	int    inCardLen, j;
	int inMaxCDTid = 0;
	int inFindRecordNum=0;
    int inCPTID[50];
	
    char szAppMenu[1024];
	BYTE key;
	BYTE bHeaderAttr = 0x01+0x04, iCol = 1;
    BYTE  x = 1;
    char szHeaderString[50] = "SELECT APP";
	BOOL fCUPEnabled = inCDTChkCUPEnable() > 0? TRUE: FALSE;

	
	vdDebug_LogPrintf("inCTOS_LoadCDTIndex");

	memset(szChoiceMsg, 0x00, sizeof(szChoiceMsg));
  
	vdDebug_LogPrintf("fCUPEnabled[%d] strTCT.TaxiMode[%d] srTransRec.szPAN[%s] srTransRec.usLoadCDTMode[%d]", fCUPEnabled, strTCT.TaxiMode, srTransRec.szPAN, srTransRec.usLoadCDTMode);
	/*
	UOB requirements for DUAL BRAND CARD:
	For Card Swipe / Manual Key-In (MKI) / Fallback transaction:
	Terminal should use the card bin range for switching to corresponding payment
	application directly.
	*/
	if(!fCUPEnabled)
		{
			if((srTransRec.byEntryMode == CARD_ENTRY_ICC || srTransRec.byEntryMode == CARD_ENTRY_EASY_ICC) && memcmp(srTransRec.stEMVinfo.T84,UNIONPAY_RID,5) == 0)
				srTransRec.usLoadCDTMode = 2; //Load CUP Only
			else	
				srTransRec.usLoadCDTMode = 1; //Load exclude CUP
		}
	else
		{
		if (srTransRec.byEntryMode == CARD_ENTRY_MSR ||
			srTransRec.byEntryMode == CARD_ENTRY_MANUAL ||
			srTransRec.byEntryMode == CARD_ENTRY_FALLBACK
			)
			{
				if (((srTransRec.szPAN[0] == '4') && (strTCT.fAllowVisaCUPAIDSelect == 1)) ||
					((srTransRec.szPAN[0] == '5') && (strTCT.fAllowMasterCUPAIDSelect == 1)))
				{
					srTransRec.usLoadCDTMode = 0; //Load all records.
				}
				else if (((srTransRec.szPAN[0] == '4') && (strTCT.fAllowVisaCUPAIDSelect == 0)) ||
					((srTransRec.szPAN[0] == '5') && (strTCT.fAllowMasterCUPAIDSelect == 0)))
					srTransRec.usLoadCDTMode = 1; //Load exclude CUP
			}
		else if(srTransRec.byEntryMode == CARD_ENTRY_ICC || srTransRec.byEntryMode == CARD_ENTRY_EASY_ICC || srTransRec.byEntryMode == CARD_ENTRY_WAVE)
			{
				if(memcmp(srTransRec.stEMVinfo.T84,UNIONPAY_RID,5) == 0)
					srTransRec.usLoadCDTMode = 2; //Load CUP Only
				else
					srTransRec.usLoadCDTMode = 1; //Load exclude CUP
			}
		}
	if (strTCT.TaxiMode)
		inCDTReadMultiExclApp(srTransRec.szPAN, &inFindRecordNum,srTransRec.usLoadCDTMode, "NETS");
	else
		inCDTReadMulti(srTransRec.szPAN, &inFindRecordNum,srTransRec.usLoadCDTMode);

	//inCTOS_SelectHostFilter(strMCDT, inCPTID);

	if(inFindRecordNum == 0)
	{
		vdDebug_LogPrintf("Not find in CDT");
		vdSetErrorMessage("CARD NOT SUPPORTED");
		return INVALID_CARD;
	}
	vdDebug_LogPrintf("inCTOS_LoadCDTIndex inFindRecordNum[%d]",inFindRecordNum);
	for(j=0;j<inFindRecordNum;j++)
	{
        vdDebug_LogPrintf("---strTCT.fDebitFlag[%d], strMCDT[j].fIsOnUsCard=%d, strMCDT[%d].inType=%d, strMCDT[j].IITid=%d, inIssuer=%d", strTCT.fDebitFlag, strMCDT[j].fIsOnUsCard, j, strMCDT[j].inType, strMCDT[j].IITid, inIssuer);
//		if (!(strTCT.fDebitFlag == VS_FALSE && strMCDT[j].inType == DEBIT_CARD) && !(!strMCDT[j].fManEntry && srTransRec.byEntryMode == CARD_ENTRY_MANUAL)) 
//		if (!(strTCT.fDebitFlag == VS_FALSE && strMCDT[j].inType == DEBIT_CARD) )
    	{
            if(strMCDT[j].fIsOnUsCard == TRUE)
            {
                vdDebug_LogPrintf("ONUS card, just break, only find 1");
                if (strMCDT[j].inType != DEBIT_CARD && strMCDT[j].inType != EBT_CARD)
                    inIssuer = strMCDT[j].IITid;
                vdDebug_LogPrintf("ONUS card, just break, only find 1");
                inRecNumArray[inNumberOfMatches++] = strMCDT[j].CDTid;
                break;
            }

//			if (/*(strMCDT[j].fIsOnUsCard == TRUE) ||*/ (strMCDT[j].inType == DEBIT_CARD) || (strMCDT[j].inType == EBT_CARD) || (strMCDT[j].IITid != inIssuer))
      		{
				if (strMCDT[j].inType != DEBIT_CARD && strMCDT[j].inType != EBT_CARD)
					inIssuer = strMCDT[j].IITid;

				inRecNumArray[inNumberOfMatches++] = strMCDT[j].CDTid;
				vdDebug_LogPrintf("inCTOS_LoadCDTIndex, offus, strMCDT[%d].szCardLabel[%s]",j,strMCDT[j].szCardLabel);
				if (inNumberOfMatches > 1)
					strcat(szChoiceMsg,"\n");
				strcat(szChoiceMsg,strMCDT[j].szCardLabel);
#if 0
				switch (strMCDT[j].inType)
				{
					case DEBIT_CARD:
					break;
					case EBT_CARD:
					break;
					case PURCHASE_CARD:
					break;
					default:                    
					break;
				}
#endif
//				if (inNumberOfMatches > 3)
                if (inNumberOfMatches > 0)
					break;
			}
		}
    }

    vdDebug_LogPrintf("inCTOS_LoadCDTIndex inNumberOfMatches[%d],inRecNumArray[0]=%d",inNumberOfMatches, inRecNumArray[0]);
    if (inNumberOfMatches == 1) 
    {
        inRetVal = inRecNumArray[0];
    }
    else if (inNumberOfMatches > 1) 
    {
    	memset(szHeaderString,0x00,sizeof(szHeaderString));
	strcpy(szHeaderString,"SELECT CARD");	
        vdDispTransTitle(srTransRec.byTransType);
	key = MenuDisplay(szHeaderString, strlen(szHeaderString), bHeaderAttr, iCol, x, szChoiceMsg, TRUE);	
	vdDebug_LogPrintf("inCTOS_LoadCDTIndex key[%d]",key);
		if (key == 0xFF) 
		{
			//vdDisplayErrorMsg(1, 8, "WRONG INPUT!!!");
		    return -1;  
		}
		
	    if(key > 0)
	    {
	        if(d_KBD_CANCEL == key)
	            return -1;
		inRetVal = inRecNumArray[key-1];
	    	}
    }

    if (inRetVal >= 0) 
    {
        inCDTRead(inRetVal);
        srTransRec.CDTid = inRetVal;// save the current CDT that is loaded

        /* Get the CDT also for card labels */
        inGetIssuerRecord(strCDT.IITid);
    }
    else
    {
        vdDebug_LogPrintf("Not find in CDT");
        vdSetErrorMessage("CARD NOT SUPPORTED");
        return INVALID_CARD;
    }

    /* Check for proper card length */
    inCardLen = strlen(srTransRec.szPAN);

	vdDebug_LogPrintf("inCardLen [%ld], inMinPANDigit [%ld], inMaxPANDigit [%ld]]", inCardLen, strCDT.inMinPANDigit, strCDT.inMaxPANDigit);

    if ((inCardLen < strCDT.inMinPANDigit) ||
        (inCardLen > strCDT.inMaxPANDigit))
    {
        vdDebug_LogPrintf("BAD CARD LEN");
        vdSetErrorMessage("BAD CARD LEN");
        return INVALID_CARD;
    }

    if (strCDT.fluhnCheck == VS_TRUE)   /* Check Luhn */
    {
        if (chk_luhn(srTransRec.szPAN))
        {
            vdDisplayErrorMsg(1, 8, "INVALID LUHN");
            return INVALID_CARD;
        }
    }


/*    if (strTCT.fDebitFlag != DEBIT && strCDT.inType == DEBIT_CARD )
    {
        vdDisplayErrorMsg(1, 8, "INVALID CARD");
        return INVALID_CARD;
    }*/

    if(strCDT.fExpDtReqd)
    {
        if(srTransRec.byEntryMode == CARD_ENTRY_ICC || srTransRec.byEntryMode == CARD_ENTRY_EASY_ICC || srTransRec.byEntryMode == CARD_ENTRY_WAVE)
        {
            vdDebug_LogPrintf("ctls or chip ignore exp checking");
        }
        else
        {
            if(shChk_ExpireDate() != d_OK)
            {
                vdDisplayErrorMsg(1, 8, "CARD EXPIRED");
                return CARD_EXPIRED;
            }
        }
    }
    
    //for save Accum file
    srTransRec.IITid= strCDT.IITid;
    srTransRec.HDTid = strCDT.HDTid;
    srTransRec.inCardType = strCDT.inType;
    srTransRec.byIsOnusCard = strCDT.fIsOnUsCard;//no need to save it to batch, so no update db save/read
	//CTOS_LCDTClearDisplay();
	//vduiClearBelow(2);
//    CTOS_LCDTPrintXY(1, 8, "                   ");  
    return d_OK;
}

int inCTOS_EMVCardReadProcess (void)
{
    short shResult = 0;
    USHORT usMsgFailedResult = 0;
    
    vdDebug_LogPrintf("-------shCT0S_EMVInitialize1---[%d]--",shResult); 
	inMultiAP_Database_EMVTransferDataInit();//for improve transaction speed 
	shResult = shCTOS_EMVAppSelectedProcess();
    
    vdDebug_LogPrintf("-------shCT0S_EMVInitialize---[%d]--",shResult); 

    if(shResult == EMV_CHIP_FAILED)
    {
        usMsgFailedResult = MSG_TRANS_ERROR;
        return usMsgFailedResult;
    }
    else if(shResult == EMV_USER_ABORT)
    {
        usMsgFailedResult = MSG_USER_CANCEL;
        return usMsgFailedResult;
    }
    shCTOS_EMVGetChipDataReady();

    return (d_OK);
}


int inCTOS_ManualEntryProcess (BYTE *szPAN)
{
    USHORT  usMaxLen = 19;
    BYTE    szTempBuf[10];
    BYTE    bDisplayStr[MAX_CHAR_PER_LINE+1];
    vdDebug_LogPrintf("inCTOS_ManualEntryProcess");
    CTOS_LCDTClearDisplay();
	//vduiClearBelow(2);
//    vdDispTransTitle(srTransRec.byTransType);
    char szTxnTittleBuf[30] = {0};
    szGetTransTitle(srTransRec.byTransType, szTxnTittleBuf);
    usCTOSS_ClearAllShowTittle(szTxnTittleBuf);

    setLCDPrint(7, DISPLAY_POSITION_LEFT, "CARD NO: ");

	fGetCardNO = 1;
    if(getCardNO(szPAN) != d_OK)
    {
    	fGetCardNO = 0;
        return USER_ABORT;
    }
	fGetCardNO = 0;
    CTOS_LCDTClearDisplay();
	//vduiClearBelow(2);
    vdDispTransTitle(srTransRec.byTransType);
    setLCDPrint(4, DISPLAY_POSITION_LEFT, "CARD NO: ");
    memset(bDisplayStr, 0x00, sizeof(bDisplayStr));
    memset(bDisplayStr, 0x20, usMaxLen*2);
    strcpy(&bDisplayStr[(usMaxLen-strlen(szPAN))*2], szPAN);
    CTOS_LCDTPrintXY(MAX_CHAR_PER_LINE-usMaxLen*2, 5, bDisplayStr);
    setLCDPrint(7, DISPLAY_POSITION_LEFT, "EXPIRY DATE(MM/YY):");
    
    memset(szTempBuf,0,sizeof(szTempBuf));
    if(getExpDate(szTempBuf) != d_OK)
    {
        return USER_ABORT;
    }
    wub_str_2_hex(szTempBuf, &srTransRec.szExpireDate[1], 2);
    wub_str_2_hex(&szTempBuf[2], srTransRec.szExpireDate, 2);
    CTOS_KBDBufFlush ();

    vdCTOS_SetTransEntryMode(CARD_ENTRY_MANUAL);
    return d_OK;;
}

int inCTOS_GetCardFields(void)
{
    USHORT EMVtagLen;
    BYTE   EMVtagVal[64];
    BYTE byKeyBuf = 0;
    BYTE bySC_status;
    BYTE byMSR_status;
    BYTE szTempBuf[10];
    USHORT usTk1Len, usTk2Len, usTk3Len;
    BYTE szTk1Buf[TRACK_I_BYTES], szTk2Buf[TRACK_II_BYTES], szTk3Buf[TRACK_III_BYTES];
    usTk1Len = TRACK_I_BYTES ;
    usTk2Len = TRACK_II_BYTES ;
    usTk3Len = TRACK_III_BYTES ;
    int  usResult;

 DebugAddSTR("inCTOS_GetCardFields","Processing...",20); 
    //InsertCardUI();
	if (0 == fGetAppRunBySelf() && inMultiAP_CheckSubAPStatus() == d_OK)
     	{
     		vdDebug_LogPrintf("inMultiAP_CheckSubAPStatus");
        	return d_OK;
     	}
 SWIPE_AGAIN:

//	InsertCardUIEx();
	if(strTCT.fManualInput == 1)
	{
		InsertCardUINew("0111");
	}else{
		InsertCardUINew("0110");
	}


	vdDebug_LogPrintf("inCTOS_GetCardFields");
    if(d_OK != inCTOS_ValidFirstIdleKey())
    {
        //CTOS_LCDTClearDisplay();
		//vduiClearBelow(2);
        vdDispTransTitle(srTransRec.byTransType);

        inCTOS_DisplayIdleBMP();
    }
// patrick ECR 20140516 start
    if (strTCT.fECR) // tct
    {
    	if (memcmp(srTransRec.szBaseAmount, "\x00\x00\x00\x00\x00\x00", 6) != 0)
    	{
    		char szDisplayBuf[30];	
    		BYTE szTemp1[30+1];

    		CTOS_LCDTPrintXY(1, 7, "AMOUNT:");		 
    		memset(szDisplayBuf, 0x00, sizeof(szDisplayBuf));
    		wub_hex_2_str(srTransRec.szBaseAmount, szTemp1, AMT_BCD_SIZE);
			//gcitra-0728
    		//sprintf(szDisplayBuf, "%s %10lu.%02lu", strCST.szCurSymbol,atol(szTemp1)/100, atol(szTemp1)%100);
    		//CTOS_LCDTPrintXY(1, 8, szDisplayBuf);	
    		CTOS_LCDTPrintXY(1, 8, strCST.szCurSymbol);	
    		memset(szDisplayBuf,0x00,sizeof(szDisplayBuf));
			//format amount 10+2
			vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szTemp1, szDisplayBuf);
			//sprintf(szDisplayBuf,"%10.0f.%02.0f",(atof(szTemp1)/100), (atof(szTemp1)%100));
			//sprintf(szDisplayBuf, "%lu.%02lu", atol(szTemp1)/100, atol(szTemp1)%100);
    		setLCDPrint(8, DISPLAY_POSITION_RIGHT, szDisplayBuf);
			//gcitra-0728

    	}
    }
// patrick ECR 20140516 end
    CTOS_TimeOutSet (TIMER_ID_1 , GET_CARD_DATA_TIMEOUT_VALUE);
    ing_KeyPressed = 0;
    while (1)
    {
        if(CTOS_TimeOutCheck(TIMER_ID_1 )  == d_YES)
            return READ_CARD_TIMEOUT ;

        if (ing_KeyPressed == 'C')
        {
            vdDebug_LogPrintf("putchar C");
            CTOS_KBDBufPut('C');
            vdDebug_LogPrintf("return USER_ABORT");
            return USER_ABORT;
        }
        CTOS_KBDInKey(&byKeyBuf);
        vdDebug_LogPrintf("byKeyBuf=[%d][%c],ing_KeyPressed=%d", byKeyBuf, byKeyBuf, ing_KeyPressed);
        if ((byKeyBuf) || (d_OK == inCTOS_ValidFirstIdleKey()) || (ing_KeyPressed == 'M'))
        {
            vdDebug_LogPrintf("check byKeyBuf=[%d][%c]", byKeyBuf, byKeyBuf);
			if (byKeyBuf == d_KBD_CANCEL){
				CTOS_KBDBufFlush();
				return USER_ABORT;
			}
            memset(srTransRec.szPAN, 0x00, sizeof(srTransRec.szPAN));
            if(d_OK == inCTOS_ValidFirstIdleKey())
                srTransRec.szPAN[0] = chGetFirstIdleKey();
            
            vdDebug_LogPrintf("-srTransRec.szPAN[%s],byKeyBuf=[%d], inCTOS_ValidFirstIdleKey()=%d", srTransRec.szPAN, byKeyBuf, inCTOS_ValidFirstIdleKey());
            //get the card number and ger Expire Date
            if (d_OK != inCTOS_ManualEntryProcess(srTransRec.szPAN))
            {
                vdSetFirstIdleKey(0x00);
                CTOS_KBDBufFlush ();
                //vdSetErrorMessage("Get Card Fail M");
                return USER_ABORT;
            }
			vdSetFirstIdleKey(0x00);
            //Load the CDT table
            if (d_OK != inCTOS_LoadCDTIndex())
            {
                CTOS_KBDBufFlush();
                return USER_ABORT;
            }
            
            break;
        }

        CTOS_SCStatus(d_SC_USER, &bySC_status);
        if(bySC_status & d_MK_SC_PRESENT)
        {
            vdCTOS_SetTransEntryMode(CARD_ENTRY_ICC);
            
            vdDebug_LogPrintf("--EMV flow----" );
            if (d_OK != inCTOS_EMVCardReadProcess ())
            {
                if(inFallbackToMSR == SUCCESS)
                {
                    vdCTOS_ResetMagstripCardData();
                    vdDisplayErrorMsg(1, 8, "PLS SWIPE CARD");
                    goto SWIPE_AGAIN;
                }
                else
                {
                    //vdSetErrorMessage("Get Card Fail C");
                    return USER_ABORT;
                }
            }
            vdDebug_LogPrintf("--EMV Read succ----" );
			if (srTransRec.byTransType == REFUND)
				vdCTOS_SetTransEntryMode(CARD_ENTRY_EASY_ICC);
            //Load the CDT table
            if (d_OK != inCTOS_LoadCDTIndex())
            {
                CTOS_KBDBufFlush();
                return USER_ABORT;
            }
            
            break;
        }

        //for Idle swipe card
        if (strlen(srTransRec.szPAN) > 0)
         {
             if (d_OK != inCTOS_LoadCDTIndex())
             {
                 CTOS_KBDBufFlush();
                 //vdSetErrorMessage("Get Card Fail");
                 return USER_ABORT;
             }

             if(d_OK != inCTOS_CheckEMVFallback())
             {
                vdCTOS_ResetMagstripCardData();
                vdDisplayErrorMsg(1, 8, "PLS INSERT CARD"); 
                
                goto SWIPE_AGAIN;

             }
                     
             break;
         
         }
        
        byMSR_status = CTOS_MSRRead(szTk1Buf, &usTk1Len, szTk2Buf, &usTk2Len, szTk3Buf, &usTk3Len);
		//Fix for Track2 Len < 35
        //if((byMSR_status == d_OK ) && (usTk2Len > 35))
        if(byMSR_status == d_OK )
		//Fix for Track2 Len < 35
        {
			usResult = shCTOS_SetMagstripCardTrackData(szTk1Buf, usTk1Len, szTk2Buf, usTk2Len, szTk3Buf, usTk3Len); 
        	if (usResult != d_OK)
			{
                 CTOS_KBDBufFlush();
				 vdDisplayErrorMsg(1, 8, "READ CARD FAILED");
                 return USER_ABORT;
             }
			
            if (d_OK != inCTOS_LoadCDTIndex())
             {
                 CTOS_KBDBufFlush();
                 return USER_ABORT;
             }
            
            if(d_OK != inCTOS_CheckEMVFallback())
             {
                vdCTOS_ResetMagstripCardData();
                vdDisplayErrorMsg(1, 8, "PLS INSERT CARD"); 
                
                goto SWIPE_AGAIN;

             }
                
            break;
        }

       }

    if (srTransRec.byEntryMode == CARD_ENTRY_ICC)
    {    
        EMVtagLen = 0;
        if(EMVtagLen > 0)
        {
            sprintf(srTransRec.szCardLable, "%s", EMVtagVal);
        }
        else
        {
            strcpy(srTransRec.szCardLable, strIIT.szIssuerLabel);
        }
    }
    else
    {
        strcpy(srTransRec.szCardLable, strIIT.szIssuerLabel);
    }
    srTransRec.IITid = strIIT.inIssuerNumber;

    char szTxnTittleBuf[30] = {0};
    szGetTransTitle(srTransRec.byTransType, szTxnTittleBuf);
    usCTOSS_ClearAllShowTittle(szTxnTittleBuf);
    vdDebug_LogPrintf("srTransRec.byIsOnusCard=%d, srTransRec.byTransType[%d]srTransRec.IITid[%d], srTransRec.szCardLable=[%s]", srTransRec.byIsOnusCard, srTransRec.byTransType, srTransRec.IITid, srTransRec.szCardLable);
    if((srTransRec.byTransType == IPP) && (!srTransRec.byIsOnusCard))
    {
        //vdSetErrorMessage("INVALID CARD");
        //return d_NO;
    }

    return d_OK;
}



void InsertCardUINew(BYTE *szType) /*flags for tap/swipe/chip/manual. 1=enable, 0=disable*/
{	

    BYTE szTitle[20+1];
	BYTE szDisMsg[200];
    BYTE    szTotalAmt[12+1];
    BYTE    szStr[45];


    memset(szTitle, 0x00, sizeof(szTitle));
	memset(szDisMsg, 0x00, sizeof(szDisMsg));

    strcpy(szDisMsg, "cardidle");
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "back");

    szGetTransTitle(srTransRec.byTransType, szTitle);


	vdDebug_LogPrintf("szTitle[%s]", szTitle);
    strcat(szDisMsg, "|");
	strcat(szDisMsg, szTitle);


    memset(szTotalAmt, 0x00, sizeof(szTotalAmt));
	wub_hex_2_str(srTransRec.szTotalAmount, szTotalAmt, AMT_BCD_SIZE);
	
    
    vdTrimLeadZeroes(szTotalAmt);

    memset(szStr, 0x00, sizeof(szStr));
    vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szTotalAmt, szStr);
    vdDebug_LogPrintf("szStr[%s]", szStr);
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "Amount:");
	strcat(szDisMsg, "|");
	strcat(szDisMsg, szStr);
	strcat(szDisMsg, "|"); 
	strcat(szDisMsg, szType); 
 
 	//usCTOSS_DisplayUI(szDisMsg);
 	usCTOSS_CardEntryUI(szDisMsg);
}


void InsertCardUI(void)
{	

    BYTE szTitle[20+1];
	BYTE szDisMsg[200];
    BYTE    szTotalAmt[12+1];
    BYTE    szStr[45];


    memset(szTitle, 0x00, sizeof(szTitle));
	memset(szDisMsg, 0x00, sizeof(szDisMsg));

    strcpy(szDisMsg, "cardidle");
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "back");

    szGetTransTitle(srTransRec.byTransType, szTitle);


	vdDebug_LogPrintf("szTitle[%s]", szTitle);
    strcat(szDisMsg, "|");
	strcat(szDisMsg, szTitle);


    memset(szTotalAmt, 0x00, sizeof(szTotalAmt));
	wub_hex_2_str(srTransRec.szTotalAmount, szTotalAmt, AMT_BCD_SIZE);
	
    
    vdTrimLeadZeroes(szTotalAmt);

    memset(szStr, 0x00, sizeof(szStr));
    vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szTotalAmt, szStr);
    vdDebug_LogPrintf("szStr[%s]", szStr);
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "Amount:");
	strcat(szDisMsg, "|");
	strcat(szDisMsg, szStr);
	strcat(szDisMsg, "|1111"); //project_tine: t_newUI - flags for tap/swipe/chip/manual. 1=enable, 0=disable
	
 
 	//usCTOSS_DisplayUI(szDisMsg);
 	usCTOSS_CardEntryUI(szDisMsg);
}



void InsertCardUIEx(void)
{

    BYTE szTitle[20+1];
    BYTE szDisMsg[200];
    BYTE    szTotalAmt[12+1];
    BYTE    szStr[45];


    memset(szTitle, 0x00, sizeof(szTitle));
    memset(szDisMsg, 0x00, sizeof(szDisMsg));

    strcpy(szDisMsg, "cardidle");
    strcat(szDisMsg, "|");
    strcat(szDisMsg, "back");

    szGetTransTitle(srTransRec.byTransType, szTitle);


    vdDebug_LogPrintf("szTitle[%s]", szTitle);
    strcat(szDisMsg, "|");
    strcat(szDisMsg, szTitle);


    memset(szTotalAmt, 0x00, sizeof(szTotalAmt));
    wub_hex_2_str(srTransRec.szTotalAmount, szTotalAmt, AMT_BCD_SIZE);


    vdTrimLeadZeroes(szTotalAmt);

    memset(szStr, 0x00, sizeof(szStr));
    vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szTotalAmt, szStr);
    vdDebug_LogPrintf("szStr[%s]", szStr);
    strcat(szDisMsg, "|");
    strcat(szDisMsg, "Amount:");
    strcat(szDisMsg, "|");
    strcat(szDisMsg, szStr);
    strcat(szDisMsg, "|0111"); //project_tine: t_newUI - flags for tap/swipe/chip/manual. 1=enable, 0=disable


    //usCTOSS_DisplayUI(szDisMsg);
    usCTOSS_CardEntryUI(szDisMsg);
}

void DisplayApproved(void)
{	

    BYTE szTitle[20+1];
	BYTE szDisMsg[200];

	
	memset(szDisMsg, 0x00, sizeof(szDisMsg));
	strcpy(szDisMsg, "approved");
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "front");
	
 	usCTOSS_DisplayBmp(szDisMsg);


}

void DisplayMp4Appvoed(char *pSchemeName)
{

    BYTE szTitle[20+1];
    BYTE szDisMsg[200];


    memset(szDisMsg, 0x00, sizeof(szDisMsg));
    strcpy(szDisMsg, pSchemeName);

    usCTOSS_DisplayMp4(szDisMsg);


}
void DisplayDeclined(void)
{	

    BYTE szTitle[20+1];
	BYTE szDisMsg[200];

	
	memset(szDisMsg, 0x00, sizeof(szDisMsg));
	strcpy(szDisMsg, "declined");
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "front");
	
 	usCTOSS_DisplayBmp(szDisMsg);


}



void ProcessingUI(void)
{	

    BYTE szTitle[20+1];
	BYTE szDisMsg[200];

	
	memset(szDisMsg, 0x00, sizeof(szDisMsg));
	strcpy(szDisMsg, "process");
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "front");
	
// 	usCTOSS_DisplayUI(szDisMsg);
    usCTOSS_DisplayUIEx(szDisMsg);


}

/*void PrintReceiptUI(void)
{	

    BYTE szTitle[20+1];
	BYTE szDisMsg[200];

	
	memset(szDisMsg, 0x00, sizeof(szDisMsg));
	strcpy(szDisMsg, "receipt");
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "front");
		
 	usCTOSS_DisplayUI(szDisMsg);


}*/

void PrintReceiptUI(void)
{

    BYTE szTitle[20+1];
    BYTE szReceiptPath[200];
    char szTxnTittleBuf[30] = {0};
    szGetTransTitle(srTransRec.byTransType, szTxnTittleBuf);
    usCTOSS_ClearAllShowTittle(szTxnTittleBuf);
    vdDebug_LogPrintf("PrintReceiptUI");
    memset(szReceiptPath, 0x00, sizeof(szReceiptPath));
    strcpy(szReceiptPath, "/data/data/pub/Print_BMP.bmp");
    usCTOSS_DisplayAnimationBmp(szReceiptPath);

}



void SendAndRecvUI(void)
{	

    BYTE szTitle[20+1];
	BYTE szDisMsg[200];

	
	memset(szDisMsg, 0x00, sizeof(szDisMsg));
	strcpy(szDisMsg, "sendrecv");
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "front");
	
// 	usCTOSS_DisplayUI(szDisMsg);
    usCTOSS_DisplayUIEx(szDisMsg);


}




int inCheckPan(unsigned char* szCurrentPAN, unsigned char* szOriginalPAN)
{
    int inRet = d_OK;
	if (memcmp(szCurrentPAN, szOriginalPAN, strlen(szOriginalPAN)) != 0)
	{
	   TransNotFound();	   
	   vdSetDisplayMsg(1);
	   inRet = d_NO;   
	}
	
	memcpy(srTransRec.szPAN, szOriginalPAN, PAN_SIZE);

	return inRet;
}


int inCTOSS_ClearSwipeCard(void)
{
	BYTE byMSR_status;
	USHORT usTk1Len, usTk2Len, usTk3Len;
	BYTE szTk1Buf[TRACK_I_BYTES], szTk2Buf[TRACK_II_BYTES], szTk3Buf[TRACK_III_BYTES];
	usTk1Len = TRACK_I_BYTES ;
	usTk2Len = TRACK_II_BYTES ;
	usTk3Len = TRACK_III_BYTES ;
	int  usResult;
	char szTotalAmount[AMT_ASC_SIZE+1];

 	vdDebug_LogPrintf("inCTOSS_ClearSwipeCard........");

           
    byMSR_status = CTOS_MSRRead(szTk1Buf, &usTk1Len, szTk2Buf, &usTk2Len, szTk3Buf, &usTk3Len);

	vdDebug_LogPrintf("--CTOS_MSRRead [%ld][%ld]----", byMSR_status, usTk2Len);
	DebugAddHEX("CTOS_MSRRead", szTk2Buf, usTk2Len);

    if((byMSR_status == d_OK ))
    {
        //break;
    }

    return d_OK;
}



char szOriginalPAN[PAN_SIZE+1];

int inCTOS_WaveGetCardFields(void)
{
	USHORT EMVtagLen;
	BYTE   EMVtagVal[64];
	BYTE byKeyBuf = 0;
	BYTE bySC_status;
	BYTE byMSR_status;
	BYTE szTempBuf[10];
	USHORT usTk1Len, usTk2Len, usTk3Len;
	BYTE szTk1Buf[TRACK_I_BYTES], szTk2Buf[TRACK_II_BYTES], szTk3Buf[TRACK_III_BYTES];
	usTk1Len = TRACK_I_BYTES ;
	usTk2Len = TRACK_II_BYTES ;
	usTk3Len = TRACK_III_BYTES ;
	int  usResult;
	ULONG ulAPRtn;
	BYTE temp[64],temp1[64];
	char szTotalAmount[AMT_ASC_SIZE+1];
	EMVCL_RC_DATA_EX stRCDataEx;
	BYTE szOtherAmt[12+1],szTransType[2+1],szCatgCode[4+1],szCurrCode[4+1];
	char szTxnTittleBuf[30] = {0};

	//InsertCardUI();


 	vdDebug_LogPrintf("inCTOS_WaveGetCardFields..byExtReadCard=[%d].......",strTCT.byExtReadCard);
	memset(szOriginalPAN,0,sizeof(szOriginalPAN));
	memcpy(szOriginalPAN, srTransRec.szPAN, PAN_SIZE);

	if(strTCT.byExtReadCard == 1)
	{
		usResult = inCTOS_WaveGetCardFieldsExternal();
		return usResult;
	}


	
#if 1
     if (0 == fGetAppRunBySelf() && inMultiAP_CheckSubAPStatus() == d_OK)
     {
//         if (fECRTxnFlg == 1)//To handle ecr indenpendly as sub app
         if(0)
         {
             vdDebug_LogPrintf("fGetAppRunBySelf is 0, inMultiAP_CheckSubAPStatus is ok,  ecr txn, ignore it");
         }
         else
         return d_OK;
     }
#endif

 
SWIPE_AGAIN:
EntryOfStartTrans:

	
    //szGetTransTitle(srTransRec.byTransType, szTxnTittleBuf);
    //usCTOSS_ClearAllShowTittle(szTxnTittleBuf);
	//InsertCardUI();
	#if 0
	if(strTCT.fManualInput == 1)
    {
		InsertCardUINew("1111");
	}else{
		InsertCardUINew("1110");
	}
	#endif

	if(srTransRec.byTransType == VOID)
    {	
	  PresentCardUIVoid();
	  
	  memset(srTransRec.szPAN, 0x00, sizeof(srTransRec.szPAN));
	  
	  vdDebug_LogPrintf("strlen(srTransRec.szPAN) [%d]", strlen(srTransRec.szPAN) );
    }
	else
	{	
	  PresentCardUI();
	}

	//clear the MSR info before in tap/insert card UI
	inCTOSS_ClearSwipeCard();
	
#if 0
    if(d_OK != inCTOS_ValidFirstIdleKey())
    {
        CTOS_LCDTClearDisplay();
        vdDispTransTitle(srTransRec.byTransType);
     
        inCTOS_DisplayIdleBMP();
    }
#endif
	memset(&stRCDataEx,0x00,sizeof(EMVCL_RC_DATA_EX));
	memset(szOtherAmt,0x00,sizeof(szOtherAmt));
	memset(szTransType,0x00,sizeof(szTransType));
	memset(szCatgCode,0x00,sizeof(szCatgCode));
	memset(szCurrCode,0x00,sizeof(szCurrCode));
	memset(temp,0x00,sizeof(temp));
	memset(szTotalAmount,0x00,sizeof(szTotalAmount));
	wub_hex_2_str(srTransRec.szTotalAmount, szTotalAmount, 6);
	vdDebug_LogPrintf("szTotalAmount[%s].strTCT.szCTLSLimit=[%s]........",szTotalAmount,strTCT.szCTLSLimit);
	if (atol(szTotalAmount) > atol(strTCT.szCTLSLimit))
	{
		CTOS_LCDTPrintXY(1, 3, "Insert/Swipe Card");
		CTOS_Beep();
		CTOS_Delay(500);
		return (inCTOS_GetCardFields());
	}
	CTOS_LCDTClearDisplay();
	//vduiClearBelow(2);//this function will delay the transaction speed
	if(CTLS_V3_SHARECTLS != inCTOSS_GetCtlsMode() && CTLS_V3_INT_SHARECTLS != inCTOSS_GetCtlsMode() && 1 != chGetIdleEventSC_MSR())
	{
		//CTOS_LCDTClearDisplay();
		vdDispTransTitle(srTransRec.byTransType);
		CTOS_LCDTPrintXY(1, 3, "    Present Card   ");
		wub_hex_2_str(srTransRec.szTotalAmount, szTotalAmount, 6);
		
		//format amount 10+2
		memset(temp1,0x00,sizeof(temp1));
		vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szTotalAmount, temp1);
		sprintf(temp," Amount: %s",temp1);
		//sprintf(temp," Amount: %10.0f.%02.0f",(atof(szTotalAmount)/100), (atof(szTotalAmount)%100));
		//sprintf(temp, " Amount: %lu.%02lu", atol(szTotalAmount)/100, atol(szTotalAmount)%100);
		CTOS_LCDTPrintXY(1, 4, temp);
	}
	else if (strTCT.TaxiMode && strTCT.fHFee)
	{
		wub_hex_2_str(srTransRec.szTotalAmount, szTotalAmount, 6);
		displaybmpEx(0, 0, "Ctls.bmp");
		//format amount 10+2
		memset(temp1,0x00,sizeof(temp1));
		vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szTotalAmount, temp1);
		sprintf(temp, "Amount: %s%s", strCST.szCurSymbol, temp1);
		//CTOS_LCDTPrintXY(1, 4, "TAP/INSERT/SWIPE");
		CTOS_LCDTPrintAligned(4, "TAP/INSERT/SWIPE", d_LCD_ALIGNCENTER);
		CTOS_LCDTPrintAligned(5, temp, d_LCD_ALIGNCENTER);
	}
	else
		wub_hex_2_str(srTransRec.szTotalAmount, szTotalAmount, 6);

	if (srTransRec.byTransType == REFUND)
		szTransType[0] = 0x20;

	sprintf(szCatgCode, "%04d", atoi(strCST.szCurCode));
	strcpy(szCurrCode, szCatgCode);

	//if(CTLS_V3_SHARECTLS != inCTOSS_GetCtlsMode())
	if(CTLS_V3_SHARECTLS != inCTOSS_GetCtlsMode() && CTLS_V3_INT_SHARECTLS != inCTOSS_GetCtlsMode() && 1 != chGetIdleEventSC_MSR())
	{
		ulAPRtn = inCTOSS_CLMInitTransaction(szTotalAmount,szOtherAmt,szTransType,szCatgCode,szCurrCode);
	//	ulAPRtn = EMVCL_InitTransaction(atol(szTotalAmount));
		if(ulAPRtn != d_EMVCL_NO_ERROR)
		{
			vdSetErrorMessage("CTLS InitTrans Fail!");
			return d_NO;
		}
	}
 
    CTOS_TimeOutSet (TIMER_ID_1 , GET_CARD_DATA_TIMEOUT_VALUE);

	ing_KeyPressed = 0;
	
    while (1)
    {
        if (CTOS_TimeOutCheck(TIMER_ID_1) == d_YES)
        {
			if(CTLS_V3_SHARECTLS != inCTOSS_GetCtlsMode() && CTLS_V3_INT_SHARECTLS != inCTOSS_GetCtlsMode() && CTLS_V3_INT_SHARECTLS != inCTOSS_GetCtlsMode())
        		inCTOSS_CLMCancelTransaction();

            return READ_CARD_TIMEOUT ;
        }

//        CTOS_KBDInKey(&byKeyBuf);
        CTOS_KBDHit(&byKeyBuf);
		if (byKeyBuf == d_KBD_BACK)
		{
			CTOS_KBDBufFlush();						
		    vdDebug_LogPrintf("byKeyBuf[%d]", byKeyBuf);
			CTOS_KBDBufFlush ();//before return need clear the key buff
			return d_BACK;
		}
		

        vdDebug_LogPrintf("check CTOS_SCStatus");
        CTOS_SCStatus(d_SC_USER, &bySC_status);
        vdDebug_LogPrintf("check CTOS_SCStatus,[%d]", bySC_status);
        if(bySC_status & d_MK_SC_PRESENT)
        {
			if(CTLS_V3_SHARECTLS != inCTOSS_GetCtlsMode() && CTLS_V3_INT_SHARECTLS != inCTOSS_GetCtlsMode())
        		inCTOSS_CLMCancelTransaction();

            vdCTOS_SetTransEntryMode(CARD_ENTRY_ICC);
            
            vdDebug_LogPrintf("--EMV flow----" );
            if (d_OK != inCTOS_EMVCardReadProcess ())
            {
                if(inFallbackToMSR == SUCCESS)
                {
                    vdCTOS_ResetMagstripCardData();
                    vdDisplayErrorMsg(1, 8, "PLS SWIPE CARD");
                    goto SWIPE_AGAIN;
                }
                else
                {
                    //vdSetErrorMessage("Get Card Fail C");
                    return USER_ABORT;
                }
            }
            vdDebug_LogPrintf("--EMV Read succ----" );
            //Load the CDT table
            if (d_OK != inCTOS_LoadCDTIndex())
            {
                CTOS_KBDBufFlush();
                return USER_ABORT;
            }
            
            break;
        }

        //for Idle swipe card
        if (strlen(srTransRec.szPAN) > 0)
         {
         	if(CTLS_V3_SHARECTLS != inCTOSS_GetCtlsMode() && CTLS_V3_INT_SHARECTLS != inCTOSS_GetCtlsMode())
        		inCTOSS_CLMCancelTransaction();

			 vdDebug_LogPrintf("inCTOS_LoadCDTIndex");
             if (d_OK != inCTOS_LoadCDTIndex())
             {
                 CTOS_KBDBufFlush();
                 //vdSetErrorMessage("Get Card Fail");
                 return USER_ABORT;
             }

             if(d_OK != inCTOS_CheckEMVFallback())
             {
                vdCTOS_ResetMagstripCardData();
                vdDisplayErrorMsg(1, 8, "PLS INSERT CARD");
				if (1 == chGetIdleEventSC_MSR())
					return USER_ABORT;
                
                goto SWIPE_AGAIN;

             }
                     
             break;
         
         }
        vdDebug_LogPrintf("--try to do CTOS_MSRRead----" );
        byMSR_status = CTOS_MSRRead(szTk1Buf, &usTk1Len, szTk2Buf, &usTk2Len, szTk3Buf, &usTk3Len);

		vdDebug_LogPrintf("--CTOS_MSRRead [%ld] [%ld] [%ld]----", byMSR_status, d_OK , usTk2Len);
		DebugAddHEX("CTOS_MSRRead", szTk2Buf, usTk2Len);

		//if((byMSR_status == d_OK ) && (usTk2Len > 35))
        if((byMSR_status == d_OK ))
        {
			if(CTLS_V3_SHARECTLS != inCTOSS_GetCtlsMode() && CTLS_V3_INT_SHARECTLS != inCTOSS_GetCtlsMode())
        		inCTOSS_CLMCancelTransaction();

			usResult = shCTOS_SetMagstripCardTrackData(szTk1Buf, usTk1Len, szTk2Buf, usTk2Len, szTk3Buf, usTk3Len); 
			if (usResult != d_OK)
			{
                 CTOS_KBDBufFlush();
				 vdDisplayErrorMsg(1, 8, "READ CARD FAILED");
                 return USER_ABORT;
             }

			
			vdDebug_LogPrintf("inCTOS_LoadCDTIndex");
            if (d_OK != inCTOS_LoadCDTIndex())
             {
                 CTOS_KBDBufFlush();
                 return USER_ABORT;
             }
            
            if(d_OK != inCTOS_CheckEMVFallback())
             {
                vdCTOS_ResetMagstripCardData();
                vdDisplayErrorMsg(1, 8, "PLS INSERT CARD");
				if (1 == chGetIdleEventSC_MSR())
					return USER_ABORT;
                
                goto SWIPE_AGAIN;

             }
                
            break;
        }

		if(CTLS_V3_SHARECTLS != inCTOSS_GetCtlsMode() && CTLS_V3_INT_SHARECTLS != inCTOSS_GetCtlsMode())
		{				
			ulAPRtn = inCTOSS_CLMPollTransaction(&stRCDataEx, 5);

		// V3 contactless reader
//		EMVCL_StopIdleLEDBehavior(NULL);
//		EMVCL_SetLED(0x0F, 0x08);	

// patrick test code 20141230 start
#define d_EMVCL_RC_SEE_PHONE		0xA00000AF
// patrick test code 20141230 end		

			if(ulAPRtn == d_EMVCL_RC_DEK_SIGNAL)
			{
				vdDebug_LogPrintf("DEK Signal Data[%d][%s]", stRCDataEx.usChipDataLen,stRCDataEx.baChipData);
			}
			else if ((ulAPRtn == d_EMVCL_RC_SEE_PHONE) || (ulAPRtn ==d_EMVCL_RC_SEE_PHONE2))
			{
				//CTOS_LCDTClearDisplay();
				vdDisplayErrorMsg(1, 8, "PLEASE SEE PHONE");
				CTOS_Delay(3000);
				goto EntryOfStartTrans;
			}			
			else if(ulAPRtn == d_EMVCL_TX_CANCEL)
			{
				vdDisplayErrorMsg(1, 8, "USER CANCEL");
				return USER_ABORT;
			}
			else if(ulAPRtn == d_EMVCL_RX_TIMEOUT)
			{
				if(CTLS_V3_SHARECTLS != inCTOSS_GetCtlsMode() && CTLS_V3_INT_SHARECTLS != inCTOSS_GetCtlsMode())
	        		inCTOSS_CLMCancelTransaction();

				CTOS_Beep();
			    CTOS_Delay(50);
			    CTOS_Beep();
				CTOS_Delay(50);
				vdDisplayErrorMsg(1, 8, "TIMEOUT");
				return USER_ABORT;
			}
			else if(ulAPRtn != d_EMVCL_PENDING)
			{
				CTOS_Beep();
			    CTOS_Delay(50);
			    CTOS_Beep();
				CTOS_Delay(50);
			    CTOS_Beep();
				vdCTOS_SetTransEntryMode(CARD_ENTRY_WAVE);
				break;
			}
		}
		else
		{
			memset(szTransType,0x00,sizeof(szTransType));
			strcpy(szOtherAmt,"000000000000");
			if (srTransRec.byTransType == REFUND)
				strcpy(szTransType,"20");
			else
				strcpy(szTransType,"00");
			ulAPRtn = usCTOSS_CtlsV3Trans(szTotalAmount,szOtherAmt,szTransType,szCatgCode,szCurrCode,&stRCDataEx);
			CTOS_Beep();
		    CTOS_Delay(50);
		    CTOS_Beep();
			CTOS_Delay(50);
		    CTOS_Beep();
			if(ulAPRtn == d_OK)
			{
				vdCTOS_SetTransEntryMode(CARD_ENTRY_WAVE);
				ulAPRtn = d_EMVCL_RC_DATA;
				break;
			}
			if (ulAPRtn == d_NO)
				return USER_ABORT;

			if (ulAPRtn == d_MORE_PROCESSING)
				return (inCTOS_GetCardFields());
		}
  

	}

	if (srTransRec.byEntryMode == CARD_ENTRY_WAVE)
	{
		if(ulAPRtn != d_EMVCL_RC_DATA)
		{
			vdCTOSS_WaveCheckRtCode(ulAPRtn);
			if(CTLS_V3_SHARECTLS != inCTOSS_GetCtlsMode() && CTLS_V3_INT_SHARECTLS != inCTOSS_GetCtlsMode())
        		inCTOSS_CLMCancelTransaction();

			return d_NO;
		}

		if(CTLS_V3_SHARECTLS != inCTOSS_GetCtlsMode() && CTLS_V3_INT_SHARECTLS != inCTOSS_GetCtlsMode())
		{
			if (d_OK != inCTOSS_WaveAnalyzeTransaction(&stRCDataEx))
			{
				inCTOSS_CLMCancelTransaction();
				return d_NO;
			}
		}
		else
		{
			if (d_OK != inCTOSS_V3AnalyzeTransaction(&stRCDataEx))
				return d_NO;
		}

		//Load the CDT table
		
		vdDebug_LogPrintf("inCTOS_LoadCDTIndex");
        if (d_OK != inCTOS_LoadCDTIndex())
        {
        	if(CTLS_V3_SHARECTLS != inCTOSS_GetCtlsMode() && CTLS_V3_INT_SHARECTLS != inCTOSS_GetCtlsMode())
        		inCTOSS_CLMCancelTransaction();
			
            CTOS_KBDBufFlush();
            return USER_ABORT;
        }

		if(CTLS_V3_SHARECTLS != inCTOSS_GetCtlsMode() && CTLS_V3_INT_SHARECTLS != inCTOSS_GetCtlsMode())
        	inCTOSS_CLMCancelTransaction();

	}

	//CTOS_Beep();
	
    if (srTransRec.byEntryMode == CARD_ENTRY_ICC)
    {    
        EMVtagLen = 0;
/*        if(EMVtagLen > 0)
        {
            sprintf(srTransRec.szCardLable, "%s", EMVtagVal);
        }
        else*/
        {
            strcpy(srTransRec.szCardLable, strIIT.szIssuerLabel);
        }
    }
    else
    {
        strcpy(srTransRec.szCardLable, strIIT.szIssuerLabel);
    }
    srTransRec.IITid = strIIT.inIssuerNumber;

	/*Taxi Mode not support QuickPass For now*/
	if (1 == strTCT.TaxiMode && srTransRec.bWaveSID == d_EMVCL_SID_CUP_QPBOC)	// CUP QuickPass
	{
		vdSetErrorMessage("Card Tap Not Support");
		return d_NO;
	}

	
    vdDebug_LogPrintf("srTransRec.byTransType[%d]srTransRec.IITid[%d], srTransRec.szCardLable=[%s]", srTransRec.byTransType, srTransRec.IITid, srTransRec.szCardLable);
    return d_OK;
}


int inCTOS_PreConnect(void)
{
    int inRetVal;

#if 1	
	if(in_gConnectType == 1)
	{
	   srTransRec.usTerminalCommunicationMode = strCPT.inCommunicationMode;
	   inCTOS_InitComm(srTransRec.usTerminalCommunicationMode) ;
	   return d_OK;
	}

#endif

	 if (1 == inGetConnectedFlag())  //handled by sharls_com 20150722
	 {
		return(d_OK);
	 }

	srTransRec.usTerminalCommunicationMode = strCPT.inCommunicationMode;
    
    vdDebug_LogPrintf("strCPT.inCommunicationMode[%d]",strCPT.inCommunicationMode);
    if (inCTOS_InitComm(srTransRec.usTerminalCommunicationMode) != d_OK) 
    {
        vdSetErrorMessage("COMM INIT ERR");
        return(d_NO);
    }

	if(VS_TRUE == strTCT.fDemo)
		return(d_OK);
	
    inRetVal = inCTOS_CheckInitComm(srTransRec.usTerminalCommunicationMode); 
	if (inRetVal != d_OK)
	{
		if (srTransRec.usTerminalCommunicationMode == GPRS_MODE)
			vdSetErrorMessage("GPRS NOT ESTABLISHED");
		else
        vdSetErrorMessage("COMM INIT ERR");
        return(d_NO);
    }
	
    if (CN_FALSE == srTransRec.byOffline)
    {   
        inRetVal = srCommFuncPoint.inCheckComm(&srTransRec);        
		//for improve transaction speed
    }

    return(d_OK);
}

int inCTOS_CheckAndSelectMutipleMID(void)
{
#define ITEMS_PER_PAGE          4

    char szMMT[50];
    char szDisplay[50];
    int inNumOfRecords = 0;
    short shCount = 0;
    short shTotalPageNum;
    short shCurrentPageNum;
    short shLastPageItems = 0;
    short shPageItems = 0;
    short shLoop;
    short shFalshMenu = 1;
     BYTE isUP = FALSE, isDOWN = FALSE;
	 BYTE bHeaderAttr = 0x01+0x04, iCol = 1;
    BYTE  x = 1;
    BYTE key;
    char szHeaderString[50] = "SELECT MERCHANT";
    char szMitMenu[1024];
    int inLoop = 0;
	short shMinLen = 1;
    short shMaxLen = 20;
    BYTE Bret;
	unsigned char szOutput[30];

    unsigned char bstatus = 0; 

//    if (inMultiAP_CheckSubAPStatus() == d_OK)
   //     return d_OK;


    memset(szMitMenu, 0x00, sizeof(szMitMenu));
    vdDebug_LogPrintf("inCTOS_CheckAndSelectMutipleMID=[%d], srTransRec.MITid=%d",strHDT.inHostIndex, srTransRec.MITid);
    //get the index , then get all MID from the MMT list and prompt to user to select
    if(srTransRec.byTransType == IPP)
    	{
		if (inMMTReadRecord(strHDT.inHostIndex,srTransRec.MITid) == d_OK)
			inNumOfRecords = 1;
    	}
	else
		{
    			inMMTReadNumofRecords(strHDT.inHostIndex,&inNumOfRecords);
		}
	
    vdDebug_LogPrintf("inCTOS_CheckAndSelectMutipleMID inNumOfRecords=[%d]",inNumOfRecords);
	if (inNumOfRecords == 0)
	{
        vdSetErrorMessage("LOAD MMT ERR");
        return(d_NO);
	}
	
	CTOS_KBDBufFlush();//cleare key buffer
    if(inNumOfRecords > 1)
	{
	    for (inLoop = 0; inLoop < inNumOfRecords; inLoop++)
	    {
            strcat((char *)szMitMenu, strMMT[inLoop].szMerchantName);
			if(inLoop + 1 != inNumOfRecords)
            	strcat((char *)szMitMenu, (char *)" \n");
	    }
	        
	    key = MenuDisplay(szHeaderString, strlen(szHeaderString), bHeaderAttr, iCol, x, szMitMenu, TRUE);

		if (key == 0xFF) 
		{
		    CTOS_LCDTClearDisplay();
		    setLCDPrint(1, DISPLAY_POSITION_CENTER, "WRONG INPUT!!!");
		    vduiWarningSound();
		    return -1;  
		}

    	if(key > 0)
	    {
	        if(d_KBD_CANCEL == key)
	            return -1;
	        
	        vdDebug_LogPrintf("key[%d]--fEnablePSWD[%d]-----", key,strMMT[key-1].fEnablePSWD);
			if (strMMT[key-1].fEnablePSWD == 1)
			{
                //CTOS_LCDTClearDisplay();
				vduiClearBelow(2);
                vdDispTransTitle(srTransRec.byTransType);
                CTOS_LCDTPrintXY(1, 3,"ENTER PASSWORD:");
				while (1)
				{
					memset(szOutput,0x00,sizeof(szOutput));			
					shMinLen = strlen(strMMT[key-1].szPassWord);
					
	                Bret = InputString(1, 4, 0x01, 0x02,szOutput, &shMaxLen, shMinLen, 12, d_INPUT_TIMEOUT);
	                if(strncmp(szOutput,strMMT[key-1].szPassWord,shMinLen) == 0)
	                {       
	                    break;
	                }
	                else if(d_KBD_CANCEL == Bret)
	                    return Bret;
	                else 
	                {
	                    vdDisplayErrorMsg(1, 8, "WRONG PASSWORD");
						//CTOS_LCDTClearDisplay();
						vduiClearBelow(2);
		                vdDispTransTitle(srTransRec.byTransType);
		                CTOS_LCDTPrintXY(1, 3,"ENTER PASSWORD:");
	                }
				}
			}
			
			memcpy(&strMMT[0],&strMMT[key-1],sizeof(STRUCT_MMT));
	    }
	}

    srTransRec.MITid = strMMT[0].MITid;
    strcpy(srTransRec.szTID, strMMT[0].szTID);
    strcpy(srTransRec.szMID, strMMT[0].szMID);
    memcpy(srTransRec.szBatchNo, strMMT[0].szBatchNo, 4);
    strcpy(srTransRec.szHostLabel, strHDT.szHostLabel);

    vdDebug_LogPrintf("szATCMD1=[%s] [%s] [%s] [%s] [%s]",strMMT[0].szATCMD1,strMMT[0].szATCMD2,strMMT[0].szATCMD3,strMMT[0].szATCMD4,strMMT[0].szATCMD5);
    
    vdDebug_LogPrintf("inCTOS_CheckAndSelectMutipleMID srTransRec.MITid=[%d], srTransRec.szTID[%s], srTransRec.szMID[%s], srTransRec.szBatchNo[%s], srTransRec.szHostLabel[%s]",srTransRec.MITid, srTransRec.szTID, srTransRec.szMID, srTransRec.szBatchNo, srTransRec.szHostLabel);

    inCTOS_GenerateOrderNum(srTransRec.szOrderNum, srTransRec.szTID);
    return SUCCESS;
}

int inCTOS_CheckAndSelectMID(void)
{
#define ITEMS_PER_PAGE          4

    char szMMT[50];
    char szDisplay[50];
    int inNumOfRecords = 0;
    short shCount = 0;
    short shTotalPageNum;
    short shCurrentPageNum;
    short shLastPageItems = 0;
    short shPageItems = 0;
    short shLoop;
    
    unsigned char key;
    unsigned char bstatus = 0; 

    DebugAddSTR("inCTOS_CheckAndSelectMutipleMID","Processing...",20);
    
    //get the index , then get all MID from the MMT list and prompt to user to select
    inMMTReadNumofRecords(srTransRec.HDTid,&inNumOfRecords);
	
    vdDebug_LogPrintf("inCTOS_CheckAndSelectMutipleMID inNumOfRecords=[%d]",inNumOfRecords);
	if (inNumOfRecords == 0)
	{
        vdSetErrorMessage("LOAD MMT ERR");
        return(d_NO);
	}
    shCurrentPageNum = 1;
    CTOS_KBDBufFlush();//cleare key buffer
    if(inNumOfRecords > 1)
    {
        shTotalPageNum = (inNumOfRecords/ITEMS_PER_PAGE == 0) ? (inNumOfRecords/ITEMS_PER_PAGE) :(inNumOfRecords/ITEMS_PER_PAGE + 1); 
        shLastPageItems = (inNumOfRecords/ITEMS_PER_PAGE == 0) ? (ITEMS_PER_PAGE) : (inNumOfRecords%ITEMS_PER_PAGE);
        
        do
        {
                //display items perpage
                if(shTotalPageNum == 0)//the total item is amaller than ITEMS_PER_PAGE
                {
                    shPageItems = inNumOfRecords;
                }
                else if(shCurrentPageNum == shTotalPageNum)//Last page
                    shPageItems = shLastPageItems;
                else
                    shPageItems = ITEMS_PER_PAGE;
                
                CTOS_LCDTClearDisplay();
                for(shLoop=0 ; shLoop < shPageItems/*ITEMS_PER_PAGE*/ ; shLoop++)
                {
                
                    memset(szDisplay,0,sizeof(szDisplay));
                    sprintf(szDisplay,"%d: %s",shLoop+1,strMMT[shLoop + (shCurrentPageNum -1)*ITEMS_PER_PAGE].szMerchantName);
                    CTOS_LCDTPrintXY(2,shLoop + 2,szDisplay);

                }
                
                key=WaitKey(60);
                
                switch(key)
                {
                    case d_KBD_DOWN:
                        
                        shCurrentPageNum ++;
                        if(shCurrentPageNum > shTotalPageNum)
                            shCurrentPageNum = 1;
                        bstatus = 2;
                        break;
                        
                        
                    case d_KBD_UP:

                        shCurrentPageNum --;
                        if(shCurrentPageNum < 1)
                            shCurrentPageNum = shTotalPageNum;
                        bstatus = 2;        
                        break;          
                        
                    case d_KBD_CANCEL:                          
                        return FAIL;
                    case d_KBD_1:
                        //set the unique MMT num
                                            
                        memcpy(&strMMT[0],&strMMT[0 + (shCurrentPageNum -1)*ITEMS_PER_PAGE],sizeof(STRUCT_MMT));
                        //vduiDisplayStringCenter(2,strMMT[0].szMID);
                        bstatus = 0;
                        break;
                    case d_KBD_2:
                        if(shPageItems < 2)
                        {
                            bstatus = -1;
                        }
                        else
                        {
                            memcpy(&strMMT[0],&strMMT[1 + (shCurrentPageNum -1)*ITEMS_PER_PAGE],sizeof(STRUCT_MMT));
                            bstatus = 0;
                        }
                        break;
                    case d_KBD_3:   
                        if(shPageItems < 3)
                        {
                            bstatus = -1;
                        }
                        else
                        {
                            memcpy(&strMMT[0],&strMMT[2 + (shCurrentPageNum -1)*ITEMS_PER_PAGE],sizeof(STRUCT_MMT));
                            bstatus = 0;
                        }
                        break;
                                            
                    case d_KBD_4:
                        if(shPageItems < 4)
                        {
                            bstatus = -1;
                        }
                        else
                        {
                            memcpy(&strMMT[0],&strMMT[3 + (shCurrentPageNum -1)*ITEMS_PER_PAGE],sizeof(STRUCT_MMT));
                            bstatus = 0;
                        }
                        break;
                    case d_KBD_5:
                        if(ITEMS_PER_PAGE < 5)
                        {
                            bstatus = -1 ;
                            break;
                        }
                        else
                        {
                            if(shPageItems < 5)
                            {
                                bstatus = -1;
                            }
                            else
                            {
                                memcpy(&strMMT[0],&strMMT[4 + (shCurrentPageNum -1)*ITEMS_PER_PAGE],sizeof(STRUCT_MMT));
                                bstatus = 0;
                            }
                            break;

                        }
                    case d_KBD_6:
                        if(ITEMS_PER_PAGE < 6)
                        {
                            bstatus = -1 ;
                            break;
                        }
                        else
                        {
                            if(shPageItems < 6)
                            {
                                bstatus = -1;
                            }
                            else
                            {
                                memcpy(&strMMT[0],&strMMT[5 + (shCurrentPageNum -1)*ITEMS_PER_PAGE],sizeof(STRUCT_MMT));
                                bstatus = 0;
                            }
                            break;

                        }
                    case d_KBD_7:
                        if(ITEMS_PER_PAGE < 7)
                        {
                            bstatus = -1 ;
                            break;
                        }
                        else
                        {
                            if(shPageItems < 7)
                            {
                                bstatus = -1;
                            }
                            else
                            {
                                memcpy(&strMMT[0],&strMMT[6 + (shCurrentPageNum -1)*ITEMS_PER_PAGE],sizeof(STRUCT_MMT));
                                bstatus = 0;
                            }
                            break;

                        }
                    case d_KBD_8:   //Max 8 items for one page
                        if(ITEMS_PER_PAGE < 8)
                        {
                            bstatus = -1 ;
                            break;
                        }
                        else
                        {
                            if(shPageItems < 8)
                            {
                                bstatus = -1;
                            }
                            else
                            {
                                memcpy(&strMMT[0],&strMMT[7 + (shCurrentPageNum -1)*ITEMS_PER_PAGE],sizeof(STRUCT_MMT));
                                bstatus = 0;
                            }
                            break;

                        }
                    default:
                        bstatus = -1 ;
                        break;

                }
                
                if((-1) == bstatus)
                {
                    return FAIL;
                    
                }
                else if(0 == bstatus)
                {
                    break;
                }
                
            }while(1);

    }
    else
    {
        //One merchant only     
        //vduiDisplayStringCenter(1,strMMT[0].szMID);
        
    }

    srTransRec.MITid = strMMT[0].MITid;
    return SUCCESS;
    

}


int inCTOS_GetTxnPassword(void)
{
#define NO_PW           0
#define SUPER_PW            1
#define SYSTERM_PW          2
#define ENGINEERPW         3
#define TXN_PW         9    //individual password
    DebugAddSTR("inCTOS_GetTxnPassword","Processing...",20);


    unsigned char szOutput[30],szDisplay[30];
    int  inTxnTypeID;
    short ret = 0;
    short shMinLen = 4;
    short shMaxLen = 12;
    BYTE key;
    BYTE Bret;
    short shCount =0;
    short shRsesult = d_OK;
    
    if (0 == fGetAppRunBySelf() && inMultiAP_CheckSubAPStatus() == d_OK)
        return d_OK;
	
    //if (srTransRec.byTransType == PAY_CASH && 1 == fGetAppRunBySelf() && inMultiAP_CheckSubAPStatus() == d_OK)
    //	return d_OK;
	
    memset(szOutput,0,sizeof(szOutput));
    memset(szDisplay,0,sizeof(szDisplay));
    inTxnTypeID = srTransRec.byTransType;

    vduiLightOn();
    
    inPITRead(inTxnTypeID);
    if (NO_PW== strPIT.inPasswordLevel)
    {
        return d_OK;
    }

    CTOS_KBDHit(&key);//clear key buffer
    while(shCount < 3)
    {
    	shMaxLen = 12;
        switch(srTransRec.byTransType)
        {
            case SALE:
            case PRE_AUTH:
			case PRE_COMP:
            case REFUND:
            case SALE_OFFLINE:
            case EPP_SALE:
            case VOID:
            case SALE_TIP:
            case SALE_ADJUST:
            case SETTLE:
            case BATCH_REVIEW:
            case BATCH_TOTAL:
	     	case PAY_CASH:
			case VOID_PREAUTH:
			case CLS_BATCH:
			case SETUP: /*sidumili: Issue#:000087 [prompt password]*/
                if (TXN_PW== strPIT.inPasswordLevel)
                {
                    vdDebug_LogPrintf("individual pw,strPIT.szTxnPW[%s]", strPIT.szTxnPW);
                    if(strcmp("9999",strPIT.szTxnPW) == 0)
                    {
                        return d_OK;
                    }

                    //input
                    CTOS_LCDTClearDisplay();
                    //vduiClearBelow(2);
                    vdDispTransTitle(srTransRec.byTransType);
                    usCTOSS_ClearAllExceptTittle();
                    CTOS_LCDTPrintXY(1, 3,"ENTER PASSWORD:");
                    Bret = InputString(1, 4, 0x02, 0x02, szOutput, &shMaxLen, shMinLen, 12, d_INPUT_TIMEOUT);
                    if (Bret == 255) //timeout
                        return Bret;
                    else if(d_KBD_CANCEL == Bret)
                        return Bret;
                    else if(strcmp(szOutput,strPIT.szTxnPW) == 0)
                    {
                        return d_OK;
                    }
                    else
                    {
                        vdDisplayErrorMsg(1, 8, "WRONG PASSWORD");
                        shRsesult = FAIL;
                        break;
                    }
                }
                if(SUPER_PW== strPIT.inPasswordLevel)
                {
                    CTOS_LCDTClearDisplay();
					//vduiClearBelow(2);
                    vdDispTransTitle(srTransRec.byTransType);
                    usCTOSS_ClearAllExceptTittle();

                    CTOS_LCDTPrintXY(1, 3,"ENTER PASSWORD:");

					
					
					if (strTCT.byTerminalType == 8) //UPT1000F
					{						
//					   Bret = usCTOSS_VirtualStringGetEx(1, 4, 0x01, 0x02, szOutput, &shMaxLen, shMinLen, d_INPUT_TIMEOUT);
					   CTOS_LCDTClearDisplay();
					}
					else
					{   
                       Bret = InputString(1, 4, 0x02, 0x02, szOutput, &shMaxLen, shMinLen, 12,d_INPUT_TIMEOUT);
					}
										//issue:285
										if (Bret == 255) //timeout
										    return Bret;
									  //issue:285

										//#issue:231
										if(d_KBD_CANCEL == Bret)
                    	return Bret;
										else
										//#issue:231
                    if((strcmp(szOutput,strTCT.szSuperPW) == 0) || 
                        (strcmp(szOutput,strTCT.szSystemPW) == 0)||
                        (strcmp(szOutput,strTCT.szEngineerPW) == 0))
                    {       
                        return d_OK;
                    }
										//#issue:23
                    /*else if(d_KBD_CANCEL == Bret)
                        			return Bret;*/
										//#issue:23
                    else 
                    {
                        //CTOS_LCDTClearDisplay();
						//vduiClearBelow(2);
                        vdDisplayErrorMsg(1, 8, "WRONG PASSWORD");
                        shRsesult = FAIL;
                        break;
                    }
                }
                else if(SYSTERM_PW== strPIT.inPasswordLevel)
                {
                    CTOS_LCDTClearDisplay();
					//vduiClearBelow(2);
                    vdDispTransTitle(srTransRec.byTransType);
                    usCTOSS_ClearAllExceptTittle();
                    CTOS_LCDTPrintXY(1, 3,"SYSTEM PASSWORD:");
					
					if (strTCT.byTerminalType == 8) //UPT1000F
					{						
//					   Bret = usCTOSS_VirtualStringGetEx(1, 4, 0x01, 0x02,szOutput, &shMaxLen, shMinLen, d_INPUT_TIMEOUT);
					   CTOS_LCDTClearDisplay();

					}
					else
					{
                      Bret = InputString(1, 4, 0x02, 0x02,szOutput, &shMaxLen, shMinLen, 12,d_INPUT_TIMEOUT);
					}
										//issue:285
										if (Bret == 255) //timeout
										    return Bret;
									  //issue:285

										//#issue:231
										if(d_KBD_CANCEL == Bret)
                    	return Bret;
										else
										//#issue:231

                    if( (strcmp(szOutput,strTCT.szSystemPW) == 0) ||
                        (strcmp(szOutput,strTCT.szEngineerPW) == 0)
                        )
                    {       
                        return d_OK;
                    }
										//#issue:23
                    /*else if(d_KBD_CANCEL == Bret)
                        			return Bret;*/
										//#issue:23
                    else 
                    {
                        //CTOS_LCDTClearDisplay();
						//vduiClearBelow(2);
                        vdDisplayErrorMsg(1, 8, "WRONG PASSWORD");
                        shRsesult = FAIL;                       
                        break;
                    }
                }   
                else if(ENGINEERPW== strPIT.inPasswordLevel)            
                {
                    CTOS_LCDTClearDisplay();
					//vduiClearBelow(2);
                    vdDispTransTitle(srTransRec.byTransType);
                    usCTOSS_ClearAllExceptTittle();
					CTOS_LCDTPrintXY(1, 3,"PASSWORD:");

					if (strTCT.byTerminalType == 8) //UPT1000F
					{						
//					   Bret = usCTOSS_VirtualStringGetEx(1, 4, 0x01, 0x02,szOutput, &shMaxLen, shMinLen, d_INPUT_TIMEOUT);
					   CTOS_LCDTClearDisplay();

					}
					else
					{
                       Bret = InputString(1, 4, 0x02, 0x02,szOutput, &shMaxLen, shMinLen, 12,d_INPUT_TIMEOUT);
					}					//issue:285
										if (Bret == 255) //timeout
										    return Bret;
									  //issue:285
										
										//#issue:231
										if(d_KBD_CANCEL == Bret)
                    	return Bret;
										else
										//#issue:231

                    if(strcmp(szOutput,strTCT.szEngineerPW) == 0)
                    {       
                        return d_OK;
                    }
										//#issue:23
                    /*else if(d_KBD_CANCEL == Bret)
                        			return Bret;*/
										//#issue:23
                    {
                        CTOS_LCDTClearDisplay();    
                        vdDisplayErrorMsg(1, 8, "WRONG PASSWORD");
                        shRsesult = FAIL;                       
                        break;
                    }

                }
                else
                {            
                    return d_OK;
                }
                

            default:
                
                return d_OK;        

        }
        if(FAIL == shRsesult)
            shCount ++ ;

		//reset lengths
		shMinLen = 4;
		shMaxLen = 12;
    }

    return shRsesult;
}

int inCTOS_GetTxnPasswordByLevel(void)
{
#define NO_PW           0
#define SUPER_PW            1
#define SYSTERM_PW          2
#define ENGINEERPW         3

    DebugAddSTR("inCTOS_GetTxnPasswordByLevel","Processing...",20);


    unsigned char szOutput[30],szDisplay[30];
    int  inTxnTypeID;
    short ret = 0;
    short shMinLen = 4;
    short shMaxLen = 12;
    BYTE key;
    BYTE Bret;
    short shCount =0;
    short shRsesult = d_OK;
    
    if (0 == fGetAppRunBySelf() && inMultiAP_CheckSubAPStatus() == d_OK)
        return d_OK;
	
    //if (srTransRec.byTransType == PAY_CASH && 1 == fGetAppRunBySelf() && inMultiAP_CheckSubAPStatus() == d_OK)
    //	return d_OK;
	
    memset(szOutput,0,sizeof(szOutput));
    memset(szDisplay,0,sizeof(szDisplay));
    inTxnTypeID = srTransRec.byTransType;

    vduiLightOn();
    
    inPITRead(inTxnTypeID);
    if (NO_PW== strPIT.inPasswordLevel)
    {
        return d_OK;
    }
    
    CTOS_KBDHit(&key);//clear key buffer
    while(shCount < 3)
    {
    	shMaxLen = 12;
        switch(srTransRec.byTransType)
        {
            case SALE:
            case PRE_AUTH:
			case PRE_COMP:
            case REFUND:
            case SALE_OFFLINE:
            case EPP_SALE:
            case VOID:
            case SALE_TIP:
            case SALE_ADJUST:
            case SETTLE:
            case BATCH_REVIEW:
            case BATCH_TOTAL:
	     case PAY_CASH:
		case VOID_PREAUTH: 	
		case SETUP: /*sidumili: Issue#:000087 [prompt password]*/
                
                if(SUPER_PW== strPIT.inPasswordLevel)
                {
                    CTOS_LCDTClearDisplay();
					//vduiClearBelow(2);
                    vdDispTransTitle(srTransRec.byTransType);
                    CTOS_LCDTPrintXY(1, 3,"ENTER PASSWORD:");

					
					
					if (strTCT.byTerminalType == 8) //UPT1000F
					{						
//					   Bret = usCTOSS_VirtualStringGetEx(1, 4, 0x01, 0x02, szOutput, &shMaxLen, shMinLen, d_INPUT_TIMEOUT);
					   CTOS_LCDTClearDisplay();
					}
					else
					{   
                       Bret = InputString(1, 4, 0x01, 0x02, szOutput, &shMaxLen, shMinLen, 12,d_INPUT_TIMEOUT);
					}
										//issue:285
										if (Bret == 255) //timeout
										    return Bret;
									  //issue:285

										//#issue:231
										if(d_KBD_CANCEL == Bret)
                    	return Bret;
										else
										//#issue:231
                    if(strcmp(szOutput,strTCT.szSuperPW) == 0)
                    {       
                        return d_OK;
                    }
										//#issue:23
                    /*else if(d_KBD_CANCEL == Bret)
                        			return Bret;*/
										//#issue:23
                    else 
                    {
                        //CTOS_LCDTClearDisplay();
						//vduiClearBelow(2);
                        vdDisplayErrorMsg(1, 8, "WRONG PASSWORD");
                        shRsesult = FAIL;
                        break;
                    }
                }
                else if(SYSTERM_PW== strPIT.inPasswordLevel)
                {
                    CTOS_LCDTClearDisplay();
					//vduiClearBelow(2);
                    vdDispTransTitle(srTransRec.byTransType);
                    CTOS_LCDTPrintXY(1, 3,"SYSTEM PASSWORD:");
					
					if (strTCT.byTerminalType == 8) //UPT1000F
					{						
//					   Bret = usCTOSS_VirtualStringGetEx(1, 4, 0x01, 0x02,szOutput, &shMaxLen, shMinLen, d_INPUT_TIMEOUT);
					   CTOS_LCDTClearDisplay();

					}
					else
					{
                      Bret = InputString(1, 4, 0x01, 0x02,szOutput, &shMaxLen, shMinLen, 12,d_INPUT_TIMEOUT);
					}
										//issue:285
										if (Bret == 255) //timeout
										    return Bret;
									  //issue:285

										//#issue:231
										if(d_KBD_CANCEL == Bret)
                    	return Bret;
										else
										//#issue:231

                    if(strcmp(szOutput,strTCT.szSystemPW) == 0)
                    {       
                        return d_OK;
                    }
										//#issue:23
                    /*else if(d_KBD_CANCEL == Bret)
                        			return Bret;*/
										//#issue:23
                    else 
                    {
                        //CTOS_LCDTClearDisplay();
						//vduiClearBelow(2);
                        vdDisplayErrorMsg(1, 8, "WRONG PASSWORD");
                        shRsesult = FAIL;                       
                        break;
                    }
                }   
                else if(ENGINEERPW== strPIT.inPasswordLevel)            
                {
                    CTOS_LCDTClearDisplay();
					//vduiClearBelow(2);
                    vdDispTransTitle(srTransRec.byTransType);
					CTOS_LCDTPrintXY(1, 3,"PASSWORD:");

					if (strTCT.byTerminalType == 8) //UPT1000F
					{						
//					   Bret = usCTOSS_VirtualStringGetEx(1, 4, 0x01, 0x02,szOutput, &shMaxLen, shMinLen, d_INPUT_TIMEOUT);
					   CTOS_LCDTClearDisplay();

					}
					else
					{
                       Bret = InputString(1, 4, 0x01, 0x02,szOutput, &shMaxLen, shMinLen, 12,d_INPUT_TIMEOUT);
					}					//issue:285
										if (Bret == 255) //timeout
										    return Bret;
									  //issue:285
										
										//#issue:231
										if(d_KBD_CANCEL == Bret)
                    	return Bret;
										else
										//#issue:231

                    if(strcmp(szOutput,strTCT.szEngineerPW) == 0)
                    {       
                        return d_OK;
                    }
										//#issue:23
                    /*else if(d_KBD_CANCEL == Bret)
                        			return Bret;*/
										//#issue:23
                    {
                        CTOS_LCDTClearDisplay();    
                        vdDisplayErrorMsg(1, 8, "WRONG PASSWORD");
                        shRsesult = FAIL;                       
                        break;
                    }

                }
                else
                {            
                    return d_OK;
                }
                

            default:
                
                return d_OK;        

        }
        if(FAIL == shRsesult)
            shCount ++ ;

		//reset lengths
		shMinLen = 4;
		shMaxLen = 12;
    }

    return shRsesult;
}

// title from parameter, not based on trans type. This is for the case trans type not in PIT
int inCTOS_GetTxnPasswordEx(BYTE * szTitle)
{
#define NO_PW           0
#define SUPER_PW            1
#define SYSTERM_PW          2
#define ENGINEERPW         3

    DebugAddSTR("inCTOS_GetTxnPasswordEx","Processing...",20);


    unsigned char szOutput[30],szDisplay[30];
    int  inTxnTypeID;
    short ret = 0;
    short shMinLen = 4;
    short shMaxLen = 12;
    BYTE key;
    BYTE Bret;
    short shCount =0;
    short shRsesult = d_OK;
    
    if (0 == fGetAppRunBySelf() && inMultiAP_CheckSubAPStatus() == d_OK)
        return d_OK;
	
    //if (srTransRec.byTransType == PAY_CASH && 1 == fGetAppRunBySelf() && inMultiAP_CheckSubAPStatus() == d_OK)
    //	return d_OK;
	
    memset(szOutput,0,sizeof(szOutput));
    memset(szDisplay,0,sizeof(szDisplay));
    inTxnTypeID = srTransRec.byTransType;

    vduiLightOn();
    
    inPITRead(inTxnTypeID);
    if (NO_PW== strPIT.inPasswordLevel)
    {
        return d_OK;
    }
    
    CTOS_KBDHit(&key);//clear key buffer
    while(shCount < 3)
    {
    	shMaxLen = 12;
        switch(srTransRec.byTransType)
        {
            case SALE:
            case PRE_AUTH:
			case PRE_COMP:
            case REFUND:
            case SALE_OFFLINE:
            case EPP_SALE:
            case VOID:
            case SALE_TIP:
            case SALE_ADJUST:
            case SETTLE:
            case BATCH_REVIEW:
            case BATCH_TOTAL:
	     case PAY_CASH:
		case VOID_PREAUTH: 	
		case SETUP: /*sidumili: Issue#:000087 [prompt password]*/
                
                if(SUPER_PW== strPIT.inPasswordLevel)
                {
                    CTOS_LCDTClearDisplay();
					//vduiClearBelow(2);
                    vdDispTitleString(szTitle);//vdDispTransTitle(srTransRec.byTransType);
                    CTOS_LCDTPrintXY(1, 3,"ENTER PASSWORD:");

					
					
					if (strTCT.byTerminalType == 8) //UPT1000F
					{						
//					   Bret = usCTOSS_VirtualStringGetEx(1, 4, 0x01, 0x02, szOutput, &shMaxLen, shMinLen, d_INPUT_TIMEOUT);
					   CTOS_LCDTClearDisplay();
					}
					else
					{   
                       Bret = InputString(1, 4, 0x01, 0x02, szOutput, &shMaxLen, shMinLen, 12,d_INPUT_TIMEOUT);
					}
										//issue:285
										if (Bret == 255) //timeout
										    return Bret;
									  //issue:285

										//#issue:231
										if(d_KBD_CANCEL == Bret)
                    	return Bret;
										else
										//#issue:231
                    if((strcmp(szOutput,strTCT.szSuperPW) == 0) || 
                        (strcmp(szOutput,strTCT.szSystemPW) == 0)||
                        (strcmp(szOutput,strTCT.szEngineerPW) == 0))
                    {       
                        return d_OK;
                    }
										//#issue:23
                    /*else if(d_KBD_CANCEL == Bret)
                        			return Bret;*/
										//#issue:23
                    else 
                    {
                        //CTOS_LCDTClearDisplay();
						//vduiClearBelow(2);
                        vdDisplayErrorMsg(1, 8, "WRONG PASSWORD");
                        shRsesult = FAIL;
                        break;
                    }
                }
                else if(SYSTERM_PW== strPIT.inPasswordLevel)
                {
                    CTOS_LCDTClearDisplay();
					//vduiClearBelow(2);
                    vdDispTitleString(szTitle);//vdDispTransTitle(srTransRec.byTransType);
                    CTOS_LCDTPrintXY(1, 3,"SYSTEM PASSWORD:");
					
					if (strTCT.byTerminalType == 8) //UPT1000F
					{						
//					   Bret = usCTOSS_VirtualStringGetEx(1, 4, 0x01, 0x02,szOutput, &shMaxLen, shMinLen, d_INPUT_TIMEOUT);
					   CTOS_LCDTClearDisplay();

					}
					else
					{
                      Bret = InputString(1, 4, 0x01, 0x02,szOutput, &shMaxLen, shMinLen, 12,d_INPUT_TIMEOUT);
					}
										//issue:285
										if (Bret == 255) //timeout
										    return Bret;
									  //issue:285

										//#issue:231
										if(d_KBD_CANCEL == Bret)
                    	return Bret;
										else
										//#issue:231

                    if( (strcmp(szOutput,strTCT.szSystemPW) == 0) ||
                        (strcmp(szOutput,strTCT.szEngineerPW) == 0)
                        )
                    {       
                        return d_OK;
                    }
										//#issue:23
                    /*else if(d_KBD_CANCEL == Bret)
                        			return Bret;*/
										//#issue:23
                    else 
                    {
                        //CTOS_LCDTClearDisplay();
						//vduiClearBelow(2);
                        vdDisplayErrorMsg(1, 8, "WRONG PASSWORD");
                        shRsesult = FAIL;                       
                        break;
                    }
                }   
                else if(ENGINEERPW== strPIT.inPasswordLevel)            
                {
                    CTOS_LCDTClearDisplay();
					//vduiClearBelow(2);
                    vdDispTitleString(szTitle);//vdDispTransTitle(srTransRec.byTransType);
					CTOS_LCDTPrintXY(1, 3,"PASSWORD:");

					if (strTCT.byTerminalType == 8) //UPT1000F
					{						
//					   Bret = usCTOSS_VirtualStringGetEx(1, 4, 0x01, 0x02,szOutput, &shMaxLen, shMinLen, d_INPUT_TIMEOUT);
					   CTOS_LCDTClearDisplay();

					}
					else
					{
                       Bret = InputString(1, 4, 0x01, 0x02,szOutput, &shMaxLen, shMinLen, 12,d_INPUT_TIMEOUT);
					}					//issue:285
										if (Bret == 255) //timeout
										    return Bret;
									  //issue:285
										
										//#issue:231
										if(d_KBD_CANCEL == Bret)
                    	return Bret;
										else
										//#issue:231

                    if(strcmp(szOutput,strTCT.szEngineerPW) == 0)
                    {       
                        return d_OK;
                    }
										//#issue:23
                    /*else if(d_KBD_CANCEL == Bret)
                        			return Bret;*/
										//#issue:23
                    {
                        CTOS_LCDTClearDisplay();    
                        vdDisplayErrorMsg(1, 8, "WRONG PASSWORD");
                        shRsesult = FAIL;                       
                        break;
                    }

                }
                else
                {            
                    return d_OK;
                }
                

            default:
                
                return d_OK;        

        }
        if(FAIL == shRsesult)
            shCount ++ ;

		//reset lengths
		shMinLen = 4;
		shMaxLen = 12;
    }

    return shRsesult;
}

int inCTOS_SelectHost(void) 
{
    short shGroupId ;
    int inHostIndex;
    short shCommLink;
    int inCurrencyIdx=0;
	char szAPName[100];
	int inAPPID, i,inNumOfHDTRecord;

    if(srTransRec.byTransType == IPP)
    {
        inHostIndex = UOB_IPP_HDT;
    }
    else
        inHostIndex = (short) strCDT.HDTid;

	vdDebug_LogPrintf("inCTOS_SelectHost");
	if ((srTransRec.byTransType == EFTSEC_TMK)||(srTransRec.byTransType == EFTSEC_TWK)||(srTransRec.byTransType == EFTSEC_TMK_RSA)||(srTransRec.byTransType == EFTSEC_TWK_RSA))
	{
		inHDTRead(strEFT[0].inHDTid);
/*
		inMultiAP_CurrentAPNamePID(szAPName, &inAPPID);
		inNumOfHDTRecord = inHDTNumRecord();
		for(i=0; i <= inNumOfHDTRecord; i++)
		{
			inHDTRead(i);
			if(memcmp(strHDT.szHostLabel, "EFTSEC", 6) == 0)
			{
				inEFTReadByHDTid(i);
				break;
			}
		}
*/	
		inHostIndex = strHDT.inHostIndex;
	}

     vdDebug_LogPrintf("inCTOS_SelectHost =[%d]",inHostIndex);
    
     if (0 == fGetAppRunBySelf() && inMultiAP_CheckSubAPStatus() == d_OK)
     {
         vdDebug_LogPrintf("fGetAppRunBySelf is 0 and inMultiAP_CheckSubAPStatus is ok");
//         if (fECRTxnFlg == 1)//To handle ecr indenpendly as sub app
         if(0)
         {
             vdDebug_LogPrintf("ecr txn, ignore it");
         }
         else
         return d_OK;
     }

    vdDebug_LogPrintf("inHDTRead =[%d]",inHostIndex);
    if ( inHDTRead(inHostIndex) != d_OK)
    {
        vdSetErrorMessage("HOST SELECTION ERR");
        return(d_NO);
    } 
    else 
    {
        
        srTransRec.HDTid = inHostIndex;

        inCurrencyIdx = strHDT.inCurrencyIdx;

        if (inCSTRead(inCurrencyIdx) != d_OK) 
        {
            vdSetErrorMessage("LOAD CST ERR");
            return(d_NO);
        }


        if ( inCPTRead(inHostIndex) != d_OK)
        {
            vdSetErrorMessage("LOAD CPT ERR");
            return(d_NO);
        }

        return (d_OK);
    }
}


int inCTOS_SelectHostEx(void) 
{
    short shGroupId ;
    int inHostIndex;
    short shCommLink;
    int inCurrencyIdx=0;
	char szAPName[100];
	int inAPPID, i,inNumOfHDTRecord;

    
    inHostIndex = 99;

    vdDebug_LogPrintf("inCTOS_SelectHostEx =[%d]",inHostIndex);
    
     if (0 == fGetAppRunBySelf() && inMultiAP_CheckSubAPStatus() == d_OK)
     {
         vdDebug_LogPrintf("fGetAppRunBySelf is 0 and inMultiAP_CheckSubAPStatus is ok");
//         if (fECRTxnFlg == 1)//To handle ecr indenpendly as sub app
         if(0)
         {
             vdDebug_LogPrintf("ecr txn, ignore it");
         }
         else
         return d_OK;
     }

    vdDebug_LogPrintf("inHDTRead =[%d]",inHostIndex);
    if ( inHDTRead(inHostIndex) != d_OK)
    {
        vdSetErrorMessage("HOST SELECTION ERR");
        return(d_NO);
    } 
    else 
    {
        
        srTransRec.HDTid = inHostIndex;

        if ( inCPTRead(inHostIndex) != d_OK)
        {
            vdSetErrorMessage("LOAD CPT ERR");
            return(d_NO);
        }else{
			
				vdDebug_LogPrintf("strCPT.szPriTxnHostIP=[%s]",strCPT.szPriTxnHostIP);
				vdDebug_LogPrintf("strCPT.inPriTxnHostPortNum=[%d]",strCPT.inPriTxnHostPortNum);
		}
		

        return (d_OK);
    }
}

int inCTOS_getCard4DBC(BYTE *baBuf)
{
    USHORT usRet;
    USHORT usInputLen;
    USHORT usLens;
    USHORT usMinLen = 4;
    USHORT usMaxLen = 4;
    BYTE bBuf[4+1];
    BYTE bDisplayStr[MAX_CHAR_PER_LINE+1];

	if (isCheckTerminalMP200() == d_OK)
	{
		CTOS_LCDTClearDisplay();
	}
	else
	{
		vduiClearBelow(2);
	}
    vdDispTransTitle(srTransRec.byTransType);

    if(CARD_ENTRY_MANUAL == srTransRec.byEntryMode)
    {
        setLCDPrint(2, DISPLAY_POSITION_LEFT, "CARD NO: ");
        memset(bDisplayStr, 0x00, sizeof(bDisplayStr));
        strcpy(bDisplayStr, srTransRec.szPAN);
        //CTOS_LCDTPrintXY(MAX_CHAR_PER_LINE-strlen(bDisplayStr)*2, 3, bDisplayStr);
        CTOS_LCDTPrintAligned(3, bDisplayStr, d_LCD_ALIGNRIGHT);
        setLCDPrint(5, DISPLAY_POSITION_LEFT, "EXPIRY DATE(MM/YY):");

        memset(bDisplayStr, 0x00, sizeof(bDisplayStr));
        memset(bBuf, 0x00, sizeof(bBuf));
        wub_hex_2_str(&srTransRec.szExpireDate[1], &bBuf[0], 1);
        memcpy(bDisplayStr, bBuf, 2);
        bDisplayStr[2] = '/';
        memset(bBuf, 0x00, sizeof(bBuf));
        wub_hex_2_str(&srTransRec.szExpireDate[0], &bBuf[0], 1);
        memcpy(bDisplayStr+3, bBuf, 2);
        //CTOS_LCDTPrintXY(MAX_CHAR_PER_LINE-strlen(bDisplayStr)*2, 6, bDisplayStr);
        CTOS_LCDTPrintAligned(6, bDisplayStr, d_LCD_ALIGNRIGHT);
        
        usInputLen = 7;
    }
    else
    {
        inCTOS_DisplayCardTitle(4, 5);
        usInputLen = 7;
    }
    
    setLCDPrint(usInputLen, DISPLAY_POSITION_LEFT, "4DBC: ");
    
    while(1)
    {
        usRet = shCTOS_GetNum(usInputLen+1, 0x01, baBuf, &usLens, usMinLen, usMaxLen, 0, d_INPUT_TIMEOUT);
        if (usRet == d_KBD_CANCEL )
            return (d_EDM_USER_CANCEL);
        if (usRet >= usMinLen && usRet <= usMaxLen)
        {
            return (d_OK);   
        }
        
        baBuf[0] = 0x00;
    }
}

int inCTOS_Get4DBC()
{
#define d_GETPIN_TIMEOUT        6000

    BYTE szCVV2Code[CVV2_SIZE + 1];
    int inResult = FAIL;
    BYTE key;
    short shCount = 0;
    DebugAddSTR("inCTOS_Get4DBC","Processing...",20);
    USHORT usX =1, usY = 6;
    USHORT iStrLen = 6;
    BYTE bShowAttr = 0x02; 
    USHORT usInvoiceLen = 4;
    BYTE bRet;
    char szTxnTittleBuf[30] = {0};

    vdDebug_LogPrintf("inCTOS_Get4DBC byEntryMode[%ld] inCVV_II[%ld]", srTransRec.byEntryMode, strCDT.inCVV_II);     

    memset(srTransRec.szCVV2 , 0x00, sizeof(srTransRec.szCVV2));
    memset(szCVV2Code , 0x00, sizeof(szCVV2Code));

    if( CARD_ENTRY_ICC == srTransRec.byEntryMode || CARD_ENTRY_WAVE == srTransRec.byEntryMode)
    {   
        return d_OK;
    }
    
    if(CVV2_NONE == strCDT.inCVV_II)
    {
        return d_OK;;
    }
	
	if(srTransRec.byTransType == SALE && fAmountLessThanFloorLimit() == d_OK)
    {
        return (d_OK);
    }
    
	// strCDT.inCVV_II = CVV2_MANUAL_MSR;
	// srTransRec.byEntryMode = CARD_ENTRY_MSR;
	// vdDebug_LogPrintf("inCTOS_Get4DBC byEntryMode[%ld] inCVV_II[%ld]", srTransRec.byEntryMode, strCDT.inCVV_II);	 
			
    while(shCount < 3)
    {
        
        if(((CVV2_MANUAL == strCDT.inCVV_II) &&( CARD_ENTRY_MANUAL == srTransRec.byEntryMode))
        || ((CVV2_MSR == strCDT.inCVV_II) &&(( CARD_ENTRY_MSR == srTransRec.byEntryMode) ||( CARD_ENTRY_FALLBACK == srTransRec.byEntryMode)))
        || ((CVV2_MANUAL_MSR == strCDT.inCVV_II) &&(( CARD_ENTRY_MANUAL == srTransRec.byEntryMode) ||( CARD_ENTRY_MSR == srTransRec.byEntryMode) ||( CARD_ENTRY_FALLBACK == srTransRec.byEntryMode)))
        )
        {
            vdDebug_LogPrintf("hello");
            CTOS_LCDTClearDisplay();
			//vduiClearBelow(2);
			
			szGetTransTitle(srTransRec.byTransType, szTxnTittleBuf);
			usCTOSS_ClearAllShowTittle(szTxnTittleBuf);

            setLCDPrint(5, DISPLAY_POSITION_LEFT, "4DBC: ");
			
			vdDebug_LogPrintf("InputString");

            bRet = InputString(usX, usY, 0x01, bShowAttr, szCVV2Code, &usInvoiceLen, 4,4, d_GETPIN_TIMEOUT);

            if (bRet == d_KBD_CANCEL )
            {
                CTOS_LCDTClearDisplay();    
                vdSetErrorMessage("USER CANCEL");
                memcpy(&srTransRec, &srTransRec, sizeof(TRANS_DATA_TABLE));
                return (d_EDM_USER_CANCEL);
            }
            
            
            if(atoi(szCVV2Code) != 0)
            {
                strcpy(srTransRec.szCVV2,szCVV2Code);
                {
					return d_OK;
                }
            }   
			else
		   {
			   if(d_EDM_USER_CANCEL == inResult)
			   {
				   vdSetErrorMessage("USER CANCEL");
				   return inResult;
			   }
			   
			   memset(szCVV2Code , 0x00, sizeof(szCVV2Code));
			   vdDisplayErrorMsg(1, 8, "INVALID 4DBC");

			   break;  
		   }   		
        }
        else
        {
            return d_OK;;
        }

        shCount ++ ;
    }
    vdSetErrorMessage("Get 4DBC ERR");
    // strcpy(srTransRec.szRespMesg, "Get 4DBC ERR");
    vdDebug_LogPrintf("Get 4DBC ERR");     
    return FAIL;
    
}

int inCTOS_getCardCVV2(BYTE *baBuf)
{
    USHORT usRet;
    USHORT usInputLen;
    USHORT usLens;
    USHORT usMinLen = 3;
    USHORT usMaxLen = 4;
    BYTE bBuf[4+1];
    BYTE bDisplayStr[MAX_CHAR_PER_LINE+1];

    //CTOS_LCDTClearDisplay();
	vduiClearBelow(2);
    vdDispTransTitle(srTransRec.byTransType);

    if(CARD_ENTRY_MANUAL == srTransRec.byEntryMode)
    {
        setLCDPrint(2, DISPLAY_POSITION_LEFT, "CARD NO: ");
        memset(bDisplayStr, 0x00, sizeof(bDisplayStr));
        strcpy(bDisplayStr, srTransRec.szPAN);
        CTOS_LCDTPrintXY(MAX_CHAR_PER_LINE-strlen(bDisplayStr)*2, 3, bDisplayStr);
        setLCDPrint(5, DISPLAY_POSITION_LEFT, "EXPIRY DATE(MM/YY):");

        memset(bDisplayStr, 0x00, sizeof(bDisplayStr));
        memset(bBuf, 0x00, sizeof(bBuf));
        wub_hex_2_str(&srTransRec.szExpireDate[1], &bBuf[0], 1);
        memcpy(bDisplayStr, bBuf, 2);
        bDisplayStr[2] = '/';
        memset(bBuf, 0x00, sizeof(bBuf));
        wub_hex_2_str(&srTransRec.szExpireDate[0], &bBuf[0], 1);
        memcpy(bDisplayStr+3, bBuf, 2);
        CTOS_LCDTPrintXY(MAX_CHAR_PER_LINE-strlen(bDisplayStr)*2, 6, bDisplayStr);
        
        usInputLen = 7;
    }
    else
    {
        inCTOS_DisplayCardTitle(4, 5);
        usInputLen = 7;
    }
    
    setLCDPrint(usInputLen, DISPLAY_POSITION_LEFT, "CVV2: ");
    
    while(1)
    {
        usRet = shCTOS_GetNum(usInputLen+1, 0x01, baBuf, &usLens, usMinLen, usMaxLen, 1, d_INPUT_TIMEOUT);
        if (usRet == d_KBD_CANCEL )
            return (d_EDM_USER_CANCEL);
        if (usRet >= usMinLen && usRet <= usMaxLen)
        {
            return (d_OK);   
        }
        else if(0 == usRet)
        {
            return (d_OK);   
        }

        baBuf[0] = 0x00;
    }
}

int inCTOS_GetCVV2()
{
    BYTE szCVV2Code[CVV2_SIZE + 1];
    int inResult = FAIL;
    BYTE key;
    short shCount = 0;
    DebugAddSTR("inCTOS_GetCVV2","Processing...",20);

    memset(srTransRec.szCVV2 , 0x00, sizeof(srTransRec.szCVV2));
    memset(szCVV2Code , 0x00, sizeof(szCVV2Code));

    if(CVV2_NONE == strCDT.inCVV_II)
    {
        return d_OK;
    }
    
    while(shCount < 3)
    {
        
        if(((CVV2_MANUAL == strCDT.inCVV_II) &&( CARD_ENTRY_MANUAL == srTransRec.byEntryMode))
        || ((CVV2_MSR == strCDT.inCVV_II) &&(( CARD_ENTRY_MSR == srTransRec.byEntryMode) ||( CARD_ENTRY_FALLBACK == srTransRec.byEntryMode)))
        || ((CVV2_MANUAL_MSR == strCDT.inCVV_II) &&(( CARD_ENTRY_MANUAL == srTransRec.byEntryMode) ||( CARD_ENTRY_MSR == srTransRec.byEntryMode) ||( CARD_ENTRY_FALLBACK == srTransRec.byEntryMode)))
        || ((CVV2_MANUAL_MSR_CHIP == strCDT.inCVV_II) &&(( CARD_ENTRY_MANUAL == srTransRec.byEntryMode) ||( CARD_ENTRY_MSR == srTransRec.byEntryMode) ||( CARD_ENTRY_FALLBACK == srTransRec.byEntryMode) || ( CARD_ENTRY_ICC == srTransRec.byEntryMode))))
        {
            CTOS_KBDBufFlush();

            inResult = inCTOS_getCardCVV2(szCVV2Code);
            if(d_OK == inResult)
            {
                strcpy(srTransRec.szCVV2,szCVV2Code);
                return d_OK;
            }
            else
            {
                if(d_EDM_USER_CANCEL == inResult)
                {
                    vdSetErrorMessage("USER CANCEL");
                    return inResult;
                }
                
                memset(szCVV2Code , 0x00, sizeof(szCVV2Code));
                vdDisplayErrorMsg(1, 8, "INVALID CVV");

                break;  
            }   
        }
        else
        {
            return d_OK;
        }

        shCount ++ ;
    }
    vdSetErrorMessage("Get CVV ERR");
    return FAIL;
    
}

int inCTOS_EMVSetTransType(BYTE byTransType)
{
	switch(byTransType)
	{
		//for improve transaction speed 
		//case SALE:
		//	ushCTOS_EMV_NewTxnDataSet(TAG_9C_TRANS_TYPE,1,"\x00");
		//	break;
		case REFUND:
			ushCTOS_EMV_NewTxnDataSet(TAG_9C_TRANS_TYPE,1,"\x20");
			break;
		case PRE_AUTH:
			ushCTOS_EMV_NewTxnDataSet(TAG_9C_TRANS_TYPE,1,"\x30");
			break;
		default:
            break;
	}
}
//for improve transaction speed 
void vdCTOSS_EMV_SetAmount(void)
{
	BYTE szBaseAmount[20];
	BYTE szTipAmount[20];
	BYTE szTotalAmount[20];
	BYTE   EMVtagVal[64];
	BYTE   szStr[64];
	BYTE  byDataTmp1[32];
	BYTE  byDataTmp2[32];
	BYTE  bPackSendBuf[256];
	USHORT usPackSendLen = 0;
	USHORT ushEMVtagLen;
	ULONG lnTmp;
		
  memset(byDataTmp1, 0x00, sizeof(byDataTmp1));
  memset(byDataTmp2, 0x00, sizeof(byDataTmp2));
  wub_hex_2_str(srTransRec.szTotalAmount, byDataTmp1, 6);
  lnTmp = atol(byDataTmp1);
  wub_long_2_array(lnTmp, byDataTmp2);

  memcpy(&bPackSendBuf[usPackSendLen++], "\x81", 1);
  bPackSendBuf[usPackSendLen++] = 0x04;
  memcpy(&bPackSendBuf[usPackSendLen], byDataTmp2, 4);
  usPackSendLen += 4;
  
  memcpy(srTransRec.stEMVinfo.T9F02, srTransRec.szTotalAmount, 6);
  
  memcpy(&bPackSendBuf[usPackSendLen], "\x9F\x02", 2);
  usPackSendLen += 2;
  bPackSendBuf[usPackSendLen++] = 0x06;
  if(srTransRec.stDCCinfo.bySelectedCurrency == 2)
  {
      wub_str_2_hex(srTransRec.stDCCinfo.szDccAmt, srTransRec.stEMVinfo.T9F02, 12);
      DebugAddHEX("Foreign T9F02:", srTransRec.stEMVinfo.T9F02, 6);
  }
    DebugAddHEX("emv T9F02:", srTransRec.stEMVinfo.T9F02, 6);
  memcpy(&bPackSendBuf[usPackSendLen], srTransRec.stEMVinfo.T9F02, 6);
  usPackSendLen += 6;

  if(atol(szTipAmount) > 0)
  {
      memcpy(srTransRec.stEMVinfo.T9F03, srTransRec.szTipAmount, 6);
  }
  else
  {
      memset(szTipAmount, 0x00, sizeof(szTipAmount));
      memcpy(srTransRec.stEMVinfo.T9F03, szTipAmount, 6);
  }

  memcpy(&bPackSendBuf[usPackSendLen], "\x9F\x03", 2);
  usPackSendLen += 2;
  bPackSendBuf[usPackSendLen++] = 0x06;
  memcpy(&bPackSendBuf[usPackSendLen], srTransRec.stEMVinfo.T9F03, 6);
  usPackSendLen += 6;

	vdPCIDebug_HexPrintf("EMV_SetAmount",bPackSendBuf,usPackSendLen);
  usCTOSS_EMV_MultiDataSet(usPackSendLen, bPackSendBuf);

}


int inCTOS_EMVProcessing()
{
    int inRet;
    BYTE   EMVtagVal[64];
    BYTE bySC_status;
	int AIDlen;
	BYTE AID[16];

	
    if( CARD_ENTRY_ICC == srTransRec.byEntryMode)
    {
		inCTOS_EMVSetTransType(srTransRec.byTransType);
		//test inCTOSS_CheckNSR(0);
		vdCTOSS_EMV_SetAmount();//for improve transaction speed 
		
		if(srTransRec.byTransType == PRE_AUTH)
		{
			ushCTOS_EMV_NewTxnDataSet(TAG_9F1B_TERM_FLOOR_LIMIT,4,"\x00\x00\x00\x00");
			vdDebug_LogPrintf("byTransType=[%ld], 9F1B 000000", srTransRec.byTransType);
		}
		
		if(fIsQPS())
		{
			vdDebug_LogPrintf("isQPS");
			if(memcmp(strIIT.szIssuerAbbrev,"CUP",3) == 0)
			{
				memset(AID,0x00,sizeof(AID));
				AIDlen = srTransRec.stEMVinfo.T84_len;
				memcpy(AID,srTransRec.stEMVinfo.T84,AIDlen);

				DebugAddHEX("CUP AID", AID, AIDlen);
				vdDebug_LogPrintf("IIT CUP");
				if ((memcmp(AID,"\xA0\x00\x00\x03\x33\x01\x01\x02",AIDlen) == 0)
					|| (memcmp(AID,"\xA0\x00\x00\x03\x33\x01\x01\x03",AIDlen) == 0))
				{
					//reset the terminal cability for STT
					vdDebug_LogPrintf("CUP NSR");
					/*if (strTCT.TaxiMode)	//signature
					{
						ushCTOS_EMV_NewTxnDataSet(TAG_9F33_TERM_CAB,3,"\xE0\xD8\xC8");
					}
					else*/
						ushCTOS_EMV_NewTxnDataSet(TAG_9F33_TERM_CAB,3,"\xE0\x98\xC8");
				}
			}
		}
        inRet = usCTOSS_EMV_TxnPerform();
        inCTOS_FirstGenACGetAndSaveEMVData();
		inCTOSS_CheckNSR(1);
        
        EMVtagVal[0] = srTransRec.stEMVinfo.T9F27;
            
        switch( EMVtagVal[0] & 0xC0)
        {
            case 0: //Declined --- AAC  
                strcpy(srTransRec.szAuthCode,"Z1");

							/* Issue# 000065 - start -- jzg */
							CTOS_SCStatus(d_SC_USER, &bySC_status);
							if (!(bySC_status & d_MK_SC_PRESENT))
								vdSetErrorMessage("CHIP MALFUNCTION");
							else
							vdSetErrorMessage("EMV Decline");
							/* Issue# 000065 - end -- jzg */
								
                vdDebug_LogPrintf("1st ACs, card dec");
               
                return EMV_CRITICAL_ERROR;
        
            case 0x40: //Approval --- TC
                strcpy(srTransRec.szAuthCode,"Y1");
                srTransRec.shTransResult = TRANS_AUTHORIZED;
			
				if(TRUE == strTCT.fNoCTOffline)			
                    srTransRec.shTransResult = TRANS_REJECTED;
			    
                vdDebug_LogPrintf("1nd AC app fNoCTOffline [%ld]", strTCT.fNoCTOffline);
                break;
                
            case 0x80: //ARQC
                vdDebug_LogPrintf("go online");
                break; 
                
            default:
                strcpy(srTransRec.szAuthCode,"Z1");
                
								/* Issue# 000065 - start -- jzg */
								CTOS_SCStatus(d_SC_USER, &bySC_status);
								if (!(bySC_status & d_MK_SC_PRESENT))
											vdSetErrorMessage("CHIP MALFUNCTION");
								else
											vdSetErrorMessage("EMV Decline");
								/* Issue# 000065 - end -- jzg */
                
                return EMV_CRITICAL_ERROR;
        
        }

        vdDebug_LogPrintf("usCTOSS_EMV_TxnPerform return[%d]", inRet);
        if (inRet != d_OK)
            vdSetErrorMessage("First GenAC ERR");
        return inRet;

    }
    
    return d_OK;
}

int inCTOS_CheckTipAllowd()
{   

    if(SALE_TIP == srTransRec.byTransType)
    {
        if (inMultiAP_CheckSubAPStatus() == d_OK)
            return d_OK;
    }
    
    if (0 ==strTCT.byTipAllowFlag)
    {
        if (SALE_TIP == srTransRec.byTransType)
            vdSetErrorMessage("TIP NOT ALLWD");
        
        return d_NO;
    }

//1010
	if(srTransRec.byVoided == TRUE)
	{
		vdSetErrorMessage("TIP NOT ALLWD");
		return d_NO;
	}
//1010

    return d_OK;
}

//only byTipAllowFlag set to 2, then allow to key in tip when do sale/offline sale
int inCTOS_CheckTipAllowdEx()
{
    if (2 !=strTCT.byTipAllowFlag)
    {
        return d_NO;
    }

//1010
    if(srTransRec.byVoided == TRUE)
    {
        vdSetErrorMessage("TIP NOT ALLWD");
        return d_NO;
    }
//1010

    return d_OK;
}

SHORT shCTOS_EMVAppSelectedProcess(void)
{
    short shResult;
    BYTE SelectedAID[16]; 
    USHORT SelectedAIDLen = 0;
    BYTE label[32];
    USHORT label_len = 0;
    BYTE PreferAID[128];
    CTOS_RTC SetRTC;
    
    vdDebug_LogPrintf("-------EMV_TxnAppSelect-----"); 
    memset(PreferAID,0,sizeof(PreferAID));
    memset(label,0,sizeof(label));

    shResult = usCTOSS_EMV_TxnAppSelect(PreferAID, 0, SelectedAID, &SelectedAIDLen, label, &label_len);
    vdDebug_LogPrintf("-EMV_TxnAppSelect=[%d] SelectedAIDLen[%d] label[%s]fback[%d]",shResult, SelectedAIDLen, label,strEMVT.inEMVFallbackAllowed);
    if(d_OK == shResult)
        DebugAddHEX("SelectedAIDLen", SelectedAID, SelectedAIDLen);
    
    if((shResult != PP_OK) && (shResult != EMV_USER_ABORT))
    {
		//EMV: should display "CHIP NOT DETECTED" instead of doing fallback - start -- jzg
		if (shResult == EMV_CHIP_NOT_DETECTED)
		{
			vdDisplayErrorMsg(1, 8, "CHIP NOT DETECTED");
			return EMV_CHIP_FAILED;
		}
		//EMV: should display "CHIP NOT DETECTED" instead of doing fallback - end -- jzg

		//EMV: If AID not found display "TRANS NOT ALLOWED" - start -- jzg
		if (shResult == EMV_TRANS_NOT_ALLOWED)
		{
			vdDisplayErrorMsg(1, 8, "TRANS NOT ALLOWED");
			return EMV_CHIP_FAILED;
		}
		//EMV: If AID not found display "TRANS NOT ALLOWED" - end -- jzg

		//VISA: Testcase 29 - should display "CARD BLOCKED" instead of doing fallback - start -- jzg
		if (shResult == EMV_CARD_BLOCKED)
		{
			inFallbackToMSR = FAIL;
			vdDisplayErrorMsg(1, 8, "CARD BLOCKED");
			return EMV_CHIP_FAILED;
		}
		//VISA: Testcase 29 - should display "CARD BLOCKED" instead of doing fallback - end -- jzg

        if(EMV_FALLBACK == shResult)
        {
            //0826
            //vdDisplayErrorMsg(1, 8, "PLS SWIPE CARD");
            vdDisplayErrorMsg(1, 8, "CHIP NOT DETECTED");
			//0826
            if(strTCT.fFallbackFlag == 1)
			{
	            CTOS_RTCGet(&SetRTC);
	            inFallbackToMSR = SUCCESS;
	            sprintf(strTCT.szFallbackTime,"%02d%02d%02d",SetRTC.bHour,SetRTC.bMinute,SetRTC.bSecond);
            }else{
				inFallbackToMSR = FAIL;
			}
        }
        else
        {
            vdDisplayErrorMsg(1, 8, "READ CARD FAILED");
        }
        
        return EMV_CHIP_FAILED;
    }
    
    if(shResult == EMV_USER_ABORT)
    {
        

        if(strEMVT.inEMVFallbackAllowed)
        {
            //0826
            //vdDisplayErrorMsg(1, 8, "PLS SWIPE CARD");
            vdDisplayErrorMsg(1, 8, "CHIP NOT DETECTED");
			//0826
            
            CTOS_RTCGet(&SetRTC);
            inFallbackToMSR = SUCCESS;
            sprintf(strTCT.szFallbackTime,"%02d%02d%02d",SetRTC.bHour,SetRTC.bMinute,SetRTC.bSecond);
        }
        else
        {
            vdDisplayErrorMsg(1, 8, "READ CARD FAILED");
        }

        
        return EMV_USER_ABORT;
    }

    return d_OK;
        
}

short shCTOS_EMVSecondGenAC(BYTE *szIssuerScript, UINT inIssuerScriptlen)
{
#define ACT_ONL_APPR 1
#define ACT_ONL_DENY 2
#define ACT_UNAB_ONL 3
#define ACT_ONL_ISSUER_REFERRAL 4           //From Host
#define ACT_ONL_ISSUER_REFERRAL_APPR 4
#define ACT_ONL_ISSUER_REFERRAL_DENY 5

    USHORT usResult;
    EMV_ONLINE_RESPONSE_DATA st2ACResponseData;
    BYTE   EMVtagVal[64];
		BYTE bySC_status; //Issue# 000065 -- jzg

    memset(&st2ACResponseData,0,sizeof(st2ACResponseData));

    //st2ACData.iAction will decide trans approve or not
    if(srTransRec.shTransResult == TRANS_COMM_ERROR)
    {
        //st2ACResponseData.bAction = ACT_UNAB_ONL;
        st2ACResponseData.bAction = ACT_ONL_DENY;
    }
    else if(srTransRec.shTransResult == TRANS_REJECTED)
        st2ACResponseData.bAction = ACT_ONL_DENY;
    else if(srTransRec.shTransResult == TRANS_CALL_BANK)
        st2ACResponseData.bAction = ACT_ONL_ISSUER_REFERRAL;
    else if(srTransRec.shTransResult == TRANS_AUTHORIZED)
        st2ACResponseData.bAction = ACT_ONL_APPR;

	//For online processing host replied with IAD"0A 28 FB D1 79 30 77 8E F6 30 31", 
	//however when terminal send 2nd generate AC command, it changed this response code from "30 31" to "30 32" . 
	//Maybe terminal is copying the response code from DF39 in 0210 but not IAD
	if (srTransRec.shTransResult != TRANS_AUTHORIZED)
    {
    	if (srTransRec.stEMVinfo.T91Len == 10)
    	{
    		memset(srTransRec.szRespCode,0x00,sizeof(srTransRec.szRespCode));
			memcpy(srTransRec.szRespCode, &srTransRec.stEMVinfo.T91[8], 2);
    	}
    }

	/*no need this for production, may for certification only*/
	#if 1	// no need for AXP EMV 012, but need for AXP EMV 015
    if(srTransRec.shTransResult == TRANS_COMM_ERROR && st2ACResponseData.bAction == ACT_ONL_DENY)
    {
		strcpy(srTransRec.szRespCode, "Z3");
    }
	#endif

	vdDebug_LogPrintf("szRespCode:%s %d", srTransRec.szRespCode, srTransRec.shTransResult);
    memset(srTransRec.stEMVinfo.T8A, 0x00, sizeof(srTransRec.stEMVinfo.T8A));
    memcpy(srTransRec.stEMVinfo.T8A, srTransRec.szRespCode, strlen(srTransRec.szRespCode));
	
	st2ACResponseData.pAuthorizationCode = srTransRec.szRespCode;
    st2ACResponseData.pIssuerAuthenticationData = srTransRec.stEMVinfo.T91;
    st2ACResponseData.IssuerAuthenticationDataLen = srTransRec.stEMVinfo.T91Len;
    st2ACResponseData.pIssuerScript = szIssuerScript;
    st2ACResponseData.IssuerScriptLen = inIssuerScriptlen;

    usResult = TRANS_AUTHORIZED;
    DebugAddHEX("shCTOS_EMVSecondGenAC ", st2ACResponseData.pIssuerScript, st2ACResponseData.IssuerScriptLen);
    usResult = usCTOSS_EMV_TxnCompletion(&st2ACResponseData);

    vdDebug_LogPrintf("PP_iCompletion:%d ", usResult);

    inCTOS_SecondGenACGetAndSaveEMVData();
    
    if(VS_TRUE == strTCT.fDemo)
    {
        usResult = PP_OK;
        EMVtagVal[0] = 0x40;
    }
    if(usResult != PP_OK)
    {
        if(strcmp((char *)srTransRec.szRespCode, "00") ==  0)
        {
            vdSetErrorMessage("EMV Decline");
			strcpy(srTransRec.szRespCode, "ER");
        }
        return EMV_CRITICAL_ERROR;
    }

    EMVtagVal[0] = srTransRec.stEMVinfo.T9F27;
        
    switch( EMVtagVal[0] & 0xC0)
    {
        case 0: //Declined --- AAC
            if(strcmp((char *)srTransRec.szRespCode, "00") ==  0)  //approve by host, but not by card
            {
                
								/* Issue# 000065 - start -- jzg */
										CTOS_SCStatus(d_SC_USER, &bySC_status);
								if (!(bySC_status & d_MK_SC_PRESENT))
										vdSetErrorMessage("CHIP MALFUNCTION");
								else
										vdSetErrorMessage("EMV Decline");
								/* Issue# 000065 - end -- jzg */
                
                vdDebug_LogPrintf("Host app, card dec");
				strcpy(srTransRec.szRespCode, "ER");
            }
            else
            {
                vdDebug_LogPrintf("Host reject");
            }
            return EMV_CRITICAL_ERROR;

        case 0x40: //Approval --- TC       
			vdDebug_LogPrintf("2nd AC app");
			if (srTransRec.shTransResult != TRANS_AUTHORIZED)  //not approve by host, but approve by card
			{
				
				/* Issue# 000065 - start -- jzg */
					CTOS_SCStatus(d_SC_USER, &bySC_status);
					if (!(bySC_status & d_MK_SC_PRESENT))
							vdSetErrorMessage("CHIP MALFUNCTION");
					else
							vdSetErrorMessage("EMV Decline");
					/* Issue# 000065 - end -- jzg */
				
				vdDebug_LogPrintf("Host decline, card approve");
				return EMV_CRITICAL_ERROR;								 
			}
            break;
        
        default:
            
						/* Issue# 000065 - start -- jzg */
						CTOS_SCStatus(d_SC_USER, &bySC_status);
						if (!(bySC_status & d_MK_SC_PRESENT))
									vdSetErrorMessage("CHIP MALFUNCTION");
						else
									vdSetErrorMessage("EMV Decline");
						/* Issue# 000065 - end -- jzg */
            
            return EMV_CRITICAL_ERROR;

    }
    
    vdDebug_LogPrintf("End 2nd GenAC shTransResult=%d iAction=%d",srTransRec.shTransResult, st2ACResponseData.bAction);
    return PP_OK;
    
}

int inCTOS_EMVTCUpload(void)
{
    int inRet;

	vdDebug_LogPrintf("inCTOS_EMVTCUpload");
    
//0929
////remove ctls txn tc
    //if( CARD_ENTRY_ICC == srTransRec.byEntryMode) 
    if((CARD_ENTRY_ICC == srTransRec.byEntryMode)/*||

	((CARD_ENTRY_WAVE == srTransRec.byEntryMode) &&
	((srTransRec.bWaveSID == d_VW_SID_JCB_WAVE_QVSDC) ||
	(srTransRec.bWaveSID == d_VW_SID_AE_EMV) ||
	(srTransRec.bWaveSID == d_VW_SID_PAYPASS_MCHIP) ||
	(srTransRec.bWaveSID == d_VW_SID_VISA_WAVE_QVSDC)))*/
	)
//0929
    {
		inCTLOS_Updatepowrfail(PFR_IDLE_STATE);
        inRet = inProcessEMVTCUpload(&srTransRec, -1);// TC upload

            vdDebug_LogPrintf("szFileName, %s%02d%02drev"
                                , strHDT.szHostLabel
                                , strHDT.inHostIndex
                                , srTransRec.MITid);
        inCTOS_inDisconnect();
    }
    
    return d_OK;
}

 int inCTOS_AdviceTransUpload(void)
{
    TRANS_DATA_TABLE* srTransPara;
	
    vdDebug_LogPrintf("inCTOS_AdviceTransUpload");

	srTransPara = srGetISOEngTransDataAddress();

	if(strHDT.inNumAdv > 0)
	{
	   inProcessAdviceTrans(srTransPara, strHDT.inNumAdv);
	}

	return d_OK;
 }
int inCTOS_GetPubKey(const char *filename, unsigned char *modulus, int *mod_len, unsigned char *exponent, int *exp_len)
{
    unsigned char tmp[1024];
    int iRead;
    int iMod;
    int iExp;
    FILE  *fPubKey;                   
    UINT uintRet ;             

    fPubKey = fopen( (char*)filename, "rb" );
    if (fPubKey == NULL)
        return CTOS_RET_PARAM;

    uintRet = fread( tmp, 1, sizeof(tmp), fPubKey );
    fclose(fPubKey);  
    vdDebug_LogPrintf("CAPK=Len[%d]==[%s]",uintRet,tmp);
    
    if(uintRet >0)
    {
        iMod=(int)(tmp[0]-0x30)*100+(tmp[1]-0x30)*10+(tmp[2]-0x30);     
        vdDebug_LogPrintf("iMod===[%d]",iMod);
        if(iMod%8 != 0)
            return(CTOS_RET_PARAM);

        if(iMod > CTOS_PED_RSA_MAX)
            return(CTOS_RET_PARAM);

        *mod_len=iMod;      
        wub_str_2_hex((tmp+3), (modulus), iMod*2);
        
        vdDebug_LogPrintf("*mod_len===[%d]",*mod_len);
        DebugAddHEX("Module HEX string===", modulus, iMod);

        
        iExp=(int)tmp[iMod*2+4] - 0x30;
        wub_str_2_hex((&tmp[5+(iMod*2)]), (exponent), iExp*2);

        vdDebug_LogPrintf("iExp===[%d]",iExp);      
        DebugAddHEX("Exponent HEX string===", exponent, iExp);

        *exp_len = iExp;        
    }
    else
        return(CTOS_RET_CALC_FAILED);

    return(CTOS_RET_OK);
}

USHORT ushCTOS_EMV_NewDataGet(IN USHORT usTag, INOUT USHORT *pLen, OUT BYTE *pValue)
{
    USHORT usResult;
    USHORT usTagLen;
    static USHORT usGetEMVTimes = 0;
    
    usResult = usCTOSS_EMV_DataGet(usTag, &usTagLen, pValue);
    *pLen = usTagLen;

    usGetEMVTimes ++;
    vdDebug_LogPrintf("ushCTOS_EMV_NewDataGet Times[%d] usTagLen[%d]",usGetEMVTimes, usTagLen);
    return usResult;

}

USHORT ushCTOS_EMV_NewTxnDataSet(IN USHORT usTag, IN USHORT usLen, IN BYTE *pValue)
{
    USHORT usResult;
    static USHORT usSetEMVTimes = 0;

    usResult = usCTOSS_EMV_DataSet(usTag, usLen, pValue);
    usSetEMVTimes ++;
    vdDebug_LogPrintf("ushCTOS_EMV_NewTxnDataSet Times[%d] usResult[%d]",usSetEMVTimes, usResult);

    return usResult;
}

short shCTOS_EMVGetChipDataReady(void)
{
    short       shResult;
    BYTE        byDataTmp1[64];
    BYTE        byVal[64];
    USHORT      usLen;
    USHORT      inIndex ;
    BYTE        szDataTmp[5];
    BYTE szGetEMVData[128];
    BYTE szOutEMVData[2048];
    
    USHORT inTagLen = 0;
    

    memset(szGetEMVData,0,sizeof(szGetEMVData));
    memset(szOutEMVData,0,sizeof(szOutEMVData));

	//for improve transaction speed
    //shResult = usCTOSS_EMV_MultiDataGet(GET_EMV_TAG_AFTER_SELECT_APP, &inTagLen, szOutEMVData);
	inMultiAP_Database_EMVTransferDataRead(&inTagLen, szOutEMVData);
	DebugAddHEX("GET_EMV_TAG_AFTER_SELECT_APP",szOutEMVData,inTagLen);
        
    shResult = usCTOSS_FindTagFromDataPackage(TAG_57, byVal, &usLen, szOutEMVData, inTagLen);
    vdDebug_LogPrintf("-------TAG_57[%d] usLen[%d] [%02X %02X %02X]--", shResult, usLen, byVal[0], byVal[1], byVal[2]);

    memset(byDataTmp1, 0x00, sizeof(byDataTmp1));
    wub_hex_2_str(byVal, byDataTmp1, usLen);
    memcpy(srTransRec.szTrack2Data, byDataTmp1, (usLen*2));
    for(inIndex = 0; inIndex < (usLen*2); inIndex++)
    {
        if(byDataTmp1[inIndex] == 'F')
            srTransRec.szTrack2Data[inIndex]=0;
    }
    vdDebug_LogPrintf("szTrack2Data: %s %d", srTransRec.szTrack2Data, inIndex);
    
    for(inIndex = 0; inIndex < (usLen*2); inIndex++)
    {
        if(byDataTmp1[inIndex] != 'D')
            srTransRec.szPAN[inIndex] = byDataTmp1[inIndex];
        else
            break;
    }
    srTransRec.byPanLen = inIndex;
    vdDebug_LogPrintf("PAN: %s %d", srTransRec.szPAN, inIndex);
    inIndex++;
    memset(szDataTmp, 0x00, sizeof(szDataTmp));
    wub_str_2_hex(&byDataTmp1[inIndex], szDataTmp, 4);
    srTransRec.szExpireDate[0] = szDataTmp[0];
    srTransRec.szExpireDate[1] = szDataTmp[1];
    vdMyEZLib_LogPrintf("EMV functions Expiry Date [%02x%02x]",srTransRec.szExpireDate[0],srTransRec.szExpireDate[1]);
    inIndex = inIndex + 4;
    memcpy(srTransRec.szServiceCode, &byDataTmp1[inIndex], 3);

    shResult = usCTOSS_FindTagFromDataPackage(TAG_5A_PAN, srTransRec.stEMVinfo.T5A, &usLen, szOutEMVData, inTagLen);
    vdDebug_LogPrintf("-------TAG_5A_PAN[%d] usLen[%d] [%02X %02X %02X]--", shResult, usLen, srTransRec.stEMVinfo.T5A[0], srTransRec.stEMVinfo.T5A[1], srTransRec.stEMVinfo.T5A[2]);

    srTransRec.stEMVinfo.T5A_len = (BYTE)usLen;
    shResult = usCTOSS_FindTagFromDataPackage(TAG_5F30_SERVICE_CODE, srTransRec.stEMVinfo.T5F30, &usLen, szOutEMVData, inTagLen);
    
    memset(byVal, 0x00, sizeof(byVal));
    shResult = usCTOSS_FindTagFromDataPackage(TAG_5F34_PAN_IDENTFY_NO, byVal, &usLen, szOutEMVData, inTagLen);
    vdMyEZLib_LogPrintf("5F34: %02x %d", byVal[0], usLen);
    srTransRec.stEMVinfo.T5F34_len = usLen;
    srTransRec.stEMVinfo.T5F34 = byVal[0];

    shResult = usCTOSS_FindTagFromDataPackage(TAG_82_AIP, srTransRec.stEMVinfo.T82, &usLen, szOutEMVData, inTagLen);
    vdDebug_LogPrintf("-------TAG_82_AIP-[%02x][%02x]-", srTransRec.stEMVinfo.T82[0], srTransRec.stEMVinfo.T82[1]); 

    shResult = usCTOSS_FindTagFromDataPackage(TAG_84_DF_NAME, srTransRec.stEMVinfo.T84, &usLen, szOutEMVData, inTagLen);
    srTransRec.stEMVinfo.T84_len = (BYTE)usLen;

    shResult = usCTOSS_FindTagFromDataPackage(TAG_5F24_EXPIRE_DATE, srTransRec.stEMVinfo.T5F24, &usLen, szOutEMVData, inTagLen);

    shResult = usCTOSS_FindTagFromDataPackage(TAG_9F08_IC_VER_NUMBER, szDataTmp, &usLen, szOutEMVData, inTagLen);
    vdDebug_LogPrintf("-------TAG_9F08_IC_VER_NUMBER-[%02x][%02x]-",szDataTmp[0],szDataTmp[1]);

    shResult = usCTOSS_FindTagFromDataPackage(TAG_9F09_TERM_VER_NUMBER, srTransRec.stEMVinfo.T9F09, &usLen, szOutEMVData, inTagLen);
    vdDebug_LogPrintf("-------TAG_9F09_TERM_VER_NUMBER-[%02x][%02x]-",srTransRec.stEMVinfo.T9F09[0],srTransRec.stEMVinfo.T9F09[1]); 
    if(usLen == 0)
        memcpy(srTransRec.stEMVinfo.T9F09, "\x00\x4C", 2);// can not get value from api like ...,so i hardcode a value from EMV level 2 cert document
    vdDebug_LogPrintf("9F09: %02x%02x %d", srTransRec.stEMVinfo.T9F09[0],srTransRec.stEMVinfo.T9F09[1], usLen);

    shResult = usCTOSS_FindTagFromDataPackage(TAG_5F20, srTransRec.szCardholderName, &usLen, szOutEMVData, inTagLen);
    vdDebug_LogPrintf("5F20,szCardholderName: %s", srTransRec.szCardholderName);

	shResult = usCTOSS_FindTagFromDataPackage(TAG_50, srTransRec.stEMVinfo.szChipLabel, &usLen, szOutEMVData, inTagLen);
	vdDebug_LogPrintf("-------TAG_50[%d] usLen[%d] [%s]--", shResult, usLen, srTransRec.stEMVinfo.szChipLabel);
        
}

int inCTOS_FirstGenACGetAndSaveEMVData(void)
{
    USHORT usLen = 64;
    BYTE szGetEMVData[128];
    BYTE szOutEMVData[2048];
    USHORT inTagLen = 0;    
    int ret = 0;
    unsigned char szTransSeqCounter[6+1];
	unsigned char szHEXTransSeqCounter[3+1];

	BYTE bAppLabel[64] = {0}; // EMV: Get Application Label -- jzg 
	BYTE bAppPrefName[64] = {0}; // EMV: Get Application Preferred Name -- jzg

	//1026	
	char szAscBuf[4 + 1], szBcdBuf[2 + 1];
	//1026
	
    memset(szGetEMVData,0,sizeof(szGetEMVData));
    memset(szGetEMVData,0,sizeof(szGetEMVData));

	//for improve transaction speed
    //usCTOSS_EMV_MultiDataGet(GET_EMV_TAG_AFTER_1STAC, &inTagLen, szOutEMVData);
	inMultiAP_Database_EMVTransferDataRead(&inTagLen, szOutEMVData);
	inMultiAP_Database_EMVTransferDataInit();
	DebugAddHEX("GET_EMV_TAG_AFTER_1STAC",szOutEMVData,inTagLen);

    vdDebug_LogPrintf("inCTOS_FirstGenACGetAndSaveEMVData");

    usCTOSS_FindTagFromDataPackage(TAG_5F28_ISSUER_COUNTRY_CODE, srTransRec.stEMVinfo.T5F28, &usLen, szOutEMVData, inTagLen);
    DebugAddHEX("TAG_5F28_ISSUER_COUNTRY_CODE", srTransRec.stEMVinfo.T5F28, 2);

    usCTOSS_FindTagFromDataPackage(TAG_5F2A_TRANS_CURRENCY_CODE, srTransRec.stEMVinfo.T5F2A, &usLen, szOutEMVData, inTagLen);



	/* EMV: Get Application Preferred Name - start -- jzg */
	usCTOSS_FindTagFromDataPackage(TAG_9F12, bAppPrefName, &usLen, szOutEMVData, inTagLen);
    vdDebug_LogPrintf("TAG 9F12 = [%s],bAppPrefName=%s, usLen=%d", srTransRec.stEMVinfo.szChipLabel, bAppPrefName, usLen);
	if(usLen > 0)
    {
	    //Visa Debit, DBS VISA, DBS VISAit
	    memset(srTransRec.stEMVinfo.szChipLabel, 0 , sizeof(srTransRec.stEMVinfo.szChipLabel));
    }
    vdDispAppLabel(bAppPrefName, usLen, srTransRec.stEMVinfo.szChipLabel);
	vdDebug_LogPrintf("TAG 9F12 = [%s]", srTransRec.stEMVinfo.szChipLabel);
	/* EMV: Get Application Preferred Name - end -- jzg */
	 
	vdDebug_LogPrintf("TAG 9F12 = [0x%02X]", srTransRec.stEMVinfo.szChipLabel[0]);
	 
	/* EMV: Get Application Label - start -- jzg */
	//if ((!((srTransRec.stEMVinfo.szChipLabel[0] >= 'a') && (srTransRec.stEMVinfo.szChipLabel[0] <= 'z'))) ||
	//(!((srTransRec.stEMVinfo.szChipLabel[0] >= 'A') && (srTransRec.stEMVinfo.szChipLabel[0] <= 'Z'))))
//	if(!((srTransRec.stEMVinfo.szChipLabel[0] >= 'a' && srTransRec.stEMVinfo.szChipLabel[0] <= 'z') ||(srTransRec.stEMVinfo.szChipLabel[0] >= 'A' && srTransRec.stEMVinfo.szChipLabel[0] <= 'Z'))) // if not ascii
        if(((srTransRec.stEMVinfo.szChipLabel[0] >= 'a' && srTransRec.stEMVinfo.szChipLabel[0] <= 'z')
            || (srTransRec.stEMVinfo.szChipLabel[0] >= 'A' && srTransRec.stEMVinfo.szChipLabel[0] <= 'Z')
            || (srTransRec.stEMVinfo.szChipLabel[0] >= '0' && srTransRec.stEMVinfo.szChipLabel[0] <= '9'))
                )
        {
            vdDebug_LogPrintf("use 9F12 to print,=%c", srTransRec.stEMVinfo.szChipLabel[0]);
        }
        else
	    {
            //memset(srTransRec.stEMVinfo.szChipLabel, 0, sizeof(srTransRec.stEMVinfo.szChipLabel));
            usCTOSS_FindTagFromDataPackage(TAG_50, bAppLabel, &usLen, szOutEMVData, inTagLen);

            if (strlen(bAppLabel) > 0){
                memset(srTransRec.stEMVinfo.szChipLabel, 0, sizeof(srTransRec.stEMVinfo.szChipLabel));
                vdDispAppLabel(bAppLabel, usLen, srTransRec.stEMVinfo.szChipLabel);
                vdDebug_LogPrintf("TAG 50 = [%s]", srTransRec.stEMVinfo.szChipLabel);
            }
	    }

    usCTOSS_FindTagFromDataPackage(TAG_95, srTransRec.stEMVinfo.T95, &usLen, szOutEMVData, inTagLen);
    
    usCTOSS_FindTagFromDataPackage(TAG_9A_TRANS_DATE, srTransRec.stEMVinfo.T9A, &usLen, szOutEMVData, inTagLen);


    usCTOSS_FindTagFromDataPackage(TAG_9C_TRANS_TYPE, (BYTE *)&(srTransRec.stEMVinfo.T9C), &usLen, szOutEMVData, inTagLen);
        
    usCTOSS_FindTagFromDataPackage(TAG_9F06, srTransRec.stEMVinfo.T9F06, &usLen, szOutEMVData, inTagLen);
    srTransRec.stEMVinfo.T9F06_len = usLen;

	if (srTransRec.HDTid == AMEX_HOST_INDEX)
    {
	    usCTOSS_FindTagFromDataPackage(TAG_9F08_IC_VER_NUMBER, szGetEMVData, &usLen, szOutEMVData, inTagLen);
	    DebugAddHEX("Save TAG_9F08_IC_VER_NUMBER", szGetEMVData, 2);
	    inCTOSS_StoreBatchFieldData(&srTransFlexiData, AMEX_FLEXI_9F08_VALUE, szGetEMVData, usLen);
	    
	    usCTOSS_FindTagFromDataPackage(TAG_9F0D, szGetEMVData, &usLen, szOutEMVData, inTagLen);
	    DebugAddHEX("Save TAG_9F0D", szGetEMVData, 5);
	    inCTOSS_StoreBatchFieldData(&srTransFlexiData, AMEX_FLEXI_9F0D_VALUE, szGetEMVData, usLen);

	    usCTOSS_FindTagFromDataPackage(TAG_9F0E, szGetEMVData, &usLen, szOutEMVData, inTagLen);
	    DebugAddHEX("Save TAG_9F0E", szGetEMVData, 5);
	    inCTOSS_StoreBatchFieldData(&srTransFlexiData, AMEX_FLEXI_9F0E_VALUE, szGetEMVData, usLen);

	    usCTOSS_FindTagFromDataPackage(TAG_9F0F, szGetEMVData, &usLen, szOutEMVData, inTagLen);
	    DebugAddHEX("Save TAG_9F0F", szGetEMVData, 5);
	    inCTOSS_StoreBatchFieldData(&srTransFlexiData, AMEX_FLEXI_9F0F_VALUE, szGetEMVData, usLen);
	}
	
    usCTOSS_FindTagFromDataPackage(TAG_9F09_TERM_VER_NUMBER, srTransRec.stEMVinfo.T9F09, &usLen, szOutEMVData, inTagLen);
    
    usCTOSS_FindTagFromDataPackage(TAG_9F10_IAP, srTransRec.stEMVinfo.T9F10, &usLen, szOutEMVData, inTagLen);
    srTransRec.stEMVinfo.T9F10_len = usLen;
    
    usCTOSS_FindTagFromDataPackage(TAG_9F1A_TERM_COUNTRY_CODE, srTransRec.stEMVinfo.T9F1A, &usLen, szOutEMVData, inTagLen);
    
    usCTOSS_FindTagFromDataPackage(TAG_9F1E, srTransRec.stEMVinfo.T9F1E, &usLen, szOutEMVData, inTagLen);
    
    usCTOSS_FindTagFromDataPackage(TAG_9F26_EMV_AC, srTransRec.stEMVinfo.T9F26, &usLen, szOutEMVData, inTagLen);
    
    usCTOSS_FindTagFromDataPackage(TAG_9F27, (BYTE *)&(srTransRec.stEMVinfo.T9F27), &usLen, szOutEMVData, inTagLen);
    
    usCTOSS_FindTagFromDataPackage(TAG_9F33_TERM_CAB, srTransRec.stEMVinfo.T9F33, &usLen, szOutEMVData, inTagLen);
    
    usCTOSS_FindTagFromDataPackage(TAG_9F34_CVM, srTransRec.stEMVinfo.T9F34, &usLen, szOutEMVData, inTagLen);
    
    usCTOSS_FindTagFromDataPackage(TAG_9F35_TERM_TYPE, (BYTE *)&(srTransRec.stEMVinfo.T9F35), &usLen, szOutEMVData, inTagLen);
    
    usCTOSS_FindTagFromDataPackage(TAG_9F36_ATC, srTransRec.stEMVinfo.T9F36, &usLen, szOutEMVData, inTagLen);
    srTransRec.stEMVinfo.T9F36_len = usLen;
    
    usCTOSS_FindTagFromDataPackage(TAG_9F37_UNPREDICT_NUM, srTransRec.stEMVinfo.T9F37, &usLen, szOutEMVData, inTagLen);

    usCTOSS_FindTagFromDataPackage(TAG_9F42_APP_CURRENCY_CODE, srTransRec.stEMVinfo.T9F42, &usLen, szOutEMVData, inTagLen);
    DebugAddHEX("TAG_9F42_APP_CURRENCY_CODE", srTransRec.stEMVinfo.T9F42, 2);

    usCTOSS_FindTagFromDataPackage(TAG_9F0D, srTransRec.stEMVinfo.T9F0D, &usLen, szOutEMVData, inTagLen);

    usCTOSS_FindTagFromDataPackage(TAG_9F0E, srTransRec.stEMVinfo.T9F0E, &usLen, szOutEMVData, inTagLen);

    usCTOSS_FindTagFromDataPackage(TAG_9F0F, srTransRec.stEMVinfo.T9F0F, &usLen, szOutEMVData, inTagLen);
    
    ret = inIITRead(srTransRec.IITid);
    vdDebug_LogPrintf("inIITRead[%d]",ret);
    sprintf(szTransSeqCounter, "%06ld", strIIT.ulTransSeqCounter);
    wub_str_2_hex(szTransSeqCounter, (char *)szHEXTransSeqCounter, 6);
    memcpy(srTransRec.stEMVinfo.T9F41,szHEXTransSeqCounter,3);
    strIIT.ulTransSeqCounter++;
    ret = inIITSave(srTransRec.IITid);    
    vdDebug_LogPrintf(" ret[%d] srTransRec.IITid[%d]strIIT.ulTransSeqCounter[%ld]",ret, srTransRec.IITid,strIIT.ulTransSeqCounter);
    //usCTOSS_FindTagFromDataPackage(TAG_9F41, srTransRec.stEMVinfo.T9F41, &usLen, szOutEMVData, inTagLen);
    
    usCTOSS_FindTagFromDataPackage(TAG_9F53, (BYTE *)&(srTransRec.stEMVinfo.T9F53), &usLen, szOutEMVData, inTagLen);

    return d_OK;
}

int inCTOS_SecondGenACGetAndSaveEMVData(void)
{

    USHORT usLen = 64;
    BYTE szGetEMVData[128];
    BYTE szOutEMVData[2048];
    USHORT inTagLen = 0;
    
    memset(szGetEMVData,0,sizeof(szGetEMVData));
    memset(szGetEMVData,0,sizeof(szGetEMVData));

	//for improve transaction speed
    //usCTOSS_EMV_MultiDataGet(GET_EMV_TAG_AFTER_2NDAC, &inTagLen, szOutEMVData);
	inMultiAP_Database_EMVTransferDataRead(&inTagLen, szOutEMVData);
	DebugAddHEX("GET_EMV_TAG_AFTER_1STAC",szOutEMVData,inTagLen);

    vdDebug_LogPrintf("inCTOS_SecondGenACGetAndSaveEMVData");
                
    usCTOSS_FindTagFromDataPackage(TAG_95, srTransRec.stEMVinfo.T95, &usLen, szOutEMVData, inTagLen);
                    
    usCTOSS_FindTagFromDataPackage(TAG_9F10_IAP, srTransRec.stEMVinfo.T9F10, &usLen, szOutEMVData, inTagLen);
    srTransRec.stEMVinfo.T9F10_len = usLen;
            
    usCTOSS_FindTagFromDataPackage(TAG_9F26_EMV_AC, srTransRec.stEMVinfo.T9F26, &usLen, szOutEMVData, inTagLen);
    
    usCTOSS_FindTagFromDataPackage(TAG_9F27, (BYTE *)&(srTransRec.stEMVinfo.T9F27), &usLen, szOutEMVData, inTagLen);
        
    usCTOSS_FindTagFromDataPackage(TAG_9F34_CVM, srTransRec.stEMVinfo.T9F34, &usLen, szOutEMVData, inTagLen);
                    
    return d_OK;
}

int inCTOS_showEMV_TagLog (void)
{
    #define TOTAL_TAGS  24
    int i;
    unsigned short tagLen;
    char outp[40];
    
    typedef struct
    {
            unsigned short Tags;
            char description[20];
    }print_tag;
    
    print_tag EMVTag[TOTAL_TAGS] = 
    { 
        {   TAG_95,                     "TVR"},         
        {   TAG_9B,                     "TSI"},
        {   TAG_9F26_EMV_AC,            "ARQC"},
        {   TAG_9F27,                   "Crypt Info Data"},
        {   TAG_9F10_IAP,               "Issuer Appl Data"},
        {   TAG_9F37_UNPREDICT_NUM,     "Unpredicte number"},
        {   TAG_9F36_ATC,               "Appl Trans Counter"},
        {   TAG_9A_TRANS_DATE,          "Trans Date"},
        {   TAG_9C_TRANS_TYPE,          "Trans Type"},
        {   TAG_5A_PAN,                 "PAN"},
        {   TAG_5F34_PAN_IDENTFY_NO,    "Sequence Num"},
        {   TAG_9F02_AUTH_AMOUNT,       "Amount Authorized"},
        {   TAG_9F03_OTHER_AMOUNT,      "Add Amount"},
        {   TAG_5F2A_TRANS_CURRENCY_CODE, "Trans Currency Code"},
        {   TAG_82_AIP,                 "AIP"},
        {   TAG_9F1A_TERM_COUNTRY_CODE, "Term Country Code"},
        {   TAG_9F34_CVM,               "CVR"},
        {   TAG_9F10_IAP,               "Issuer auth Data"},
        {   TAG_9F06,                   "AID"},
        {   TAG_50,                     "Appl Label Name"},
        {   TAG_8F,                     "CA Public Key Index"},
        {   TAG_9F0D,                   "IAC Default"},
        {   TAG_9F0E,                   "IAC Denial"},
        {   TAG_9F0F,                   "IAC Online"}

    };


    for(i = 0; i<TOTAL_TAGS; i++)
    {
        memset(outp,0x00,sizeof(outp));
        ushCTOS_EMV_NewDataGet(EMVTag[i].Tags, &tagLen, outp);
        vdDebug_LogPrintf("----TAG[%s][%x]=====Len[%d]----",EMVTag[i].description,EMVTag[i].Tags,tagLen); 
        DebugAddHEX("Value===",outp,tagLen);
    }
    return d_OK;
}

void vdCTOSS_GetAmt(void)
{
	memcpy(srTransRec.szBaseAmount, szBaseAmount, 6);	
}

void vdCTOSS_SetAmt(BYTE *baAmount)
{
	BYTE szTemp[20];

	memset(szTemp, 0x00, sizeof(szTemp));
	//format amount 10+2
	sprintf(szTemp, "%012.0f", atof(baAmount));
	wub_str_2_hex(szTemp, szBaseAmount,12);	
}

int inCTOSS_GetOnlinePIN(void)
{
	int inRet = d_NO;
	BYTE   EMVtagVal[64];
    USHORT EMVtagLen;
	
	vdDebug_LogPrintf("inCTOSS_GetOnlinePIN EntryMode=[%d]........",srTransRec.byEntryMode);
	
	if( CARD_ENTRY_ICC != srTransRec.byEntryMode)
		return d_OK;
	
	memset(EMVtagVal, 0x00, sizeof(EMVtagVal));
	EMVtagLen = 3;
    memcpy(EMVtagVal, srTransRec.stEMVinfo.T9F34, EMVtagLen);
	vdDebug_LogPrintf("T9F34 EMVtagVal=[%x][%x][%x]......",EMVtagVal[0],EMVtagVal[1],EMVtagVal[2]);
//'02' or '42'(CVM Code for 'Online PIN') Byte 1(CVM Performed)
	if(EMVtagVal[0] == 0x02 || EMVtagVal[0] == 0x42)
    {   
         inInitializePinPad();
#ifdef DUKPT_3DESONLINEPIN
		inRet = GetPIN_With_3DESDUKPT(0);
#else
	    inRet = inGetIPPPin();
#endif
	    if(d_OK != inRet)
	       return inRet;
    }
	else
		inRet = d_OK;

	return inRet;
}

void vdCTOSS_DispalyAmexSOC(void)
{
	char szDispBuf[30];

	if (srTransRec.HDTid == DINERS_HOST_INDEX)
		return;
	
	CTOS_LCDTClearDisplay();

	memset(szDispBuf,0x00,sizeof(szDispBuf));
	sprintf(szDispBuf,"        %s",srTransRec.szMacBlock);
	CTOS_LCDTPrintXY(1, 3, "SOC NUM:");
	CTOS_LCDTPrintXY(1, 4, szDispBuf);
    CTOS_Beep();
    //CTOS_Delay(500);

}

char DecToChar_For_SSL(int Dec)
{
	if((Dec >= 0)&&(Dec <= 9))
	{
		return Dec+0x30;
	}
	else if((Dec >= 10)&&(Dec <= 15))
	{
		return ( Dec - 10 )+ 0x41;
	}
	return 0;
}
void Byte2Acii_For_SSL(char* result,char Bytedata[],int len)
{
	int index=0;
	int FstChar=0;
	int SecChar=0;
    int offset=0;
	for(index=0;index<len;index++)
	{
        FstChar = Bytedata[index]/16;
        SecChar = Bytedata[index]%16;
        result[index+offset]   = DecToChar_For_SSL(FstChar);
	    result[index+offset+1] = DecToChar_For_SSL(SecChar);
	    offset++;    
	}
	result[index+offset] = '\0';
}



void intTostr_For_SSL(char* buffer,int IntValue)
{
	int n = IntValue;

	int i = 0;

	int isNeg = n<0;

	unsigned int n1 = isNeg ? -n : n;

	int t=0;
	while(n1!=0)
	{
		buffer[i++] = n1%10+'0';
		n1=n1/10;
	}

	if(isNeg)
		buffer[i++] = '-';

	buffer[i] = '\0';

	for(t = 0; t < i/2; t++)
	{
		buffer[t] ^= buffer[i-t-1];
		buffer[i-t-1] ^= buffer[t];
		buffer[t] ^= buffer[i-t-1];
	}
}

int Char2Digit_For_SSL(char tempchar)
{
    if (tempchar >= 0x30 && tempchar <= 0x39)
    {
		return tempchar - 0x30;
    }
    if (tempchar >= 0x41 && tempchar <= 0x46)
    {
        return tempchar - 0x41 + 10;
    }
    if (tempchar >= 0x61 && tempchar <= 0x66)
    {
        return tempchar - 0x61 + 10;
    }
	return 0;
}


void AciiToByte_For_SSL( char * ByteBuff, char * strAcii)
{
	char * temp = ( char *)malloc(sizeof(char)*(strlen(strAcii)/2));
	char NumTemp = 0;
	int offset=0;
	int index;
	int LENG = strlen(strAcii)/2;
	for(index=0;index<LENG;index++)
	{
		NumTemp  = Char2Digit_For_SSL(*(strAcii+offset)) * 16;
		NumTemp += Char2Digit_For_SSL(*(strAcii+offset+1));
		#ifdef AMEX_POP_FIELD44_FOR_CER
		if(index == 12)
		{
			if((NumTemp&0x10) == 0x10)
			{
				vdDebug_LogPrintf("NumTemp<OLD> = %x",NumTemp);
				NumTemp = NumTemp&0xEF;
				vdDebug_LogPrintf("NumTemp<NEW> = %x",NumTemp);
				LENG = LENG-3;
				FlagAmexField44 = 1;
			}
			else
			{
				vdDebug_LogPrintf("NumTemp<NOCHANGE> = %x",NumTemp);
				FlagAmexField44 = 0;
			}
		}
		#endif
		temp[index] = NumTemp;
		offset += 2;
		NumTemp = 0;
	}

	memset(ByteBuff,0,sizeof(ByteBuff));
	memcpy(ByteBuff,&temp[0],index);
	free(temp);
}


#define MGTAMEXCGI1 "/IPPAYMENTS/INTER/CARDAUTHORIZATION.DO"
char * AmexSSL_getPOSTHdr(void)
{
	char  StrMethod[100] ,*tmp;

	//sprintf(StrMethod, "POST %s HTTP/1.1\n",MGTAMEXCGI1);
	if (strlen(strSSL.szDummy1) > 0)
		sprintf(StrMethod, "POST %s HTTP/1.1\x0D\x0A",strSSL.szDummy1);
	else
		sprintf(StrMethod, "POST %s HTTP/1.1\x0D\x0A",MGTAMEXCGI1);
	tmp = (char *)malloc( sizeof(char)*strlen(StrMethod) +1);
	strcpy( tmp , StrMethod );

	return tmp;
}

void AmexSSL_addHdr ( char **data , const char * hdr , const char * value )
{ 
     char* tmp = (char *)malloc( strlen(*data) + strlen(hdr)+strlen(value)+2 + 1 +1 +1); //( +2 for : and space ) ( +1 two time for \0,\n )
	 char * privot = tmp + strlen(*data);
     strcpy( tmp , *data ); // first copy old header to a new one

     strcpy( privot , hdr ); // secound append header to a header
     
     privot += strlen(hdr);
     *privot = ':';privot++;
     *privot = 0x20;privot++;
     
     strcpy( privot , value ); // third append sesion's value
     privot += strlen(value);
     *privot = 0x0D; privot++;
	 *privot = 0x0A; privot++;
     *privot = 0x00;
   
	 free(*data);
     *data = tmp;
     
}
void AmexSSL_addHdr2 ( char **data , const char * appended )
{ 
     char* tmp = (char *)malloc( strlen(*data) + strlen(appended) +1); //for space and \0
	 char * privot = tmp + strlen(*data);
     strcpy( tmp , *data ); // first copy all data into new buffer

     strcpy( privot , appended  ); // secound appends data into a header
     
     privot += strlen(appended);
     *privot = 0x00;
         
     //privot += strlen(hdr);
   
	 free(*data);
     *data = tmp;
     
}


void vdCTOSS_PackAmexETHDirect(unsigned char* uszSendData,int *inSendLen)
{
	char LengthData[50];
	char strAMEXRTIND[50];
	char strAMEXORIGIN[50];
	char strAMEXCOUNTRY[50];
	char strAMEXMESSAGE[50];
	char strAMEXREGION[50];
	char strAMEXMERCHBNR[50];
	char strAMEXTID[50];
	char strAMEXCONTENT_TYPE[50];
	char strAMEXHOST[50];
	char strAMEXCACHE_CONTROL[50];
	char strAMEXCONNECTION[50];
	char * PtrSend;
	char * data;
	int inSendSize=0;

	inSendSize = *inSendLen;
	memset(strAMEXRTIND,0x00,sizeof(strAMEXRTIND));
	strcpy(strAMEXRTIND,strSSL.szAMEXRTIND);
	memset(strAMEXORIGIN,0x00,sizeof(strAMEXORIGIN));
	strcpy(strAMEXORIGIN,strSSL.szAMEXORIGIN);
	memset(strAMEXCOUNTRY,0x00,sizeof(strAMEXCOUNTRY));
	strcpy(strAMEXCOUNTRY,strSSL.szAMEXCOUNTRY);
	memset(strAMEXMESSAGE,0x00,sizeof(strAMEXMESSAGE));
	strcpy(strAMEXMESSAGE,strSSL.szAMEXMESSAGE);
	memset(strAMEXREGION,0x00,sizeof(strAMEXREGION));
	strcpy(strAMEXREGION,strSSL.szAMEXREGION);
	memset(strAMEXREGION,0x00,sizeof(strAMEXREGION));
	strcpy(strAMEXREGION,strSSL.szAMEXREGION);
	memset(strAMEXCONTENT_TYPE,0x00,sizeof(strAMEXCONTENT_TYPE));
	strcpy(strAMEXCONTENT_TYPE,strSSL.szAMEXCONTENT_TYPE);
	memset(strAMEXHOST,0x00,sizeof(strAMEXHOST));
	strcpy(strAMEXHOST,strSSL.szAMEXHOST);
	memset(strAMEXCACHE_CONTROL,0x00,sizeof(strAMEXCACHE_CONTROL));
	strcpy(strAMEXCACHE_CONTROL,strSSL.szAMEXCACHE_CONTROL);
	memset(strAMEXCONNECTION,0x00,sizeof(strAMEXCONNECTION));
	strcpy(strAMEXCONNECTION,strSSL.szAMEXCONNECTION);
	memset(strAMEXMERCHBNR,0x00,sizeof(strAMEXMERCHBNR));
	strcpy(strAMEXMERCHBNR,strMMT[0].szMID);
	memset(strAMEXTID,0x00,sizeof(strAMEXTID));
	strcpy(strAMEXTID,strMMT[0].szTID);

	vdDebug_LogPrintf("POST = [%s]",strSSL.szDummy1);
	vdDebug_LogPrintf("AMEXRTIND = [%s],szAMEXRTIND=[%s]",strAMEXRTIND,strSSL.szAMEXRTIND);
	vdDebug_LogPrintf("AMEXORIGIN = [%s]",strAMEXORIGIN);
	vdDebug_LogPrintf("AMEXCOUNTRY = [%s]",strAMEXCOUNTRY);
	vdDebug_LogPrintf("AMEXMESSAGE = [%s]",strAMEXMESSAGE);
	vdDebug_LogPrintf("AMEXREGION = [%s]",strAMEXREGION);
	vdDebug_LogPrintf("AMXMMID(MID) = [%s]",strAMEXMERCHBNR);
	vdDebug_LogPrintf("AMEXTID(TID) = [%s]",strAMEXTID);
	vdDebug_LogPrintf("AMEXCONTENT_TYPE = [%s]",strAMEXCONTENT_TYPE);
	vdDebug_LogPrintf("AMEXHOST = [%s]",strAMEXHOST);
	vdDebug_LogPrintf("AMEXCACHE_CONTROL = [%s]",strAMEXCACHE_CONTROL);
	vdDebug_LogPrintf("AMEXCONNECTION = [%s]",strAMEXCONNECTION);
	
	PtrSend = (char *)malloc(sizeof(char)*((inSendSize*2)+1));
	//Byte2Acii_For_SSL(PtrSend,uszSendData,inSendSize);
	vdPCIDebug_HexPrintf("uszSendData",uszSendData,inSendSize);
	wub_hex_2_str(uszSendData,PtrSend,inSendSize);
	vdDebug_LogPrintf("inSendSize = %d,PtrSend=[%d][%s]",inSendSize,strlen(PtrSend),PtrSend);
	intTostr_For_SSL(LengthData,strlen(PtrSend)+26); 		// + 26 for "AuthorizationRequestParam="
	vdDebug_LogPrintf("LengthData = [%s]",LengthData);
	data = AmexSSL_getPOSTHdr();	
	AmexSSL_addHdr( &data , "origin" , strAMEXORIGIN );
	AmexSSL_addHdr( &data , "country" , strAMEXCOUNTRY );
	AmexSSL_addHdr( &data , "message" , strAMEXMESSAGE );
	AmexSSL_addHdr( &data , "region" , strAMEXREGION );
	AmexSSL_addHdr( &data , "MerchNbr" ,strAMEXMERCHBNR );
	AmexSSL_addHdr( &data , "RtInd" ,strAMEXRTIND);
	//AmexSSL_addHdr( &data , "TID" ,strAMEXTID );
	AmexSSL_addHdr( &data , "Content-Type" , strAMEXCONTENT_TYPE );
	AmexSSL_addHdr( &data , "Host" , strAMEXHOST );
	AmexSSL_addHdr( &data , "Content-Length" , LengthData );
	AmexSSL_addHdr( &data , "Cache-Control" , strAMEXCACHE_CONTROL );
	AmexSSL_addHdr( &data , "Connection" , strAMEXCONNECTION );
	AmexSSL_addHdr2( &data ,"\x0D\x0A" );
	AmexSSL_addHdr2( &data ,"AuthorizationRequestParam=" );
	AmexSSL_addHdr2( &data ,PtrSend );

	*inSendLen = strlen(data);
    memcpy(uszSendData,data,*inSendLen);
	uszSendData[*inSendLen] = 0x00;

	free(data);
	free(PtrSend);

	return  ;
}


void vdCTOSS_UnPackAmexETHDirect(unsigned char* uszReceData,int *inReceLen)
{
	char * PtrReceive;
	int inRetval;
	int inLen = 0;
	char szReceive[2140];

	memset(szReceive,0x00,sizeof(szReceive));
		
	PtrReceive = uszReceData;
	inRetval = *inReceLen;
	
	vdDebug_LogPrintf("inRetval=%d",inRetval);
	
	if (inRetval <= 0)
	{
		vdDebug_LogPrintf("TIME OUT");
		return ;		
	}
	
	PtrReceive += strlen(uszReceData)-1;
	while(*PtrReceive != '\n')
	{
		PtrReceive--;
	}
	
	if(strlen(PtrReceive) != 0) {
		PtrReceive += 1;
	
		inLen = strlen(PtrReceive)/2;

	}		
	AciiToByte_For_SSL(szReceive, PtrReceive);
	memcpy(uszReceData, szReceive, inLen);
	
	vdDebug_LogPrintf("LENGTH BYTE BUFFER = %d",inLen );

	if(FlagAmexField44 == 1)
	{
		inLen = inLen-3;
		vdDebug_LogPrintf("LENGTH BYTE BUFFER2 = %d",inLen );
		FlagAmexField44 = 0;
	}

	*inReceLen = inLen;

	return  ;
}

/* EMV: Get Application Label - start -- jzg */
void vdDispAppLabel(unsigned char *ucHex, int inLen, char *szOutStr)
{
	int i;
	char szBuf[80] = {0};

	for (i = 0; i < inLen; i++)
		szBuf[i] = ucHex[i];
	szBuf[i] = 0x00;

	memcpy(szOutStr, szBuf, inLen);
}
/* EMV: Get Application Label - end -- jzg */

int inCTOSS_TWKRSAFlow(void)
{
	CTOS_LCDTClearDisplay();

	vdCTOS_TxnsBeginInit();
	srTransRec.HDTid = strEFT[0].inHDTid;
	int inRetVal = 0;

	inRetVal = inHTLESelectAcqVendorIDByHostId(srTransRec.HDTid);
	if (SUCCESS != inRetVal)
	{
		return ST_ERROR;
	}

	inCTOSS_TWKRSAFlowProcess();					
	inCTOS_inDisconnect();
	vdCTOS_TransEndReset();

	return ST_SUCCESS;
}



int inCTOS_SelecIPPPlan(void)
{
#define ITEMS_PER_PAGE          4

    char szIPP[50];
    char szDisplay[50];
    int inNumOfRecords = 0;
    short shCount = 0;
    short shTotalPageNum;
    short shCurrentPageNum;
    short shLastPageItems = 0;
    short shPageItems = 0;
    short shLoop;
    short shFalshMenu = 1;
     BYTE isUP = FALSE, isDOWN = FALSE;
	 BYTE bHeaderAttr = 0x01+0x04, iCol = 1;
    BYTE  x = 1;
    BYTE key, key2;
    char szHeaderString[50] = "SELECT PLAN";
    char szHeaderString2[50] = "SELECT TENURE";
    char szIPPMenu[1024];
    int inLoop = 0;
	short shMinLen = 1;
    short shMaxLen = 20;
    BYTE Bret;
	unsigned char szOutput[30];
    char szIPPTenureList[128];
    char szIPPTenureMenu[512];
    unsigned char bstatus = 0;


    memset(szIPPTenureMenu, 0x00, sizeof(szIPPTenureMenu));
    memset(szIPPMenu, 0x00, sizeof(szIPPMenu));
    vdDebug_LogPrintf("inCTOS_SelecIPPPlan");
    //get the index , then get all MID from the MMT list and prompt to user to select
    inIPPReadNumofRecords(&inNumOfRecords);
	
    vdDebug_LogPrintf("inNumOfRecords[%d]", inNumOfRecords);

	CTOS_KBDBufFlush();//cleare key buffer
    if(inNumOfRecords >= 1)
	{
	    for (inLoop = 0; inLoop < inNumOfRecords; inLoop++)
	    {
            strcat((char *)szIPPMenu, strIPP[inLoop].Plans);
			if(inLoop + 1 != inNumOfRecords)
            	strcat((char *)szIPPMenu, (char *)" \n");
	    }

		if(inNumOfRecords == 1)
		{
		   key = 1;
		}else{
	       key = MenuDisplay(szHeaderString, strlen(szHeaderString), bHeaderAttr, iCol, x, szIPPMenu, TRUE);
	    }

		if (key == 0xFF) 
		{
		    CTOS_LCDTClearDisplay();
		    setLCDPrint(1, DISPLAY_POSITION_CENTER, "WRONG INPUT!!!");
		    vduiWarningSound();
		    return -1;  
		}

    	if(key > 0)
	    {
	        if(d_KBD_CANCEL == key)
	            return -1;
	    }
	}

    srTransRec.HDTid = strIPP[key-1].HDTid;
	srTransRec.MITid = strIPP[key-1].MITid;

	//Start to select tenure
	memset(szIPPTenureList, 0, sizeof(szIPPTenureList));
    strcpy(szIPPTenureList, strIPP[key-1].szTenureList);
    vdDebug_LogPrintf("szIPPTenureList[%s]", szIPPTenureList);
    char *ip_arr[20] ;
    int num = 0, i;
//    num = split_str(szIPPTenureList, ',', ip_arr);
    num = split_strEx(szIPPTenureList, "|", ip_arr);
    vdDebug_LogPrintf("-szIPPTenureList[%s], num=%d", szIPPTenureList, num);
    if(num >= 1)
    {
        for (inLoop = 0; inLoop < num; inLoop++)
        {
            strcat((char *)szIPPTenureMenu, ip_arr[inLoop]);
            vdDebug_LogPrintf("ip_arr[%d]=%s", inLoop, ip_arr[inLoop]);
            strcat(szIPPTenureMenu, " Months");
            if(inLoop + 1 != num)
                strcat((char *)szIPPTenureMenu, (char *)" \n");
        }
        vdDebug_LogPrintf("-szIPPTenureMenu[%s]", szIPPTenureMenu);
        key2 = MenuDisplay(szHeaderString2, strlen(szHeaderString2), bHeaderAttr, iCol, x, szIPPTenureMenu, TRUE);
        vdDebug_LogPrintf("key2[%d]", key2);
        if (key2 == 0xFF)
        {
            CTOS_LCDTClearDisplay();
            setLCDPrint(1, DISPLAY_POSITION_CENTER, "WRONG INPUT!!!");
            vduiWarningSound();
            return -1;
        }

        if(key2 > 0)
        {
            if(d_KBD_CANCEL == key2)
                return -1;
            vdDebug_LogPrintf("-TENURE[%s]", ip_arr[key2 - 1]);
            srTransRec.IPPTenure = atoi(ip_arr[key2 - 1]);
            vdDebug_LogPrintf("-srTransRec.IPPTenure[%d]", srTransRec.IPPTenure);
        }
    }
// 	srTransRec.IPPTenure = strIPP[key-1].Tenure;
//end

	memcpy(srTransRec.IPPPlan, strIPP[key-1].Plans, sizeof(srTransRec.IPPPlan));
    srTransRec.IPPPlanID = atoi(strIPP[key-1].szPlanCode);
    vdDebug_LogPrintf("-srTransRec.IPPPlanID[%d]", srTransRec.IPPPlanID);
    return SUCCESS;
}

   


void vdCTOSS_RecoverRAM(void)
{
	int limitRAM;

	limitRAM = get_env_int("LIMITRAM");
	if (limitRAM < 0)
		limitRAM = 6000;
	
    vdCTOSS_RestoreMemory(limitRAM);

    return ;
    
}


void vdCTOSS_GetPurchaseIdentifier(TRANS_DATA_TABLE *srTransPara, int inMode, char *pOutData)
{
    int offset = 0;
    vdDebug_LogPrintf("vdCTOSS_GetPurchaseIdentifier, inMode[%d],srTransPara->ulTraceNum=%ld,srTransPara->byTransType=%d,srTransPara->byPackType=%d", inMode, srTransPara->ulTraceNum,srTransPara->byTransType, srTransPara->byPackType);
/*    if(inMode == 1)
    {

    }
    else*/
        {
            //batcch NO.
            DebugAddHEX("GetPurchaseIdentifier->szBatchNo",srTransPara->szBatchNo, 3);
            sprintf((char *)pOutData + offset, "%06ld", wub_bcd_2_long(srTransPara->szBatchNo,3));
            offset += 6;
            //Trace NO.
            pOutData[offset++] = '-';
            if((SALE_TIP == srTransPara->byTransType) || (VOID == srTransPara->byTransType) || (VOID_PREAUTH == srTransPara->byTransType))
                sprintf((char *)pOutData + offset, "%06ld", srTransPara->ulOrgTraceNum);
            else if(BATCH_UPLOAD == srTransPara->byPackType)
            {
                sprintf((char *)pOutData + offset, "%06ld", srTransPara->ulOrgTraceNum);
            }
            else
                sprintf((char *)pOutData + offset, "%06ld", srTransPara->ulTraceNum);
            offset += 6;
            pOutData[offset++] = '-';
            vdDebug_LogPrintf("srTransPara->ulTraceNum=%ld,ulOrgTraceNum=%ld", srTransPara->ulTraceNum, srTransPara->ulOrgTraceNum);
            //inv num
            DebugAddHEX("GetPurchaseIdentifier->szInvoiceNo",srTransPara->szInvoiceNo, 3);
            sprintf((char *)pOutData + offset, "%06ld", wub_bcd_2_long(srTransPara->szInvoiceNo,3));
            offset += 6;

            vdPCIDebug_HexPrintf("pOutData",pOutData, offset);
    }


}
//pOrderNum = date(YYMMDD) + time(HHMMSS) + TID
int inCTOS_GenerateOrderNum(char *pOrderNum, char *pTID) {
    CTOS_RTC SetRTC;
    BYTE szCurrentTime[20];
    CTOS_RTCGet(&SetRTC);
    sprintf(pOrderNum,"%02d%02d%02d%02d%02d%02d%s",SetRTC.bYear, SetRTC.bMonth, SetRTC.bDay, SetRTC.bHour, SetRTC.bMinute, SetRTC.bSecond, pTID);
    vdDebug_LogPrintf("pOrderNum: %s",pOrderNum);
    if((CARD_ENTRY_WAVE != srTransRec.byEntryMode) && (CARD_ENTRY_EASY_ICC != srTransRec.byEntryMode))
    {
        sprintf(szCurrentTime,"%02d%02d",SetRTC.bMonth, SetRTC.bDay);
        wub_str_2_hex(szCurrentTime,srTransRec.szDate,DATE_ASC_SIZE);
        sprintf(szCurrentTime,"%02d%02d%02d", SetRTC.bHour,SetRTC.bMinute,SetRTC.bSecond);
        wub_str_2_hex(szCurrentTime,srTransRec.szTime,TIME_ASC_SIZE);
        vdDebug_LogPrintf("szCurrentTime: %s",szCurrentTime);
    }

    return 0;
}


int inCTOS_ChkSettleStatus(void)
{
    int inNumOfHost = 0, inNum1;
    int inNumOfMit = 0, inNum2;
    char szBcd[INVOICE_BCD_SIZE + 1];
    char szAPName[50];
    int inAPPID;
    int inRet = d_NO;

    memset(szAPName, 0x00, sizeof (szAPName));
    inMultiAP_CurrentAPNamePID(szAPName, &inAPPID);

    inNumOfHost = inHDTNumRecord();
    vdDebug_LogPrintf("inNumOfHost=[%d]-----", inNumOfHost);

    for (inNum1 = 1; inNum1 <= inNumOfHost; inNum1++) {
        if (inHDTRead(inNum1) == d_OK) {
            vdDebug_LogPrintf("szAPName=[%s]-[%s]----", szAPName, strHDT.szAPName);
            if (strcmp(szAPName, strHDT.szAPName) != 0) {
                continue;
            }
            if (memcmp(strHDT.szHostLabel, "EFTSEC", 6) == 0) {
                continue;
            }

            inCPTRead(inNum1);
            srTransRec.HDTid = inNum1;
            strcpy(srTransRec.szHostLabel, strHDT.szHostLabel);
            memset(szBcd, 0x00, sizeof (szBcd));
            memcpy(szBcd, strHDT.szTraceNo, INVOICE_BCD_SIZE);
            inBcdAddOne(szBcd, strHDT.szTraceNo, INVOICE_BCD_SIZE);
            srTransRec.ulTraceNum = wub_bcd_2_long(strHDT.szTraceNo, 3);

            inMMTReadNumofRecords(strHDT.inHostIndex, &inNumOfMit);
            vdDebug_LogPrintf("inNumOfMit=[%d]-----", inNumOfMit);
            for (inNum2 = 0; inNum2 < inNumOfMit; inNum2++) {
                memcpy(&strMMT[0], &strMMT[inNum2], sizeof (STRUCT_MMT));
                srTransRec.MITid = strMMT[0].MITid;
                strcpy(srTransRec.szTID, strMMT[0].szTID);
                strcpy(srTransRec.szMID, strMMT[0].szMID);
                memcpy(srTransRec.szBatchNo, strMMT[0].szBatchNo, 4);
                strcpy(srTransRec.szHostLabel, strHDT.szHostLabel);

                inRet = inCTOS_ChkBatchStatus();
                if (d_NO == inRet)
                    return d_NO;
            }

        }
    }

    return d_OK;

}


int inCTOS_ChkBatchEmpty_AllHosts(void)
{
    int inNumOfHost = 0, inNum1;
    int inNumOfMit = 0, inNum2;
    char szBcd[INVOICE_BCD_SIZE + 1];
    char szAPName[50];
    int inAPPID;
    int inRet = d_NO;

    memset(szAPName, 0x00, sizeof (szAPName));
    inMultiAP_CurrentAPNamePID(szAPName, &inAPPID);

    inNumOfHost = inHDTNumRecord();
    vdDebug_LogPrintf("inNumOfHost=[%d]-----", inNumOfHost);

    for (inNum1 = 1; inNum1 <= inNumOfHost; inNum1++) {
        if (inHDTRead(inNum1) == d_OK) {
            vdDebug_LogPrintf("szAPName=[%s]-[%s]----", szAPName, strHDT.szAPName);
            if (strcmp(szAPName, strHDT.szAPName) != 0) {
                continue;
            }
            if (memcmp(strHDT.szHostLabel, "EFTSEC", 6) == 0) {
                continue;
            }

            inCPTRead(inNum1);
            srTransRec.HDTid = inNum1;
            strcpy(srTransRec.szHostLabel, strHDT.szHostLabel);
            memset(szBcd, 0x00, sizeof (szBcd));
            memcpy(szBcd, strHDT.szTraceNo, INVOICE_BCD_SIZE);
            inBcdAddOne(szBcd, strHDT.szTraceNo, INVOICE_BCD_SIZE);
            srTransRec.ulTraceNum = wub_bcd_2_long(strHDT.szTraceNo, 3);

            inMMTReadNumofRecords(strHDT.inHostIndex, &inNumOfMit);
            vdDebug_LogPrintf("inNumOfMit=[%d]-----", inNumOfMit);
            for (inNum2 = 0; inNum2 < inNumOfMit; inNum2++) {
                memcpy(&strMMT[0], &strMMT[inNum2], sizeof (STRUCT_MMT));
                srTransRec.MITid = strMMT[0].MITid;
                strcpy(srTransRec.szTID, strMMT[0].szTID);
                strcpy(srTransRec.szMID, strMMT[0].szMID);
                memcpy(srTransRec.szBatchNo, strMMT[0].szBatchNo, 4);
                strcpy(srTransRec.szHostLabel, strHDT.szHostLabel);

                inRet = inCTOS_ChkBatchEmpty();
                if (d_NO != inRet)
                    return d_NO;
            }

        }
    }

    return d_OK;

}


int inCTOS_ChkNoReveral_AllHosts(void)
{
    int inNumOfHost = 0, inNum1;
    int inNumOfMit = 0, inNum2;
    char szBcd[INVOICE_BCD_SIZE + 1];
    char szAPName[50];
    char szFileName[50];
    int inAPPID;
    int inRet = d_NO;

    memset(szAPName, 0x00, sizeof (szAPName));
    inMultiAP_CurrentAPNamePID(szAPName, &inAPPID);

    inNumOfHost = inHDTNumRecord();
    vdDebug_LogPrintf("inCTOS_ChkNoReveral_AllHosts, inNumOfHost=[%d]-----", inNumOfHost);

    for (inNum1 = 1; inNum1 <= inNumOfHost; inNum1++) {
        if (inHDTRead(inNum1) == d_OK) {
            vdDebug_LogPrintf("szAPName=[%s]-[%s]----", szAPName, strHDT.szAPName);
            if (strcmp(szAPName, strHDT.szAPName) != 0) {
                continue;
            }
            if (memcmp(strHDT.szHostLabel, "EFTSEC", 6) == 0) {
                continue;
            }

            srTransRec.HDTid = inNum1;

            inMMTReadNumofRecords(strHDT.inHostIndex, &inNumOfMit);
            vdDebug_LogPrintf("inNumOfMit=[%d]-----", inNumOfMit);
            for (inNum2 = 0; inNum2 < inNumOfMit; inNum2++) {
                memcpy(&strMMT[0], &strMMT[inNum2], sizeof (STRUCT_MMT));
//                srTransRec.MITid = strMMT[0].MITid;
                sprintf(szFileName, "%s%02d%02drev"
                        , strHDT.szHostLabel
                        , strHDT.inHostIndex
                        , strMMT[0].MITid);
                if((inMyFile_CheckFileExist(szFileName)) < 0)
                {
                    vdDebug_LogPrintf("inMyFile_CheckFileExist <0");
                    continue;
                } else{
                    vdDebug_LogPrintf("Got reversal file[%s]", szFileName);
                    return d_NO;
                }

            }

        }
    }

    return d_OK;

}

int inCTOS_ECR_ReadCard(void)
{
	int inRet;
	
    inRet = inCTOS_ECR_GetCardFields();
    if(d_OK != inRet)
        return inRet;

	return d_OK;
}

int inCTOS_ECR_GetCardFields(void)
{
    USHORT EMVtagLen;
    BYTE   EMVtagVal[64];
    BYTE byKeyBuf = 0;
    BYTE bySC_status;
    BYTE byMSR_status;
    BYTE szTempBuf[10];
    USHORT usTk1Len, usTk2Len, usTk3Len;
    BYTE szTk1Buf[TRACK_I_BYTES], szTk2Buf[TRACK_II_BYTES], szTk3Buf[TRACK_III_BYTES];
    usTk1Len = TRACK_I_BYTES ;
    usTk2Len = TRACK_II_BYTES ;
    usTk3Len = TRACK_III_BYTES ;
    int  usResult;

 DebugAddSTR("inCTOS_GetCardFields","Processing...",20); 
    //InsertCardUI();
	if (0 == fGetAppRunBySelf() && inMultiAP_CheckSubAPStatus() == d_OK)
     	{
     		vdDebug_LogPrintf("inMultiAP_CheckSubAPStatus");
        	return d_OK;
     	}
 SWIPE_AGAIN:

//	InsertCardUIEx();
#if 0
	if(strTCT.fManualInput == 1)
	{
		InsertCardUINew("0111");
	}else{
		InsertCardUINew("0110");
	}
#else
    InsertCardUINew("0100");
#endif

	vdDebug_LogPrintf("inCTOS_GetCardFields");
    if(d_OK != inCTOS_ValidFirstIdleKey())
    {
        //CTOS_LCDTClearDisplay();
		//vduiClearBelow(2);
        vdDispTransTitle(srTransRec.byTransType);

        inCTOS_DisplayIdleBMP();
    }
// patrick ECR 20140516 start
    if (strTCT.fECR) // tct
    {
    	if (memcmp(srTransRec.szBaseAmount, "\x00\x00\x00\x00\x00\x00", 6) != 0)
    	{
    		char szDisplayBuf[30];	
    		BYTE szTemp1[30+1];

    		CTOS_LCDTPrintXY(1, 7, "AMOUNT:");		 
    		memset(szDisplayBuf, 0x00, sizeof(szDisplayBuf));
    		wub_hex_2_str(srTransRec.szBaseAmount, szTemp1, AMT_BCD_SIZE);
			//gcitra-0728
    		//sprintf(szDisplayBuf, "%s %10lu.%02lu", strCST.szCurSymbol,atol(szTemp1)/100, atol(szTemp1)%100);
    		//CTOS_LCDTPrintXY(1, 8, szDisplayBuf);	
    		CTOS_LCDTPrintXY(1, 8, strCST.szCurSymbol);	
    		memset(szDisplayBuf,0x00,sizeof(szDisplayBuf));
			//format amount 10+2
			vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szTemp1, szDisplayBuf);
			//sprintf(szDisplayBuf,"%10.0f.%02.0f",(atof(szTemp1)/100), (atof(szTemp1)%100));
			//sprintf(szDisplayBuf, "%lu.%02lu", atol(szTemp1)/100, atol(szTemp1)%100);
    		setLCDPrint(8, DISPLAY_POSITION_RIGHT, szDisplayBuf);
			//gcitra-0728

    	}
    }
// patrick ECR 20140516 end
    CTOS_TimeOutSet (TIMER_ID_1 , GET_CARD_DATA_TIMEOUT_VALUE);
    ing_KeyPressed = 0;
    while (1)
    {
        if(CTOS_TimeOutCheck(TIMER_ID_1 )  == d_YES)
            return READ_CARD_TIMEOUT ;

        if (ing_KeyPressed == 'C')
        {
            vdDebug_LogPrintf("putchar C");
            CTOS_KBDBufPut('C');
            vdDebug_LogPrintf("return USER_ABORT");
            return USER_ABORT;
        }
        CTOS_KBDInKey(&byKeyBuf);
        vdDebug_LogPrintf("byKeyBuf=[%d][%c],ing_KeyPressed=%d", byKeyBuf, byKeyBuf, ing_KeyPressed);
        if ((byKeyBuf) || (d_OK == inCTOS_ValidFirstIdleKey()) || (ing_KeyPressed == 'M'))
        {
            vdDebug_LogPrintf("check byKeyBuf=[%d][%c]", byKeyBuf, byKeyBuf);
			if (byKeyBuf == d_KBD_CANCEL){
				CTOS_KBDBufFlush();
				return USER_ABORT;
			}
            memset(srTransRec.szPAN, 0x00, sizeof(srTransRec.szPAN));
            if(d_OK == inCTOS_ValidFirstIdleKey())
                srTransRec.szPAN[0] = chGetFirstIdleKey();
            
            vdDebug_LogPrintf("-srTransRec.szPAN[%s],byKeyBuf=[%d], inCTOS_ValidFirstIdleKey()=%d", srTransRec.szPAN, byKeyBuf, inCTOS_ValidFirstIdleKey());
            //get the card number and ger Expire Date
            if (d_OK != inCTOS_ManualEntryProcess(srTransRec.szPAN))
            {
                vdSetFirstIdleKey(0x00);
                CTOS_KBDBufFlush ();
                //vdSetErrorMessage("Get Card Fail M");
                return USER_ABORT;
            }
			vdSetFirstIdleKey(0x00);
            //Load the CDT table
            if (d_OK != inCTOS_LoadCDTIndex())
            {
                CTOS_KBDBufFlush();
                return USER_ABORT;
            }
            
            break;
        }
		
#if 0
        CTOS_SCStatus(d_SC_USER, &bySC_status);
        if(bySC_status & d_MK_SC_PRESENT)
        {
            vdCTOS_SetTransEntryMode(CARD_ENTRY_ICC);
            
            vdDebug_LogPrintf("--EMV flow----" );
            if (d_OK != inCTOS_EMVCardReadProcess ())
            {
                if(inFallbackToMSR == SUCCESS)
                {
                    vdCTOS_ResetMagstripCardData();
                    vdDisplayErrorMsg(1, 8, "PLS SWIPE CARD");
                    goto SWIPE_AGAIN;
                }
                else
                {
                    //vdSetErrorMessage("Get Card Fail C");
                    return USER_ABORT;
                }
            }
            vdDebug_LogPrintf("--EMV Read succ----" );
			if (srTransRec.byTransType == REFUND)
				vdCTOS_SetTransEntryMode(CARD_ENTRY_EASY_ICC);
            //Load the CDT table
            if (d_OK != inCTOS_LoadCDTIndex())
            {
                CTOS_KBDBufFlush();
                return USER_ABORT;
            }
            
            break;
        }

        //for Idle swipe card
        if (strlen(srTransRec.szPAN) > 0)
         {
             if (d_OK != inCTOS_LoadCDTIndex())
             {
                 CTOS_KBDBufFlush();
                 //vdSetErrorMessage("Get Card Fail");
                 return USER_ABORT;
             }

             if(d_OK != inCTOS_CheckEMVFallback())
             {
                vdCTOS_ResetMagstripCardData();
                vdDisplayErrorMsg(1, 8, "PLS INSERT CARD"); 
                
                goto SWIPE_AGAIN;

             }
                     
             break;
         
         }
#endif

        byMSR_status = CTOS_MSRRead(szTk1Buf, &usTk1Len, szTk2Buf, &usTk2Len, szTk3Buf, &usTk3Len);
		//Fix for Track2 Len < 35
        //if((byMSR_status == d_OK ) && (usTk2Len > 35))
        if(byMSR_status == d_OK )
		//Fix for Track2 Len < 35
        {
			usResult = shCTOS_SetMagstripCardTrackData(szTk1Buf, usTk1Len, szTk2Buf, usTk2Len, szTk3Buf, usTk3Len); 
        	if (usResult != d_OK)
			{
                 CTOS_KBDBufFlush();
				 vdDisplayErrorMsg(1, 8, "READ CARD FAILED");
                 return USER_ABORT;
             }
#if 0			
            if (d_OK != inCTOS_LoadCDTIndex())
             {
                 CTOS_KBDBufFlush();
                 return USER_ABORT;
             }
            
            if(d_OK != inCTOS_CheckEMVFallback())
             {
                vdCTOS_ResetMagstripCardData();
                vdDisplayErrorMsg(1, 8, "PLS INSERT CARD"); 
                
                goto SWIPE_AGAIN;

             }
#endif                
            break;
        }

       }

    if (srTransRec.byEntryMode == CARD_ENTRY_ICC)
    {    
        EMVtagLen = 0;
        if(EMVtagLen > 0)
        {
            sprintf(srTransRec.szCardLable, "%s", EMVtagVal);
        }
        else
        {
            strcpy(srTransRec.szCardLable, strIIT.szIssuerLabel);
        }
    }
    else
    {
        strcpy(srTransRec.szCardLable, strIIT.szIssuerLabel);
    }
    srTransRec.IITid = strIIT.inIssuerNumber;

    char szTxnTittleBuf[30] = {0};
    szGetTransTitle(srTransRec.byTransType, szTxnTittleBuf);
    usCTOSS_ClearAllShowTittle(szTxnTittleBuf);
    vdDebug_LogPrintf("srTransRec.byIsOnusCard=%d, srTransRec.byTransType[%d]srTransRec.IITid[%d], srTransRec.szCardLable=[%s]", srTransRec.byIsOnusCard, srTransRec.byTransType, srTransRec.IITid, srTransRec.szCardLable);
    if((srTransRec.byTransType == IPP) && (!srTransRec.byIsOnusCard))
    {
        //vdSetErrorMessage("INVALID CARD");
        //return d_NO;
    }

    return d_OK;
}

int inCTOS_EMVTCUploadComfort(void)
{
    int inRet;

	vdDebug_LogPrintf("Call TC upload?");
	vdDebug_LogPrintf("**inCTOS_EMVTCUploadComfort START**byTransType=[%d]",srTransRec.byTransType);
	if (srTransRec.byTransType == SALE_OFFLINE_FRM_ONLINE)
	{
		vdDebug_LogPrintf("SALE_OFFLINE_FRM_ONLINE no need TC");
		return d_OK;
	}
	
	DebugAddHEX("srTransRec.stEMVinfo.T84", srTransRec.stEMVinfo.T84, srTransRec.stEMVinfo.T84_len);
    if((CARD_ENTRY_ICC == srTransRec.byEntryMode)) // [sn] 20200408 crt-654 applicable to JCB chip
    {
        inCTLOS_Updatepowrfail(PFR_IDLE_STATE);
		vdDebug_LogPrintf("Processing TC upload");
		inRet = inProcessEMVTCUploadComfort(&srTransRec, -1);// TC upload

            vdDebug_LogPrintf("szFileName, %s%02d%02drev"
                                , strHDT.szHostLabel
                                , strHDT.inHostIndex
                                , srTransRec.MITid);
        inCTOS_inDisconnect();
    }
    
    return d_OK;
}

int inCTOS_SelectDinersAppHost(void) 
{
    int inHostIndex = DINERS_HOSTINDEX;
    int inCurrencyIdx=0;

	inDatabase_TerminalOpenDatabase();
    if ( inHDTReadEx(inHostIndex) != d_OK)
    {
        vdSetErrorMessage("HOST SELECTION ERR");
		inDatabase_TerminalCloseDatabase();// fix memoery lost
        return(d_NO);
    } 
    else 
    {
        srTransRec.HDTid = inHostIndex;
		srTransFlexiData.HDTid = inHostIndex;	// need this to restore NETS srTransRec.HDTid because inCTOS_GetCardFields (inCTOS_LoadCDTIndex load card PAN index which may != NETS index) is called later than inCTOS_SelectCurrAppHost
		srBatchFlexiData.HDTid = inHostIndex;	// need this to restore NETS srTransRec.HDTid because inCTOS_GetCardFields (inCTOS_LoadCDTIndex load card PAN index which may != NETS index) is called later than inCTOS_SelectCurrAppHost

        if ( inCPTReadEx(inHostIndex) != d_OK)
        {
            vdSetErrorMessage("LOAD CPT ERR");
			inDatabase_TerminalCloseDatabase();// fix memoery lost
            return(d_NO);
        }
		
		inDatabase_TerminalCloseDatabase();// fix memoery lost
        return (d_OK);
    }
}

int ginSaveReversalNeed = 0;

void vdSetSaveReversal(BOOL flg)
{
	ginSaveReversalNeed = flg;
}

BOOL fGetSaveReversal(void)
{
    return ginSaveReversalNeed;
}

