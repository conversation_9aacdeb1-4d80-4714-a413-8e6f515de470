/*******************************************************************************

*******************************************************************************/

#include <string.h>
#include <stdio.h>
#include <ctosapi.h>
#include <stdlib.h>
#include <stdarg.h>
#include <typedef.h>
#include <EMVAPLib.h>
#include <EMVLib.h>
#include<zlib.h>

#include "..\Includes\POSTypedef.h"
#include "..\Debug\Debug.h"

#include "..\Includes\POSMain.h"
#include "..\Includes\POSTrans.h"
#include "..\Includes\POSHost.h"
#include "..\Includes\POSSale.h"
#include "..\Includes\POSbatch.h"
#include "..\ui\Display.h"
#include "..\Includes\V5IsoFunc.h"
#include "..\Accum\Accum.h"
#include "..\print\Print.h"
#include "..\Comm\V5Comm.h"
#include "..\Includes\MultiApLib.h"
#include "..\Aptrans\MultiAptrans.h"
#include "..\Database\DatabaseFunc.h"
#include "..\erm\Poserm.h"

extern int inFirstUpdate;

int inCTOS_FileDLFlowProcess(void)
{
    int inRet = d_NO;

    USHORT ushEMVtagLen;
    BYTE   EMVtagVal[64];
    BYTE   szStr[64];


    vdCTOS_SetTransType(PARAM_DL);
    
    //display title
    vdDispTransTitle(PARAM_DL);
    
    inRet = inCTOSS_CheckMemoryStatus();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_GetTxnPassword();
    if(d_OK != inRet)
        return inRet;
    
    inRet = inCTOS_SelectHostEx();
    if(d_OK != inRet)
        return inRet;

    if (inMultiAP_CheckMainAPStatus() == d_OK)
    {
        inRet = inCTOS_MultiAPSaveData(d_IPC_CMD_ONLINES_SALE);
        if(d_OK != inRet)
            return inRet;
    }
    else
    {
        if (inMultiAP_CheckSubAPStatus() == d_OK)
        {
            inRet = inCTOS_MultiAPGetData();
            if(d_OK != inRet)
                return inRet;
            
            inRet = inCTOS_MultiAPReloadTable();
            if(d_OK != inRet)
                return inRet;
        }
        inRet = inCTOS_MultiAPCheckAllowd();
        if(d_OK != inRet)
            return inRet;
    }
	
  
    inRet = inCTOS_PreConnect();
    if(d_OK != inRet)
        return inRet;


    inRet = inBuildAndSendIsoData();
    if(d_OK != inRet)
        return inRet;
 
    vdSetErrorMessage("");

    return d_OK;
}

int inCTOS_TransFinishFlowProcess(void)
{
    int inRet = d_NO;

    USHORT ushEMVtagLen;
    BYTE   EMVtagVal[64];
    BYTE   szStr[64];

    vdCTOS_SetTransType(FINISH_TRANS);
    
    //display title
    vdDispTransTitle(FINISH_TRANS);
    
    inRet = inCTOSS_CheckMemoryStatus();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_GetTxnPassword();
    if(d_OK != inRet)
        return inRet;
    
    inRet = inCTOS_SelectHostEx();
    if(d_OK != inRet)
        return inRet;

    if (inMultiAP_CheckMainAPStatus() == d_OK)
    {
        inRet = inCTOS_MultiAPSaveData(d_IPC_CMD_ONLINES_SALE);
        if(d_OK != inRet)
            return inRet;
    }
    else
    {
        if (inMultiAP_CheckSubAPStatus() == d_OK)
        {
            inRet = inCTOS_MultiAPGetData();
            if(d_OK != inRet)
                return inRet;
            
            inRet = inCTOS_MultiAPReloadTable();
            if(d_OK != inRet)
                return inRet;
        }
        inRet = inCTOS_MultiAPCheckAllowd();
        if(d_OK != inRet)
            return inRet;
    }
	
  
    inRet = inCTOS_PreConnect();
    if(d_OK != inRet)
        return inRet;


    inRet = inBuildAndSendIsoData();
    if(d_OK != inRet)
        return inRet;

   
    vdSetErrorMessage("");

    return d_OK;
}


int inCTOS_TransFinish(void)
{
    int inRet = d_NO;
	BYTE bret = d_NO;
	
	vdDebug_LogPrintf("inCTOS_TransFinish");

		
	if(inFirstUpdate == 0)
		return d_OK;
    //CTOS_LCDTClearDisplay();
    
    vdCTOS_TxnsBeginInit();


	vdDebug_LogPrintf("inCTOS_TransFinishFlowProcess");
    inRet = inCTOS_TransFinishFlowProcess();

    inCTOS_inDisconnect();


    vdCTOS_TransEndReset();

    return inRet;
}






int inCTOS_UnzipFile(void)
{
   
	char szSystemCmdPath[250];
	char szFileNameBuf[20];
	
    memset(szFileNameBuf, 0x00, sizeof(szFileNameBuf));
	

	memset(szSystemCmdPath, 0x00, sizeof(szSystemCmdPath));
    sprintf(szSystemCmdPath, "gzip -dN %s.gz", szFileNameBuf);
	vdDebug_LogPrintf("szSystemCmdPath[%s]", szSystemCmdPath);
	system(szSystemCmdPath);	
	CTOS_Delay(200);

    return d_OK;
}


#define MAX_PRM_SIZE	(2000*1024)
#define MAX_PRM_BUF	6000

long inGzip_GetFileSize(const char* pchFileName)
{
    FILE  *fPubKey;
    long curpos,length;

    //vdDebug_LogPrintf("lnGetFileSize[%s]", pchFileName);
    fPubKey = fopen( (char*)pchFileName, "rb" );
    if(fPubKey == NULL)
        return 0;

    curpos=ftell(fPubKey);
    fseek(fPubKey,0L,SEEK_END);
    length=ftell(fPubKey);
    fseek(fPubKey,curpos,SEEK_SET);

    fclose(fPubKey);
    //vdDebug_LogPrintf("lnGetFileSize[%d],length=[%d]", curpos,length);

    return(length);
}

int inGzip_WriteFile(unsigned char *pchFileName, unsigned char *pchRecBuf, int inMaxRecSize)
{
    int h_file;
    int inRetVal = 0;
    FILE  *fPubKey;
    int times,i;
    long length;
    char szSystemCmdPath[250];

    vdDebug_LogPrintf("inWriteFile[%s],inMaxRecSize=[%d]", pchFileName, inMaxRecSize);

    length = inGzip_GetFileSize(pchFileName);

    fPubKey = fopen((char *)pchFileName, "ab+" );
    if(fPubKey == NULL)
    {
        fPubKey = fopen((char *)pchFileName, "wb+" );
        if(fPubKey == NULL)
            return -1;
    }

    if (inMaxRecSize > MAX_PRM_BUF)
    {
        times = inMaxRecSize/MAX_PRM_BUF;
        for (i = 0;i<times;i++)
        {
            inRetVal = fwrite(&pchRecBuf[i*MAX_PRM_BUF],MAX_PRM_BUF, 1, fPubKey);
        }

        times = inMaxRecSize%MAX_PRM_BUF;
        if(times>0)
        {
            inRetVal = fwrite(&pchRecBuf[i*MAX_PRM_BUF],times, 1, fPubKey);
        }

    }
    else

	inRetVal = fwrite(pchRecBuf,inMaxRecSize, 1, fPubKey);
    fclose(fPubKey);

    //vdDebug_LogPrintf("inWriteFile[%d].inMaxRecSize=[%d]...", inRetVal, inMaxRecSize);

    return inRetVal;
}



/* 返回ch字符在sign数组中的序号 */

int getIndexOfSigns(char ch)

{

    if(ch >= '0' && ch <= '9')

    {

        return ch - '0';

    }

    if(ch >= 'A' && ch <='F') 

    {

        return ch - 'A' + 10;

    }

    if(ch >= 'a' && ch <= 'f')

    {

        return ch - 'a' + 10;

    }

    return -1;

}


/* 十六进制数转换为十进制数 */

long hexToDec(char *source)

{

    long sum = 0;

    long t = 1;

    int i, len;

 

    len = strlen(source);

    for(i=len-1; i>=0; i--)

    {

        sum += t * getIndexOfSigns(*(source + i));

        t *= 16;

    }  

 

    return sum;

}

 




#define PARA_EN_FILE				"PARA.TXT"
#define PARA_FILE				"PARA.INI"



int inAnalyzeData(unsigned char *pchFileName)
{
	int h_file;
	int inRetVal = 0;
	FILE  *fPubKey;
	int times,i;	
    BYTE szHexZipData[1024];
    BYTE szStrZipData[2048];
    BYTE szClearData[1024+1];
	int inClearDataLen = 1024;
	int offset = 0;
	int inLen = 0;
	int inRet = 0;
	int c;
	int inTotalLen = 0;
	int inFileLen = 0;
	char ParaFileName[100];
	char szAPName[100];
    int inAPPID = 0;

	

    memset(szAPName, 0x00, sizeof(szAPName));
	inMultiAP_CurrentAPNamePID(szAPName, &inAPPID);

    memset(ParaFileName, 0x00, sizeof(ParaFileName));
    sprintf(ParaFileName, "/data/data/%s/%s", szAPName, PARA_FILE);
    vdDebug_LogPrintf("ParaFileName=[%s]",ParaFileName);


	inRetVal = inGzip_GetFileSize(ParaFileName);
	vdDebug_LogPrintf("[%s] =[%d] ", ParaFileName,inRetVal);
	if (inRetVal >= 0)
		remove(ParaFileName);
	
	vdDebug_LogPrintf("inAnalyzeData[%s]", pchFileName);

	inFileLen = inGzip_GetFileSize(pchFileName);
		
	vdDebug_LogPrintf("inFileLen[%d]", inFileLen);
	
	fPubKey = fopen((char *)pchFileName, "rb" );
	if(fPubKey == NULL)
		return -1;

	memset(szHexZipData, 0x00, sizeof(szHexZipData));
	inRetVal = fread (szHexZipData, 1, 2, fPubKey);

	inTotalLen = inTotalLen + inRetVal;
	
	vdDebug_LogPrintf("inRetVal=[%d]",inRetVal); 	
	DebugAddHEX("First 2 Bytes",szHexZipData,inRetVal);

	while(inFileLen != inTotalLen){

		memset(szHexZipData, 0x00, sizeof(szHexZipData));
		inRetVal = fread (szHexZipData, 1, 2, fPubKey);
	
		inTotalLen = inTotalLen + inRetVal;
		
		vdDebug_LogPrintf("inRetVal=[%d]",inRetVal);
		DebugAddHEX("Data Length",szHexZipData,inRetVal);

		wub_hex_2_str(szHexZipData, szStrZipData, 2);
		vdDebug_LogPrintf("szStrZipData=[%s]",szStrZipData);	

		inLen = hexToDec(szStrZipData);
		vdDebug_LogPrintf("inLen=[%d]",inLen);	


		memset(szHexZipData, 0x00, sizeof(szHexZipData));
		inRetVal = fread (szHexZipData, 1, inLen, fPubKey);
		
		inTotalLen = inTotalLen + inRetVal;
		
		vdDebug_LogPrintf("inRetVal=[%d]",inRetVal);	
		DebugAddHEX("szHexZipData",szHexZipData,inRetVal);

        memset(szClearData, 0x00, sizeof(szClearData));
		inRet = uncompress(szClearData,&inClearDataLen, szHexZipData, inLen);
		vdDebug_LogPrintf("inRet=[%d]",inRet);	

		vdDebug_LogPrintf("inClearDataLen=[%d]",inClearDataLen);	
		vdDebug_LogPrintf("szClearData=[%s]",szClearData);

		
		inGzip_WriteFile(ParaFileName, szClearData, inClearDataLen);
		
		vdDebug_LogPrintf("inTotalLen=[%d]",inTotalLen);	
		//DebugAddHEX("szClearData",szClearData,inRetVal);
	}


	
	fclose(fPubKey);

	return inRetVal;
}

extern int inDLLen;
extern int inDLTotalLen;
extern int inCurLen;



int inCTOS_FileDL(void)
{
    int inRet = d_NO;
	BYTE bret = d_NO;
	char GzipFileName[100];
	char szAPName[100];
    int inAPPID = 0;
	int inFileSize = 0;
	int inRetVal = 0;
	
	vdDebug_LogPrintf("inCTOS_FileDL");
    //CTOS_LCDTClearDisplay();

	
	if(inFirstUpdate == 0)
		return d_OK;

	#if 0
	inRet = inCTOS_ChkSettleStatus();
	
    if (d_OK != inRet) {
	   vdCTOS_TransEndReset();
	   return inRet;
    }
    vdSetErrorMessage("");
	#endif
    
    vdCTOS_TxnsBeginInit();

	memset(szAPName, 0x00, sizeof(szAPName));
    inMultiAP_CurrentAPNamePID(szAPName, &inAPPID);

    memset(GzipFileName, 0x00, sizeof(GzipFileName));
    sprintf(GzipFileName, "/data/data/%s/%s", szAPName, PARA_EN_FILE);
    vdDebug_LogPrintf("GzipFileName=[%s]",GzipFileName);

	
	inRetVal = inGzip_GetFileSize(GzipFileName);
	vdDebug_LogPrintf("[%s] =[%d] ", GzipFileName,inRetVal);
	if (inRetVal >= 0)
		remove(GzipFileName);

	inDLLen = 0;
	inCurLen = 0;
	inDLTotalLen = 0;


    do{

	  vdDebug_LogPrintf("inCTOS_FileDLFlowProcess");
      inRet = inCTOS_FileDLFlowProcess();	
	  vdSetConnectedFlag(1);
	  
	  vdDebug_LogPrintf("inCTOS_FileDLFlowProcess inRet[%d] inDLLen[%d] inCurLen[%d] inDLTotalLen[%d]", inRet, inDLLen, inCurLen, inDLTotalLen);
	  if(d_OK != inRet)
	  {
		vdSetConnectedFlag(0);
		return inRet;
	  }


    }while(inCurLen != inDLTotalLen);

	vdSetConnectedFlag(0);



	inFileSize = inGzip_GetFileSize(GzipFileName);	
    vdDebug_LogPrintf("inFileSize=[%d]",inFileSize);

	inAnalyzeData(GzipFileName);

	inDLLen = 0;
	inCurLen = 0;
	inDLTotalLen = 0;

    inCTOS_inDisconnect();


    vdCTOS_TransEndReset();

    return inRet;
}

int TestZlib(void)
{

    BYTE szHexZipData[1024];
    BYTE szStrZipData[2048];
    BYTE szClearData[4096];
	int inHexZipDataLen = 194;
	ULONG inClearDataLen = 1024;
	int inRet = 0;

	
	memset(szStrZipData, 0x00, sizeof(szStrZipData));	
	memset(szHexZipData, 0x00, sizeof(szHexZipData));

	memcpy(szStrZipData, "789CED92410A02310C45F78237292549934E137021A8E8C6853ADEFF28A6D58A030A2E06DC98D2F0F3F3E9E2D1804A1173891811C04A010CB599B8552412B0DF30996A0A6A0A8C29668CA2314978D12F89EFDE4131F4AC98BB5E87CDC9C8968BDD1169B53F5F905C5F5DAB170092819780EADDC896AB41B5F9CAD786ED489D1F53CE442A83862E2C3315E10142176F32133EE113AD39533F25BF1E379D7C6AE453239F3AF9D4C8EB9FFCECE4B7E3F3CF7323CF8D3C77F2DCC80F3393BF010028CF35", inHexZipDataLen*2);

	wub_str_2_hex(szStrZipData, szHexZipData, inHexZipDataLen*2);

	
	DebugAddHEX("szHexZipData",szHexZipData,inHexZipDataLen);

    memset(szClearData, 0x00, sizeof(szClearData));
	inRet = uncompress(szClearData,&inClearDataLen, szHexZipData, inHexZipDataLen);
	
	vdDebug_LogPrintf("inRet=[%d] Z_OK[%d] Z_MEM_ERROR[%d]  Z_BUF_ERROR[%d] Z_DATA_ERROR[%d]",inRet, Z_OK, Z_MEM_ERROR, Z_BUF_ERROR, Z_DATA_ERROR);	

	vdDebug_LogPrintf("inClearDataLen=[%d]",inClearDataLen);	
	vdDebug_LogPrintf("szClearData=[%s]",szClearData);	
	DebugAddHEX("szClearData",szClearData,inClearDataLen);
}


int TestZlib2(void)
{

    int err;
    BYTE compr[200], uncompr[200];    // big enough
    uLong comprLen, uncomprLen;
    const char* hello = "12345678901234567890123456789012345678901234567890";
 
    uLong len = strlen(hello) + 1;
    comprLen  = sizeof(compr) / sizeof(compr[0]);
 
    err = compress(compr, &comprLen, (const Bytef*)hello, len);
 
    if (err != Z_OK) {
		vdDebug_LogPrintf("compess error: =[%d]",err);	
    }

	
	vdDebug_LogPrintf("orignal size:  =[%d], compressed size :  =[%d]",len, comprLen);
	
	DebugAddHEX("compr",compr,comprLen);

    strcpy((char*)uncompr, "garbage");
 
    err = uncompress(uncompr, &uncomprLen, compr, comprLen);
 
    if (err != Z_OK) {		
	    vdDebug_LogPrintf("uncompess error: =[%d]",err);	
    }

	vdDebug_LogPrintf("orignal size:  =[%d], uncompressed size :  =[%d]",len, uncomprLen);  
 
    if (strcmp((char*)uncompr, hello)) {		
	    vdDebug_LogPrintf("BAD uncompress!!!\n");	
    } else {
	    vdDebug_LogPrintf("uncompress() succeed: \n[%s]", uncompr);	
    }

}

int TestZlib1(void) {


    unsigned char szSrc[] = "test the compression and uncompression of zlib.";

    unsigned long nSrcLen = sizeof(szSrc);


    unsigned char szZip[1024] = {0};

    unsigned long nZipLen = 1024;


    compress(szZip, &nZipLen, szSrc, nSrcLen);





    unsigned char szUnZip[1024] = {0};

    unsigned long nUnZipLen = 1024;


    uncompress(szUnZip, &nUnZipLen, szZip, nZipLen);




    vdDebug_LogPrintf("Src:  =[%s], len:  =[%d]",szSrc, nSrcLen);
    vdDebug_LogPrintf("nZipLen:  =[%d]",nZipLen);
    DebugAddHEX("Zip:",szZip,nZipLen);
    vdDebug_LogPrintf("UnZip:  =[%s], len:  =[%d]",szUnZip, nUnZipLen);


    return 0;

}


int inGetAppVersionEx(void)
{
    unsigned char szMsg[10];
    unsigned char szAppVer[20];

    memset(szMsg, 0x00, sizeof(szMsg));
    memset(szAppVer, 0x00, sizeof(szAppVer));

    inGetAppVersion(szMsg, szAppVer);
    vdDebug_LogPrintf("szAppVer[%s]", szAppVer);

    return 0;

}

