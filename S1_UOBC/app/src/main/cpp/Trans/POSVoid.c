/*******************************************************************************

*******************************************************************************/

#include <string.h>
#include <stdio.h>
#include <ctosapi.h>
#include <stdlib.h>
#include <stdarg.h>
#include <typedef.h>


#include "..\Includes\POSTypedef.h"


#include "..\Includes\POSMain.h"
#include "..\Includes\POSTrans.h"
#include "..\Includes\POSHost.h"
#include "..\Includes\POSSale.h"
#include "..\Includes\POSbatch.h"
#include "..\ui\Display.h"
#include "..\Includes\V5IsoFunc.h"
#include "..\Accum\Accum.h"
#include "..\print\Print.h"
#include "..\Comm\V5Comm.h"
#include "..\Includes\MultiApLib.h"
#include "..\Aptrans\MultiAptrans.h"
#include "..\erm\Poserm.h"

#include "..\Debug\Debug.h"

int inCTOS_VoidFlowProcess(void)
{
    int inRet = d_NO;
	TRANS_DATA_TABLE srtmpTransRec;

    vdCTOS_SetTransType(VOID);
    
    //display title
    vdDispTransTitle(VOID);
    
    inRet = inCTOS_GetTxnPassword();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_CheckTranAllowd();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_GeneralGetInvoice();
    if(d_OK != inRet)
        return inRet;
#if 0
    if (inMultiAP_CheckMainAPStatus() == d_OK)
    {
		vdDebug_LogPrintf("inMultiAP_CheckMainAPStatus");
        inRet = inCTOS_MultiAPBatchSearch(d_IPC_CMD_VOID_SALE);
        if(d_OK != inRet)
            return inRet;
    }
    else
    {
        vdDebug_LogPrintf("not inMultiAP_CheckMainAPStatus");
	    if (1 == fGetAppRunBySelf() && inMultiAP_CheckSubAPStatus() == d_OK)
	    {
			   inRet = inCTOS_MultiAPBatchSearch(d_IPC_CMD_VOID_SALE);
               if(d_OK != inRet)
               return inRet;
	    }

		if (0 == fGetAppRunBySelf() && inMultiAP_CheckSubAPStatus() == d_OK)
        {
            inRet = inCTOS_MultiAPGetVoid();
            if(d_OK != inRet)
                return inRet;
        }       
        inRet = inCTOS_BatchSearch();
        if(d_OK != inRet)
            return inRet;
    }

#endif	
	inRet = inCTOS_BatchSearch();
	if(d_OK != inRet)
		return inRet;

    inRet = inCTOS_CheckVOID();
    if(d_OK != inRet)
        return inRet;
    
    inRet = inCTOS_LoadCDTandIIT();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_VoidSelectHost();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_CheckMustSettle();
    if(d_OK != inRet)
        return inRet;

	memset(&srtmpTransRec,0x00,sizeof(TRANS_DATA_TABLE));
	memcpy(&srtmpTransRec,&srTransRec,sizeof(TRANS_DATA_TABLE));
		

	inRet = inCTOS_WaveGetCardFields();
	if(srTransRec.byTransType == VOID)
	{
		vdSetDisplayMsg(0);
		if(inCheckPan(srTransRec.szPAN, szOriginalPAN) != d_OK)
		    inRet = d_NO;
	}
	if(d_OK != inRet)
	{
		if(inGetDisplayMsg() != 1)
		    TransCancelledUI();		
		vdSetDisplayMsg(0);
		return inRet;
	}

	//fix get card field change iit
	memset(&srTransRec,0x00,sizeof(TRANS_DATA_TABLE));
	memcpy(&srTransRec,&srtmpTransRec,sizeof(TRANS_DATA_TABLE));
	
    inRet = inCTOS_ConfirmInvAmt();
    if(d_OK != inRet)
        return inRet;

    inRet = inBuildAndSendIsoData();
    if(d_OK != inRet)
    {
        NotApprovedUIVoid();
        return inRet;
    }

	vdCTOSS_UpdateVoidOffline();
    inRet = inCTOS_SaveBatchTxn();
    if(d_OK != inRet)
        return inRet;

   // inRet = inCTOS_UpdateAccumTotal();
   // if(d_OK != inRet)
   //     return inRet;

		// patrick add code 20141205 start
    inRet = inMultiAP_ECRSendSuccessResponse();
/*
	inRet = inCTOSS_ERM_ReceiptRecvVia();
	if(d_OK != inRet)
        return inRet;
	
	if (isCheckTerminalMP200() == d_OK)
	{
		vdCTOSS_DisplayStatus(d_OK);
	}
	
    inRet = ushCTOS_printReceipt();
    if(d_OK != inRet)
        return inRet;
    else
        vdSetErrorMessage("");
  */      
    return d_OK;
}

int inCTOS_VOID(void)
{
    int inRet = d_NO;
    
    //CTOS_LCDTClearDisplay();
    
    vdCTOS_TxnsBeginInit();
    
    inRet = inCTOS_VoidFlowProcess();

    inCTOS_inDisconnect();

	if(d_OK == inRet)
	inCTOSS_UploadReceipt();

	if (isCheckTerminalMP200() == d_OK)
	{
		//CTOS_KBDBufFlush();
		if (d_OK != inRet)
		vdCTOSS_DisplayStatus(inRet);
		//WaitKey(5);
	}
	
    vdCTOS_TransEndReset();

    return inRet;
}



int inCTOS_VoidPreauthFlowProcess(void)
{
    int inRet = d_NO;


    vdCTOS_SetTransType(VOID_PREAUTH);
    
    //display title
    vdDispTransTitle(VOID_PREAUTH);
    
    inRet = inCTOS_GetTxnPassword();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_CheckTranAllowd();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_GeneralGetInvoiceAndApprvoedCode();
    if(d_OK != inRet)
        return inRet;

    if (inMultiAP_CheckMainAPStatus() == d_OK)
    {
        inRet = inCTOS_MultiAPBatchSearch(d_IPC_CMD_VOID_SALE);
        if(d_OK != inRet)
            return inRet;
    }
    else
    {
        if (inMultiAP_CheckSubAPStatus() == d_OK)
        {
            inRet = inCTOS_MultiAPGetVoid();
            if(d_OK != inRet)
                return inRet;
        }       
        inRet = inCTOS_BatchSearch();
        if(d_OK != inRet)
            return inRet;
    }

    inRet = inCTOS_CheckVOIDPreauth();
    if(d_OK != inRet)
        return inRet;
    
    inRet = inCTOS_LoadCDTandIIT();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_VoidSelectHost();
    if(d_OK != inRet)
        return inRet;

/*	inRet = inCTOS_CheckCUPTranAllowd();
    if(d_OK != inRet)
        return inRet;*/

    inRet = inCTOS_CheckMustSettle();
    if(d_OK != inRet)
        return inRet;
    
    inRet = inCTOS_ConfirmInvAmt();
    if(d_OK != inRet)
        return inRet;

    inRet = inBuildAndSendIsoData();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_SaveBatchTxn();
    if(d_OK != inRet)
        return inRet;

    //inRet = inCTOS_UpdateAccumTotal();
    //if(d_OK != inRet)
    //    return inRet;

		// patrick add code 20141205 start
    inRet = inMultiAP_ECRSendSuccessResponse();

	if (isCheckTerminalMP200() == d_OK)
	{
		vdCTOSS_DisplayStatus(d_OK);
	}
	
    inRet = ushCTOS_printReceipt();
    if(d_OK != inRet)
        return inRet;
    else
        vdSetErrorMessage("");
        
    return d_OK;
}


int inCTOS_VOIDPREAUTH(void)
{
    int inRet = d_NO;
    
    //CTOS_LCDTClearDisplay();
    
    vdCTOS_TxnsBeginInit();
    
    inRet = inCTOS_VoidPreauthFlowProcess();

    inCTOS_inDisconnect();

	if(d_OK == inRet)
	inCTOSS_UploadReceipt();

	if (isCheckTerminalMP200() == d_OK)
	{
		//CTOS_KBDBufFlush();
		if (d_OK != inRet)
		vdCTOSS_DisplayStatus(inRet);
		//WaitKey(5);
	}
	
    vdCTOS_TransEndReset();

    return inRet;
}

