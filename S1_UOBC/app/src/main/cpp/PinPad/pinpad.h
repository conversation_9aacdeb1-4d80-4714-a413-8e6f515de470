
#ifndef ___PINPAD_H___
#define	___PINPAD_H___

#ifdef	__cplusplus
extern "C" {
#endif

	void vdCTOSS_SetPinByPass(int inPinByPass);
	int inCTOSS_GetPinByPass(void);
    int inInitializePinPad(void);
    void TEST_Write3DES_Plaintext(void);
    void inCTOS_DisplayCurrencyAmount(BYTE *szAmount, int inLine);
    void OnGetPINDigit(BYTE NoDigits);
    void OnGetPINCancel(void);
    void OnGetPINBackspace(BYTE NoDigits);
    int inGetIPPPin(void);
    int inIPPGetMAC(BYTE *szDataIn, int inLengthIn, BYTE *szInitialVector, BYTE *szMAC);
    int inCalculateMAC(BYTE *szDataIn, int inLengthIn, BYTE *szMAC);
    void vdCTOS_PinEntryPleaseWaitDisplay(void);
	int inCheckKeys(USHORT ushKeySet, USHORT ushKeyIndex);
	int GetPIN_With_3DESDUKPT(int byEntryMode);
	void TEST_Write3DESDUKPT_Plaintext(void);
	int inCTOS_KMS2PINGetExDukpt(USHORT KeySet,  USHORT KeyIndex,  BYTE* pInData, BYTE* szPINBlock, BYTE* szKSN);
	USHORT inCTOS_KMS2PINGetEx3Des(USHORT KeySet,  USHORT KeyIndex,  BYTE* pInData, BYTE* szPINBlock, BYTE* szKSN, USHORT pinBypassAllow);
	int inEFTNAC_WriteClearKey(int inKeySet, int inKeyIndex, unsigned char *szClearKey);
	
#ifdef	__cplusplus
}
#endif

#endif	/* ___PINPAD_H___ */

