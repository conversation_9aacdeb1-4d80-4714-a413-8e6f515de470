#include <string.h>
#include <stdio.h>
#include <stdlib.h>
/** These two files are necessary for calling CTOS API **/
#include <ctosapi.h>
#include "../Includes/POSbatch.h"
#include "../Includes/POSTypedef.h"

#include "../FileModule/myFileFunc.h"
#include "../POWRFAIL/POSPOWRFAIL.h"
#include "..\debug\debug.h"
#include "..\Database\DatabaseFunc.h"
#include "..\Includes\myEZLib.h"
#include "..\ui\Display.h"
#include "..\Includes\POSHost.h"
#include "..\debug\debug.h"
#include "../Ctls/POSCtls.h"
#include "../Accum/accum.h"

#define DINERS_HOSTINDEX       3


int inCTOS_SaveBatchTxn(void)
{
    int inResult ;
    char BeloFloorLimitFlg = 0x30;
    int inSaveType;
    char szSTAN[6+1];
	BYTE szInvoiceNo[12+1];

    vdDebug_LogPrintf("inCTOS_SaveBatchTxn");
	inCTLOS_Updatepowrfail(PFR_BEGIN_BATCH_UPDATE);
	
    inCTOSS_GetBatchFieldData(&srTransFlexiData, AMEX_BELOW_FLOOR_LIMIT, &BeloFloorLimitFlg, 1);


   if(srTransRec.byTransType == SALE
   		|| srTransRec.byTransType == SALE_OFFLINE_FRM_ONLINE
		|| srTransRec.byTransType == SALE_OFFLINE)
   {	
		memset(szInvoiceNo, 0x00, sizeof(szInvoiceNo));
		
		wub_hex_2_str(srTransRec.szInvoiceNo, szInvoiceNo, INVOICE_BCD_SIZE);		
	    vdDebug_LogPrintf("inCTOS_SaveBatchTxn szInvoiceNo[%s]", szInvoiceNo);

	    put_env("TRACENUMD",szInvoiceNo, 12);
   }
	
    
    if(srTransRec.byOffline == CN_TRUE)
    {
        if (strlen((char *)srTransRec.szRRN) < 1)
        {
            sprintf(szSTAN, "%06ld", srTransRec.ulTraceNum);
            wub_hex_2_str(srTransRec.szTime, srTransRec.szRRN, 3);
            memcpy(srTransRec.szRRN+6, szSTAN, 6);
        }
    }
	
    if((srTransRec.byTransType == SALE) && (srTransRec.stDCCinfo.bySelectedCurrency == 2))
    {
        DebugAddHEX(".szLocalAmt=", srTransRec.stDCCinfo.szLocalAmt, 12);
        DebugAddHEX(".szDccAmt=", srTransRec.stDCCinfo.szDccAmt, 12);
        DebugAddHEX(".szTotalAmount=", srTransRec.szTotalAmount, 6);
        wub_str_2_hex(srTransRec.stDCCinfo.szDccAmt, srTransRec.szTotalAmount, 12);
    }
    
    vdDebug_LogPrintf("inCTOS_SaveBatchTxn byTransType[%d],byOrgTransType[%d].byOffline[%d].byUploaded[%d]", srTransRec.byTransType, srTransRec.byOrgTransType, srTransRec.byOffline, srTransRec.byUploaded);
    if((srTransRec.byTransType == VOID /*|| srTransRec.byTransType == VOID_PREAUTH*/) && srTransRec.byOffline == CN_TRUE)
    {
        //OFFLINE SALE, then VOID it, the VOID is offline, should delete the OFFLINE SALE Advice
        if(srTransRec.byUploaded == CN_FALSE)
        {
            inCTOSS_DeleteAdviceByINV(srTransRec.szInvoiceNo);
        }
    }
    
    if((srTransRec.byTransType == SALE) || (srTransRec.byTransType == SALE_OFFLINE_FRM_ONLINE) || (srTransRec.byTransType == REFUND) || (srTransRec.byTransType == SALE_OFFLINE) || (srTransRec.byTransType == PRE_AUTH) || (srTransRec.byTransType == IPP) || (srTransRec.byTransType == PRE_COMP))
    {
        inSaveType = DF_BATCH_APPEND;

        //Should be Online void the Intial SALE amount.
        if(srTransRec.byOffline == CN_FALSE)
        {
            //use szStoreID to store how much amount fill up in DE4 for VOID
            //either enter tip or not enter tip are ok
            memcpy(srTransRec.szStoreID, srTransRec.szTotalAmount, 6);
        }
    }
    else
    {
        inSaveType = DF_BATCH_UPDATE;
    }
    
    if(srTransRec.byTransType == VOID || VOID_PREAUTH == srTransRec.byTransType)
        srTransRec.byVoided = TRUE;

    if(srTransRec.byTransType == PRE_COMP)
    {
        vdDebug_LogPrintf("srTransRec.byAuthStatus=[%d]",srTransRec.byAuthStatus);
        srTransRec.byAuthStatus = 1;
    }
    else if(srTransRec.byTransType == VOID_PREAUTH)
    {
        vdDebug_LogPrintf("srTransRec.byAuthStatus=[%d]",srTransRec.byAuthStatus);
        srTransRec.byAuthStatus = 2;
    }

	inResult = inDatabase_BatchSave(&srTransRec, inSaveType);
    
    if(inResult != ST_SUCCESS)
    {
    	vdSetErrorMessage("Batch Save Error");
        return ST_ERROR;
    }
            
    if((memcmp(srTransRec.szAuthCode, "Y3",2))  && 
       (srTransRec.byTransType != SALE_OFFLINE) &&
       (srTransRec.byTransType != SALE_ADJUST)  &&
       (srTransRec.byTransType != SALE_TIP) &&
       ( srTransRec.byOffline != VS_TRUE))
    {
        inMyFile_ReversalDelete();
    }
        
    if((srTransRec.byTransType == SALE_TIP) || (srTransRec.byTransType == SALE_ADJUST) || (srTransRec.byTransType == SALE_OFFLINE))
    {
        //fix send tip adjust advie two times issue
        inCTOSS_DeleteAdviceByINV(srTransRec.szInvoiceNo);
        inMyFile_AdviceSave(&srTransRec, srTransRec.byPackType);
    }
    //if original txn is offline, then do a online txn piggy back the offline advice, the offline advice been deleted, then void it, the void is offline, need to create advice for this offline void
    else if((srTransRec.byTransType == VOID || srTransRec.byTransType == VOID_PREAUTH) && srTransRec.byOffline == CN_TRUE && srTransRec.byUploaded == CN_TRUE)
    {
        vdDebug_LogPrintf("Create Advice for offline VOID");
        inMyFile_AdviceSave(&srTransRec, OFFLINE_VOID);
    }
    else if((srTransRec.byTransType == PRE_COMP) && (srTransRec.byOffline == CN_TRUE))
    {
        vdDebug_LogPrintf("Create Advice for offline PRE_COMP");
        inMyFile_AdviceSave(&srTransRec, PRE_COMP);
    }
    //remove ctls txn tc
//    else if((srTransRec.byTransType == SALE) && (srTransRec.byEntryMode == CARD_ENTRY_ICC))
	else if (((srTransRec.byEntryMode == CARD_ENTRY_ICC) /*||
	*//* EMV: Revised EMV details printing - start -- jzg *//*
	(srTransRec.bWaveSID == d_VW_SID_JCB_WAVE_QVSDC) ||
	(srTransRec.bWaveSID == d_VW_SID_AE_EMV) ||
	(srTransRec.bWaveSID == d_VW_SID_PAYPASS_MCHIP) ||
	(srTransRec.bWaveSID == d_VW_SID_VISA_WAVE_QVSDC))*/ && (srTransRec.byTransType == SALE)))
	/* EMV: Revised EMV details printing - end -- jzg */ // patrick fix contactless 20140828
    {
       if((srTransRec.byTCuploaded == 0) ||
          (!memcmp(srTransRec.szAuthCode, "Y1",2)) || 
          (!memcmp(srTransRec.szAuthCode, "Y3",2)) 
         )
       {
           if((memcmp(srTransRec.szAuthCode, "Y1",2)) && 
              (memcmp(srTransRec.szAuthCode, "Y3",2)) 
             )
           {
		   		if (srTransRec.HDTid != AMEX_HOST_INDEX)
		   		{
					srTransRec.byPackType = TC_UPLOAD;
					inMyFile_TCUploadFileSave(&srTransRec, srTransRec.byPackType);
		   		}
           }
           else
           {
               srTransRec.byPackType = SEND_ADVICE;
               inMyFile_AdviceSave(&srTransRec, srTransRec.byPackType);
           }
       }
    }
    else if((srTransRec.byTransType == SALE) 
        && (srTransRec.byEntryMode == CARD_ENTRY_MSR || srTransRec.byEntryMode == CARD_ENTRY_MANUAL) 
        && ( VS_TRUE == srTransRec.byOffline)
        && 0x31 == BeloFloorLimitFlg)
    {
        srTransRec.byPackType = SEND_ADVICE;
        inMyFile_AdviceSave(&srTransRec, srTransRec.byPackType);
    }
    else if((/*srTransRec.byTransType == REFUND || */srTransRec.byTransType == PRE_AUTH) && (srTransRec.byEntryMode == CARD_ENTRY_ICC))
    {
       	if (srTransRec.HDTid != AMEX_HOST_INDEX)
       	{
			srTransRec.byPackType = TC_UPLOAD;
			inMyFile_TCUploadFileSave(&srTransRec, srTransRec.byPackType);
		}           
    }

    return d_OK;
}


char szBatchMsg[600];

int inCTOS_BatchReviewFlow(void)
{
    int inResult;
    int inReadRecResult;
 	int inCount = 0;
	int inBatchRecordNum = 0;
    BYTE key;
	char szPanDisplay[24+1];
    BYTE szTemp1[30+1];
	int  *pinTransDataid = NULL;	
	int inFlag = 0;

	inBatchRecordNum = inBatchNumRecord();

	pinTransDataid = (int*)malloc(inBatchRecordNum * sizeof(int));
	
	inBatchByMerchandHost(inBatchRecordNum, srTransRec.HDTid, srTransRec.MITid, srTransRec.szBatchNo, pinTransDataid);

	inResult = inDatabase_BatchReadByTransId(&srTransRec, pinTransDataid[inCount]);
	
	if(inResult == d_NO)
	{
	    vdDisplayErrorMsg(1, 8, "BATCH EMPTY");
		free(pinTransDataid);
	    return d_OK;
	}
	
REVIEW:
    inCTOS_DisplayBatchRecordDetail(BATCH_REVIEW);
    
    while(1)
	{ 
		vdDebug_LogPrintf("szBatchMsg[%s]", szBatchMsg);
		key = usCTOSS_UpDown(szBatchMsg);


		//CTOS_KBDHit(&key); 
		
		vdDebug_LogPrintf("usCTOSS_UpDown[%d]", key);
		if (key == d_KBD_CANCEL) 
		{ 
			free(pinTransDataid);
		    return inResult;
		} 
		else if ((key == d_KBD_DOWN) || (key == d_KBD_DOT))
	    {
           inCount++;

            vdDebug_LogPrintf("inCount=%d", inCount);

		    if(inCount >= inBatchRecordNum)
		   {
		      vdDisplayErrorMsgNoTimeout(1, 8, "END OF RECORD");
			  inCount = inBatchRecordNum - 1;
			  inFlag = 1;			  		  			  
		   }
		  
		   inResult = inDatabase_BatchReadByTransId(&srTransRec, pinTransDataid[inCount]);
		   
		   inCTOS_DisplayBatchRecordDetail(BATCH_REVIEW);	

		    if(inFlag == 1)
			{
				strcat(szBatchMsg, "|");
				strcat(szBatchMsg, "    ");
				strcat(szBatchMsg, "|");
	            strcat(szBatchMsg, "END OF RECORD");
				inFlag = 0;
				vdDebug_LogPrintf("szBatchMsg[%s]", szBatchMsg);
			}
		} 
        else if ((key == d_KBD_UP) || (key == d_KBD_00))
        { 
            inCount--;

            inResult = inDatabase_BatchReadByTransId(&srTransRec, pinTransDataid[inCount]);

            if(inResult == d_NO)
            {
				vdDisplayErrorMsgNoTimeout(1, 8, "FIRST OF RECORD");
				inFlag = 1;
                inCount = 0;	  
                inResult = inDatabase_BatchReadByTransId(&srTransRec, pinTransDataid[inCount]);
            }

            inCTOS_DisplayBatchRecordDetail(BATCH_REVIEW);

			if(inFlag == 1)
			{
				strcat(szBatchMsg, "|");
				strcat(szBatchMsg, "    ");
				strcat(szBatchMsg, "|");
				strcat(szBatchMsg, "FIRST OF RECORD");
				inFlag = 0;
				vdDebug_LogPrintf("szBatchMsg[%s]", szBatchMsg);
			}
		} 
    } 	
    
	free(pinTransDataid);
    return d_OK;
    
}


void vdDiners_EmptyBatch(void)
{
    int         shHostIndex = 1;
    int         inResult,inRet;
    ACCUM_REC srAccumRec;
    STRUCT_FILE_SETTING strFile;

    
//    vduiLightOn();                

    srTransRec.HDTid = DINERS_HOSTINDEX;
    strHDT.inHostIndex = DINERS_HOSTINDEX;
    inHDTRead(DINERS_HOSTINDEX);
    inCPTRead(DINERS_HOSTINDEX);
    strcpy(srTransRec.szHostLabel, strHDT.szHostLabel);

  

    inResult = inCTOS_CheckAndSelectMutipleMID();
    if(d_OK != inResult)
        return;
            

    // delete batch where hostid and mmtid is matcj
    inDatabase_BatchDelete();

    memset(&srAccumRec, 0x00, sizeof(ACCUM_REC));
    memset(&strFile,0,sizeof(strFile));
    vdCTOS_GetAccumName(&strFile, &srAccumRec);

    if((inResult = CTOS_FileDelete(strFile.szFileName)) != d_OK)
    {
        vdDebug_LogPrintf("[inMyFile_SettleRecordDelete]---Delete Record error[%04x]", inResult);
    }
    //create the accum file
	memset(&srAccumRec, 0x00, sizeof(ACCUM_REC));
	inCTOS_ReadAccumTotal(&srAccumRec);
	
    inCTOS_DeleteBKAccumTotal(&srAccumRec,strHDT.inHostIndex,srTransRec.MITid);

//    inMyFile_ReversalDelete();

//    inMyFile_AdviceDelete();
    
//    inMyFile_TCUploadDelete();

//    CTOS_LCDTClearDisplay();
    //setLCDPrint(5, DISPLAY_POSITION_CENTER, "RESET NPX");
    //setLCDPrint(6, DISPLAY_POSITION_CENTER, "RECORD DONE");
//    CTOS_Delay(1000); 
               
}

