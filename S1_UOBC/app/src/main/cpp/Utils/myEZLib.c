/** 
**  A Template for developing new terminal shared application
**/

#include <string.h>
#include <stdio.h>
#include <stdlib.h>
#include <stdarg.h>
#include <ctosapi.h>

/** These two files are necessary for calling CTOS API **/
#include "../Includes/myEZLib.h"
#include "../Includes/POSTypedef.h"

#include "../FileModule/myFileFunc.h"
#include "..\Includes\Wub_lib.h"
#include "..\Includes\myEZLib.h"
#include "..\Includes\epad.h"


#define SHOW_SCRREN 1
#define d_LINE_DOT 12

BOOL fLogByUSB;
BOOL fLogByRS232;
BOOL fZMKByRS232;
BOOL ONUSB = FALSE; 

#define d_LCD_Width 16+1


void vdMyEZLib_ComPortInit()
{
    int inResult;
    USHORT usVendorID=0x0CA6, usProductID=0xA050, usRtn;
    
    fLogByUSB   = FALSE;
    fLogByRS232 = FALSE; 
    
    CTOS_RS232Close(DBG_PORT);
    if(ONUSB==TRUE)
    {
    
        CTOS_USBHostOpen(usVendorID, usProductID); 
        fLogByUSB = TRUE;   
    }
    else
    {            
        if((inResult=CTOS_RS232Open(DBG_PORT, 115200, 'N', 8, 1)) == d_OK)
        {
            fLogByRS232 = TRUE;    
        }        
    }
}



void vdMyEZLib_Printf(const char* fmt, ...)
{
	char printBuf[2048];
	char msg[2048];
	char tmpbuf[50];
	int times = 0,i;
	int inSendLen;
	va_list marker;
	BYTE baTemp[PAPER_X_SIZE * 64];   
	CTOS_FONT_ATTRIB stFONT_ATTRIB;

	memset(msg, 0x00, sizeof(msg));
	
	va_start( marker, fmt );
	vsprintf( msg, fmt, marker );
	va_end( marker );
	
	memset(printBuf, 0x00, sizeof(printBuf));
	strcat(printBuf, msg);
	strcat(printBuf ,"\n" );
	
	inSendLen = strlen(printBuf);
	
	memset (baTemp, 0x00, sizeof(baTemp));
	stFONT_ATTRIB.FontSize = d_FONT_16x16;		// Font Size = 12x24
	stFONT_ATTRIB.X_Zoom = 1;		// The width magnifies X_Zoom diameters
	stFONT_ATTRIB.Y_Zoom = 2;		// The height magnifies Y_Zoom diameters
	stFONT_ATTRIB.X_Space = 0;		// The width of the space between the font with next font
	stFONT_ATTRIB.Y_Space = 0;		// The Height of the space between the font with next font		  
	if (strTCT.inFontFNTMode != 1)
	{
		stFONT_ATTRIB.FontSize = d_FONT_9x18;
		stFONT_ATTRIB.Y_Zoom = 1;
	}
	if (inSendLen > 48)
	{
		times = inSendLen/48;
		for (i=0; i<times; i++)
		{
			memset (baTemp, 0x00, sizeof(baTemp));
			memset (tmpbuf, 0x00, sizeof(tmpbuf));
			memcpy(tmpbuf,&printBuf[i*48],48);
			//CTOS_PrinterBufferPutString((BYTE *)baTemp, 1, 1, tmpbuf, &stFONT_ATTRIB);
			//CTOS_PrinterBufferOutput((BYTE *)baTemp, 3);
			inCCTOS_PrinterBufferOutput(tmpbuf,&stFONT_ATTRIB,0);
		}

		times = inSendLen%48;
		if (times > 0)
		{
			memset (baTemp, 0x00, sizeof(baTemp));
			memset (tmpbuf, 0x00, sizeof(tmpbuf));
			memcpy(tmpbuf,&printBuf[i*48],times);
			//CTOS_PrinterBufferPutString((BYTE *)baTemp, 1, 1, tmpbuf, &stFONT_ATTRIB);
			//CTOS_PrinterBufferOutput((BYTE *)baTemp, 3);
			inCCTOS_PrinterBufferOutput(tmpbuf,&stFONT_ATTRIB,0);
		}
	}
	else
	{
		//CTOS_PrinterBufferPutString((BYTE *)baTemp, 1, 1, printBuf, &stFONT_ATTRIB);
		//CTOS_PrinterBufferOutput((BYTE *)baTemp, 3);
		inCCTOS_PrinterBufferOutput(printBuf,&stFONT_ATTRIB,0);
	}
	
}




/*==========================================================================*
 *Name:vdMyEZLib_LogPrintf                                                  *                           
 *input: as Like vdMyEZLib_LogPrintf("%s", test);                           *           
 *output: void                                                              *
 *Description:                                                              *
 *  Caller can ouput that any data type wanted to check to console by RS232 *
 *==========================================================================*/
void vdMyEZLib_LogPrintff(unsigned char *fmt, short inlen)
{

    char printBuf[2048];
    char msg[2048];
    short i=0;
    
        
    memset(msg, 0x00, sizeof(msg));
    memset(printBuf, 0x00, sizeof(printBuf));
    memset(printBuf, 0x30, inlen*2);
    for ( i=0 ;i<inlen; i++)
    {
        sprintf(&printBuf[i*2], "%02X", fmt[i]);
    }

    vdMyEZLib_LogPrintf(printBuf);
      
}


void vdMyEZLib_Rpad(char *pad, char *space)
{
    short inlen;
    
    for(inlen=0; inlen<(79-strlen(pad)); inlen ++)
        space[inlen]=0x20;
}


void vdMyEZLib_LogPrintf(const char* fmt, ...)
{
    char printBuf[2048];
    char msg[2048];
    char space[100];
    int inSendLen;
    va_list marker;
    int j = 0;
               
    if(fLogByUSB == FALSE && fLogByRS232 == FALSE)
        return;

    memset(msg, 0x00, sizeof(msg));
    memset(printBuf, 0x00, sizeof(printBuf));
    memset(space, 0x00, sizeof(space));
    
    va_start( marker, fmt );
    vsprintf( msg, fmt, marker );
    va_end( marker );
    
    memset(printBuf, 0x00, sizeof(printBuf));       
    strcat(printBuf, msg);
    strcat(printBuf, space);
    strcat(printBuf ,"\n" );
    
    inSendLen = strlen(printBuf);

    
    j = (int)strTCT.byRS232DebugPort;        
    if(j == 1 ) //Remark this line to view log
    {
           
        if(fLogByUSB==TRUE)
        {       
            if (CTOS_USBTxReady() == d_OK)
                CTOS_USBTxData(printBuf, inSendLen);
        }
        else
        {        
            while(CTOS_RS232TxReady(DBG_PORT) != d_OK);
            CTOS_RS232TxData(DBG_PORT, printBuf, inSendLen);    
         
        }
    }
    return;        
}

int inAscii2Bcd(BYTE *szAscii, BYTE *szBcd, int inBcdLen)
{
    int inAsciiLen = inBcdLen*2;
    int inBcdIdx;
    int inAsciiIdx;
    BYTE szAscii2[inBcdLen*2+1];
    BYTE byLo;
    BYTE byHi;
        
    memset(szAscii2, 0x00, sizeof(szAscii2));
    /*pad zero for ascii buffer*/
    if((inAsciiLen = strlen(szAscii)) <= (inBcdLen*2))
    {
        memset(szAscii2, 0x30, inBcdLen*2);
        memcpy(&szAscii2[(inBcdLen*2)-inAsciiLen], szAscii, inAsciiLen);        
        vdMyEZLib_LogPrintf("[inAscii2Bcd]---szAscii2[%s], szAscii[%s]", szAscii2, szAscii);
    }
    else
    {       
        memcpy(szAscii2, szAscii, inBcdLen*2);
    }   
        
    
    inAsciiIdx=0;
    inBcdIdx=0;
    while(inBcdIdx < inBcdLen)
    {
        //for Hi Bit
        if(szAscii2[inAsciiIdx] >= 'A' && szAscii2[inAsciiIdx] <= 'F')
            byHi = (szAscii2[inAsciiIdx]-0x37) << 4; // 0x61 - 0x37 = 0x0A  
        else if(szAscii2[inAsciiIdx] >= 'a' && szAscii2[inAsciiIdx] <= 'f')
            byHi = (szAscii2[inAsciiIdx]-0x57) << 4;        
        else
            byHi = szAscii2[inAsciiIdx] << 4;   
        
        
        //for Low Bit
        if(szAscii2[inAsciiIdx+1] >= 'A' && szAscii2[inAsciiIdx+1] <= 'F')
            byLo = (szAscii2[inAsciiIdx+1]-0x37) & 0x0F; // 0x61('A') - 0x37 = 0x0A 
        else if(szAscii2[inAsciiIdx+1] >= 'a' && szAscii2[inAsciiIdx+1] <= 'f')
            byLo = (szAscii2[inAsciiIdx+1]-0x57) & 0x0F;        
        else
            byLo = szAscii2[inAsciiIdx+1] & 0x0F;   
                
        
        //byLo = szAscii2[inAsciiIdx+1] & 0x0F;
                                        
        szBcd[inBcdIdx] = byHi | byLo;
               
        
        inBcdIdx++;
        inAsciiIdx = inBcdIdx *2;
                
    }
    
    
    return TRUE;        
    
}


int inBcd2Ascii(BYTE *szBcd, BYTE *szAscii, int inBcdLen)
{
    int inBcdIdx;
    int inAsciiIdx;
    BYTE szAscii2[3];
    BYTE byLo;
    BYTE byHi;
    
    inBcdIdx = 0;
    inAsciiIdx = 0;
    while(inBcdIdx < inBcdLen)
    {
        sprintf(szAscii2, "%02x", szBcd[inBcdIdx]);
        memcpy(&szAscii[inAsciiIdx], szAscii2, 2);
        inBcdIdx ++;
        inAsciiIdx = inBcdIdx*2;        
    }

    return TRUE;    
    
}

int inBcd2Bin(BYTE *szBcd, int inBcdLen)
{
    char szAscii[inBcdLen*2+1];
    int inBin;
    
    memset(szAscii, 0x00, sizeof(szAscii));

    if(inBcd2Ascii(szBcd, szAscii, inBcdLen))
    {
        inBin = atoi(szAscii);
        return inBin;
    }                        
    
    return ST_ERROR;
}

int inBin2Bcd(int inBin, BYTE *szBcd, int inBcdLen)
{
    char szAscii[inBcdLen*2+1];
    memset(szAscii, 0x00, sizeof(szAscii));

    sprintf(szAscii, "%d", inBin);
    vdMyEZLib_LogPrintf("[inBin2Bcd]---Bin2Ascii[%s]",szAscii);
    
    
    return(inAscii2Bcd(szAscii, szBcd, inBcdLen));
}

int inBcdAddOne(BYTE *szInBcd, BYTE *szOutBcd, int inBcdLen)
{
    int inBin;
    int inResult;
    char szAscii[inBcdLen*2+1];
    
    
    memset(szAscii, 0x00, sizeof(szAscii));
        
    /*BCD to Bin*/
    inBin = inBcd2Bin(szInBcd, inBcdLen);
    vdMyEZLib_LogPrintf("[inBcdAddOne]---BCD 2 Bin[%d]", inBin);
    
    /*Bin++*/
    inBin++;
    
    /*Bin to BCD*/
    inResult = inBin2Bcd(inBin, szOutBcd, inBcdLen);
        
    return inResult;
    
}

   
//gcitra
void vdFormatAmount(char *str, char *strCur, char *strAmt, BOOL fVoid)
{
	int inAmtLen = 0,
		inCtr = 0,
		inTmpLen = 0,
		i = 0;
	char szTmpBuf[101] = {0},
		szTmpAmt[101] = {0},
		szDec[3] = {0};

	memset(szTmpBuf, 0, sizeof(szTmpBuf));
	inAmtLen = strlen(strAmt) -2;

	memcpy(szDec, &strAmt[inAmtLen], 2);
	memcpy(szTmpAmt, strAmt, inAmtLen);

	for (i = strlen(szTmpAmt)-1; i >= 0; i--)
	{
		if (inCtr >= 3)
		{
			szTmpBuf[inTmpLen] = ',';
			++i;
			inCtr = 0;
		}
		else
		{
			szTmpBuf[inTmpLen] = szTmpAmt[i];
			inCtr++;
		}
		inTmpLen++;
	}
	inTmpLen--;

	memset(szTmpAmt, 0, sizeof(szTmpAmt));
	for (i = 0; i <= inTmpLen; i++)
		szTmpAmt[i] = szTmpBuf[inTmpLen - i];

	if (strlen(strCur) > 0)
	{
		strCur[3] = 0x00;
		
		if (fVoid == TRUE)
			sprintf(str, "-%s %s.%s", strCur, szTmpAmt, szDec);
		else
			sprintf(str, "%s %s.%s", strCur, szTmpAmt, szDec);
	}
	else
	{
		if (fVoid == TRUE)
			sprintf(str, "-%s.%s", szTmpAmt, szDec);
		else
			sprintf(str, "%s.%s", szTmpAmt, szDec);
	}

}
//gcitra

/*************************************************************************************************
*
*	The following code has been extracted from a CVision library, it therefore does not reflect
*	the coding standards of SoftPay. All code except is contained within this block of code.
*	Simon_h2	2nd Feb 2000
*
*************************************************************************************************/
#define CURRENCY_SYMBOL         'C'
#define CURRENCY_SYMBOL_NO_PAD  'c'
#define NUMERIC_VALUE           'N'
#define NUMERIC_VALUE_NO_PAD    'n'

char formatAmt(char *into, char*amt,char *currency, char *format, VS_BOOL print)
{
    int n, writePos = 0;
    int max;
    int amtCount = strlen(amt)-1;
    int currencyCount;// = strlen(currency)-1;
    int nCount = 0;
    int addNs = 0;
    char seenANoPad = 0;
    char forcePad = 0;
    char formatstring[20+1];
    char newAmt[20+1];
    VS_BOOL first = VS_TRUE;

    /* Don't want to change original format because the caller uses it later */
    memset(formatstring,0x00,sizeof(formatstring));
    strcpy(formatstring,format);

    /* Don't want to change original amt because the caller uses it later */
    memset(newAmt,0x00,sizeof(newAmt));
    strcpy(newAmt,amt);

	currencyCount = strlen(currency)-1;

    for (n=0, max = strlen(formatstring); n < max; n++)
    {
	    if(formatstring[n] == NUMERIC_VALUE || formatstring[n] == NUMERIC_VALUE_NO_PAD)
	    {
	        if (first)
	        {
	            /* If this is our first n then mark it so we can */
	            /* insert before it */
	            first = VS_FALSE;
	            writePos = n;
	        }
	        /* count how many numeric digits */
	        nCount++;
	    } /* end if numeric digit */
	} /* end for all of the chars in formatstring */

	#if 0
    /* we are probably printing on a receipt or report and we want to */
    /* make sure we print all of the numeric digits */
    if ((amtCount+1) > 0 && print)
    {
		if ((amtCount+1) > nCount)
		{
		    addNs = (amtCount+1) - nCount;
		    if ( (strlen(formatstring) + addNs) <= FORMATTED_AMOUNT_SIZE)
		    {
		        for(n=addNs; n > 0; n--)
		        {
		            insert_char(formatstring, writePos, NUMERIC_VALUE_NO_PAD);
		        }
		    } /* end if we have room in the formatstring string */
		    
		} /* end if we need more room to print the numbers in the amount */
		    
    } /* if amount count is greater than zero */
	#endif


    /* If we have a negative amount don't print it next to the last digit */
    /* print it after the padding so that NNN,NNN.NN won't look like */
    /* $ 000,0-5.00     instead it will look like  $ -00,005.00  */
    if (newAmt[0] == '-')
    {
       formatstring[writePos] = '-';
       /* Don't print the negative from the amt string, print from formatstring */
       strcpy(newAmt,&newAmt[1]);
       amtCount--;
    }
    
    
    for(n=0, max = strlen(formatstring); n < max && !forcePad; n++)
    {
        if(formatstring[n] == CURRENCY_SYMBOL)
            forcePad = 1;
		if(formatstring[n] == NUMERIC_VALUE || formatstring[n] == NUMERIC_VALUE_NO_PAD)
			break;	/* No Pad Force required if currency after amount */
    }
    into[strlen(formatstring)] = '\0';
    for(writePos = n = strlen(formatstring)-1; n >= 0; n--)
    {
        switch(formatstring[n])
        {
            case CURRENCY_SYMBOL:
            case CURRENCY_SYMBOL_NO_PAD:
				if(currencyCount >= 0)
				{
					into[writePos--] = currency[currencyCount--];
				}
                break;
            case NUMERIC_VALUE:
                if(amtCount < 0)
                {
                    if(seenANoPad)
                    {
                        if(forcePad)
                            into[writePos--] = ' ';
                    }
                    else
                        into[writePos--] = '0';
                }
                else
	            {
                   into[writePos--] = newAmt[amtCount--];
                }
               break;
           case NUMERIC_VALUE_NO_PAD:
               if(amtCount < 0)
               {
                   if(forcePad)
                       into[writePos--] = ' ';
               }
               else
               {
                   into[writePos--] = newAmt[amtCount--];
               }
               seenANoPad = 1;
               break;
           case ',':
           case '.':
			   if(formatstring[n-1] == NUMERIC_VALUE_NO_PAD && amtCount < 0)
				   seenANoPad = 1;	// Shouldn't be a ',' if no character printed after it !!
               if(!seenANoPad  || (amtCount >= 0 && newAmt[amtCount] != '-'))
                   into[writePos--] = formatstring[n];
			   else
			   if(forcePad)
				   into[writePos--] = ' ';
               break;
           default:
               into[writePos--] = formatstring[n];
               break;
       }
   }
   if(writePos >= 0)
       memmove(into, &into[writePos+1], strlen(formatstring)-writePos+1);
   
   return  amtCount <= 0;	// Returns 1 if amount fully expressed
}

/*-------------------------------------------------------------------------
    Function :     vdFormatAmount
    Description :
  		Formats an amount string by padding zeroes, padding spaces in the begining
        etc. The size of the amount string is defined as AMT_STR_SIZE. Also puts
        decimal point at the proper posn.
    Parameters :
    Returns :
    Globals :
    Notes :
    Modification History :
      #     Date      Who                     Description
    ----- -------- ---------- ---------------------------------------------
	  1   06/09/95  Henry_G1   Eliminated the padding of spaces.
--------------------------------------------------------------------------*/
// rename to vdFormatAmountEx because already has existing name
void vdFormatAmountEx (char *pchAmt, BOOL fPutSymbol)

{
	char szCurSym[3 + 1];
	char szLocAmt[20 + 1];

    /** /pdebug(("--vdFormatAmount--"));*/

	memset(szLocAmt, '\0', sizeof(szLocAmt));

	//ju change from str2int to str2long dun know why str2int returns me a -ve number for omni 3750...
	if((strlen(pchAmt) <= 3) && (atol(pchAmt) < 0))			//(str2long(pchAmt) < 0)
		sprintf( &pchAmt[1], "%03li", (-1L) * atol(pchAmt));	//str2long(pchAmt));


    if (fPutSymbol)
    {
		inHDTRead(srTransRec.HDTid);
    	inCSTRead(strHDT.inCurrencyIdx);
	    strcpy( szCurSym, strCST.szCurSymbol);					//strcpy( szCurSym, szGetCurSymbol() );
    }
	else
	    memset(szCurSym, 0x00, sizeof(szCurSym));
	strcpy(szLocAmt, pchAmt); //szLocAmt[strlen(szLocAmt)] = '\0';//strlcpyNETS(szLocAmt, pchAmt, FORMATTED_AMOUNT_SIZE);
	formatAmt(pchAmt, szLocAmt, szCurSym, "cnnn,nnN.NN", VS_TRUE);

}

