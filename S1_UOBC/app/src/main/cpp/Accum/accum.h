
#ifndef ACCUM_H
#define ACCUM_H

#include "../Includes/Trans.h"
#include "../Includes/POSTypedef.h"
#include "../Filemodule/myFileFunc.h"
//#include "../FileModule/myFileFunc.h"


#define MAX_ISSUERS		20
#define MAX_TOTAL_SETS		2
#define MULTIPLE_CURRENCY_ACCUM "MUL.total"

typedef struct TagACCUM_REC
{
    BYTE        szTID[TERMINAL_ID_BYTES+1];
    BYTE        szMID[MERCHANT_ID_BYTES+1];
    BYTE        szYear[DATE_BCD_SIZE+1];
    BYTE        szDate[DATE_BCD_SIZE+1];
    BYTE        szTime[TIME_BCD_SIZE+1];
    BYTE        szBatchNo[BATCH_NO_BCD_SIZE+1];
    BYTE        szSOCNum[TRACE_NO_ASC_SIZE+1];
	TRANS_TOTAL stBankTotal[MAX_TOTAL_SETS];
} ACCUM_REC;

ACCUM_REC gStMultiCurrencyAccum;

int inCTOS_UpdateAccumTotal(void);
int inCTOS_SaveAccumTotal(ACCUM_REC *strTotal);
int inCTOS_SaveDccAccumTotal(ACCUM_REC *strTotal);
int inCTOS_ReadAccumTotal(ACCUM_REC *strTotal);
int inCTOS_ReadDccAccumTotal(ACCUM_REC *strTotal);
int inCTOS_ClearAccumTotal(void);

int inCTOS_FileCopy(char *szSourceFile, char *szDesFile,ULONG ulFileSize);

int inCTOS_DeleteBKAccumTotal(ACCUM_REC *strTotal,int HostIndex, int MITid);
int inCTOS_DeleteBKDccAccumTotal(ACCUM_REC *strTotal,int HostIndex, int MITid);

int inCTOS_ReadBKAccumTotal(ACCUM_REC *strTotal,int inhostIndex, int inMITid);
int inCTOS_ReadBKDccAccumTotal(ACCUM_REC *strTotal,int inhostIndex, int inMITid);
void vdCTOS_SetBackupAccumFile(char *szOriFileName);
void vdCTOS_SetBackupDccAccumFile(char *szOriFileName);
void vdCTOS_GetBackupAccumFile(STRUCT_FILE_SETTING *strFile, ACCUM_REC *strTotal, int HostIndex, int MITid);
void vdCTOS_GetAccumName(STRUCT_FILE_SETTING *strFile, ACCUM_REC *strTotal);

void vdCTOS_GetDccAccumName(STRUCT_FILE_SETTING *strFile, ACCUM_REC *strTotal);
int inCTOS_ReadMultiCurrencyAccumTotal(ACCUM_REC *strTotal);

#endif


