
#include <string.h>
#include <stdio.h>
#include <stdlib.h>
#include <ctosapi.h>


#include "accum.h"
#include "..\FileModule\myFileFunc.h"
#include "..\Includes\POSTrans.h"
#include "..\POWRFAIL\POSPOWRFAIL.h"
#include "..\Debug\Debug.h"
#include "..\Includes\Wub_lib.h"
#include "..\Includes\myEZLib.h"
#include "..\Includes\POSSetting.h"
#include "..\ui\Display.h"
#include "../Ctls/POSCtls.h"

void vdCTOS_GetAccumName(STRUCT_FILE_SETTING *strFile, ACCUM_REC *strTotal)
{
    sprintf(strFile->szFileName, "ACC%02d%02d.total"
                                , strHDT.inHostIndex
                                , srTransRec.MITid);
                                
    strFile->bSeekType           = d_SEEK_FROM_BEGINNING;
    strFile->bStorageType        = d_STORAGE_FLASH ;
    strFile->fCloseFileNow       = TRUE;
    strFile->ulRecSize           = sizeof(ACCUM_REC);
    strFile->ptrRec              = strTotal;    
    
}

void vdCTOS_GetDccAccumName(STRUCT_FILE_SETTING *strFile, ACCUM_REC *strTotal)
{
    sprintf(strFile->szFileName, "DCC%02d%02d.total"
            , strHDT.inHostIndex
            , srTransRec.MITid);

    strFile->bSeekType           = d_SEEK_FROM_BEGINNING;
    strFile->bStorageType        = d_STORAGE_FLASH ;
    strFile->fCloseFileNow       = TRUE;
    strFile->ulRecSize           = sizeof(ACCUM_REC);
    strFile->ptrRec              = strTotal;

}


int inCTOS_ReadAccumTotal(ACCUM_REC *strTotal)
{
    STRUCT_FILE_SETTING strFile;
    
    memset(&strFile, 0x00, sizeof(STRUCT_FILE_SETTING));
    memset(strTotal, 0x00, sizeof(ACCUM_REC));
    
    vdCTOS_GetAccumName(&strFile, strTotal);

    return (inMyFile_RecRead(&strFile));    
}

int inCTOS_ReadDccAccumTotal(ACCUM_REC *strTotal)
{
    STRUCT_FILE_SETTING strFile;

    memset(&strFile, 0x00, sizeof(STRUCT_FILE_SETTING));
    memset(strTotal, 0x00, sizeof(ACCUM_REC));

    vdCTOS_GetDccAccumName(&strFile, strTotal);

    return (inMyFile_RecRead(&strFile));
}

int inCTOS_ReadMultiCurrencyAccumTotal(ACCUM_REC *strTotal)
{
    STRUCT_FILE_SETTING strFile;

    memset(&strFile, 0x00, sizeof(STRUCT_FILE_SETTING));
    memset(strTotal, 0x00, sizeof(ACCUM_REC));
    sprintf(strFile.szFileName, MULTIPLE_CURRENCY_ACCUM);

    strFile.bSeekType           = d_SEEK_FROM_BEGINNING;
    strFile.bStorageType        = d_STORAGE_FLASH ;
    strFile.fCloseFileNow       = TRUE;
    strFile.ulRecSize           = sizeof(ACCUM_REC);
    strFile.ptrRec              = strTotal;
//    vdCTOS_GetDccAccumName(&strFile, strTotal);

    return (inMyFile_RecRead(&strFile));
}

int inCTOS_SaveAccumTotal(ACCUM_REC *strTotal)
{
    STRUCT_FILE_SETTING strFile;
    
    memset(&strFile, 0x00, sizeof(STRUCT_FILE_SETTING));
    
    vdCTOS_GetAccumName(&strFile, strTotal);

    return (inMyFile_RecSave(&strFile));    
}

int inCTOS_SaveDccAccumTotal(ACCUM_REC *strTotal)
{
    STRUCT_FILE_SETTING strFile;

    memset(&strFile, 0x00, sizeof(STRUCT_FILE_SETTING));

    vdCTOS_GetDccAccumName(&strFile, strTotal);

    return (inMyFile_RecSave(&strFile));
}

int inCTOS_SaveMultiCurrencyAccumTotal(ACCUM_REC *strTotal)
{
    STRUCT_FILE_SETTING strFile;

    memset(&strFile, 0x00, sizeof(STRUCT_FILE_SETTING));
    sprintf(strFile.szFileName, MULTIPLE_CURRENCY_ACCUM);
    strFile.bSeekType           = d_SEEK_FROM_BEGINNING;
    strFile.bStorageType        = d_STORAGE_FLASH ;
    strFile.fCloseFileNow       = TRUE;
    strFile.ulRecSize           = sizeof(ACCUM_REC);
    strFile.ptrRec              = strTotal;

    return (inMyFile_RecSave(&strFile));
}

//format amount 10+2 change all atol to atof
int inCTOS_UpdateAccumTotal(void)
{
	ACCUM_REC srAccumRec;
    BYTE        szTransAmt[12+1];
    BYTE        szTipAmt[12+1];
    BYTE        szOrgAmt[12+1];
    BYTE        szOrgTIPAmt[12+1];	
    int         inResult;
	int inTranCardType = 0;

    vdDebug_LogPrintf("inCTOS_UpdateAccumTotal");
    
    memset(szTransAmt, 0x00, sizeof(szTransAmt));
    memset(szTipAmt, 0x00, sizeof(szTipAmt));
    memset(szOrgAmt, 0x00, sizeof(szOrgAmt));
	memset(szOrgTIPAmt, 0x00, sizeof(szOrgTIPAmt));
    wub_hex_2_str(srTransRec.szTotalAmount, szTransAmt, 6);
    wub_hex_2_str(srTransRec.szTipAmount, szTipAmt, 6);
    wub_hex_2_str(srTransRec.szOrgAmount, szOrgAmt, 6);
	wub_hex_2_str(srTransRec.szOrgTIPAmount, szOrgTIPAmt, 6);

	if (srTransRec.inCardType == CREDIT_CARD)
	{
		inTranCardType = 0;//save credit card accue total
	}
    else
    {
    	inTranCardType = 1;//save debit card accue total
    }

	memset(&srAccumRec, 0x00, sizeof(ACCUM_REC));
    if((inResult = inCTOS_ReadAccumTotal(&srAccumRec)) == ST_ERROR)
    {
        vdMyEZLib_LogPrintf("[vdUpdateAmountTotal]---Read Total Rec. error");
		vdSetErrorMessage("Read Accum Error");
        return ST_ERROR;    
    }        

    vdDebug_LogPrintf("byTransType[%d].byOrgTransType[%d].szOriginTipTrType[%d]IITid[%d]", srTransRec.byTransType, srTransRec.byOrgTransType, szOriginTipTrType, srTransRec.IITid);
    
	vdDebug_LogPrintf("szTotalAmount=[%s],szTipAmount=[%s],szOrgAmount=[%s],.inCardType[%d]",szTransAmt,szTipAmt,szOrgAmt,srTransRec.inCardType);
    vdDebug_LogPrintf("CardTotal SaleCount[%d] [%12.0f]", srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usSaleCount, srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount);
    vdDebug_LogPrintf("CardTotal OfflCount[%d] [%12.0f]", srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usOffSaleCount, srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulOffSaleTotalAmount);
    vdDebug_LogPrintf("CardTotal RefdCount[%d] [%12.0f]", srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usRefundCount, srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulRefundTotalAmount);            
    vdDebug_LogPrintf("CardTotal VoidCount[%d] [%12.0f]", srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usVoidSaleCount, srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulVoidSaleTotalAmount);    
    vdDebug_LogPrintf("CardTotal TipCount [%d] [%12.0f]", srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usTipCount, srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulTipTotalAmount);

    vdDebug_LogPrintf("HostTotal SaleCount[%d] [%12.0f]", srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usSaleCount, srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount);
    vdDebug_LogPrintf("HostTotal OfflCount[%d] [%12.0f]", srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usOffSaleCount, srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulOffSaleTotalAmount);
    vdDebug_LogPrintf("HostTotal RefdCount[%d] [%12.0f]", srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usRefundCount, srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulRefundTotalAmount);            
    vdDebug_LogPrintf("HostTotal VoidCount[%d] [%12.0f]", srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usVoidSaleCount, srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulVoidSaleTotalAmount);    
    vdDebug_LogPrintf("HostTotal TipCount [%d] [%12.0f]", srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usTipCount, srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulTipTotalAmount);
    
    switch(srTransRec.byTransType)
    {
        case SALE:
		case PRE_COMP:
	 case IPP:		
            srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usSaleCount++;
            srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount + atof(szTransAmt);
            
            srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usSaleCount++;
            srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount + atof(szTransAmt);    
		if(atof(szTipAmt) > 0)
			{
			srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usTipCount++;
			srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulTipTotalAmount += atof(szTipAmt);	
			srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usTipCount++;
			srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulTipTotalAmount += atof(szTipAmt);
			}
//            if((srTransRec.byTransType == SALE) && (srTransRec.byEntryMode == CARD_ENTRY_ICC))
			if (((srTransRec.byEntryMode == CARD_ENTRY_ICC) ||
				/* EMV: Revised EMV details printing - start -- jzg */
				(srTransRec.bWaveSID == d_VW_SID_JCB_WAVE_QVSDC) ||
				(srTransRec.bWaveSID == d_VW_SID_AE_EMV) ||
				(srTransRec.bWaveSID == d_VW_SID_PAYPASS_MCHIP) ||
				(srTransRec.bWaveSID == d_VW_SID_AE_EMV) ||
				(srTransRec.bWaveSID == d_VW_SID_VISA_WAVE_QVSDC)) && (srTransRec.byTransType == SALE))
				/* EMV: Revised EMV details printing - end -- jzg */ // patrick fix contactless ********
                srAccumRec.stBankTotal[inTranCardType].usEMVTCCount++;
			// patrick adds store IPP record ******** start 
			
			int inNumOfRecords = 0;
			
			inIPPReadNumofRecords(&inNumOfRecords);
			vdDebug_LogPrintf("inIPPReadNumofRecords inNumOfRecords[%d]", inNumOfRecords);
			
			if(inNumOfRecords >= 1)
			{
				int inIPPSeek = 0;
				
				for (int inLoop = 0; inLoop < inNumOfRecords; inLoop++)
				{

					char *s1;
					char *s2;
					s2=(void*)&strIPP[inLoop].szTenureList; //this is here to avoid warning of assignment from incompatible pointer type 
					do {
						while( *s2 == ' ' || *s2 == '\t' )	s2++;
						s1 = strsep( &s2, "|" );
						if( !*s1 ){
							vdDebug_LogPrintf("val: (empty)" );
						}
						else{
							int val;
							char ch;
							int ret = sscanf( s1, " %i %c", &val, &ch );
							if( ret != 1 ){
								vdDebug_LogPrintf("val: (syntax error)" );
							}
							else{
								
								// sprintf((char *) srAccumRec.stBankTotal[0].stIPPTotal[inIPPSeek].szIPPPlanCode, "%s", (char*)strIPP[inLoop].szPlanCode);
								strcpy((char *) srAccumRec.stBankTotal[0].stIPPTotal[inIPPSeek].szIPPPlanCode, (char*)strIPP[inLoop].szPlanCode);
								sprintf((char *) srAccumRec.stBankTotal[0].stIPPTotal[inIPPSeek].szIPPTenure, "%02d", val);

								vdDebug_LogPrintf("val: %i %d szIPPPlanCode[%s] szIPPTenure[%s]", val , inIPPSeek, (char *) srAccumRec.stBankTotal[0].stIPPTotal[inIPPSeek].szIPPPlanCode, (char *) srAccumRec.stBankTotal[0].stIPPTotal[inIPPSeek].szIPPTenure);									
								vdDebug_LogPrintf("IPPTenure[%d] IPPPlanID[%d] szPlanCode[%d]  ulIPPSaleTotalAmount[%12.0f]", srTransRec.IPPTenure, srTransRec.IPPPlanID, atoi((char*)strIPP[inLoop].szPlanCode) , srAccumRec.stBankTotal[0].stIPPTotal[inIPPSeek].ulIPPSaleTotalAmount);

								// vdDebug_LogPrintf("IPPTenure[%d] val[%d] IPPPlanID[%d]  szPlanCode[%d]", srTransRec.IPPTenure, val, srTransRec.IPPPlanID, atoi((char*)strIPP[inLoop].szPlanCode));

								if ((srTransRec.IPPTenure == val) && (srTransRec.IPPPlanID == atoi((char*)strIPP[inLoop].szPlanCode)))
								{									
									srAccumRec.stBankTotal[0].stIPPTotal[inIPPSeek].usIPPSaleCount++;
									srAccumRec.stBankTotal[0].stIPPTotal[inIPPSeek].ulIPPSaleTotalAmount = srAccumRec.stBankTotal[0].stIPPTotal[inIPPSeek].ulIPPSaleTotalAmount + atof(szTransAmt);

									vdDebug_LogPrintf("HIT usIPPSaleCount[%d] ulIPPSaleTotalAmount[%12.0f]", srAccumRec.stBankTotal[0].stIPPTotal[inIPPSeek].usIPPSaleCount, srAccumRec.stBankTotal[0].stIPPTotal[inIPPSeek].ulIPPSaleTotalAmount);
									inLoop = inNumOfRecords;
									break;
								}
								else
								{
									// srAccumRec.stBankTotal[0].stIPPTotal[inIPPSeek].usIPPSaleCount = srAccumRec.stBankTotal[0].stIPPTotal[inIPPSeek].usIPPSaleCount + 0;
									// srAccumRec.stBankTotal[0].stIPPTotal[inIPPSeek].ulIPPSaleTotalAmount = srAccumRec.stBankTotal[0].stIPPTotal[inIPPSeek].ulIPPSaleTotalAmount + 0;

								}
								
								vdDebug_LogPrintf("usIPPSaleCount[%d] ulIPPSaleTotalAmount[%12.0f]", srAccumRec.stBankTotal[0].stIPPTotal[inIPPSeek].usIPPSaleCount, srAccumRec.stBankTotal[0].stIPPTotal[inIPPSeek].ulIPPSaleTotalAmount);
								
								inIPPSeek++;
							}
						}
					} while (s2!=0 );
				}
			}

			// patrick adds store IPP record ******** end

			break;            
        case SALE_OFFLINE:
            
            srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usOffSaleCount++;
            srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulOffSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulOffSaleTotalAmount + atof(szTransAmt);
            
            srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usOffSaleCount++;
            srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulOffSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulOffSaleTotalAmount + atof(szTransAmt);

            srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usSaleCount++;
            srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount + atof(szTransAmt);
            
            srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usSaleCount++;
            srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount + atof(szTransAmt);
            //sale with tip, settle receipt didn't print tip amt and count
            if(atof(szTipAmt) > 0)
            {
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usTipCount++;
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulTipTotalAmount += atof(szTipAmt);
                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usTipCount++;
                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulTipTotalAmount += atof(szTipAmt);
            }
            break;
        case REFUND:
            srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usRefundCount++;
            srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulRefundTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulRefundTotalAmount + atof(szTransAmt);
            
            srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usRefundCount++;
            srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulRefundTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulRefundTotalAmount + atof(szTransAmt);
            
            break;
        case VOID:
	//	case VOID_PREAUTH:
            vdDebug_LogPrintf("srTransRec.byOrgTransType=%d",srTransRec.byOrgTransType);
		if(srTransRec.byOrgTransType == REFUND)
			{
		            srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usVoidRefundCount++;
		            srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulVoidRefundTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulVoidRefundTotalAmount + atof(szTransAmt);
		            
		            srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usVoidRefundCount++;
		            srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulVoidRefundTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulVoidRefundTotalAmount + atof(szTransAmt);

			}
		else
			{
		            srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usVoidSaleCount++;
		            srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulVoidSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulVoidSaleTotalAmount + atof(szTransAmt);
		            
		            srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usVoidSaleCount++;
		            srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulVoidSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulVoidSaleTotalAmount + atof(szTransAmt);
			}
            if(srTransRec.byOrgTransType == SALE || srTransRec.byOrgTransType == IPP || srTransRec.byOrgTransType == PRE_COMP)
            {
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usSaleCount--;
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount - atof(szTransAmt);
            
                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usSaleCount--;
                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount - atof(szTransAmt);

				if(atof(szTipAmt) > 0)
				{
					srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usTipCount--;
					srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulTipTotalAmount -= atof(szTipAmt);	
					srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usTipCount--;
					srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulTipTotalAmount -= atof(szTipAmt);
				}
            }
            else if(srTransRec.byOrgTransType == SALE_OFFLINE)
            {
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usSaleCount--;
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount - atof(szTransAmt);
                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usSaleCount--;
                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount - atof(szTransAmt);
                
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usOffSaleCount--;
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulOffSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulOffSaleTotalAmount - atof(szTransAmt);            
                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usOffSaleCount--;
                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulOffSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulOffSaleTotalAmount - atof(szTransAmt);

				//Offline Txn that is not uploaded and Voided, not need to include count and amount in bit63 in settlement
				if (srTransRec.byUploaded == CN_FALSE)
				{
					srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usVoidOffSaleCount++;
		            srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulVoidOffSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulVoidOffSaleTotalAmount + atof(szTransAmt);
		            
		            srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usVoidOffSaleCount++;
		            srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulVoidOffSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulVoidOffSaleTotalAmount + atof(szTransAmt);
				}

            }
            else if(srTransRec.byOrgTransType == SALE_TIP)
            {
                if (szOriginTipTrType == SALE_OFFLINE)
                {                    
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usSaleCount--;
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount - atof(szOrgAmt);
                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usSaleCount--;
                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount - atof(szOrgAmt);
                
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usOffSaleCount--;
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulOffSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulOffSaleTotalAmount - atof(szTransAmt);            
                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usOffSaleCount--;
                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulOffSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulOffSaleTotalAmount - atof(szTransAmt);

                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usTipCount--;
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulTipTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulTipTotalAmount - atof(szTipAmt);            
                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usTipCount--;
                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulTipTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulTipTotalAmount - atof(szTipAmt);
                }
                else if(szOriginTipTrType == SALE || szOriginTipTrType == PRE_COMP) 
                {
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usTipCount--;
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulTipTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulTipTotalAmount - atof(szTipAmt);            
                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usTipCount--;
                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulTipTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulTipTotalAmount - atof(szTipAmt);
                
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usSaleCount--;
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount - atof(szTransAmt);            
                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usSaleCount--;
                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount - atof(szTransAmt);
                }

                //Tip adj Txn that is not uploaded and Voided, not need to include count and amount in bit63 in settlement
                if (srTransRec.byUploaded == CN_FALSE)
                {
                    srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usVoidTipCount++;
                    srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulVoidTipTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulVoidTipTotalAmount + atof(szTipAmt);
                    vdDebug_LogPrintf(".not byUploaded");
                    srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usVoidTipCount++;
                    srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulVoidTipTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulVoidTipTotalAmount + atof(szTipAmt);
                }
            }
            else if(srTransRec.byOrgTransType == REFUND)
            {
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usRefundCount--;
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulRefundTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulRefundTotalAmount - atof(szTransAmt);
            
                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usRefundCount--;
                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulRefundTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulRefundTotalAmount - atof(szTransAmt);
            }
            
            break;
        case SALE_TIP:
            if(srTransRec.byOrgTransType == SALE_OFFLINE)
            {
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulOffSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulOffSaleTotalAmount + atof(szTipAmt);  
                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulOffSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulOffSaleTotalAmount + atof(szTipAmt) ;
                if(!(atof(szOrgTIPAmt)>0))
                    srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usTipCount++;
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulTipTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulTipTotalAmount + atof(szTipAmt);

                //update sale total as well
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount + atof(szTransAmt) - atof(szOrgAmt);
                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount + atof(szTransAmt) - atof(szOrgAmt);
                if(!(atof(szOrgTIPAmt)>0))
                    srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usTipCount++;
                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulTipTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulTipTotalAmount + atof(szTipAmt)  - atof(szOrgTIPAmt);

            }
            else 
            {
            	if(!(atof(szOrgTIPAmt)>0))
                	srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usTipCount++;
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulTipTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulTipTotalAmount + atof(szTipAmt)  - atof(szOrgTIPAmt);

		srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount + atof(szTransAmt) - atof(szOrgAmt);
		srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount + atof(szTransAmt) - atof(szOrgAmt); 		   
		if(!(atof(szOrgTIPAmt)>0))
	                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usTipCount++;
                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulTipTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulTipTotalAmount + atof(szTipAmt)  - atof(szOrgTIPAmt);
            }
            break;
        case SALE_ADJUST:            
            srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount - atof(szOrgAmt);
            srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount - atof(szOrgAmt);
            
            srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount + atof(szTransAmt);
            srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount + atof(szTransAmt);
            break;
        default:
            break;
        
    }

    
    vdDebug_LogPrintf("szTotalAmount=[%s],szTipAmount=[%s],szOrgAmount=[%s],.inCardType[%d]",szTransAmt,szTipAmt,szOrgAmt,srTransRec.inCardType);
    vdDebug_LogPrintf("CardTotal SaleCount[%d] [%12.0f]", srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usSaleCount, srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount);
    vdDebug_LogPrintf("CardTotal OfflCount[%d] [%12.0f]", srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usOffSaleCount, srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulOffSaleTotalAmount);
    vdDebug_LogPrintf("CardTotal RefdCount[%d] [%12.0f]", srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usRefundCount, srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulRefundTotalAmount);            
    vdDebug_LogPrintf("CardTotal VoidCount[%d] [%12.0f]", srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usVoidSaleCount, srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulVoidSaleTotalAmount);    
    vdDebug_LogPrintf("CardTotal TipCount [%d] [%12.0f]", srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usTipCount, srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulTipTotalAmount);

    vdDebug_LogPrintf("HostTotal SaleCount[%d] [%12.0f]", srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usSaleCount, srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount);
    vdDebug_LogPrintf("HostTotal OfflCount[%d] [%12.0f]", srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usOffSaleCount, srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulOffSaleTotalAmount);
    vdDebug_LogPrintf("HostTotal RefdCount[%d] [%12.0f]", srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usRefundCount, srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulRefundTotalAmount);            
    vdDebug_LogPrintf("HostTotal VoidCount[%d] [%12.0f]", srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usVoidSaleCount, srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulVoidSaleTotalAmount);    
    vdDebug_LogPrintf("HostTotal TipCount [%d] [%12.0f]", srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usTipCount, srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulTipTotalAmount);
    
    if((inResult = inCTOS_SaveAccumTotal(&srAccumRec)) == ST_ERROR)
    {
        vdMyEZLib_LogPrintf("[vdUpdateAmountTotal]---Save Total Rec. error");
		vdSetErrorMessage("Save Accum Error");
        return ST_ERROR;    
    }

    if(srTransRec.stDCCinfo.bySelectedCurrency == 2)
    {
        inResult = inCTOS_UpdateDccAccumTotal();
        if(inResult != ST_SUCCESS)
        {
            vdSetErrorMessage("Save Dcc Accum Error");
            return ST_ERROR;
        }
    }
   	inCTLOS_Updatepowrfail(PFR_BATCH_UPDATE_COMPLETE);

    vdMyEZLib_LogPrintf("total file saved successfully");

    return ST_SUCCESS;
}

//This is used to save local currency accum for dcc host
int inCTOS_UpdateDccAccumTotal(void)
{
    ACCUM_REC srAccumRec;
    BYTE        szTransAmt[12+1];
    BYTE        szTipAmt[12+1];
    BYTE        szOrgAmt[12+1];
    BYTE        szOrgTIPAmt[12+1];
    int         inResult;
    int inTranCardType = 0;

    vdDebug_LogPrintf("inCTOS_UpdateDccAccumTotal");

    memset(szTransAmt, 0x00, sizeof(szTransAmt));
    memset(szTipAmt, 0x00, sizeof(szTipAmt));
    memset(szOrgAmt, 0x00, sizeof(szOrgAmt));
    memset(szOrgTIPAmt, 0x00, sizeof(szOrgTIPAmt));
//    wub_hex_2_str(srTransRec.szTotalAmount, szTransAmt, 6);
    memcpy(szTransAmt, srTransRec.stDCCinfo.szLocalAmt, 12);
    wub_hex_2_str(srTransRec.szTipAmount, szTipAmt, 6);
    wub_hex_2_str(srTransRec.szOrgAmount, szOrgAmt, 6);
    wub_hex_2_str(srTransRec.szOrgTIPAmount, szOrgTIPAmt, 6);
    vdDebug_LogPrintf("szTransAmt[%s],szTipAmt[%s]", szTransAmt, szTipAmt);
    if (srTransRec.inCardType == CREDIT_CARD)
    {
        inTranCardType = 0;//save credit card accue total
    }
    else
    {
        inTranCardType = 1;//save debit card accue total
    }

    memset(&srAccumRec, 0x00, sizeof(ACCUM_REC));
    if((inResult = inCTOS_ReadDccAccumTotal(&srAccumRec)) == ST_ERROR)
    {
        vdMyEZLib_LogPrintf("[inCTOS_ReadDccAccumTotal]---Read Total Rec. error");
        vdSetErrorMessage("Read Accum Error");
        return ST_ERROR;
    }

    vdDebug_LogPrintf("byTransType[%d].byOrgTransType[%d].szOriginTipTrType[%d]IITid[%d]", srTransRec.byTransType, srTransRec.byOrgTransType, szOriginTipTrType, srTransRec.IITid);

    vdDebug_LogPrintf("szTotalAmount=[%s],szTipAmount=[%s],szOrgAmount=[%s],.inCardType[%d]",szTransAmt,szTipAmt,szOrgAmt,srTransRec.inCardType);
    vdDebug_LogPrintf("CardTotal SaleCount[%d] [%12.0f]", srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usSaleCount, srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount);
    vdDebug_LogPrintf("CardTotal OfflCount[%d] [%12.0f]", srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usOffSaleCount, srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulOffSaleTotalAmount);
    vdDebug_LogPrintf("CardTotal RefdCount[%d] [%12.0f]", srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usRefundCount, srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulRefundTotalAmount);
    vdDebug_LogPrintf("CardTotal VoidCount[%d] [%12.0f]", srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usVoidSaleCount, srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulVoidSaleTotalAmount);
    vdDebug_LogPrintf("CardTotal TipCount [%d] [%12.0f]", srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usTipCount, srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulTipTotalAmount);

    vdDebug_LogPrintf("HostTotal SaleCount[%d] [%12.0f]", srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usSaleCount, srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount);
    vdDebug_LogPrintf("HostTotal OfflCount[%d] [%12.0f]", srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usOffSaleCount, srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulOffSaleTotalAmount);
    vdDebug_LogPrintf("HostTotal RefdCount[%d] [%12.0f]", srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usRefundCount, srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulRefundTotalAmount);
    vdDebug_LogPrintf("HostTotal VoidCount[%d] [%12.0f]", srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usVoidSaleCount, srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulVoidSaleTotalAmount);
    vdDebug_LogPrintf("HostTotal TipCount [%d] [%12.0f]", srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usTipCount, srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulTipTotalAmount);

    switch(srTransRec.byTransType)
    {
        case SALE:
        case PRE_COMP:
        case IPP:
            srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usSaleCount++;
            srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount + atof(szTransAmt);

            srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usSaleCount++;
            srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount + atof(szTransAmt);
            if(atof(szTipAmt) > 0)
            {
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usTipCount++;
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulTipTotalAmount += atof(szTipAmt);
                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usTipCount++;
                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulTipTotalAmount += atof(szTipAmt);
            }
//            if((srTransRec.byTransType == SALE) && (srTransRec.byEntryMode == CARD_ENTRY_ICC))
            if (((srTransRec.byEntryMode == CARD_ENTRY_ICC) ||
                 /* EMV: Revised EMV details printing - start -- jzg */
                 (srTransRec.bWaveSID == d_VW_SID_JCB_WAVE_QVSDC) ||
                 (srTransRec.bWaveSID == d_VW_SID_AE_EMV) ||
                 (srTransRec.bWaveSID == d_VW_SID_PAYPASS_MCHIP) ||
                 (srTransRec.bWaveSID == d_VW_SID_AE_EMV) ||
                 (srTransRec.bWaveSID == d_VW_SID_VISA_WAVE_QVSDC)) && (srTransRec.byTransType == SALE))
                /* EMV: Revised EMV details printing - end -- jzg */ // patrick fix contactless ********
                srAccumRec.stBankTotal[inTranCardType].usEMVTCCount++;
            break;
        case SALE_OFFLINE:

            srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usOffSaleCount++;
            srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulOffSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulOffSaleTotalAmount + atof(szTransAmt);

            srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usOffSaleCount++;
            srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulOffSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulOffSaleTotalAmount + atof(szTransAmt);

            srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usSaleCount++;
            srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount + atof(szTransAmt);

            srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usSaleCount++;
            srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount + atof(szTransAmt);
            //sale with tip, settle receipt didn't print tip amt and count
            if(atof(szTipAmt) > 0)
            {
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usTipCount++;
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulTipTotalAmount += atof(szTipAmt);
                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usTipCount++;
                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulTipTotalAmount += atof(szTipAmt);
            }
            break;
        case REFUND:
            srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usRefundCount++;
            srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulRefundTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulRefundTotalAmount + atof(szTransAmt);

            srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usRefundCount++;
            srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulRefundTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulRefundTotalAmount + atof(szTransAmt);

            break;
        case VOID:
            //	case VOID_PREAUTH:
            if(srTransRec.byOrgTransType == REFUND)
            {
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usVoidRefundCount++;
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulVoidRefundTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulVoidRefundTotalAmount + atof(szTransAmt);

                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usVoidRefundCount++;
                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulVoidRefundTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulVoidRefundTotalAmount + atof(szTransAmt);

            }
            else
            {
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usVoidSaleCount++;
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulVoidSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulVoidSaleTotalAmount + atof(szTransAmt);

                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usVoidSaleCount++;
                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulVoidSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulVoidSaleTotalAmount + atof(szTransAmt);
            }
            if(srTransRec.byOrgTransType == SALE || srTransRec.byOrgTransType == IPP || srTransRec.byOrgTransType == PRE_COMP)
            {
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usSaleCount--;
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount - atof(szTransAmt);

                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usSaleCount--;
                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount - atof(szTransAmt);

                if(atof(szTipAmt) > 0)
                {
                    srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usTipCount--;
                    srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulTipTotalAmount -= atof(szTipAmt);
                    srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usTipCount--;
                    srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulTipTotalAmount -= atof(szTipAmt);
                }
            }
            else if(srTransRec.byOrgTransType == SALE_OFFLINE)
            {
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usSaleCount--;
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount - atof(szTransAmt);
                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usSaleCount--;
                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount - atof(szTransAmt);

                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usOffSaleCount--;
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulOffSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulOffSaleTotalAmount - atof(szTransAmt);
                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usOffSaleCount--;
                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulOffSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulOffSaleTotalAmount - atof(szTransAmt);

            }
            else if(srTransRec.byOrgTransType == SALE_TIP)
            {
                if (szOriginTipTrType == SALE_OFFLINE)
                {
                    srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usSaleCount--;
                    srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount - atof(szOrgAmt);
                    srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usSaleCount--;
                    srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount - atof(szOrgAmt);

                    srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usOffSaleCount--;
                    srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulOffSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulOffSaleTotalAmount - atof(szTransAmt);
                    srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usOffSaleCount--;
                    srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulOffSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulOffSaleTotalAmount - atof(szTransAmt);

                    srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usTipCount--;
                    srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulTipTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulTipTotalAmount - atof(szTipAmt);
                    srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usTipCount--;
                    srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulTipTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulTipTotalAmount - atof(szTipAmt);
                }
                else if(szOriginTipTrType == SALE || szOriginTipTrType == PRE_COMP)
                {
                    srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usTipCount--;
                    srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulTipTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulTipTotalAmount - atof(szTipAmt);
                    srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usTipCount--;
                    srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulTipTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulTipTotalAmount - atof(szTipAmt);

                    srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usSaleCount--;
                    srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount - atof(szTransAmt);
                    srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usSaleCount--;
                    srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount - atof(szTransAmt);
                }
            }
            else if(srTransRec.byOrgTransType == REFUND)
            {
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usRefundCount--;
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulRefundTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulRefundTotalAmount - atof(szTransAmt);

                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usRefundCount--;
                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulRefundTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulRefundTotalAmount - atof(szTransAmt);
            }

            break;
        case SALE_TIP:
            if(srTransRec.byOrgTransType == SALE_OFFLINE)
            {
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulOffSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulOffSaleTotalAmount + atof(szTipAmt);
                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulOffSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulOffSaleTotalAmount + atof(szTipAmt) ;
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usTipCount++;
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulTipTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulTipTotalAmount + atof(szTipAmt);

                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usTipCount++;
                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulTipTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulTipTotalAmount + atof(szTipAmt);
            }
            else
            {
                if(!(atof(szOrgTIPAmt)>0))
                    srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usTipCount++;
                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulTipTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulTipTotalAmount + atof(szTipAmt)  - atof(szOrgTIPAmt);

                srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount + atof(szTransAmt) - atof(szOrgAmt);
                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount + atof(szTransAmt) - atof(szOrgAmt);
                if(!(atof(szOrgTIPAmt)>0))
                    srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usTipCount++;
                srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulTipTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulTipTotalAmount + atof(szTipAmt)  - atof(szOrgTIPAmt);
            }
            break;
        case SALE_ADJUST:
            srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount - atof(szOrgAmt);
            srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount - atof(szOrgAmt);

            srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount + atof(szTransAmt);
            srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount = srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount + atof(szTransAmt);
            break;
        default:
            break;

    }


    vdDebug_LogPrintf("szTotalAmount=[%s],szTipAmount=[%s],szOrgAmount=[%s],.inCardType[%d]",szTransAmt,szTipAmt,szOrgAmt,srTransRec.inCardType);
    vdDebug_LogPrintf("CardTotal SaleCount[%d] [%12.0f]", srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usSaleCount, srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulSaleTotalAmount);
    vdDebug_LogPrintf("CardTotal OfflCount[%d] [%12.0f]", srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usOffSaleCount, srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulOffSaleTotalAmount);
    vdDebug_LogPrintf("CardTotal RefdCount[%d] [%12.0f]", srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usRefundCount, srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulRefundTotalAmount);
    vdDebug_LogPrintf("CardTotal VoidCount[%d] [%12.0f]", srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usVoidSaleCount, srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulVoidSaleTotalAmount);
    vdDebug_LogPrintf("CardTotal TipCount [%d] [%12.0f]", srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].usTipCount, srAccumRec.stBankTotal[inTranCardType].stCardTotal[srTransRec.IITid].ulTipTotalAmount);

    vdDebug_LogPrintf("HostTotal SaleCount[%d] [%12.0f]", srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usSaleCount, srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulSaleTotalAmount);
    vdDebug_LogPrintf("HostTotal OfflCount[%d] [%12.0f]", srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usOffSaleCount, srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulOffSaleTotalAmount);
    vdDebug_LogPrintf("HostTotal RefdCount[%d] [%12.0f]", srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usRefundCount, srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulRefundTotalAmount);
    vdDebug_LogPrintf("HostTotal VoidCount[%d] [%12.0f]", srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usVoidSaleCount, srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulVoidSaleTotalAmount);
    vdDebug_LogPrintf("HostTotal TipCount [%d] [%12.0f]", srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.usTipCount, srAccumRec.stBankTotal[inTranCardType].stHOSTTotal.ulTipTotalAmount);

    if((inResult = inCTOS_SaveDccAccumTotal(&srAccumRec)) == ST_ERROR)
    {
        vdDebug_LogPrintf("[inCTOS_SaveDccAccumTotal]---Save Total Rec. error");
        vdSetErrorMessage("Save Dcc Accum Error");
        return ST_ERROR;
    }

//    inCTLOS_Updatepowrfail(PFR_BATCH_UPDATE_COMPLETE);

    vdDebug_LogPrintf("total file saved successfully");

    return ST_SUCCESS;
}
int inCTOS_ClearAccumTotal(void)
{
	short shHostIndex;
	int inResult;
	ACCUM_REC srAccumRec;
    STRUCT_FILE_SETTING strFile;
	
	shHostIndex = inCTOS_SelectHostSetting();
	if (shHostIndex == -1)
		return -1;
	strHDT.inHostIndex = shHostIndex;
	DebugAddINT("summary host Index",shHostIndex);

    memset(&srAccumRec, 0x00, sizeof(ACCUM_REC));
	memset(&strFile,0,sizeof(strFile));
	inResult = inCTOS_CheckAndSelectMutipleMID();
    vdCTOS_GetAccumName(&strFile, &srAccumRec);
	
	if((inResult = CTOS_FileDelete(strFile.szFileName)) != d_OK)
    {
        vdMyEZLib_LogPrintf("[inMyFile_SettleRecordDelete]---Delete Record error[%04x]", inResult);
        return ST_ERROR;
    }
    inCTOS_ClearDccAccumTotal();
    return ST_SUCCESS;
	
}

int inCTOS_ClearDccAccumTotal(void)
{
    short shHostIndex;
    int inResult;
    ACCUM_REC srAccumRec;
    STRUCT_FILE_SETTING strFile;

    shHostIndex = inCTOS_SelectHostSetting();
    if (shHostIndex == -1)
        return -1;
    strHDT.inHostIndex = shHostIndex;
    DebugAddINT("summary host Index",shHostIndex);

    memset(&srAccumRec, 0x00, sizeof(ACCUM_REC));
    memset(&strFile,0,sizeof(strFile));
    inResult = inCTOS_CheckAndSelectMutipleMID();
    vdCTOS_GetDccAccumName(&strFile, &srAccumRec);

    if((inResult = CTOS_FileDelete(strFile.szFileName)) != d_OK)
    {
        vdMyEZLib_LogPrintf("[inMyFile_SettleRecordDelete]---Delete Record error[%04x]", inResult);
        return ST_ERROR;
    }

    return ST_SUCCESS;

}

int inCTOS_ClearMultiCurrencyAccumTotal(void)
{
    int inResult;
    ACCUM_REC srAccumRec;
    STRUCT_FILE_SETTING strFile;

    memset(&srAccumRec, 0x00, sizeof(ACCUM_REC));
    memset(&strFile,0,sizeof(strFile));

    sprintf(strFile.szFileName, MULTIPLE_CURRENCY_ACCUM);

    strFile.bSeekType           = d_SEEK_FROM_BEGINNING;
    strFile.bStorageType        = d_STORAGE_FLASH ;
    strFile.fCloseFileNow       = TRUE;
    strFile.ulRecSize           = sizeof(ACCUM_REC);
    strFile.ptrRec              = &srAccumRec;

    if((inResult = CTOS_FileDelete(strFile.szFileName)) != d_OK)
    {
        vdMyEZLib_LogPrintf("[MultiCurrency]---Delete Record error[%04x]", inResult);
        return ST_ERROR;
    }

    return ST_SUCCESS;
}

void vdCTOS_SetBackupAccumFile(char *szOriFileName)
{
    ACCUM_REC srAccumRec;
    STRUCT_FILE_SETTING strFile;
	char szBKAccumeFileName[30];
	ULONG ulFileSize;
	int inResult;
	
	memset(&srAccumRec,0x00,sizeof(srAccumRec));
	memset(&strFile,0x00,sizeof(strFile));
	memset(szBKAccumeFileName,0x00,sizeof(szBKAccumeFileName));
	strcpy(szBKAccumeFileName,szOriFileName);
	CTOS_FileGetSize(szOriFileName, &ulFileSize);
	strcat(szBKAccumeFileName,".BK");
		
	vdDebug_LogPrintf("delete old BK acculFileSize[%d],del[%d]szOriFileName[%s][%s]",ulFileSize,inResult,szOriFileName,szBKAccumeFileName);
	inCTOS_FileCopy(szOriFileName,szBKAccumeFileName,ulFileSize);

	vdDebug_LogPrintf("szOriFileName[%s]set BKAccum[%s]ulFileSize[%ld]",szOriFileName,szBKAccumeFileName,ulFileSize);
	CTOS_FileGetSize(szBKAccumeFileName, &ulFileSize);
	vdDebug_LogPrintf("BK Acc ulFileSize[%ld]",ulFileSize);

	
}

void vdCTOS_SetBackupDccAccumFile(char *szOriFileName)
{
    ACCUM_REC srAccumRec;
    STRUCT_FILE_SETTING strFile;
    char szBKAccumeFileName[30];
    ULONG ulFileSize;
    int inResult;

    memset(&srAccumRec,0x00,sizeof(srAccumRec));
    memset(&strFile,0x00,sizeof(strFile));
    memset(szBKAccumeFileName,0x00,sizeof(szBKAccumeFileName));
    strcpy(szBKAccumeFileName,szOriFileName);
    CTOS_FileGetSize(szOriFileName, &ulFileSize);
    strcat(szBKAccumeFileName,".BK");

    vdDebug_LogPrintf("delete old BK acculFileSize[%d],del[%d]szOriFileName[%s][%s]",ulFileSize,inResult,szOriFileName,szBKAccumeFileName);
    inCTOS_FileCopy(szOriFileName,szBKAccumeFileName,ulFileSize);

    vdDebug_LogPrintf("szOriFileName[%s]set BKAccum[%s]ulFileSize[%ld]",szOriFileName,szBKAccumeFileName,ulFileSize);
    CTOS_FileGetSize(szBKAccumeFileName, &ulFileSize);
    vdDebug_LogPrintf("BK Acc ulFileSize[%ld]",ulFileSize);


}

void vdCTOS_GetBackupAccumFile(STRUCT_FILE_SETTING *strFile, ACCUM_REC *strTotal, int HostIndex, int MITid)
{

	sprintf(strFile->szFileName, "ACC%02d%02d.total%s"
								, HostIndex
								, MITid,".BK");
	
	vdDebug_LogPrintf("get BKAccum[%s]",strFile->szFileName);

								
	strFile->bSeekType			 = d_SEEK_FROM_BEGINNING;
	strFile->bStorageType		 = d_STORAGE_FLASH ;
	strFile->fCloseFileNow		 = TRUE;
	strFile->ulRecSize			 = sizeof(ACCUM_REC);
	strFile->ptrRec 			 = strTotal;	
		
}

void vdCTOS_GetBackupDccAccumFile(STRUCT_FILE_SETTING *strFile, ACCUM_REC *strTotal, int HostIndex, int MITid)
{

    sprintf(strFile->szFileName, "DCC%02d%02d.total%s"
            , HostIndex
            , MITid,".BK");

    vdDebug_LogPrintf("get BK dcc Accum[%s]",strFile->szFileName);


    strFile->bSeekType			 = d_SEEK_FROM_BEGINNING;
    strFile->bStorageType		 = d_STORAGE_FLASH ;
    strFile->fCloseFileNow		 = TRUE;
    strFile->ulRecSize			 = sizeof(ACCUM_REC);
    strFile->ptrRec 			 = strTotal;

}

int inCTOS_ReadBKAccumTotal(ACCUM_REC *strTotal,int HostIndex, int MITid)
{
    STRUCT_FILE_SETTING strFile;
    
    memset(&strFile, 0x00, sizeof(STRUCT_FILE_SETTING));
    memset(strTotal, 0x00, sizeof(ACCUM_REC));
    vdCTOS_GetBackupAccumFile(&strFile, strTotal,HostIndex, MITid);

    return (inMyFile_RecRead(&strFile));    
}
int inCTOS_ReadBKDccAccumTotal(ACCUM_REC *strTotal,int HostIndex, int MITid)
{
    STRUCT_FILE_SETTING strFile;

    memset(&strFile, 0x00, sizeof(STRUCT_FILE_SETTING));
    memset(strTotal, 0x00, sizeof(ACCUM_REC));
    vdCTOS_GetBackupDccAccumFile(&strFile, strTotal,HostIndex, MITid);

    return (inMyFile_RecRead(&strFile));
}
int inCTOS_DeleteBKAccumTotal(ACCUM_REC *strTotal,int HostIndex, int MITid)
{
	
    STRUCT_FILE_SETTING strFile;
	int inResult;
    
    memset(&strFile, 0x00, sizeof(STRUCT_FILE_SETTING));
    memset(strTotal, 0x00, sizeof(ACCUM_REC));
    vdCTOS_GetBackupAccumFile(&strFile, strTotal,HostIndex, MITid);
	
	if((inResult = CTOS_FileDelete(strFile.szFileName)) != d_OK)
    {
        vdMyEZLib_LogPrintf("[inMyFile_SettleRecordDelete]---Delete Record error[%04x]", inResult);
        return ST_ERROR;
    }     
	
}

int inCTOS_DeleteBKDccAccumTotal(ACCUM_REC *strTotal,int HostIndex, int MITid)
{

    STRUCT_FILE_SETTING strFile;
    int inResult;

    memset(&strFile, 0x00, sizeof(STRUCT_FILE_SETTING));
    memset(strTotal, 0x00, sizeof(ACCUM_REC));
    vdCTOS_GetBackupDccAccumFile(&strFile, strTotal,HostIndex, MITid);

    if((inResult = CTOS_FileDelete(strFile.szFileName)) != d_OK)
    {
        vdMyEZLib_LogPrintf("[inMyFile_SettleRecordDelete]---Delete Record error[%04x]", inResult);
        return ST_ERROR;
    }

}
int inCTOS_FileCopy(char *szSourceFile, char *szDesFile,ULONG ulFileSize)
{

	FILE *in = NULL;
	FILE *out = NULL;
	unsigned long ulFileHandle;
	unsigned long ulOutFileHandle;
	char ch;
	ULONG ulFileSizeTemp;
	char *pAccumeData;
	int inresult;
	
	CTOS_FileGetSize(szSourceFile, &ulFileSizeTemp);
    inresult = CTOS_FileOpen(szSourceFile, d_STORAGE_FLASH, &ulFileHandle);
	vdDebug_LogPrintf("CTOS_FileOpen[%d]ulFileSizeTemp[%d]",inresult,ulFileSizeTemp);

	inresult = CTOS_FileSeek(ulFileHandle, 0, d_SEEK_FROM_BEGINNING);
	
	vdDebug_LogPrintf("CTOS_FileSeek[%d]ulFileSizeTemp[%d]",inresult,ulFileSizeTemp);
	pAccumeData = (unsigned char *) malloc(ulFileSizeTemp+1);
	vdDebug_LogPrintf("ulFileSizeTemp[%d]",ulFileSizeTemp);
	
	if(pAccumeData == NULL)
	{	
		vdDisplayErrorMsg(1, 8,	"accume bk fail");
		free(pAccumeData);
		return FAIL;
	}
	
	
    inresult = CTOS_FileRead(ulFileHandle, pAccumeData, &ulFileSizeTemp);
	
	vdDebug_LogPrintf("CTOS_FileRead[%d]ulFileSizeTemp[%d]ulFileSize[%d]",inresult,ulFileSizeTemp,ulFileSize);

	if((inresult  = CTOS_FileClose(ulFileHandle)) != d_OK)
	{				 
		vdDebug_LogPrintf("[CTOS_FileClose]---FileClz err[%04x]", inresult);
		free(pAccumeData);

		return ST_ERROR;
	}

	inresult = CTOS_FileDelete(szDesFile);
	vdDebug_LogPrintf("CTOS_FileDelete[%d]",inresult);


	vdDebug_LogPrintf("33333[%d]",ulFileSize);

	CTOS_Delay(50);
	
	
	inresult = CTOS_FileOpen(szDesFile , d_STORAGE_FLASH , &ulOutFileHandle);
	vdDebug_LogPrintf("CTOS_FileOpen[%d]",inresult);
	if(inresult == d_OK)
	{																						
		/* Move the file pointer to a specific position. 
		* Move backward from the end of the file.		 */
		inresult = CTOS_FileSeek(ulOutFileHandle, 0, d_SEEK_FROM_BEGINNING);
		if (inresult != d_OK)
		{
			vdDebug_LogPrintf("[inMyFile_RecSave]---Rec Seek inResult[%04x]", inresult);
			CTOS_FileClose(ulOutFileHandle);
			free(pAccumeData);
			return ST_ERROR;																		
		}
		else
			;
		/* Write data into this opened file */
		inresult = CTOS_FileWrite(ulOutFileHandle ,pAccumeData ,ulFileSize); 										
		if (inresult != d_OK)
		{
			vdDebug_LogPrintf("[inMyFile_RecSave]---Rec Write error, inResult[%04x]", inresult);
			CTOS_FileClose(ulOutFileHandle);
			
			free(pAccumeData);
			return ST_ERROR; 
		}																		
		
		vdDebug_LogPrintf("[inMyFile_RecSave]---Write finish,  inResult[%d]",  inresult);
		
		{	 
			if((inresult  = CTOS_FileClose(ulOutFileHandle)) != d_OK)
			{				 
				vdDebug_LogPrintf("[inMyFile_RecSave]---FileClz err[%04x]", inresult);
				free(pAccumeData);

				return ST_ERROR;
			}
			else
				ulOutFileHandle = 0x00;

			vdDebug_LogPrintf("[inMyFile_RecSave]---User close immed.");
		}

        vdDebug_LogPrintf("[inMyFile_RecSave]---User did not close immed.");
	}

	free(pAccumeData);
	
	vdDebug_LogPrintf(("last--CTOS_FileWrite,inresult[%d]", (char*)&inresult));
} 




