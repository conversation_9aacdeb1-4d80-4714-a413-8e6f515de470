<?xml version="1.0"?>
	
<project name="declinedPaymentApi" basedir="." default="deploy">
	<property file="build.properties"/>
	<defaultexcludes add="**/.*.swp"/>

	<target name="clean" description="clean">
		<delete dir="classes" includeEmptyDirs="true" failonerror="false"/>
		<delete dir="dist" includeEmptyDirs="true" failonerror="false"/>
	</target>
	
    <path id="compile.classpath">
		<fileset dir="${repos.dir}">
			<include name="commons-codec-1.4/*.jar"/>
            <include name="commons/*.jar"/>
			<include name="log4j/*.jar"/>
			<include name="spring-3.1.1/*.jar"/>
            <include name="abl/*.jar"/>
            <include name="gson-2.2.4/*.jar"/>
            <include name="bouncycastle/*.jar"/>
            <include name="httpcomponents-client-4.5.7/*.jar"/>
        </fileset>
    </path>

    <target name="compile" description="compile">
        <mkdir dir="classes"/>
        <javac source="1.7" target="1.7" destdir="classes" debug="true" includeAntRuntime="false"
               deprecation="false" optimize="false" failonerror="true">
            <src path="src"/>
			<classpath refid="compile.classpath"/>
        </javac>
    </target>
	
	<property name="project.jar" value="${project.name}-${project.version}.jar"/>

	<target name="jar" depends="compile" description="jar">
        <mkdir dir="dist"/>
        <tstamp>
            <format property="today" pattern="dd-MMM-yyyy HH:mm:ss" />
        </tstamp>        
        <jar basedir="classes"
             destfile="dist/${project.jar}"
             excludes="*.jar">
            <manifest>
                <attribute name="Built-By" value="${user.name}"/>
                <attribute name="Built-Date" value="${today}"/>
                <attribute name="Implementation-Version" value="${project.version}"/>
            </manifest>
        </jar>
    </target>

    <target name="deploy" depends="jar">
        <copy todir="${repos.dir}/abl">
            <fileset dir="dist">
                <include name="${project.jar}"/>
            </fileset>
        </copy>
    </target>

</project>
