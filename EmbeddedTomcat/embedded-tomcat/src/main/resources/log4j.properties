log4j.rootLogger=WARN, logfile

log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
log4j.appender.stdout.layout.ConversionPattern=%d %p [%c] - <%m>%n

log4j.appender.logfile=org.apache.log4j.DailyRollingFileAppender
log4j.appender.logfile.File=logs/tomcat-root.log
log4j.appender.logfile.DatePattern=-yyyyMMdd'.log'
log4j.appender.logfile.layout=org.apache.log4j.PatternLayout
log4j.appender.logfile.layout.ConversionPattern=%d %-5p %X{id} = %m - %c (%F:%L)%n
log4j.appender.logfile.encoding = UTF-8
log4j.appender.logfile.append = true

log4j.logger.org.hibernate.SQL=ERROR, hibernate
log4j.appender.hibernate=org.apache.log4j.DailyRollingFileAppender
log4j.appender.hibernate.File=logs/app-hibernate.log
log4j.appender.hibernate.DatePattern=-yyyyMMdd'.log'
log4j.appender.hibernate.layout=org.apache.log4j.PatternLayout
log4j.appender.hibernate.layout.ConversionPattern=%d %-5p %X{id} = %m (%F:%L)%n
log4j.appender.hibernate.encoding = UTF-8
log4j.appender.hibernate.append = false
