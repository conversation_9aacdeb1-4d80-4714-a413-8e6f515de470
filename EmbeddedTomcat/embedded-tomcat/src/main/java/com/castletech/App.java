package com.castletech;

import org.apache.catalina.LifecycleException;
import org.apache.catalina.Service;
import org.apache.catalina.connector.Connector;
import org.apache.catalina.startup.Tomcat;
import org.apache.catalina.util.ServerInfo;
import org.apache.coyote.http11.Http11NioProtocol;

import java.io.File;

import static org.apache.catalina.util.ServerInfo.getServerInfo;

public class App {
    public static void main(String[] args) throws Exception {
        if (args.length < 2) {
            System.out.println("Usage: java App port path_to_war");
            System.exit(1);
        }
        final Tomcat tomcat = new Tomcat();

        Thread printingHook = new Thread(new Runnable() {
            @Override
            public void run() {
                System.out.println("Shutting down tomcat.");
                try {
                    tomcat.stop();
                } catch (LifecycleException e) {
                    e.printStackTrace();
                }
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                }
            }
        });

        Runtime.getRuntime().addShutdownHook(printingHook);

        java.util.logging.Logger.getLogger("org.apache").setLevel(java.util.logging.Level.WARNING);

        int port=Integer.parseInt(args[0]);

        Service service = tomcat.getService();
        service.addConnector(getSslConnector(port));

        tomcat.setBaseDir("temp");
        //tomcat.setPort(port);

        String contextPath = "/comfort";
        String warFilePath = args[1];

        tomcat.getHost().setAppBase(".");
        tomcat.addWebapp(contextPath, warFilePath);

        System.out.println("Starting tomcat - listen port: "+args[0]);
        tomcat.start();
        String info=getServerInfo();;
        System.out.println("Started Tomcat - "+ info);
        tomcat.getServer().await();
    }

    private static Connector getSslConnector(int port) {
        //Connector connector = new Connector();
        //connector.setPort(9000);
        //connector.setSecure(true);
        //connector.setScheme("https");
        //connector.setAttribute("keyAlias", "tomcat");
        //connector.setAttribute("keystorePass", "password");
        //connector.setAttribute("keystoreType", "JKS");
        //connector.setAttribute("keystoreFile",
        //        "keystore.jks");
        //connector.setAttribute("clientAuth", "false");
        //connector.setAttribute("protocol", "HTTP/1.1");
        //connector.setAttribute("sslProtocol", "TLS");
        //connector.setAttribute("maxThreads", "200");
        //connector.setAttribute("protocol", "org.apache.coyote.http11.Http11AprProtocol");
        //connector.setAttribute("SSLEnabled", true);

        Connector connector = new Connector(Http11NioProtocol.class.getName());
        Http11NioProtocol protocol = (Http11NioProtocol) connector.getProtocolHandler();
        connector.setPort(port);
        connector.setSecure(true);
        connector.setScheme("https");
        protocol.setSSLEnabled(true);
        protocol.setKeyAlias("tomcat");
        protocol.setKeystorePass("password");
        String dir = System.getProperty("user.dir");

        protocol.setKeystoreFile(dir+ File.separator+"keystore.jks");
        protocol.setSslProtocol("TLS");

        return connector;
    }
}
