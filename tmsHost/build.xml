<?xml version="1.0"?>
	
<project name="tms-host" basedir="." default="deploy">

    <property file="build.properties"/>

	<property name="project.jar" value="${project.name}-${project.version}.jar"/>

    <property name="test.src.dir" value="test"/>

    <path id="master.classpath">
		<fileset dir="${repos.dir}">
			<include name="abl/abl-common*.jar"/>
			<!--<include name="abl/abl-crypto*.jar"/>-->
			<!--<include name="abl/abl-hsm*.jar"/>-->
			<!--<include name="abl/abl-smartcard*.jar"/>-->
			<include name="abl/comfortSG-database*.jar"/>
			<!--<include name="abl/epurse-hsm*.jar"/>-->
			<!--<include name="abl/epurse-smartcard*.jar"/>-->
			<include name="hibernate/*.jar"/>
            <include name="jcs-1.3/*.jar"/>
            <include name="jpos/*.jar"/>
			<include name="log4j/*.jar"/>
			<!--<include name="safenet/*.jar"/>-->
			<include name="spring-3.2.9/*.jar"/>
        </fileset>
    </path>
    
	<target name="clean">
		<delete includeemptydirs="true" failonerror="false">
			<fileset dir="${build.dir}" includes="**/*"/>
		</delete>
		<delete includeemptydirs="true" failonerror="false">
			<fileset dir="${dist.dir}" includes="**/*"/>
		</delete>
	</target>
	
    <target name="compile">
        <mkdir dir="${build.dir}"/>
        <javac destdir="${build.dir}" debug="true" includeAntRuntime="false"
               deprecation="false" optimize="false" failonerror="true">
            <src path="${src.dir}"/>
            <classpath refid="master.classpath"/>
        </javac>
    </target>

    <path id="test.classpath">
        <path refid="master.classpath"/>
        <pathelement location="${build.dir}"/>
        <pathelement location="."/>
        <fileset dir="${repos.dir}">
            <include name="junit/*.jar"/>
        </fileset>
    </path>

    <target name="compile-test" depends="compile">
        <javac destdir="${build.dir}" debug="true" includeAntRuntime="false"
               deprecation="false" optimize="false" failonerror="true">
            <src path="${test.src.dir}"/>
            <classpath refid="test.classpath"/>
        </javac>
    </target>

    <target name="build" depends="compile">
        <mkdir dir="${dist.dir}"/>
        <tstamp>
            <format property="today" pattern="dd-MMM-yyyy HH:mm:ss" />
        </tstamp>        
        <jar basedir="${build.dir}"
             destfile="${dist.dir}/${project.jar}"
             excludes="*.jar">
            <manifest>
                <attribute name="Built-By" value="${user.name}"/>
                <attribute name="Built-Date" value="${today}"/>
                <attribute name="Implementation-Version" value="${project.version}"/>
            </manifest>
        </jar>
    </target>

    <target name="deploy" depends="build">
		<copy todir="${deploy.dir}/lib" flatten="true" includeEmptyDirs="false">
			<fileset dir="${repos.dir}">
				<include name="abl/abl-common*.jar"/>
				<!--<include name="abl/abl-crypto*.jar"/>-->
				<!--<include name="abl/abl-hsm*.jar"/>-->
				<!--<include name="abl/abl-smartcard*.jar"/>-->
				<include name="abl/comfortSG-database*.jar"/>
				<!--<include name="abl/epurse-hsm*.jar"/>-->
				<!--<include name="abl/epurse-smartcard*.jar"/>-->
				<include name="aopalliance-1.0/*.jar"/>
				<!--<include name="bouncycastle/*.jar"/>-->
				<include name="commons/*.jar"/>
				<include name="hibernate/*.jar"/>
                <include name="jcs-1.3/*.jar"/>
                <include name="jpos/*.jar"/>
				<include name="log4j/*.jar"/>
				<include name="jtds/*.jar"/>
				<!--<include name="safenet/*.jar"/>-->
				<include name="sl4j-1.6.0/*.jar"/>
				<include name="spring-3.2.9/*.jar"/>
				<include name="spring-security-3.0.7/*.jar"/>
        	</fileset>
            <fileset dir="${dist.dir}">
                <include name="${project.jar}"/>
            </fileset>
    	</copy>
    	<copy todir="${deploy.dir}/deploy">
    	    <fileset dir="deploy"/>
    	</copy>
    	<copy todir="${deploy.dir}/conf">
    	    <fileset dir="${conf.dir}"/>
    	    <fileset dir="conf"/>
    	</copy>
    	<copy todir="${deploy.dir}">
    	    <fileset dir="bin"/>
    	</copy>
    	<mkdir dir="${deploy.dir}/logs"/>
    	<mkdir dir="${deploy.dir}/space"/>

    </target>
    
</project>
