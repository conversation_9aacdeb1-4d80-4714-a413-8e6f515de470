# DEFAULT CACHE REGION
jcs.default=DC
jcs.default.cacheattributes=org.apache.jcs.engine.CompositeCacheAttributes
jcs.default.cacheattributes.MaxObjects=1000
jcs.default.cacheattributes.MemoryCacheName=org.apache.jcs.engine.memory.lru.LRUMemoryCache
jcs.default.cacheattributes.UseMemoryShrinker=false
jcs.default.cacheattributes.MaxMemoryIdleTimeSeconds=3600
jcs.default.cacheattributes.ShrinkerIntervalSeconds=60
jcs.default.elementattributes=org.apache.jcs.engine.ElementAttributes
jcs.default.elementattributes.IsEternal=false
jcs.default.elementattributes.MaxLifeSeconds=21600
jcs.default.elementattributes.IdleTime=1800
jcs.default.elementattributes.IsSpool=false
jcs.default.elementattributes.IsRemote=false
jcs.default.elementattributes.IsLateral=false

# PRE-DEFINED CACHE REGIONS
jcs.region.releaseFileCache=DC
jcs.region.releaseFileCache.cacheattributes=org.apache.jcs.engine.CompositeCacheAttributes
jcs.region.releaseFileCache.cacheattributes.MaxObjects=10000
jcs.region.releaseFileCache.cacheattributes.MemoryCacheName=org.apache.jcs.engine.memory.lru.LRUMemoryCache
jcs.region.releaseFileCache.cacheattributes.UseMemoryShrinker=false
jcs.region.releaseFileCache.cacheattributes.MaxMemoryIdleTimeSeconds=3600
jcs.region.releaseFileCache.cacheattributes.ShrinkerIntervalSeconds=60
jcs.region.releaseFileCache.cacheattributes.MaxSpoolPerRun=500
jcs.region.releaseFileCache.elementattributes=org.apache.jcs.engine.ElementAttributes
jcs.region.releaseFileCache.elementattributes.IsEternal=true

jcs.region.releaseFileNextRecordCache=DC
jcs.region.releaseFileNextRecordCache.cacheattributes=org.apache.jcs.engine.CompositeCacheAttributes
jcs.region.releaseFileNextRecordCache.cacheattributes.MaxObjects=10000
jcs.region.releaseFileNextRecordCache.cacheattributes.MemoryCacheName=org.apache.jcs.engine.memory.lru.LRUMemoryCache
jcs.region.releaseFileNextRecordCache.cacheattributes.UseMemoryShrinker=false
jcs.region.releaseFileNextRecordCache.cacheattributes.MaxMemoryIdleTimeSeconds=3600
jcs.region.releaseFileNextRecordCache.cacheattributes.ShrinkerIntervalSeconds=60
jcs.region.releaseFileNextRecordCache.cacheattributes.MaxSpoolPerRun=500
jcs.region.releaseFileNextRecordCache.elementattributes=org.apache.jcs.engine.ElementAttributes
jcs.region.releaseFileNextRecordCache.elementattributes.IsEternal=true

jcs.region.blacklistFileCache=DC
jcs.region.blacklistFileCache.cacheattributes=org.apache.jcs.engine.CompositeCacheAttributes
jcs.region.blacklistFileCache.cacheattributes.MaxObjects=10000
jcs.region.blacklistFileCache.cacheattributes.MemoryCacheName=org.apache.jcs.engine.memory.lru.LRUMemoryCache
jcs.region.blacklistFileCache.cacheattributes.UseMemoryShrinker=false
jcs.region.blacklistFileCache.cacheattributes.MaxMemoryIdleTimeSeconds=3600
jcs.region.blacklistFileCache.cacheattributes.ShrinkerIntervalSeconds=60
jcs.region.blacklistFileCache.cacheattributes.MaxSpoolPerRun=500
jcs.region.blacklistFileCache.elementattributes=org.apache.jcs.engine.ElementAttributes
jcs.region.blacklistFileCache.elementattributes.IsEternal=true

jcs.region.blacklistFileNextRecordCache=DC
jcs.region.blacklistFileNextRecordCache.cacheattributes=org.apache.jcs.engine.CompositeCacheAttributes
jcs.region.blacklistFileNextRecordCache.cacheattributes.MaxObjects=10000
jcs.region.blacklistFileNextRecordCache.cacheattributes.MemoryCacheName=org.apache.jcs.engine.memory.lru.LRUMemoryCache
jcs.region.blacklistFileNextRecordCache.cacheattributes.UseMemoryShrinker=false
jcs.region.blacklistFileNextRecordCache.cacheattributes.MaxMemoryIdleTimeSeconds=3600
jcs.region.blacklistFileNextRecordCache.cacheattributes.ShrinkerIntervalSeconds=60
jcs.region.blacklistFileNextRecordCache.cacheattributes.MaxSpoolPerRun=500
jcs.region.blacklistFileNextRecordCache.elementattributes=org.apache.jcs.engine.ElementAttributes
jcs.region.blacklistFileNextRecordCache.elementattributes.IsEternal=true

jcs.region.terminalStanCache=DC
jcs.region.terminalStanCache.cacheattributes=org.apache.jcs.engine.CompositeCacheAttributes
jcs.region.terminalStanCache.cacheattributes.MaxObjects=10000
jcs.region.terminalStanCache.cacheattributes.MemoryCacheName=org.apache.jcs.engine.memory.lru.LRUMemoryCache
jcs.region.terminalStanCache.cacheattributes.UseMemoryShrinker=false
jcs.region.terminalStanCache.cacheattributes.MaxMemoryIdleTimeSeconds=3600
jcs.region.terminalStanCache.cacheattributes.ShrinkerIntervalSeconds=60
jcs.region.terminalStanCache.cacheattributes.MaxSpoolPerRun=500
jcs.region.terminalStanCache.elementattributes=org.apache.jcs.engine.ElementAttributes
jcs.region.terminalStanCache.elementattributes.IsEternal=true

# AVAILABLE AUXILIARY CACHES
jcs.auxiliary.DC=org.apache.jcs.auxiliary.disk.indexed.IndexedDiskCacheFactory
jcs.auxiliary.DC.attributes=org.apache.jcs.auxiliary.disk.indexed.IndexedDiskCacheAttributes
jcs.auxiliary.DC.attributes.DiskPath=jcs_swap
jcs.auxiliary.DC.attributes.MaxPurgatorySize=10000000
jcs.auxiliary.DC.attributes.MaxKeySize=1000000
jcs.auxiliary.DC.attributes.MaxRecycleBinSize=5000
jcs.auxiliary.DC.attributes.OptimizeAtRemoveCount=300000
jcs.auxiliary.DC.attributes.ShutdownSpoolTimeLimit=60
