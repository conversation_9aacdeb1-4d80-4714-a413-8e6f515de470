<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:jdbc="http://www.springframework.org/schema/jdbc"	
	xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:util="http://www.springframework.org/schema/util"
	xsi:schemaLocation="
			http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
			http://www.springframework.org/schema/jdbc http://www.springframework.org/schema/jdbc/spring-jdbc-3.0.xsd
			http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd
			http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-2.0.xsd
			http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-2.0.xsd
			http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-3.0.xsd">
	
	<!-- Root Context: defines shared resources accessible to all other web components -->
	
	<bean id="properties" class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
		<property name="location">
			<value>classpath:applicationContext.properties</value>
		</property>
	</bean>

	<!-- creates a bean to hold the application properties, so that the properties can be access using @Value annotation -->
	<!-- example: @Value({appConfig['host.name']}) -->
	<util:properties id="appConfig" location="classpath:applicationContext.properties"/>

	<!-- Drives transactions using hibernate -->
	<bean id="transactionManager" class="org.springframework.orm.hibernate3.HibernateTransactionManager">
		<property name="sessionFactory" ref="sessionFactory" />
	</bean>

	<bean id="sessionFactory" class="org.springframework.orm.hibernate3.annotation.AnnotationSessionFactoryBean">
		<property name="dataSource" ref="dataSource"/>
		<property name="hibernateProperties">
			<props>
				<prop key="hibernate.dialect">${hibernate.dialect}</prop>
				<prop key="hibernate.show_sql">${hibernate.show_sql}</prop>
				<prop key="hibernate.connection.pool_size">100</prop>
				<prop key="hibernate.jdbc.batch_size">1000</prop>
				<prop key="hibernate.bytecode.use_reflection_optimizer">true</prop>
                <prop key="hibernate.cache.use_second_level_cache">false</prop>
			</props>
		</property>
		<property name="packagesToScan" value="com.abl.db"/>
	</bean>

	<bean id="dataSource" destroy-method="close" class="org.apache.commons.dbcp.BasicDataSource">
        <property name="driverClassName" value="${datasource.driverClassName}"/>
        <property name="url" value="${datasource.url}"/>
        <property name="username" value="${datasource.username}"/>
        <property name="password" value="${datasource.password}"/>
        <property name="maxActive" value="${datasource.maxActive}"/>
        <property name="maxWait" value="${datasource.maxWait}"/>
        <property name="testOnBorrow" value="${datasource.testOnBorrow}"/>
        <property name="validationQuery" value="${datasource.validationQuery}"/>
        <property name="defaultAutoCommit" value="false"/>
    </bean>

	<!-- enable the configuration of transactional behavior based on annotations -->
	<tx:annotation-driven transaction-manager="transactionManager"/>

	<!-- Beans -->

	<context:annotation-config/>
	<context:component-scan base-package="com.abl.db.service" />
	<context:component-scan base-package="com.abl.db.dao" />
	<context:component-scan base-package="com.abl.tms.service" />

    <bean id="configService" class="com.abl.db.service.impl.ConfigServiceImpl" >
        <property name="config">
            <map>
                <entry key="default_group_name" value="DEFAULT"/>
            </map>
        </property>
    </bean>
</beans>
