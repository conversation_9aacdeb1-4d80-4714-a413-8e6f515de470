log4j.rootLogger=ALL, logfile

log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
log4j.appender.stdout.layout.ConversionPattern=%d %p [%c] - <%m>%n

log4j.appender.logfile=org.apache.log4j.DailyRollingFileAppender
log4j.appender.logfile.File=logs/tmsserver-root.log
log4j.appender.logfile.DatePattern=-yyyyMMdd'.log'
log4j.appender.logfile.layout=org.apache.log4j.PatternLayout
log4j.appender.logfile.layout.ConversionPattern=%d %-5p %X{id} = %m - %c (%F:%L)%n
log4j.appender.logfile.encoding = UTF-8
log4j.appender.logfile.append = true

log4j.logger.com.abl=ALL, com.abl
log4j.appender.com.abl=org.apache.log4j.DailyRollingFileAppender
log4j.appender.com.abl.File=logs/tmsserver-app.log
log4j.appender.com.abl.DatePattern=-yyyyMMdd'.log'
log4j.appender.com.abl.layout=org.apache.log4j.PatternLayout
log4j.appender.com.abl.layout.ConversionPattern=%d %-5p %X{id} = %m (%F:%L)%n
log4j.appender.com.abl.encoding = UTF-8
log4j.appender.com.abl.append = true

log4j.logger.org.jpos=ALL, org.jpos
log4j.appender.org.jpos=org.apache.log4j.DailyRollingFileAppender
log4j.appender.org.jpos.File=logs/tmsserver-jpos.log
log4j.appender.org.jpos.DatePattern=-yyyyMMdd'.log'
log4j.appender.org.jpos.layout=org.apache.log4j.PatternLayout
log4j.appender.org.jpos.layout.ConversionPattern=%d %-5p %X{id} = %m (%F:%L)%n
log4j.appender.org.jpos.encoding = UTF-8
log4j.appender.org.jpos.append = true

log4j.logger.org.hibernate.SQL=DEBUG, hibernate
log4j.appender.hibernate=org.apache.log4j.DailyRollingFileAppender
log4j.appender.hibernate.File=logs/tmsserver-hibernate.log
log4j.appender.hibernate.DatePattern=-yyyyMMdd'.log'
log4j.appender.hibernate.layout=org.apache.log4j.PatternLayout
log4j.appender.hibernate.layout.ConversionPattern=%d %-5p %X{id} = %m (%F:%L)%n
log4j.appender.hibernate.encoding = UTF-8
log4j.appender.hibernate.append = false

