#!/bin/sh

# script to run txn server

export JAVA=/usr/bin/java
export ERACOM_LIB=/opt/Eracom/lib
export LD_LIBRARY_PATH=${ERACOM_LIB}
# export ET_HSM_NETCLIENT_SERVERLIST=127.0.0.1

CP=conf
for fname in ${ERACOM_LIB}/*.jar; do
        CP=${CP}:$fname
done
for fname in lib/*.jar; do
        CP=${CP}:$fname
done

export CLASSPATH=$CLASSPATH:$CP
nohup ${JAVA} -Xmx1024m com.abl.tms.TmsServer > tmsServer.out 2>&1 &
echo $! > pid

