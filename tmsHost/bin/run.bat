@echo off

rem ==================================================
rem Configuration
rem
rem MAIN = class containing the main method
rem ARGS = list of arguments, delimited by space
rem        Use quotation marks for args with space
rem        Use %*% for user provide args
rem ==================================================

set MAIN=com.abl.tms.TmsServer
set ARGS=

set JAVA_PATH="C:\Program Files\Java\jre1.8.0_251\bin"

rem ==================================================
rem RUN
rem ==================================================

set CLASSPATH=conf
for %%i in (lib\*.jar) do call :CP %%i

set opath=%PATH%
set PATH=%PATH%;lib;

%JAVA_PATH%\java -Xmx1024m %MAIN% %ARGS%

set CLASSPATH=
set PATH=%opath%
GOTO :EOF

:CP
SET CLASSPATH=%CLASSPATH%;%1
GOTO :EOF

:EOF
