<server name="Q-server" class="org.jpos.q2.iso.QServer" logger="Q2" realm="Q2.host.server">

    <attr name="port" type="java.lang.Integer">5657</attr>
	<attr name="minSessions" type="java.lang.Integer">1000</attr>  <!-- Min -->
	<attr name="maxSessions" type="java.lang.Integer">1000</attr> <!-- Max -->

    <channel name="server.channel"
            class="org.jpos.iso.channel.NACChannel"
            packager="org.jpos.iso.packager.GenericPackager" header="6000028000"
            logger="Q2" realm="Q2.host.server">
        <property name="packager-config" value="conf/packager/abl.xml" />
    </channel>

    <request-listener class="com.abl.tms.RequestListener">
        <property name="space" value="transient:tmsServer:space/tmsServer" />
        <property name="queue" value="tmsServer" />
        <property name="timeout" value="10000" />
    </request-listener>
</server>
