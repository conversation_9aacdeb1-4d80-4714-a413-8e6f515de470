<txnmgr name="TxnMgr" logger="Q2" class="org.jpos.transaction.TransactionManager">

	<property name="space" value="transient:tmsServer:space/tmsServer" />
    <property name="queue" value="tmsServer" />
    <property name="sessions" value="100" />

	<participant class="com.abl.tms.participant.BeginTransaction"/>

	<!--<participant class="com.abl.tms.participant.InsertTxnLog"/>-->

	<participant class="com.abl.tms.participant.SelectTxn">
		<property name="PARAM_VERSION"		    value="InsertTxnLog ParamVersion PrepareResponse SaveTxn SendResponse" />
		<property name="PARAM_DOWNLOAD"			value="InsertTxnLog ParamDownload PrepareResponse SaveTxn SendResponse" />
		<property name="APP_VERSION"			value="InsertTxnLog AppVersion PrepareResponse SaveTxn SendResponse" />
		<property name="FILE_DOWNLOAD"			value="FileDownload PrepareResponse CommitTxn SendResponse" />
        <property name="LOG_UPLOAD"		    	value="InsertTxnLog LogUpload PrepareResponse SaveTxn SendResponse" />
        <property name="STUCK_UPLOAD"		    value="InsertTxnLog StuckUpload PrepareResponse SaveTxn SendResponse" />
        <property name="TMS_LOGON"		    	value="InsertTxnLog TmsLogon PrepareResponse SaveTxn SendResponse" />
		<property name="UNKNOWN"				value="InsertTxnLog Unknown PrepareResponse SaveTxn SendResponse" />
	</participant>

    <group name="InsertTxnLog">
        <participant class="com.abl.tms.participant.InsertTxnLog"/>
    </group>

    <group name="PrepareResponse">
        <participant class="com.abl.tms.participant.UpdateResponseCode"/>
    </group>

	<group name="SaveTxn">
		<participant class="com.abl.tms.participant.SaveTxn"/>
	</group>

    <group name="CommitTxn">
        <participant class="com.abl.tms.participant.CommitTxn"/>
    </group>

    <group name="SendResponse">
		<participant class="com.abl.tms.participant.SendResponse"/>
	</group>

	<group name="ParamVersion">
		<participant class="com.abl.tms.participant.CreateResponse">
			<property name="fields" value="3,11,12,13,41,48,63" />
			<property name="12" value="txnDate,HHmmss" />
			<property name="13" value="txnDate,MMdd" />
		</participant>
		<participant class="com.abl.tms.participant.CheckRequiredFields">
			<property name="fields" value="3,11,48,63" />
		</participant>
		<participant class="com.abl.tms.participant.ParseParamVersion"/>
		<participant class="com.abl.tms.participant.GetTerminal"/>
        <participant class="com.abl.tms.participant.GetParamVersion"/>
	</group>

    <group name="ParamDownload">
        <participant class="com.abl.tms.participant.CreateResponse">
            <property name="fields" value="3,11,12,13,41,48,61,63" />
            <property name="12" value="txnDate,HHmmss" />
            <property name="13" value="txnDate,MMdd" />
        </participant>
        <participant class="com.abl.tms.participant.CheckRequiredFields">
            <property name="fields" value="3,11,48,61,63" />
        </participant>
        <participant class="com.abl.tms.participant.ParseParamDownload"/>
        <participant class="com.abl.tms.participant.GetTerminal"/>
        <participant class="com.abl.tms.participant.DownloadParam"/>
    </group>

	<group name="AppVersion">
		<participant class="com.abl.tms.participant.CreateResponse">
			<property name="fields" value="3,11,12,13,41,48,63" />
			<property name="12" value="txnDate,HHmmss" />
			<property name="13" value="txnDate,MMdd" />
		</participant>
		<participant class="com.abl.tms.participant.CheckRequiredFields">
			<property name="fields" value="3,11,48,63" />
		</participant>
		<participant class="com.abl.tms.participant.ParseAppVersion"/>
		<participant class="com.abl.tms.participant.GetTerminal"/>
        <participant class="com.abl.tms.participant.GetAppVersion"/>
	</group>

    <group name="FileDownload">
        <participant class="com.abl.tms.participant.CreateResponse">
            <property name="fields" value="3,11,41,48,61,63" />
        </participant>
        <participant class="com.abl.tms.participant.CheckRequiredFields">
            <property name="fields" value="3,11,48,61,63" />
        </participant>
        <participant class="com.abl.tms.participant.ParseFileDownload"/>
        <participant class="com.abl.tms.participant.GetTerminal"/>
        <participant class="com.abl.tms.participant.DownloadFile"/>
    </group>

    <group name="LogUpload">
        <participant class="com.abl.tms.participant.CreateResponse">
            <property name="fields" value="3,11,41,61,63" />
        </participant>
        <participant class="com.abl.tms.participant.CheckRequiredFields">
            <property name="fields" value="3,11,48,61,63" />
        </participant>
        <participant class="com.abl.tms.participant.ParseLogUpload"/>
        <participant class="com.abl.tms.participant.GetTerminal"/>
        <participant class="com.abl.tms.participant.UploadLog"/>
    </group>

    <group name="StuckUpload">
        <participant class="com.abl.tms.participant.CreateResponse">
            <property name="fields" value="3,11,41,48,61" />
        </participant>
        <participant class="com.abl.tms.participant.CheckRequiredFields">
            <property name="fields" value="3,11,41,48,61,63" />
        </participant>
        <participant class="com.abl.tms.participant.ParseStuckTxnUpload"/>
        <participant class="com.abl.tms.participant.GetTerminal"/>
        <participant class="com.abl.tms.participant.StuckTxnUpload"/>

    </group>

    <group name="TmsLogon">
        <participant class="com.abl.tms.participant.CreateResponse">
            <property name="fields" value="3,11,12,13,41" />
            <property name="12" value="txnDate,HHmmss" />
            <property name="13" value="txnDate,MMdd" />
        </participant>
        <participant class="com.abl.tms.participant.CheckRequiredFields">
            <property name="fields" value="3,11,61,63" />
        </participant>
        <participant class="com.abl.tms.participant.ParseLogon"/>
        <participant class="com.abl.tms.participant.GetTerminal"/>
        <participant class="com.abl.tms.participant.GetTerminalActionCode"/>
    </group>

	<group name="Unknown">
		<participant class="com.abl.tms.participant.CreateResponse">
			<property name="fields" value="3,11,12,13,41,42" />
			<property name="12" value="txnDate,HHmmss" />
			<property name="13" value="txnDate,MMdd" />
		</participant>
		<participant class="com.abl.tms.participant.ParseTxn"/>
		<participant class="com.abl.tms.participant.Abort"/>
	</group>

</txnmgr>
