package com.abl.tms.utils;

import org.jpos.transaction.Context;

public class ContextUtils {
    public static enum Item {
//		CARD,				// Card
        ISO_SOURCE(false),    // ISOSource of the request
        LAST_TXN_LOG,        // last tms log
//		MERCHANT,			// Merchant object
        REQUEST,            // request msg
        RESPONSE,            // response msg
        RESULT_CODE,        // TxnResult.ResultCode object
        SPRING_TXN_MGR,
        SPRING_TXN_STATUS,
        TERMINAL,            // Terminal object
        TERMINAL_PROFILE,    // Terminal profile
        TXN_DETAILS,        // TxnDetails object
        TXN_LOG,            // TxnLog object
        VEHICLE_GROUP,      // VehicleGroup object
        ;

        private boolean serializable = true; // by default, all items are serializable

        Item() {
        }

        Item(boolean serializable) {
            this.serializable = serializable;
        }

        public boolean isSerializable() {
            return serializable;
        }
    }

    /**
     * put into context
     *
     * @param context
     * @param key
     * @param value
     */
    public static void put(Context context, Item key, Object value) {
        context.put(key, value, key.isSerializable());
    }

    /**
     * put into context if the context does not already contain an object with this key
     *
     * @param context
     * @param key
     * @param value
     */
    public static void putIfDoesNotExist(Context context, Item key, Object value) {
        if (context.get(key) == null) {
            context.put(key, value, key.isSerializable());
        }
    }

    /**
     * gets object from context.
     * Alternatively, you may want to use "context.get(key)".
     *
     * @param context
     * @param key
     * @return
     */
    public static Object get(Context context, Item key) {
        return context.get(key);
    }

}
