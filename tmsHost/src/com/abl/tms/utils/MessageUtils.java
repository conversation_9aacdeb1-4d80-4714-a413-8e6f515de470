package com.abl.tms.utils;

import com.abl.utils.ByteUtils;
import org.apache.log4j.Logger;

import java.util.ArrayList;

public class MessageUtils {

    private static final Logger logger = Logger.getLogger(MessageUtils.class);

    /**
     * parse a byte[] with multiple lllVar bytes into an ArrayList of byte[]
     *
     * @param lllVarBytes a byte array with multiple lllVar bytes
     * @return an ArrayList of byte[]
     */
    public static ArrayList<byte[]> parseLllVarBytes(byte[] lllVarBytes) {
        if (lllVarBytes == null)
            return null;

        ArrayList bytesRecords = new ArrayList();
        int recordInd = 0;
        int numOfBytes = 0;
        do {
            byte[] lengthBytes = ByteUtils.subArray(lllVarBytes, recordInd, recordInd + 4);
            numOfBytes = (int) ByteUtils.binaryToLong(lengthBytes);
            logger.debug("numOfBytes=" + numOfBytes);
            byte[] recordBytes = ByteUtils.subArray(lllVarBytes, recordInd + 4, recordInd + 4 + numOfBytes);
            logger.debug("recordBytes.length=" + recordBytes.length);
            bytesRecords.add(recordBytes);
            recordInd = recordInd + 4 + numOfBytes;
            logger.debug("recordInd=" + recordInd);
        } while (recordInd < lllVarBytes.length);

        return bytesRecords;
    }
}
