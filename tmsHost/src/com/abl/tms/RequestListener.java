package com.abl.tms;

import com.abl.tms.utils.ContextUtils;
import org.jpos.core.Configurable;
import org.jpos.core.Configuration;
import org.jpos.core.ConfigurationException;
import org.jpos.iso.ISOMsg;
import org.jpos.iso.ISORequestListener;
import org.jpos.iso.ISOSource;
import org.jpos.space.Space;
import org.jpos.space.SpaceFactory;
import org.jpos.transaction.Context;

/**
 * Receives incoming requests, sets up the context, and hands it to the transaction manager
 */
public class RequestListener implements ISORequestListener, Configurable {
    private String space;
    private String queue;
    private Long timeout;

    @Override
    public void setConfiguration(Configuration cfg) throws ConfigurationException {
        space = cfg.get("space");
        queue = cfg.get("queue");
        timeout = cfg.getLong("timeout");
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    @Override
    public boolean process(ISOSource isoSource, ISOMsg isoMsg) {

        // sets up context
        Context context = new Context();
        ContextUtils.put(context, ContextUtils.Item.ISO_SOURCE, isoSource);
        ContextUtils.put(context, ContextUtils.Item.REQUEST, isoMsg);

        // put context into space
        Space sp = SpaceFactory.getSpace(space);
        sp.out(queue, context, timeout);

        return true;
    }

}
