package com.abl.tms.participant;

import com.abl.db.model.TxnResult;
import com.abl.tms.utils.ContextUtils;
import com.abl.tms.utils.ContextUtils.Item;
import org.apache.log4j.Logger;
import org.jpos.core.Configuration;
import org.jpos.core.ConfigurationException;
import org.jpos.iso.ISOMsg;
import org.jpos.iso.ISOSource;
import org.jpos.transaction.AbortParticipant;
import org.jpos.transaction.Context;

import java.io.Serializable;

public class SendResponse extends AbstractParticipant implements AbortParticipant {

    private static final Logger logger = Logger.getLogger(SendResponse.class);

    private boolean sendOnError = true;    // true means to send response even when an error has occurred

    @Override
    public void setConfiguration(Configuration cfg)
            throws ConfigurationException {
        super.setConfiguration(cfg);

        String cfgSendOnError = cfg.get("sendOnError", null);
        if (cfgSendOnError != null) {
            sendOnError = cfgSendOnError.equalsIgnoreCase("true");
        }
    }

    @Override
    public int prepareForAbort(long id, Serializable context) {
        logger.error("prepareForAbort");
        return PREPARED | READONLY;
    }

    @Override
    public void abort(long id, Context context) {
        logger.error("abort");

        if (!sendOnError) {
            logger.error("not sending response on error");
            return;
        }

        ISOMsg response = (ISOMsg) context.get(Item.RESPONSE);
        if (response == null) {
            // no response
            logger.warn("no response found");
            return;
        }

        ISOSource isoSource = (ISOSource) context.get(Item.ISO_SOURCE);
        try {
            isoSource.send(response);
        } catch (Exception e) {
            logger.error("unable to send response", e);
        }
    }

    @Override
    public void commit(long id, Context context) {
        logger.debug("commit");
    }

    @Override
    public int prepare(long id, Context context) {
        logger.debug("prepare");

        ISOMsg response = (ISOMsg) context.get(Item.RESPONSE);
        if (response == null) {
            // no response, assume this is intentional
            // e.g. for batch uploads, only respond at the last message of each batch
            ContextUtils.putIfDoesNotExist(context, Item.RESULT_CODE, TxnResult.APPROVED);
            return PREPARED;
        }

        ISOSource isoSource = (ISOSource) context.get(Item.ISO_SOURCE);
        try {
            isoSource.send(response);
        } catch (Exception e) {
            logger.error("unable to send response", e);
            ContextUtils.putIfDoesNotExist(context, Item.RESULT_CODE, TxnResult.SYSERR_COMM);
            return ABORTED;
        }

        return PREPARED;
    }
}
