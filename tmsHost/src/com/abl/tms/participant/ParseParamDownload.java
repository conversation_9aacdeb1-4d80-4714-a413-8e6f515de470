package com.abl.tms.participant;

import com.abl.db.model.TxnType;
import com.abl.db.service.TxnTypeService;
import com.abl.tms.TxnDetails;
import com.abl.utils.SpringUtils;
import org.apache.log4j.Logger;
import org.jpos.iso.ISOMsg;

public class ParseParamDownload extends ParseTxn {

    private static final Logger logger = Logger.getLogger(ParseParamDownload.class);
    private static final int PARAM_VERSION_LENGTH = 22;
    private static final int FILE_NO_LENGTH = 6;
    public static final int TERMINAL_INFO_LENGTH = 60;

    @Override
    protected boolean parseRequest(TxnDetails txnDetails, ISOMsg request) {
        super.parseRequest(txnDetails, request);

        String taxiNumber = "";
        try {
            taxiNumber = getStringField(request, 63).substring(40, 60).trim();
        } catch (Exception e) {
            logger.error("Vehicle number cannot be parsed successfully");
        }

        TxnTypeService txnTypeService = SpringUtils.getBean(TxnTypeService.class);
        TxnType txnType = txnTypeService.getTxnType(TxnType.TYPE_PARAM_DOWNLOAD);
        txnDetails.setTxnType(txnType);

        byte[] field48 = request.getBytes(48);
        if (field48.length != PARAM_VERSION_LENGTH) {
            logger.error("[" + taxiNumber + "] " + "field 48 has invalid length: " + field48.length);
            return false;
        }

        String versionStr = new String(field48);
        logger.debug("reqField48=" + versionStr);
        String appName = versionStr.substring(0, 10);
        logger.debug("appName=" + appName);
        String appVersion = versionStr.substring(10, 16);
        logger.debug("appVersion=" + appVersion);

        try {
            Integer.parseInt(appVersion);
        } catch (Exception e) {
            logger.error("[" + taxiNumber + "] " + "app version is invalid " + appVersion);
            return false;
        }

        byte[] field61 = request.getBytes(61);
        if (field61.length != FILE_NO_LENGTH) {
            logger.error("[" + taxiNumber + "] " + "field 61 has invalid length: " + field61.length);
            return false;
        }

        try {
            Integer.parseInt(new String(field61));
        } catch (Exception e) {
            logger.error("[" + taxiNumber + "] " + "field 61 has invalid value: " + new String(field61));
            return false;
        }

        byte[] field63 = request.getBytes(63);
        if (field63 == null || field63.length != TERMINAL_INFO_LENGTH) {
            logger.error("[" + taxiNumber + "] " + "field 63 has invalid length: " + (field63 == null ? 0 : field63.length));
            return false;
        }
        txnDetails.setTermSerialNo(getStringField(request, 63).substring(0, 20).trim());
        txnDetails.setModelName(getStringField(request, 63).substring(20, 40).trim());
        txnDetails.setTaxiNumber(taxiNumber);

        checkStan(txnDetails);

        return true;
    }
}
