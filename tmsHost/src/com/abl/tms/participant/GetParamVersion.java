package com.abl.tms.participant;

import com.abl.db.model.*;
import com.abl.db.service.ApplicationService;
import com.abl.db.service.ParameterDefinitionService;
import com.abl.db.service.TerminalProfileService;
import com.abl.db.service.VehicleGroupService;
import com.abl.tms.utils.ContextUtils;
import com.abl.tms.utils.ContextUtils.Item;
import com.abl.utils.DateUtils;
import com.abl.utils.SpringUtils;
import com.abl.utils.StringUtils;
import org.apache.log4j.Logger;
import org.jpos.iso.ISOException;
import org.jpos.iso.ISOMsg;
import org.jpos.transaction.Context;

import java.util.Date;
import java.util.List;

public class GetParamVersion extends AbstractParticipant {

    private static final Logger logger = Logger.getLogger(GetParamVersion.class);

    @Override
    public void abort(long id, Context context) {
        logger.error("abort");
    }

    @Override
    public void commit(long id, Context context) {
        logger.debug("commit");
    }

    @Override
    public int prepare(long id, Context context) {
        logger.debug("prepare");

        TmsTerminal terminal = (TmsTerminal) context.get(Item.TERMINAL);

        ISOMsg request = (ISOMsg) context.get(Item.REQUEST);
        ApplicationService applicationService = SpringUtils.getBean(ApplicationService.class);
//        VehicleGroupService vehicleGroupService = SpringUtils.getBean(VehicleGroupService.class);
        TerminalProfileService terminalProfileService = SpringUtils.getBean(TerminalProfileService.class);
        ParameterDefinitionService parameterDefinitionService = SpringUtils.getBean(ParameterDefinitionService.class);
        byte[] reqField48 = request.getBytes(48);
        logger.debug("reqField48=" + new String(reqField48));
        StringBuffer resField48 = new StringBuffer();

        for (int i = 0; i < (reqField48.length / ParseParamVersion.PARAM_VERSION_LENGTH); i++) {
            byte[] versionBytes = new byte[ParseParamVersion.PARAM_VERSION_LENGTH];
            System.arraycopy(reqField48, i * ParseParamVersion.PARAM_VERSION_LENGTH, versionBytes, 0, ParseParamVersion.PARAM_VERSION_LENGTH);
            String versionStr = new String(versionBytes);
            logger.debug("versionStr=" + versionStr);
            String appName = versionStr.substring(0, 10);
            logger.debug("appName=" + appName);
            String appVersion = versionStr.substring(10, 16);
            logger.debug("appVersion=" + appVersion);
            resField48.append(appName);
            resField48.append(appVersion);

            List<Application> applications = applicationService.searchApplicationByNameVersion(appName.trim(), Integer.parseInt(appVersion.trim()));
            if (applications == null || applications.size() <= 0) {
                logger.error("[" + terminal.getVehicleNo() + "] " + "application version (" + appName + "," + appVersion + ") not found");
                ContextUtils.putIfDoesNotExist(context, Item.RESULT_CODE, TxnResult.ERROR_APPLICATION_NOT_FOUND);
                return ABORTED;
            }

//            VehicleGroup vehicleGroup = vehicleGroupService.getVehicleGroup(terminal.getVehicleNo());
            VehicleGroup vehicleGroup = (VehicleGroup) context.get(ContextUtils.Item.VEHICLE_GROUP);

            if (vehicleGroup == null) {
                logger.error("[" + terminal.getVehicleNo() + "] " + "vehicle does not belong to any group");
                ContextUtils.putIfDoesNotExist(context, Item.RESULT_CODE, TxnResult.ERROR_NO_GROUP);
                return ABORTED;
            }

            Application application = applications.get(0);

            int numberOfParameter = parameterDefinitionService.countParameters(application.getId());

            if (numberOfParameter == 0) {
                // no parameters for this application, return '000000'
                resField48.append(StringUtils.padLeftToLen(Long.toString(0), '0', 6));
            } else {
                TerminalProfile terminalProfile = terminalProfileService.getLatestActiveTerminalProfile(application.getId(), vehicleGroup.getGroup().getId());

                if (terminalProfile == null || terminalProfile.getProfileVersion() <= 0 || terminalProfile.getProfileParameterValue().size() == 0) {
                    // 2013-06-04, ky, requested by julee to return '000000' for no profile found
                    resField48.append(StringUtils.padLeftToLen(Long.toString(0), '0', 6));

//                    logger.error("[" + terminal.getVehicleNo() + "] " + "terminal profile for application version (" + appName + "," + appVersion + "), group id (" + vehicleGroup.getGroup().getId() + ") is not found");
//                    ContextUtils.putIfDoesNotExist(context, Item.RESULT_CODE, TxnResult.ERROR_NO_PROFILE);
//                    return ABORTED;
                } else {

                    resField48.append(StringUtils.padLeftToLen(Long.toString(terminalProfile.getId()), '0', 6));

                }

            }
        }

        ISOMsg response = (ISOMsg) context.get(Item.RESPONSE);
        try {
            response.set(12, DateUtils.format(new Date(), "HHmmss"));
            response.set(13, DateUtils.format(new Date(), "MMdd"));
            response.set(48, resField48.toString().getBytes());
        } catch (ISOException e) {
            logger.error("[" + terminal.getVehicleNo() + "] " + "param version error", e);
            ContextUtils.putIfDoesNotExist(context, Item.RESULT_CODE, TxnResult.ERROR);
            return ABORTED;
        }

        return PREPARED;
    }
}
