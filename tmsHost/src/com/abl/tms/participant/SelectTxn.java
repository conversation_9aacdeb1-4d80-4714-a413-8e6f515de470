package com.abl.tms.participant;

import com.abl.tms.utils.ContextUtils;
import com.abl.tms.utils.ContextUtils.Item;
import org.apache.log4j.Logger;
import org.jpos.iso.ISOException;
import org.jpos.iso.ISOMsg;
import org.jpos.transaction.Context;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Check the ISOMsg to determine what is the transaction, based on MTI and processing code.
 * <p/>
 * The mapping of (MTI, proc code) to transaction is defined in TXN_MAPPING_FILE file,
 * with the following format:
 * [MTI];[proc code]=[transaction name]
 * <p/>
 * [MTI] and [proc code] accepts any java regex pattern, for example:
 * <p/>
 * 0200;123456		// matches mti="0200", proc code="123456"
 * 0200;1....6		// matches mti="0200", 6 char proc code that begins with '1' and ends with '6'
 * 0200;.*			// matches mti="0200", and any proc code
 * .*;.*			// matches any mti and any proc code
 * <p/>
 * The search will be in sequential order, and will stop at the first match.  If nothing match,
 * the string UNKNOWN_TRANS will be returned.
 * <p/>
 * Notes:
 * 1. Lines that begin with '#' is treated as comments
 * 2. TXN_MAPPING_FILE must be in classpath
 */
public class SelectTxn extends AbstractSelector {

    private static final Logger logger = Logger.getLogger(SelectTxn.class);

    public static final String TXN_MAPPING_FILE = "txnMap.properties";

    private List<TransactionMatcher> transactionMatchers = new ArrayList<TransactionMatcher>();

    public SelectTxn() {
        try {
            initialize();
        } catch (Exception e) {
            logger.error("initialize error", e);
        }
    }

    /**
     * performs initialization by reading the transaction mapping file
     *
     * @throws Exception
     */
    private void initialize() throws Exception {

        URL resource = SelectTxn.class.getClassLoader().getResource(TXN_MAPPING_FILE);
        if (resource == null) {
            logger.error(TXN_MAPPING_FILE + " not found");
            return;
        }

        BufferedReader input = new BufferedReader(new FileReader(resource.getFile()));
        try {
            String line;
            while ((line = input.readLine()) != null) {

                if (line.startsWith("#")) { // ignore lines beginning with '#'
                    continue;
                }

                line = line.trim(); // remove extra spaces
                if (line.length() == 0) { // ignore empty lines
                    continue;
                }

                String[] keyValue = line.split("=", 2);
                if (keyValue.length != 2) {
                    logger.error("invalid line \"" + line + "\"");
                    break;
                }

                String[] mtiProcCode = keyValue[0].trim().split(";", 2);
                if (mtiProcCode.length != 2) {
                    logger.error("invalid line \"" + line + "\"");
                    break;
                }

                String mti = mtiProcCode[0];
                String procCode = mtiProcCode[1];
                String trans = keyValue[1].split("#", 2)[0]; // ignore comments at the end of line

                transactionMatchers.add(new TransactionMatcher(mti, procCode, trans));
            }
        } finally {
            input.close();
        }
    }

    /**
     * performs matching, returns the transaction if found
     * if not found, UNKNOWN_TRANS is returned
     *
     * @param mti
     * @param procCode
     * @return
     */
    private String match(String mti, String procCode) {

        // searches through "transactionMatcher" sequentially until a match is found
        for (TransactionMatcher transactionMatcher : transactionMatchers) {
            if (transactionMatcher.match(mti, procCode)) {
                String trans = transactionMatcher.getTransaction();
                return trans;
            }
        }

        logger.info("transaction not found");
        return "";
    }

    @Override
    public String select(long id, Context context) {
        logger.debug("select");

        ISOMsg isoMsg = (ISOMsg) ContextUtils.get(context, Item.REQUEST);
        String mti;
        try {
            mti = isoMsg.getMTI();
        } catch (ISOException e) {
            logger.error("MTI not found");
            return null;
        }
        String procCode = isoMsg.getString(3);
        String trans = match(mti, procCode);

        logger.debug("trans is " + trans);
        return getConfiguration().get(trans, null);
    }

    @Override
    public void abort(long id, Context context) {
        logger.error("abort");
    }

    @Override
    public void commit(long id, Context context) {
        logger.debug("commit");
    }

    @Override
    public int prepare(long id, Context context) {
        logger.debug("prepare");
        return PREPARED;
    }

}

/**
 * Used for pattern searching
 */
class TransactionMatcher {

    private Pattern mtiPattern;
    private Pattern procCodePattern;
    private String transaction;

    public TransactionMatcher(String mti, String procCode, String transaction) {
        this.mtiPattern = Pattern.compile(mti);
        this.procCodePattern = Pattern.compile(procCode);
        this.transaction = transaction;
    }

    public String getTransaction() {
        return transaction;
    }

    public boolean match(String mti, String procCode) {
        Matcher mtiMatcher = mtiPattern.matcher(mti);
        if (!mtiMatcher.matches()) {
            return false;
        }

        Matcher procCodeMatcher = procCodePattern.matcher(procCode);
        if (!procCodeMatcher.matches()) {
            return false;
        }
        return true;
    }
}
