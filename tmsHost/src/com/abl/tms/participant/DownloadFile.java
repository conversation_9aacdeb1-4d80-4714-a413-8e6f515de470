package com.abl.tms.participant;

import com.abl.db.model.*;
import com.abl.db.service.*;
import com.abl.tms.utils.ContextUtils;
import com.abl.utils.ByteUtils;
import com.abl.utils.DateUtils;
import com.abl.utils.SpringUtils;
import com.abl.utils.StringUtils;
import org.apache.log4j.Logger;
import org.jpos.iso.ISOException;
import org.jpos.iso.ISOMsg;
import org.jpos.transaction.Context;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

public class DownloadFile extends AbstractParticipant {
    private static final Logger logger = Logger.getLogger(DownloadFile.class);

    @Override
    public void abort(long id, Context context) {
        logger.error("abort");
    }

    @Override
    public void commit(long id, Context context) {
        logger.debug("commit");
    }

    @Override
    public int prepare(long id, Context context) {
        logger.debug("prepare");

        TmsTerminal terminal = (TmsTerminal) context.get(ContextUtils.Item.TERMINAL);

        ISOMsg request = (ISOMsg) context.get(ContextUtils.Item.REQUEST);

        byte[] reqField48 = request.getBytes(48);
        String versionStr = new String(reqField48);
        logger.debug("reqField48=" + versionStr);
        String fileName = versionStr.substring(0, 10);
        logger.debug("fileName=" + fileName);
        String fileVersion = versionStr.substring(10, 16);
        logger.debug("fileVersion=" + fileVersion);

        if (fileName.trim().equalsIgnoreCase("CASTLE_REL")) {
            return downloadRelease(context);
        } else {
            logger.error("[" + terminal.getVehicleNo() + "] " + "Invalid file name (" + fileName + ")");
            ContextUtils.putIfDoesNotExist(context, ContextUtils.Item.RESULT_CODE, TxnResult.ERROR_INV_FILE_NAME);
            return ABORTED;
        }
    }

    private int downloadRelease(Context context) {

        TmsTerminal terminal = (TmsTerminal) context.get(ContextUtils.Item.TERMINAL);

        ISOMsg request = (ISOMsg) context.get(ContextUtils.Item.REQUEST);

        byte[] reqField48 = request.getBytes(48);
        String versionStr = new String(reqField48);
        String fileVersion = versionStr.substring(10, 16);

        ReleaseService releaseService = SpringUtils.getBean(ReleaseService.class);
        Release release = releaseService.getReleaseByVersion(Integer.parseInt(fileVersion));

        logger.debug("release=" + release);

        if (release == null) {
            String errorMessage = "requested release version (" + fileVersion + ") is not found";
            handleError(context, TxnResult.ERROR_INV_VERSION, errorMessage, terminal, release);

            return ABORTED;
        }

        Model model = terminal.getModel();
        logger.debug("model=" + model);

        if (!releaseService.hasModel(release.getId(), model)) {
            String errorMessage = "requested release (" + release.getDescription() + ") does not support this vehicle model (" + model.getModelName() + ")";
            handleError(context, TxnResult.ERROR_INV_REQ_DATA, errorMessage, terminal, release);

            return ABORTED;
        }

        logger.debug("release support model=" + model.getModelName());

        VehicleGroup vehicleGroup = (VehicleGroup) context.get(ContextUtils.Item.VEHICLE_GROUP);

        if (vehicleGroup == null) {
            String errorMessage = "vehicle (" + terminal.getVehicleNo() + ") does not belong to any group";
            handleError(context, TxnResult.ERROR_NO_GROUP, errorMessage, terminal, release);

            return ABORTED;
        }

        logger.debug("vehicleGroup=" + vehicleGroup);

        ApplicationDownloadJobService jobService = SpringUtils.getBean(ApplicationDownloadJobService.class);

        Group group = vehicleGroup.getGroup();
        logger.debug("group=" + group);

        List<ApplicationDownloadJob> allActiveJobs = jobService.getApplicationDownloadJobs(group.getId(), ApplicationDownloadJob.ACTIVE);
        if (allActiveJobs == null || allActiveJobs.size() <= 0) {
            logger.error("[" + terminal.getVehicleNo() + "] " + "no active jobs for this group (" + group.getName() + ")");
            ContextUtils.putIfDoesNotExist(context, ContextUtils.Item.RESULT_CODE, TxnResult.ERROR_JOB_SUSPENDED);
            return ABORTED;
        }

        logger.debug("allActiveJobs.size()=" + allActiveJobs.size());

        List<ApplicationDownloadJob> currentJobs = jobService.getCurrentJobs(group.getId());
        if (!checkJobWindowAvailable(currentJobs, allActiveJobs, context, terminal, group))
            return ABORTED;

        logger.debug("currentJobs.size()=" + currentJobs.size());

        ArrayList<ApplicationDownloadJob> currentActiveJobs = updateActiveJobs(currentJobs);

        if (currentActiveJobs.size() <= 0) {
            logger.error("[" + terminal.getVehicleNo() + "] " + "all jobs are either cancelled/suspended or not yet reach start date for this group (" + group.getName() + ")");
            ContextUtils.putIfDoesNotExist(context, ContextUtils.Item.RESULT_CODE, TxnResult.ERROR_JOB_SUSPENDED);
            return ABORTED;
        }

        // 2013-04-19, ky, get the job the terminal is using if any
        TerminalApplicationDownloadJobService terminalJobService = SpringUtils.getBean(TerminalApplicationDownloadJobService.class);
        TerminalApplicationDownloadJob terminalInProgressJob = terminalJobService.findTerminalApplicationDownloadJob(
                group.getId(),
                terminal.getId(),
                terminal.getVehicleNo(),
                release.getId());

        logger.debug("terminalInProgressJob=" + terminalInProgressJob);

        VehicleService vehicleService = SpringUtils.getBean(VehicleService.class);
        ApplicationDownloadJob applicationDownloadJob = getApplicationDownloadJob(terminalInProgressJob, terminal, context,
                group,terminalJobService, currentActiveJobs);

        if (applicationDownloadJob==null) {
            logger.error("Unable to get application download job");
            return ABORTED;
        }

        int fileNo = Integer.parseInt(request.getString(61));
        ReleaseFileService releaseFileService = SpringUtils.getBean(ReleaseFileService.class);
        ReleaseFile releaseFile = releaseFileService.getReleaseFile(release.getId(), fileNo);
        if (releaseFile == null) {
            String errorMessage = "release file for release version (" + fileVersion + "), file number (" + fileNo + ") is not found";
            handleError(context, TxnResult.ERROR_INV_FILE_NO, errorMessage, terminal, release);

            return ABORTED;
        }
        logger.debug("releaseFile=" + releaseFile);

        byte[] resField48 = ByteUtils.hexToBytes(releaseFile.getData());
        int nextRecordNo = releaseFileService.getNextRecordNo(release.getId(), fileNo);
        logger.debug("nextRecordNo=" + nextRecordNo);

        TerminalApplicationDownloadJobId termDownloadJobId = new TerminalApplicationDownloadJobId();
        termDownloadJobId.setApplicationDownloadJob(applicationDownloadJob);
        termDownloadJobId.setTmsTerminal(terminal);
        termDownloadJobId.setVehicle(vehicleService.getVehicle(terminal.getVehicleNo()));
        termDownloadJobId.setRelease(release);

        TerminalApplicationDownloadJob termDownloadJob = null;
        if (terminalInProgressJob != null)
            termDownloadJob = terminalInProgressJob;
        else {
            termDownloadJob = terminalJobService.getByPk(termDownloadJobId);
            logger.debug("termDownloadJob=" + termDownloadJob);
        }

        Date now = new Date();
        if (termDownloadJob == null) {
            termDownloadJob = new TerminalApplicationDownloadJob();
            termDownloadJob.setPk(termDownloadJobId);
            termDownloadJob.setStatus(TerminalApplicationDownloadJob.IN_PROGRESS);
            termDownloadJob.setRecordNo(fileNo);
            termDownloadJob.setUpdateDateTime(now);
        } else {
            termDownloadJob.setStatus(TerminalApplicationDownloadJob.IN_PROGRESS);
            termDownloadJob.setRecordNo(fileNo);
            termDownloadJob.setUpdateDateTime(now);
        }

        if (fileNo == 1) {
            termDownloadJob.setFirstRequestDateTime(now);
        }
        if (nextRecordNo == 0) {
            termDownloadJob.setLastRequestDateTime(now);
        }

        terminalJobService.save(termDownloadJob);
        logger.debug("termDownloadJob saved");

        // 2013-04-18, ky, release other jobs so 1 terminal will not take up 2 different jobs at the same time
        int released = terminalJobService.releaseOtherJobs(termDownloadJob);
        logger.debug("released=" + released);
        if (released > 0) {
            logger.info("Released " + released + " other jobs for this terminal, this application version");
        }

        // soft delete the failed report for the vehicle and terminal if any
        FailedDownloadService failedDownloadService = SpringUtils.getBean(FailedDownloadService.class);
        List<FailedDownload> failedDownloads = failedDownloadService.getFailedDownload(terminal.getVehicleNo(), terminal.getSerialNo());
        logger.debug("failedDownloads=" + failedDownloads);
        for (FailedDownload failedDownload : failedDownloads) {
            failedDownload.setDeleted(true);
            failedDownloadService.save(failedDownload);
        }

        ISOMsg response = (ISOMsg) context.get(ContextUtils.Item.RESPONSE);
        try {
            response.set(48, resField48);
            response.set(61, StringUtils.padLeftToLen(Integer.toString(nextRecordNo), '0', 6));
        } catch (ISOException e) {

            String errorMessage = "Download application system error: " + e.getMessage();
            handleError(context, TxnResult.ERROR, errorMessage, terminal, release);

            return ABORTED;
        }

        logger.debug("return PREPARED");
        return PREPARED;
    }

    private void handleError(Context context, String errorCode, String errorMessage, TmsTerminal terminal, Release downloadRelease) {
        FailedDownloadService failedDownloadService = SpringUtils.getBean(FailedDownloadService.class);
        TerminalApplicationService terminalApplicationService = SpringUtils.getBean(TerminalApplicationService.class);

        logger.error("[" + terminal.getVehicleNo() + "] " + errorMessage);
        ContextUtils.putIfDoesNotExist(context, ContextUtils.Item.RESULT_CODE, errorCode);

        Release currentRelease = terminalApplicationService.getLatestRelease(terminal);

        FailedDownload failedDownload = new FailedDownload();
        failedDownload.setUpdateDateTime(new Date());
        failedDownload.setSerialNo(terminal.getSerialNo());
        failedDownload.setVehicleNo(terminal.getVehicleNo());
        failedDownload.setFailedReason(errorMessage);
        if (currentRelease != null) failedDownload.setCurrentRelease(currentRelease);
        failedDownload.setDownloadRelease(downloadRelease);
        failedDownload.setDeleted(false);
        failedDownloadService.save(failedDownload);
    }

    private ArrayList<ApplicationDownloadJob> updateActiveJobs( List<ApplicationDownloadJob> currentJobs) {
        ArrayList<ApplicationDownloadJob> currentActiveJobs = new ArrayList<ApplicationDownloadJob>();
        for (ApplicationDownloadJob currentJob : currentJobs) {
            if (currentJob.getStatus() == ApplicationDownloadJob.ACTIVE) {
                ApplicationDownloadJobInfo jobInfo=currentJob.getApplicationDownloadJobInfo();

                if (jobInfo!=null) {
                    Date currentDate=new Date();
                    Date startDate=jobInfo.getStartDate();
                    if (startDate!=null) {
                        if (currentDate.after(startDate)) {
                            currentActiveJobs.add(currentJob);
                        }
                    } else { // when there is no start date defined, as per current implementation
                        currentActiveJobs.add(currentJob);
                    }
                } else {
                    currentActiveJobs.add(currentJob);
                }
            }
        }
        return currentActiveJobs;
    }

    private ApplicationDownloadJob getApplicationDownloadJob(TerminalApplicationDownloadJob terminalInProgressJob, TmsTerminal terminal,
                                                             Context context,Group group,TerminalApplicationDownloadJobService terminalJobService,
                                                             ArrayList<ApplicationDownloadJob> currentActiveJobs) {
        ApplicationDownloadJob applicationDownloadJob = null;

        if (terminalInProgressJob == null) {

            ArrayList<ApplicationDownloadJob> currentAvailableJobs = new ArrayList<ApplicationDownloadJob>();
            for (ApplicationDownloadJob job : currentActiveJobs) {
                int inProgressTerminals = terminalJobService.countTerminalApplicationDownloadJobs(job.getId(), TerminalApplicationDownloadJob.IN_PROGRESS, terminal.getId());
                logger.debug("inProgressTerminals=" + inProgressTerminals);
                if (inProgressTerminals < job.getConcurrentDownload()) {
                    currentAvailableJobs.add(job);
                }
            }

            if (currentAvailableJobs.size() <= 0) {
                logger.error("[" + terminal.getVehicleNo() + "] " + "all jobs are busy for this group (" + group.getName() + ")");
                ContextUtils.putIfDoesNotExist(context, ContextUtils.Item.RESULT_CODE, TxnResult.ERROR_JOB_SUSPENDED);

                return null;
            }
            applicationDownloadJob = currentAvailableJobs.get(0);
        } else {
            applicationDownloadJob = terminalInProgressJob.getPk().getApplicationDownloadJob();
        }
        return applicationDownloadJob;
    }

    private boolean checkJobWindowAvailable(List<ApplicationDownloadJob> currentJobs, List<ApplicationDownloadJob> allActiveJobs, Context context, TmsTerminal terminal, Group group ) {
        if (currentJobs == null || currentJobs.size() <= 0) {

            ArrayList<Date> nextTimes = new ArrayList<Date>();
            for (ApplicationDownloadJob activeJob : allActiveJobs) {
                ApplicationDownloadJobInfo jobInfo=activeJob.getApplicationDownloadJobInfo();
                Date startDate=new Date();
                if (jobInfo!=null) {
                    if (jobInfo.getStartDate()!=null && jobInfo.getStartDate().after(startDate)) {
                        startDate=jobInfo.getStartDate();
                    }
                }
                nextTimes.add(DateUtils.getNextTime(startDate, activeJob.getStartWindow()));
                Collections.sort(nextTimes);
            }

            ISOMsg response = (ISOMsg) context.get(ContextUtils.Item.RESPONSE);
            try {
                response.set(60, DateUtils.format(nextTimes.get(0), "yyyyMMddHHmm"));
            } catch (ISOException e) {
                logger.error("[" + terminal.getVehicleNo() + "] " + "download application error", e);
                ContextUtils.putIfDoesNotExist(context, ContextUtils.Item.RESULT_CODE, TxnResult.ERROR);
                return false;
            }

            logger.error("[" + terminal.getVehicleNo() + "] " + "no job found for this window for this group (" + group.getName() + ")");
            ContextUtils.putIfDoesNotExist(context, ContextUtils.Item.RESULT_CODE, TxnResult.ERROR_OUT_OF_TIME_WINDOW);
            return false;
        }
        return true;
    }
}
