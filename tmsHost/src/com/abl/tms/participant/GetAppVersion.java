package com.abl.tms.participant;

import com.abl.db.model.*;
import com.abl.db.service.*;
import com.abl.tms.utils.ContextUtils;
import com.abl.tms.utils.ContextUtils.Item;
import com.abl.utils.ByteUtils;
import com.abl.utils.DateUtils;
import com.abl.utils.SpringUtils;
import com.abl.utils.StringUtils;
import org.apache.log4j.Logger;
import org.jpos.iso.ISOException;
import org.jpos.iso.ISOMsg;
import org.jpos.transaction.Context;

import java.util.Date;
import java.util.List;

public class GetAppVersion extends AbstractParticipant {

    private static final Logger logger = Logger.getLogger(GetAppVersion.class);

    @Override
    public void abort(long id, Context context) {
        logger.error("abort");
    }

    @Override
    public void commit(long id, Context context) {
        logger.debug("commit");
    }

    @Override
    public int prepare(long id, Context context) {
        logger.debug("prepare");

        TmsTerminal terminal = (TmsTerminal) context.get(Item.TERMINAL);
        ISOMsg request = (ISOMsg) context.get(Item.REQUEST);
        byte[] reqField48 = request.getBytes(48);
        logger.debug("reqField48=" + new String(reqField48));

        byte[] releaseVersionBytes = new byte[ParseAppVersion.RELEASE_VERSION_LENGTH];
        System.arraycopy(reqField48, 0, releaseVersionBytes, 0, ParseAppVersion.RELEASE_VERSION_LENGTH);
        String releaseVersion = new String(releaseVersionBytes);
        logger.debug("releaseVersion=" + releaseVersion);

        ReleaseService releaseService = SpringUtils.getBean(ReleaseService.class);
        Release currentRelease = releaseService.getReleaseByVersion(Integer.parseInt(releaseVersion));

        processCurrentRelease(currentRelease, reqField48, terminal);

        // update the terminal application download job to complete for same release, same terminal, same vehicle
        TerminalApplicationDownloadJobService terminalApplicationDownloadJobService = SpringUtils.getBean(TerminalApplicationDownloadJobService.class);

        updateTerminalJobSuccess(terminalApplicationDownloadJobService, terminal, releaseVersion);

        VehicleGroup vehicleGroup = (VehicleGroup) context.get(ContextUtils.Item.VEHICLE_GROUP);

        if (vehicleGroup == null) {
            String errorMessage = "vehicle does not belong to any group";
            handleError(context, TxnResult.ERROR_NO_GROUP, errorMessage, terminal, currentRelease, null);

            return ABORTED;
        }

        Release release = vehicleGroup.getGroup().getRelease();
        if (release == null) {
            String errorMessage = "vehicle's group (" + vehicleGroup.getGroup().getName() + ") does not have any release assigned";
            handleError(context, TxnResult.ERROR_NO_DOWNLOAD, errorMessage, terminal, currentRelease, release);

            return ABORTED;
        }

        if (!releaseService.hasModel(release.getId(), terminal.getModel())) {
            String errorMessage = "vehicle's group assigned release (" + release.getDescription() + ") does not support this vehicle model (" + terminal.getModel().getModelName() + ")";
            handleError(context, TxnResult.ERROR_INV_REQ_DATA, errorMessage, terminal, currentRelease, release);

            return ABORTED;
        }

        if (release.getMinVersion() > 0 && release.getMinVersion() > Integer.parseInt(releaseVersion.trim())) {
            String errorMessage = "release minimum version (" + release.getMinVersion() + ") is not met, current version is " + Integer.parseInt(releaseVersion.trim());
            handleError(context, TxnResult.ERROR_MIN_VERSION_NOT_MET, errorMessage, terminal, currentRelease, release);

            return ABORTED;
        }

        if (Integer.parseInt(releaseVersion) == release.getVersion()) {
            // no download required as terminal already having the latest release version
            // so soft delete the failed report for the vehicle and terminal if any
            FailedDownloadService failedDownloadService = SpringUtils.getBean(FailedDownloadService.class);
            List<FailedDownload> failedDownloads = failedDownloadService.getFailedDownload(terminal.getVehicleNo(), terminal.getSerialNo());
            for (FailedDownload failedDownload : failedDownloads) {
                failedDownload.setDeleted(true);
                failedDownloadService.save(failedDownload);
            }
        }

        // all other in progress terminal application download job tied to this vehicle number will be set to failed
        // all other in progress terminal application download job tied to this terminal serial number will also be set to failed
        updateTerminalJobFailed(terminalApplicationDownloadJobService, terminal, release);

        StringBuffer resField48 = new StringBuffer();
        resField48.append(StringUtils.padLeftToLen(Integer.toString(release.getVersion()), '0', 6));

        for (Application application : release.getApplication()) {

            resField48.append(new String(ByteUtils.padRightToLen(application.getName().getBytes(), (byte) 0, 10)));

            resField48.append(StringUtils.padLeftToLen(Integer.toString(application.getVersion()), '0', 6));
        }

        ISOMsg response = (ISOMsg) context.get(Item.RESPONSE);
        try {
            response.set(12, DateUtils.format(new Date(), "HHmmss"));
            response.set(13, DateUtils.format(new Date(), "MMdd"));
            response.set(48, resField48.toString().getBytes());
        } catch (ISOException e) {

            String errorMessage = "Get application version system error: " + e.getMessage();
            handleError(context, TxnResult.ERROR, errorMessage, terminal, currentRelease, release);

            return ABORTED;
        }

        return PREPARED;
    }

    private void handleError(Context context, String errorCode, String errorMessage, TmsTerminal terminal, Release currentRelease, Release downloadRelease) {
        FailedDownloadService failedDownloadService = SpringUtils.getBean(FailedDownloadService.class);

        logger.error("[" + terminal.getVehicleNo() + "] " + errorMessage);
        ContextUtils.putIfDoesNotExist(context, Item.RESULT_CODE, errorCode);

        FailedDownload failedDownload = new FailedDownload();
        failedDownload.setUpdateDateTime(new Date());
        failedDownload.setSerialNo(terminal.getSerialNo());
        failedDownload.setVehicleNo(terminal.getVehicleNo());
        failedDownload.setFailedReason(errorMessage);
        if (currentRelease != null) failedDownload.setCurrentRelease(currentRelease);
        failedDownload.setDownloadRelease(downloadRelease);
        failedDownload.setDeleted(false);
        failedDownloadService.save(failedDownload);
    }

    private void processCurrentRelease(Release currentRelease, byte reqField48[], TmsTerminal terminal) {
        if (currentRelease != null) {
            ApplicationService applicationService = SpringUtils.getBean(ApplicationService.class);
            TerminalApplicationService terminalApplicationService = SpringUtils.getBean(TerminalApplicationService.class);
            for (int i = 0; i < ((reqField48.length - ParseAppVersion.RELEASE_VERSION_LENGTH) / ParseAppVersion.APP_VERSION_LENGTH); i++) {
                byte[] versionBytes = new byte[ParseAppVersion.APP_VERSION_LENGTH];
                System.arraycopy(reqField48, ParseAppVersion.RELEASE_VERSION_LENGTH + (i * ParseAppVersion.APP_VERSION_LENGTH), versionBytes, 0, ParseAppVersion.APP_VERSION_LENGTH);
                String versionStr = new String(versionBytes);
                logger.debug("versionStr=" + versionStr);
                String appName = versionStr.substring(0, 10);
                logger.debug("appName=" + appName);
                String appVersion = versionStr.substring(10, 16);
                logger.debug("appVersion=" + appVersion);

                List<Application> currentApplications = applicationService.searchApplicationByNameVersion(appName.trim(), Integer.parseInt(appVersion.trim()));
                if (currentApplications != null && currentApplications.size() > 0) {
                    // log the application terminal is holding
                    Application currentApplication = currentApplications.get(0);
                    TerminalApplicationId termAppId = new TerminalApplicationId();
                    termAppId.setApplication(currentApplication);
                    termAppId.setTmsTerminal(terminal);
                    termAppId.setRelease(currentRelease);
                    TerminalApplication terminalApplication = new TerminalApplication();
                    terminalApplication.setPk(termAppId);
                    terminalApplication.setUpdateDateTime(new Date());
                    terminalApplicationService.save(terminalApplication);
                }
            }
        }
    }

    private void updateTerminalJobSuccess(TerminalApplicationDownloadJobService terminalApplicationDownloadJobService, TmsTerminal terminal, String releaseVersion ) {
        List<TerminalApplicationDownloadJob> termDownloadJobs = terminalApplicationDownloadJobService.getNotSuccessTerminalApplicationDownloadJobs(
                Integer.parseInt(releaseVersion.trim()), terminal.getId(), terminal.getVehicleNo());
        if (termDownloadJobs != null && termDownloadJobs.size() > 0) {
            for (TerminalApplicationDownloadJob termDownloadJob : termDownloadJobs) {
                if (termDownloadJob.getStatus() != TerminalApplicationDownloadJob.SUCCESS) {
                    termDownloadJob.setStatus(TerminalApplicationDownloadJob.SUCCESS);
                    termDownloadJob.setUpdateDateTime(new Date());
                    terminalApplicationDownloadJobService.save(termDownloadJob);
                }
            }
        }
    }

    private void updateTerminalJobFailed(TerminalApplicationDownloadJobService terminalApplicationDownloadJobService, TmsTerminal terminal, Release release) {
        List<TerminalApplicationDownloadJob>termDownloadJobs = terminalApplicationDownloadJobService.getInProgressTerminalApplicationDownloadJobs(terminal.getVehicleNo());
        if (termDownloadJobs != null && termDownloadJobs.size() > 0) {
            for (TerminalApplicationDownloadJob termDownloadJob : termDownloadJobs) {
                // 2013-06-28, ky, only set to FAILED when it's not from the same terminal or not the same release
                if (termDownloadJob.getPk().getTmsTerminal().getId() != terminal.getId() ||
                        termDownloadJob.getPk().getRelease().getId() != release.getId()) {
                    if (termDownloadJob.getStatus() != TerminalApplicationDownloadJob.FAIL) {
                        logger.debug("termDownloadJob.getPk().getTmsTerminal().getId()=" + termDownloadJob.getPk().getTmsTerminal().getId());
                        logger.debug("terminal.getId()=" + terminal.getId());
                        logger.debug("termDownloadJob.getPk().getRelease().getId()=" + termDownloadJob.getPk().getRelease().getId());
                        logger.debug("release.getId()=" + release.getId());
                        termDownloadJob.setStatus(TerminalApplicationDownloadJob.FAIL);
                        termDownloadJob.setUpdateDateTime(new Date());
                        terminalApplicationDownloadJobService.save(termDownloadJob);
                    }
                }
            }
        }

        termDownloadJobs = terminalApplicationDownloadJobService.getInProgressTerminalApplicationDownloadJobs(terminal.getId());
        if (termDownloadJobs != null && termDownloadJobs.size() > 0) {
            for (TerminalApplicationDownloadJob termDownloadJob : termDownloadJobs) {
                // 2013-06-28, ky, only set to FAILED when it's not from the same vehicle or not the same release
                if ((!termDownloadJob.getPk().getVehicle().getVehicleId().equalsIgnoreCase(terminal.getVehicleNo())) ||
                        termDownloadJob.getPk().getRelease().getId() != release.getId()) {
                    if (termDownloadJob.getStatus() != TerminalApplicationDownloadJob.FAIL) {
                        logger.debug("termDownloadJob.getPk().getVehicle().getVehicleId()=" + termDownloadJob.getPk().getVehicle().getVehicleId());
                        logger.debug("terminal.getVehicleNo()=" + terminal.getVehicleNo());
                        logger.debug("termDownloadJob.getPk().getRelease().getId()=" + termDownloadJob.getPk().getRelease().getId());
                        logger.debug("release.getId()=" + release.getId());
                        termDownloadJob.setStatus(TerminalApplicationDownloadJob.FAIL);
                        termDownloadJob.setUpdateDateTime(new Date());
                        terminalApplicationDownloadJobService.save(termDownloadJob);
                    }
                }
            }
        }

    }
}
