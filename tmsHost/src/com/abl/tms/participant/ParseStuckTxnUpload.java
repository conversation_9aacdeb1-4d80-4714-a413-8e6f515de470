package com.abl.tms.participant;

import com.abl.db.model.TxnType;
import com.abl.db.service.TxnTypeService;
import com.abl.tms.TxnDetails;
import com.abl.utils.SpringUtils;
import org.apache.log4j.Logger;
import org.jpos.iso.ISOMsg;

public class ParseStuckTxnUpload extends ParseTxn {

    private static final Logger logger = Logger.getLogger(ParseStuckTxnUpload.class);
    public static final int TERMINAL_INFO_LENGTH = 60;

    @Override
    protected boolean parseRequest(TxnDetails txnDetails, ISOMsg request) {
        super.parseRequest(txnDetails, request);

        String taxiNumber = "";
        try {
            taxiNumber = getStringField(request, 63).substring(40, 60).trim();
        } catch (Exception e) {
            logger.error("Vehicle number cannot be parsed successfully");
        }

        TxnTypeService txnTypeService = SpringUtils.getBean(TxnTypeService.class);
        TxnType txnType = txnTypeService.getTxnType(TxnType.TYPE_UPLOAD_STUCK_TXN);
        txnDetails.setTxnType(txnType);

        String field61 = request.getString(61);
        byte[] field48 = request.getBytes(48);
        if (field61.equals("000000")) {
            if (field48.length % 97 != 0) {
                logger.error("[" + taxiNumber + "] " + "field 48 has invalid length: " + field48.length);
                return false;
            }
        } else if (field61.equals("000001")) {
            if (field48.length % 7 != 0) {
                logger.error("[" + taxiNumber + "] " + "field 48 has invalid length: " + field48.length);
                return false;
            }
        } else {
            logger.error("[" + taxiNumber + "] " + "field 61 invalid " + field61);
            return false;
        }

        byte[] field63 = request.getBytes(63);
        if (field63 == null || field63.length != TERMINAL_INFO_LENGTH) {
            logger.error("[" + taxiNumber + "] " + "field 63 has invalid length: " + (field63 == null ? 0 : field63.length));
            return false;
        }
        txnDetails.setTermSerialNo(getStringField(request, 63).substring(0, 20).trim());
        txnDetails.setModelName(getStringField(request, 63).substring(20, 40).trim());
        txnDetails.setTaxiNumber(taxiNumber);

        return true;
    }
}
