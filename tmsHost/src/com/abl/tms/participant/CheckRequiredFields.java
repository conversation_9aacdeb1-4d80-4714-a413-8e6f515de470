package com.abl.tms.participant;

import com.abl.db.model.TxnResult;
import com.abl.tms.utils.ContextUtils;
import com.abl.tms.utils.ContextUtils.Item;
import org.apache.log4j.Logger;
import org.jpos.core.Configuration;
import org.jpos.core.ConfigurationException;
import org.jpos.iso.ISOMsg;
import org.jpos.transaction.Context;

public class CheckRequiredFields extends AbstractParticipant {

    private static final Logger logger = Logger.getLogger(CheckRequiredFields.class);

    private int[] fields;

    @Override
    public void setConfiguration(Configuration cfg)
            throws ConfigurationException {

        super.setConfiguration(cfg);

        String cfgFields = cfg.get("fields");
        String[] cfgFieldsArray = cfgFields.split(",");
        if (cfgFieldsArray != null) {
            fields = new int[cfgFieldsArray.length];
            for (int i = 0; i < cfgFieldsArray.length; i++) {
                fields[i] = Integer.parseInt(cfgFieldsArray[i]);
            }
        }
    }

    @Override
    public void abort(long id, Context context) {
        logger.error("abort");
    }

    @Override
    public void commit(long id, Context context) {
        logger.debug("commit");
    }

    @Override
    public int prepare(long id, Context context) {
        logger.debug("prepare");

        ISOMsg request = (ISOMsg) context.get(Item.REQUEST);
        for (int field : fields) {
            if (!request.hasField(field)) {
                logger.error("required field " + field + " missing");
                ContextUtils.putIfDoesNotExist(context, Item.RESULT_CODE, TxnResult.ERROR_FORMAT_ERROR);
                return ABORTED;
            }
        }

        return PREPARED;
    }

}
