package com.abl.tms.participant;

import com.abl.db.model.TxnType;
import com.abl.db.service.TxnTypeService;
import com.abl.tms.TxnDetails;
import com.abl.utils.DateUtils;
import com.abl.utils.SpringUtils;
import org.apache.log4j.Logger;
import org.jpos.iso.ISOMsg;

import java.util.Date;

public class ParseLogUpload extends ParseTxn {
    private static final Logger logger = Logger.getLogger(ParseLogUpload.class);
    private static final int FIELD_61_LENGTH = 14;
    public static final int TERMINAL_INFO_LENGTH = 60;

    @Override
    protected boolean parseRequest(TxnDetails txnDetails, ISOMsg request) {
        super.parseRequest(txnDetails, request);

        String taxiNumber = "";
        try {
            taxiNumber = getStringField(request, 63).substring(40, 60).trim();
        } catch (Exception e) {
            logger.error("Vehicle number cannot be parsed successfully");
        }

        TxnTypeService txnTypeService = SpringUtils.getBean(TxnTypeService.class);
        TxnType txnType = txnTypeService.getTxnType(TxnType.TYPE_LOG_UPLOAD);
        txnDetails.setTxnType(txnType);

        byte[] field48 = request.getBytes(48);
        if (field48.length < 1) {
            logger.error("[" + taxiNumber + "] " + "field 48 has invalid length: " + field48.length);
            return false;
        }

        byte[] field61 = request.getBytes(61);
        if (field61.length != FIELD_61_LENGTH) {
            logger.error("[" + taxiNumber + "] " + "field 61 has invalid length: " + field61.length);
            return false;
        }

        String field61Str = new String(field61);
        Date logDate = DateUtils.parse(field61Str.substring(0, 8), "yyyyMMdd");
        if (logDate == null) {
            logger.error("[" + taxiNumber + "] " + "field 61 has invalid value: " + field61Str);
            return false;
        }

        try {
            Integer.parseInt(field61Str.substring(8));
        } catch (Exception e) {
            logger.error("[" + taxiNumber + "] " + "field 61 has invalid value: " + field61Str);
            return false;
        }

        byte[] field63 = request.getBytes(63);
        if (field63 == null || field63.length != TERMINAL_INFO_LENGTH) {
            logger.error("[" + taxiNumber + "] " + "field 63 has invalid length: " + (field63 == null ? 0 : field63.length));
            return false;
        }
        txnDetails.setTermSerialNo(getStringField(request, 63).substring(0, 20).trim());
        txnDetails.setModelName(getStringField(request, 63).substring(20, 40).trim());
        txnDetails.setTaxiNumber(taxiNumber);

        checkStan(txnDetails);

        return true;
    }
}
