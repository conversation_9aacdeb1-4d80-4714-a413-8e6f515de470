/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */

package com.abl.tms.participant;

import com.abl.db.model.Model;
import com.abl.db.model.TmsTerminal;
import org.jpos.transaction.Context;
import org.jpos.iso.ISOMsg;
import org.jpos.iso.ISOException;
import org.apache.log4j.Logger;
import com.abl.db.model.TxnResult;
import com.abl.db.service.ModelService;
import com.abl.db.service.TmsTerminalService;
import com.abl.tms.TxnDetails;
import com.abl.tms.utils.ContextUtils.Item;
import com.abl.tms.utils.ContextUtils;
import com.abl.utils.SpringUtils;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class GetTerminalActionCode extends AbstractParticipant{

    private static final Logger logger = Logger.getLogger(GetParamVersion.class);

    @Override
    public void abort(long id, Context context) {
        logger.error("abort");
    }

    @Override
    public void commit(long id, Context context) {
        logger.debug("commit");
    }

    @Override
    public int prepare(long id, Context context) {
        logger.debug("prepare");
        
        TmsTerminal terminal = (TmsTerminal)ContextUtils.get(context, Item.TERMINAL);
        if (terminal==null){
            logger.error("TMS logon error: no terminal found");
            ContextUtils.putIfDoesNotExist(context, Item.RESULT_CODE, TxnResult.ERROR);
            return ABORTED;
        }

        ISOMsg response = (ISOMsg) context.get(Item.RESPONSE);
        StringBuilder terminalActionCode = new StringBuilder("00000000");
        if (terminal.isUploadLog())
            terminalActionCode.setCharAt(0, '1');
        if (terminal.isDisabledLogging())
            terminalActionCode.setCharAt(0, '1');
        try {
            response.set(60, terminalActionCode.toString());
        } catch (ISOException e) {
            logger.error("[" + terminal.getVehicleNo() + "] " + "TMS Logon error: " + e);
            ContextUtils.putIfDoesNotExist(context, Item.RESULT_CODE, TxnResult.ERROR);
            return ABORTED;
        }
        return PREPARED;
    }

}
