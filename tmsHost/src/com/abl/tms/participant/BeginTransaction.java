package com.abl.tms.participant;

import com.abl.tms.TxnDetails;
import com.abl.tms.utils.ContextUtils;
import com.abl.tms.utils.ContextUtils.Item;
import com.abl.utils.DateUtils;
import com.abl.utils.SpringUtils;
import org.apache.log4j.Logger;
import org.apache.log4j.MDC;
import org.jpos.transaction.Context;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.util.Date;

/**
 * Begins transaction
 * <p/>
 * This should be the first participant.
 */
public class BeginTransaction extends AbstractParticipant {

    private static final Logger logger = Logger.getLogger(BeginTransaction.class);

    @Override
    public void abort(long id, Context context) {
        logger.error("abort");
    }

    @Override
    public void commit(long id, Context context) {
        logger.debug("commit");
    }

    @Override
    public int prepare(long id, Context context) {
        // setup the log4j diagnostic context, so each logging message is tagged with a
        // different id to make it easier to trace
        MDC.put("id", id);

        logger.debug("prepare");

        TxnDetails txnDetails = new TxnDetails();
        ContextUtils.put(context, Item.TXN_DETAILS, txnDetails);

        // set tms date/time
        // note: millisec is set to 0 bec ISO msg date/time fields do not have millisec
        Date date = DateUtils.removeMillisec(new Date());
        txnDetails.setTxnDateTime(date);

        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        PlatformTransactionManager txnManager = (PlatformTransactionManager) SpringUtils.getBean(PlatformTransactionManager.class);
        TransactionStatus status = txnManager.getTransaction(def);
        ContextUtils.put(context, Item.SPRING_TXN_MGR, txnManager);
        ContextUtils.put(context, Item.SPRING_TXN_STATUS, status);

        return PREPARED | READONLY | NO_JOIN;
    }

}
