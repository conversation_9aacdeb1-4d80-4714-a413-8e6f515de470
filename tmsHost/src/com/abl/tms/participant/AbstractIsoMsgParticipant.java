package com.abl.tms.participant;

import com.abl.tms.TxnDetails;
import com.abl.tms.participant.Field.Type;
import com.abl.tms.utils.ContextUtils.Item;
import com.abl.utils.ByteUtils;
import com.abl.utils.DateUtils;
import org.apache.log4j.Logger;
import org.jpos.core.Configuration;
import org.jpos.core.ConfigurationException;
import org.jpos.iso.ISOException;
import org.jpos.iso.ISOMsg;
import org.jpos.transaction.Context;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public abstract class AbstractIsoMsgParticipant extends AbstractParticipant {

    private static final Logger logger = Logger.getLogger(AbstractIsoMsgParticipant.class);

    private List<Field> fieldList = new ArrayList<Field>();
    private Type defaultFieldType;

    public void setDefaultFieldType(Type defaultFieldType) {
        this.defaultFieldType = defaultFieldType;
    }
    public static String BINARY_STR="binary";

    @Override
    public void setConfiguration(Configuration cfg)
            throws ConfigurationException {

        super.setConfiguration(cfg);

        String[] fields = cfg.get("fields", "").split(",");

        for (String field : fields) {
            String fieldConfig = cfg.get(field, null);
            if (fieldConfig == null) {
                fieldList.add(new Field(Integer.parseInt(field), defaultFieldType, null));
            } else {
                String[] configs = fieldConfig.split(",", 2);

                Type fieldType;
                try {
                    fieldType = Type.valueOf(configs[0]);
                } catch (Exception e) {
                    logger.error("invalid field type " + configs[0], e);
                    break;
                }

                if (configs.length == 2) {
                    fieldList.add(new Field(Integer.parseInt(field), fieldType, configs[1]));
                } else {
                    fieldList.add(new Field(Integer.parseInt(field), fieldType, null));
                }
            }

        }
    }

    protected ISOMsg createResponse(ISOMsg request) throws ISOException {
        ISOMsg response = new ISOMsg();
        response.setMTI(request.getMTI());
        response.setHeader(request.getHeader());
        response.setPackager(request.getPackager());
        response.setDirection(request.getDirection());
        response.setResponseMTI();
        return response;
    }

    protected void prepareISOMsg(ISOMsg isoMsg, Context context) throws ISOException {

        ISOMsg request = (ISOMsg) context.get(Item.REQUEST);
        TxnDetails txnDetails = (TxnDetails) context.get(Item.TXN_DETAILS);

        for (Field field : fieldList) {

            int fieldNum = field.getFieldNum();
            logger.debug("fieldNum is " + fieldNum + ", type is " + field.getType());

            switch (field.getType()) {
                case fix:
                    if (field.getParam1().equals(BINARY_STR)) {
                        isoMsg.set(fieldNum, ByteUtils.hexToBytes(field.getParam2()));
                    } else {
                        isoMsg.set(fieldNum, field.getParam2());
                    }
                    break;

                case request:
                    Object obj;
                    if (field.getParam1() != null) {
                        obj = request.getValue(Integer.parseInt(field.getParam1()));
                    } else {
                        obj = request.getValue(field.getFieldNum());
                    }
                    if (obj instanceof String) {
                        logger.debug("request: string");
                        isoMsg.set(fieldNum, (String) obj);
                    } else {
                        logger.debug("request: not string");
                        isoMsg.set(fieldNum, (byte[]) obj);
                    }
                    break;

                case response:
                    // TODO: not implemented yet
                    break;

                case txnDate:
                    Date txnDate = txnDetails.getTxnDateTime();
                    isoMsg.set(fieldNum, DateUtils.format(txnDate, field.getParam1()));
                    break;
                default:
                    logger.warn("Default field");
                    break;

            }
        }
    }
}

class Field {

    public enum Type {
        fix,        // param contains the value
        request,    // copy value from request, param may contain request field num
        response,    // copy value from host response (param is "host:[field_no]")
        txnDate,    // copy value from tms date/time (param contains format)
        bizDate,    // copy value from business date (param contains format)
    }

    private int fieldNum;                // field number
    private Type type;
    private String param1;
    private String param2;

    public Field(int field, Type type, String params) {
        this.fieldNum = field;
        this.type = type;

        String[] paramsArray;

        switch (this.type) {
            case fix:
                paramsArray = params.split(",", 2);
                if (paramsArray.length == 2) {
                    if (paramsArray[0].equalsIgnoreCase(AbstractIsoMsgParticipant.BINARY_STR)) {
                        param1 = AbstractIsoMsgParticipant.BINARY_STR;
                        param2 = paramsArray[1];
                    } else {
                        param1 = "ascii";
                        param2 = paramsArray[1];
                    }
                } else {
                    param1 = "ascii";
                    param2 = paramsArray[0];
                }
                break;
            case request:
            case txnDate:
            case bizDate:
                param1 = params;
                break;
            case response:
                paramsArray = params.split(",", 2);
                if (paramsArray.length == 2) {
                    param1 = paramsArray[0];
                    param2 = paramsArray[1];
                } else {
                    param1 = paramsArray[0];
                }
        }
    }

    public int getFieldNum() {
        return fieldNum;
    }

    public void setFieldNum(int fieldNum) {
        this.fieldNum = fieldNum;
    }

    public Type getType() {
        return type;
    }

    public void setType(Type type) {
        this.type = type;
    }

    public String getParam1() {
        return param1;
    }

    public void setParam1(String param1) {
        this.param1 = param1;
    }

    public String getParam2() {
        return param2;
    }

    public void setParam2(String param2) {
        this.param2 = param2;
    }

}