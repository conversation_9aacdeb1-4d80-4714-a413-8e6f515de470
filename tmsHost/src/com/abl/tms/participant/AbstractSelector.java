package com.abl.tms.participant;

import org.jpos.core.Configurable;
import org.jpos.core.Configuration;
import org.jpos.core.ConfigurationException;
import org.jpos.transaction.Context;
import org.jpos.transaction.GroupSelector;

import java.io.Serializable;

public abstract class AbstractSelector implements GroupSelector, Configurable {

    private Configuration cfg;

    // These methods provide exact same functions as GroupSelector methods
    // of the same name, but "Serializable" is cast as "Context".
    public abstract String select(long id, Context context);

    public abstract void abort(long id, Context context);

    public abstract void commit(long id, Context context);

    public abstract int prepare(long id, Context context);

    public Configuration getConfiguration() {
        return cfg;
    }

    public void setConfiguration(Configuration cfg) throws ConfigurationException {
        this.cfg = cfg;
    }

    @Override
    public String select(long id, Serializable context) {
        return select(id, (Context) context);
    }

    @Override
    public void abort(long id, Serializable context) {
        abort(id, (Context) context);
    }

    @Override
    public void commit(long id, Serializable context) {
        commit(id, (Context) context);
    }

    @Override
    public int prepare(long id, Serializable context) {
        return prepare(id, (Context) context);
    }

}
