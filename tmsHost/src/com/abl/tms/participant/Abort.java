package com.abl.tms.participant;

import com.abl.db.model.TxnResult;
import com.abl.tms.utils.ContextUtils;
import com.abl.tms.utils.ContextUtils.Item;
import org.apache.log4j.Logger;
import org.jpos.transaction.Context;

/**
 * This participant simply performs abort
 */
public class Abort extends AbstractParticipant {

    private static final Logger logger = Logger.getLogger(Abort.class);

    @Override
    public void abort(long id, Context context) {
        logger.error("abort");
    }

    @Override
    public void commit(long id, Context context) {
        logger.debug("commit");
    }

    @Override
    public int prepare(long id, Context context) {
        logger.debug("prepare");
        ContextUtils.putIfDoesNotExist(context, Item.RESULT_CODE, TxnResult.ERROR);
        return ABORTED;
    }

}
