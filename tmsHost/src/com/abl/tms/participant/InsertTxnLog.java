package com.abl.tms.participant;

import com.abl.db.model.TxnLog;
import com.abl.db.model.TxnResult;
import com.abl.db.service.TxnLogService;
import com.abl.db.service.TxnResultService;
import com.abl.tms.TxnDetails;
import com.abl.tms.utils.ContextUtils;
import com.abl.tms.utils.ContextUtils.Item;
import com.abl.utils.SpringUtils;
import org.apache.log4j.Logger;
import org.jpos.iso.ISOMsg;
import org.jpos.transaction.Context;

public class InsertTxnLog extends AbstractParticipant {

    private static final Logger logger = Logger.getLogger(InsertTxnLog.class);

    @Override
    public void abort(long id, Context context) {
        logger.error("abort");
    }

    @Override
    public void commit(long id, Context context) {
        logger.debug("commit");
    }

    @Override
    public int prepare(long id, Context context) {
        logger.debug("prepare");

        try {
            // get txnlog updated with transaction data
            TxnLog txnLog = getFillTxnLog(context);

            // update result code
            String resultCode = (String) context.get(Item.RESULT_CODE);
            if (resultCode == null) {
                logger.debug("resultCode == null, setting resultCode to ERROR");
                resultCode = TxnResult.ERROR;
            }
            TxnResultService txnResultService = (TxnResultService) SpringUtils.getBean(TxnResultService.class);
            TxnResult txnResult = txnResultService.getTxnResult(resultCode);
            txnLog.setTxnResult(txnResult);

            // insert new record
            TxnLogService txnLogService = (TxnLogService) SpringUtils.getBean(TxnLogService.class);
            txnLogService.save(txnLog);

        } catch (Exception e) {
            logger.error("unable to save TxnLog", e);
            return ABORTED;
        }

        return PREPARED;
    }

    protected TxnLog getFillTxnLog(Context context) {

        TxnLog txnLog = new TxnLog();
        ContextUtils.put(context, Item.TXN_LOG, txnLog);

        // if txnDetails has data, fill in txnLog

        TxnDetails txnDetails = (TxnDetails) context.get(Item.TXN_DETAILS);
        txnLog.setMti(txnDetails.getMti());
        txnLog.setTxnDateTime(txnDetails.getTxnDateTime());
        txnLog.setProcCode(txnDetails.getProcCode());
        txnLog.setTid(txnDetails.getTid());
        txnLog.setTxnType(txnDetails.getTxnType());
        txnLog.setData(txnDetails.getData());

        ISOMsg response = (ISOMsg) context.get(Item.RESPONSE);
        if (response != null) {
            if (response.hasField(39)) {
                txnLog.setResponseCode(response.getString(39));
            }
        }

        return txnLog;
    }

}
