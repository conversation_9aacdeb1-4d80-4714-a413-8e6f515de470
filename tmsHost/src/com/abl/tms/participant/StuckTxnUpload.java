package com.abl.tms.participant;

import com.abl.db.bean.StuckTransactionSearchParam;
import com.abl.db.model.StuckTransaction;
import com.abl.db.model.TmsTerminal;
import com.abl.db.model.TxnResult;
import com.abl.db.service.StuckTransactionService;
import com.abl.db.service.TerminalLogRecordService;
import com.abl.tms.TxnDetails;
import com.abl.tms.utils.ContextUtils;
import com.abl.utils.DateUtils;
import com.abl.utils.SpringUtils;
import com.abl.utils.StringUtils;
import org.apache.log4j.Logger;
import org.jpos.iso.ISOException;
import org.jpos.iso.ISOMsg;
import org.jpos.transaction.Context;

import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class StuckTxnUpload extends AbstractParticipant {
    private static final Logger logger = Logger.getLogger(StuckTxnUpload.class);

    @Override
    public void abort(long id, Context context) {
        logger.error("abort");
    }

    @Override
    public void commit(long id, Context context) {
        logger.debug("commit");
    }

    @Override
    public int prepare(long id, Context context) {
        TxnDetails txnDetails = (TxnDetails) context.get(ContextUtils.Item.TXN_DETAILS);
        TmsTerminal terminal = (TmsTerminal) context.get(ContextUtils.Item.TERMINAL);
        ISOMsg request = (ISOMsg) context.get(ContextUtils.Item.REQUEST);

        String uploadUpdate=request.getString(61);
        logger.debug("Field 61="+uploadUpdate);

        byte[] reqField48 = request.getBytes(48);
        //logger.debug("reqField48=" + new String(reqField48));
        logger.debug("TmsTerminal="+terminal.getId()+" "+terminal.getVehicleNo()+" "+terminal.getSerialNo());
        try {
            if (uploadUpdate.contentEquals("000000")) {
                processUpload(reqField48, terminal);
            } else {
                processUpdate(reqField48, terminal);
            }
        } catch (ParseException e) {
            logger.error("[" + terminal.getVehicleNo() + "] " + "Stuck Txn Upload", e);
            ContextUtils.putIfDoesNotExist(context, ContextUtils.Item.RESULT_CODE, TxnResult.ERROR);
            return ABORTED;
        }

        ISOMsg response = (ISOMsg) context.get(ContextUtils.Item.RESPONSE);
        try {
            response.set(48, checkForAction(terminal));
        } catch (ISOException e) {
            logger.error("[" + terminal.getVehicleNo() + "] " + "Stuck Txn Upload", e);
            ContextUtils.putIfDoesNotExist(context, ContextUtils.Item.RESULT_CODE, TxnResult.ERROR);
            return ABORTED;
        }
        return PREPARED;
    }

    private static String TIMESTAMP_FORMAT="yyMMddHHmmss";

    private SimpleDateFormat timestampFormat = new SimpleDateFormat(TIMESTAMP_FORMAT);

    private boolean processUpload(byte data[], TmsTerminal terminal) throws ParseException {
        int len=data.length;
        int idx=0;
        Date uploadDate=new Date();

        StuckTransactionService stuckTransactionService = SpringUtils.getBean(StuckTransactionService.class);
        while (idx<len) {
            // if this transaction has previously been uploaded, ignore
            String ts=com.abl.utils.ByteUtils.bytesToHex(data,idx,6);
            Date tsDate=timestampFormat.parse(ts);

            if (stuckTransactionService.countStuckTransactionsByTerminalAndTimestamp(terminal, tsDate )>0) {
                idx+=97;
                continue;
            }
            StuckTransaction stuckTransaction=new StuckTransaction();
            stuckTransaction.setTxnDateTime(tsDate);

            idx+=6;
            byte tt[]=new byte[3];
            System.arraycopy(data,idx,tt,0,3);
            stuckTransaction.setTxnType(StuckTransaction.TxnType.valueOf(new String(tt)));

            idx+=3;
            String cardNo=String.format("%02X%02X%02X**%02X%02X",data[idx],data[idx+1],data[idx+2],
                    data[idx+3],data[idx+4]);
            stuckTransaction.setCardNo(cardNo);
            idx+=5;

            long fare=com.abl.utils.ByteUtils.bcdToLong(data,idx,3);
            stuckTransaction.setFare(fare);
            idx+=3;
            long gst=com.abl.utils.ByteUtils.bcdToLong(data,idx,3);
            stuckTransaction.setGst(gst);
            idx+=3;
            long admin=com.abl.utils.ByteUtils.bcdToLong(data,idx,3);
            stuckTransaction.setAdminFee(admin);
            idx+=3;
            long totalAmount=com.abl.utils.ByteUtils.bcdToLong(data,idx,6);
            stuckTransaction.setTxnAmt(totalAmount);
            idx+=6;
            byte rrnStr[]=new byte[20];
            System.arraycopy(data,idx,rrnStr, 0,20);
            //String rrn=com.abl.utils.ByteUtils.bytesToHex(data,idx,10);
            stuckTransaction.setRrn(new String(rrnStr).trim());
            idx+=20;
            byte jobNo[]=new byte[10];
            System.arraycopy(data,idx,jobNo,0,10); idx+=10;
            stuckTransaction.setJobNumber(new String(jobNo).trim());

            byte driverID[]=new byte[9];
            System.arraycopy(data,idx,driverID,0,9); idx+=9;
            stuckTransaction.setDriverId(new String(driverID).trim());
            byte mid[]=new byte[15];
            System.arraycopy(data,idx,mid,0,15); idx+=15;
            stuckTransaction.setMid(new String(mid).trim());
            byte tid[]=new byte[8];
            System.arraycopy(data,idx,tid,0,8); idx+=8;
            stuckTransaction.setTid(new String(tid).trim());
            byte relVer[]=new byte[6];
            System.arraycopy(data,idx,relVer,0,6); idx+=6;
            stuckTransaction.setReleaseVer(new String(relVer).trim());

            stuckTransaction.setTid(new String(tid).trim());
            stuckTransaction.setState(StuckTransaction.State.STUCK); // All new uploaded has state stuck
            stuckTransaction.setTmsTerminal(terminal);
            stuckTransaction.setUploadDateTime(uploadDate);
            stuckTransactionService.save(stuckTransaction);
        }

        return true;
    }

    private boolean processUpdate(byte data[], TmsTerminal terminal) throws ParseException {
        int len=data.length;
        int idx=0;
        StuckTransactionService stuckTransactionService = SpringUtils.getBean(StuckTransactionService.class);

        while (idx<len) {
            String ts=com.abl.utils.ByteUtils.bytesToHex(data,idx,6);
            Date tsDate=timestampFormat.parse(ts);
            // find the transaction
            StuckTransaction txn=stuckTransactionService.getStuckTransactionByTerminalAndTimestamp(terminal, tsDate);
            if (txn!=null) {
                if (data[idx+6]=='A') {
                    txn.setState(StuckTransaction.State.AUTO_CLEARED);
                    stuckTransactionService.save(txn);
                } else if (data[idx+6]=='D') {
                    txn.setState(StuckTransaction.State.CLEARED);
                    stuckTransactionService.save(txn);
                } else if (data[idx+6]=='N') {
                    txn.setState(StuckTransaction.State.NOT_FOUND);
                    stuckTransactionService.save(txn);
                } else {
                    logger.warn("Unknown state " + data[idx + 6] + " for " + terminal.getVehicleNo() + "/" + terminal.getSerialNo() + " ts:" + ts);
                }
            } else {
                logger.warn("Stuck transaction not found for "+terminal.getVehicleNo()+"/"+terminal.getSerialNo()+" ts:"+ts);
            }

            idx+=7;
        }

        return true;
    }

    private byte[] checkForAction(TmsTerminal terminal) {
        // find all the action that are required for this terminal, form the command string and send down
        // search criteria, admin not null, state is pending. max action to send per response = 22 records.
        StuckTransactionService stuckTransactionService = SpringUtils.getBean(StuckTransactionService.class);

        List<StuckTransaction> list=stuckTransactionService.getStuckTransactionsWithPendingActions(terminal, 22);

        byte b[]=new byte[list.size()*27];
        Iterator<StuckTransaction> e=list.iterator();
        int idx=0;
        while (e.hasNext()) {
            StuckTransaction s=e.next();
            String txnDate=timestampFormat.format(s.getTxnDateTime());

            byte d[]=com.abl.utils.ByteUtils.hexToBytes(txnDate);
            System.arraycopy(d,0,b,idx,6);
            b[idx+6]='D';

            String admin=s.getTriggerBy();
            if (admin.length()<20) {
                System.arraycopy(String.format("%1$20s", admin).getBytes(StandardCharsets.UTF_8), 0, b, idx+7, 20);
            } else {
                System.arraycopy(admin.substring(0,20).getBytes(StandardCharsets.UTF_8),0,b, idx+7,20);
            }
            idx+=27;
        }

        return b;
    }
}
