package com.abl.tms.participant;

import com.abl.db.model.*;
import com.abl.db.service.ApplicationService;
import com.abl.db.service.TerminalProfileFileService;
import com.abl.db.service.TerminalProfileService;
import com.abl.db.service.VehicleGroupService;
import com.abl.tms.utils.ContextUtils;
import com.abl.utils.ByteUtils;
import com.abl.utils.DateUtils;
import com.abl.utils.SpringUtils;
import com.abl.utils.StringUtils;
import org.apache.log4j.Logger;
import org.jpos.iso.ISOException;
import org.jpos.iso.ISOMsg;
import org.jpos.transaction.Context;

import java.util.Date;
import java.util.List;

public class DownloadParam extends AbstractParticipant {
    private static final Logger logger = Logger.getLogger(DownloadParam.class);

    @Override
    public void abort(long id, Context context) {
        logger.error("abort");
    }

    @Override
    public void commit(long id, Context context) {
        logger.debug("commit");
    }

    @Override
    public int prepare(long id, Context context) {
        logger.debug("prepare");

        TmsTerminal terminal = (TmsTerminal) context.get(ContextUtils.Item.TERMINAL);

        ISOMsg request = (ISOMsg) context.get(ContextUtils.Item.REQUEST);
        ApplicationService applicationService = SpringUtils.getBean(ApplicationService.class);
        TerminalProfileService terminalProfileService = SpringUtils.getBean(TerminalProfileService.class);
        byte[] reqField48 = request.getBytes(48);
        String versionStr = new String(reqField48);
        logger.debug("reqField48=" + versionStr);
        String appName = versionStr.substring(0, 10);
        logger.debug("appName=" + appName);
        String appVersion = versionStr.substring(10, 16);
        logger.debug("appVersion=" + appVersion);
        String profileId = versionStr.substring(16, 22);
        logger.debug("profileId=" + profileId);

        List<Application> applications = applicationService.searchApplicationByNameVersion(appName.trim(), Integer.parseInt(appVersion.trim()));
        if (applications == null || applications.size() <= 0) {
            logger.error("[" + terminal.getVehicleNo() + "] " + "application version (" + appName + "," + appVersion + ") not found");
            ContextUtils.putIfDoesNotExist(context, ContextUtils.Item.RESULT_CODE, TxnResult.ERROR_APPLICATION_NOT_FOUND);
            return ABORTED;
        }

//        VehicleGroupService vehicleGroupService = SpringUtils.getBean(VehicleGroupService.class);
//        VehicleGroup vehicleGroup = vehicleGroupService.getVehicleGroup(terminal.getVehicleNo());
        VehicleGroup vehicleGroup = (VehicleGroup) context.get(ContextUtils.Item.VEHICLE_GROUP);

        if (vehicleGroup == null) {
            logger.error("[" + terminal.getVehicleNo() + "] " + "vehicle does not belong to any group");
            ContextUtils.putIfDoesNotExist(context, ContextUtils.Item.RESULT_CODE, TxnResult.ERROR_NO_GROUP);
            return ABORTED;
        }

        Application application = applications.get(0);

        TerminalProfile terminalProfile = terminalProfileService.getTerminalProfile(new Long(profileId));

        if (terminalProfile == null) {
            logger.error("[" + terminal.getVehicleNo() + "] " + "profile id (" + profileId + ") not found ");
            ContextUtils.putIfDoesNotExist(context, ContextUtils.Item.RESULT_CODE, TxnResult.ERROR_PROFILE_NOT_VALID);
            return ABORTED;
        }

        if (terminalProfile.getApplication().getId() != application.getId()) {
            logger.error("[" + terminal.getVehicleNo() + "] " + "profile id (" + profileId + ") is not for this application (" + application.getName() + ")");
            ContextUtils.putIfDoesNotExist(context, ContextUtils.Item.RESULT_CODE, TxnResult.ERROR_PROFILE_NOT_VALID);
            return ABORTED;
        }

        if (terminalProfile.getGroup().getId().longValue() != vehicleGroup.getGroup().getId().longValue()) {
            logger.error("[" + terminal.getVehicleNo() + "] " + "profile id (" + profileId + ") is not for this group (" + vehicleGroup.getGroup().getId() + ")");
            ContextUtils.putIfDoesNotExist(context, ContextUtils.Item.RESULT_CODE, TxnResult.ERROR_PROFILE_NOT_VALID);
            return ABORTED;
        }

        if (terminalProfile.isDeleted()) {
            logger.error("[" + terminal.getVehicleNo() + "] " + "profile id (" + profileId + ") is deleted");
            ContextUtils.putIfDoesNotExist(context, ContextUtils.Item.RESULT_CODE, TxnResult.ERROR_PROFILE_NOT_VALID);
            return ABORTED;
        }

        if ((DateUtils.getStartOfDay(terminalProfile.getEffectiveDate())).after(new Date())) {
            logger.error("[" + terminal.getVehicleNo() + "] " + "profile id (" + profileId + ") is not effective yet, effective date is " + terminalProfile.getEffectiveDate());
            ContextUtils.putIfDoesNotExist(context, ContextUtils.Item.RESULT_CODE, TxnResult.ERROR_PROFILE_NOT_VALID);
            return ABORTED;
        }

        int fileNo = Integer.parseInt(request.getString(61));
        TerminalProfileFileService terminalProfileFileService = SpringUtils.getBean(TerminalProfileFileService.class);
        List<TerminalProfileFile> paramFiles = terminalProfileFileService.getTerminalProfileFile(terminalProfile.getId(), fileNo);
        if (paramFiles == null || paramFiles.size() <= 0) {
            logger.error("[" + terminal.getVehicleNo() + "] " + "parameter file for profile id (" + profileId + "), file number (" + fileNo + ") is not found");
            ContextUtils.putIfDoesNotExist(context, ContextUtils.Item.RESULT_CODE, TxnResult.ERROR_INV_FILE_NO);
            return ABORTED;
        }

        TerminalProfileFile paramFile = paramFiles.get(0);
        byte[] resField48 = ByteUtils.hexToBytes(paramFile.getData());
        int nextRecordNo = terminalProfileFileService.getNextRecordNo(terminalProfile.getId(), fileNo);

        ISOMsg response = (ISOMsg) context.get(ContextUtils.Item.RESPONSE);
        try {
            response.set(12, DateUtils.format(new Date(), "HHmmss"));
            response.set(13, DateUtils.format(new Date(), "MMdd"));
            response.set(48, resField48);
            response.set(61, StringUtils.padLeftToLen(Integer.toString(nextRecordNo), '0', 6));
        } catch (ISOException e) {
            logger.error("[" + terminal.getVehicleNo() + "] " + "download param error", e);
            ContextUtils.putIfDoesNotExist(context, ContextUtils.Item.RESULT_CODE, TxnResult.ERROR);
            return ABORTED;
        }

        return PREPARED;
    }
}
