package com.abl.tms.participant;

import com.abl.db.model.TxnType;
import com.abl.db.service.TxnTypeService;
import com.abl.tms.TxnDetails;
import com.abl.utils.SpringUtils;
import org.apache.log4j.Logger;
import org.jpos.iso.ISOMsg;

public class ParseAppVersion extends ParseTxn {

    private static final Logger logger = Logger.getLogger(ParseAppVersion.class);
    public static final int RELEASE_VERSION_LENGTH = 6;
    public static final int APP_VERSION_LENGTH = 16;
    public static final int TERMINAL_INFO_LENGTH = 60;

    @Override
    protected boolean parseRequest(TxnDetails txnDetails, ISOMsg request) {
        super.parseRequest(txnDetails, request);

        String taxiNumber = "";
        try {
            taxiNumber = getStringField(request, 63).substring(40, 60).trim();
        } catch (Exception e) {
            logger.error("Vehicle number cannot be parsed successfully");
        }

        TxnTypeService txnTypeService = SpringUtils.getBean(TxnTypeService.class);
        TxnType txnType = txnTypeService.getTxnType(TxnType.TYPE_APP_VERSION);
        txnDetails.setTxnType(txnType);

        byte[] field48 = request.getBytes(48);
        if (field48.length < (RELEASE_VERSION_LENGTH + APP_VERSION_LENGTH) || (field48.length - RELEASE_VERSION_LENGTH) % APP_VERSION_LENGTH != 0) {
            logger.error("[" + taxiNumber + "] " + "field 48 has invalid length: " + field48.length);
            return false;
        }

        byte[] releaseVersionBytes = new byte[RELEASE_VERSION_LENGTH];
        System.arraycopy(field48, 0, releaseVersionBytes, 0, RELEASE_VERSION_LENGTH);
        String releaseVersion = new String(releaseVersionBytes);
        logger.debug("releaseVersion=" + releaseVersion);
        try {
            Integer.parseInt(releaseVersion);
            txnDetails.setCurrentReleaseVersion(releaseVersion);
        } catch (Exception e) {
            logger.error("[" + taxiNumber + "] " + "release version is invalid " + releaseVersion);
            return false;
        }

        for (int i = 0; i < ((field48.length - RELEASE_VERSION_LENGTH) / APP_VERSION_LENGTH); i++) {
            byte[] versionBytes = new byte[APP_VERSION_LENGTH];
            System.arraycopy(field48, RELEASE_VERSION_LENGTH + (i * APP_VERSION_LENGTH), versionBytes, 0, APP_VERSION_LENGTH);
            String versionStr = new String(versionBytes);
            logger.debug("versionStr=" + versionStr);
            String appName = versionStr.substring(0, 10);
            logger.debug("appName=" + appName);
            String appVersion = versionStr.substring(10, 16);
            logger.debug("appVersion=" + appVersion);
            try {
                Integer.parseInt(appVersion);
            } catch (Exception e) {
                logger.error("[" + taxiNumber + "] " + "app version is invalid " + appVersion);
                return false;
            }
        }

        byte[] field63 = request.getBytes(63);
        if (field63 == null || field63.length != TERMINAL_INFO_LENGTH) {
            logger.error("[" + taxiNumber + "] " + "field 63 has invalid length: " + (field63 == null ? 0 : field63.length));
            return false;
        }
        txnDetails.setTermSerialNo(getStringField(request, 63).substring(0, 20).trim());
        txnDetails.setModelName(getStringField(request, 63).substring(20, 40).trim());
        txnDetails.setTaxiNumber(taxiNumber);

        checkStan(txnDetails);

        return true;
    }
}
