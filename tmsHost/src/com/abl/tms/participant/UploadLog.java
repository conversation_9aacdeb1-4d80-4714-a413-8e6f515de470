package com.abl.tms.participant;

import com.abl.db.model.TerminalLog;
import com.abl.db.model.TerminalLogRecord;
import com.abl.db.model.TmsTerminal;
import com.abl.db.model.TxnResult;
import com.abl.db.service.TerminalLogRecordService;
import com.abl.db.service.TerminalLogService;
import com.abl.tms.utils.ContextUtils;
import com.abl.tms.utils.MessageUtils;
import com.abl.utils.ByteUtils;
import com.abl.utils.DateUtils;
import com.abl.utils.SpringUtils;
import org.apache.log4j.Logger;
import org.jpos.iso.ISOMsg;
import org.jpos.transaction.Context;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class UploadLog extends AbstractParticipant {
    private static final Logger logger = Logger.getLogger(UploadLog.class);

    @Override
    public void abort(long id, Context context) {
        logger.error("abort");
    }

    @Override
    public void commit(long id, Context context) {
        logger.debug("commit");
    }

    @Override
    public int prepare(long id, Context context) {
        logger.debug("prepare");

        TmsTerminal terminal = (TmsTerminal) context.get(ContextUtils.Item.TERMINAL);

        if (!terminal.isUploadLog()) {
            logger.error("[" + terminal.getVehicleNo() + "] " + "terminal.isUploadLog is false");
            ContextUtils.putIfDoesNotExist(context, ContextUtils.Item.RESULT_CODE, TxnResult.ERROR_UPLOAD_SUSPENDED);
            return ABORTED;
        }

        ISOMsg request = (ISOMsg) context.get(ContextUtils.Item.REQUEST);
        byte[] field61 = request.getBytes(61);
        String field61Str = new String(field61);
        Date logDate = DateUtils.parse(field61Str.substring(0, 8), "yyyyMMdd");
        int sequenceNo = Integer.parseInt(field61Str.substring(8).trim());

        TerminalLogService terminalLogService = SpringUtils.getBean(TerminalLogService.class);
        TerminalLog terminalLog = terminalLogService.getTerminaLogByTerminalDate(terminal.getId(), logDate);
        if (terminalLog == null) {
            terminalLog = new TerminalLog();
        }
        terminalLog.setLogDate(logDate);
        terminalLog.setTmsTerminal(terminal);
        terminalLogService.save(terminalLog);

        TerminalLogRecordService terminalLogRecordService = SpringUtils.getBean(TerminalLogRecordService.class);
        List<TerminalLogRecord> terminalLogRecords = terminalLogRecordService.getTerminaLogRecordByLogSequence(terminalLog.getId(), sequenceNo);
        if (terminalLogRecords != null && terminalLogRecords.size() > 0) {
            for (TerminalLogRecord terminalLogRecord : terminalLogRecords) {
                terminalLogRecordService.delete(terminalLogRecord);
            }
        }

        byte[] field48 = request.getBytes(48);
        try {
            ArrayList<byte[]> recordsList = MessageUtils.parseLllVarBytes(field48);
            if (recordsList == null) {
                logger.error("[" + terminal.getVehicleNo() + "] " + "field 48 data is invalid");
                ContextUtils.putIfDoesNotExist(context, ContextUtils.Item.RESULT_CODE, TxnResult.ERROR_FORMAT_ERROR);
                return ABORTED;
            }

            for (byte[] logRecordBytes : recordsList) {
                TerminalLogRecord termLogRecord = new TerminalLogRecord();
                termLogRecord.setTerminalLog(terminalLog);
                termLogRecord.setSequenceNo(sequenceNo);
                termLogRecord.setLength(logRecordBytes.length);
                String timestampStr = new String(ByteUtils.subArray(logRecordBytes, 0, 12));
                Date timestamp = DateUtils.parse(timestampStr, "yyMMddHHmmss");
                termLogRecord.setTimestamp(timestamp);
                termLogRecord.setSource(new String(ByteUtils.subArray(logRecordBytes, 12, 15)));
                termLogRecord.setData(ByteUtils.bytesToHex(ByteUtils.subArray(logRecordBytes, 15, logRecordBytes.length)));
                termLogRecord.setUploadDateTime(new Date());

                terminalLogRecordService.save(termLogRecord);
            }
        } catch (Exception e) {
            logger.error("[" + terminal.getVehicleNo() + "] " + "field 48 data is invalid", e);
            ContextUtils.putIfDoesNotExist(context, ContextUtils.Item.RESULT_CODE, TxnResult.ERROR_FORMAT_ERROR);
            return ABORTED;
        }

        return PREPARED;
    }
}
