package com.abl.tms.participant;

import org.jpos.core.Configurable;
import org.jpos.core.Configuration;
import org.jpos.core.ConfigurationException;
import org.jpos.transaction.Context;
import org.jpos.transaction.TransactionParticipant;

import java.io.Serializable;

/**
 * This is basically layer on top of TransactionParticipant.  All participants should
 * extend from this class.
 */
public abstract class AbstractParticipant implements TransactionParticipant, Configurable {

    // These methods provide exact same functions as TransactionParticipant methods
    // of the same name, but "Serializable" is cast as "Context".

    public abstract void abort(long id, Context context);

    public abstract void commit(long id, Context context);

    public abstract int prepare(long id, Context context);

    /**
     * override this method if participant requires some initialization
     */
    public void setConfiguration(Configuration cfg) throws ConfigurationException {
    }

    @Override
    public void abort(long id, Serializable context) {
        abort(id, (Context) context);
    }

    @Override
    public void commit(long id, Serializable context) {
        commit(id, (Context) context);
    }

    @Override
    public int prepare(long id, Serializable context) {
        return prepare(id, (Context) context);
    }

}
