package com.abl.tms.participant;

import com.abl.db.model.TxnResult;
import com.abl.tms.participant.Field.Type;
import com.abl.tms.utils.ContextUtils;
import com.abl.tms.utils.ContextUtils.Item;
import org.apache.log4j.Logger;
import org.jpos.iso.ISOException;
import org.jpos.iso.ISOMsg;
import org.jpos.transaction.Context;

public class CreateResponse extends AbstractIsoMsgParticipant {

    private static final Logger logger = Logger.getLogger(CreateResponse.class);

    public CreateResponse() {
        setDefaultFieldType(Type.request);
    }

    @Override
    public void abort(long id, Context context) {
        logger.error("abort");
    }

    @Override
    public void commit(long id, Context context) {
        logger.debug("commit");
    }

    @Override
    public int prepare(long id, Context context) {
        logger.debug("prepare");

        ISOMsg request = (ISOMsg) context.get(Item.REQUEST);
        ISOMsg response;
        try {
            response = createResponse(request);
        } catch (ISOException e) {
            logger.error("unable to create response", e);
            ContextUtils.putIfDoesNotExist(context, Item.RESULT_CODE, TxnResult.SYSERR);
            return ABORTED;
        }

        try {
            prepareISOMsg(response, context);
        } catch (ISOException e) {
            logger.error("unable to prepare response", e);
            ContextUtils.putIfDoesNotExist(context, Item.RESULT_CODE, TxnResult.SYSERR);
            return ABORTED;
        }

        ContextUtils.put(context, Item.RESPONSE, response);

        return PREPARED;
    }

}
