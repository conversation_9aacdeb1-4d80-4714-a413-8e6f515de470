package com.abl.tms.participant;

import com.abl.db.model.TxnLog;
import com.abl.db.model.TxnResult;
import com.abl.db.service.TxnResultService;
import com.abl.tms.TxnDetails;
import com.abl.tms.service.TmsServerService;
import com.abl.tms.utils.ContextUtils;
import com.abl.tms.utils.ContextUtils.Item;
import com.abl.utils.SpringUtils;
import org.apache.log4j.Logger;
import org.jpos.iso.ISOMsg;
import org.jpos.transaction.AbortParticipant;
import org.jpos.transaction.Context;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;

import java.io.Serializable;

public class SaveTxn extends AbstractParticipant implements AbortParticipant {

    private static final Logger logger = Logger.getLogger(SaveTxn.class);

    @Override
    public int prepareForAbort(long id, Serializable context) {
        logger.error("prepareForAbort");
        return PREPARED | READONLY;
    }

    @Override
    public void abort(long id, Context context) {
        logger.error("abort");
        try {
            // get txnlog updated with transaction data
            TxnLog txnLog = getFillTxnLog(context);

            // update result code
            String resultCode = (String) context.get(Item.RESULT_CODE);
            if (resultCode == null) {
                logger.error("resultCode == null, setting resultCode to ERROR");
                resultCode = TxnResult.ERROR;
            }
            TxnResultService txnResultService = SpringUtils.getBean(TxnResultService.class);
            TxnResult txnResult = txnResultService.getTxnResult(resultCode);
            txnLog.setTxnResult(txnResult);

            // save transaction
            TmsServerService tmsServerService = SpringUtils.getBean(TmsServerService.class);
            tmsServerService.saveFailedTransaction(context);

            PlatformTransactionManager txnManager = (PlatformTransactionManager) context.get(Item.SPRING_TXN_MGR);
            TransactionStatus status = (TransactionStatus) context.get(Item.SPRING_TXN_STATUS);
            txnManager.commit(status);

        } catch (Exception e) {
            logger.error("unable to save TxnLog", e);
        }
    }

    @Override
    public void commit(long id, Context context) {
        logger.debug("commit");
    }

    @Override
    public int prepare(long id, Context context) {
        logger.debug("prepare");

        try {
            // get txnlog updated with transaction data
            TxnLog txnLog = getFillTxnLog(context);

            // update result code
            String resultCode = (String) context.get(Item.RESULT_CODE);
            if (resultCode == null) {
                resultCode = TxnResult.APPROVED;
            }
            TxnResultService txnResultService = SpringUtils.getBean(TxnResultService.class);
            TxnResult txnResult = txnResultService.getTxnResult(resultCode);
            txnLog.setTxnResult(txnResult);

            logger.debug("txnManager.commit");

            // save transaction
            TmsServerService tmsServerService = SpringUtils.getBean(TmsServerService.class);
            tmsServerService.saveSuccessfulTransaction(context);

            PlatformTransactionManager txnManager = (PlatformTransactionManager) context.get(Item.SPRING_TXN_MGR);
            TransactionStatus status = (TransactionStatus) context.get(Item.SPRING_TXN_STATUS);
            txnManager.commit(status);
        } catch (Exception e) {
            logger.error("unable to save TxnLog", e);
            return ABORTED;
        }

        return PREPARED;
    }

    protected TxnLog getFillTxnLog(Context context) {

        TxnDetails txnDetails = (TxnDetails) context.get(Item.TXN_DETAILS);

        TxnLog txnLog = (TxnLog) context.get(Item.TXN_LOG);
        if (txnLog == null) {
            txnLog = new TxnLog();
            ContextUtils.put(context, Item.TXN_LOG, txnLog);
        }

        txnLog.setMti(txnDetails.getMti());
        txnLog.setTxnDateTime(txnDetails.getTxnDateTime());
        txnLog.setProcCode(txnDetails.getProcCode());
        txnLog.setTid(txnDetails.getTid());
        txnLog.setTxnType(txnDetails.getTxnType());
        txnLog.setTermSerialNo(txnDetails.getTermSerialNo());
        txnLog.setData(txnDetails.getData());

        ISOMsg response = (ISOMsg) context.get(Item.RESPONSE);
        if (response != null) {
            if (response.hasField(39)) {
                txnLog.setResponseCode(response.getString(39));
            }
        }

        return txnLog;
    }

}
