/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package com.abl.tms.participant;

import com.abl.tms.TxnDetails;
import org.jpos.iso.ISOMsg;
import org.apache.log4j.Logger;

/**
 *
 * <AUTHOR>
 */
public class ParseLogon extends ParseTxn{
    private static final Logger logger = Logger.getLogger(ParseLogon.class);
    public static final int TERMINAL_INFO_LENGTH = 60;
    public static final int TERMINAL_FIRMWARE_INFO_LENGTH = 60;

    @Override
    protected boolean parseRequest(TxnDetails txnDetails, ISOMsg request) {
        super.parseRequest(txnDetails, request);

        String taxiNumber = "";
        try {
            taxiNumber = getStringField(request, 63).substring(40, 60).trim();
        } catch (Exception e) {
            logger.error("Vehicle number cannot be parsed successfully");
        }

        byte[] field61 = request.getBytes(61);
        if (field61 == null || field61.length != TERMINAL_FIRMWARE_INFO_LENGTH) {
            logger.error("[" + taxiNumber + "] " + "field 61 has invalid length: " + (field61 == null ? 0 : field61.length));
            return false;
        }
        txnDetails.setOsVersion(getStringField(request, 61).substring(0, 20).trim());
        txnDetails.setCtlsVersion(getStringField(request, 61).substring(20, 40).trim());
        txnDetails.setPtid(getStringField(request, 61).substring(40, 60).trim());

        byte[] field63 = request.getBytes(63);
        if (field63 == null || field63.length != TERMINAL_INFO_LENGTH) {
            logger.error("[" + taxiNumber + "] " + "field 63 has invalid length: " + (field63 == null ? 0 : field63.length));
            return false;
        }
        txnDetails.setTermSerialNo(getStringField(request, 63).substring(0, 20).trim());
        txnDetails.setModelName(getStringField(request, 63).substring(20, 40).trim());
        txnDetails.setTaxiNumber(taxiNumber);

        checkStan(txnDetails);

        return true;
    }
}
