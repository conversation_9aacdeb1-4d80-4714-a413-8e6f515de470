package com.abl.tms.participant;

import com.abl.db.model.TxnResult;
import com.abl.db.service.TxnResultService;
import com.abl.tms.utils.ContextUtils;
import com.abl.tms.utils.ContextUtils.Item;
import com.abl.utils.SpringUtils;
import org.apache.log4j.Logger;
import org.jpos.iso.ISOException;
import org.jpos.iso.ISOMsg;
import org.jpos.transaction.AbortParticipant;
import org.jpos.transaction.Context;

import java.io.Serializable;

public class UpdateResponseCode extends AbstractParticipant implements AbortParticipant {

    private static final Logger logger = Logger.getLogger(UpdateResponseCode.class);

    @Override
    public int prepareForAbort(long id, Serializable context) {
        logger.error("prepareForAbort");
        return PREPARED | READONLY;
    }

    @Override
    public void abort(long id, Context context) {
        logger.error("abort");

        String resultCode = (String) context.get(Item.RESULT_CODE);
        if (resultCode == null) {
            logger.error("resultCode == null, setting resultCode to ERROR");
            resultCode = TxnResult.ERROR;
        }

        setResponseCode(context, resultCode);
    }

    @Override
    public void commit(long id, Context context) {
        logger.debug("commit");
    }

    @Override
    public int prepare(long id, Context context) {
        logger.debug("prepare");

        String resultCode = (String) context.get(Item.RESULT_CODE);
        if (resultCode == null) {
            resultCode = TxnResult.APPROVED;
        }

        if (!setResponseCode(context, resultCode)) {
            return ABORTED;
        }

        return PREPARED;
    }

    protected boolean setResponseCode(Context context, String resultCode) {
        ISOMsg response = (ISOMsg) context.get(Item.RESPONSE);
        if (response == null) {
            logger.warn("no response found");
            return true;
        }

        TxnResultService txnResultService = (TxnResultService) SpringUtils.getBean(TxnResultService.class);
        String respCode = txnResultService.getResponseCode(resultCode);
        if (respCode == null) {
            logger.error("no resp code found for result=" + resultCode);
            ContextUtils.putIfDoesNotExist(context, Item.RESULT_CODE, TxnResult.SYSERR);
            return false;
        } else {
            try {
                response.set(39, respCode);
            } catch (ISOException e) {
                logger.error("unable to set response code", e);
                ContextUtils.putIfDoesNotExist(context, Item.RESULT_CODE, TxnResult.SYSERR);
                return false;
            }
        }

        return true;
    }

}
