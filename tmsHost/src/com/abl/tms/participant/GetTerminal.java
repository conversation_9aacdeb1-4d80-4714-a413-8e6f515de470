package com.abl.tms.participant;

import com.abl.db.model.*;
import com.abl.db.service.*;
import com.abl.tms.TxnDetails;
import com.abl.tms.utils.ContextUtils;
import com.abl.tms.utils.ContextUtils.Item;
import com.abl.utils.SpringUtils;
import org.apache.log4j.Logger;
import org.jpos.transaction.Context;

import java.util.Date;

public class GetTerminal extends AbstractParticipant {

    private static final Logger logger = Logger.getLogger(GetTerminal.class);
    private static final String CONFIG_DEFAULT_GROUP_NAME_KEY = "default_group_name";

    @Override
    public void abort(long id, Context context) {
        logger.error("abort");
    }

    @Override
    public void commit(long id, Context context) {
        logger.debug("commit");
    }

    @Override
    public int prepare(long id, Context context) {
        logger.debug("prepare");

        TxnDetails txnDetails = (TxnDetails) context.get(Item.TXN_DETAILS);

        String termSerialNo = txnDetails.getTermSerialNo();
        if ((termSerialNo == null) || termSerialNo.trim().length() <= 0) {
            logger.error("terminal serial number is empty");
            ContextUtils.putIfDoesNotExist(context, Item.RESULT_CODE, TxnResult.ERROR_INV_TERM);
            return ABORTED;
        }

        logger.info("***termSerialNo=" + termSerialNo);

        String vehicleNo = txnDetails.getTaxiNumber();
        if ((vehicleNo == null) || vehicleNo.trim().length() <= 0) {
            logger.error("vehicle number is empty");
            ContextUtils.putIfDoesNotExist(context, Item.RESULT_CODE, TxnResult.ERROR_INV_TERM);
            return ABORTED;
        }

        logger.info("***VehicleNo=" + vehicleNo);

        TmsTerminalService terminalService = SpringUtils.getBean(TmsTerminalService.class);
        ModelService modelService = SpringUtils.getBean(ModelService.class);
//        List<TmsTerminal> tmsTerminalList = new ArrayList<TmsTerminal>();

        Model model = modelService.getModelByName(txnDetails.getModelName().trim().toUpperCase());
        if (model == null) {
            logger.error("[" + vehicleNo + "] " + "Model name (" + txnDetails.getModelName().trim() + ") does not exist");
            ContextUtils.putIfDoesNotExist(context, Item.RESULT_CODE, TxnResult.ERROR_INV_MODEL);
            return ABORTED;
        }

        int unpaired = terminalService.unpairOtherTerminalsForVehicleNo(vehicleNo.trim(), termSerialNo.trim());
        if (unpaired > 0) {
            logger.info("Unpaired " + unpaired + " device(s) with current vehicle " + vehicleNo.trim() + ", new serial number = " + termSerialNo.trim());
        }

        logger.debug("getTerminalForUpdate");
//        TmsTerminal terminal = terminalService.getTerminalForUpdate(termSerialNo.trim());
        // 2013-04-17, ky, change not to use getTerminalForUpdate as causing deadlock
        TmsTerminal terminal = getTerminal(terminalService,termSerialNo,vehicleNo,model,txnDetails);

        ContextUtils.put(context, Item.TERMINAL, terminal);

        VehicleGroupService vehicleGroupService = SpringUtils.getBean(VehicleGroupService.class);
        VehicleGroup vehicleGroup = vehicleGroupService.getVehicleGroupByVehicleId(terminal.getVehicleNo());

        // if no grouping for this vehicle, assign to default group
        if (vehicleGroup == null) {
            ConfigService configService = SpringUtils.getBean(ConfigService.class);
            String defaultGroupName = configService.getConfig(CONFIG_DEFAULT_GROUP_NAME_KEY);
            if (defaultGroupName == null || defaultGroupName.trim().length() <= 0) {
                logger.error("[" + vehicleNo + "] " + "default group name is not configured");
                ContextUtils.putIfDoesNotExist(context, Item.RESULT_CODE, TxnResult.ERROR_TMS_ERROR);
                return ABORTED;
            }

            VehicleService vehicleService = SpringUtils.getBean(VehicleService.class);
            Vehicle vehicle = vehicleService.getVehicle(terminal.getVehicleNo());
            if (vehicle == null) {
                logger.error("[" + vehicleNo + "] " + "vehicle not found: " + terminal.getVehicleNo());
                ContextUtils.putIfDoesNotExist(context, Item.RESULT_CODE, TxnResult.ERROR_INV_VEHICLE);
                return ABORTED;
            }

            GroupService groupService = SpringUtils.getBean(GroupService.class);
            Group defaultGroup = groupService.getGroupByName(defaultGroupName.trim());

            if (defaultGroup == null) {
                logger.error("[" + vehicleNo + "] " + "default group (" + defaultGroupName.trim() + ") is not found");
                ContextUtils.putIfDoesNotExist(context, Item.RESULT_CODE, TxnResult.ERROR_TMS_ERROR);
                return ABORTED;
            }

            vehicleGroup = new VehicleGroup();
            vehicleGroup.setGroup(defaultGroup);
            vehicleGroup.setVehicle(vehicle);
            vehicleGroup.setAssignDateTime(new Date());
            vehicleGroupService.save(vehicleGroup);
            logger.info("Default group assigned to vehicle: " + terminal.getVehicleNo());
        }

        logger.debug("vehicleGroup=" + vehicleGroup);
        ContextUtils.put(context, Item.VEHICLE_GROUP, vehicleGroup);

        logger.debug("getTerminalForUpdate done");
        return PREPARED;
    }

    private TmsTerminal getTerminal(TmsTerminalService terminalService,String termSerialNo, String vehicleNo, Model model, TxnDetails txnDetails ) {
        TmsTerminal terminal = terminalService.getTerminalBySerialNo(termSerialNo.trim());
        if (terminal == null) {

            logger.error("[" + vehicleNo + "] " + "terminal record not found for serial no=" + termSerialNo + ", creating record now...");

            Date now = new Date();
            terminal = new TmsTerminal();
            terminal.setCreateDateTime(now);
            terminal.setUpdateDateTime(now);
            terminal.setSerialNo(termSerialNo.trim());
            terminal.setVehicleNo(vehicleNo.trim().toUpperCase());
            terminal.setModel(model);
            terminal.setOsVersion(txnDetails.getOsVersion());
            terminal.setCtlsVersion(txnDetails.getCtlsVersion());
            terminal.setPtid(txnDetails.getPtid());
            terminal.setCurrentVersion(txnDetails.getCurrentReleaseVersion());

            // insert new record
            terminalService.save(terminal);
        } else {
            logger.debug("terminal is not null");
            Date now = new Date();
            boolean changed = false;

            if (!terminal.getVehicleNo().equalsIgnoreCase(vehicleNo.trim())) {
                logger.info("original vehicle no:" + terminal.getVehicleNo());
                logger.info("new vehicle no:" + vehicleNo.trim());
                terminal.setVehicleNo(vehicleNo.trim());
                changed = true;
            }

            if (!terminal.getModel().getModelName().equalsIgnoreCase(txnDetails.getModelName().trim())) {
                logger.info("original model:" + terminal.getModel().getModelName());
                logger.info("new model:" + model.getModelName());
                terminal.setModel(model);
                changed = true;
            }

            if (txnDetails.getOsVersion() != null && !terminal.getOsVersion().equalsIgnoreCase(txnDetails.getOsVersion().trim())) {
                logger.info("original os version: " + terminal.getOsVersion());
                logger.info("new os version: " + txnDetails.getOsVersion());
                terminal.setOsVersion(txnDetails.getOsVersion().trim());
                changed = true;
            }

            if (txnDetails.getCtlsVersion() != null && !terminal.getCtlsVersion().equalsIgnoreCase(txnDetails.getCtlsVersion().trim())) {
                logger.info("original ctls version: " + terminal.getCtlsVersion());
                logger.info("new ctls version: " + txnDetails.getCtlsVersion());
                terminal.setCtlsVersion(txnDetails.getCtlsVersion().trim());
                changed = true;
            }

            if (txnDetails.getPtid() != null && !terminal.getPtid().equalsIgnoreCase(txnDetails.getPtid().trim())) {
                logger.info("original ptid: " + terminal.getPtid());
                logger.info("new ptid: " + txnDetails.getPtid());
                terminal.setPtid(txnDetails.getPtid().trim());
                changed = true;
            }

            if ((terminal.getCurrentVersion()== null) || // no current version stored yet.
                (txnDetails.getCurrentReleaseVersion() != null && !terminal.getCurrentVersion().equalsIgnoreCase(txnDetails.getCurrentReleaseVersion().trim()))) {
                logger.info("original release version: " + (terminal.getCurrentVersion()==null?"Null":terminal.getCurrentVersion()));
                logger.info("new release version: " + txnDetails.getCurrentReleaseVersion());
                terminal.setCurrentVersion(txnDetails.getCurrentReleaseVersion().trim());
                changed = true;
            }

            if (terminal.isVehicleUnpaired()) {
                logger.info("original VehicleUnpaired: " + terminal.isVehicleUnpaired());
                terminal.setVehicleUnpaired(false);
                changed = true;
            }

            if (changed) {
                logger.info("Terminal (" + terminal.getSerialNo() + "," + terminal.getVehicleNo() + ") is changed, updating now...");

                terminal.setUpdateDateTime(now);
                terminalService.save(terminal);
            }

        }
        return terminal;
    }
}
