package com.abl.tms.participant;

import com.abl.db.model.TxnResult;
import com.abl.tms.TxnDetails;
import com.abl.tms.utils.ContextUtils;
import com.abl.tms.utils.ContextUtils.Item;
import org.apache.jcs.JCS;
import org.apache.log4j.Logger;
import org.jpos.iso.ISOException;
import org.jpos.iso.ISOMsg;
import org.jpos.transaction.Context;

/**
 * Parses request message and fill in TxnDetails object.
 * <p/>
 * If necessary, extend this class and override the parseRequest method.
 * The idea is that after parsing, the later participants just access the TxnDetails object, and
 * they do not need to understand how to parse the request message.
 */
public class ParseTxn extends AbstractParticipant {

    private static final Logger logger = Logger.getLogger(ParseTxn.class);

    private String txnTypeName;
    private static JCS terminalStanCache;

    private static JCS getTerminalStanCache() {
        if (terminalStanCache != null) return terminalStanCache;
        try {
            terminalStanCache = JCS.getInstance("terminalStanCache");
        } catch (Exception e) {
            // Handle cache region initialization failure
            logger.warn("terminalStanCache instance cannot be created");
        }
        return terminalStanCache;
    }

    public String getTxnType() {
        return txnTypeName;
    }

    public void setTxnType(String txnType) {
        this.txnTypeName = txnType;
    }

    @Override
    public void abort(long id, Context context) {
        logger.error("abort");
    }

    @Override
    public void commit(long id, Context context) {
        logger.debug("commit");
    }

    @Override
    public int prepare(long id, Context context) {
        logger.debug("prepare");

        TxnDetails txnDetails = (TxnDetails) context.get(Item.TXN_DETAILS);

        // parse the request to fill up the txnLog object
        ISOMsg request = (ISOMsg) context.get(Item.REQUEST);
        if (!parseRequest(txnDetails, request)) {
            logger.error("error parsing request msg");
            ContextUtils.putIfDoesNotExist(context, Item.RESULT_CODE, TxnResult.ERROR_FORMAT_ERROR);
            return ABORTED;
        }

        return PREPARED;
    }

    /**
     * Parses the "request" message, and fill in "txnDetails" object.
     * <p/>
     * Override this method, if necessary
     *
     * @param txnDetails
     * @param request
     * @return true if parse is successful
     */
    protected boolean parseRequest(TxnDetails txnDetails, ISOMsg request) {
        try {
            txnDetails.setMti(request.getMTI());
        } catch (ISOException e) {
            logger.error("mti not found", e);
            return false;
        }
        txnDetails.setProcCode(getStringField(request, 3));
        String tid = getStringField(request, 41);
        if (tid != null) {
            txnDetails.setTid(tid.trim());
        }

        txnDetails.setSystemTraceAuditNumber(getStringField(request, 11));

        return true;
    }

    protected void checkStan(TxnDetails txnDetails) {
        String key = txnDetails.getTermSerialNo();
        String currentStan = txnDetails.getSystemTraceAuditNumber();

        if (key != null && key.trim().length() > 0 && currentStan != null) {
            JCS terminalStanCache = getTerminalStanCache();
            if (terminalStanCache != null) {
                Object value = terminalStanCache.get(key);
                if (value != null) {
                    String previousStan = (String) value;
                    if (previousStan.equals(currentStan)) {
                        logger.error("WARNING: DUPLICATE STAN for " + key + ": " + currentStan);
                    }
                }
                try {
                    terminalStanCache.put(key, currentStan);
                } catch (Exception e) {
                    logger.error("Not able to store stan to cache " + e.getMessage());
                }
            }
        }
    }

    protected String getStringField(ISOMsg request, int field) {
        if (request.hasField(field)) {
            return request.getString(field);
        } else {
            return null;
        }
    }

    protected Integer getIntegerField(ISOMsg request, int field) {
        if (request.hasField(field)) {
            return Integer.parseInt(request.getString(field));
        } else {
            return null;
        }
    }

    protected Long getLongField(ISOMsg request, int field) {
        if (request.hasField(field)) {
            return Long.parseLong(request.getString(field));
        } else {
            return null;
        }
    }

}
