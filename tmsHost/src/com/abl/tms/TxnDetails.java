package com.abl.tms;

import com.abl.db.model.TxnType;

import java.io.Serializable;
import java.util.Date;

/**
 * stores transaction details
 */
@SuppressWarnings("serial")
public class TxnDetails implements Serializable {

    private String mti;
    private String procCode;
    private String systemTraceAuditNumber;
    private Date txnDateTime;
    private String tid;
    private transient TxnType txnType;
    private String termSerialNo;
    private String modelName;
    private String taxiNumber;
    private String osVersion;
    private String ctlsVersion;
    private String ptid;
    private String currentReleaseVersion;
    private byte[] data;

    public String getMti() {
        return mti;
    }

    public void setMti(String mti) {
        this.mti = mti;
    }

    public String getProcCode() {
        return procCode;
    }

    public void setProcCode(String procCode) {
        this.procCode = procCode;
    }

    public String getSystemTraceAuditNumber() {
        return systemTraceAuditNumber;
    }

    public void setSystemTraceAuditNumber(String systemTraceAuditNumber) {
        this.systemTraceAuditNumber = systemTraceAuditNumber;
    }

    public Date getTxnDateTime() {
        return txnDateTime;
    }

    public void setTxnDateTime(Date txnDateTime) {
        this.txnDateTime = txnDateTime;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public TxnType getTxnType() {
        return txnType;
    }

    public void setTxnType(TxnType txnType) {
        this.txnType = txnType;
    }

    public String getTermSerialNo() {
        return termSerialNo;
    }

    public void setTermSerialNo(String termSerialNo) {
        this.termSerialNo = termSerialNo;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public String getTaxiNumber() {
        return taxiNumber;
    }

    public void setTaxiNumber(String taxiNumber) {
        this.taxiNumber = taxiNumber;
    }

    public String getCtlsVersion() {
        return ctlsVersion;
    }

    public void setCtlsVersion(String ctlsVersion) {
        this.ctlsVersion = ctlsVersion;
    }

    public String getOsVersion() {
        return osVersion;
    }

    public void setOsVersion(String osVersion) {
        this.osVersion = osVersion;
    }

    public String getPtid() {
        return ptid;
    }

    public void setPtid(String ptid) {
        this.ptid = ptid;
    }

    public String getCurrentReleaseVersion() {
        return currentReleaseVersion;
    }

    public void setCurrentReleaseVersion(String currentReleaseVersion) {
        this.currentReleaseVersion = currentReleaseVersion;
    }

    public byte[] getData() {
        return data;
    }

    public void setData(byte[] data) {
        this.data = data;
    }
}
