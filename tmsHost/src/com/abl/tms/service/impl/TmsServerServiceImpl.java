package com.abl.tms.service.impl;

import com.abl.db.dao.TmsTerminalDao;
import com.abl.db.dao.TxnLogDao;
import com.abl.db.model.TxnLog;
import com.abl.tms.service.TmsServerService;
import com.abl.tms.utils.ContextUtils;
import com.abl.tms.utils.ContextUtils.Item;
import org.jpos.transaction.Context;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional(readOnly = false)
public class TmsServerServiceImpl implements TmsServerService {

    @Autowired
    TmsTerminalDao terminalDao;
    @Autowired
    TxnLogDao txnLogDao;

    @Override
    public void saveSuccessfulTransaction(Context context) {

        TxnLog txnLog = (TxnLog) ContextUtils.get(context, Item.TXN_LOG);
        if (txnLog != null) {
            txnLogDao.save(txnLog);
        }
    }

    @Override
    public void saveFailedTransaction(Context context) {
        TxnLog txnLog = (TxnLog) ContextUtils.get(context, Item.TXN_LOG);
        if (txnLog != null) {
            txnLogDao.save(txnLog);
        }
    }
}
