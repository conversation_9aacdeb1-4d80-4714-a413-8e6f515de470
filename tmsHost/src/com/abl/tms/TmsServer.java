package com.abl.tms;

import com.abl.utils.GeneralUtils;
import com.abl.utils.SpringUtils;
import org.apache.log4j.Logger;
import org.jpos.q2.Q2;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;

/**
 * Transaction Server
 */
public class TmsServer {

    private static final Logger logger = Logger.getLogger(TmsServer.class);

    private Q2 q2;

    public TmsServer() {
        q2 = new Q2();
    }

    public static void main(String[] args) {
        startService();
    }

    public static void startService() {
        TmsServer tmsServer = new TmsServer();
        tmsServer.start();
    }

    private void start() {

        if (!initializeSpring()) {
            return;
        }

        addShutdownHook();

        try {
            q2.start();
        } catch (Exception e) {
            logger.error("unable to start", e);
        }
    }

    private boolean initializeSpring() {
        try {
            ApplicationContext appContext = new ClassPathXmlApplicationContext("applicationContext.xml");
            SpringUtils.initialize(appContext);
        } catch (Exception e) {
            logger.error("unable to initialize spring", e);
            logger.error("initialize spring fails");
            GeneralUtils.sleep(10000);
            return false;
        }

        return true;
    }

    public void addShutdownHook() {
        Runtime.getRuntime().addShutdownHook(
                new Thread("ShutdownHook") {
                    public void run() {
                        logger.info("Shutdown hook");

                        q2.shutdown();
                    }
                }
        );
    }
}
