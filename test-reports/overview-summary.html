<html xmlns:string="xalan://java.lang.String" xmlns:lxslt="http://xml.apache.org/xslt">
<head>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Unit Test Results: Summary</title>
<link rel="stylesheet" type="text/css" title="Style" href="stylesheet.css">
</head>
<body onload="open('allclasses-frame.html','classListFrame')">
<h1>Unit Test Results.</h1>
<table width="100%">
<tr>
<td align="left"></td><td align="right">Designed for use with <a href="https://www.junit.org/">JUnit</a> and <a href="https://ant.apache.org/">Ant</a>.</td>
</tr>
</table>
<hr size="1">
<h2>Summary</h2>
<table class="details" border="0" cellpadding="5" cellspacing="2" width="95%">
<tr valign="top">
<th>Tests</th><th>Failures</th><th>Errors</th><th>Skipped</th><th>Success rate</th><th>Time</th>
</tr>
<tr valign="top" class="Error">
<td><a title="Display all tests" href="all-tests.html">18</a></td><td><a title="Display all failures" href="alltests-fails.html">2</a></td><td><a title="Display all errors" href="alltests-errors.html">12</a></td><td><a title="Display all skipped test" href="alltests-skipped.html">0</a></td><td>22.22%</td><td>0.496</td>
</tr>
</table>
<table border="0" width="95%">
<tr>
<td style="text-align: justify;">
        Note: <em>failures</em> are anticipated and checked for with assertions while <em>errors</em> are unanticipated.
        </td>
</tr>
</table>
<h2>Packages</h2>
<table class="details" border="0" cellpadding="5" cellspacing="2" width="95%">
<tr valign="top">
<th width="80%">Name</th><th>Tests</th><th>Errors</th><th>Failures</th><th>Skipped</th><th nowrap>Time(s)</th><th nowrap>Time Stamp</th><th>Host</th>
</tr>
<tr valign="top" class="Error">
<td><a href="./com/abl/nof/batch/package-summary.html">com.abl.nof.batch</a></td><td>18</td><td>12</td><td>2</td><td>0</td><td>0.496</td><td>2020-02-24T09:46:40</td><td>twwdpc002-dt</td>
</tr>
</table>
</body>
</html>
