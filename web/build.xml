<?xml version="1.0"?>
	
<project basedir="." default="build" name="comfortSG-web">

    <property file="build.properties"/>

    <property name="project.jar" value="${project.name}-${project.version}.jar"/>
    
    <path id="master.classpath">
        <fileset dir="${repos.dir}">
			<include name="abl/abl-common*.jar"/>
			<include name="abl/abl-crypto*.jar"/>
			<include name="abl/comfortSG-database*.jar"/>
            <include name="aopalliance-1.0/*.jar"/>
			<include name="commons/*.jar"/>
			<include name="groovy/*.jar"/>
			<!-- <include name="jasperreports/*.jar"/> -->
			<include name="gson/*.jar"/>
			<include name="hibernate/*.jar"/>
            <include name="jaxws/*.jar"/>
			<include name="log4j/*.jar"/>
			<include name="safenet/*.jar"/>
			<include name="servlet/*.jar"/>
			<include name="spring-3.2.9/*.jar"/>
			<include name="spring-security-3.0.7/*.jar"/>
			<include name="spring-ldap-1.3.1/*.jar"/>
			<include name="spring-webflow-2.1.1/*.jar"/>
            <include name="gson/*.jar"/>
            <include name="jtds/*.jar"/>
			<include name="jcs-1.3/*.jar"/>
            <include name="tiles/*.jar"/>
            <include name="POI/*.jar"/>
			<!--include name="oracle/*.jar"/-->
			<include name="json/*.jar"/>
        </fileset>
    </path>
    
	<target name="clean">
		<delete includeemptydirs="true" failonerror="false">
			<fileset dir="${build.dir}" includes="**/*" excludes="META-INF/*"/>
		</delete>
		<delete includeemptydirs="true" failonerror="false">
			<fileset dir="${dist.dir}" includes="**/*"/>
		</delete>
		<delete includeemptydirs="true" failonerror="false">
			<fileset dir="${deploy.dir}" includes="**/*"/>
		</delete>
	</target>
	
    <target name="compile">
        <mkdir dir="${build.dir}"/>
        <javac destdir="${build.dir}" debug="true" includeAntRuntime="false"
               deprecation="false" optimize="false" failonerror="true">
            <src path="${src.dir}"/>
            <classpath refid="master.classpath"/>
        </javac>
    </target>
	
	<!-- ant task for compiling jasper reports -->
	<!-- <taskdef name="jrc" classname="net.sf.jasperreports.ant.JRAntCompileTask"> -->
    <!--	<classpath refid="master.classpath"/> -->
    <!--</taskdef> -->

    <!-- <target name="compile-reports" description="Compile reports"> -->
	<!--	<jrc srcdir="report" destdir="report"> -->
    <!--		<classpath refid="master.classpath"/> -->
    <!--        <include name="*.jrxml"/> -->
     <!--   </jrc> -->
	<!-- </target> -->

    <target name="build" depends="compile">
        <mkdir dir="${dist.dir}"/>
        <tstamp>
            <format property="today" pattern="dd-MMM-yyyy HH:mm:ss" />
        </tstamp>        
        <jar basedir="${build.dir}"
             destfile="${dist.dir}/${project.jar}"
             excludes="*.jar">
            <manifest>
                <attribute name="Built-By" value="${user.name}"/>
                <attribute name="Built-Date" value="${today}"/>
                <attribute name="Implementation-Version" value="${project.version}"/>
            </manifest>
        </jar>
    </target>

	<condition property="deploy.env" value="${deploy.env}"
			   else="dev">
		<isset property="deploy.env"/>
	</condition>

	<target name="war" depends="deploy,deploy_jsp">
		<war destfile="dist/${deploy.env}/comfort.war">
			<fileset dir="deploy"/>
		</war>
	</target>

    <target name="deploy" depends="build">
		<copy todir="${deploy.dir}" overwrite="true">
    	    <fileset dir="webapp">
            	<exclude name="**/.*"/>
			</fileset>
    	</copy>
		<copy todir="${deploy.dir}/WEB-INF/lib" flatten="true" includeEmptyDirs="false">
        	<fileset dir="${repos.dir}">
				<include name="abl/abl-common*.jar"/>
				<include name="abl/abl-crypto*.jar"/>
				<include name="abl/comfortSG-database*.jar"/>
				<include name="aopalliance-1.0/*.jar"/>
				<include name="asm-3.3/*.jar"/>
				<include name="commons/*.jar"/>
				<include name="hibernate/*.jar"/>
				<include name="jdbc-driver/*.jar"/>
				<include name="jtds/*.jar"/>
				<include name="jcs-1.3/*.jar"/>
				<include name="log4j/*.jar"/>
				<include name="servlet/*.jar"/>
				<include name="sl4j-1.6.0/*.jar"/>
				<include name="spring-3.2.9/*.jar"/>
				<include name="spring-security-3.0.7/*.jar"/>
				<include name="spring-webflow-2.1.1/*.jar"/>
				<include name="spring-ldap-1.3.1/*.jar"/>
				<include name="tiles/*.jar"/>
				<include name="POI/*.jar"/>
				<include name="ojdbc8.jar"/>
				<include name="json/*.jar"/>
        	</fileset>
			<fileset dir="${dist.dir}">
				<include name="${project.jar}" />
			</fileset>
    	</copy>
		<copy todir="${deploy.dir}/WEB-INF/classes" overwrite="true">
			<fileset file="properties/${deploy.env}/applicationContext.properties"/>
			<fileset file="properties/${deploy.env}/applicationContext-security.xml"/>
		</copy>
		<!--<copy todir="${deploy.dir}/WEB-INF/report">-->
    	    <!--<fileset dir="report">-->
            	<!--<exclude name="**/.*"/>-->
            	<!--<exclude name="**/*.jrxml"/>-->
			<!--</fileset>-->
    	<!--</copy>-->
	</target>

    <target name="deploy_jsp">
		<copy todir="${deploy.dir}/WEB-INF/views" overwrite="false">
    	    <fileset dir="webapp/WEB-INF/views">
            	<exclude name="**/.*"/>
			</fileset>
    	</copy>
        <copy todir="${deploy.dir}/WEB-INF/layouts" overwrite="true">
            <fileset dir="webapp/WEB-INF/layouts">
                <exclude name="**/.*"/>
            </fileset>
        </copy>
    </target>
</project>
