
# database configurations
# -----------------------
datasource.driverClassName=oracle.jdbc.driver.OracleDriver
#datasource.driverClassName=com.mysql.jdbc.Driver
#datasource.url=*******************************
datasource.url=*********************************************************
datasource.username=comfort
datasource.password=password
datasource.maxActive=100
datasource.maxIdle=30
datasource.maxWait=1000
datasource.testOnBorrow=true
datasource.validationQuery=select 1 from dual

#datasource.url=
#datasource.username=
#datasource.password=
#datasource.maxActive=100
#datasource.maxIdle=30
#datasource.maxWait=1000
#datasource.testOnBorrow=true
#datasource.validationQuery=select 1 from dual

# ezlink database configurations
ezlinkDataSource.driverClassName=oracle.jdbc.driver.OracleDriver
#ezlinkDataSource.driverClassName=com.mysql.jdbc.Driver
#ezlinkDataSource.url=***************************/
ezlinkDataSource.url=*********************************************************
ezlinkDataSource.username=comfort
ezlinkDataSource.password=password
ezlinkDataSource.maxActive=100
ezlinkDataSource.maxIdle=30
ezlinkDataSource.maxWait=1000
ezlinkDataSource.testOnBorrow=true
ezlinkDataSource.validationQuery=select 1 from dual

#hibernate.dialect=org.hibernate.dialect.SQLServerDialect
hibernate.dialect=org.hibernate.dialect.Oracle10gDialect
#hibernate.dialect=org.hibernate.dialect.MySQLDialect
hibernate.show_sql=false

# web configurations
# ------------------

# timeout period (in seconds)
session.timeoutPeriod=900

packet.size=800


# LDAP config
# -----------
ldap.url=ldap://localhost/o=comfort,c=SG
ldap.managerUser=
ldap.managerPassword=
ldap.groupSearchBase=
ldap.searchBase=CN=Users
ldap.groupRoleAttribute=CN

upload.app.location=webapps/comfort/WEB-INF/app/

default.group.name=DEFAULT

files.directory=/Blacklist/
xml.file.name=PPAppsList.xml
udt.file.name=blacklist.udt

