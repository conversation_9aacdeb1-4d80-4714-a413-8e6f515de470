
# database configurations
# -----------------------
datasource.driverClassName=oracle.jdbc.driver.OracleDriver

datasource.url=jdbc:oracle:thin:@(DESCRIPTION=(ADDRESS_LIST=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521)))(CONNECT_DATA=(SERVICE_NAME=cn2uat.aix61.db.cdgtaxi.com.sg)))
datasource.username=cn2tmapps
datasource.password=cn2tmuatapps
datasource.maxActive=100
datasource.maxIdle=30
datasource.maxWait=1000
datasource.testOnBorrow=true
datasource.validationQuery=select 1 from dual

# ezlink database configurations
ezlinkDataSource.driverClassName=oracle.jdbc.driver.OracleDriver

ezlinkDataSource.url=****************************************
ezlinkDataSource.username=cn2pmapps
ezlinkDataSource.password=cn2pmappsuat
ezlinkDataSource.maxActive=100
ezlinkDataSource.maxIdle=30
ezlinkDataSource.maxWait=1000
ezlinkDataSource.testOnBorrow=true
ezlinkDataSource.validationQuery=select 1 from dual

hibernate.dialect=org.hibernate.dialect.Oracle10gDialect
hibernate.show_sql=false

# web configurations
# ------------------

# timeout period (in seconds)
session.timeoutPeriod=900

packet.size=800

# LDAP config
# -----------
ldap.url=ldap://cdgdc4.cdgtaxi.network:389/dc=cdgtaxi,dc=network
#ldap.managerUser=CN=abladmin,OU=Vendor - ABL,OU=Department,DC=cdgtaxi,DC=network
ldap.managerUser=abladmin
ldap.managerPassword=titpfabluser

#ldap.managerUser=CN=ABL_system_user,CN=Users,DC=abl,DC=com
#ldap.managerPassword=1234Password

ldap.searchBase=
ldap.groupSearchBase=
ldap.groupRoleAttribute=CN

upload.app.location=webapps/comfort/WEB-INF/app/
default.group.name=DEFAULT



