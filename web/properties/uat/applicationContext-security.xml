<?xml version="1.0" encoding="UTF-8"?>
<beans:beans xmlns="http://www.springframework.org/schema/security"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:beans="http://www.springframework.org/schema/beans"
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd
		http://www.springframework.org/schema/security http://www.springframework.org/schema/security/spring-security-3.0.xsd">

	<http access-decision-manager-ref="accessDecisionManager">

		<access-denied-handler error-page="/accessDenied.html" />

		<port-mappings>
			<port-mapping http="8080" https="8443" />
		</port-mappings>

		<!-- Authorization policy -->
		<intercept-url pattern="/ezlink/queryEzlink.html" access="PERMIT_ALL" /> <!-- new -->
		<intercept-url pattern="/" access="IS_AUTHENTICATED_ANONYMOUSLY" />
		<intercept-url pattern="/login.html*" access="IS_AUTHENTICATED_ANONYMOUSLY" />
		<intercept-url pattern="/logoutSuccess.html*" filters="none" />
		<intercept-url pattern="/accessDenied.html*" filters="none" />
		<intercept-url pattern="/sessionTimeout.html*" filters="none" />
		<intercept-url pattern="/error.html*" filters="none" />
		<intercept-url pattern="/resources/**" filters="none" />
		<!-- intercept-url pattern="/**" access="ROLE_TESTGROUP" / -->
		<intercept-url pattern="/**" access="ACCESS_PROFILE" />

		<logout invalidate-session="true" logout-success-url="/login.html" logout-url="/logout.html"/>
		<session-management>
			<concurrency-control max-sessions="1" expired-url="/expired.html"/>
		</session-management>

		<form-login 
			login-page="/login.html" 
			login-processing-url="/login/authenticate" 
			default-target-url="/main.html" 
			authentication-failure-url="/login.html?login_error=1"
		/>

	</http>

	<beans:bean class="org.springframework.security.ldap.DefaultSpringSecurityContextSource" id="contextSource">
		<beans:constructor-arg value="${ldap.url}"/>
		<beans:property name="userDn" value="${ldap.managerUser}"/>
		<beans:property name="password" value="${ldap.managerPassword}"/>
	</beans:bean>

	<authentication-manager alias="authenticationManager">
		<authentication-provider ref="ldapAuthProvider"/>
	</authentication-manager>

	<beans:bean class="com.abl.web.security.AblLdapAuthenticationProvider" id="ldapAuthProvider">
		<beans:constructor-arg ref="ldapBindAuthenticator"/>
		<beans:constructor-arg ref="ldapAuthoritiesPopulator"/>
		<beans:property name="userDetailsContextMapper" ref="ldapUserDetailsContextMapper"/>
	</beans:bean>

	<beans:bean class="org.springframework.security.ldap.authentication.BindAuthenticator" id="ldapBindAuthenticator">
		<beans:constructor-arg ref="contextSource"/>
		<beans:property name="userSearch" ref="ldapSearchBean"/>
	</beans:bean>

	<beans:bean class="org.springframework.security.ldap.search.FilterBasedLdapUserSearch" id="ldapSearchBean">
		<!-- user-search-base; relative to base of configured context source -->
		<beans:constructor-arg value="${ldap.searchBase}"/>
		<!-- user-search-filter -->
		<beans:constructor-arg value="(sAMAccountName={0})"/>
		<beans:constructor-arg ref="contextSource"/>
	</beans:bean>
	
	<beans:bean class="org.springframework.security.ldap.userdetails.DefaultLdapAuthoritiesPopulator" id="ldapAuthoritiesPopulator">
		<beans:constructor-arg ref="contextSource" />				<!-- ldap server -->
		<beans:constructor-arg value="${ldap.groupSearchBase}" />					<!-- search base -->
		<beans:property name="groupRoleAttribute" value="${ldap.groupRoleAttribute}" />		<!-- where to get group name -->
		<beans:property name="convertToUpperCase" value="true"/>	<!-- convert to upper case (and add "ROLE_" in front) -->
		<beans:property name="searchSubtree" value="true"/>
		<beans:property name="ignorePartialResultException" value="true"/>
	</beans:bean>

    <beans:bean class="com.abl.web.security.LdapPersonContextMapper" id="ldapUserDetailsContextMapper"/>

	<beans:bean class="org.springframework.security.web.access.expression.DefaultWebSecurityExpressionHandler" id="expressionHandler"/>

	<beans:bean id="passwordEncoder" class="com.abl.web.security.PasswordEncoderImpl"/>
	<beans:bean id="saltSource" class="com.abl.web.security.SaltSourceImpl"/>

</beans:beans>

