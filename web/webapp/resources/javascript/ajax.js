function createRequestObject() {

    var req;

    if (window.XMLHttpRequest) {
        //For Firefox, Safari, Opera
        req = new XMLHttpRequest();
    }
    else if (window.ActiveXObject) {
        //For IE 5+
        req = new ActiveXObject("Microsoft.XMLHTTP");
    }
    else {
        //Error for an old browser
        alert('Your browser is not IE 5 or higher, or Firefox or Safari or Opera');
    }

    return req;
}

function toggleview(element1) {

    element1 = document.getElementById(element1);

    if (element1.style.display == 'block' || element1.style.display == '')
        element1.style.display = 'none';
    else
        element1.style.display = 'block';

    return;
}

function AJAXInteraction(url, callback) {

    var req = init();
    req.onreadystatechange = processRequest;

    function init() {
        if (window.XMLHttpRequest) {
            return new XMLHttpRequest();
        } else if (window.ActiveXObject) {
            return new ActiveXObject("Microsoft.XMLHTTP");
        }
    }

    function processRequest() {
        //            alert('readystate is '+req.readyState+' status is '+req.status);
        // readyState of 4 signifies request is complete
        if (req.readyState == 4) {

            // status of 200 signifies sucessful HTTP call
            if (req.status == 200) {
                //                    alert('response xml is ' + req.responseText);
                if (callback) callback(req.responseText);
            }
        }
    }

    this.doGet = function() {
        // make a HTTP GET request to the URL asynchronously
        req.open("GET", url, true);
        req.send(null);
    }
}

function AJAXInteraction(url, callback, elemId) {

    var req = init();
    req.onreadystatechange = processRequest;

    function init() {
        if (window.XMLHttpRequest) {
            return new XMLHttpRequest();
        } else if (window.ActiveXObject) {
            return new ActiveXObject("Microsoft.XMLHTTP");
        }
    }

    function processRequest() {
        //            alert('readystate is '+req.readyState+' status is '+req.status);
        // readyState of 4 signifies request is complete
        if (req.readyState == 4) {

            // status of 200 signifies sucessful HTTP call
            if (req.status == 200) {
                //                    alert('response xml is ' + req.responseText);
                if (callback) callback(req.responseText, elemId);
            }
        }
    }

    this.doGet = function() {
        // make a HTTP GET request to the URL asynchronously
        req.open("GET", url, true);
        req.send(null);
    }
}

function getCurrentTime() {
    var my_current_timestamp;
    my_current_timestamp = new Date();		//stamp current date & time
    return my_current_timestamp.getTime();
}

function doNothing() {
}

function checkAll(field) {
    for (i = 0; i < field.length; i++)
        field[i].checked = true;
}

function uncheckAll(field) {
    for (i = 0; i < field.length; i++)
        field[i].checked = false;
}

function trim(s) {
    var l = 0;
    var r = s.length - 1;
    while (l < s.length && (s[l] == ' ' || s[l] == '\n')) {
        l++;
    }
    while (r > l && s[r] == ' ') {
        r -= 1;
    }
    return s.substring(l, r + 1);
}

var iChars = " !@#$%^&*()+=-[]\\\';,./{}\":<>?";
function checkSpecialCharOK(s) {


    for (var i = 0; i < s.length; i++) {
        if (iChars.indexOf(s.charAt(i)) != -1) {
            return false;
        }
    }
}

function stripSpecialChar(s) {

    var retVal = "";
    for (var i = 0; i < s.length; i++) {
        if (iChars.indexOf(s.charAt(i)) != -1) {
            retVal = retVal;
        } else {
            retVal = retVal + s.charAt(i);
        }
    }
    return retVal;
}

function CheckboxNameIsArray(obj) {
    if(obj != null){
    return ((typeof obj.type != "string") && (obj.length > 0) && (obj[0] != null) && (obj[0].type == "checkbox"));
        }else{
        return false;
    }
}

function isURL(urlStr) {
    if (urlStr.indexOf(" ") != -1) {
        alert("Spaces are not allowed in a URL");
        return false;
    }

    if (urlStr == "" || urlStr == null) {
        return true;
    }

    urlStr = urlStr.toLowerCase();

    var specialChars = "\\(\\)><@,;:\\\\\\\"\\.\\[\\]";
    var validChars = "\[^\\s" + specialChars + "\]";
    var atom = validChars + '+';
    var urlPat = /^http:\/\/(\w*)\.([\-\+a-z0-9]*)\.(\w*)/;
    var matchArray = urlStr.match(urlPat);

    if (matchArray == null) {
        alert("The URL seems incorrect \ncheck it begins with http://\n and it has 2 .'s");
        return false;
    }

    var user = matchArray[2];
    var domain = matchArray[3];

    for (i = 0; i < user.length; i++) {
        if (user.charCodeAt(i) > 127) {
            alert("This URL contains invalid characters.");
            return false;
        }
    }

    for (i = 0; i < domain.length; i++) {
        if (domain.charCodeAt(i) > 127) {
            alert("This URL name contains invalid characters.");
            return false;
        }
    }

    var atomPat = new RegExp("^" + atom + "$");
    var domArr = domain.split(".");
    var len = domArr.length;

    for (i = 0; i < len; i++) {
        if (domArr[i].search(atomPat) == -1) {
            alert("The URL name does not seem to be valid.");
            return false;
        }
    }

    return true;
}
