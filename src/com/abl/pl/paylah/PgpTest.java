package com.abl.pl.paylah;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;

import org.apache.log4j.Logger;
import org.bouncycastle.openpgp.PGPPrivateKey;
import org.bouncycastle.openpgp.PGPPublicKey;
import org.bouncycastle.openpgp.PGPSecretKey;
import org.c02e.jpgpj.Key;
import org.c02e.jpgpj.Subkey;

import com.abl.utils.ByteUtils;

/**
 * test program for testing pgp
 *
 */
public class PgpTest {

	private static final Logger logger=Logger.getLogger(PgpTest.class);
	
	private static String pubKeyFile;
	private static String priKeyFile;
	private static String passphrase;
	private static Key pubKey;
	private static Key priKey;
	private static String data;
	private static String file;
	private static String outfile;
	
	public static void main(String[] args) throws Exception {
		try {
			String cmd = parseArgs(args);
			System.out.println("cmd=" + cmd);
			switch (cmd) {
				case "encrypt":
					doEncrypt();
					break;
				case "decrypt":
					doDecrypt();
					break;
				case "check":
					doCheck();
					break;
				case "usage":
				default:
					usage();
					break;
					
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("exception caught", e);
		}
	}
	
	private static void usage() throws Exception {
		System.out.println("usage:");
		System.out.println("    check -file [keyFile]");
		System.out.println("    check -file [keyFile] -pass [passphrase]");
		System.out.println("    encrypt -pubkey [pubkey] -prikey [prikey] -pass [passphrase] [data]");
		System.out.println("    encrypt -pubkey [pubkey] -prikey [prikey] -pass [passphrase] -file [infile]");
		System.out.println("    encrypt -pubkey [pubkey] -prikey [prikey] -pass [passphrase] -out [outfile] \"hello\"");
		System.out.println("    decrypt -pubkey [pubkey] -prikey [prikey] -pass [passphrase] [data]");
		System.out.println("    decrypt -pubkey [pubkey] -prikey [prikey] -pass [passphrase] -file [infile]");
	}
	
	private static void doEncrypt() throws Exception {
		if (pubKey == null) {
			System.out.println("error: pubKey is null");
			return;
		}
		if (file != null) {
			data = new String(readFromFile(file));
		}
		if (data == null) {
			System.out.println("error: data is null");
			return;
		}
		logger.debug("encrypting (ascii) " + data);
		byte[] result = PgpUtil.signAndEncryptData(priKey, pubKey, data.getBytes(), true);
		if (outfile == null) {
			System.out.println("result is " + ByteUtils.bytesToHex(result));
		} else {
			writeToFile(outfile, result);
		}
	}
	
	private static void writeToFile(String filename, byte[] data) throws Exception {
		File file = new File(filename);
		try (OutputStream out = new FileOutputStream(file)) {
			out.write(data);
		}
	}
	
	private static byte[] readFromFile(String filename) throws Exception {
		File file = new File(filename);
		byte[] data = new byte[(int)file.length()];
		try (InputStream in = new FileInputStream(file)) {
			int n = in.read(data);
			return ByteUtils.subArray(data, 0, n);
		}
	}
	
	private static void doDecrypt() throws Exception {
		if (priKey == null) {
			System.out.println("error: priKey is null");
			return;
		}
		byte[] bytes;
		if (file != null) {
			bytes = readFromFile(file);
			data = ByteUtils.bytesToHex(bytes);
		} else if (data != null) {
			bytes = ByteUtils.hexToBytes(data);
		} else {
			System.out.println("error: data is null");
			return;
		}
		logger.debug("decrypting (hex) " + data);
		byte[] result = PgpUtil.decryptAndVerifyData(pubKey, priKey, bytes);
		if (outfile == null) {
			System.out.println("result is " + ByteUtils.bytesToHex(result));
		} else {
			writeToFile(outfile, result);
		}
	}
	
	private static void doCheck() throws Exception {
		checkKeyFile(new File(file), passphrase);
	}
	
	// return cmd
	public static String parseArgs(String[] args) throws Exception {
		String cmd=null;
		for (int n=0; n<args.length; n++) {
			String arg = args[n];
			if (!arg.startsWith("-")) {
				if (cmd == null) {
					cmd = arg;
				} else if (data == null) {
					data = arg;
				}
				continue;
			}
			
			switch (arg.toLowerCase()) {
				case "-pubkey":
					pubKeyFile = args[++n];
					break;
				case "-prikey":
					priKeyFile = args[++n];
					break;
				case "-pass":
					passphrase = args[++n];
					break;
				case "-file":
					file = args[++n];
					break;
				case "-out":
					outfile = args[++n];
					break;
				case "-data":
					data = args[++n];
					break;
				default:
					System.out.println("unknown flag " + arg);
					break;
			}
		}
		
		if (pubKeyFile != null) {
			pubKey = PgpUtil.getKeyFromFile(new File(pubKeyFile));
		}
		
		if (priKeyFile != null) {
			if (passphrase == null) {
				passphrase = "passphrase";
			}
			priKey = PgpUtil.getKeyFromFile(new File(priKeyFile), passphrase);
		}
		
		return cmd;
	}
	
	private static void checkKeyFile(File file, String passphrase) {
		System.out.println("checkKeyFile, file=" + file);
		try {
			Key key;
			if (passphrase == null) {
				key = PgpUtil.getKeyFromFile(file);
			} else {
				key = PgpUtil.getKeyFromFile(file, passphrase);
			}
			for (Subkey subkey: key.getSubkeys()) {
				System.out.println("checkKeyFile: subkey=" + subkey + ", id=" + subkey.getId() + ", fingerprint=" + subkey.getFingerprint());
				System.out.println("    flags:");
				System.out.println("      isForEncryption=" + subkey.isForEncryption());
				System.out.println("      isForDecryption=" + subkey.isForDecryption());
				System.out.println("      isForSigning=" + subkey.isForSigning());
				System.out.println("      isForVerification=" + subkey.isForVerification());
				
				PGPPublicKey pubKey = subkey.getPublicKey();
				System.out.println("    publicKey=" + pubKey);
				if (pubKey != null) {
					System.out.println("      id=" + String.format("%X", pubKey.getKeyID()));
				}
				
				PGPPrivateKey priKey = subkey.getPrivateKey();
				System.out.println("    privateKey=" + priKey);
				if (priKey != null) {
					System.out.println("      id=" + String.format("%X", priKey.getKeyID()));
				}
				
				PGPSecretKey secretKey = subkey.getSecretKey();
				System.out.println("    secretKey=" + secretKey);
				if (secretKey != null) {
					System.out.println("      id=" + String.format("%X", secretKey.getKeyID()));
				}
				
			}
		} catch (Exception e) {
			logger.error("exception caught", e);
			e.printStackTrace();
		}
		System.out.println("checkKeyFile done");
	}

}
