package com.abl.pl.paylah;

@SuppressWarnings("serial")
public class PlHttpException extends Exception {

	private Integer httpStatusCode;
	
	public PlHttpException() {
		super();
	}

	public PlHttpException(String message, Throwable cause) {
		super(message, cause);
	}

	public PlHttpException(String message) {
		super(message);
	}

	public PlHttpException(Throwable cause) {
		super(cause);
	}

	public PlHttpException(Integer httpStatusCode) {
		super();
		this.httpStatusCode = httpStatusCode;
	}

	public PlHttpException(String message, Integer httpStatusCode, Throwable cause) {
		super(message, cause);
		this.httpStatusCode = httpStatusCode;
	}

	public PlHttpException(String message, Integer httpStatusCode) {
		super(message);
		this.httpStatusCode = httpStatusCode;
	}

	public PlHttpException(Integer httpStatusCode, Throwable cause) {
		super(cause);
		this.httpStatusCode = httpStatusCode;
	}

	public Integer getHttpStatusCode() {
		return httpStatusCode;
	}

	public void setHttpStatusCode(Integer httpStatusCode) {
		this.httpStatusCode = httpStatusCode;
	}
	
}
