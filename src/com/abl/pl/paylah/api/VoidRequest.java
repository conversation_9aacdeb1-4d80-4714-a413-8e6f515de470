package com.abl.pl.paylah.api;

import com.google.gson.annotations.Expose;

public class VoidRequest implements PaylahRequest{
	@Expose
	private Header header;
	@Expose
	private VoidTxnInfo txnInfo;

	@Override
	public void setHeader(Header header) {
		this.header = header;
		
	}

	@Override
	public Header getHeader() {
		return this.header;
	}
	

	public VoidTxnInfo getTxnInfo() {
		return txnInfo;
	}

	public void setTxnInfo(VoidTxnInfo txnInfo) {
		this.txnInfo = txnInfo;
	}
	
	

}
