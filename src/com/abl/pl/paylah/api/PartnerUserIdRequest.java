package com.abl.pl.paylah.api;

import com.google.gson.annotations.Expose;

public class PartnerUserIdRequest implements PaylahRequest{
	
	@Expose
	private Header header;
	
	@Expose
	private PartnerUserTxnInfo txnInfo;
	

	@Override
	public void setHeader(Header header) {
		this.header = header;
		
	}

	@Override
	public Header getHeader() {
		return this.header;
	}

	public PartnerUserTxnInfo getTxnInfo() {
		return txnInfo;
	}

	public void setTxnInfo(PartnerUserTxnInfo txnInfo) {
		this.txnInfo = txnInfo;
	}

	@Override
	public String toString() {
		return "PartnerUserIdRequest [header=" + header + ", txnInfo=" + txnInfo + "]";
	}
	
	
	

}
