package com.abl.pl.paylah.api;

import com.google.gson.annotations.Expose;

public class PartnerUserIdTxnResponseInfo {
	@Expose
	private String txnStatus;
	@Expose
	private String txnStatusCode;
	@Expose
	private String txnStatusDescription;
	

	public String getTxnStatus() {
		return txnStatus;
	}

	public void setTxnStatus(String txnStatus) {
		this.txnStatus = txnStatus;
	}

	public String getTxnStatusCode() {
		return txnStatusCode;
	}

	public void setTxnStatusCode(String txnStatusCode) {
		this.txnStatusCode = txnStatusCode;
	}

	public String getTxnStatusDescription() {
		return txnStatusDescription;
	}

	public void setTxnStatusDescription(String txnStatusDescription) {
		this.txnStatusDescription = txnStatusDescription;
	}

	@Override
	public String toString() {
		return "PartnerUserIdTxnResponseInfo [txnStatus=" + txnStatus + ", txnStatusCode=" + txnStatusCode
				+ ", txnStatusDescription=" + txnStatusDescription + "]";
	}
	
	

}
