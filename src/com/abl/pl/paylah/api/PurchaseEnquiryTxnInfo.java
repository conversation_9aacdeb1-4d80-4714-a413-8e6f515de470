package com.abl.pl.paylah.api;

import com.google.gson.annotations.Expose;

public class PurchaseEnquiryTxnInfo {
	
	public static final String TXN_TYPE_EC_SETUP="E";
	public static final String TXN_TYPE_SALE="O";
	
	@Expose private String txnType;	// E - EC Setup, O=app/web request and purchase
	@Expose private String txnMsgId;
	@Expose private String originalMsgId;

	public String getTxnType() {
		return txnType;
	}

	public void setTxnType(String txnType) {
		this.txnType = txnType;
	}

	public String getTxnMsgId() {
		return txnMsgId;
	}

	public void setTxnMsgId(String txnMsgId) {
		this.txnMsgId = txnMsgId;
	}

	public String getOriginalMsgId() {
		return originalMsgId;
	}

	public void setOriginalMsgId(String originalMsgId) {
		this.originalMsgId = originalMsgId;
	}
	
}
