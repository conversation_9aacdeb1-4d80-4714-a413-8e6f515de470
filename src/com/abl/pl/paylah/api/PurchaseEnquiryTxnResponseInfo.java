package com.abl.pl.paylah.api;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

/**
 * TxnResponse for purchase enquiry.
 * 
 * note that the response fields are different, depending on whether the request.txnType is E or O.
 * 
 * if the specific E-or-O fields are not present, assume the enquiry has failed;
 * else we assume that the enquiry is successful, and the txnStatus refers to the status of the enquired transaction
 *
 */
public class PurchaseEnquiryTxnResponseInfo extends TxnResponseInfo {
	
	public static final String SETUP_STATUS_ENABLED="Enabled";
	public static final String SETUP_STATUS_DISABLED="Disabled";
	public static final String SETUP_STATUS_EXPIRED="Expired";
	public static final String SETUP_STATUS_PENDING="Pending";

	
	// these are for purchase enquiry (i.e. request.txnType=O)
	@Expose private String txnType;	// Checkout, Refund
	@Expose private String txnSource;	// 01-web, 02-app, 03-paylah app, 04-eca, 05-ecw
	@Expose private Float txnAmount;	// in dollars, with 2 decimal
	@Expose private String txnRefId;
	@Expose private Float remainingAmount;
	@Expose private String approvalCode;
	@Expose private String originalMsgId;
	@Expose private String originalBankTxnRefId;
	@Expose private String payeeShippingAddress;
	@Expose private Address address;
	@Expose private String paymentDetails;
	@Expose private String qrCode;
	
	// these are for ecSetup enquiry (i.e. request.txnType=E)
	@Expose @SerializedName("setuptimeStamp") private String setupTimeStamp;
	@Expose private String setupStatus;	// 1-enabled, 2-disabled, 3-expired, 4-pending
	@Expose private String ecSetupId;

	public String getTxnType() {
		return txnType;
	}

	public void setTxnType(String txnType) {
		this.txnType = txnType;
	}

	public String getTxnSource() {
		return txnSource;
	}

	public void setTxnSource(String txnSource) {
		this.txnSource = txnSource;
	}

	public Float getTxnAmount() {
		return txnAmount;
	}

	public void setTxnAmount(Float txnAmount) {
		this.txnAmount = txnAmount;
	}

	public String getTxnRefId() {
		return txnRefId;
	}

	public void setTxnRefId(String txnRefId) {
		this.txnRefId = txnRefId;
	}

	public Float getRemainingAmount() {
		return remainingAmount;
	}

	public void setRemainingAmount(Float remainingAmount) {
		this.remainingAmount = remainingAmount;
	}

	public String getApprovalCode() {
		return approvalCode;
	}

	public void setApprovalCode(String approvalCode) {
		this.approvalCode = approvalCode;
	}

	public String getOriginalMsgId() {
		return originalMsgId;
	}

	public void setOriginalMsgId(String originalMsgId) {
		this.originalMsgId = originalMsgId;
	}

	public String getOriginalBankTxnRefId() {
		return originalBankTxnRefId;
	}

	public void setOriginalBankTxnRefId(String originalBankTxnRefId) {
		this.originalBankTxnRefId = originalBankTxnRefId;
	}

	public String getPayeeShippingAddress() {
		return payeeShippingAddress;
	}

	public void setPayeeShippingAddress(String payeeShippingAddress) {
		this.payeeShippingAddress = payeeShippingAddress;
	}

	public Address getAddress() {
		return address;
	}

	public void setAddress(Address address) {
		this.address = address;
	}

	public String getPaymentDetails() {
		return paymentDetails;
	}

	public void setPaymentDetails(String paymentDetails) {
		this.paymentDetails = paymentDetails;
	}

	public String getQrCode() {
		return qrCode;
	}

	public void setQrCode(String qrCode) {
		this.qrCode = qrCode;
	}

	public String getSetupTimeStamp() {
		return setupTimeStamp;
	}

	public void setSetupTimeStamp(String setupTimeStamp) {
		this.setupTimeStamp = setupTimeStamp;
	}

	public String getSetupStatus() {
		return setupStatus;
	}

	public void setSetupStatus(String setupStatus) {
		this.setupStatus = setupStatus;
	}

	public String getEcSetupId() {
		return ecSetupId;
	}

	public void setEcSetupId(String ecSetupId) {
		this.ecSetupId = ecSetupId;
	}

}
