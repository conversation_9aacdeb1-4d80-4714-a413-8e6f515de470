package com.abl.pl.paylah.api;

import com.google.gson.annotations.Expose;

public class Header {
	@Expose private String msgId;	// ANS 20
	@Expose private String orgId;	// AN 12
	@Expose private String timeStamp; // yyyy-MM-ddTHHmmss.sss

	public String getMsgId() {
		return msgId;
	}

	public void setMsgId(String msgId) {
		this.msgId = msgId;
	}

	public String getOrgId() {
		return orgId;
	}

	public void setOrgId(String orgId) {
		this.orgId = orgId;
	}

	public String getTimeStamp() {
		return timeStamp;
	}

	public void setTimeStamp(String timeStamp) {
		this.timeStamp = timeStamp;
	}

	@Override
	public String toString() {
		return "Header [msgId=" + msgId + ", orgId=" + orgId + ", timeStamp=" + timeStamp + "]";
	}
	
	
	
}
