package com.abl.pl.paylah.api;

import com.google.gson.annotations.Expose;

public class CaptureTxnInfo {
	
	@Expose
	private String txnMsgId;
	
	@Expose
	private String txnCcy;
	
	@Expose
	private String txnAmount;
	
	@Expose
	private String originalTxnAmount;
	
	@Expose
	private String originalMsgId;
	
	@Expose
	private String originalTxnRefId;
	
	@Expose
	private String txnSource;

	public String getTxnMsgId() {
		return txnMsgId;
	}

	public void setTxnMsgId(String txnMsgId) {
		this.txnMsgId = txnMsgId;
	}

	public String getTxnCcy() {
		return txnCcy;
	}

	public void setTxnCcy(String txnCcy) {
		this.txnCcy = txnCcy;
	}

	public String getTxnAmount() {
		return txnAmount;
	}

	public void setTxnAmount(String txnAmount) {
		this.txnAmount = txnAmount;
	}

	public String getOriginalTxnAmount() {
		return originalTxnAmount;
	}

	public void setOriginalTxnAmount(String originalTxnAmount) {
		this.originalTxnAmount = originalTxnAmount;
	}

	public String getOriginalMsgId() {
		return originalMsgId;
	}

	public void setOriginalMsgId(String originalMsgId) {
		this.originalMsgId = originalMsgId;
	}

	public String getOriginalTxnRefId() {
		return originalTxnRefId;
	}

	public void setOriginalTxnRefId(String originalTxnRefId) {
		this.originalTxnRefId = originalTxnRefId;
	}

	public String getTxnSource() {
		return txnSource;
	}

	public void setTxnSource(String txnSource) {
		this.txnSource = txnSource;
	}

	@Override
	public String toString() {
		return "CaptureTxnInfo [txnMsgId=" + txnMsgId + ", txnCcy=" + txnCcy + ", txnAmount=" + txnAmount
				+ ", originalTxnAmount=" + originalTxnAmount + ", originalMsgId=" + originalMsgId
				+ ", originalTxnRefId=" + originalTxnRefId + ", txnSource=" + txnSource + "]";
	}
	
	
	

}
