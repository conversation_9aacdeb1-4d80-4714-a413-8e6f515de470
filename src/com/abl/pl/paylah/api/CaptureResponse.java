package com.abl.pl.paylah.api;

import com.google.gson.annotations.Expose;

public class CaptureResponse implements PaylahResponse{
	@Expose 
	private Header header;
	
	@Expose 
	private CaptureTxnResponseInfo txnResponse;
	
	@Expose
	private CaptureTxnResponseInfo txnInfo;
	
	@Expose 
	private CaptureTxnResponseInfo enqResponse;
	
	
	public Header getHeader() {
		return header;
	}
	public void setHeader(Header header) {
		this.header = header;
	}
	public CaptureTxnResponseInfo getTxnResponse() {
		return txnResponse;
	}
	public void setTxnResponse(CaptureTxnResponseInfo txnResponse) {
		this.txnResponse = txnResponse;
	}
	
	
	public CaptureTxnResponseInfo getTxnInfo() {
		return txnInfo;
	}
	public void setTxnInfo(CaptureTxnResponseInfo txnInfo) {
		this.txnInfo = txnInfo;
	}
	public CaptureTxnResponseInfo getEnqResponse() {
		return enqResponse;
	}
	public void setEnqResponse(CaptureTxnResponseInfo enqResponse) {
		this.enqResponse = enqResponse;
	}
	
	@Override
	public String toString() {
		return "CaptureResponse [header=" + header + ", txnResponse=" + txnResponse + ", txnInfo=" + txnInfo
				+ ", enqResponse=" + enqResponse + "]";
	}
	
	
	

}
