package com.abl.pl.paylah.api;

import com.google.gson.annotations.Expose;

public class VoidResponse implements PaylahResponse{
	@Expose 
	private Header header;
	@Expose 	
	private VoidTxnResponseInfo txnResponse;
	@Expose 	
	private VoidTxnResponseInfo txnInfo;
	@Expose 	
	private VoidTxnResponseInfo enqResponse;
	

	public Header getHeader() {
		return header;
	}

	public void setHeader(Header header) {
		this.header = header;
	}

	public VoidTxnResponseInfo getTxnResponse() {
		return txnResponse;
	}

	public void setTxnResponse(VoidTxnResponseInfo txnResponse) {
		this.txnResponse = txnResponse;
	}

	public VoidTxnResponseInfo getTxnInfo() {
		return txnInfo;
	}

	public void setTxnInfo(VoidTxnResponseInfo txnInfo) {
		this.txnInfo = txnInfo;
	}

	public VoidTxnResponseInfo getEnqResponse() {
		return enqResponse;
	}

	public void setEnqResponse(VoidTxnResponseInfo enqResponse) {
		this.enqResponse = enqResponse;
	}

	@Override
	public String toString() {
		return "VoidResponse [header=" + header + ", txnResponse=" + txnResponse + ", txnInfo=" + txnInfo
				+ ", enqResponse=" + enqResponse + "]";
	}
	
	
	

}
