package com.abl.pl.paylah.api;

import com.google.gson.annotations.Expose;

public class WebCheckoutRequest implements PaylahRequest {
	@Expose private Header header;
	@Expose private WebCheckoutTxnInfo txnInfo;
	
	@Override
	public Header getHeader() {
		return header;
	}

	@Override
	public void setHeader(Header header) {
		this.header = header;
	}

	public WebCheckoutTxnInfo getTxnInfo() {
		return txnInfo;
	}

	public void setTxnInfo(WebCheckoutTxnInfo txnInfo) {
		this.txnInfo = txnInfo;
	}
	
}
