package com.abl.pl.paylah.api;

import com.google.gson.annotations.Expose;

public class PartnerUserIdResponse implements PaylahResponse{
	@Expose
	private Header header;
	@Expose
	private PartnerUserIdTxnResponseInfo txnResponse;
	
	@Expose
	private PartnerUserIdTxnResponseInfo enqResponse;
	
	
	public void setHeader(Header header) {
		this.header = header;
		
	}
	
	public Header getHeader() {
		return this.header;
	}
	
	
	public PartnerUserIdTxnResponseInfo getTxnResponse() {
		return txnResponse;
	}
	public void setTxnResponse(PartnerUserIdTxnResponseInfo txnResponse) {
		this.txnResponse = txnResponse;
	}

	@Override
	public String toString() {
		return "PartnerUserIdResponse [header=" + header + ", txnResponse=" + txnResponse + "]";
	}

	public PartnerUserIdTxnResponseInfo getEnqResponse() {
		return enqResponse;
	}

	public void setEnqResponse(PartnerUserIdTxnResponseInfo enqResponse) {
		this.enqResponse = enqResponse;
	}
	
	
	
	
	
}
