package com.abl.pl.paylah.api;

import com.google.gson.annotations.Expose;

public class UserInfoResponse implements PaylahResponse {
	@Expose
	private Header header;
	
	@Expose
	private UserTxnResponseInfo txnResponse;
	
	@Expose
	private UserTxnResponseInfo txnInfo;
	
	@Expose
	private UserTxnResponseInfo enqResponse;
	
	
	

	public Header getHeader() {
		return header;
	}

	public void setHeader(Header header) {
		this.header = header;
	}

	public UserTxnResponseInfo getTxnResponse() {
		return txnResponse;
	}

	public void setTxnResponse(UserTxnResponseInfo txnResponse) {
		this.txnResponse = txnResponse;
	}

	public UserTxnResponseInfo getTxnInfo() {
		return txnInfo;
	}

	public void setTxnInfo(UserTxnResponseInfo txnInfo) {
		this.txnInfo = txnInfo;
	}

	public UserTxnResponseInfo getEnqResponse() {
		return enqResponse;
	}

	public void setEnqResponse(UserTxnResponseInfo enqResponse) {
		this.enqResponse = enqResponse;
	}

	@Override
	public String toString() {
		return "UserInfoResponse [header=" + header + ", txnResponse=" + txnResponse + ", txnInfo=" + txnInfo
				+ ", enqResponse=" + enqResponse + "]";
	}
	
	
	
	
}
