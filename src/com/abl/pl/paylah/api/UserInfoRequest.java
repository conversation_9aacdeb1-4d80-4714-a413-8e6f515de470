package com.abl.pl.paylah.api;

import com.google.gson.annotations.Expose;

public class UserInfoRequest implements PaylahRequest {
	
	@Expose
	private Header header;
	
	@Expose
	private UserTxnInfo txnInfo;
	

	@Override
	public void setHeader(Header header) {
		this.header = header;
	}

	@Override
	public Header getHeader() {
		return this.header;
	}

	public UserTxnInfo getTxnInfo() {
		return txnInfo;
	}

	public void setTxnInfo(UserTxnInfo txnInfo) {
		this.txnInfo = txnInfo;
	}

	@Override
	public String toString() {
		return "UserInfoRequest [header=" + header + ", txnInfo=" + txnInfo + "]";
	}

}
