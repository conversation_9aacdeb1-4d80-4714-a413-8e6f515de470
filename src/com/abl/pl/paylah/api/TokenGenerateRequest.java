package com.abl.pl.paylah.api;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class TokenGenerateRequest{
	
	@Expose @SerializedName("grant_type")
	private String grantType;
	
	@Expose
	private String code;
	
	@Expose @SerializedName("redirect_uri")
	private String redirectUri;
	
	@Expose
	private String uuid;
	
	

	public String getGrantType() {
		return grantType;
	}

	public void setGrantType(String grantType) {
		this.grantType = grantType;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getRedirectUri() {
		return redirectUri;
	}

	public void setRedirectUri(String redirectUri) {
		this.redirectUri = redirectUri;
	}

	public String getUuid() {
		return uuid;
	}

	public void setUuid(String uuid) {
		this.uuid = uuid;
	}

	@Override
	public String toString() {
		return "grant_type=" + grantType + "&code=" + code + "&redirect_uri=" + redirectUri + "&uuid=" + uuid;
	}

	

}
