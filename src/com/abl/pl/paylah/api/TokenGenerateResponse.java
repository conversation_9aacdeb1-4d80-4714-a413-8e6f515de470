package com.abl.pl.paylah.api;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class TokenGenerateResponse implements PaylahResponse {
	
	@Expose @SerializedName("access_token")
	private String accessToken;
	
	@Expose
	private String type;
	
	@Expose @SerializedName("expires_in")
	private String expiresIn;
	
	@Expose
	private String scope;
	
	@Expose
	private String respStatus;
	
	@Expose
	private String respStatusDescription;
	
	@Expose
	private String respRejectCode;
	
	@Expose
	private String respId;

	public String getAccessToken() {
		return accessToken;
	}

	public void setAccessToken(String accessToken) {
		this.accessToken = accessToken;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getExpiresIn() {
		return expiresIn;
	}

	public void setExpiresIn(String expiresIn) {
		this.expiresIn = expiresIn;
	}

	public String getScope() {
		return scope;
	}

	public void setScope(String scope) {
		this.scope = scope;
	}

	public String getRespStatus() {
		return respStatus;
	}

	public void setRespStatus(String respStatus) {
		this.respStatus = respStatus;
	}

	public String getRespStatusDescription() {
		return respStatusDescription;
	}

	public void setRespStatusDescription(String respStatusDescription) {
		this.respStatusDescription = respStatusDescription;
	}

	public String getRespRejectCode() {
		return respRejectCode;
	}

	public void setRespRejectCode(String respRejectCode) {
		this.respRejectCode = respRejectCode;
	}

	public String getRespId() {
		return respId;
	}

	public void setRespId(String respId) {
		this.respId = respId;
	}
			

}
