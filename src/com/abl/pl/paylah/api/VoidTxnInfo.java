package com.abl.pl.paylah.api;

import com.google.gson.annotations.Expose;

public class VoidTxnInfo {
	@Expose
	private String txnMsgId;
	@Expose
	private String originalTxnAmount;
	@Expose
	private String originalMsgId;
	@Expose
	private String originalTxnRefId;

	public String getTxnMsgId() {
		return txnMsgId;
	}

	public void setTxnMsgId(String txnMsgId) {
		this.txnMsgId = txnMsgId;
	}

	

	public String getOriginalTxnAmount() {
		return originalTxnAmount;
	}

	public void setOriginalTxnAmount(String originalTxnAmount) {
		this.originalTxnAmount = originalTxnAmount;
	}

	public String getOriginalMsgId() {
		return originalMsgId;
	}

	public void setOriginalMsgId(String originalMsgId) {
		this.originalMsgId = originalMsgId;
	}

	public String getOriginalTxnRefId() {
		return originalTxnRefId;
	}

	public void setOriginalTxnRefId(String originalTxnRefId) {
		this.originalTxnRefId = originalTxnRefId;
	}

	@Override
	public String toString() {
		return "VoidTnxInfo [txnMsgId=" + txnMsgId + ", originaltxnAmount=" + originalTxnAmount + ", originalMsgId="
				+ originalMsgId + ", originalTxnRefId=" + originalTxnRefId + "]";
	}
	
	
	

}
