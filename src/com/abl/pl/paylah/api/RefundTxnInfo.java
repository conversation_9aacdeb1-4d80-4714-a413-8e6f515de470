package com.abl.pl.paylah.api;

import com.google.gson.annotations.Expose;

public class RefundTxnInfo {
	@Expose private String txnMsgId;
	@Expose private String txnSource;	// 01-web, 02-app, 03-paylah app, 04-eca, 05-ecw
	@Expose private String txnType;	// R
	@Expose private String txnCcy;	// SGD
	@Expose private String txnAmount;	// in dollars, with 2 decimal
	@Expose private String returnUrl;	// optional, just set null
	@Expose private String phoneNumber;	// for P, is 8 digit phone number; for E is 20 digit ecSetupId
	@Expose private String originalMsgId;
	@Expose private String originalTxnRefId;
	@Expose private String payeeShippingAddress;	// NA
	@Expose private Address address; // pass empty string inside
	@Expose private TxnDetails rmtInf;	// set qrCode to empty string

	public String getTxnMsgId() {
		return txnMsgId;
	}

	public void setTxnMsgId(String txnMsgId) {
		this.txnMsgId = txnMsgId;
	}

	public String getTxnSource() {
		return txnSource;
	}

	public void setTxnSource(String txnSource) {
		this.txnSource = txnSource;
	}

	public String getTxnType() {
		return txnType;
	}

	public void setTxnType(String txnType) {
		this.txnType = txnType;
	}

	public String getTxnCcy() {
		return txnCcy;
	}

	public void setTxnCcy(String txnCcy) {
		this.txnCcy = txnCcy;
	}

	public String getTxnAmount() {
		return txnAmount;
	}

	public void setTxnAmount(String txnAmount) {
		this.txnAmount = txnAmount;
	}

	public String getReturnUrl() {
		return returnUrl;
	}

	public void setReturnUrl(String returnUrl) {
		this.returnUrl = returnUrl;
	}

	public String getPhoneNumber() {
		return phoneNumber;
	}

	public void setPhoneNumber(String phoneNumber) {
		this.phoneNumber = phoneNumber;
	}

	public String getOriginalMsgId() {
		return originalMsgId;
	}

	public void setOriginalMsgId(String originalMsgId) {
		this.originalMsgId = originalMsgId;
	}

	public String getOriginalTxnRefId() {
		return originalTxnRefId;
	}

	public void setOriginalTxnRefId(String originalTxnRefId) {
		this.originalTxnRefId = originalTxnRefId;
	}

	public String getPayeeShippingAddress() {
		return payeeShippingAddress;
	}

	public void setPayeeShippingAddress(String payeeShippingAddress) {
		this.payeeShippingAddress = payeeShippingAddress;
	}

	public Address getAddress() {
		return address;
	}

	public void setAddress(Address address) {
		this.address = address;
	}

	public TxnDetails getRmtInf() {
		return rmtInf;
	}

	public void setRmtInf(TxnDetails rmtInf) {
		this.rmtInf = rmtInf;
	}
	
}
