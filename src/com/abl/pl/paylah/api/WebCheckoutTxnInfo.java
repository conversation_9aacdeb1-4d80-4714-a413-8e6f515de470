package com.abl.pl.paylah.api;

import com.google.gson.annotations.Expose;

public class WebCheckoutTxnInfo {
	@Expose private String txnMsgId;
	@Expose private String txnSource;	// 01-web, 02-paylah app, 03-ecw
	@Expose private String txnType;	// P-web checkout, E-express checkout
	@Expose private String txnCcy;	// SGD
	@Expose private String txnAmount;	// in dollars, with 2 decimal
	@Expose private String returnUrl;	// optional, just set null
	@Expose private String phoneNumber;	// for P, is 8 digit phone number; for E is 20 digit ecSetupId
	@Expose private String payeeShippingAddress;	// Y=partner to pass address; N=paylah to get user address; NA = not applicable. should be NA
	@Expose private Address address;
	@Expose private TxnDetails rmtInf;

	public String getTxnMsgId() {
		return txnMsgId;
	}

	public void setTxnMsgId(String txnMsgId) {
		this.txnMsgId = txnMsgId;
	}

	public String getTxnSource() {
		return txnSource;
	}

	public void setTxnSource(String txnSource) {
		this.txnSource = txnSource;
	}

	public String getTxnType() {
		return txnType;
	}

	public void setTxnType(String txnType) {
		this.txnType = txnType;
	}

	public String getTxnCcy() {
		return txnCcy;
	}

	public void setTxnCcy(String txnCcy) {
		this.txnCcy = txnCcy;
	}

	public String getTxnAmount() {
		return txnAmount;
	}

	public void setTxnAmount(String txnAmount) {
		this.txnAmount = txnAmount;
	}

	public String getReturnUrl() {
		return returnUrl;
	}

	public void setReturnUrl(String returnUrl) {
		this.returnUrl = returnUrl;
	}

	public String getPhoneNumber() {
		return phoneNumber;
	}

	public void setPhoneNumber(String phoneNumber) {
		this.phoneNumber = phoneNumber;
	}

	public String getPayeeShippingAddress() {
		return payeeShippingAddress;
	}

	public void setPayeeShippingAddress(String payeeShippingAddress) {
		this.payeeShippingAddress = payeeShippingAddress;
	}

	public Address getAddress() {
		return address;
	}

	public void setAddress(Address address) {
		this.address = address;
	}

	public TxnDetails getRmtInf() {
		return rmtInf;
	}

	public void setRmtInf(TxnDetails rmtInf) {
		this.rmtInf = rmtInf;
	}
	
}
