package com.abl.pl.paylah.api;

import com.google.gson.annotations.Expose;

public class EcSetupTxnInfo {
	@Expose private String txnSource="04";	// 04=ECA (express checkout app), 05=ECW (express checkout web)
	@Expose private String phoneNumber;	// for P, is 8 digit phone number; for E is 20 digit ecSetupId
	@Expose private String returnUrl;	// mobile app url scheme

	public String getTxnSource() {
		return txnSource;
	}

	public void setTxnSource(String txnSource) {
		this.txnSource = txnSource;
	}

	public String getPhoneNumber() {
		return phoneNumber;
	}

	public void setPhoneNumber(String phoneNumber) {
		this.phoneNumber = phoneNumber;
	}

	public String getReturnUrl() {
		return returnUrl;
	}

	public void setReturnUrl(String returnUrl) {
		this.returnUrl = returnUrl;
	}
	
}
