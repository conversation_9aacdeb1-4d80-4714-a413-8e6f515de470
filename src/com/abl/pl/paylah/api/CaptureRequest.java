package com.abl.pl.paylah.api;

import com.google.gson.annotations.Expose;

public class CaptureRequest implements PaylahRequest{
	
	@Expose
	private Header header;
	
	@Expose
	private CaptureTxnInfo txnInfo;

	@Override
	public void setHeader(Header header) {
		this.header = header;
		
	}

	@Override
	public Header getHeader() {
		return this.header;
	}

	public CaptureTxnInfo getTxnInfo() {
		return txnInfo;
	}

	public void setTxnInfo(CaptureTxnInfo txnInfo) {
		this.txnInfo = txnInfo;
	}
	
	

}
