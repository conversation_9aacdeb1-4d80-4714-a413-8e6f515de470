package com.abl.pl.paylah.api;

import com.google.gson.annotations.Expose;

public class PreAuthResponse implements PaylahResponse {
	@Expose
	private Header header;
	
	@Expose
	private PreAuthTxnResponseInfo txnResponse;
	
	
	@Expose
	private PreAuthTxnResponseInfo txnInfo;
	
	@Expose 
	private PreAuthTxnResponseInfo enqResponse;
	
	

	public Header getHeader() {
		return header;
	}

	public void setHeader(Header header) {
		this.header = header;
	}

	public PreAuthTxnResponseInfo getTxnResponse() {
		return txnResponse;
	}

	public void setTxnResponse(PreAuthTxnResponseInfo txnResponse) {
		this.txnResponse = txnResponse;
	}


	public PreAuthTxnResponseInfo getTxnInfo() {
		return txnInfo;
	}

	public void setTxnInfo(PreAuthTxnResponseInfo txnInfo) {
		this.txnInfo = txnInfo;
	}

	

	public PreAuthTxnResponseInfo getEnqResponse() {
		return enqResponse;
	}

	public void setEnqResponse(PreAuthTxnResponseInfo enqResponse) {
		this.enqResponse = enqResponse;
	}

	@Override
	public String toString() {
		return "PreAuthResponse [header=" + header + ", txnResponse=" + txnResponse + ", txnInfo=" + txnInfo
				+ ", enqResponse=" + enqResponse + "]";
	}
	
	
	
}
