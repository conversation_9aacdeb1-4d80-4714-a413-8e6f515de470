package com.abl.pl.paylah.api;

import com.google.gson.annotations.Expose;

public class UserTxnResponseInfo extends TxnResponseInfo{
	@Expose
	private String mandateId;
	
	@Expose
	private String name;
	
	@Expose
	private String dob;
	
	@Expose
	private String nationality;
	
	@Expose
	private String phoneNumber;
	
	@Expose
	private String email;
	
	@Expose
	private String cinSuffix;

	public String getMandateId() {
		return mandateId;
	}

	public void setMandateId(String mandateId) {
		this.mandateId = mandateId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDob() {
		return dob;
	}

	public void setDob(String dob) {
		this.dob = dob;
	}

	public String getNationality() {
		return nationality;
	}

	public void setNationality(String nationality) {
		this.nationality = nationality;
	}

	public String getPhoneNumber() {
		return phoneNumber;
	}

	public void setPhoneNumber(String phoneNumber) {
		this.phoneNumber = phoneNumber;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getCinSuffix() {
		return cinSuffix;
	}

	public void setCinSuffix(String cinSuffix) {
		this.cinSuffix = cinSuffix;
	}

	@Override
	public String toString() {
		return "UserTxnResponseInfo [mandateId=" + mandateId + ", name=" + name + ", dob=" + dob + ", nationality="
				+ nationality + ", phoneNumber=" + phoneNumber + ", email=" + email + ", cinSuffix=" + cinSuffix
				+ ", getTxnStatus()=" + getTxnStatus() + ", getTxnStatusCode()=" + getTxnStatusCode()
				+ ", getTxnStatusDescription()=" + getTxnStatusDescription() + ", getTxnRejectCode()="
				+ getTxnRejectCode() + "]";
	}
	
}
