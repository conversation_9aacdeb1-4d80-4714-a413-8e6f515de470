package com.abl.pl.paylah.api;

import com.google.gson.annotations.Expose;

public class CaptureTxnResponseInfo extends TxnResponseInfo{
	
	@Expose
	private String txnRefId ;
	
	@Expose
	private String authorizationCode;

	public String getTxnRefId() {
		return txnRefId;
	}

	public void setTxnRefId(String txnRefId) {
		this.txnRefId = txnRefId;
	}

	public String getAuthorizationCode() {
		return authorizationCode;
	}

	public void setAuthorizationCode(String authorizationCode) {
		this.authorizationCode = authorizationCode;
	}

	@Override
	public String toString() {
		return "CaptureTxnResponseInfo [txnRefId=" + txnRefId + ", authorizationCode=" + authorizationCode + "]";
	}
	
	
	
	
}
