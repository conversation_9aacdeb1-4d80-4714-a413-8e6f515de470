package com.abl.pl.paylah.api;

import com.google.gson.annotations.Expose;

public class RefundTxnResponseInfo extends TxnResponseInfo {
	
	// these are for purchase enquiry (i.e. request.txnType=O)
	@Expose private String txnDate;	// YYYY-MM-DDTHH:MM:SS.mmm
	@Expose private String txnType;	// R (echo from request)
	@Expose private String txnSource;	// 01-web, 02-app, 03-paylah app, 04-eca, 05-ecw
	@Expose private Float txnAmount;	// in dollars, with 2 decimal
	@Expose private String txnCcy;
	@Expose private String approvalCode;
	@Expose private String phoneNumber;
	@Expose private String payeeShippingAddress;
	@Expose private Address address;
	@Expose private PaymentDetails paymentDetails;
	@Expose private String originalMsgId;
	@Expose private String originalBankTxnRefId;

	public String getTxnDate() {
		return txnDate;
	}

	public void setTxnDate(String txnDate) {
		this.txnDate = txnDate;
	}

	public String getTxnType() {
		return txnType;
	}

	public void setTxnType(String txnType) {
		this.txnType = txnType;
	}

	public String getTxnSource() {
		return txnSource;
	}

	public void setTxnSource(String txnSource) {
		this.txnSource = txnSource;
	}

	public Float getTxnAmount() {
		return txnAmount;
	}

	public void setTxnAmount(Float txnAmount) {
		this.txnAmount = txnAmount;
	}

	public String getTxnCcy() {
		return txnCcy;
	}

	public void setTxnCcy(String txnCcy) {
		this.txnCcy = txnCcy;
	}

	public String getApprovalCode() {
		return approvalCode;
	}

	public void setApprovalCode(String approvalCode) {
		this.approvalCode = approvalCode;
	}

	public String getPhoneNumber() {
		return phoneNumber;
	}

	public void setPhoneNumber(String phoneNumber) {
		this.phoneNumber = phoneNumber;
	}

	public String getPayeeShippingAddress() {
		return payeeShippingAddress;
	}

	public void setPayeeShippingAddress(String payeeShippingAddress) {
		this.payeeShippingAddress = payeeShippingAddress;
	}

	public Address getAddress() {
		return address;
	}

	public void setAddress(Address address) {
		this.address = address;
	}

	public PaymentDetails getPaymentDetails() {
		return paymentDetails;
	}

	public void setPaymentDetails(PaymentDetails paymentDetails) {
		this.paymentDetails = paymentDetails;
	}

	public String getOriginalMsgId() {
		return originalMsgId;
	}

	public void setOriginalMsgId(String originalMsgId) {
		this.originalMsgId = originalMsgId;
	}

	public String getOriginalBankTxnRefId() {
		return originalBankTxnRefId;
	}

	public void setOriginalBankTxnRefId(String originalBankTxnRefId) {
		this.originalBankTxnRefId = originalBankTxnRefId;
	}
	
}
