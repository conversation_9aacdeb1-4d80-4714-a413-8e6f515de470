package com.abl.pl.paylah.api;

import com.google.gson.annotations.Expose;

public class PurchaseEnquiryResponse implements PaylahResponse {
	@Expose 
	private Header header;
	
	@Expose 
	private PurchaseEnquiryTxnResponseInfo txnResponse;
	
	@Expose 
	private PurchaseEnquiryTxnResponseInfo enqResponse;

	public Header getHeader() {
		return header;
	}

	public void setHeader(Header header) {
		this.header = header;
	}

	public PurchaseEnquiryTxnResponseInfo getTxnResponse() {
		return txnResponse;
	}

	public void setTxnResponse(PurchaseEnquiryTxnResponseInfo txnResponse) {
		this.txnResponse = txnResponse;
	}

	public PurchaseEnquiryTxnResponseInfo getEnqResponse() {
		return enqResponse;
	}

	public void setEnqResponse(PurchaseEnquiryTxnResponseInfo enqResponse) {
		this.enqResponse = enqResponse;
	}
	
	
	
}
