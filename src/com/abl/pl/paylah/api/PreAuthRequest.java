package com.abl.pl.paylah.api;

import com.google.gson.annotations.Expose;

public class PreAuthRequest implements PaylahRequest {
	
	@Expose
	private Header header;
	
	@Expose
	private PreAuthTxnInfo txnInfo;
	

	@Override
	public void setHeader(Header header) {
		this.header = header;

	}

	@Override
	public Header getHeader() {
		return this.header;
	}
	
	

	public PreAuthTxnInfo getTxnInfo() {
		return txnInfo;
	}

	public void setTxnInfo(PreAuthTxnInfo txnInfo) {
		this.txnInfo = txnInfo;
	}

	@Override
	public String toString() {
		return "PreAuthRequest [header=" + header + ", txnInfo=" + txnInfo + "]";
	}
	
	

}
