package com.abl.pl.paylah.api;

import com.google.gson.annotations.Expose;

public class PreAuthTxnInfo {
	
	@Expose
	private String txnMsgId;
	
	@Expose
	private String txnCcy;
	
	@Expose
	private String txnAmount;
	
	@Expose
	private String description;
	
	@Expose
	private String txnSource;
	
	@Expose
	private String userToken;
	

	public String getTxnMsgId() {
		return txnMsgId;
	}

	public void setTxnMsgId(String txnMsgId) {
		this.txnMsgId = txnMsgId;
	}

	public String getTxnCcy() {
		return txnCcy;
	}

	public void setTxnCcy(String txnCcy) {
		this.txnCcy = txnCcy;
	}

	public String getTxnAmount() {
		return txnAmount;
	}

	public void setTxnAmount(String txnAmount) {
		this.txnAmount = txnAmount;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getTxnSource() {
		return txnSource;
	}

	public void setTxnSource(String txnSource) {
		this.txnSource = txnSource;
	}

	public String getUserToken() {
		return userToken;
	}

	public void setUserToken(String userToken) {
		this.userToken = userToken;
	}

	@Override
	public String toString() {
		return "PreAuthTxnInfo [txnMsgId=" + txnMsgId + ", txnCcy=" + txnCcy + ", txnAmount=" + txnAmount
				+ ", description=" + description + ", txnSource=" + txnSource + ", userToken=" + userToken + "]";
	}
	
	
}
