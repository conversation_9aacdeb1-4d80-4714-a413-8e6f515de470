package com.abl.pl.paylah.api;

import com.google.gson.annotations.Expose;

public class WebCheckoutResponse implements PaylahResponse {
	@Expose private Header header;
	@Expose private WebCheckoutTxnResponseInfo txnResponse;
	@Expose private WebCheckoutTxnResponseInfo enqResponse;
	

	public Header getHeader() {
		return header;
	}

	public void setHeader(Header header) {
		this.header = header;
	}

	public WebCheckoutTxnResponseInfo getTxnResponse() {
		return txnResponse;
	}

	public void setTxnResponse(WebCheckoutTxnResponseInfo txnResponse) {
		this.txnResponse = txnResponse;
	}

	public WebCheckoutTxnResponseInfo getEnqResponse() {
		return enqResponse;
	}

	public void setEnqResponse(WebCheckoutTxnResponseInfo enqResponse) {
		this.enqResponse = enqResponse;
	}
	
	
	
}
