package com.abl.pl.paylah;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.SocketTimeoutException;
import java.util.Base64;
import java.util.HashMap;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import org.apache.http.*;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.config.RequestConfig.Builder;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.InputStreamEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.abl.pl.paylah.api.CaptureRequest;
import com.abl.pl.paylah.api.CaptureResponse;
import com.abl.pl.paylah.api.EcSetupRequest;
import com.abl.pl.paylah.api.EcSetupResponse;
import com.abl.pl.paylah.api.PartnerUserIdRequest;
import com.abl.pl.paylah.api.PartnerUserIdResponse;
import com.abl.pl.paylah.api.PreAuthRequest;
import com.abl.pl.paylah.api.PreAuthResponse;
import com.abl.pl.paylah.api.PurchaseEnquiryRequest;
import com.abl.pl.paylah.api.PurchaseEnquiryResponse;
import com.abl.pl.paylah.api.RefundRequest;
import com.abl.pl.paylah.api.RefundResponse;
import com.abl.pl.paylah.api.TokenGenerateRequest;
import com.abl.pl.paylah.api.TokenGenerateResponse;
import com.abl.pl.paylah.api.UserInfoRequest;
import com.abl.pl.paylah.api.UserInfoResponse;
import com.abl.pl.paylah.api.VoidRequest;
import com.abl.pl.paylah.api.VoidResponse;
import com.abl.pl.paylah.api.WebCheckoutRequest;
import com.abl.pl.paylah.api.WebCheckoutResponse;
import com.abl.utils.StringUtils;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

@Component
public class PaylahClientService {

	private static final Logger logger=Logger.getLogger(PaylahClientService.class);
	
	@Value("${paylah.ecSetupUrl:http://127.0.0.1:5001/rapid/paylah/v1/purchase/ecSetup}")
	public String ecSetupUrl;
	
	@Value("${paylah.enquiryUrl:http://127.0.0.1:5001/rapid/paylah/v1/purchase/enquiry}")
	public String enquiryUrl;
	
	@Value("${paylah.webCheckoutUrl:http://127.0.0.1:5001/rapid/paylah/v1/purchase/webCheckout}")
	public String webCheckoutUrl;
	
	@Value("${paylah.refundUrl:http://127.0.0.1:5001/rapid/paylah/v1/purchase/refund}")
	public String refundUrl;
	
	@Value("${paylah.tokenGenerateUrl:http://127.0.0.1:5001/rapid/oauth/v1/token/generate}")
	private String tokenGenerateUrl;
	
	@Value("${paylah.preAuthUrl:http://127.0.0.1:5001/rapid/paylah/v1/earmark/preauth/off}")
	private String preAuthOffUrl;
	
	@Value("${paylah.preAuthUrl2:http://127.0.0.1:5001/rapid/paylah/v1/earmark/preauth/on}")
	private String preAuthOnUrl;
	
	@Value("${paylah.voidUrl:http://127.0.0.1:5001/rapid/paylah/v1/purchase/void}")
	private String voidUrl;	
	
	@Value("${paylah.userInfoUrl:http://127.0.0.1:5001/rapid/paylah/v1/purchase/userInfo}")
	private String userInfoUrl;
	
	@Value("${paylah.captureUrl:http://127.0.0.1:5001/rapid/paylah/v2/purchase/capture}")
	private String captureUrl;
	
	
	@Value("${paylah.partnerUidUrl:http://127.0.0.1:5001/rapid/paylah/v1/userId/store}")
	private String partnerUidUrl;
	
	
	
	@Value("${paylah.orgId:}")
	private String orgId;
	
	@Value("${paylah.keyId:}")
	private String keyId;
	
	@Value("${paylah.clientSecret:}")
	private String clientSecret;
	
	@Value("${paylah.connectTimeoutMs:1000}")
	private int connectTimeoutMs;
	
	@Value("${paylah.socketTimeoutMs:10000}")
	private int socketTimeoutMs;
	
	@Value("${paylah.requestTimeoutMs:10000}")
	private int requestTimeoutMs;
	
	@Value("${paylah.proxyHost:}")
	private String proxyHost;
	
	@Value("${paylah.proxyPort:}")
	private Integer proxyPort;
	
	@Autowired
	private PgpService pgpService;
	
	private HttpClient getHttpClient() throws Exception {
        HttpClientBuilder httpClientBuilder = HttpClientBuilder.create();
        Builder requestConfigBuilder = RequestConfig.custom()
        		.setConnectTimeout(connectTimeoutMs)
                .setConnectionRequestTimeout(requestTimeoutMs)
                .setSocketTimeout(socketTimeoutMs);
        if ((proxyHost != null)&&(!proxyHost.isEmpty())) {
        	logger.trace("using proxy " + proxyHost + ":" + proxyPort);
        	HttpHost proxy = new HttpHost(proxyHost, proxyPort, "http");
        	requestConfigBuilder.setProxy(proxy);
        }
        RequestConfig rc = requestConfigBuilder.build();
        httpClientBuilder.setDefaultRequestConfig(rc);
        
        // create trustall trustmanager for ssl
        X509TrustManager tm = new X509TrustManager() {
            @Override
            public void checkClientTrusted(java.security.cert.X509Certificate[] x509Certificates, String s) throws java.security.cert.CertificateException {
            	// nothing
            }
            @Override
            public void checkServerTrusted(java.security.cert.X509Certificate[] x509Certificates, String s) throws java.security.cert.CertificateException {
            	// nothing
            }
            @Override
            public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                return null;
            }
        };
        
        // configure ssl
        SSLContext sslContext;
        try {
			sslContext = SSLContext.getInstance("TLSv1.2");
            sslContext.init(null, new TrustManager[]{tm},null);
        } catch (Exception e) {
        	throw new Exception("unable to init ssl", e);
        }
        SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext, NoopHostnameVerifier.INSTANCE);
        httpClientBuilder.setSSLSocketFactory(sslsf);

        // create http client
        return httpClientBuilder.build();	
	}
	
	/**
	 * get HttpClient using getHttpClient() and then sends httpRequest
	 * 
	 * if no response, should return null
	 * 
	 * @param httpRequest
	 * @return http response data as string
	 * @throws Exception
	 */
	private String sendHttpRequest(HttpUriRequest httpRequest) throws Exception {
		logger.trace("getHttpClient");
		HttpClient httpClient = getHttpClient();
        HttpResponse response;
        try {
        	logger.trace("sending HttpRequest");
            response = httpClient.execute(httpRequest);
        } catch (SocketTimeoutException e) {
        	// no response should catch this exception
        	logger.error("no response", e);
        	return null;
        } catch (IOException e) {
        	// unable to connect should catch this:
        	//		org.apache.http.conn.HttpHostConnectException
        	//		caused by: java.net.ConnectException
        	throw new Exception("error sending " + httpRequest.getMethod() + " to " + httpRequest.getURI(), e);
        }
        
        String respStr = null;
        HttpEntity respEntity = response.getEntity();
        if (respEntity == null) {
        	logger.debug("resp is null");
        } else {
	        logger.debug("Response content length: " + respEntity.getContentLength());
	        respStr = new String(EntityUtils.toByteArray(respEntity));
	        logger.trace("resp data=" + respStr);
        }
        
        int statusCode=response.getStatusLine().getStatusCode();
        logger.trace("http statusCode=" + statusCode);
        if (statusCode != 200) {
        	// if http status code is not 200, just throw exception
        	throw new PlHttpException("error sending " + httpRequest.getMethod() + " to " + httpRequest.getURI() + ": http status code=" + statusCode + ", respStr=" + respStr , statusCode);
        }
        
        return respStr;
	}
	
	private boolean respIsPgpEncrypted(String resp) {
		return (!resp.startsWith("{"));
	}
	
	/**
	 * sends HTTP POST request
	 * 
	 * if no response, should return null
	 * 
	 * @param url
	 * @param data
	 * @return http response data as string
	 * @throws Exception
	 */
	private String sendPostRequest(String url, String data) throws Exception {
        // prepare post
		HttpPost httpPost=new HttpPost(url);
		httpPost.addHeader("X-DBS-ORG_ID", orgId);
		httpPost.addHeader("x-api-key", keyId);
		httpPost.addHeader("Content-Type", "text/plain");
		
        InputStream inputStream=new ByteArrayInputStream(data.getBytes());
        InputStreamEntity inputStreamEntity=new InputStreamEntity(inputStream);
        inputStreamEntity.setContentType("application/json");
        httpPost.setEntity(inputStreamEntity);
     
        // send http post
        logger.info("sending POST to " + url);
        logger.trace("POST data=" + data);
        System.out.println(url + "--->" + data);
        return sendHttpRequest(httpPost);
	}
	
	/**
	 * sends HTTP PUT request
	 * 
	 * if no response, should return null
	 * 
	 * @param url
	 * @param data
	 * @return http response data as string
	 * @throws Exception
	 */
	private String sendPutRequest(String url, String data) throws Exception {
        // prepare post
		HttpPut httpPut=new HttpPut(url);
		httpPut.addHeader("X-DBS-ORG_ID", orgId);
		httpPut.addHeader("x-api-key", keyId);
		httpPut.addHeader("Content-Type", "application/json");
		
        InputStream inputStream=new ByteArrayInputStream(data.getBytes());
        InputStreamEntity inputStreamEntity=new InputStreamEntity(inputStream);
        inputStreamEntity.setContentType("application/json");
        httpPut.setEntity(inputStreamEntity);
     
        // send http put
        logger.debug("sending PUT to " + url);
        logger.trace("PUT data=" + data);
        return sendHttpRequest(httpPut);
	}
	
	/**
	 * sends EcSetup request
	 * 
	 * The request will be formatted as json, and then encrypted before sending
	 * 
	 * if no response, should return null
	 * 
	 * if response data is not json format, will assume is encrypted, and will be decrypted.
	 * the decrypted response data in json (or raw response data in json) is formatted as 
	 * EcSetupResponse object and returned
	 * 
	 * @param request
	 * @return
	 * @throws Exception
	 */
	public EcSetupResponse sendEcSetupRequest(EcSetupRequest request) throws Exception {
		request.getHeader().setOrgId(orgId);
		String json = new Gson().toJson(request);
		logger.info("ecSetup request (json): " + json);
		String encrypted = pgpService.encrypt(json);
		String resp = sendPostRequest(ecSetupUrl, encrypted);
		String jsonResp;
		if (resp == null) {
			return null;
		} else if (respIsPgpEncrypted(resp)) {
			logger.info("ecSetup resp (encrypted): " + resp);
			jsonResp = pgpService.decrypt(resp);
			logger.info("ecSetup resp (decrypted): " + jsonResp);
		} else {
			logger.warn("ecSetup resp (not encrypted): " + resp);
			jsonResp = resp;
		}
		JsonObject jsonObject= (JsonObject)new JsonParser().parse(jsonResp);
		return new Gson().fromJson(jsonObject, EcSetupResponse.class);
	}
	
	
	
	public TokenGenerateResponse sendTokenGenerateRequest(TokenGenerateRequest request) throws Exception {

		String body = request.toString();

		System.out.println("tokenGenerateUrl===>" + tokenGenerateUrl);
		logger.info("orgId + clientSecret===>" + orgId + ":" + clientSecret);
		logger.info("base64 of orgId + clientSecret===>" + new String(Base64.getEncoder().encode((orgId + ":" + clientSecret).getBytes("UTF-8")), "UTF-8"));
		// prepare post
		HttpPost httpPost = new HttpPost(tokenGenerateUrl);
		httpPost.addHeader("Authorization", "Basic " + new String(Base64.getEncoder().encode((orgId + ":" + clientSecret).getBytes("UTF-8")),"UTF-8"));
		httpPost.addHeader("Content-Type", "application/x-www-form-urlencoded");

		InputStream inputStream = new ByteArrayInputStream(body.getBytes());
		InputStreamEntity inputStreamEntity = new InputStreamEntity(inputStream);
		inputStreamEntity.setContentType("application/x-www-form-urlencoded");
		httpPost.setEntity(inputStreamEntity);

		// send http post
		logger.info("sendTokenGenerateRequest POST to " + tokenGenerateUrl);
		logger.info("sendTokenGenerateRequest POST data=" + body);
		System.out.println(tokenGenerateUrl + "--->" + body);
		String resp = sendHttpRequest(httpPost);

		String jsonResp;
		if (resp == null) {
			return null;
		} else if (respIsPgpEncrypted(resp)) {
			logger.info("token generate resp (encrypted): " + resp);
			jsonResp = pgpService.decrypt(resp);
			logger.info("token generate resp (decrypted): " + jsonResp);
		} else {
			logger.warn("token generate resp (not encrypted): " + resp);
			jsonResp = resp;
		}
		JsonObject jsonObject = (JsonObject) new JsonParser().parse(jsonResp);
		return new Gson().fromJson(jsonObject, TokenGenerateResponse.class);
	}
	
		
	public PreAuthResponse sendPreAuthRequest(PreAuthRequest request) throws Exception {
		System.out.println("header---------:" + request.getHeader());
		logger.info("header---------:" + request.getHeader());
		request.getHeader().setOrgId(orgId);
		String json = new Gson().toJson(request);
		//TODO 是否要加密???
		String encrypted = pgpService.encrypt(json);     //測試本機不加密
		System.out.println("preAuthOffUrl===>" + preAuthOffUrl);
		//String resp = sendPostRequest(preAuthUrl, json);
			
		
        // prepare post
		HttpPost httpPost=new HttpPost(preAuthOffUrl);
		httpPost.addHeader("X-DBS-ORG_ID", orgId);
		httpPost.addHeader("x-api-key", keyId);
		httpPost.addHeader("Content-Type", "text/plain");
		
        InputStream inputStream=new ByteArrayInputStream(encrypted.getBytes());
        InputStreamEntity inputStreamEntity=new InputStreamEntity(inputStream);
        inputStreamEntity.setContentType("application/json");
        httpPost.setEntity(inputStreamEntity);
     
        // send http post
        logger.info("sending POST to " + preAuthOffUrl);
        logger.info("POST data=" + json);
        System.out.println(preAuthOffUrl + "--->" + json);
        String resp =  sendHttpRequest(httpPost);

		String jsonResp;
		if (resp == null) {
			return null;
		} else if (respIsPgpEncrypted(resp)) {
			logger.info("preauth off resp (encrypted): " + resp);
			jsonResp = pgpService.decrypt(resp);
			logger.info("preauth off resp (decrypted): " + jsonResp);
		} else {
			logger.warn("preauth off resp (not encrypted): " + resp);
			jsonResp = resp;
		}
		JsonObject jsonObject= (JsonObject)new JsonParser().parse(jsonResp);
		return new Gson().fromJson(jsonObject, PreAuthResponse.class);
	}
	
		
	public UserInfoResponse sendUserInfoRequest(UserInfoRequest request, String authorizationToken) throws Exception{
		request.getHeader().setOrgId(orgId);
		String json = new Gson().toJson(request);
		//TODO 是否要加密???
		String encrypted = pgpService.encrypt(json);     //測試本機不加密
		System.out.println("userInfoUrl===>" + userInfoUrl);
		
		// prepare post
		HttpPost httpPost=new HttpPost(userInfoUrl);
		httpPost.addHeader("x-api-key", keyId);
		httpPost.addHeader("X-DBS-ORG_ID", orgId);
		httpPost.addHeader("Authorization", "Bearer " + authorizationToken);
		httpPost.addHeader("Content-Type", "text/plain");
		
		logger.info("userinfo Authorization token--> " + authorizationToken);
		
		InputStream inputStream=new ByteArrayInputStream(encrypted.getBytes());
        InputStreamEntity inputStreamEntity=new InputStreamEntity(inputStream);
        inputStreamEntity.setContentType("application/json");
        httpPost.setEntity(inputStreamEntity);
     
        // send http post
        logger.info("sending POST to " + userInfoUrl);
        logger.info("POST data=" + json);
        System.out.println(userInfoUrl + "--->" + json);
        String resp =  sendHttpRequest(httpPost);

		String jsonResp;
		if (resp == null) {
			return null;
		} else if (respIsPgpEncrypted(resp)) {
			logger.info("userinfo resp (encrypted): " + resp);
			jsonResp = pgpService.decrypt(resp);
			logger.info("userinfo resp (decrypted): " + jsonResp);
		} else {
			logger.warn("userinfo resp (not encrypted): " + resp);
			jsonResp = resp;
		}
		JsonObject jsonObject= (JsonObject)new JsonParser().parse(jsonResp);
		return new Gson().fromJson(jsonObject, UserInfoResponse.class);
	
	}
	
	
	public PreAuthResponse sendPreAuthRequest(PreAuthRequest request, String authorizationToken) throws Exception {
		request.getHeader().setOrgId(orgId);
		String json = new Gson().toJson(request);
		//TODO 是否要加密???
		String encrypted = pgpService.encrypt(json);     //測試本機不加密
		System.out.println("preAuthOnUrl===>" + preAuthOnUrl);
		
			
		
        // prepare post
		HttpPost httpPost=new HttpPost(preAuthOnUrl);
		httpPost.addHeader("x-api-key", keyId);
		httpPost.addHeader("X-DBS-ORG_ID", orgId);
		httpPost.addHeader("Authorization", "Bearer " + authorizationToken);
		httpPost.addHeader("Content-Type", "text/plain");
		
		logger.info("access token---->" + authorizationToken);

        InputStream inputStream=new ByteArrayInputStream(encrypted.getBytes());
        InputStreamEntity inputStreamEntity=new InputStreamEntity(inputStream);
        inputStreamEntity.setContentType("application/json");
        httpPost.setEntity(inputStreamEntity);

        // send http post
        logger.info("sending POST to " + preAuthOnUrl);
        logger.info("POST data=" + json);
        System.out.println(preAuthOnUrl + "--->" + json);
		String resp =  sendHttpRequest(httpPost);

		String jsonResp;
		if (resp == null) {
			return null;
		} else if (respIsPgpEncrypted(resp)) {
			logger.info("preauth resp (encrypted): " + resp);
			jsonResp = pgpService.decrypt(resp);
			logger.info("preauth resp (decrypted): " + jsonResp);
		} else {
			logger.warn("preauth resp (not encrypted): " + resp);
			jsonResp = resp;
		}
		JsonObject jsonObject= (JsonObject)new JsonParser().parse(jsonResp);
		return new Gson().fromJson(jsonObject, PreAuthResponse.class);
	}



	public VoidResponse sendVoidRequest(VoidRequest request) throws Exception{
		request.getHeader().setOrgId(orgId);
		String json = new Gson().toJson(request);
		//TODO 是否要加密???
		String encrypted = pgpService.encrypt(json);     //測試本機不加密
		
		System.out.println("voidUrl===>" + voidUrl);
		logger.info("voidUrl===>" + voidUrl);
		 // prepare post
		HttpPost httpPost=new HttpPost(voidUrl);
		httpPost.addHeader("X-DBS-ORG_ID", orgId);
		httpPost.addHeader("x-api-key", keyId);
		httpPost.addHeader("Content-Type", "text/plain");
		
		InputStream inputStream=new ByteArrayInputStream(encrypted.getBytes());
        InputStreamEntity inputStreamEntity=new InputStreamEntity(inputStream);
        inputStreamEntity.setContentType("application/json");
        httpPost.setEntity(inputStreamEntity);
        
     // send http post
        logger.info("sending POST to " + voidUrl);
        logger.info("POST data=" + json);
        System.out.println(voidUrl + "--->" + json);
        String resp =  sendHttpRequest(httpPost);

		String jsonResp;
		if (resp == null) {
			return null;
		} else if (respIsPgpEncrypted(resp)) {
			logger.info("void resp (encrypted): " + resp);
			jsonResp = pgpService.decrypt(resp);
			logger.info("void resp (decrypted): " + jsonResp);
		} else {
			logger.warn("void resp (not encrypted): " + resp);
			jsonResp = resp;
		}
		JsonObject jsonObject= (JsonObject)new JsonParser().parse(jsonResp);
		return new Gson().fromJson(jsonObject, VoidResponse.class);		
	}
	
	
	
	
	
	/**
	 * sends purchaseEnquiry request
	 * 
	 * The request will be formatted as json, and then encrypted before sending
	 * 
	 * if no response, should return null
	 * 
	 * if response data is not json format, will assume is encrypted, and will be decrypted.
	 * the decrypted response data in json (or raw response data in json) is formatted as 
	 * PurchaseEnquiryResponse object and returned
	 * 
	 * @param request
	 * @return
	 * @throws Exception
	 */
	public PurchaseEnquiryResponse sendPurchaseEnquiryRequest(PurchaseEnquiryRequest request) throws Exception {
		request.getHeader().setOrgId(orgId);
		String json = new Gson().toJson(request);
		logger.info("enquiry request (json): " + json);
		String encrypted = pgpService.encrypt(json);
		String resp = sendPostRequest(enquiryUrl, encrypted);
		String jsonResp;
		if (resp == null) {
			return null;
		} else if (respIsPgpEncrypted(resp)) {
			logger.info("enquiry resp (encrypted): " + resp);
			jsonResp = pgpService.decrypt(resp);
			logger.info("enquiry resp (decrypted): " + jsonResp);
		} else {
			logger.warn("enquiry resp (not encrypted): " + resp);
			jsonResp = resp;
		}
		JsonObject jsonObject= (JsonObject)new JsonParser().parse(jsonResp);
		return new Gson().fromJson(jsonObject, PurchaseEnquiryResponse.class);
	}
	
	/**
	 * sends refund request
	 * 
	 * The request will be formatted as json, and then encrypted before sending
	 * 
	 * if no response, should return null
	 * 
	 * if response data is not json format, will assume is encrypted, and will be decrypted.
	 * the decrypted response data in json (or raw response data in json) is formatted as 
	 * RefundResponse object and returned
	 * 
	 * @param request
	 * @return
	 * @throws Exception
	 */
	public RefundResponse sendRefundRequest(RefundRequest request) throws Exception {
		request.getHeader().setOrgId(orgId);
		String json = new Gson().toJson(request);
		logger.info("refund request (json): " + json);
		String encrypted = pgpService.encrypt(json);
		String resp = sendPostRequest(refundUrl, encrypted);
		String jsonResp;
		if (resp == null) {
			return null;
		} else if (respIsPgpEncrypted(resp)) {
			logger.info("refund resp (encrypted): " + resp);
			jsonResp = pgpService.decrypt(resp);
			logger.info("refund resp (decrypted): " + jsonResp);
		} else {
			logger.warn("refund resp (not encrypted): " + resp);
			jsonResp = resp;
		}
		JsonObject jsonObject= (JsonObject)new JsonParser().parse(jsonResp);
		return new Gson().fromJson(jsonObject, RefundResponse.class);
	}
	
	/**
	 * sends webCheckout request
	 * 
	 * The request will be formatted as json, and then encrypted before sending
	 * 
	 * if no response, should return null
	 * 
	 * if response data is not json format, will assume is encrypted, and will be decrypted.
	 * the decrypted response data in json (or raw response data in json) is formatted as 
	 * WebCheckoutResponse object and returned
	 * 
	 * @param request
	 * @return
	 * @throws Exception
	 */
	public WebCheckoutResponse sendWebCheckoutRequest(WebCheckoutRequest request) throws Exception {
		request.getHeader().setOrgId(orgId);
		String json = new Gson().toJson(request);
		logger.info("webCheckout request (json): " + json);
		String encrypted = pgpService.encrypt(json);
		String resp = sendPostRequest(webCheckoutUrl, encrypted);
		String jsonResp;
		if (resp == null) {
			return null;
		} else if (respIsPgpEncrypted(resp)) {
			logger.info("webCheckout resp (encrypted): " + resp);
			jsonResp = pgpService.decrypt(resp);
			logger.info("webCheckout resp (decrypted): " + jsonResp);
		} else {
			logger.warn("webCheckout resp (not encrypted): " + resp);
			jsonResp = resp;
		}
		JsonObject jsonObject= (JsonObject)new JsonParser().parse(jsonResp);
		return new Gson().fromJson(jsonObject, WebCheckoutResponse.class);
	}
	
	
	public CaptureResponse sendCaptureRequest(CaptureRequest request) throws Exception{
		request.getHeader().setOrgId(orgId);
		String json = new Gson().toJson(request);
		logger.info("CaptureRequest request (json): " + json);
		String encrypted = pgpService.encrypt(json);   //TODO 
		System.out.println("captureUrl===>" + captureUrl);
		
		 // prepare post
		HttpPost httpPost=new HttpPost(captureUrl);
		httpPost.addHeader("X-DBS-ORG_ID", orgId);
		httpPost.addHeader("x-api-key", keyId);
		httpPost.addHeader("Content-Type", "text/plain");
		
        InputStream inputStream=new ByteArrayInputStream(encrypted.getBytes());
        InputStreamEntity inputStreamEntity=new InputStreamEntity(inputStream);
        inputStreamEntity.setContentType("application/json");
        httpPost.setEntity(inputStreamEntity);
        
         // send http post
        logger.info("sending POST to " + captureUrl);
		logger.trace("POST data=" + json);
        System.out.println(captureUrl + "--->" + json);
        String resp =  sendHttpRequest(httpPost);

		String jsonResp;
		if (resp == null) {
			return null;
		} else if (respIsPgpEncrypted(resp)) {
			logger.info("capture resp (encrypted): " + resp);
			jsonResp = pgpService.decrypt(resp);
			logger.info("capture resp (decrypted): " + jsonResp);
		} else {
			logger.warn("capture resp (not encrypted): " + resp);
			jsonResp = resp;
		}
		JsonObject jsonObject= (JsonObject)new JsonParser().parse(jsonResp);
		logger.info("jsonObject: " + jsonObject);
		return new Gson().fromJson(jsonObject, CaptureResponse.class);
	}
	
	public PartnerUserIdResponse sendStorePartnerUserIdRequest(PartnerUserIdRequest request, String authorizationToken) throws Exception {
		request.getHeader().setOrgId(orgId);
		String json = new Gson().toJson(request);
		logger.info("PartnerUserIdRequest request (json): " + json);
		String encrypted = pgpService.encrypt(json); // TODO
		System.out.println("partnerUidUrl===>" + partnerUidUrl);

		// prepare post
		HttpPost httpPost = new HttpPost(partnerUidUrl);
		httpPost.addHeader("x-api-key", keyId);
		httpPost.addHeader("X-DBS-ORG_ID", orgId);
		httpPost.addHeader("Authorization", "Bearer " + authorizationToken);
		httpPost.addHeader("Content-Type", "text/plain");

		logger.info("access token---->" + authorizationToken);

		InputStream inputStream = new ByteArrayInputStream(encrypted.getBytes());
		InputStreamEntity inputStreamEntity = new InputStreamEntity(inputStream);
		inputStreamEntity.setContentType("application/json");
		httpPost.setEntity(inputStreamEntity);

		// send http post
		logger.info("sending POST to " + partnerUidUrl);
		logger.info("POST data=" + json);
		System.out.println(partnerUidUrl + "--->" + json);
		String resp = sendHttpRequest(httpPost);

		String jsonResp;
		if (resp == null) {
			return null;
		} else if (respIsPgpEncrypted(resp)) {
			logger.info("partnerUid resp (encrypted): " + resp);
			jsonResp = pgpService.decrypt(resp);
			logger.info("partnerUid resp (decrypted): " + jsonResp);
		} else {
			logger.warn("partnerUid resp (not encrypted): " + resp);
			jsonResp = resp;
		}
		JsonObject jsonObject = (JsonObject) new JsonParser().parse(jsonResp);
		return new Gson().fromJson(jsonObject, PartnerUserIdResponse.class);

	}
	
	
	public PartnerUserIdResponse sendDeletePartnerUserIdRequest(PartnerUserIdRequest request, String authorizationToken) throws Exception{
		request.getHeader().setOrgId(orgId);
		String json = new Gson().toJson(request);
		logger.info("PartnerUserIdRequest request (json): " + json);
		String encrypted = pgpService.encrypt(json);   //TODO 
		System.out.println("partnerUidUrl===>" + partnerUidUrl);
		
		 // prepare delete
		HttpDeleteWithBody httpDelete = new HttpDeleteWithBody(partnerUidUrl);
		httpDelete.addHeader("X-DBS-ORG_ID", orgId);
		httpDelete.addHeader("x-api-key", keyId);
		httpDelete.addHeader("Authorization", "Bearer " + authorizationToken);
		httpDelete.addHeader("Content-Type", "text/plain");
		
        InputStream inputStream=new ByteArrayInputStream(encrypted.getBytes());
        InputStreamEntity inputStreamEntity=new InputStreamEntity(inputStream);
        inputStreamEntity.setContentType("application/json");
        
        httpDelete.setEntity(inputStreamEntity);
        
         // send http post
        logger.info("sending Delete to " + partnerUidUrl);
        logger.trace("Delete data=" + json);
        System.out.println(partnerUidUrl + "--->" + json);
        String resp =  sendHttpRequest(httpDelete);

		String jsonResp;
		if (resp == null) {
			return null;
		} else if (respIsPgpEncrypted(resp)) {
			logger.info("partnerUid resp (encrypted): " + resp);
			jsonResp = pgpService.decrypt(resp);
			logger.info("partnerUid resp (decrypted): " + jsonResp);
		} else {
			logger.warn("partnerUid resp (not encrypted): " + resp);
			jsonResp = resp;
		}
		JsonObject jsonObject= (JsonObject)new JsonParser().parse(jsonResp);
		return new Gson().fromJson(jsonObject, PartnerUserIdResponse.class);
	}
	
	
	
	
	
	
	public static void main(String args[]) throws UnsupportedEncodingException {
//		byte[] v = Base64.getEncoder().encode("168700800034:password".getBytes("UTF-8"));
//		System.out.println(new String(v, "UTF-8"));
//		
//		String[] field48StrArr = org.apache.commons.lang.StringUtils.split(";06F3AFF045-2552-49CB-A537-58D185ED7D04;435465364564564757876824312321001230", ";");
//		System.out.println(field48StrArr[0] + "," +field48StrArr[1] );
//		String entryMode = field48StrArr[0].substring(0, 2);
//		String userid = field48StrArr[0].substring(2);
//		String bookingNum = field48StrArr[1].substring(0, field48StrArr[1].length()-6);
//		String authCode = field48StrArr[1].substring(field48StrArr[1].length()-6);
//		System.out.println("entryMode:" + entryMode);
//		System.out.println("userid:" + userid);
//		System.out.println("bookingNum:" + bookingNum);
//		System.out.println("authCode:" + authCode);
	}
	
	
}
