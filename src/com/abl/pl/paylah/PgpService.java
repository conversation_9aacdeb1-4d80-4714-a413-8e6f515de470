package com.abl.pl.paylah;

import java.io.File;

import org.apache.log4j.Logger;
import org.c02e.jpgpj.Key;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class PgpService implements InitializingBean {
	
	private static final Logger logger=Logger.getLogger(PgpService.class);
	
	private @Value("${paylah.pgpPubKeyFile:}") String pubKeyFile;
	private @Value("${paylah.pgpPriKeyFile:}") String priKeyFile;
	private @Value("${paylah.pgpPriPassword:}") char[] priKeyPassword;
	
	// pubKey is the RECIPIENT public key
	private Key pubKey;
	
	// priKey is the SENDER private key 
	private Key priKey;
	
	@Override
	public void afterPropertiesSet() throws Exception {
		try {
			logger.debug("reading pgp pub key " + pubKeyFile);		
			System.out.println("reading pgp pub key " + pubKeyFile);
			pubKey = PgpUtil.getKeyFromFile(new File(pubKeyFile));
			System.out.println("reading pgp pub key done");
			logger.debug("reading pgp pub key done");
			logger.debug("pubKey=" + pubKey);
		} catch (Exception e) {
			throw new Exception("unable to read pgp pub key file " + pubKeyFile, e);
		}
		try {
			logger.debug("reading pgp pri key " + priKeyFile);			
			priKey = PgpUtil.getKeyFromFile(new File(priKeyFile), new String(priKeyPassword));
			logger.debug("reading pgp pri key done");
			logger.debug("priKey=" + priKey);
		} catch (Exception e) {
			throw new Exception("unable to read pgp pri key file " + priKeyFile, e);
		}
	}
	
	/**
	 * encrypt using pubKey
	 * 
	 * @param plain
	 * @return
	 * @throws Exception
	 */
	public String encrypt(String plain) throws Exception {
		byte[] cipher = PgpUtil.signAndEncryptData(priKey, pubKey, plain.getBytes(), true);
		return new String(cipher);
	}

	/**
	 * decrypt using priKey
	 * 
	 * @param cipher
	 * @return
	 * @throws Exception
	 */
	public String decrypt(String cipher) throws Exception {
		byte[] recover = PgpUtil.decryptAndVerifyData(pubKey, priKey, cipher.getBytes());
		return new String(recover);
	}
}
