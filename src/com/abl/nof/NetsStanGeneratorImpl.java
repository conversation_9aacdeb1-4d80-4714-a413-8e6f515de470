package com.abl.nof;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.abl.db.service.SequenceService;
import com.abl.nof.nets.NetsStanGenerator;

@Component
public class NetsStanGeneratorImpl implements NetsStanGenerator {

	private static final Logger logger=Logger.getLogger(NetsStanGeneratorImpl.class);
	
	private static final String SEQUENCE_NAME_PREFIX="TMSQ_NOF_";
	
	@Autowired private SequenceService sequenceService;
	
	/**
	 * get next sequence without throwing exceptions

	 * @param seqName
	 * @return
	 */
	private Long nextSequence(String seqName) {
		try {
			return sequenceService.nextSequence(seqName);
		} catch (Exception e) {
			logger.error("unable to get next seq for " + seqName, e);
			return null;
		}
	}
	
	@Override
	public String nextStan(String mid, String tid) {
		// append mid will cause ORA-00972: identifier is too long 
		String seqName = SEQUENCE_NAME_PREFIX + tid;
		Long seq = nextSequence(seqName);
		if (seq == null) {
			// if unable to get next sequence, assume sequence not created
			// create sequence
			sequenceService.createSequence(seqName);
			// get next sequence again
			seq = sequenceService.nextSequence(seqName);
		}
		String stan = String.format("%06d", seq);
		if (stan.length() > 6) {
			stan = stan.substring(stan.length()-6);
		}
		return stan;
	}

}
