package com.abl.nof.util;

import com.abl.db.model.NofAccount;
import com.abl.nof.nets.api.MerchantTokenStatus;

public class NofUtil {

	/**
	 * convert merchantTokenStatus to NofAccount.Status
	 * 
	 * @param tokenStatus
	 * @return
	 */
	public static NofAccount.Status merchantTokenStatusToNofAccountStatus(String tokenStatus) {
		if (MerchantTokenStatus.PRE_REGISTERED.equals(tokenStatus)) {
			return NofAccount.Status.PRE_REGISTERED;
		} else if (MerchantTokenStatus.BLOCKED.equals(tokenStatus)) {
			return NofAccount.Status.BLOCKED;
		} else if (MerchantTokenStatus.DE_REGISTERED.equals(tokenStatus)) {
			return NofAccount.Status.DE_REGISTERED;
		} else if (MerchantTokenStatus.REGISTERED.equals(tokenStatus)) {
			return NofAccount.Status.REGISTERED;
		} else if (MerchantTokenStatus.SUSPENDED.equals(tokenStatus)) {
			return NofAccount.Status.SUSPENDED;
		} else {
			return null;
		}
	}
	
	/**
	 * convert NofAccount.Status to merchantTokenStatus
	 * 
	 * @param status
	 * @return
	 */
	public static String nofAccountStatusToMerchantTokenStatus(NofAccount.Status status) {
		if (status == null) {
			return null;
		}
		switch (status) {
			case PRE_REGISTERED:
				return MerchantTokenStatus.PRE_REGISTERED;
			case BLOCKED:
				return MerchantTokenStatus.BLOCKED;
			case DE_REGISTERED:
				return MerchantTokenStatus.DE_REGISTERED;
			case REGISTERED:
				return MerchantTokenStatus.REGISTERED;
			case SUSPENDED:
				return MerchantTokenStatus.SUSPENDED;
			default:
				return null;
		}
	}
}
