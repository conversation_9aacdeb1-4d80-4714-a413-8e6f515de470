package com.abl.nof.util;

import java.util.Random;

import org.apache.log4j.Logger;

public class TransactionUtils {
	
	private static final Logger logger = Logger.getLogger(TransactionUtils.class);
	
	public static String generateAuthCode() {
		Random random = new Random();
		String rand = String.format("%06X", random.nextInt(16777215));	// 16777215 = 0xFFFFFF
		return rand;
	}
	
	public static String genRrn(long id) {
		Random random = new Random();
		// assume id does not exceed 11 digits
		String rrn = String.format("%011d", id) + String.format("%01d", random.nextInt(10)); 
		return rrn;
	}
}
