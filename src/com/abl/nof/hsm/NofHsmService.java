package com.abl.nof.hsm;

public interface NofHsmService {
	
	/**
	 * add listener to the hsm service
	 * 
	 * @param listener
	 */
	public void addEventListener(NofHsmServiceEventListener listener);
	
	/**
	 * unwrap session key "sessionKey" using KIS, and return the hsm object.
	 * the returned hsm object is temporary
	 * 
	 * @param sessionKey
	 * @return
	 * @throws NofHsmException
	 */
	public Object unwrapSessionKeyWithKis(byte[] sessionKey) throws NofHsmException;
	
	/**
	 * generate mac with sessionKey using 3DES_CBC
	 * padding: 0x30 is added to data on the right if necessary
	 * 
	 * @param sessionKey
	 * @param data
	 * @return 8 byte mac
	 * @throws NofHsmException
	 */
	public byte[] generateMac(Object sessionKey, byte[] data) throws NofHsmException;
	
	/**
	 * verify mac with sessionKey using 3DES_CBC
	 * padding: 0x30 is added to data on the right if necessary
	 * 
	 * @param sessionKey
	 * @param data
	 * @param mac
	 * @return
	 * @throws NofHsmException
	 */
	public boolean verifyMac(Object sessionKey, byte[] data, byte[] mac) throws NofHsmException;
	
	/**
	 * encrypt data with DPK using 3DES_CBC
	 * padding: 0x30 is added to data on the right if necessary
	 * 
	 * @param data
	 * @return
	 * @throws NofHsmException
	 */
	public byte[] encryptWithDpk(byte[] data) throws NofHsmException;
	
	/**
	 * decrypt data with DPK using 3DES_CBC
	 * returned decrypted data may have padding
	 * 
	 * @param data
	 * @return
	 * @throws NofHsmException
	 */
	public byte[] decryptWithDpk(byte[] data) throws NofHsmException;

	/**
	 * encrypt data with MK using AES_CBC
	 * padding method 2 is applied
	 * 
	 * @param data
	 * @return
	 * @throws NofHsmException
	 */
	public byte[] encryptWithMk(byte[] data) throws NofHsmException;
	
	/**
	 * decrypt data with MK using AES_CBC
	 * padding is removed after decryption (padding method 2)
	 * exception will be thrown if unable to remove padding
	 * 
	 * @param data
	 * @return
	 * @throws NofHsmException
	 */
	public byte[] decryptWithMk(byte[] data) throws NofHsmException;
	
	/**
	 * get kcv of key
	 * 
	 * @param key
	 * @return
	 * @throws NofHsmException
	 */
	public byte[] getKcv(Object key) throws NofHsmException;
	
}
