package com.abl.nof.hsm;

import java.util.Arrays;

import javax.crypto.BadPaddingException;

public class PaddingUtils {

	public static byte[] addPadding(byte[] data, byte pad, int blocksize) {
		int rem = (data.length) % blocksize;
		int numPad = 0;
		if (rem > 0) {
			numPad = blocksize - rem;
		}
		byte[] result = new byte[data.length + numPad];
		System.arraycopy(data, 0, result, 0, data.length);
		Arrays.fill(result, data.length, result.length, pad);
		return result;
	}
	
	public static byte[] addPaddingMethod2(byte[] data, int blocksize) {
		int rem = (data.length + 1) % blocksize;
		int numZeroes = 0;
		if (rem > 0) {
			numZeroes = blocksize - rem;
		}
		byte[] result = new byte[data.length + 1 + numZeroes];
		System.arraycopy(data, 0, result, 0, data.length);
		result[data.length] = (byte)0x80;
		return result;
	}
	
	public static byte[] removePaddingMethod2(byte[] data) throws BadPaddingException {
		int n=data.length-1;
		for (;n>=0; n--) {
			if (data[n] == 0) {
				continue;
			}
			if (data[n] == (byte)0x80) {
				break;
			}
			throw new BadPaddingException();
		}
		if (n < 0) {
			throw new BadPaddingException();
		}
		byte[] result = new byte[n];
		System.arraycopy(data, 0, result, 0, n);
		return result;
	}
}
