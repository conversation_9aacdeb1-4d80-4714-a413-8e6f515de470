package com.abl.nof.hsm;

import java.util.HashSet;
import java.util.Set;

import javax.crypto.BadPaddingException;

import org.apache.commons.pool2.ObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;

import com.abl.utils.ByteUtils;

import safenet.jcprov.CK_C_INITIALIZE_ARGS;
import safenet.jcprov.CK_OBJECT_HANDLE;
import safenet.jcprov.Cryptoki;
import safenet.jcprov.constants.CKF;
import safenet.jcprov.constants.CK_RV;

/**
 * provides hsm service
 * 
 * maintains a pool of "numSessions" number of hsm sessions 
 *
 */
public class NofSafenetHsmService implements NofHsmService, InitializingBean  {

	private static final Logger logger=Logger.getLogger(NofSafenetHsmService.class);
			
	// configs
	@Value("${hsm.slotId}")
	private int slotId;
	
	@Value("${hsm.pin}")
	private char[] pin;
	
	// pool size
	@Value("${hsm.numSessions}")
	private int numSessions;
	
	@Value("${hsm.sessionMaxWaitMs}")
	private long sessionMaxWaitMs;
	
	// internal vars
	private ObjectPool<NofSafenetHsmSession> nofSafenetHsmSessionPool;
	private long lastIntializeResult=0;	// stores last initialize hsm result
	private Set<NofHsmServiceEventListener> listeners = new HashSet<NofHsmServiceEventListener>();
	
	/**
	 * initialize hsm
	 * inform all listeners of hsm initialized event
	 * 
	 * @throws NofHsmException
	 */
	public void initializeHsm() throws NofHsmException {
		logger.debug("calling C_Initialize");
		CK_RV rv = Cryptoki.C_Initialize(new CK_C_INITIALIZE_ARGS(CKF.OS_LOCKING_OK));
		lastIntializeResult = rv.longValue();
		logger.debug("informing listeners");
		for (NofHsmServiceEventListener listener: listeners) {
			logger.debug("informing listener: " + listener);
			listener.hsmInitialized();
		}
		if (rv.longValue() == 0) {
			logger.debug("C_Initialize returns " + rv.longValue());
		} else {
			logger.error("C_Initialize returns " + rv.longValue());
			throw new NofHsmException("initialize hsm error", rv.longValue());
		}
	}
	
	public void finalizeHsm() throws NofHsmException {
		logger.debug("calling C_Finalize");
		CK_RV rv = Cryptoki.C_Finalize(null);
		logger.debug("C_Finalize returns " + rv.longValue());
	}
	
	@Override
	public void afterPropertiesSet() throws Exception {
		try {
			initializeHsm();
		} catch (Exception e) {
			// catch exception, should be able to recover later
			logger.error("unable to initialize hsm", e);
		}
		
		logger.debug("numSession=" + numSessions);
		GenericObjectPoolConfig config = new GenericObjectPoolConfig();
		config.setMaxTotal(numSessions);
		config.setMaxWaitMillis(sessionMaxWaitMs);
		config.setTestOnBorrow(true);
		NofSafenetHsmSessionFactory nofSafenetHsmSessionFactory = new NofSafenetHsmSessionFactory(this, slotId, pin);
		nofSafenetHsmSessionPool = new GenericObjectPool<NofSafenetHsmSession>(nofSafenetHsmSessionFactory, config);
	}

	public void addEventListener(NofHsmServiceEventListener listener) {
		listeners.add(listener);
	}
	
	/**
	 * get hsmSession from pool of hsmSessions.
	 * caller must either call:
	 * - returnHsmSession() to return the hsmSession to the pool
	 * - or invalidateHsmSession() to invalidate the hsmSession 
	 * 
	 * @return
	 * @throws NofHsmException
	 */
	private NofSafenetHsmSession getHsmSession() throws NofHsmException {
		try {
			return nofSafenetHsmSessionPool.borrowObject();
		} catch (Exception e) {
			throw new NofHsmException("unable to get hsm session", e);
		}
	}
	
	/**
	 * inform hsmSession pool to invalidate hsmSession.
	 * the pool will use the factory to create new session, when needed.
	 * 
	 * @param hsmSession
	 * @throws NofHsmException
	 */
	private void invalidateHsmSession(NofSafenetHsmSession hsmSession) throws NofHsmException {
		try {
			nofSafenetHsmSessionPool.invalidateObject(hsmSession);
		} catch (Exception e) {
			throw new NofHsmException("unable to invalidate hsm session " + hsmSession, e);
		}
	}
	
	/**
	 * return hsmSession to hsmSession pool
	 * 
	 * @param hsmSession
	 * @throws NofHsmException
	 */
	private void returnHsmSession(NofSafenetHsmSession hsmSession) throws NofHsmException {
		try {
			nofSafenetHsmSessionPool.returnObject(hsmSession);
		} catch (Exception e) {
			throw new NofHsmException("unable to return hsm session " + hsmSession, e);
		}
	}
	
	@Override
	public Object unwrapSessionKeyWithKis(byte[] encSessionKey) throws NofHsmException {
		NofSafenetHsmSession hsmSession = getHsmSession();
		logger.debug("hsmSession=" + hsmSession);
		try {
			CK_OBJECT_HANDLE sessionKey = (CK_OBJECT_HANDLE)hsmSession.unwrapSessionKeyWithKis(encSessionKey);
			return sessionKey;
		} catch (NofHsmInvalidSessionException e) {
			logger.error("caught invalid hsm session exception, invaliding session " + hsmSession);
			invalidateHsmSession(hsmSession);
			hsmSession = null;
			throw e;
		} finally {
			if (hsmSession != null) {
				returnHsmSession(hsmSession);
			}
		}
	}
	
	@Override
	public byte[] generateMac(Object sessionKey, byte[] data) throws NofHsmException {
		NofSafenetHsmSession hsmSession = getHsmSession();
		logger.debug("hsmSession=" + hsmSession);
		try {
			logger.debug("before padding: " + ByteUtils.bytesToHex(data));
			byte[] padData = PaddingUtils.addPadding(data, (byte)0x0, 8);
			logger.debug("after padding: " + ByteUtils.bytesToHex(padData));
			return hsmSession.generateMac((CK_OBJECT_HANDLE)sessionKey, padData);
		} catch (NofHsmInvalidSessionException e) {
			logger.error("caught invalid hsm session exception, invaliding session " + hsmSession);
			invalidateHsmSession(hsmSession);
			hsmSession = null;
			throw e;
		} finally {
			if (hsmSession != null) {
				returnHsmSession(hsmSession);
			}
		}
	}
	
	@Override
	public boolean verifyMac(Object sessionKey, byte[] data, byte[] mac) throws NofHsmException {
		NofSafenetHsmSession hsmSession = getHsmSession();
		logger.debug("hsmSession=" + hsmSession);
		try {
			logger.debug("before padding: " + ByteUtils.bytesToHex(data));
			byte[] padData = PaddingUtils.addPadding(data, (byte)0x30, 8);
			logger.debug("after padding: " + ByteUtils.bytesToHex(padData));
			return hsmSession.verifyMac((CK_OBJECT_HANDLE)sessionKey, padData, mac);
		} catch (NofHsmInvalidSessionException e) {
			logger.error("caught invalid hsm session exception, invaliding session " + hsmSession);
			invalidateHsmSession(hsmSession);
			hsmSession = null;
			throw e;
		} finally {
			if (hsmSession != null) {
				returnHsmSession(hsmSession);
			}
		}
	}
	
	@Override
	public byte[] encryptWithDpk(byte[] data) throws NofHsmException {
		NofSafenetHsmSession hsmSession = getHsmSession();
		logger.debug("hsmSession=" + hsmSession);
		try {
			logger.debug("before padding: " + ByteUtils.bytesToHex(data));
			byte[] padData = PaddingUtils.addPadding(data, (byte)0x30, 8);
			logger.debug("after padding: " + ByteUtils.bytesToHex(padData));
			return hsmSession.encryptWithDpk(padData);
		} catch (NofHsmInvalidSessionException e) {
			logger.error("caught invalid hsm session exception, invaliding session " + hsmSession);
			invalidateHsmSession(hsmSession);
			hsmSession = null;
			throw e;
		} finally {
			if (hsmSession != null) {
				returnHsmSession(hsmSession);
			}
		}
	}
	
	@Override
	public byte[] decryptWithDpk(byte[] data) throws NofHsmException {
		NofSafenetHsmSession hsmSession = getHsmSession();
		logger.debug("hsmSession=" + hsmSession);
		try {
			return hsmSession.decryptWithDpk(data);
		} catch (NofHsmInvalidSessionException e) {
			logger.error("caught invalid hsm session exception, invaliding session " + hsmSession);
			invalidateHsmSession(hsmSession);
			hsmSession = null;
			throw e;
		} finally {
			if (hsmSession != null) {
				returnHsmSession(hsmSession);
			}
		}
	}

	@Override
	public byte[] encryptWithMk(byte[] data) throws NofHsmException {
		NofSafenetHsmSession hsmSession = getHsmSession();
		logger.debug("hsmSession=" + hsmSession);
		try {
			logger.debug("before padding: " + ByteUtils.bytesToHex(data));
			byte[] padData = PaddingUtils.addPaddingMethod2(data, 16);
			logger.debug("after padding: " + ByteUtils.bytesToHex(padData));
			return hsmSession.encryptWithMk(padData);
		} catch (NofHsmInvalidSessionException e) {
			logger.error("caught invalid hsm session exception, invaliding session " + hsmSession);
			invalidateHsmSession(hsmSession);
			hsmSession = null;
			throw e;
		} finally {
			if (hsmSession != null) {
				returnHsmSession(hsmSession);
			}
		}
	}

	@Override
	public byte[] decryptWithMk(byte[] data) throws NofHsmException {
		NofSafenetHsmSession hsmSession = getHsmSession();
		logger.debug("hsmSession=" + hsmSession);
		try {
			byte[] paddedResult = hsmSession.decryptWithMk(data);
			logger.debug("before removing padding: " + ByteUtils.bytesToHex(paddedResult));
			byte[] result = PaddingUtils.removePaddingMethod2(paddedResult);
			logger.debug("after removing padding: " + ByteUtils.bytesToHex(result));
			return result;
		} catch (NofHsmInvalidSessionException e) {
			logger.error("caught invalid hsm session exception, invaliding session " + hsmSession);
			invalidateHsmSession(hsmSession);
			hsmSession = null;
			throw e;
		} catch (BadPaddingException e) {
			throw new NofHsmException("unable to decrypt with mk", e);
		} finally {
			if (hsmSession != null) {
				returnHsmSession(hsmSession);
			}
		}
	}
	
	@Override
	public byte[] getKcv(Object key) throws NofHsmException {
		NofSafenetHsmSession hsmSession = getHsmSession();
		logger.debug("hsmSession=" + hsmSession);
		try {
			logger.debug("get kcv");
			byte[] kcv = hsmSession.getKcv((CK_OBJECT_HANDLE)key);
			logger.debug("kcv=" + ByteUtils.bytesToHex(kcv));
			return kcv;
		} catch (NofHsmInvalidSessionException e) {
			logger.error("caught invalid hsm session exception, invaliding session " + hsmSession);
			invalidateHsmSession(hsmSession);
			hsmSession = null;
			throw e;
		} finally {
			if (hsmSession != null) {
				returnHsmSession(hsmSession);
			}
		}
	}
	
	public int getSlotId() {
		return slotId;
	}

	public void setSlotId(int slotId) {
		this.slotId = slotId;
	}

	public void setPin(char[] pin) {
		this.pin = pin;
	}

	public int getNumSessions() {
		return numSessions;
	}

	public void setNumSessions(int numSessions) {
		this.numSessions = numSessions;
	}

	public long getSessionMaxWaitMs() {
		return sessionMaxWaitMs;
	}

	public void setSessionMaxWaitMs(long sessionMaxWaitMs) {
		this.sessionMaxWaitMs = sessionMaxWaitMs;
	}

	public synchronized long getLastIntializeResult() {
		return lastIntializeResult;
	}

	public synchronized void setLastIntializeResult(long lastIntializeResult) {
		this.lastIntializeResult = lastIntializeResult;
	}
	
}
