package com.abl.nof.hsm;

import org.apache.commons.pool2.BasePooledObjectFactory;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.apache.log4j.Logger;

import safenet.jcprov.CK_SESSION_HANDLE;
import safenet.jcprov.Cryptoki;
import safenet.jcprov.constants.CKF;
import safenet.jcprov.constants.CKR;
import safenet.jcprov.constants.CKU;
import safenet.jcprov.constants.CK_RV;

/**
 * factory to create NofSafenetHsmSession objects for pooling
 *
 */
public class NofSafenetHsmSessionFactory extends BasePooledObjectFactory<NofSafenetHsmSession> {

	private static final Logger logger=Logger.getLogger(NofSafenetHsmSessionFactory.class);

	private NofSafenetHsmService nofSafenetHsmService;
	private int slotId;
	private char[] pin;
	
	public NofSafenetHsmSessionFactory(NofSafenetHsmService nofSafenetHsmService, int slotId, char[] pin) {
		this.nofSafenetHsmService = nofSafenetHsmService;
		this.slotId = slotId;
		this.pin = pin;
	}
	
	@Override
	public NofSafenetHsmSession create() throws NofHsmException {
		logger.debug("create");
		
		if (nofSafenetHsmService.getLastIntializeResult() != 0) {
			// if last attempt to initialize hsm fails, try to initialize hsm again
			synchronized(this) {
				nofSafenetHsmService.initializeHsm();
			}
		}
		
		CK_SESSION_HANDLE session = new CK_SESSION_HANDLE();
		
		// open session
		logger.debug("calling C_OpenSession");
		CK_RV rv = Cryptoki.C_OpenSession(slotId, CKF.RW_SESSION, null, null, session);
		logger.debug("C_OpenSession returns " + rv.longValue());
		if (rv.longValue() >= 2147483648L) {
			// if rv < 0x80000000, assume hsm need to reinitialize
			// initialize again
			logger.error("C_OpenSession returns " + rv.longValue());
			
			synchronized(this) {
				nofSafenetHsmService.setLastIntializeResult(-1);
				nofSafenetHsmService.finalizeHsm();
				nofSafenetHsmService.initializeHsm();
			}
			
			logger.debug("calling C_OpenSession again");
			rv = Cryptoki.C_OpenSession(slotId, CKF.RW_SESSION, null, null, session);
			logger.debug("C_OpenSession returns " + rv.longValue());
		}
		if (!rv.equals(CKR.OK)) {
			throw new NofHsmException("open session error", rv.longValue());
		}
		
		// login to safenet session
		logger.debug("calling C_Login");
		rv = Cryptoki.C_Login(session, CKU.USER, new String(pin).getBytes(), (long)pin.length);
		logger.debug("C_Login returns " + rv.longValue());
		if (rv.longValue() == 0x100) {
			logger.warn("hsm already login");
		} else if (!rv.equals(CKR.OK)) {
			throw new NofHsmException("hsm login error", rv.longValue());
		}
		
		NofSafenetHsmSession nofSafenetHsmSession = new NofSafenetHsmSession(session);
		logger.debug("created " + nofSafenetHsmSession);
		return nofSafenetHsmSession;
	}

	@Override
	public PooledObject<NofSafenetHsmSession> wrap(NofSafenetHsmSession session) {
		return new DefaultPooledObject<NofSafenetHsmSession>(session);
	}

	@Override
	public void destroyObject(PooledObject<NofSafenetHsmSession> p) throws Exception {
		logger.debug("destroy, p=" + p + ", session=" + p.getObject());
		try {
			p.getObject().close();
		} catch (Exception e) {
			logger.error("unable to close hsm session", e);
		}
		super.destroyObject(p);
	}

	@Override
	public boolean validateObject(PooledObject<NofSafenetHsmSession> p) {
		boolean result = super.validateObject(p) && p.getObject().isValid();
		logger.debug("validateObject returns false");
		return result;
	}

}
