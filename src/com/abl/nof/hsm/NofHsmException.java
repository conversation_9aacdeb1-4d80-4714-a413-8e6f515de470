package com.abl.nof.hsm;

@SuppressWarnings("serial")
public class NofHsmException extends Exception {

	private final Long errorCode;
	
	public NofHsmException() {
		super();
		this.errorCode = null;
	}

	public NofHsmException(String message, Throwable cause) {
		super(message, cause);
		this.errorCode = null;
	}

	public NofHsmException(String message) {
		super(message);
		this.errorCode = null;
	}

	public NofHsmException(Throwable cause) {
		super(cause);
		this.errorCode = null;
	}

	public NofHsmException(String message, long errorCode) {
		super(message);
		this.errorCode = errorCode;
	}
	
	@Override
	public String getMessage() {
		if (errorCode == null) {
			return super.getMessage();
		} else {
			return super.getMessage() + ": errorCode(hex)=" + Long.toHexString(errorCode);
		}
	}

	public Long getErrorCode() {
		return errorCode;
	}
}
