package com.abl.nof.hsm;

import org.springframework.beans.factory.InitializingBean;

import com.abl.crypto.CryptoUtils;
import com.abl.utils.ByteUtils;

/**
 * this is for development testing only
 * 
 * DO NOT USE IN UAT or PRODUCTION
 *
 */
public class TestNofHsmService implements NofHsmService, InitializingBean {
	
	private String dpk="00000000000000000000000000000000";
	private String kis="********************************";
	private byte[] bDpk;
	private byte[] bKis;
	
	@Override
	public void afterPropertiesSet() throws Exception {
		bDpk = ByteUtils.hexToBytes(dpk);
		bKis = ByteUtils.hexToBytes(kis);
	}
	
	@Override
	public void addEventListener(NofHsmServiceEventListener listener) {
		// nothing
	}

	@Override
	public Object unwrapSessionKeyWithKis(byte[] sessionKey) throws NofHsmException {
		if (sessionKey == null) {
			throw new NofHsmException("sessionKey is null");
		} else if (sessionKey.length % 8 != 0) {
			throw new NofHsmException("invalid sessionKey.length " + sessionKey.length);
		}
		return CryptoUtils.decryptDes3Cbc(bKis, sessionKey);
	}

	@Override
	public byte[] generateMac(Object sessionKey, byte[] data) throws NofHsmException {
		if (sessionKey == null) {
			throw new NofHsmException("sessionKey is null");
		} else {
			byte[] macKey = (byte[]) sessionKey;
			return CryptoUtils.macDes3(macKey, data);
		}
	}

	@Override
	public boolean verifyMac(Object sessionKey, byte[] data, byte[] mac) throws NofHsmException {
		if (sessionKey == null) {
			throw new NofHsmException("sessionKey is null");
		} else {
			return true;
		}
	}

	@Override
	public byte[] encryptWithDpk(byte[] data) throws NofHsmException {
		byte[] padData;
		if (data == null) {
			throw new NofHsmException("data is null");
		} else if (data.length % 8 != 0) {
			int rem = 8 - data.length % 8;
			padData = ByteUtils.padRight(data, (byte)0x30, rem);
		} else {
			padData = data;
		}
		return CryptoUtils.encryptDes3Cbc(bDpk, padData);
	}

	@Override
	public byte[] decryptWithDpk(byte[] data) throws NofHsmException {
		if (data == null) {
			throw new NofHsmException("data is null");
		} else if (data.length % 8 != 0) {
			throw new NofHsmException("invalid data.length " + data.length);
		}
		return CryptoUtils.decryptDes3Cbc(bDpk, data);
	}

	@Override
	public byte[] encryptWithMk(byte[] data) throws NofHsmException {
		try {
			return PaddingUtils.addPaddingMethod2(data, 16);
		} catch (Exception e) {
			throw new NofHsmException("Exception caught", e);
		}
	}

	@Override
	public byte[] decryptWithMk(byte[] data) throws NofHsmException {
		try {
			return PaddingUtils.removePaddingMethod2(data);
		} catch (Exception e) {
			throw new NofHsmException("Exception caught", e);
		}
	}

	@Override
	public byte[] getKcv(Object key) throws NofHsmException {
		// dummy implementation
		return new byte[] {0,0,0};
	}
	
	public String getDpk() {
		return dpk;
	}

	public void setDpk(String dpk) {
		this.dpk = dpk;
	}

	public String getKis() {
		return kis;
	}

	public void setKis(String kis) {
		this.kis = kis;
	}
	
}
