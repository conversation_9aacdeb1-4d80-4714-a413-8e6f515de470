package com.abl.nof.hsm;

import org.apache.log4j.Logger;

import safenet.jcprov.CK_ATTRIBUTE;
import safenet.jcprov.CK_MECHANISM;
import safenet.jcprov.CK_OBJECT_HANDLE;
import safenet.jcprov.CK_SESSION_HANDLE;
import safenet.jcprov.Cryptoki;
import safenet.jcprov.LongRef;
import safenet.jcprov.constants.CKA;
import safenet.jcprov.constants.CKK;
import safenet.jcprov.constants.CKM;
import safenet.jcprov.constants.CKO;
import safenet.jcprov.constants.CKR;
import safenet.jcprov.constants.CK_RV;

public class NofSafenetHsmSession {

	private static final Logger logger=Logger.getLogger(NofSafenetHsmSession.class);
	
	public static final String HSM_DPK = "DPK";
	public static final String HSM_KIS = "KIS";
	public static final String HSM_MK = "MK";
	
	private CK_SESSION_HANDLE session;
	private CK_OBJECT_HANDLE dpk;	// cache DPK
	private CK_OBJECT_HANDLE mk;	// cache MK

	public NofSafenetHsmSession(CK_SESSION_HANDLE session) {
		this.session = session;
	}
	
	public void close() throws NofHsmException {
		if (session != null) {
			CK_RV rv = Cryptoki.C_CloseSession(session);
			if (!rv.equals(CKR.OK)) {
				throw new NofHsmException("close session error", rv.longValue());
			}
			session = null;
		}
	}
	
	public boolean isValid() {
		return session.isValidHandle();
	}
	
	/**
	 * find and return DPK from hsm
	 * 
	 * @return
	 * @throws NofHsmException
	 */
	public synchronized CK_OBJECT_HANDLE getDpk() throws NofHsmException {
		if (dpk == null) {
			dpk = findObject(HSM_DPK, CKK.DES2);
		}
		return dpk;
	}
	
	/**
	 * find and return KIS from hsm
	 * 
	 * @return
	 * @throws NofHsmException
	 */
	public synchronized CK_OBJECT_HANDLE getKis() throws NofHsmException {
		return findObject(HSM_KIS, CKK.DES2);
	}
	
	/**
	 * find and return MK from hsm
	 * 
	 * @return
	 * @throws NofHsmException
	 */
	public synchronized CK_OBJECT_HANDLE getMk() throws NofHsmException {
		if (mk == null) {
			mk = findObject(HSM_MK, CKK.AES);
		}
		return mk;
	}
	
	/**
	 * find object with label "label" and key type "keyType"
	 * 
	 * @param label - key label (cannot be null)
	 * @param keyType - ley type (cannt be null)
	 * @return
	 * @throws NofHsmException
	 */
	private synchronized CK_OBJECT_HANDLE findObject(String label, CKK keyType) throws NofHsmException {
		CK_ATTRIBUTE[] attributes = new CK_ATTRIBUTE[2];
		attributes[0] = new CK_ATTRIBUTE(CKA.LABEL, label.getBytes());
		attributes[1] = new CK_ATTRIBUTE(CKA.KEY_TYPE, keyType);
		
		logger.debug("calling C_FindObjectsInit");
		CK_RV rv = Cryptoki.C_FindObjectsInit(session, attributes, attributes.length);
		logger.debug("C_FindObjectsInit returns " + rv.longValue());
		if (!rv.equals(CKR.OK)) {
			if (isInvalidSession(rv)) {
				throw new NofHsmInvalidSessionException("error calling C_FindObjectsInit, label=" + label, rv.longValue());
			} else {
				throw new NofHsmException("error calling C_FindObjectsInit, label=" + label, rv.longValue());
			}
		}
		CK_OBJECT_HANDLE[] hObjects = new CK_OBJECT_HANDLE[1];
		hObjects[0] = new CK_OBJECT_HANDLE();
		LongRef objectCount = new LongRef();
		
		logger.debug("calling C_FindObjects");
		rv = Cryptoki.C_FindObjects(session, hObjects, hObjects.length, objectCount);
		logger.debug("C_FindObjects returns " + rv.longValue());
		if (!rv.equals(CKR.OK)) {
			if (isInvalidSession(rv)) {
				throw new NofHsmInvalidSessionException("error calling C_FindObjects, label=" + label, rv.longValue());
			} else {
				throw new NofHsmException("error calling C_FindObjects, label=" + label, rv.longValue());
			}
		}
		
		logger.debug("calling C_FindObjectsFinal");
		rv = Cryptoki.C_FindObjectsFinal(session);
		logger.debug("C_FindObjectsFinal returns " + rv.longValue());
		if (!rv.equals(CKR.OK)) {
			if (isInvalidSession(rv)) {
				throw new NofHsmInvalidSessionException("error calling C_FindObjectsFinal, label=" + label, rv.longValue());
			} else {
				throw new NofHsmException("error calling C_FindObjectsFinal, label=" + label, rv.longValue());
			}
		}
		return hObjects[0];
	}
	
	/**
	 * unwrap session key "sessionKey" with KIS and return key.
	 * The unwrapped key is temporary
	 * 
	 * @param sessionKey
	 * @return
	 * @throws NofHsmException
	 */
	public synchronized Object unwrapSessionKeyWithKis(byte[] sessionKey) throws NofHsmException {
		CK_OBJECT_HANDLE kis = getKis();
		if (kis == null) {
			throw new NofHsmException("kis is null");
		}
		CK_MECHANISM mechanism = new CK_MECHANISM(CKM.DES3_ECB);
		CK_OBJECT_HANDLE hKey = new CK_OBJECT_HANDLE();
		CK_ATTRIBUTE[] attributes = new CK_ATTRIBUTE[9];
		attributes[0] = new CK_ATTRIBUTE(CKA.CLASS, CKO.SECRET_KEY);
		attributes[1] = new CK_ATTRIBUTE(CKA.KEY_TYPE, CKK.DES2);
		attributes[2] = new CK_ATTRIBUTE(CKA.TOKEN, Boolean.FALSE);	// false means temporary
		attributes[3] = new CK_ATTRIBUTE(CKA.DECRYPT, Boolean.TRUE);
		attributes[4] = new CK_ATTRIBUTE(CKA.ENCRYPT, Boolean.TRUE);
		attributes[5] = new CK_ATTRIBUTE(CKA.SIGN, Boolean.TRUE);
		attributes[6] = new CK_ATTRIBUTE(CKA.VERIFY, Boolean.TRUE);
		attributes[7] = new CK_ATTRIBUTE(CKA.SENSITIVE, Boolean.TRUE);
		attributes[8] = new CK_ATTRIBUTE(CKA.PRIVATE, Boolean.TRUE);
		
		logger.debug("calling C_UnwrapKey");
		CK_RV rv = Cryptoki.C_UnwrapKey(session, mechanism, kis, sessionKey, sessionKey.length, attributes, attributes.length, hKey);
		logger.debug("C_UnwrapKey returns " + rv.longValue());
		if (!rv.equals(CKR.OK)) {
			if (isInvalidSession(rv)) {
				throw new NofHsmInvalidSessionException("error calling C_UnwrapKey", rv.longValue());
			} else if (isInvalidKeyError(rv)) {
				throw new NofHsmInvalidKeyException("error calling C_UnwrapKey", rv.longValue());
			} else {
				throw new NofHsmException("error calling C_UnwrapKey", rv.longValue());
			}
		}
		return hKey;
	}
	
	/**
	 * generate mac on "data" using 3DES key "sessionKey".
	 * 
	 * @param sessionKey
	 * @param data
	 * @return 8 byte mac
	 * @throws NofHsmException
	 */
	public synchronized byte[] generateMac(CK_OBJECT_HANDLE sessionKey, byte[] data) throws NofHsmException {
		CK_MECHANISM mechanism = new CK_MECHANISM(CKM.DES3_MAC_GENERAL, 8L);
		
		logger.debug("calling C_SignInit");
		CK_RV rv = Cryptoki.C_SignInit(session, mechanism, sessionKey);
		logger.debug("C_SignInit returns " + rv.longValue());
		if (!rv.equals(CKR.OK)) {
			if (isInvalidSession(rv)) {
				throw new NofHsmInvalidSessionException("error calling C_SignInit", rv.longValue());
			} else if (isInvalidKeyError(rv)) {
				throw new NofHsmInvalidKeyException("error calling C_SignInit", rv.longValue());
			} else {
				throw new NofHsmException("error calling C_SignInit", rv.longValue());
			}
		}
		
        LongRef lRefDec = new LongRef(8);
        byte[] data2 = new byte[(int)lRefDec.value];
        
        logger.debug("calling C_Sign");
        rv = Cryptoki.C_Sign(session, data, data.length, data2, lRefDec);
        logger.debug("C_Sign returns " + rv.longValue());
        if (!rv.equals(CKR.OK)) {
			if (isInvalidSession(rv)) {
				throw new NofHsmInvalidSessionException("error calling C_Sign", rv.longValue());
			} else if (isInvalidKeyError(rv)) {
				throw new NofHsmInvalidKeyException("error calling C_Sign", rv.longValue());
			} else {
				throw new NofHsmException("error calling C_Sign", rv.longValue());
			}
		}
        
        int data2Len = (int)lRefDec.value;
        byte[] result = new byte[data2Len];
        System.arraycopy(data2, 0, result, 0, data2Len);
        
        return result;
	}
	
	/**
	 * verifies mac using sessionKey
	 * 
	 * @param sessionKey
	 * @param data
	 * @param mac
	 * @return true if mac is verified
	 * @throws NofHsmException
	 */
	public synchronized boolean verifyMac(CK_OBJECT_HANDLE sessionKey, byte[] data, byte[] mac) throws NofHsmException {		
		CK_MECHANISM mechanism = new CK_MECHANISM(CKM.DES3_MAC_GENERAL, 8L);
		
		logger.debug("calling C_VerifyInit");
		CK_RV rv = Cryptoki.C_VerifyInit(session, mechanism, sessionKey);
		logger.debug("C_VerifyInit returns " + rv.longValue());
		if (!rv.equals(CKR.OK)) {
			if (isInvalidSession(rv)) {
				throw new NofHsmInvalidSessionException("error calling C_VerifyInit", rv.longValue());
			} else if (isInvalidKeyError(rv)) {
				throw new NofHsmInvalidKeyException("error calling C_VerifyInit", rv.longValue());
			} else {
				throw new NofHsmException("error calling C_VerifyInit", rv.longValue());
			}
		}
        
        logger.debug("calling C_Verify");
        rv = Cryptoki.C_Verify(session, data, data.length, mac, mac.length);
        logger.debug("C_Verify returns " + rv.longValue());
        if (!rv.equals(CKR.OK)) {
			if (isInvalidSession(rv)) {
				throw new NofHsmInvalidSessionException("error calling C_Verify", rv.longValue());
			} else if (isInvalidKeyError(rv)) {
				throw new NofHsmInvalidKeyException("error calling C_Verify", rv.longValue());
			} else {
				throw new NofHsmException("error calling C_Verify", rv.longValue());
			}
		}
		
		return true;
	}
	
	/**
	 * encrypt with DPK using DES3_CBC
	 * 
	 * @param data
	 * @return encrypted data
	 * @throws NofHsmException
	 */
	public synchronized byte[] encryptWithDpk(byte[] data) throws NofHsmException {
		CK_OBJECT_HANDLE dpk = getDpk();
		if (dpk == null) {
			throw new NofHsmException("dpk is null");
		}
		CK_MECHANISM mechanism = new CK_MECHANISM(CKM.DES3_CBC);
		
		logger.debug("calling C_EncryptInit");
		CK_RV rv = Cryptoki.C_EncryptInit(session, mechanism, dpk);
		logger.debug("C_EncryptInit returns " + rv.longValue());
        if (!rv.equals(CKR.OK)) {
			if (isInvalidSession(rv)) {
				throw new NofHsmInvalidSessionException("error calling C_EncryptInit", rv.longValue());
			} else if (isInvalidKeyError(rv)) {
				throw new NofHsmInvalidKeyException("error calling C_EncryptInit", rv.longValue());
			} else {
				throw new NofHsmException("error calling C_EncryptInit", rv.longValue());
			}
		}
		
        LongRef lRefDec = new LongRef(data.length);
        byte[] data2 = new byte[(int)lRefDec.value];
        
        logger.debug("calling C_Encrypt");
        rv = Cryptoki.C_Encrypt(session, data, data.length, data2, lRefDec);
        logger.debug("C_Encrypt returns " + rv.longValue());
        if (!rv.equals(CKR.OK)) {
        	if (isInvalidSession(rv)) {
				throw new NofHsmInvalidSessionException("error calling C_Encrypt", rv.longValue());
			} else if (isInvalidKeyError(rv)) {
				throw new NofHsmInvalidKeyException("error calling C_Encrypt", rv.longValue());
			} else {
				throw new NofHsmException("error calling C_Encrypt", rv.longValue());
			}
		}
        
        int data2Len = (int)lRefDec.value;
        byte[] result = new byte[data2Len];
        System.arraycopy(data2, 0, result, 0, data2Len);
        
        return result;
	}

	/**
	 * decrypt with DPK using DES3_CBC
	 * 
	 * @param data
	 * @return decrypted data
	 * @throws NofHsmException
	 */
	public synchronized byte[] decryptWithDpk(byte[] data) throws NofHsmException {
		CK_OBJECT_HANDLE dpk = getDpk();
		if (dpk == null) {
			throw new NofHsmException("dpk is null");
		}
		CK_MECHANISM mechanism = new CK_MECHANISM(CKM.DES3_CBC);
		
		logger.debug("calling C_DecryptInit");
		CK_RV rv = Cryptoki.C_DecryptInit(session, mechanism, dpk);
		logger.debug("C_DecryptInit returns " + rv.longValue());
		if (!rv.equals(CKR.OK)) {
        	if (isInvalidSession(rv)) {
				throw new NofHsmInvalidSessionException("error calling C_DecryptInit", rv.longValue());
			} else if (isInvalidKeyError(rv)) {
				throw new NofHsmInvalidKeyException("error calling C_DecryptInit", rv.longValue());
			} else {
				throw new NofHsmException("error calling C_DecryptInit", rv.longValue());
			}
		}
		
        LongRef lRefDec = new LongRef(data.length);
        byte[] data2 = new byte[(int)lRefDec.value];
        
        logger.debug("calling C_Decrypt");
        rv = Cryptoki.C_Decrypt(session, data, data.length, data2, lRefDec);
        logger.debug("C_Decrypt returns " + rv.longValue());
        if (!rv.equals(CKR.OK)) {
        	if (isInvalidSession(rv)) {
				throw new NofHsmInvalidSessionException("error calling C_Decrypt", rv.longValue());
			} else if (isInvalidKeyError(rv)) {
				throw new NofHsmInvalidKeyException("error calling C_Decrypt", rv.longValue());
			} else {
				throw new NofHsmException("error calling C_Decrypt", rv.longValue());
			}
		}
        
        int data2Len = (int)lRefDec.value;
        byte[] result = new byte[data2Len];
        System.arraycopy(data2, 0, result, 0, data2Len);
        
        return result;
	}

	/**
	 * encrypt with MK using AES_CBC
	 * 
	 * @param data
	 * @return encrypted data
	 * @throws NofHsmException
	 */
	public synchronized byte[] encryptWithMk(byte[] data) throws NofHsmException {
		CK_OBJECT_HANDLE mk = getMk();
		if (mk == null) {
			throw new NofHsmException("mk is null");
		}
		CK_MECHANISM mechanism = new CK_MECHANISM(CKM.AES_CBC);
		
		logger.debug("calling C_EncryptInit");
		CK_RV rv = Cryptoki.C_EncryptInit(session, mechanism, mk);
		logger.debug("C_EncryptInit returns " + rv.longValue());
        if (!rv.equals(CKR.OK)) {
        	if (isInvalidSession(rv)) {
				throw new NofHsmInvalidSessionException("error calling C_EncryptInit", rv.longValue());
			} else if (isInvalidKeyError(rv)) {
				throw new NofHsmInvalidKeyException("error calling C_EncryptInit", rv.longValue());
			} else {
				throw new NofHsmException("error calling C_EncryptInit", rv.longValue());
			}
		}
		
        LongRef lRefDec = new LongRef(data.length);
        byte[] data2 = new byte[(int)lRefDec.value];
        
        logger.debug("calling C_Encrypt");
        rv = Cryptoki.C_Encrypt(session, data, data.length, data2, lRefDec);
        logger.debug("C_Encrypt returns " + rv.longValue());
        if (!rv.equals(CKR.OK)) {
        	if (isInvalidSession(rv)) {
				throw new NofHsmInvalidSessionException("error calling C_Encrypt", rv.longValue());
			} else if (isInvalidKeyError(rv)) {
				throw new NofHsmInvalidKeyException("error calling C_Encrypt", rv.longValue());
			} else {
				throw new NofHsmException("error calling C_Encrypt", rv.longValue());
			}
		}
        
        int data2Len = (int)lRefDec.value;
        byte[] result = new byte[data2Len];
        System.arraycopy(data2, 0, result, 0, data2Len);
        
        return result;
	}

	/**
	 * decrypt with MK using AES_CBC
	 * 
	 * @param data
	 * @return decrypted data
	 * @throws NofHsmException
	 */
	public synchronized byte[] decryptWithMk(byte[] data) throws NofHsmException {
		CK_OBJECT_HANDLE mk = getMk();
		if (mk == null) {
			throw new NofHsmException("mk is null");
		}
		CK_MECHANISM mechanism = new CK_MECHANISM(CKM.AES_CBC);
		
		logger.debug("calling C_DecryptInit");
		CK_RV rv = Cryptoki.C_DecryptInit(session, mechanism, mk);
		logger.debug("C_DecryptInit returns " + rv.longValue());
		if (!rv.equals(CKR.OK)) {
        	if (isInvalidSession(rv)) {
				throw new NofHsmInvalidSessionException("error calling C_DecryptInit", rv.longValue());
			} else if (isInvalidKeyError(rv)) {
				throw new NofHsmInvalidKeyException("error calling C_DecryptInit", rv.longValue());
			} else {
				throw new NofHsmException("error calling C_DecryptInit", rv.longValue());
			}
		}
		
        LongRef lRefDec = new LongRef(data.length);
        byte[] data2 = new byte[(int)lRefDec.value];
        
        logger.debug("calling C_Decrypt");
        rv = Cryptoki.C_Decrypt(session, data, data.length, data2, lRefDec);
        logger.debug("C_Decrypt returns " + rv.longValue());
        if (!rv.equals(CKR.OK)) {
        	if (isInvalidSession(rv)) {
				throw new NofHsmInvalidSessionException("error calling C_Decrypt", rv.longValue());
			} else if (isInvalidKeyError(rv)) {
				throw new NofHsmInvalidKeyException("error calling C_Decrypt", rv.longValue());
			} else {
				throw new NofHsmException("error calling C_Decrypt", rv.longValue());
			}
		}
        
        int data2Len = (int)lRefDec.value;
        byte[] result = new byte[data2Len];
        System.arraycopy(data2, 0, result, 0, data2Len);
        
        return result;
	}
	
	public byte[] getKcv(CK_OBJECT_HANDLE key) throws NofHsmException {
		CK_ATTRIBUTE[] attribs = new CK_ATTRIBUTE[1];
		attribs[0] = new CK_ATTRIBUTE(); 
		attribs[0].valueLen = 0;
		attribs[0].type = CKA.CHECK_VALUE;
		
		logger.debug("C_GetAttributeValue 1, attribs.length=" + attribs.length);
		CK_RV rv = Cryptoki.C_GetAttributeValue(session, key, attribs, attribs.length);
		if (!rv.equals(CKR.OK)) {
			throw new NofHsmException("C_GetAttributeValue error", rv.longValue());
		}
		logger.debug("C_GetAttributeValue 1 done");
		
		for (CK_ATTRIBUTE attrib: attribs) {
			attrib.pValue = new byte[(int)attrib.valueLen];
		}
		
		logger.debug("C_GetAttributeValue 2");
		rv = Cryptoki.C_GetAttributeValue(session, key, attribs, attribs.length);
		if (!rv.equals(CKR.OK)) {
			throw new NofHsmException("C_GetAttributeValue error", rv.longValue());
		}
		logger.debug("C_GetAttributeValue 2 done");
		
		byte[] kcv = (byte[])attribs[0].pValue;
		return kcv;
	}
	
	private boolean isInvalidKeyError(CK_RV rv) {
		return rv.equals(CKR.KEY_HANDLE_INVALID);
	}
	
	private boolean isInvalidSession(CK_RV rv) {
		// 2147483648 = 0x80000000
		// assume that if rv value is >= 0x80000000 in hex,
		// then then session is invalid, and is not-recoverable without calling NofSafenetHsmService.initializeHsm
		return rv.longValue() >= 2147483648L;
	}
}
