package com.abl.nof;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.abl.db.service.SequenceService;

@Component
public class NofRrnGenerator {

	private static final Logger logger=Logger.getLogger(NofRrnGenerator.class);
	
	private static final String SEQUENCE_NAME="TMSQ_NOF_RRN";
	
	@Autowired private SequenceService sequenceService;
	
	/**
	 * get next sequence without throwing exceptions

	 * @return
	 */
	private Long nextSequence() {
		try {
			return sequenceService.nextSequence(SEQUENCE_NAME);
		} catch (Exception e) {
			logger.error("unable to get next seq for " + SEQUENCE_NAME, e);
			return null;
		}
	}
	
	/**
	 * generate rrn
	 * throws exception if unable to gen rrn
	 * 
	 * @return
	 */
	public String nextRrn() {
		logger.info("get next sequence for " + SEQUENCE_NAME);
		Long seq = nextSequence();
		if (seq == null) {
			// if unable to get next sequence, assume sequence not created
			// create sequence
			logger.info("creating sequence " + SEQUENCE_NAME);
			sequenceService.createSequence(SEQUENCE_NAME);
			// get next sequence again
			logger.info("get next sequence for " + SEQUENCE_NAME);
			seq = sequenceService.nextSequence(SEQUENCE_NAME);
		}
		String rrn = String.format("%010d", seq);
		if (rrn.length() > 10) {
			rrn = rrn.substring(rrn.length()-10);
		}
		// from nets specs: First 2 digits of RRN shall always be zeroes 
		return "00" + rrn;
	}

}
