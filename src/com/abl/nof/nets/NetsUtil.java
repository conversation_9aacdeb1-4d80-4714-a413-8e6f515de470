package com.abl.nof.nets;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;

import com.abl.nof.nets.api.F126Table1;
import com.abl.nof.nets.api.F126Table2;
import com.abl.nof.nets.api.F126Table3;
import com.abl.nof.nets.api.F126Table4;
import com.abl.nof.nets.api.F126Table5;
import com.abl.nof.nets.api.F126Table51;
import com.abl.nof.nets.api.F126Table52;
import com.abl.nof.nets.api.F126Table53;
import com.abl.nof.nets.api.F126Tlv;

public class NetsUtil {
	
	private static final Logger logger = Logger.getLogger(NetsUtil.class);
	
	/**
	 * parse f126 string into a list of TLVs
	 * 
	 * @param f126
	 * @return
	 * @throws Exception
	 */
	public static List<F126Tlv> parseF126(String f126) throws Exception {
		List<F126Tlv> list = new ArrayList<F126Tlv>();
		if (f126 == null) {
			return list;
		}
		String remainingF126 = f126;
		while (true) {
			F126Tlv tlv = parseF126Tlv(remainingF126);
			if (tlv == null) {
				logger.debug("no more tables in f126");
				break;
			}
			logger.debug("tlv: tableId=" + tlv.getTableId() + ", len=" + tlv.getLen());
			list.add(tlv);
			remainingF126 = remainingF126.substring(tlv.getLen() + 5);
		}
		logger.debug("num of tables in f126=" + list.size());
		return list;
	}
	
	/**
	 * parse the first tlv in the f126 string 
	 * 
	 * @param f126
	 * @return
	 * @throws Exception
	 */
	public static F126Tlv parseF126Tlv(String f126) throws Exception {
		if ((f126 == null)||(f126.length()==0)) {
			return null;
		}
		if (f126.length() < 5) {
			throw new Exception("unable to parse f126");
		}
		int tableId = Integer.parseInt(f126.substring(0, 2));
		logger.debug("tableId=" + tableId);
		int len = Integer.parseInt(f126.substring(2, 5));
		if (f126.length() < 5+len) {
			throw new Exception("unable to parse f126 for tableId=" + tableId + ": length too short");
		}
		String data = f126.substring(5, 5+len);
		switch (tableId) {
			case 1:
				return F126Table1.parse(data);
			case 2:
				return F126Table2.parse(data);
			case 3:
				return F126Table3.parse(data);
			case 4:
				return F126Table4.parse(data);
			case 5:
				return F126Table5.parse(data);
			case 51:
				return F126Table51.parse(data);
			case 52:
				return F126Table52.parse(data);
			case 53:
				return F126Table53.parse(data);
			default:
				// if unknown, just return base f126Tlv intead of throwing exception
				return new F126Tlv(tableId, data);
		}
	}
}
