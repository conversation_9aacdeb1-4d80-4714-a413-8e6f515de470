package com.abl.nof.nets;

import java.util.HashMap;
import java.util.Map;

import org.apache.log4j.Logger;
import org.jpos.iso.ISOMsg;

/**
 * to store unmatched network responses from nets
 *
 */
public class UnmatchResponseStore {

	private static Logger logger=Logger.getLogger(UnmatchResponseStore.class);
	
	public static final String KEY_ECHO="ECHO";
	public static final String KEY_LOGON="LOGON";
	public static final String KEY_CHANGE_KEY="CHANGE_KEY";
	
	// stores (at most) 1 UnmatchResponse for each of KEY_ECHO, KEY_LOGON, KEY_CHANGE_KEY
	private Map<String, UnmatchResponse> map = new HashMap<>();
	
	public UnmatchResponseStore() {
		map.put(KEY_ECHO, new UnmatchResponse(KEY_ECHO));
		map.put(KEY_LOGON, new UnmatchResponse(KEY_LOGON));
		map.put(KEY_CHANGE_KEY, new UnmatchResponse(KEY_CHANGE_KEY));
	}
	
	/**
	 * clear the map for key "key"
	 * 
	 * @param key
	 */
	public void clearResponse(String key) {
		UnmatchResponse response = map.get(key);
		if (response == null) {
			logger.error("clearResponse: unknown key " + key);
		} else {
			response.clearResponse();
		}
	}
	
	/**
	 * add a UnmatchResponse containing m into map for key "key"
	 * 
	 * @param key
	 * @param m
	 */
	public void addResponse(String key, ISOMsg m) {
		UnmatchResponse response = map.get(key);
		if (response == null) {
			logger.error("addResponse: unknown key " + key);
		} else {
			response.setResponse(m);
		}
	}
	
	/**
	 * get response from map for key "key"
	 * 
	 * @param key
	 * @return
	 */
	public ISOMsg getResponse(String key) {
		UnmatchResponse response = map.get(key);
		if (response == null) {
			logger.error("getResponse: unknown key " + key);
			return null;
		} else {
			return response.getResponse();
		}
	}
	
	/**
	 * get response from map for key "key"
	 * waiting at most timeoutMs for the response to be available
	 * 
	 * @param key
	 * @param timeoutMs
	 * @return
	 */
	public ISOMsg getResponse(String key, long timeoutMs) {
		UnmatchResponse response = map.get(key);
		if (response == null) {
			logger.error("getResponse: unknown key " + key);
			return null;
		} else {
			return response.getResponse(timeoutMs);
		}
	}
}
