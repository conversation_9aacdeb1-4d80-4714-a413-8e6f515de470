package com.abl.nof.nets;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.pool2.BasePooledObjectFactory;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class NetsTerminalFactory extends BasePooledObjectFactory<NetsTerminal> implements InitializingBean {

	private static final Logger logger=Logger.getLogger(NetsTerminalFactory.class);
	
    @Value("#{'${nets.terminal.tids:}'.split(',')}") private List<String> tids;
    @Value("#{'${nets.terminal.mids:}'.split(',')}") private List<String> mids;
    @Value("${nets.terminal.acquirerInstitutionId:}") private String acquirerInstitutionId;
    @Value("${nets.terminal.merchName:COMFORTDELGRO}") private String merchName;
    @Value("${nets.terminal.timeout:10000}") private long timeout;
    @Value("${nets.terminal.verifyMac:true}") private boolean verifyMac;
    
    @Autowired(required=false) private NetsStanGenerator netsStanGenerator;
    
    private List<NetsTerminal> netsTerminals = new ArrayList<NetsTerminal>();
    private int n=0;
    
    public int getNumberTerminals() {
    	return netsTerminals.size();
    }
    
    @Override
	public void afterPropertiesSet() throws Exception {
		if (mids.size() != tids.size()) {
			throw new Exception("mids and tids different size");
		}
        logger.debug("nets tids size : " + tids.size());
        
        for (int n=0; n<tids.size(); n++) {
        	String tid = tids.get(n);
        	String mid = mids.get(n);
        	logger.debug("tid : " + tid + ", mid=" + mid);
        	NetsTerminal netsTerminal = new NetsTerminal(mid, tid, merchName, timeout, verifyMac);
        	if (netsStanGenerator != null) {
        		netsTerminal.setNetsStanGenerator(netsStanGenerator);
        	}
        	netsTerminals.add(netsTerminal);
        }
	}
    
	@Override
	public NetsTerminal create() throws Exception {
		if (n >= netsTerminals.size()) {
			throw new Exception("unable to create terminal: no more terminals");
		}
		NetsTerminal netsTerminal = netsTerminals.get(n);
		n++;
		return netsTerminal;
	}

	@Override
	public PooledObject<NetsTerminal> wrap(NetsTerminal netsTerminal) {
		return new DefaultPooledObject<NetsTerminal>(netsTerminal);
	}

}
