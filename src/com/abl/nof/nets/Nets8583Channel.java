package com.abl.nof.nets;

import java.io.IOException;
import java.net.Socket;
import java.util.Date;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.jpos.core.Configuration;
import org.jpos.core.ConfigurationException;
import org.jpos.iso.BaseChannel;
import org.jpos.iso.ISOException;
import org.jpos.iso.ISOMsg;
import org.jpos.iso.ISOUtil;
import org.jpos.space.Space;
import org.jpos.space.SpaceFactory;

import com.abl.nof.hsm.NofHsmService;
import com.abl.nof.hsm.NofHsmServiceEventListener;
import com.abl.utils.AtomicModuloCounter;
import com.abl.utils.ByteUtils;
import com.abl.utils.SpringUtils;

/**
 * channel for nets ISO 8583 connection for NOF
 *
 * notes:
 * - a bean of type NofHsmService must be defined in spring context
 */
public class Nets8583Channel extends BaseChannel implements NofHsmServiceEventListener {

	private static final Logger logger=Logger.getLogger(Nets8583Channel.class);
	
    private NofHsmService netsHsmService;
    
    private static AtomicModuloCounter stanCounter = new AtomicModuloCounter(999999, 1);
    
    // maccing fields
	private final static int[] macFields0200 =  new int[] {3,4,7,11,12,13,22,37,41,126};
	private final static int[] macFields0210 =  new int[] {3,4,7,9,11,12,13,22,37,38,39,41,126};
	private final static int[] macFields0420 =  new int[] {3,4,7,9,11,12,13,22,37,39,41,90};
	private final static int[] macFields0430 =  new int[] {3,4,7,11,22,37,39,41};
	
    private Nets8583Header nets8583Header = new Nets8583Header();
    private Object macKey;	// mac key (hsm object)
    private Date macKeyDate;
    
    @Override
    public void hsmInitialized() {
    	// when hsm is initialized, it means the macKey is not valid anymore
    	// just disconnect the channel
    	// at connection establish, will do logon to get key again
    	try {
    		logger.debug("hsmInitialized: disconnecting");
			disconnect();
		} catch (IOException e) {
			logger.error("unable to disconnect", e);
		}
    }
    
    @Override
	public void setConfiguration(Configuration configuration) throws ConfigurationException {
    	super.setConfiguration(configuration);
    	netsHsmService = SpringUtils.getBean(NofHsmService.class, 10, 1000);
    	netsHsmService.addEventListener(this);
	}
    
    @Override
    protected byte[] readHeader(int hLen) throws IOException {
        byte[] header = new byte[hLen];
        serverIn.readFully(header, 0, hLen);

        if ( header[7] !=  0x30 || header[8] != 0x30 || header[9] != 0x30 )  {
            byte[] errorBytes = new byte[3];
            System.arraycopy(header, 7, errorBytes, 0, 3);
            logger.debug("Rejected Header : " + new String(errorBytes));
        }
        
        return header;
    }

    @Override
    protected int getHeaderLength() {
       // logger.debug("header length :  "+ nets8583Header.getHeader().length);
        return nets8583Header.getHeader().length;
    }

    @Override
    protected void sendMessageHeader(ISOMsg m, int len) throws IOException {
        //logger.debug("Send Message Header (String) : " + new String(nets8583Header.getHeader()).toUpperCase());
        serverOut.write(nets8583Header.getHeader());
    }

    @Override
    protected int getMessageLength() throws IOException, ISOException {
        byte[] b = new byte[2];
        serverIn.readFully(b,0,2);

        int len = Integer.parseInt(String.format("%02X%02X", b[0], b[1]), 16);
        return len;
    }

    @Override
    protected void sendMessageLength(int len) throws IOException {
        String lenHex = "0000" + Integer.toHexString(len);
        lenHex = lenHex.substring( lenHex.length()-4, lenHex.length() );
        serverOut.write( ISOUtil.hex2byte( lenHex ) );
    }

    @Override
    protected void sendMessage(byte[] b, int offset, int len) throws IOException {
        super.sendMessage(b, offset, len);
    }

    @Override
    protected void getMessage(byte[] b, int offset, int len) throws IOException, ISOException {
        super.getMessage(b, offset, len);
    }

	@Override
    protected void connect (Socket socket) throws IOException {
		logger.debug("connected, port=" + socket.getPort());
        super.connect(socket);
        if (macKey != null) {
        	logger.debug("mackey is not null");
        	enableQmux();
		}
    }
    
    @Override
	public void disconnect() throws IOException {
    	logger.debug("disconnect");
		super.disconnect();
		disableQmux();
	}

    /**
     * enable qmux
     */
	private void enableQmux() {
		// put ready into space
		// QMUX will read this to determine if the channel is ready
		// (QMUX "ready" config must be configured to match)
		Space sp = SpaceFactory.getSpace("");
		String ready = getName() + ".mac";
		logger.debug("ready = " + ready);
		sp.out(ready, macKeyDate);
	}
	
	/**
	 * disable qmux
	 */
	private void disableQmux() {
		// remove ready from space
		Space sp = SpaceFactory.getSpace("");
		String ready = getName() + ".mac";
		logger.debug("ready = " + ready);
		// ready and remove ready from space, if present
		sp.inp(ready);
	}
	
	/**
     * parse new key msg, and unwraps the new mac key to store in this channel
     * 
     * @param isoMsg
     */
    public void processNewKey(ISOMsg isoMsg) {
    	// process new key
    	boolean result = doProcessNewKey(isoMsg);
    	logger.debug("doProcessNewKey returns " + result);
    	
    	if (result) {
    		// verify the mac of the new key request
    		try {
				boolean verifyMacResult = verifyMac(isoMsg);
				if (verifyMacResult) {
					logger.warn("verify mac ok");
				} else {
					logger.error("verify mac fail");	
				}
			} catch (Exception e) {
				logger.error("unable to verify mac", e);
			}
    		
    	}
    	
    	// send response back
    	try {
    		ISOMsg respIso = (ISOMsg)isoMsg.clone();
    		respIso.setMTI("0810");
	    	if (result) {
	    		respIso.set(39, "00");
	    		respIso.set(123, "CSM(MCL/RSM RCV/NETS ORG/ACQM )");
	    	} else {
	    		respIso.set(39, "99");
	    	}
	        logger.debug("sending new key response to NETS");
	        respIso.setDirection(ISOMsg.OUTGOING);
	        this.send(respIso);
    	} catch (Exception e) {
    		logger.error("unable to send new key response to nets", e);
    	}
    }
    
    private boolean doProcessNewKey(ISOMsg isoMsg) {
    	if (!isoMsg.hasField(123)) {
    		logger.error("field 123 missing for new key request");
    		return false;
    	}
    	if (!isoMsg.hasField(120)) {
    		logger.error("field 120 missing for new key request");
    		return false;
    	}
    	String f123 = isoMsg.getString(123);
    	int index = f123.indexOf(" KD/");
    	if (index < 0) {
    		logger.error("process new key: KD/ not found in f123: " + f123);
    		return false;
    	}
    	String f120 = isoMsg.getString(120);
    	
    	String strMacKey = f123.substring(index+4, index+4+32);
    	byte[] bMacKey = ByteUtils.hexToBytes(strMacKey);
    	// this key should be wrapped using DPK
    	try {
    		// unwrap mac key
    		Object unwrappedMacKey = netsHsmService.unwrapSessionKeyWithKis(bMacKey);
			
    		// verify kcv with f120
			byte[] kcv = netsHsmService.getKcv(unwrappedMacKey);
			String kcvStr = ByteUtils.bytesToHex(kcv);
			if (kcvStr.equalsIgnoreCase(f120)) {
				logger.info("kcv for new key ok:" + f120);
			} else {
				logger.error("kcv for new key does not match, expected=" + f120 + ", actual=" + kcvStr);
				return false;
			}
			setMacKey(unwrappedMacKey);
		} catch (Exception e) {
			logger.error("unable to unwrap mac key", e);
			return false;
		}
    	
    	return true;
    }

    public synchronized boolean hasMacKey() {
		return macKey!=null;
	}
    
	public synchronized Object getMacKey() {
		return macKey;
	}
	
	public synchronized void setMacKey(Object macKey) {
		this.macKey = macKey;
		macKeyDate = new Date();
		
		if (macKey != null) {
			enableQmux();
		}
	}

	public synchronized Date getMacKeyDate() {
		return macKeyDate;
	}

	public synchronized void setMacKeyDate(Date macKeyDate) {
		this.macKeyDate = macKeyDate;
	}
    
	public String getStan() {
		return StringUtils.leftPad(Long.toString(stanCounter.incrementAndGet()), 6,'0');
	}
	
	public byte[] generateMac(ISOMsg isoMsg) throws Exception {
		if (isoMsg.getPackager() == null) {
			isoMsg.setPackager(getPackager());
		}
		byte[] header = Nets8583Header.HEADER;
		
		// set field 128 to 0 first, in case it is not set
		isoMsg.set(128, new byte[8]);
		
		byte[] data = isoMsg.pack();
		logger.debug("pack data = " + ByteUtils.bytesToHex(data));
		
		// take the MTI + bitmap
		byte[] macData1 = new byte[4+32];
		System.arraycopy(data, 0, macData1, 0, 4+32);
		logger.debug("macData1 = " + ByteUtils.bytesToHex(macData1));

		// create a tmp isoMsg containing only the required fields for maccing
		ISOMsg tmpIsoMsg;
		String mti = isoMsg.getMTI();
		if ("0200".equals(mti)) {
			logger.debug("generating mac using 0200 fields");
			tmpIsoMsg = createTmpIsoMsgForMaccing(isoMsg, macFields0200);
		} else if ("0420".equals(mti) || "0421".equals(mti)) {
			logger.debug("generating mac using 0421/0421 fields");
			tmpIsoMsg = createTmpIsoMsgForMaccing(isoMsg, macFields0420);
		} else {
			logger.debug("generating mac using all fields");
			tmpIsoMsg = createTmpIsoMsgForMaccing(isoMsg, null);
		}
		
		// pack tmpIsoMsg
		byte[] data2 = tmpIsoMsg.pack();
		logger.debug("data2 = " + ByteUtils.bytesToHex(data2));
		
		// remove MTI, bitmap, and mac from packed tmpIsoMsg
		byte[] macData2 = new byte[data2.length-4-32-16];
		System.arraycopy(data2, 4+32, macData2, 0, data2.length-4-32-16);
		logger.debug("macData2 = " + ByteUtils.bytesToHex(macData2));
		
		// create mac data
		byte[] macData = ByteUtils.concat(header, macData1, macData2);
		logger.debug("mac data = " + ByteUtils.bytesToHex(macData));
		
		NofHsmService netsHsmService = SpringUtils.getBean(NofHsmService.class, 10, 1000);
		logger.debug("generating mac, macKeyDate = " + getMacKeyDate());
		
		byte[] mac = netsHsmService.generateMac(getMacKey(), macData);
		logger.debug("mac = " + ByteUtils.bytesToHex(mac));
		
		return mac;
	}
	
	public boolean verifyMac(ISOMsg isoMsg) throws Exception {
		
		// bitmap length
		// if has secondary bitmap, will be 32; else is 16
		int bitmapLen=16;
		
		if (isoMsg.hasField(64)) {
			bitmapLen=16;
		} else if (isoMsg.hasField(128)) {
			bitmapLen=32;
		} else {
			logger.error("msg does not have field 64 or 128");
			return false;
		}
		
		if (isoMsg.getPackager() == null) {
			isoMsg.setPackager(getPackager());
		}
		byte[] header = isoMsg.getHeader();
		byte[] data = isoMsg.pack();
		logger.debug("pack data = " + ByteUtils.bytesToHex(data));
		
		// take the MTI + bitmap
		byte[] macData1 = new byte[4+bitmapLen];
		System.arraycopy(data, 0, macData1, 0, 4+bitmapLen);
		logger.debug("macData1 = " + ByteUtils.bytesToHex(macData1));
		
		// create a tmp isoMsg containing only the required fields for maccing
		ISOMsg tmpIsoMsg;
		String mti = isoMsg.getMTI();
		if ("0210".equals(mti)) {
			logger.debug("generating mac using 0210 fields");
			tmpIsoMsg = createTmpIsoMsgForMaccing(isoMsg, macFields0210);
		} else if ("0430".equals(mti)) {
			logger.debug("generating mac using 0430 fields");
			tmpIsoMsg = createTmpIsoMsgForMaccing(isoMsg, macFields0430);
		} else {
			logger.debug("generating mac using all fields");
			tmpIsoMsg = createTmpIsoMsgForMaccing(isoMsg, null);
		}
		
		// pack tmpIsoMsg
		byte[] data2 = tmpIsoMsg.pack();
		logger.debug("data2 = " + ByteUtils.bytesToHex(data2));
		
		// remove MTI, bitmap, and mac from packed tmpIsoMsg
		byte[] macData2 = new byte[data2.length-4-bitmapLen-16];
		System.arraycopy(data2, 4+bitmapLen, macData2, 0, data2.length-4-bitmapLen-16);
		logger.debug("macData2 = " + ByteUtils.bytesToHex(macData2));
		
		// create mac data
		byte[] macData = ByteUtils.concat(header, macData1, macData2);
		logger.debug("mac data = " + ByteUtils.bytesToHex(macData));
		
//		logger.debug("verify: pack data = " + ByteUtils.bytesToHex(data));
//		// remove last 16 bytes bec last 16 bytes is mac, and is 16 bytes of '0' (is pack as ascii)
//		byte[] dataWithoutMac = ByteUtils.subArray(data,  0, data.length-16);
//		// append header in-front
//		byte[] macData = ByteUtils.concat(header, dataWithoutMac);
		
		logger.debug("verify: mac data = " + ByteUtils.bytesToHex(macData));
		NofHsmService netsHsmService = SpringUtils.getBean(NofHsmService.class, 10, 1000);
		logger.debug("verify: generating mac, macKeyDate = " + getMacKeyDate());
		byte[] mac = netsHsmService.generateMac(getMacKey(), macData);
		logger.debug("verify: mac = " + ByteUtils.bytesToHex(mac));
		byte[] actualMac;
		if (isoMsg.hasField(128)) {
			actualMac = isoMsg.getBytes(128);	
		} else {
			// for 0430, mac is in field 64
			actualMac = isoMsg.getBytes(64);
		}
		if (ByteUtils.equals(mac, actualMac)) {
			logger.debug("verify mac ok");
			return true;
		} else {
			logger.error("verify mac fail, received " + ByteUtils.bytesToHex(actualMac) + ", expecting " + ByteUtils.bytesToHex(mac));
			return false;
		}
	}
	
	/**
	 * create tmp isoMsg
	 * containing only those fields in "fields" plus field 128
	 * 
	 * note:
	 * - all the fields in "fields" are string fields
	 * - "fields" must not contain 0, 1, or 128
	 * 
	 * @param isoMsg
	 * @param fields
	 * @return
	 * @throws Exception
	 */
	private ISOMsg createTmpIsoMsgForMaccing(ISOMsg isoMsg, int[] fields) throws Exception {
		ISOMsg isoMsg2;
		if (fields != null) {
			isoMsg2 = new ISOMsg();
			isoMsg2.setPackager(isoMsg.getPackager());
			isoMsg2.setMTI(isoMsg.getMTI());
			for (int field: fields) {
				if (isoMsg.hasField(field)) {
					isoMsg2.set(field, isoMsg.getString(field));
				}
			}
		} else {
			isoMsg2 = (ISOMsg)isoMsg.clone();
		}
		
		// set field 128
		isoMsg2.set(128, new byte[8]);
		return isoMsg2;
	}
	
}
