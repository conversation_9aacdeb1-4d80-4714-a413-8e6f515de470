package com.abl.nof.nets;

import org.apache.log4j.Logger;
import org.jpos.iso.ISOException;
import org.jpos.iso.ISOMsg;
import org.jpos.q2.iso.MUXPool;
import org.jpos.util.NameRegistrar;
import org.jpos.util.NameRegistrar.NotFoundException;

public class NetsTerminal {

	private static final Logger logger=Logger.getLogger(NetsTerminal.class);
	
	private String mid;
	private String tid;
	private String merchName;
	private long timeout;
	private NetsStanGenerator netsStanGenerator;
	private int stanCounter=0;
	private boolean active=false;
	private boolean verifyMac;
	
	private MUXPool muxPool;
	
	public NetsTerminal(String mid, String tid, String merchName, long timeout, boolean verifyMac) {
		this.mid = mid;
		this.tid = tid;
		this.merchName = merchName;
		this.timeout = timeout;
		this.verifyMac = verifyMac;
	}
	
	public String nextStan() {
		if (netsStanGenerator != null) {
			return netsStanGenerator.nextStan(mid, tid);
		} else {
			stanCounter=(stanCounter+1)%1000000;
			return String.format("%06d", stanCounter);
		}
	}
	
	private MUXPool getMUXPool() {
		if (muxPool == null) {
			try {
				muxPool = (MUXPool) NameRegistrar.get("mux.nets-mux");
			} catch (NotFoundException e) {
				logger.error("unable to get muxPool mux.nets-mux");
			}
		}
		return muxPool;
	}
	
	public ISOMsg sendIsoMsg(ISOMsg isoMsg) throws ISOException, NetsNoConnectionException {
		if (!isActive()) {
			throw new NetsNoConnectionException("terminal is not active, mid=" + mid + ".tid=" + tid);
		}
		MUXPool muxPool = getMUXPool();
		if (muxPool == null) {
			throw new NetsNoConnectionException("unable to get muxpool for mux.nets-mux");
		}
		if (!muxPool.isConnected()) {
			throw new NetsNoConnectionException("unable to send msg: not connected");
		}
		
		ISOMsg isoResp = muxPool.request(isoMsg, timeout);
		if (isoResp != null) {
			if (verifyMac) {
				Nets8583Channel channel = (Nets8583Channel)isoResp.getSource();
				try {
					logger.debug("verifying mac");
					boolean result = channel.verifyMac(isoResp);
					if (!result) {
						logger.error("verify mac fail");
					}
				} catch (Exception e) {
					logger.error("unable to verify mac", e);
				}
			} else {
				logger.debug("not verifying mac");
			}
		}
		return isoResp;
	}

	public NetsStanGenerator getNetsStanGenerator() {
		return netsStanGenerator;
	}

	public void setNetsStanGenerator(NetsStanGenerator netsStanGenerator) {
		this.netsStanGenerator = netsStanGenerator;
	}

	public String getMid() {
		return mid;
	}

	public void setMid(String mid) {
		this.mid = mid;
	}

	public String getTid() {
		return tid;
	}

	public void setTid(String tid) {
		this.tid = tid;
	}

	public String getMerchName() {
		return merchName;
	}

	public void setMerchName(String merchName) {
		this.merchName = merchName;
	}

	public synchronized boolean isActive() {
		return active;
	}

	public synchronized void setActive(boolean active) {
		this.active = active;
	}
	
}
