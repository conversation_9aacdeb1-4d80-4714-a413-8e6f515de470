package com.abl.nof.nets;

import java.io.IOException;

import org.apache.log4j.Logger;
import org.jpos.iso.ISOChannel;
import org.jpos.iso.ISOException;
import org.jpos.iso.ISOFilter;
import org.jpos.iso.ISOMsg;
import org.jpos.util.LogEvent;

import com.abl.nof.hsm.NofHsmInvalidKeyException;
import com.abl.utils.ByteUtils;

/**
 * filter for client side
 * 
 * for intercepting:
 * - various outgoing msg to generate mac 
 *
 */
public class NetsFilter implements ISOFilter {

	private static final Logger logger=Logger.getLogger(NetsFilter.class);
	
	@Override
	public ISOMsg filter(ISOChannel isoChannel, ISOMsg isoMsg, LogEvent logEvent) throws VetoException {
		logger.debug("filtering, channel=" + isoChannel);
		String mti;
		try {
			mti = isoMsg.getMTI();
		} catch (ISOException e) {
			return isoMsg;
		}
		
		// nmi only for 0800/0810 msg
		String nmi = null;
		if (isoMsg.hasField(70)) {
			nmi = isoMsg.getString(70);
		}
		
		logger.debug("mti=" + mti + ", dir=" + (isoMsg.isOutgoing()?"outgoing":"incoming"));
		if (isoMsg.isOutgoing()) {
			// for outgoing, add mac if necessary
			
			boolean addMac = false;
			if (("0200".equals(mti))||("0420".equals(mti))||("0421".equals(mti))) {
				// non-0800 outgoing requests add mac
				addMac = true;
			} else if (("0210".equals(mti))||("0430".equals(mti))||("0431".equals(mti))
					||(("0810".equals(mti))&&(NetsApiConstant.NETS_NMI_NEW_KEY.equals(nmi)))) {
				// outgoing 0810 only add mac for new key
				addMac = true;
			}
			
			if (addMac) {
				try {
					logger.debug("generating mac");
					byte[] mac = ((Nets8583Channel)isoChannel).generateMac(isoMsg);
					isoMsg.set(128, mac);
					logger.debug("mac is " + ByteUtils.bytesToHex(mac));
				} catch (ISOException e) {
					logger.error("unable to gen mac", e);
					throw new VetoException("unable to gen mac");
				} catch (NofHsmInvalidKeyException e) {
					logger.error("unable to gen mac", e);
					try {
						// the macKey is invalid, so just disconnect the channel
						logger.debug("disconnect channel " + isoChannel);
						isoChannel.disconnect();
						logger.debug("disconnect channel " + isoChannel + " done");
					} catch (IOException e1) {
						logger.error("unable to disconnect channel", e1);
					}
					throw new VetoException("unable to gen mac");
				} catch (Exception e) {
					logger.error("unable to gen mac", e);
					throw new VetoException("unable to gen mac");
				}
			}
		}
		
		return isoMsg;
	}
	
}
