package com.abl.nof.nets;

import java.io.IOException;

import org.apache.log4j.Logger;
import org.jpos.core.Configurable;
import org.jpos.core.Configuration;
import org.jpos.core.ConfigurationException;
import org.jpos.iso.ISOChannel;
import org.jpos.iso.ISOException;
import org.jpos.iso.ISOMsg;
import org.jpos.iso.ISORequestListener;
import org.jpos.iso.ISOSource;
import org.jpos.util.Destroyable;
import org.jpos.util.NameRegistrar;
import org.jpos.util.NameRegistrar.NotFoundException;

/**
 * - processes any request msg from nets (e.g. new key, echo, logon),
 * - processes 0810 responses from nets
 * - if required, will start NetsLogonThread to send logon and send change key
 * - if required, will start NetsEchoThread to send echo
 * 
 * this is for client side mux to use
 */
public class NetsRequestListener implements ISORequestListener, Configurable, Destroyable {

	private static final Logger logger=Logger.getLogger(NetsRequestListener.class);
    
	private String channelName;
	private boolean sendLogon = true;	// true means will send logon at startup
	private boolean sendChangeKey = false; // true means will send change key
	private long echoIntervalMs = 0L;	// interval to send echo (0 will not send)
	private long intervalMs=10000L;
	private long timeoutMs=3000L;
	
	private Nets8583Channel channel;
	
	// unmatchResponseStore is used to do simple matching for 0810 responses from nets
	// (for requests that are sent from netsLogonThread and netsEchoThread).
	private UnmatchResponseStore unmatchResponseStore = new UnmatchResponseStore();
	
	private NetsLogonThread netsLogonThread;
	private NetsEchoThread netsEchoThread;
	
	@Override
	public void setConfiguration(Configuration configuration) throws ConfigurationException {
		this.channelName=configuration.get("channel");
		this.sendLogon = configuration.getBoolean("sendLogon", true);
		this.sendChangeKey = configuration.getBoolean("sendChangeKey", true);
		this.echoIntervalMs=configuration.getLong("echoIntervalMs", 0L);
		this.intervalMs=configuration.getLong("intervalMs", 10000L);
		this.timeoutMs=configuration.getLong("timeoutMs", 3000L);
		
		if (sendLogon||sendChangeKey) {
			netsLogonThread = new NetsLogonThread(this, sendLogon, sendChangeKey, intervalMs, timeoutMs);
			netsLogonThread.start();
		}
		
		if (echoIntervalMs > 0) {
			logger.debug("echoIntervalMs is " + echoIntervalMs);
			netsEchoThread = new NetsEchoThread(this, echoIntervalMs);
			netsEchoThread.start();
		}
	}
	
	@Override
	public void destroy() {
		if (netsLogonThread != null) {
			netsLogonThread.stop();
		}
		if (netsEchoThread != null) {
			netsEchoThread.stop();
		}
	}
	
	public Nets8583Channel getChannel() throws ISOException {
		if (channel == null) {
			try {
				channel = (Nets8583Channel)NameRegistrar.get ("channel."+channelName);
			} catch (NotFoundException e) {
				throw new ISOException("channel not found "+channelName);
			}
		}
		return channel;
	}
	
	/**
	 * processes 0800 requests, and throw 0810 responses into unmatchedResponses
	 * 
	 * responses from nets, for requests that are sent via MUX should not reach here.
	 * 
	 * return true if msg is handled here
	 */
    @Override
    public boolean process(ISOSource isoSource, ISOMsg isoMsg) {
        try {
            logger.debug("received message from NET Host");
            
            if ( isoMsg.getMTI().equalsIgnoreCase("0800") ) {
                if ( isoMsg.hasField(70) ) {
                    if ( isoMsg.getString(70).equalsIgnoreCase( NetsApiConstant.NETS_NMI_ECHO ) ) {
                        logger.debug("Received echo from NETS");
                        ISOMsg respIso = (ISOMsg)isoMsg.clone();
                        respIso.setMTI("0810");
                        respIso.set(39, "00");
                        logger.debug("Responding to NETS");
                        respIso.setDirection(ISOMsg.OUTGOING);
                        isoSource.send(respIso);
                        return true;
                    } else if ( isoMsg.getString(70).equalsIgnoreCase( NetsApiConstant.NETS_NMI_NEW_KEY ) ) {
                    	logger.debug("new key req");
                    	if (isoMsg.hasField(53)) {
                        	String f53 = isoMsg.getString(53);
                        	if (f53.startsWith("01")) {
                        		Nets8583Channel channel = getChannel();
                                logger.debug("channel = " + channel);
                                channel.processNewKey(isoMsg);	
                        	} else {
                        		logger.warn("not mac key, field 53=" + f53);
                        		if (channel.hasMacKey()) {
	                        		// just send a response back
                        			logger.debug("sending new key resp");
	                        		ISOMsg respIso = (ISOMsg)isoMsg.clone();
	                        		respIso.setMTI("0810");
	                        		respIso.set(39, "00");
	                	    		respIso.set(123, "CSM(MCL/RSM RCV/NETS ORG/ACQM )");
	                	    		respIso.setDirection(ISOMsg.OUTGOING);
	                	    		channel.send(respIso);
                        		} else {
                        			// without mac key, unable to gen mac for response
                        			// so, just don't send response
                        			logger.warn("no mac key, not sending new key resp");
                        		}
                        	}
                    	} else {
                    		logger.error("new key req missing field 53");
                    	}
                        return true;
                    } else if ( isoMsg.getString(70).equalsIgnoreCase( NetsApiConstant.NETS_NMI_LOGON ) ) {
                    	logger.debug("logon req");
                    	Nets8583Channel channel = getChannel();
                    	processLogon(channel, isoMsg);
                    	return true;
                    } else {
                        logger.warn("Unknown NMI from NETS 0800 msg : " + isoMsg.getString(70));
                    }
                } else {
                	logger.error("Missing field 70 in 0800 request message");
                }
            } else if ( isoMsg.getMTI().equalsIgnoreCase("0810") ) {
                if (isoMsg.hasField(70)) {
                    if (isoMsg.getString(70).equalsIgnoreCase(NetsApiConstant.NETS_NMI_ECHO)) {
                        logger.debug("Received echo response from NETS");
                        unmatchResponseStore.addResponse(UnmatchResponseStore.KEY_ECHO, isoMsg);
                        return true;
                    } else if (isoMsg.getString(70).equalsIgnoreCase(NetsApiConstant.NETS_NMI_LOGON)) {
                    	logger.debug("Received logon response from NETS");
                    	unmatchResponseStore.addResponse(UnmatchResponseStore.KEY_LOGON, isoMsg);
                    	return true;
                    } else if (isoMsg.getString(70).equalsIgnoreCase(NetsApiConstant.NETS_NMI_CHANGE_KEY)) {
                    	logger.debug("Received change key response from NETS");
                    	unmatchResponseStore.addResponse(UnmatchResponseStore.KEY_CHANGE_KEY, isoMsg);
                    	return true;
                    } else {
                        logger.warn("Unknown NMI from NETS 0810 msg : " + isoMsg.getString(70));
                    }
                }
            } else {
                logger.error("Unknown message from nets");
            }
        } catch (ISOException e) {
            logger.error("error processing message from the host ", e);
        } catch( IOException e) {
            logger.error("error processing message from host", e);
        }
        
        logger.debug("return false");
        return false;
    }

    private void processLogon(ISOChannel isoChannel, ISOMsg isoMsg) {
		try {
			ISOMsg respIso = (ISOMsg)isoMsg.clone();
	        respIso.setMTI("0810");
	        respIso.set(39, "00");
	        logger.debug("sending logon resp to NETS");
	        respIso.setDirection(ISOMsg.OUTGOING);
	        isoChannel.send(respIso);
		} catch (Exception e) {
			logger.error("exception caught processing logon request", e);
		}
	}
    
	public String getChannelName() {
		return channelName;
	}
    
	public void clearUnmatchResponse(String key) {
		unmatchResponseStore.clearResponse(key);
	}
	
	public ISOMsg getUnmatchResponse(String key, long timeoutMs) {
		return unmatchResponseStore.getResponse(key, timeoutMs);
	}
}
