package com.abl.nof.nets;

public class Nets8583Header {
	public static final byte[] HEADER="ISO025000077".getBytes();
	
    private byte[] header;

    public Nets8583Header() {
        setHeader(HEADER);
    }

    public byte[] getHeader() {
        return header;
    }

    public void setHeader(byte[] header) {
        this.header = header;
    }

    public String getStatusCode() {
        return new String(header).substring(7,10);
    }

    public boolean isStatusApproved() {
        return getStatusCode().equalsIgnoreCase("000");
    }
}
