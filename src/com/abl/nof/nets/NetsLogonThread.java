package com.abl.nof.nets;

import java.util.Date;

import org.apache.log4j.Logger;
import org.jpos.iso.ISOException;
import org.jpos.iso.ISOMsg;

import com.abl.utils.ThreadSafeSimpleDateFormat;

public class NetsLogonThread implements Runnable {

	private static Logger logger=Logger.getLogger(NetsLogonThread.class);
	
	private static final ThreadSafeSimpleDateFormat sdf = ThreadSafeSimpleDateFormat.getInstance("MMddHHmmss");
	
	private NetsRequestListener netsRequestListener;
	private boolean sendLogon = true;
	private boolean sendChangeKey = false;
	private long intervalMs=10000L;
	private long timeoutMs=3000L;
	
	private boolean pendingLogon=true;
	private boolean running = true;

	public NetsLogonThread(NetsRequestListener netsRequestListener, boolean sendLogon, boolean sendChangeKey,
			long intervalMs, long timeoutMs) {
		this.netsRequestListener = netsRequestListener;
		this.sendLogon = sendLogon;
		this.sendChangeKey = sendChangeKey;
		this.intervalMs = intervalMs;
		this.timeoutMs = timeoutMs;
		
		if (!this.sendLogon) {
			pendingLogon = false;
		}
	}

	public void start() {
		logger.debug("starting NetsLogonThread");
		new Thread(this).start();
	}
	
	public void stop() {
		setRunning(false);
	}
	
	public synchronized boolean isRunning() {
		return running;
	}

	public synchronized void setRunning(boolean running) {
		this.running = running;
	}

	public void sleep() {
		try {
			Thread.sleep(intervalMs);
		} catch (InterruptedException e) {
			logger.error("exception caught", e);
		}
	}
	
	@Override
	public void run() {
		try {
			sleep();
			while (isRunning()) {
				
				Nets8583Channel channel = getChannel();
				
				if (pendingLogon) {
					if (channel == null) {
						logger.warn("unable to send logon: channel is null, " + netsRequestListener.getChannelName());
					} else if (!channel.isConnected()) {
						logger.warn("unable to send logon, not connected, " + netsRequestListener.getChannelName());
					} else {
						logger.info("sending logon");
						if (doLogon(channel)) {
							logger.info("doLogon returns true");
							pendingLogon = false;
						} else {
							logger.error("doLogon returns false");
						}
					}
				} else if (sendChangeKey) {
					if (channel.hasMacKey()) {
						logger.info("not sending change key, already has mac key, " + netsRequestListener.getChannelName());
						break;
					} else if (!channel.isConnected()) {
						logger.warn("unable to send change key, not connected, " + netsRequestListener.getChannelName());
					} else {
						logger.info("sending change key");
						if (doChangeKey(channel)) {
							logger.info("doChangeKey returns true");
						} else {
							logger.error("doChangeKey returns false");
						}	
					}
				} else {
					logger.info("not sending change key");
					break;
				}
				
				if (!isRunning()) {
					logger.debug("not running");
					break;
				}
				
				sleep();
			}
		} catch (Exception e) {
			logger.error("exception caught", e);
		}
	}

	private Nets8583Channel getChannel() {
		try {
			return netsRequestListener.getChannel();
		} catch (ISOException e) {
			logger.debug("channel not found "+netsRequestListener.getChannelName(), e);
			return null;
		}
	}
	
	private boolean doLogon(Nets8583Channel channel) {
		if (!sendLogon(channel)) {
			logger.error("sendLogon return false");
			return false;
		}
		return checkLogonResponse(channel);
	}
	
	private boolean checkLogonResponse(Nets8583Channel channel) {
		ISOMsg resp = netsRequestListener.getUnmatchResponse(UnmatchResponseStore.KEY_LOGON, timeoutMs);
		if (resp == null) {
			logger.debug("no logon resp received");
			return false;
		}
		if (!resp.hasField(39)) {
        	logger.error("logon resp has no f39");
        	return false;
        }
        if (!"00".equals(resp.getString(39))) {
        	logger.error("logon rc " + resp.getString(39));
        	return false;
        } else {
        	logger.info("logon resp 00");
        }
        return true;
	}
	
	private boolean sendLogon(Nets8583Channel channel) {
		netsRequestListener.clearUnmatchResponse(UnmatchResponseStore.KEY_LOGON);
    	ISOMsg isoRequest = new ISOMsg();
        try {
            isoRequest.setMTI("0800");
            Date currentTime = new Date();
            isoRequest.set(7, sdf.format(currentTime));
            isoRequest.set(11, channel.getStan());
            // susanto (20190703):
            // - encryption type should be 1
            // - key processing type should be S
            isoRequest.set(48, "0101000112S107022");
            isoRequest.set(70, NetsApiConstant.NETS_NMI_LOGON );
            logger.debug("sending logon request");
            
            channel.send(isoRequest);
            return true;
        } catch(Exception e) {
            logger.debug("error sending logon", e);
            return false;
        }
    }
	
	private boolean doChangeKey(Nets8583Channel channel) {
		if (!sendChangeKey(channel)) {
			logger.error("sendChangeKey return false");
			return false;
		}
		return checkChangeKeyResponse(channel);
	}
	
	private boolean checkChangeKeyResponse(Nets8583Channel channel) {
		ISOMsg resp = netsRequestListener.getUnmatchResponse(UnmatchResponseStore.KEY_CHANGE_KEY, timeoutMs);
		if (resp == null) {
			logger.debug("no change key resp received");
			return false;
		}
		if (!resp.hasField(39)) {
        	logger.error("change key resp has no f39");
        	return false;
        }
        if (!"00".equals(resp.getString(39))) {
        	logger.error("change key rc " + resp.getString(39));
        	return false;
        } else {
        	logger.info("change key resp 00");
        }
        return true;
	}
	
	private boolean sendChangeKey(Nets8583Channel channel) {
		netsRequestListener.clearUnmatchResponse(UnmatchResponseStore.KEY_CHANGE_KEY);
    	ISOMsg isoRequest = new ISOMsg();
        try {
        	isoRequest.setMTI("0800");
            Date currentTime = new Date();
            isoRequest.set(7, sdf.format(currentTime));
            isoRequest.set(11, channel.getStan());
            isoRequest.set(53, "0103000000000000");
            isoRequest.set(70, NetsApiConstant.NETS_NMI_CHANGE_KEY );
            isoRequest.set(123, "CSM(MCL/RSI RCV/NETS ORG/ACQM SVR/)");
            logger.debug("sending nets change key request");
            channel.send(isoRequest);
            return true;
        } catch(Exception e) {
            logger.debug("error sending change key", e);
            return false;
        }
    }
}