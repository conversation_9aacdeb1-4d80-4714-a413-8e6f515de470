package com.abl.nof.nets;

import java.util.Date;

import org.apache.log4j.Logger;
import org.jpos.iso.ISOException;
import org.jpos.iso.ISOMsg;

import com.abl.utils.ThreadSafeSimpleDateFormat;

public class NetsEchoThread implements Runnable {

	private static Logger logger=Logger.getLogger(NetsEchoThread.class);
	
	private static final long timeoutMs=1000L;
	private static final ThreadSafeSimpleDateFormat sdf = ThreadSafeSimpleDateFormat.getInstance("MMddHHmmss");
	
	private long intervalMs;
	private NetsRequestListener netsRequestListener;
	
	private boolean running = true;
	
	public NetsEchoThread(NetsRequestListener netsRequestListener, long intervalMs) {
		this.netsRequestListener = netsRequestListener;
		this.intervalMs = intervalMs;
	}

	public void start() {
		logger.debug("starting NetsEchoThread");
		new Thread(this).start();
	}
	
	public void stop() {
		setRunning(false);
	}
	
	public synchronized boolean isRunning() {
		return running;
	}

	public synchronized void setRunning(boolean running) {
		this.running = running;
	}
	
	public void sleep() {
		try {
			Thread.sleep(intervalMs);
		} catch (InterruptedException e) {
			logger.error("exception caught", e);
		}
	}
	
	@Override
	public void run() {
		try {
			sleep();
			while (isRunning()) {
				Nets8583Channel channel = getChannel();
				if (channel == null) {
					logger.warn("unable to send echo: channel is null, " + netsRequestListener.getChannelName());
				} else if (!channel.isConnected()) {
					logger.warn("unable to send echo: not connected, " + netsRequestListener.getChannelName());
				} else {
					logger.debug("sending echo");
					if (doEcho(channel)) {
						logger.debug("doEcho returns true");
					} else {
						logger.debug("doEcho returns false");
					}
				}
				
				if (!isRunning()) {
					logger.debug("not running");
					break;
				}
				
				sleep();
			}
		} catch (Exception e) {
			logger.error("exception caught", e);
		}
	}

	private Nets8583Channel getChannel() {
		try {
			return netsRequestListener.getChannel();
		} catch (ISOException e) {
			logger.debug("channel not found "+netsRequestListener.getChannelName(), e);
			return null;
		}
	}
	
	private boolean doEcho(Nets8583Channel channel) {
		if (!sendEcho(channel)) {
			logger.error("sendEcho return false");
			return false;
		}
		return checkEchoResponse(channel);
	}
	
	private boolean checkEchoResponse(Nets8583Channel channel) {
		ISOMsg resp = netsRequestListener.getUnmatchResponse(UnmatchResponseStore.KEY_ECHO, timeoutMs);
		if (resp == null) {
			logger.debug("no echo resp received");
			return false;
		}
		if (!resp.hasField(39)) {
        	logger.error("echo resp has no f39");
        	return false;
        }
        if (!"00".equals(resp.getString(39))) {
        	logger.error("echo rc " + resp.getString(39));
        	return false;
        } else {
        	logger.info("echo resp 00");
        }
        return true;
	}
	
	private boolean sendEcho(Nets8583Channel channel) {
		netsRequestListener.clearUnmatchResponse(UnmatchResponseStore.KEY_ECHO);
    	ISOMsg isoRequest = new ISOMsg();
        try {
            isoRequest.setMTI("0800");
            Date currentTime = new Date();
            isoRequest.set(7, sdf.format(currentTime));
            isoRequest.set(11, channel.getStan());
            isoRequest.set(70, NetsApiConstant.NETS_NMI_ECHO);
            logger.debug("sending nets echo");
            channel.send(isoRequest);
            return true;
        } catch(Exception e) {
            logger.debug("error sending echo", e);
            return false;
        }
    }
}