package com.abl.nof.nets.api;

import com.abl.utils.StringUtils;

public class F126Table52 extends F126Tlv {
	private String fiid;		// 4 chars: issuer bank fiid
	private String bankName;	// 22 chars
	private String merchantTokenStatus;		// 1 char: , 0=pre-registered, 1=blocked, 2=deregistered, 3=registered, 4=suspended
	private String issuerAuthorizationFlag;	// 1 char: <space>, Y, N
	private String rfu = "                                          ";	// 23 spaces

	public F126Table52() {
		super(52);
	}

	public F126Table52(String fiid, String bankName, String merchantTokenStatus,
			String issuerAuthorizationFlag) {
		super(52);
		this.fiid = fiid;
		this.bankName = bankName;
		this.merchantTokenStatus = merchantTokenStatus;
		this.issuerAuthorizationFlag = issuerAuthorizationFlag;
	}

	public static F126Table52 parse(String data) throws Exception {
		if (data.length() < 70) {
			throw new Exception("unable to parse f126 table52, len=" + data.length() + " < 70");
		}
		F126Table52 table52 = new F126Table52();
		table52.setData(data);
		table52.setLen(data.length());
		table52.setFiid(data.substring(0, 4));
		table52.setBankName(data.substring(4, 26));
		table52.setMerchantTokenStatus(data.substring(26, 27));
		table52.setIssuerAuthorizationFlag(data.substring(27, 28));
		return table52;
	}
	
	@Override
	public String toString() {
		if (getData() == null) {
			StringBuilder data = new StringBuilder();
			data.append(fiid);
			data.append(StringUtils.padRightToLen(bankName, ' ', 22));
			data.append(merchantTokenStatus);
			data.append(issuerAuthorizationFlag);
			data.append(rfu);
			setData(data.toString());
			setLen(data.length());
		}
		return super.toString();
	}

	public String getFiid() {
		return fiid;
	}

	public void setFiid(String fiid) {
		this.fiid = fiid;
	}

	public String getBankName() {
		return bankName;
	}

	public void setBankName(String bankName) {
		this.bankName = bankName;
	}

	public String getMerchantTokenStatus() {
		return merchantTokenStatus;
	}

	public void setMerchantTokenStatus(String merchantTokenStatus) {
		this.merchantTokenStatus = merchantTokenStatus;
	}

	public String getIssuerAuthorizationFlag() {
		return issuerAuthorizationFlag;
	}

	public void setIssuerAuthorizationFlag(String issuerAuthorizationFlag) {
		this.issuerAuthorizationFlag = issuerAuthorizationFlag;
	}
	
}
