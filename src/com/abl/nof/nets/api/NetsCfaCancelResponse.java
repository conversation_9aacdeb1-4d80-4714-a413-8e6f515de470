package com.abl.nof.nets.api;

import java.util.List;

import org.apache.log4j.Logger;
import org.jpos.iso.ISOMsg;

import com.abl.nof.nets.NetsUtil;

public class NetsCfaCancelResponse {
	private static final Logger logger=Logger.getLogger(NetsCfaCancelResponse.class);
	
	private String mti;
	private String stan;
	private String time;
	private String date;
	private String authCode;
	private String respCode;
	private String tid;
	private String mid;
	
	// from field 126 table 52
	private String fiid;
	private String bankName;
	private String merchantTokenStatus;
	private String issuerAuthorizationFlag;
	
	public static NetsCfaCancelResponse parseIsoMsg(ISOMsg isoMsg) throws Exception {
		NetsCfaCancelResponse response = new NetsCfaCancelResponse();
		response.setMti(isoMsg.getMTI());
		if (isoMsg.hasField(11)) {
			response.setStan(isoMsg.getString(11));
		}
		if (isoMsg.hasField(12)) {
			response.setTime(isoMsg.getString(12));
		}
		if (isoMsg.hasField(13)) {
			response.setDate(isoMsg.getString(13));
		}
		if (isoMsg.hasField(38)) {
			response.setAuthCode(isoMsg.getString(38));
		}
		if (isoMsg.hasField(39)) {
			response.setRespCode(isoMsg.getString(39));
		}
		if (isoMsg.hasField(41)) {
			response.setTid(isoMsg.getString(41));
		}
		if (isoMsg.hasField(42)) {
			response.setMid(isoMsg.getString(42));
		}
		if (isoMsg.hasField(126)) {
			List<F126Tlv> f126TlvList = NetsUtil.parseF126(isoMsg.getString(126));
			if ((f126TlvList != null)&&(!f126TlvList.isEmpty())) {
				F126Table52 f126Table52 = null;
				for (F126Tlv tlv: f126TlvList) {
					if (tlv.getTableId() == 52) {
						f126Table52 = (F126Table52)tlv;
						response.setFiid(f126Table52.getFiid());
						response.setBankName(f126Table52.getBankName());
						response.setMerchantTokenStatus(f126Table52.getMerchantTokenStatus());
						response.setIssuerAuthorizationFlag(f126Table52.getIssuerAuthorizationFlag());
						break;
					}
				}
				if (f126Table52 == null) {
					logger.debug("table 52 not in field 126");
				}
			} else {
				logger.debug("no tables in field 126");
			}
		} else {
			logger.debug("field 126 not present");
		}
		return response;
	}

	public String getMti() {
		return mti;
	}

	public void setMti(String mti) {
		this.mti = mti;
	}

	public String getStan() {
		return stan;
	}

	public void setStan(String stan) {
		this.stan = stan;
	}

	public String getTime() {
		return time;
	}

	public void setTime(String time) {
		this.time = time;
	}

	public String getDate() {
		return date;
	}

	public void setDate(String date) {
		this.date = date;
	}

	public String getAuthCode() {
		return authCode;
	}

	public void setAuthCode(String authCode) {
		this.authCode = authCode;
	}

	public String getRespCode() {
		return respCode;
	}

	public void setRespCode(String respCode) {
		this.respCode = respCode;
	}

	public String getTid() {
		return tid;
	}

	public void setTid(String tid) {
		this.tid = tid;
	}

	public String getMid() {
		return mid;
	}

	public void setMid(String mid) {
		this.mid = mid;
	}

	public String getFiid() {
		return fiid;
	}

	public void setFiid(String fiid) {
		this.fiid = fiid;
	}

	public String getBankName() {
		return bankName;
	}

	public void setBankName(String bankName) {
		this.bankName = bankName;
	}

	public String getMerchantTokenStatus() {
		return merchantTokenStatus;
	}

	public void setMerchantTokenStatus(String merchantTokenStatus) {
		this.merchantTokenStatus = merchantTokenStatus;
	}

	public String getIssuerAuthorizationFlag() {
		return issuerAuthorizationFlag;
	}

	public void setIssuerAuthorizationFlag(String issuerAuthorizationFlag) {
		this.issuerAuthorizationFlag = issuerAuthorizationFlag;
	}

}
