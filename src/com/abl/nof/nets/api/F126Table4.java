package com.abl.nof.nets.api;

import com.abl.utils.StringUtils;

public class F126Table4 extends F126Tlv {
	private String merchName;	// 20 chars
	private String taxiNumber;	// 12 chars
	private String driverId;	// 9 chars
	private String jobNumber;	// 10 chars
	private String gst;			// 6 chars
	private String admin;		// 6 chars
	private String companyCode;	// 6 chars
	private String rfu1 = "                                                   ";	// 51 spaces
	private String transRefNumber="                ";	// 16 chars
	private String rfu2 = "        ";	// 8 spaces
	private String feeInfo="000000";		// 6 chars
	
	public F126Table4() {
		super(4);
	}

	public F126Table4(String merchName, String taxiNumber, String driverId, String jobNumber, String gst,
					  String admin, String companyCode) {
		super(4);
		this.merchName = merchName;
		this.taxiNumber = taxiNumber;
		this.driverId = driverId;
		this.jobNumber = jobNumber;
		this.gst = gst;
		this.admin = admin;
		this.companyCode = companyCode;
		//set fee info
		Long fee = Long.valueOf(admin)+Long.valueOf(gst);
		String feeStr = String.format("%04d",fee);
		this.feeInfo = "02"+feeStr;

	}
	
	public F126Table4(String merchName, String taxiNumber, String driverId, String jobNumber, String gst,
			String admin, String companyCode, String transRefNumber, String feeInfo) {
		super(4);
		this.merchName = merchName;
		this.taxiNumber = taxiNumber;
		this.driverId = driverId;
		this.jobNumber = jobNumber;
		this.gst = gst;
		this.admin = admin;
		this.companyCode = companyCode;
		this.transRefNumber = transRefNumber;
		this.feeInfo = feeInfo;
	}
	
	public static F126Table4 parse(String data) throws Exception {
		if (data.length() < 150) {
			throw new Exception("unable to parse f126 table4, len=" + data.length() + " < 150");
		}
		F126Table4 table4 = new F126Table4();
		table4.setData(data);
		table4.setLen(data.length());
		table4.setMerchName(data.substring(0, 20));
		table4.setTaxiNumber(data.substring(20, 32));
		table4.setDriverId(data.substring(32, 41));
		table4.setJobNumber(data.substring(41, 51));
		table4.setGst(data.substring(51, 57));
		table4.setAdmin(data.substring(57, 63));
		table4.setCompanyCode(data.substring(63, 69));
		table4.setTransRefNumber(data.substring(120, 136));
		table4.setFeeInfo(data.substring(144, 150));
		return table4;
	}
	
	@Override
	public String toString() {
		if (getData() == null) {
			StringBuilder data = new StringBuilder();
			data.append(StringUtils.padRightToLen(merchName, ' ', 20));
			data.append(StringUtils.padRightToLen(taxiNumber, ' ', 12));
			data.append(StringUtils.padRightToLen(driverId, ' ', 9));
			data.append(StringUtils.padRightToLen(jobNumber, ' ', 10));
			data.append(StringUtils.padRightToLen(gst, ' ', 6));
			data.append(StringUtils.padRightToLen(admin, ' ', 6));
			data.append(StringUtils.padRightToLen(companyCode, ' ', 6));
			data.append(rfu1);
			data.append(StringUtils.padRightToLen(transRefNumber, ' ', 16));
			data.append(rfu2);
			data.append(StringUtils.padRightToLen(feeInfo, '0', 6));
			setData(data.toString());
			setLen(data.length());
		}
		return super.toString();
	}

	public String getMerchName() {
		return merchName;
	}

	public void setMerchName(String merchName) {
		this.merchName = merchName;
	}

	public String getTaxiNumber() {
		return taxiNumber;
	}

	public void setTaxiNumber(String taxiNumber) {
		this.taxiNumber = taxiNumber;
	}

	public String getDriverId() {
		return driverId;
	}

	public void setDriverId(String driverId) {
		this.driverId = driverId;
	}

	public String getJobNumber() {
		return jobNumber;
	}

	public void setJobNumber(String jobNumber) {
		this.jobNumber = jobNumber;
	}

	public String getGst() {
		return gst;
	}

	public void setGst(String gst) {
		this.gst = gst;
	}

	public String getAdmin() {
		return admin;
	}

	public void setAdmin(String admin) {
		this.admin = admin;
	}

	public String getCompanyCode() {
		return companyCode;
	}

	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}

	public String getTransRefNumber() {
		return transRefNumber;
	}

	public void setTransRefNumber(String transRefNumber) {
		this.transRefNumber = transRefNumber;
	}

	public String getFeeInfo() {
		return feeInfo;
	}

	public void setFeeInfo(String feeInfo) {
		this.feeInfo = feeInfo;
	}
	
}
