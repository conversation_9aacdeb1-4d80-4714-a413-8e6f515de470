package com.abl.nof.nets.api;

/**
 * represents a f126 TLV
 *
 */
public class F126Tlv {
	private int tableId;
	private int len;
	private String data;
	
	public F126Tlv(int tableId) {
		this.tableId = tableId;
	}
	
	public F126Tlv(int tableId, String data) {
		this.tableId = tableId;
		if (data == null) {
			this.data = "";
			this.len = 0;
		} else {
			this.data = data;
			this.len = data.length();
		}
	}
	
	/**
	 * gen f126 format string 
	 */
	@Override
	public String toString() {
		return String.format("%02d", tableId) + String.format("%03d", len) + data;
	}

	public int getTableId() {
		return tableId;
	}

	public void setTableId(int tableId) {
		this.tableId = tableId;
	}

	public int getLen() {
		return len;
	}

	public void setLen(int len) {
		this.len = len;
	}

	public String getData() {
		return data;
	}

	public void setData(String data) {
		this.data = data;
	}
	
}
