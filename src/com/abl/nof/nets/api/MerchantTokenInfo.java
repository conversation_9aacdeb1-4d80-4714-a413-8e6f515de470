package com.abl.nof.nets.api;

import com.abl.utils.StringUtils;

/**
 * merchant token info (field 126 table 51)
 *
 */
public class MerchantTokenInfo {
	String merchToken;	// 48 chars, left justified with space
	String merchTokenExpiry;	// mmyy
	String merchTokenIndex;		// 2 chars numeric
	String merchTokenStatus;	// 1 char, 0=pre-registered, 1=blocked, 2=deregistered, 3=registered, 4=suspended
	String last4d;				// last 4 digits of fpan
	String bankFiid;			// 4 chars
	String issuerShortName;		// 10 chars
	String currTransDate;		// mmdd
	String currTransTime;		// hhmmss
	String currTransRrn;		// 12 chars
	String respCode;			// 2 chars
	String rfu = "                       "; // 23 spaces
	
	public static MerchantTokenInfo parse(String data) throws Exception {
		if (data.length() < 120) {
			throw new Exception("unable to parse merchant token info, length " + data.length() + " < 120");
		}
		MerchantTokenInfo info = new MerchantTokenInfo();
		info.setMerchToken(data.substring(0, 48).trim());
		info.setMerchTokenExpiry(data.substring(48, 52));
		info.setMerchTokenIndex(data.substring(52, 54));
		info.setMerchTokenStatus(data.substring(54, 55));
		info.setLast4d(data.substring(55, 59));
		info.setBankFiid(data.substring(59, 63));
		info.setIssuerShortName(data.substring(63, 73));
		info.setCurrTransDate(data.substring(73, 77));
		info.setCurrTransTime(data.substring(77, 83));
		info.setCurrTransRrn(data.substring(83, 95));
		info.setRespCode(data.substring(95, 97));
		
		if (MerchantTokenStatus.PRE_REGISTERED.equals(info.getMerchTokenStatus())) {
			// pre-registered
		} else if (MerchantTokenStatus.BLOCKED.equals(info.getMerchTokenStatus())) {
			// blocked
		} else if (MerchantTokenStatus.DE_REGISTERED.equals(info.getMerchTokenStatus())) {
			// deregistered
		} else if (MerchantTokenStatus.REGISTERED.equals(info.getMerchTokenStatus())) {
			// registered
		} else if (MerchantTokenStatus.SUSPENDED.equals(info.getMerchTokenStatus())) {
			// suspended
		} else {
			throw new Exception("unable to parse merchant token info, invalid merch token status " + info.getMerchTokenStatus());
		}
		return info;
	}
	
	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append(StringUtils.padRightToLen(merchToken, ' ', 48));
		builder.append(merchTokenExpiry);
		builder.append(merchTokenIndex);
		builder.append(merchTokenStatus);
		builder.append(last4d);
		builder.append(bankFiid);
		builder.append(currTransDate);
		builder.append(currTransTime);
		builder.append(StringUtils.padLeftToLen(currTransRrn, '0', 12));
		builder.append(respCode);
		builder.append(rfu);
		return builder.toString();
	}

	public String getMerchToken() {
		return merchToken;
	}

	public void setMerchToken(String merchToken) {
		this.merchToken = merchToken;
	}

	public String getMerchTokenExpiry() {
		return merchTokenExpiry;
	}

	public void setMerchTokenExpiry(String merchTokenExpiry) {
		this.merchTokenExpiry = merchTokenExpiry;
	}

	public String getMerchTokenIndex() {
		return merchTokenIndex;
	}

	public void setMerchTokenIndex(String merchTokenIndex) {
		this.merchTokenIndex = merchTokenIndex;
	}

	public String getMerchTokenStatus() {
		return merchTokenStatus;
	}

	public void setMerchTokenStatus(String merchTokenStatus) {
		this.merchTokenStatus = merchTokenStatus;
	}

	public String getLast4d() {
		return last4d;
	}

	public void setLast4d(String last4d) {
		this.last4d = last4d;
	}

	public String getBankFiid() {
		return bankFiid;
	}

	public void setBankFiid(String bankFiid) {
		this.bankFiid = bankFiid;
	}

	public String getIssuerShortName() {
		return issuerShortName;
	}

	public void setIssuerShortName(String issuerShortName) {
		this.issuerShortName = issuerShortName;
	}

	public String getCurrTransDate() {
		return currTransDate;
	}

	public void setCurrTransDate(String currTransDate) {
		this.currTransDate = currTransDate;
	}

	public String getCurrTransTime() {
		return currTransTime;
	}

	public void setCurrTransTime(String currTransTime) {
		this.currTransTime = currTransTime;
	}

	public String getCurrTransRrn() {
		return currTransRrn;
	}

	public void setCurrTransRrn(String currTransRrn) {
		this.currTransRrn = currTransRrn;
	}

	public String getRespCode() {
		return respCode;
	}

	public void setRespCode(String respCode) {
		this.respCode = respCode;
	}
	
}
