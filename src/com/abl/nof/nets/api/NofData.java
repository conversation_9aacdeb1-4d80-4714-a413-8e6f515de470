package com.abl.nof.nets.api;

import com.abl.utils.StringUtils;

/**
 * merchant token info (field 126 table 3)
 *
 */
public class NofData {
	String merchToken;	// 48 chars, left justified with space
	String merchTokenExpiry;	// mmyy
	String merchTokenIndex;		// 2 chars numeric
	String muidHash;	// 64 chars, sha256 of muid
	String mid;			// 15 chars, nof merchant id
	String currTransDate;	// mmdd
	String currTransTime;	// hhmmss
	String currTransRrn;	// 12 chars
	String authCode;		// 6 chars
	String rfu = "                                       "; // 39 spaces
	
	public NofData() {
	}
	
	public static NofData parse(String data) throws Exception {
		NofData nofData = new NofData();
		nofData.setMerchToken(data.substring(0, 48).trim());
		nofData.setMerchTokenExpiry(data.substring(48, 52));
		nofData.setMerchTokenIndex(data.substring(52, 54));
		nofData.setMuidHash(data.substring(54, 118));
		nofData.setMid(data.substring(118, 133));
		nofData.setCurrTransDate(data.substring(133, 137));
		nofData.setCurrTransTime(data.substring(137, 143));
		nofData.setCurrTransRrn(data.substring(143, 155));
		nofData.setAuthCode(data.substring(155, 161));
		return nofData;
	}

	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append(StringUtils.padRightToLen(merchToken, ' ', 48));
		builder.append(merchTokenExpiry);
		builder.append(merchTokenIndex);
		builder.append(muidHash);
		builder.append(StringUtils.padRightToLen(mid, ' ', 15));
		builder.append(currTransDate);
		builder.append(currTransTime);
		builder.append(StringUtils.padLeftToLen(currTransRrn, '0', 12));
		builder.append(authCode);
		builder.append(rfu);
		return builder.toString();
	}

	public String getMerchToken() {
		return merchToken;
	}

	public void setMerchToken(String merchToken) {
		this.merchToken = merchToken;
	}

	public String getMerchTokenExpiry() {
		return merchTokenExpiry;
	}

	public void setMerchTokenExpiry(String merchTokenExpiry) {
		this.merchTokenExpiry = merchTokenExpiry;
	}

	public String getMerchTokenIndex() {
		return merchTokenIndex;
	}

	public void setMerchTokenIndex(String merchTokenIndex) {
		this.merchTokenIndex = merchTokenIndex;
	}

	public String getMuidHash() {
		return muidHash;
	}

	public void setMuidHash(String muidHash) {
		this.muidHash = muidHash;
	}

	public String getMid() {
		return mid;
	}

	public void setMid(String mid) {
		this.mid = mid;
	}

	public String getCurrTransDate() {
		return currTransDate;
	}

	public void setCurrTransDate(String currTransDate) {
		this.currTransDate = currTransDate;
	}

	public String getCurrTransTime() {
		return currTransTime;
	}

	public void setCurrTransTime(String currTransTime) {
		this.currTransTime = currTransTime;
	}

	public String getCurrTransRrn() {
		return currTransRrn;
	}

	public void setCurrTransRrn(String currTransRrn) {
		this.currTransRrn = currTransRrn;
	}

	public String getAuthCode() {
		return authCode;
	}

	public void setAuthCode(String authCode) {
		this.authCode = authCode;
	}
	
}
