package com.abl.nof.nets.api;

public class F126Table53 extends F126Tlv {
	// 100 chars
	private String transactionCryptogram;	// 16 chars
	private String transactionCryptogramData;	// 64 chars
	private String rfu = "                    ";	// 20 spaces

	public F126Table53() {
		super(53);
	}
	
	public F126Table53(String transactionCryptogram, String transactionCryptogramData) {
		super(53);
		this.transactionCryptogram = transactionCryptogram;
		this.transactionCryptogramData = transactionCryptogramData;
	}
	
	public static F126Table53 parse(String data) throws Exception {
		if (data.length() < 100) {
			throw new Exception("unable to parse f126 table53, len=" + data.length() + " < 100");
		}
		F126Table53 table53 = new F126Table53();
		table53.setLen(data.length());
		table53.setData(data);
		table53.setTransactionCryptogram(data.substring(0, 16));
		table53.setTransactionCryptogramData(data.substring(16, 16+64));
		table53.setRfu(data.substring(80, 100));
		return table53;
	}
	
	@Override
	public String toString() {
		if (getData() == null) {
			StringBuilder data = new StringBuilder();
			data.append(transactionCryptogram);
			data.append(transactionCryptogramData);
			data.append(rfu);
			setData(data.toString());
			setLen(data.length());
		}
		return super.toString();
	}

	public String getTransactionCryptogram() {
		return transactionCryptogram;
	}

	public void setTransactionCryptogram(String transactionCryptogram) {
		this.transactionCryptogram = transactionCryptogram;
	}

	public String getTransactionCryptogramData() {
		return transactionCryptogramData;
	}

	public void setTransactionCryptogramData(String transactionCryptogramData) {
		this.transactionCryptogramData = transactionCryptogramData;
	}

	public String getRfu() {
		return rfu;
	}

	public void setRfu(String rfu) {
		this.rfu = rfu;
	}

}
