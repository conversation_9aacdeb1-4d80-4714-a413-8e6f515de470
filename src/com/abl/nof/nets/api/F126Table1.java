package com.abl.nof.nets.api;

import com.abl.utils.StringUtils;

public class F126Table1 extends F126Tlv {
	private String procCode;	// 6 chars: GMT=310000, AVT=810000, purch=000000, refund=200000
	private String origTid;		// 16 chars
	private String origRrn;		// 12 chars
	private String origDate;	// 4 chars
	private String origTime;	// 6 chars
	private String origAmount;	// 12 chars
	private String origAuthCode;	// 6 chars
	private String rfu = "                            ";	// 28 spaces

	public F126Table1() {
		super(1);
	}
	
	public F126Table1(String procCode, String origTid, String origRrn, String origDate, String origTime,
			String origAmount, String origAuthCode) {
		super(1);
		this.procCode = procCode;
		this.origTid = origTid;
		this.origRrn = origRrn;
		this.origDate = origDate;
		this.origTime = origTime;
		this.origAmount = origAmount;
		this.origAuthCode = origAuthCode;
	}

	public static F126Table1 parse(String data) throws Exception {
		if (data.length() < 90) {
			throw new Exception("unable to parse f126 table1, len=" + data.length() + " < 90");
		}
		F126Table1 table1 = new F126Table1();
		table1.setData(data);
		table1.setLen(data.length());
		table1.setProcCode(data.substring(0, 6));
		table1.setOrigTid(data.substring(6, 22));
		table1.setOrigRrn(data.substring(22, 34));
		table1.setOrigDate(data.substring(34, 38));
		table1.setOrigTime(data.substring(38, 44));
		table1.setOrigAmount(data.substring(44, 56));
		table1.setOrigAuthCode(data.substring(56, 62));
		return table1;
	}
	
	@Override
	public String toString() {
		if (getData() == null) {
			StringBuilder data = new StringBuilder();
			data.append(procCode);
			data.append(StringUtils.padRightToLen(origTid, ' ', 16));
			data.append(StringUtils.padLeftToLen(origRrn, '0', 12));
			data.append(origDate);
			data.append(origTime);
			data.append(StringUtils.padLeftToLen(origAmount, '0', 12));
			data.append(origAuthCode);
			data.append(rfu);
			setData(data.toString());
			setLen(data.length());
		}
		return super.toString();
	}

	public String getProcCode() {
		return procCode;
	}

	public void setProcCode(String procCode) {
		this.procCode = procCode;
	}

	public String getOrigTid() {
		return origTid;
	}

	public void setOrigTid(String origTid) {
		this.origTid = origTid;
	}

	public String getOrigRrn() {
		return origRrn;
	}

	public void setOrigRrn(String origRrn) {
		this.origRrn = origRrn;
	}

	public String getOrigDate() {
		return origDate;
	}

	public void setOrigDate(String origDate) {
		this.origDate = origDate;
	}

	public String getOrigTime() {
		return origTime;
	}

	public void setOrigTime(String origTime) {
		this.origTime = origTime;
	}

	public String getOrigAmount() {
		return origAmount;
	}

	public void setOrigAmount(String origAmount) {
		this.origAmount = origAmount;
	}

	public String getOrigAuthCode() {
		return origAuthCode;
	}

	public void setOrigAuthCode(String origAuthCode) {
		this.origAuthCode = origAuthCode;
	}
	
}
