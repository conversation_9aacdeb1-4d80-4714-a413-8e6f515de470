package com.abl.nof.nets.api;

import java.util.Date;

import org.jpos.iso.ISOException;
import org.jpos.iso.ISOMsg;

/**
 * purchase reversal request
 *
 */
public class NetsPurchaseReversalRequest implements NetsRequest {

	// these variables are to be set
	private String amount;
	private String mid;
	private String tid;
	private String merchName;
	private String stan;
	private String rrn;
	private Date date;
	private String transDateTime;
	private String txnDate;
	private String txnTime;
	
	// the following are for field 90
	private String origMti="0200";	// 4 n
	private String origRrn;	// 12 n
	private String origDate;	// 4 n
	private String origTime;	// 8 n (why 8?)
	private String origCaptureDate;	// 4 n
	
	// these variables are fixed according to specs, and should not be required to set manually
	private String mti = "0420";	// TODO: for repeat is 0421
	private String procCode = "000000";
	private String posEntryMode = "100";
	private String posCondCode = "00";
	private String f126;

	public NetsPurchaseReversalRequest() {
		this.date = new Date();
	}

	public ISOMsg createIsoMsg() throws ISOException {
		ISOMsg requestMsg = new ISOMsg();
		requestMsg.setMTI(mti);
		requestMsg.set(3, procCode);
		requestMsg.set(4, amount);
		requestMsg.set(7, transDateTime);
		requestMsg.set(11, stan);
		requestMsg.set(12, txnTime);
		requestMsg.set(13, txnDate);
		requestMsg.set(15, txnDate); // requested by susanto, use transdate as settlement date
		requestMsg.set(22, posEntryMode);
		requestMsg.set(25, posCondCode);
		requestMsg.set(37, rrn);
		requestMsg.set(39, "00"); //// requested by susanto, send 00 for field 39
		requestMsg.set(41, tid);
		requestMsg.set(42, mid);
		requestMsg.set(43, merchName);
		
		StringBuilder sb = new StringBuilder();
		sb.append(origMti);
		if (origRrn != null) {
			sb.append(origRrn);
		} else {
			sb.append("000000000000");
		}
		if (origDate != null) {
			sb.append(origDate);
		} else {
			sb.append("0000");
		}
		if (origTime != null) {
			sb.append(origTime);
			sb.append("00");
		} else {
			sb.append("00000000");
		}
		if (origCaptureDate != null) {
			sb.append(origCaptureDate);
		} else {
			sb.append("0000");
		}
		sb.append("0000000000");
		String f90 = sb.toString();
		requestMsg.set(90, f90);

		requestMsg.set(126, f126);

		// set empty mac first
		requestMsg.set(128, new byte[8]);
		
		return requestMsg;
	}

	public String getAmount() {
		return amount;
	}

	public void setAmount(String amount) {
		this.amount = amount;
	}

	public String getMid() {
		return mid;
	}

	public void setMid(String mid) {
		this.mid = mid;
	}

	public String getTid() {
		return tid;
	}

	public void setTid(String tid) {
		this.tid = tid;
	}

	public String getMerchName() {
		return merchName;
	}

	public void setMerchName(String merchName) {
		this.merchName = merchName;
	}

	public String getStan() {
		return stan;
	}

	public void setStan(String stan) {
		this.stan = stan;
	}

	public String getRrn() {
		return rrn;
	}

	public void setRrn(String rrn) {
		this.rrn = rrn;
	}

	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}

	public String getTransDateTime() {
		return transDateTime;
	}

	public void setTransDateTime(String transDateTime) {
		this.transDateTime = transDateTime;
	}

	public String getTxnDate() {
		return txnDate;
	}

	public void setTxnDate(String txnDate) {
		this.txnDate = txnDate;
	}

	public String getTxnTime() {
		return txnTime;
	}

	public void setTxnTime(String txnTime) {
		this.txnTime = txnTime;
	}

	public String getOrigMti() {
		return origMti;
	}

	public void setOrigMti(String origMti) {
		this.origMti = origMti;
	}

	public String getOrigRrn() {
		return origRrn;
	}

	public void setOrigRrn(String origRrn) {
		this.origRrn = origRrn;
	}

	public String getOrigDate() {
		return origDate;
	}

	public void setOrigDate(String origDate) {
		this.origDate = origDate;
	}

	public String getOrigTime() {
		return origTime;
	}

	public void setOrigTime(String origTime) {
		this.origTime = origTime;
	}

	public String getOrigCaptureDate() {
		return origCaptureDate;
	}

	public void setOrigCaptureDate(String origCaptureDate) {
		this.origCaptureDate = origCaptureDate;
	}

	public String getMti() {
		return mti;
	}

	public void setMti(String mti) {
		this.mti = mti;
	}

	public String getProcCode() {
		return procCode;
	}

	public void setProcCode(String procCode) {
		this.procCode = procCode;
	}

	public String getPosEntryMode() {
		return posEntryMode;
	}

	public void setPosEntryMode(String posEntryMode) {
		this.posEntryMode = posEntryMode;
	}

	public String getPosCondCode() {
		return posCondCode;
	}

	public void setPosCondCode(String posCondCode) {
		this.posCondCode = posCondCode;
	}

	public String getF126() {
		return f126;
	}

	public void setF126(String f126) {
		this.f126 = f126;
	}

}
