package com.abl.nof.nets.api;

import java.util.Date;

import org.jpos.iso.ISOException;
import org.jpos.iso.ISOMsg;

import com.abl.utils.DateUtils;

/**
 * account verification request
 *
 */
public class NetsCfaRequest implements NetsRequest {

	// these variables are to be set
	private String amount;
	private String mid;
	private String tid;
	private String merchName;
	private String stan;
	private String rrn;
	private Date date;
	private String transDateTime;
	private String txnDate;
	private String txnTime;
	
	private String tableData;	// table data from sdk, should be only table 2 and 5
	
	// the following are for table 3
	private String encryptedNofData; // 400 chars in hex (decrypted to NofData)
	
	// these variables are fixed according to specs, and should not be required to set manually
	private String mti = "0200";
	private String procCode = "810000";
	private String posEntryMode = "100";
	private String posCondCode = "00";
	private String f126;
	
	public NetsCfaRequest(Date txnDateTime) {
		this.date = txnDateTime;
		this.transDateTime = DateUtils.format(date, "MMddHHmmss");
		this.txnTime = DateUtils.format(date, "HHmmss");
		this.txnDate = DateUtils.format(date, "MMdd");
	}
	
	public ISOMsg createIsoMsg() throws ISOException {
		ISOMsg requestMsg = new ISOMsg();
		requestMsg.setMTI(mti);
		requestMsg.set(3, procCode);
		requestMsg.set(4, amount);
		requestMsg.set(7, transDateTime);
		requestMsg.set(11, stan);
		requestMsg.set(12, txnTime);
		requestMsg.set(13, txnDate);
		requestMsg.set(22, posEntryMode);
		requestMsg.set(25, posCondCode);
		requestMsg.set(37, rrn);
		requestMsg.set(41, tid);
		requestMsg.set(42, mid);
		requestMsg.set(43, merchName);
		 
		F126Table3 table3 = new F126Table3(encryptedNofData);
		// assume no need table4
		f126 = tableData + table3.toString();
		requestMsg.set(126, f126);

		// set empty mac first
		requestMsg.set(128, new byte[8]);
		
		return requestMsg;
	}

	public String getMid() {
		return mid;
	}

	public void setMid(String mid) {
		this.mid = mid;
	}

	public String getTid() {
		return tid;
	}

	public void setTid(String tid) {
		this.tid = tid;
	}

	public String getMerchName() {
		return merchName;
	}

	public void setMerchName(String merchName) {
		this.merchName = merchName;
	}

	public String getStan() {
		return stan;
	}

	public void setStan(String stan) {
		this.stan = stan;
	}

	public String getRrn() {
		return rrn;
	}

	public void setRrn(String rrn) {
		this.rrn = rrn;
	}

	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}

	public String getTransDateTime() {
		return transDateTime;
	}

	public void setTransDateTime(String transDateTime) {
		this.transDateTime = transDateTime;
	}

	public String getTxnDate() {
		return txnDate;
	}

	public void setTxnDate(String txnDate) {
		this.txnDate = txnDate;
	}

	public String getTxnTime() {
		return txnTime;
	}

	public void setTxnTime(String txnTime) {
		this.txnTime = txnTime;
	}

	public String getTableData() {
		return tableData;
	}

	public void setTableData(String tableData) {
		this.tableData = tableData;
	}
	
	public String getEncryptedNofData() {
		return encryptedNofData;
	}

	public void setEncryptedNofData(String encryptedNofData) {
		this.encryptedNofData = encryptedNofData;
	}

	public String getMti() {
		return mti;
	}

	public void setMti(String mti) {
		this.mti = mti;
	}

	public String getProcCode() {
		return procCode;
	}

	public void setProcCode(String procCode) {
		this.procCode = procCode;
	}

	public String getAmount() {
		return amount;
	}

	public void setAmount(String amount) {
		this.amount = amount;
	}

	public String getPosEntryMode() {
		return posEntryMode;
	}

	public void setPosEntryMode(String posEntryMode) {
		this.posEntryMode = posEntryMode;
	}

	public String getPosCondCode() {
		return posCondCode;
	}

	public void setPosCondCode(String posCondCode) {
		this.posCondCode = posCondCode;
	}

	public String getF126() {
		return f126;
	}

	public void setF126(String f126) {
		this.f126 = f126;
	}
	
}
