package com.abl.nof.nets.api;

import java.util.Date;

import org.jpos.iso.ISOException;
import org.jpos.iso.ISOMsg;

import com.abl.utils.DateUtils;

/**
 * purchase request
 *
 */
public class NetsChangeKeyRequest implements NetsRequest {

	// these variables are to be set
	private String stan;
	private Date date;
	private String transDateTime;
	
	// these variables are fixed according to specs, and should not be required to set manually
	private String mti = "0800";
	private String securityRelatedControlInfo = "0103000000000000";	// field 53
	private String networkMgtInfoCode = "161";	// field 70, 161 = change key
	private String cryptoServiceMsg = "060" + "CSM(MCL/RSI RCV/NETS ORG/ACQM SVR/)";	// field 123
	
	public NetsChangeKeyRequest() {
		date = new Date();
		transDateTime = DateUtils.format(date, "MMddHHmmss");		
	}
	
	public ISOMsg createIsoMsg() throws ISOException {
		ISOMsg requestMsg = new ISOMsg();
		requestMsg.setMTI(mti);
		requestMsg.set(7, transDateTime);
		requestMsg.set(11, stan);
		requestMsg.set(53, securityRelatedControlInfo);
		requestMsg.set(70, networkMgtInfoCode);
		requestMsg.set(123, cryptoServiceMsg);
		
		// set empty mac first
		requestMsg.set(128, new byte[8]);
		
		return requestMsg;
	}

	public String getStan() {
		return stan;
	}

	public void setStan(String stan) {
		this.stan = stan;
	}

	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}

	public String getTransDateTime() {
		return transDateTime;
	}

	public void setTransDateTime(String transDateTime) {
		this.transDateTime = transDateTime;
	}

	public String getMti() {
		return mti;
	}

	public void setMti(String mti) {
		this.mti = mti;
	}

	public String getSecurityRelatedControlInfo() {
		return securityRelatedControlInfo;
	}

	public void setSecurityRelatedControlInfo(String securityRelatedControlInfo) {
		this.securityRelatedControlInfo = securityRelatedControlInfo;
	}

	public String getNetworkMgtInfoCode() {
		return networkMgtInfoCode;
	}

	public void setNetworkMgtInfoCode(String networkMgtInfoCode) {
		this.networkMgtInfoCode = networkMgtInfoCode;
	}

	public String getCryptoServiceMsg() {
		return cryptoServiceMsg;
	}

	public void setCryptoServiceMsg(String cryptoServiceMsg) {
		this.cryptoServiceMsg = cryptoServiceMsg;
	}

	@Override
	public String getMid() {
		return null;
	}

	@Override
	public void setMid(String mid) {
	}

	@Override
	public String getTid() {
		return null;
	}

	@Override
	public void setTid(String tid) {	
	}

	@Override
	public String getMerchName() {
		return null;
	}

	@Override
	public void setMerchName(String merchName) {
	}

}
