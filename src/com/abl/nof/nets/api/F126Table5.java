package com.abl.nof.nets.api;

public class F126Table5 extends F126Tlv {
	// 100 chars
	private String transactionCryptogram;	// 16 chars
	private String transactionCryptogramData;	// 64 chars
	private String rfu = "                    ";	// 20 spaces

	public F126Table5() {
		super(5);
	}
	
	public F126Table5(String transactionCryptogram, String transactionCryptogramData) {
		super(5);
		this.transactionCryptogram = transactionCryptogram;
		this.transactionCryptogramData = transactionCryptogramData;
	}
	
	public static F126Table5 parse(String data) throws Exception {
		if (data.length() < 80) {
			throw new Exception("unable to parse f126 table5, len=" + data.length() + " < 80");
		}
		F126Table5 table5 = new F126Table5();
		table5.setLen(data.length());
		table5.setData(data);
		table5.setTransactionCryptogram(data.substring(0, 16));
		table5.setTransactionCryptogramData(data.substring(16, 16+64));
		return table5;
	}
	
	@Override
	public String toString() {
		if (getData() == null) {
			StringBuilder data = new StringBuilder();
			data.append(transactionCryptogram);
			data.append(transactionCryptogramData);
			data.append(rfu);
			setData(data.toString());
			setLen(data.length());
		}
		return super.toString();
	}

	public String getTransactionCryptogram() {
		return transactionCryptogram;
	}

	public void setTransactionCryptogram(String transactionCryptogram) {
		this.transactionCryptogram = transactionCryptogram;
	}

	public String getTransactionCryptogramData() {
		return transactionCryptogramData;
	}

	public void setTransactionCryptogramData(String transactionCryptogramData) {
		this.transactionCryptogramData = transactionCryptogramData;
	}

	public String getRfu() {
		return rfu;
	}

	public void setRfu(String rfu) {
		this.rfu = rfu;
	}

}
