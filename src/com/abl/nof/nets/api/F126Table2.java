package com.abl.nof.nets.api;

import com.abl.utils.StringUtils;

public class F126Table2 extends F126Tlv {
	// 1 char:
	//		G = GMT, GMT retry
	//		N = cardholder not present (refund, refund reversal)
	//		P = cardholder present (AVT, AVT cancel)
	//		A = authorized by cardholder using AVT (purchase, purchase reversal)
	private String cardholderPresence;
	
	// 1 char:
	//		N = not applicable (cardholder not present) (GMT, GMT retry, refund, refund reversal)
	//		H = based on merchant host stored user id and password (GMT, GMT retry, AVT, AVT cancel, purchase, purchase reversal)
	//		M = based on mobile device authentication
	//		O = based on OTP or 2FA
	//		K = KYX
	//		X = others
	private String cardholderAuthenticationMethod;
	
	// 64 chars (left-justified with spaces)
	//		<UUID><Manufacturer><Space> <Model><Spaces>
	//		UUID = 36 chars
	
	private String merchAppDeviceInfo;
	// 11 chars gps latitude (left-justified with spaces)
	private String gpsCoordLat;
	// 11 chars gps longitude (left-justified with spaces)
	private String gpsCoordLong;
	// 15 chars ip addr (left-justified with spaces)
	private String ipAddr;
	
	private String rfu = "                                               ";	// 47 spaces

	public F126Table2() {
		super(2);
	}
	
	public F126Table2(String cardholderPresence, String cardholderAuthenticationMethod,
			String merchAppDeviceInfo, String gpsCoordLat, String gpsCoordLong, String ipAddr) {
		super(2);
		this.cardholderPresence = cardholderPresence;
		this.cardholderAuthenticationMethod = cardholderAuthenticationMethod;
		this.merchAppDeviceInfo = merchAppDeviceInfo;
		this.gpsCoordLat = gpsCoordLat;
		this.gpsCoordLong = gpsCoordLong;
		this.ipAddr = ipAddr;
	}
	
	public static F126Table2 parse(String data) throws Exception {
		if (data.length() < 150) {
			throw new Exception("unable to parse f126 table2, len=" + data.length() + " < 150");
		}
		F126Table2 table2 = new F126Table2();
		table2.setData(data);
		table2.setLen(data.length());
		table2.setCardholderPresence(data.substring(0, 1));
		table2.setCardholderAuthenticationMethod(data.substring(1, 2));
		table2.setMerchAppDeviceInfo(data.substring(2, 66));
//		table2.setGpsCoord(data.substring(66, 108));
		table2.setGpsCoordLat(data.substring(66, 77));
		table2.setGpsCoordLong(data.substring(77, 88));
		table2.setIpAddr(data.substring(88, 103));
		return table2;
	}
	
	@Override
	public String toString() {
		if (getData() == null) {
			StringBuilder data = new StringBuilder();
			data.append(cardholderPresence);
			data.append(cardholderAuthenticationMethod); 
			if (merchAppDeviceInfo == null) {
				data.append(StringUtils.padRightToLen("",' ', 64));
			} else {
				data.append(StringUtils.padRightToLen(merchAppDeviceInfo,' ', 64));
			}
//			if (gpsCoord == null) {
//				data.append(StringUtils.padRightToLen("",' ', 42));
//			} else {
//				data.append(StringUtils.padRightToLen(gpsCoord,' ', 42));
//			}
			if (gpsCoordLat == null) {
				data.append(StringUtils.padRightToLen("",' ', 11));
			} else {
				data.append(StringUtils.padRightToLen(gpsCoordLat,' ', 11));
			}
			if (gpsCoordLong == null) {
				data.append(StringUtils.padRightToLen("",' ', 11));
			} else {
				data.append(StringUtils.padRightToLen(gpsCoordLong,' ', 11));
			}
			if (ipAddr == null) {
				data.append(StringUtils.padRightToLen("",' ', 15));
			} else {
				data.append(StringUtils.padRightToLen(ipAddr,' ', 15));
			}
			data.append(rfu);
			setData(data.toString());
			setLen(data.length());
		}
		return super.toString();
	}

	public String getCardholderPresence() {
		return cardholderPresence;
	}

	public void setCardholderPresence(String cardholderPresence) {
		this.cardholderPresence = cardholderPresence;
	}

	public String getCardholderAuthenticationMethod() {
		return cardholderAuthenticationMethod;
	}

	public void setCardholderAuthenticationMethod(String cardholderAuthenticationMethod) {
		this.cardholderAuthenticationMethod = cardholderAuthenticationMethod;
	}

	public String getMerchAppDeviceInfo() {
		return merchAppDeviceInfo;
	}

	public void setMerchAppDeviceInfo(String merchAppDeviceInfo) {
		this.merchAppDeviceInfo = merchAppDeviceInfo;
	}

	public String getGpsCoordLat() {
		return gpsCoordLat;
	}

	public void setGpsCoordLat(String gpsCoordLat) {
		this.gpsCoordLat = gpsCoordLat;
	}

	public String getGpsCoordLong() {
		return gpsCoordLong;
	}

	public void setGpsCoordLong(String gpsCoordLong) {
		this.gpsCoordLong = gpsCoordLong;
	}

	public String getIpAddr() {
		return ipAddr;
	}

	public void setIpAddr(String ipAddr) {
		this.ipAddr = ipAddr;
	}

	public String getRfu() {
		return rfu;
	}

	public void setRfu(String rfu) {
		this.rfu = rfu;
	}

}
