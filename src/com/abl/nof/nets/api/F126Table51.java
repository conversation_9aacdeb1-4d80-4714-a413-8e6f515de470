package com.abl.nof.nets.api;

public class F126Table51 extends F126Tlv {
	// 240 chars
	private String encryptedMerchTokenInfo;
	private String rfu = "                                        ";	// 40 spaces

	public F126Table51() {
		super(51);
	}
	
	public F126Table51(String data) {
		super(51);
		this.encryptedMerchTokenInfo = data;
	}
	
	public static F126Table51 parse(String data) throws Exception {
		if (data.length() < 280) {
			throw new Exception("unable to parse f126 table51, len=" + data.length() + " < 280");
		}
		F126Table51 table3 = new F126Table51();
		table3.setLen(data.length());
		table3.setData(data);
		table3.setEncryptedMerchTokenInfo(data.substring(0, 240));
		return table3;
	}
	
	@Override
	public String toString() {
		if (getData() == null) {
			StringBuilder data = new StringBuilder();
			data.append(encryptedMerchTokenInfo);
			data.append(rfu);
			setData(data.toString());
			setLen(data.length());
		}
		return super.toString();
	}

	public String getEncryptedMerchTokenInfo() {
		return encryptedMerchTokenInfo;
	}

	public void setEncryptedMerchTokenInfo(String encryptedMerchTokenInfo) {
		this.encryptedMerchTokenInfo = encryptedMerchTokenInfo;
	}

	public String getRfu() {
		return rfu;
	}

	public void setRfu(String rfu) {
		this.rfu = rfu;
	}

}
