package com.abl.nof.nets.api;

public class F126Table3 extends F126Tlv {
	// 480 chars
	private String encryptedNofData;
//	private String rfu = "                                                                                ";	// 80 spaces

	public F126Table3() {
		super(3);
	}
	
	public F126Table3(String data) {
		super(3);
		this.encryptedNofData = data;
	}
	
	public static F126Table3 parse(String data) throws Exception {
		if (data.length() < 400) {
			throw new Exception("unable to parse f126 table3, len=" + data.length() + " < 480");
		}
		F126Table3 table3 = new F126Table3();
		table3.setLen(data.length());
		table3.setData(data);
		table3.setEncryptedNofData(data.substring(0, 400));
//		table3.setRfu(data.substring(400, 480));
		return table3;
	}
	
	@Override
	public String toString() {
		if (getData() == null) {
			StringBuilder data = new StringBuilder();
			data.append(encryptedNofData);
//			data.append(rfu);
			setData(data.toString());
			setLen(data.length());
		}
		return super.toString();
	}

	public String getEncryptedNofData() {
		return encryptedNofData;
	}

	public void setEncryptedNofData(String encryptedNofData) {
		this.encryptedNofData = encryptedNofData;
	}

//	public String getRfu() {
//		return rfu;
//	}
//
//	public void setRfu(String rfu) {
//		this.rfu = rfu;
//	}

}
