package com.abl.nof.nets;

import org.apache.log4j.Logger;
import org.jpos.iso.ISOMsg;

/**
 * to store network responses from nets
 *
 */
public class UnmatchResponse {

	private static Logger logger=Logger.getLogger(UnmatchResponse.class);
	
	public String key;
	public ISOMsg response;
	
	public UnmatchResponse(String key) {
		this.key = key;
	}

	public synchronized void clearResponse() {
		response = null;
	}
	
	public synchronized void setResponse(ISOMsg m) {
		this.response = m;
		logger.debug("notifyAll, key=" + key);
		this.notifyAll();
	}
	
	public synchronized ISOMsg getResponse() {
		return response;
	}
	
	public synchronized ISOMsg getResponse(long timeoutMs) {
		if (response == null) {
			try {
				logger.debug(key + ", wait " + timeoutMs);
				this.wait(timeoutMs);
			} catch (InterruptedException e) {
				// nothing
			}
		}
		return response;
	}
}
