package com.abl.nof.nets;

import org.apache.commons.pool2.ObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.apache.log4j.Logger;
import org.jpos.q2.iso.MUXPool;
import org.jpos.util.NameRegistrar;
import org.jpos.util.NameRegistrar.NotFoundException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * manages a pool of NetsTerminal
 *
 */
@Component
public class NetsTerminalManager implements InitializingBean {
	
	private static final Logger logger=Logger.getLogger(NetsTerminalManager.class);
	
	@Autowired private NetsTerminalFactory netsTerminalFactory;
	
	private ObjectPool<NetsTerminal> netsTerminalPool;
	private long maxWaitMillis = 60000;
	
	@Override
	public void afterPropertiesSet() throws Exception {
		GenericObjectPoolConfig config = new GenericObjectPoolConfig();
		config.setMaxTotal(netsTerminalFactory.getNumberTerminals());
		config.setMaxWaitMillis(maxWaitMillis);
		netsTerminalPool = new GenericObjectPool<NetsTerminal>(netsTerminalFactory, config);
	}
	
	/**
	 * returns true if there is connection to nets
	 * 
	 * @return
	 */
	public boolean isConnected() {
		MUXPool muxPool;
		try {
			muxPool = (MUXPool) NameRegistrar.get("mux.nets-mux");
		} catch (NotFoundException e) {
			logger.error("unable to get muxpool for mux.nets-mux", e);
			return false;
		}
		if (!muxPool.isConnected()) {
			logger.error("unable to get muxpool: not connected");
			return false;
		}
		return true;
	}
	
	/**
	 * get a NetsTerminal from NetsTerminal pool
	 * 
	 * caller must call returnNetsTerminal to return the terminal to the pool
	 * 
	 * @return
	 */
	public NetsTerminal borrowNetsTerminal() {
		try {
			NetsTerminal netsTerminal = netsTerminalPool.borrowObject();
			netsTerminal.setActive(true);
			return netsTerminal;
		} catch (Exception e) {
			logger.error("unable to borrow terminal", e);
			return null;
		}
	}
	
	/**
	 * return NetsTerminal to pool
	 * 
	 * @param netsTerminal
	 */
	public void returnNetsTerminal(NetsTerminal netsTerminal) {
		try {
			netsTerminal.setActive(false);
			netsTerminalPool.returnObject(netsTerminal);
		} catch (Exception e) {
			logger.error("unable to return terminal", e);
		}
	}
}
