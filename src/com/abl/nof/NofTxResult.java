package com.abl.nof;

import com.abl.TxResult;

public class NofTxResult extends TxResult {
	public static final String ACCOUNT_INACTIVE = "ACCOUNT_INACTIVE";
	public static final String ACCOUNT_NOT_FOUND = "ACCOUNT_NOT_FOUND";
	public static final String DUPLICATE = "DUPLICATE";
	public static final String DUPLICATE_OFFLINE = "DUPLICATE_OFFLINE";
	public static final String HSM_ERROR = "HSM_ERROR";
	public static final String INVALID_MUID = "INVALID_MUID";
	public static final String INVALID_MERCH_TOKEN_ID = "INVALID_MERCH_TOKEN_ID";
	public static final String INVALID_ORIG_RRN = "INVALID_ORIG_RRN";
	public static final String NETS_NO_TERMINAL_AVAIL = "NETS_NO_TERM_AVAIL";
	public static final String NETS_DECLINE = "NETS_DECLINE";
	public static final String NETS_CONNECT_ERROR = "NETS_CONNECT_ERROR";
	public static final String NETS_SEND_ERROR = "NETS_SEND_ERROR";
	public static final String NETS_NO_RESP = "NETS_NO_RESP";
	public static final String NETS_INVALID_RESP = "NETS_INVALID_RESP";
	public static final String NETS_ONLINE_PIN_REQUIRED = "NETS_ONLINE_PIN_REQUIRED";
	public static final String NETS_PIN_ERROR = "NETS_PIN_ERROR";
	public static final String NETS_PIN_TRIES_EXCEEDED = "NETS_PIN_TRIES_EXCEEDED";
	public static final String TIMEOUT = "TIMEOUT";
	public static final String ORIG_TXN_NOT_FOUND_FOR_REVERSAL = "ORIG_TXN_NOT_FOUND_FOR_REVERSAL";
	public static final String ORIG_TXN_DECLINED_FOR_REVERSAL = "ORIG_TXN_DECLINED_FOR_REVERSAL";
	public static final String ORIG_TXN_REVERSED_FOR_REVERSAL = "ORIG_TXN_REVERSED_FOR_REVERSAL";
	public static final String EXCEED_REVERSAL_MAX_TRY = "EXCEED_REVERSAL_MAX_TRY";
	public static final String PROCESSING_ERR = "PROCESSING_ERR";
}
