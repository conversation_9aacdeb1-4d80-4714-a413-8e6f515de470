package com.abl.dcp.payment.api;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import org.apache.log4j.Logger;

public class DcpDeclinedPaymentRequest {
    private static final Logger logger=Logger.getLogger(DcpDeclinedPaymentRequest.class);

    @Expose
    @SerializedName("job-no") private String jobNumber;
    @Expose @SerializedName("requested-amount") private Long requestedAmt;
    @Expose @SerializedName("fare-amount") private Long fareAmt;
    @Expose @SerializedName("gst-amount") private Long gstAmt;
    @Expose @SerializedName("admin-fee") private Long adminFee;
    @Expose @SerializedName("payment-mode") private Integer paymentMode;
    @Expose @SerializedName("response-code") private String responseCode;

    public static Logger getLogger() {
        return logger;
    }

    public String getJobNumber() {
        return jobNumber;
    }

    public void setJobNumber(String jobNumber) {
        this.jobNumber = jobNumber;
    }

    public Long getRequestedAmt() {
        return requestedAmt;
    }

    public void setRequestedAmt(Long requestedAmt) {
        this.requestedAmt = requestedAmt;
    }

    public Long getFareAmt() {
        return fareAmt;
    }

    public void setFareAmt(Long fareAmt) {
        this.fareAmt = fareAmt;
    }

    public Long getGstAmt() {
        return gstAmt;
    }

    public void setGstAmt(Long gstAmt) {
        this.gstAmt = gstAmt;
    }

    public Long getAdminFee() {
        return adminFee;
    }

    public void setAdminFee(Long adminFee) {
        this.adminFee = adminFee;
    }

    public Integer getPaymentMode() {
        return paymentMode;
    }

    public void setPaymentMode(Integer paymentMode) {
        this.paymentMode = paymentMode;
    }

    public String getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(String responseCode) {
        this.responseCode = responseCode;
    }

	@Override
	public String toString() {
		return "DcpDeclinedPaymentRequest [jobNumber=" + jobNumber + ", requestedAmt=" + requestedAmt + ", fareAmt="
				+ fareAmt + ", gstAmt=" + gstAmt + ", adminFee=" + adminFee + ", paymentMode=" + paymentMode
				+ ", responseCode=" + responseCode + "]";
	}
    
    
}
