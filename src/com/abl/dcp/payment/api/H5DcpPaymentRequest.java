package com.abl.dcp.payment.api;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class H5DcpPaymentRequest {
	
	@Expose
    @SerializedName("job_no")
	private String jobNo;
	
	@Expose
    @SerializedName("requested_amount")
	private String requestedAmount;
	
	
	@Expose
    @SerializedName("fare_amount")
	private String fareAmount;
	
	@Expose
    @SerializedName("gst_amount")
	private String gstAmount;
	
	@Expose
    @SerializedName("admin_fee")
	private String adminFee;
	
	@Expose
    @SerializedName("payment_mode")
	private String paymentMode;
	
	@Expose
    @SerializedName("response_code")
	private String responseCode;

	public String getJobNo() {
		return jobNo;
	}

	public void setJobNo(String jobNo) {
		this.jobNo = jobNo;
	}

	public String getRequestedAmount() {
		return requestedAmount;
	}

	public void setRequestedAmount(String requestedAmount) {
		this.requestedAmount = requestedAmount;
	}

	public String getFareAmount() {
		return fareAmount;
	}

	public void setFareAmount(String fareAmount) {
		this.fareAmount = fareAmount;
	}

	public String getGstAmount() {
		return gstAmount;
	}

	public void setGstAmount(String gstAmount) {
		this.gstAmount = gstAmount;
	}

	public String getAdminFee() {
		return adminFee;
	}

	public void setAdminFee(String adminFee) {
		this.adminFee = adminFee;
	}

	public String getPaymentMode() {
		return paymentMode;
	}

	public void setPaymentMode(String paymentMode) {
		this.paymentMode = paymentMode;
	}

	public String getResponseCode() {
		return responseCode;
	}

	public void setResponseCode(String responseCode) {
		this.responseCode = responseCode;
	}

	@Override
	public String toString() {
		return "H5DeclinedPaymentRequest [jobNo=" + jobNo + ", requestedAmount=" + requestedAmount + ", fareAmount="
				+ fareAmount + ", gstAmount=" + gstAmount + ", adminFee=" + adminFee + ", paymentMode=" + paymentMode
				+ ", responseCode=" + responseCode + "]";
	}
	
}
