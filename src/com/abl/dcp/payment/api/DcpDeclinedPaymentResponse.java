package com.abl.dcp.payment.api;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class DcpDeclinedPaymentResponse {
    @Expose
    @SerializedName("message") private String message;
    @Expose
    @SerializedName("responseCode") private Integer responseCode;

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Integer getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(Integer responseCode) {
        this.responseCode = responseCode;
    }

	@Override
	public String toString() {
		return "DcpDeclinedPaymentResponse [message=" + message + ", responseCode=" + responseCode + "]";
	}
    
    
}
