package com.abl.dcp.payment;

public class DcpHttpException extends Exception {

    private Integer httpStatusCode;

    public DcpHttpException() {
        super();
    }

    public DcpHttpException(String message, Throwable cause) {
        super(message, cause);
    }

    public DcpHttpException(String message) {
        super(message);
    }

    public DcpHttpException(Throwable cause) {
        super(cause);
    }

    public DcpHttpException(Integer httpStatusCode) {
        super();
        this.httpStatusCode = httpStatusCode;
    }

    public DcpHttpException(String message, Integer httpStatusCode, Throwable cause) {
        super(message, cause);
        this.httpStatusCode = httpStatusCode;
    }

    public DcpHttpException(String message, Integer httpStatusCode) {
        super(message);
        this.httpStatusCode = httpStatusCode;
    }

    public DcpHttpException(Integer httpStatusCode, Throwable cause) {
        super(cause);
        this.httpStatusCode = httpStatusCode;
    }

    public Integer getHttpStatusCode() {
        return httpStatusCode;
    }

    public void setHttpStatusCode(Integer httpStatusCode) {
        this.httpStatusCode = httpStatusCode;
    }

}
