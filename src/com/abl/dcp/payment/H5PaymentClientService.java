package com.abl.dcp.payment;

import com.abl.dcp.payment.api.H5PaymentRequest;
import com.abl.dcp.payment.api.H5PaymentResponse;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHost;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.config.RequestConfig.Builder;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.InputStreamEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.SocketTimeoutException;

@Component
public class H5PaymentClientService {
    private static final Logger logger = Logger.getLogger(H5PaymentClientService.class);

    @Value("${h5.paymentUrl:http://127.0.0.1:5001/h5Payment}")
    public String h5PaymentUrl;

    @Value("${h5.proxyHost:}")
    private String proxyHost;

    @Value("${h5.proxyPort:}")
    private Integer proxyPort;

    @Value("${h5.connectTimeoutMs:1000}")
    private int connectTimeoutMs;

    @Value("${h5.socketTimeoutMs:10000}")
    private int socketTimeoutMs;

    @Value("${h5.requestTimeoutMs:10000}")
    private int requestTimeoutMs;

    public H5PaymentClientService(){

    }

    private HttpClient getHttpClient() throws Exception {
        HttpClientBuilder httpClientBuilder = HttpClientBuilder.create();
        Builder requestConfigBuilder = RequestConfig.custom()
                .setConnectTimeout(connectTimeoutMs)
                .setConnectionRequestTimeout(requestTimeoutMs)
                .setSocketTimeout(socketTimeoutMs);
        if ((proxyHost != null)&&(!proxyHost.isEmpty())) {
            logger.trace("using proxy " + proxyHost + ":" + proxyPort);
            HttpHost proxy = new HttpHost(proxyHost, proxyPort, "http");
            requestConfigBuilder.setProxy(proxy);
        }
        RequestConfig rc = requestConfigBuilder.build();
        httpClientBuilder.setDefaultRequestConfig(rc);

        // create trustall trustmanager for ssl
        X509TrustManager tm = new X509TrustManager() {
            @Override
            public void checkClientTrusted(java.security.cert.X509Certificate[] x509Certificates, String s) throws java.security.cert.CertificateException {
                // nothing
            }
            @Override
            public void checkServerTrusted(java.security.cert.X509Certificate[] x509Certificates, String s) throws java.security.cert.CertificateException {
                // nothing
            }
            @Override
            public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                return null;
            }
        };

        // configure ssl
        SSLContext sslContext;
        try {
            sslContext = SSLContexts.custom().build();
            sslContext.init(null, new TrustManager[]{tm},null);
        } catch (Exception e) {
            throw new Exception("unable to init ssl", e);
        }
        SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext, NoopHostnameVerifier.INSTANCE);
        httpClientBuilder.setSSLSocketFactory(sslsf);

        // create http client
        return httpClientBuilder.build();
    }

    public H5PaymentResponse sendH5PaymentRequest(H5PaymentRequest request) throws Exception {
        String json = new Gson().toJson(request);
        logger.debug("H5 payment request (json): " + json);

        String resp = sendPostRequest(h5PaymentUrl, json);
        String jsonResp;
        if (resp == null) {
            return null;
        }else {
            logger.warn("H5 payment resp: " + resp);
            jsonResp = resp;
        }
        JsonObject jsonObject= (JsonObject)new JsonParser().parse(jsonResp);
        return new Gson().fromJson(jsonObject, H5PaymentResponse.class);


    }

    public String sendPostRequest(String url, String data) throws Exception {
        // prepare post
        HttpPost httpPost=new HttpPost(url);
        httpPost.addHeader("Content-Type", "application/json");

        InputStream inputStream=new ByteArrayInputStream(data.getBytes());
        InputStreamEntity inputStreamEntity=new InputStreamEntity(inputStream);
        inputStreamEntity.setContentType("application/json");
        httpPost.setEntity(inputStreamEntity);

        // send http post
        logger.debug("sending POST to " + url);
        logger.trace("POST data=" + data);
        return sendHttpRequest(httpPost);
    }

    private String sendHttpRequest(HttpUriRequest httpRequest) throws Exception {
        logger.trace("getHttpClient");
        HttpClient httpClient = getHttpClient();
        HttpResponse response;
        try {
            logger.trace("sending HttpRequest");
            response = httpClient.execute(httpRequest);
        } catch (SocketTimeoutException e) {
            // no response should catch this exception
            logger.error("no response", e);
            return null;
        } catch (IOException e) {
            // unable to connect should catch this:
            //		org.apache.http.conn.HttpHostConnectException
            //		caused by: java.net.ConnectException
            throw new Exception("error sending " + httpRequest.getMethod() + " to " + httpRequest.getURI(), e);
        }

        String respStr = null;
        HttpEntity respEntity = response.getEntity();
        if (respEntity == null) {
            logger.debug("resp is null");
        } else {
            logger.debug("Response content length: " + respEntity.getContentLength());
            respStr = new String(EntityUtils.toByteArray(respEntity));
            logger.trace("resp data=" + respStr);
        }

        int statusCode=response.getStatusLine().getStatusCode();
        logger.trace("http statusCode=" + statusCode);
        if (statusCode != 200) {
            // if http status code is not 200, just throw exception
            throw new DcpHttpException("error sending " + httpRequest.getMethod() + " to " + httpRequest.getURI() + ": http status code=" + statusCode, statusCode);
        }

        return respStr;
    }

}
