spring.profiles.active=prod

server.port=8080
#h2 in-memory database
#spring.datasource.url=jdbc:h2:mem:testdb
#spring.datasource.driverClassName=org.h2.Driver
#spring.datasource.username=test
#spring.datasource.password=test
#spring.datasource.url=********************************************************************************************************************************************************************************* = ccuat2.db.cdgtaxi.com.sg)))
spring.datasource.url=********************************************************************************************************************************************************************************* = cn2uat.aix61.db.cdgtaxi.com.sg)))
spring.datasource.driver-class-name=oracle.jdbc.OracleDriver
spring.datasource.username: cn2tmapps
spring.datasource.password: cn2tmuatapps
spring.datasource.hikari.connection-timeout=3000
spring.datasource.hikari.maximum-pool-size=100

krispay.maxrows=20

#logging
logging.level.com.cdg=debug
logging.pattern.console="%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n"
logging.file.name=log/app.log
logging.logback.rollingpolicy.max-history=30
logging.level.reactor.netty.http.client=debug

krispay.url=https://apigw.singaporeair.com/krisplus/v1/partners/
krispay.api-key=nqgnu4p97q5tugd5tkwf5g6z
krispay.secret=sa9HMXgvEj
krispay.response.timeout=10
krispay.connection.timeout=5000
krispay.schedule=30000
#h5 DCP
krispay.h5dcpBaseUrl=http://***********:2000/dcp-payment/rest/v3/h5Payment


#proxy
krispay.proxyHost=
krispay.proxyPort=

