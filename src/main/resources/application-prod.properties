server.port=8091

spring.batch.job.enabled=false

spring.datasource.url=***************************************************************************************************************************************************************************************************************)))
spring.datasource.driver-class-name=oracle.jdbc.OracleDriver
spring.datasource.username: cn2tmapps
spring.datasource.password: cn2tmpro3
spring.datasource.hikari.connection-timeout=3000
spring.datasource.hikari.maximum-pool-size=100
#spring.batch.jdbc.initialize-schema=always

spring.jpa.database-platform=org.hibernate.dialect.Oracle10gDialect
spring.jpa.hibernate.use-new-id-generator-mappings=false
spring.jpa.show-sql=true

#logging
logging.level.com.cdg=debug
logging.pattern.console="%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n"
logging.file.name=log/app.log
logging.level.reactor.netty.http.client=debug


krispay.url=https://apigw.singaporeair.com/krisplus/v1/partners/
krispay.api-key=nqgnu4p97q5tugd5tkwf5g6z
krispay.secret=sa9HMXgvEj
krispay.response.timeout=10
krispay.connection.timeout=5000

krispay.h5dcpBaseUrl=http://***********:2000/dcp-payment/rest/v3/h5Payment
krispay.maxrows=50

#set schedule every 15 mins
krispay.schedule=900000
krispay.proxyHost=***********
krispay.proxyPort=80
