server.port=8091

spring.batch.job.enabled=false

#h2 in-memory database
#spring.datasource.url=jdbc:h2:mem:testdb
#spring.datasource.driverClassName=org.h2.Driver
#spring.datasource.username=sa
#spring.datasource.password=password
#spring.datasource.url=********************************************************************************************************************************************************************************* = cn2dev.aix61.db.cdgtaxi.com.sg)))
spring.datasource.url=********************************************************************************************************************************************************************************* = cn2uat.aix61.db.cdgtaxi.com.sg)))
spring.datasource.driver-class-name=oracle.jdbc.OracleDriver
spring.datasource.username: cn2tmapps
spring.datasource.password: cn2tmuatapps
spring.datasource.hikari.connection-timeout=3000
spring.datasource.hikari.maximum-pool-size=100
#spring.batch.jdbc.initialize-schema=always

spring.jpa.database-platform=org.hibernate.dialect.Oracle10gDialect
spring.jpa.hibernate.use-new-id-generator-mappings=false
spring.jpa.show-sql=true

#logging
logging.level.com.cdg=debug
logging.pattern.console="%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n"
logging.file.name=log/app.log
logging.level.reactor.netty.http.client=debug


krispay.url=https://apigw.singaporeair.com/krisplus-dev/v1/partners/
krispay.api-key=zr7sgd964j279q83f6tda5hy
krispay.secret=bkFzzsYDBj
krispay.response.timeout=10
krispay.connection.timeout=5000

krispay.h5dcpBaseUrl=http://**********:2000/dcp-payment/rest/v3/h5Payment
krispay.maxrows=20

#set schedule every 15 mins
krispay.schedule=900000
krispay.proxyHost=************
krispay.proxyPort=80
