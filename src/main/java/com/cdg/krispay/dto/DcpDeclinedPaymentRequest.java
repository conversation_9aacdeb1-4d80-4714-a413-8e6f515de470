package com.cdg.krispay.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DcpDeclinedPaymentRequest {
	@JsonProperty("job-no")
    private String jobNumber;
	
	@JsonProperty("requested-amount")
    private double requestedAmt;
	
	@JsonProperty("fare-amount")
    private double fareAmt;
	
	@JsonProperty("gst-amount")
    private double gstAmt;
	
    @JsonProperty("admin-fee")
    private double adminFee;
    
    @JsonProperty("payment-mode")
    private int paymentMode;
    
    @JsonProperty("response-code")
    private String responseCode;
    
	public DcpDeclinedPaymentRequest(String jobNumber, double requestedAmt, double fareAmt, double gstAmt,
			double adminFee, int paymentMode, String responseCode) {
		super();
		this.jobNumber = jobNumber;
		this.requestedAmt = requestedAmt;
		this.fareAmt = fareAmt;
		this.gstAmt = gstAmt;
		this.adminFee = adminFee;
		this.paymentMode = paymentMode;
		this.responseCode = responseCode;
	}
    
    
    
}
