package com.cdg.krispay.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CaptureResponse {
    String partnerOrderId;
    String transactionId;
    String paymentStatus;
    String paymentAt;
    String status;
    String code;
    String message;
    CaptureData data;
    
    @Data
	public class CaptureData {
    	String partnerOrderId;
    	String paymentAt;
    	String transactionId;
    }
}
