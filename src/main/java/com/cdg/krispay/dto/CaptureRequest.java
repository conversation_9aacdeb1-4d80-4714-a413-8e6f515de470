package com.cdg.krispay.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CaptureRequest {
    String partnerOrderId;
    Payment payment;
    String orderMetaData;

    public CaptureRequest(String partnerOrderId, String orderMetaData, double amount) {
        this.orderMetaData = orderMetaData;
        this.partnerOrderId = partnerOrderId;
        this.payment = new Payment(amount);
    }
}
