package com.cdg.krispay.dto;

import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@Entity
@EntityListeners(AuditingEntityListener.class)
@Table(name = "TMTB_KRIS_PROCESSOR_TXN_LOG", schema = "cn2tmsys")
public class KrisPayProcessorTransactionLog {

    @Id
    @GeneratedValue(generator = "SEQGEN_ADMIN_USER")
    @SequenceGenerator(name = "SEQGEN_ADMIN_USER", schema = "cn2tmsys", sequenceName = "cn2tmsys.TMSQ_KRIS_PROCESSOR_TXN_LOG")
    @Column(name = "id")
    Long Id;

    @CreatedDate
    private Date createDt;

    @Enumerated(EnumType.STRING)
    KrisPayMessageType msgType;
    @Enumerated(EnumType.STRING)
    KrisPayRequestStatus requestStatus;

    String partnerOrderId;
    String sessionId;
    LocalDateTime createdAt;
    LocalDateTime orderExpiry;
    String request;
    String message;
    String code;
    Long txnId;
    String response;
    String responseStatus;
    String paymentStatus;
    LocalDateTime paymentAt;
    String krisTxnId;

    public KrisPayProcessorTransactionLog(KrisPayMessageType msgType) {
        this.msgType = msgType;
    }

}




