package com.cdg.krispay.dto;

public enum ResponseStatus {

    SUCCESS("SUCCESS"),
    FAILED("FAILED"),
    DECLINED("DECLINE"),
	ERROR("ERROR"),
	UNPAID("<PERSON><PERSON><PERSON>"),
	AUTHORIZED("AU<PERSON><PERSON>IZ<PERSON>"),
	CAPTURED("CAPTURED"),
	CANC<PERSON>LED("CANCELLED"),
	ERR_HOST_UNAVILABLE("ERR_HOST_UNAVILABLE"),
	ERR_HOST_TIMEOUT("ERR_HOST_TIMEOUT"),
	ERR_HOST_ERROR("ERR_HOST_ERROR"),
	ERR_VALIDATION("ERR_VALIDATION"),
	ERR_ORDER_NOT_FOUND("ERR_ORDER_NOT_FOUND"),
	ERR_DUPLICATE_ORDER("ERR_DUPLICATE_ORDER"),
	ERR_ALREADY_CAPTURE("ERR_ALREADY_CAPTURE");
    public final String type;

    private ResponseStatus(String type) {
        this.type = type;
    }

}
