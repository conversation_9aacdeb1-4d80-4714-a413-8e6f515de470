package com.cdg.krispay.dto;

import lombok.Data;

import java.util.Date;

import javax.persistence.*;
@Data
@Entity
@Table(name = "TMTB_KRIS_TXN", schema = "cn2tmsys")
public class KrisPayTransaction {

    @javax.persistence.Id
//    @GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_ADMIN_USER")
//    @SequenceGenerator(name = "SEQGEN_ADMIN_USER", sequenceName = "TMSQ_KRIS_TXN")
    @GeneratedValue(generator="TMSQ_KRIS_TXN")
    @SequenceGenerator(name="TMSQ_KRIS_TXN", schema = "cn2tmsys", sequenceName="cn2tmsys.TMSQ_KRIS_TXN")
    @Column(name = "id")
    Long Id;

    @Enumerated(EnumType.STRING)
    KrisPayMessageType krisPayMessageType;

    String idempotencyKey;

    String bookingRef;
    String requestId;
    String sessionId;
    double preAuthAmount;
    double captureAmount;
    String platform;
    String status;
    Date orderExpiry;
    Date createdAt;
    String cancelReason;
    String jobNumber;
    String entryMode;
    String vehicleId;
    String vehicleNumber;
    String vehicleModel;
    String driverId;
    String driverHp;
    String entity;
    double adminAmount;
    double gstAmount;
    double fareAmount;
    String captureRequest = "N";
    int countCapture;
    String eta;
    String errorCode;
    
    public KrisPayTransaction() {
		// TODO Auto-generated constructor stub
	}
    
	public KrisPayTransaction(KrisPayMessageType krisPayMessageType, String idempotencyKey, String bookingRef,
			String requestId, String sessionId, double preAuthAmount, String status, Date createdAt) {
		this.krisPayMessageType = krisPayMessageType;
		this.idempotencyKey = idempotencyKey;
		this.bookingRef = bookingRef;
		this.requestId = requestId;
		this.sessionId = sessionId;
		this.preAuthAmount = preAuthAmount;
		this.status = status;
		this.createdAt = createdAt;
	}
    
    
}
