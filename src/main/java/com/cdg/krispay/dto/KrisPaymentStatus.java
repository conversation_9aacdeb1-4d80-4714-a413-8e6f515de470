package com.cdg.krispay.dto;

import lombok.Data;

import java.util.Date;

import javax.persistence.*;

import org.hibernate.annotations.CreationTimestamp;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
@Data
@Entity
@EntityListeners(AuditingEntityListener.class)
@Table(name = "TMTB_KRIS_PAYMENT_STATUS", schema = "cn2tmsys")
public class KrisPaymentStatus {

    @javax.persistence.Id
//    @GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQGEN_ADMIN_USER")
//    @SequenceGenerator(name = "SEQGEN_ADMIN_USER", sequenceName = "TMSQ_KRIS_TXN")
    @GeneratedValue(generator="TMSQ_KRIS_PAYMENT_STATUS")
    @SequenceGenerator(name="TMSQ_KRIS_PAYMENT_STATUS", schema = "cn2tmsys", sequenceName="cn2tmsys.TMSQ_KRIS_PAYMENT_STATUS")
    @Column(name = "id")
    Long Id;

    double requestedAmt;
    
    @CreatedDate
    @Column(name = "CREATED_DATE")
    Date createdDate;
    
    @LastModifiedDate
    @Column(name = "UPDATED_DATE")
    Date updatedDate;
    
    String jobNumber;
    double adminFee;
    double gstAmt;
    double fareAmt;
    String retry = "N";
    int countRequest;
    String siaResponseCode;
    
    @Column(name="H5_RESPONSE_CODE")
    String h5ResponseCode;
    String message;
    
    public KrisPaymentStatus() {
		// TODO Auto-generated constructor stub
	}

    public KrisPaymentStatus(String jobNumber, double requestedAmt, double fareAmt, double gstAmt,
			double adminFee, String responseCode) {
		this.jobNumber = jobNumber;
		this.requestedAmt = requestedAmt;
		this.fareAmt = fareAmt;
		this.gstAmt = gstAmt;
		this.adminFee = adminFee;
		this.siaResponseCode = responseCode;
	}
    
    
    
}
