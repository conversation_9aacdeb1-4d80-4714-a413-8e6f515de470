package com.cdg.krispay.repo;


import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.cdg.krispay.dto.KrisPaymentStatus;

@Repository
public interface PaymentStatusRepo extends JpaRepository<KrisPaymentStatus, Long> {

	@Query(" FROM KrisPaymentStatus WHERE retry = 'Y' AND createdDate >= TRUNC(SYSDATE - 7) ")
    List<KrisPaymentStatus> findRetryPaymentStatus();
}
