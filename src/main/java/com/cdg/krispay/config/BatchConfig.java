package com.cdg.krispay.config;

import java.util.Date;

import javax.sql.DataSource;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.BatchConfigurer;
import org.springframework.batch.core.configuration.annotation.DefaultBatchConfigurer;
import org.springframework.batch.core.configuration.annotation.EnableBatchProcessing;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.explore.JobExplorer;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.launch.support.SimpleJobLauncher;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.repository.support.JobRepositoryFactoryBean;
import org.springframework.batch.core.repository.support.MapJobRepositoryFactoryBean;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.batch.support.transaction.ResourcelessTransactionManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import com.cdg.krispay.batch.CaptureReader;
import com.cdg.krispay.batch.CaptureWriter;
import com.cdg.krispay.batch.PaymentStatusReader;
import com.cdg.krispay.batch.PaymentStatusWriter;
import com.cdg.krispay.batch.RetryPaymentStatus;
import com.cdg.krispay.dto.KrisPayTransaction;
import com.cdg.krispay.dto.KrisPaymentStatus;

@Configuration
@EnableBatchProcessing
@EnableScheduling
@EnableJpaAuditing
//@EnableTransactionManagement
//public class BatchConfig {
public class BatchConfig extends DefaultBatchConfigurer {

	@Autowired
	private JobBuilderFactory jobs;

	@Autowired
	private StepBuilderFactory steps;

	@Autowired
	private CaptureReader captureReader;

	@Autowired
	private CaptureWriter captureWriter;
	
	@Autowired
	private PaymentStatusReader paymentStatusReader;
	
	@Autowired
	private PaymentStatusWriter paymentStatusWriter;

	@Autowired
	JobLauncher jobLauncher;

	@Value("${krispay.schedule}")
	String schedule;

	@Autowired
	private DataSource dataSource;

	@Override
    public void setDataSource(DataSource dataSource) {
        
    }
	
//	@Bean
//    public DataSource getDataSource()
//    {
//        DataSourceBuilder dataSourceBuilder = DataSourceBuilder.create();
//        dataSourceBuilder.driverClassName("org.h2.Driver");
//        dataSourceBuilder.url("jdbc:h2:file:C:/temp/test");
//        dataSourceBuilder.username("sa");
//        dataSourceBuilder.password("");
//        return dataSourceBuilder.build();
//    }
	
//
	@Bean
	public JpaTransactionManager jpaTransactionManager() {
		final JpaTransactionManager transactionManager = new JpaTransactionManager();
		System.out.println("datasource:::: " + dataSource);
		transactionManager.setDataSource(dataSource);
		return transactionManager;
	}
	
//	@Override
//	public PlatformTransactionManager getTransactionManager() {
//		return jpaTransactionManager();
//	}

//	@Bean
//	public BatchConfigurer batchConfigurer() {
//		return new DefaultBatchConfigurer() {
//			@Override
//			public PlatformTransactionManager getTransactionManager() {
//				return jpaTransactionManager();
//			}
//			@Override
//			public JobRepository getJobRepository() {
////				JobRepositoryFactoryBean factory = new JobRepositoryFactoryBean();
////				factory.setDataSource(dataSource);
////		        factory.setTransactionManager(getTransactionManager());
////		        try {
////					factory.afterPropertiesSet();
////					System.out.println("Factory::: " + factory);
////					return factory.getObject(); 
////				} catch (Exception e) {
////					// TODO Auto-generated catch block
////					e.printStackTrace();
////				}
////				return null;
//				MapJobRepositoryFactoryBean factoryBean = new MapJobRepositoryFactoryBean();
//		        try {
//					factoryBean.afterPropertiesSet();
//				} catch (Exception e) {
//					// TODO Auto-generated catch block
//					e.printStackTrace();
//				}
//		        try {
//					return factoryBean.getObject();
//				} catch (Exception e) {
//					// TODO Auto-generated catch block
//					e.printStackTrace();
//				}
//				return null;
//			}
//		};
//	}

	@Bean
	public Step stepOne() {
		return steps.get("step_send_capture_request").<KrisPayTransaction, KrisPayTransaction>chunk(1)
				.reader(captureReader)
				.writer(captureWriter)
				.transactionManager(jpaTransactionManager())
				.build();

	}

	@Bean
	public Step stepTwo() {
//		return steps.get("stepTwo").tasklet(new RetryPaymentStatus())
//				.transactionManager(jpaTransactionManager2())
//				.build();
		
		return steps.get("step_retry_payment_status").<KrisPaymentStatus, KrisPaymentStatus>chunk(1)
				.reader(paymentStatusReader)
				.writer(paymentStatusWriter)
				.transactionManager(jpaTransactionManager())
				.build();
	}

	@Bean
	public Job demoJob() {
//		return jobs.get("demoJob").start(stepOne()).next(stepTwo()).build();
//		return jobs.get("demoJob").incrementer(new RunIdIncrementer()).start(stepTwo()).build();
//		return jobs.get("demoJob").incrementer(new RunIdIncrementer()).start(stepTwo()).next(stepOne()).build();
		return jobs.get("krisbatch").incrementer(new RunIdIncrementer()).start(stepOne()).next(stepTwo()).build();
	}

//	@Bean
//	public SimpleJobLauncher jobLauncher(JobRepository jobRepository) {
//		SimpleJobLauncher launcher = new SimpleJobLauncher();
//		launcher.setJobRepository(jobRepository);
//		return launcher;
//	}

	@Scheduled(fixedRateString = "${krispay.schedule}")
	public void perform() throws Exception {
		System.out.println("---------------------------------------\n--------------------------------");
		int interval = Integer.valueOf(schedule)/60000;
		System.out.println("Start batch schedule with interval: " + interval + " mins" );
		Date date = new Date();
		JobExecution jobExecution = jobLauncher.run(demoJob(),
				new JobParametersBuilder().addDate("launchDate", date).toJobParameters());
	}

}