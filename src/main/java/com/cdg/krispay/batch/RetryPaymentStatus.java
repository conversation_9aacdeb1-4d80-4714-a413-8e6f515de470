package com.cdg.krispay.batch;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

import javax.net.ssl.SSLException;

import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.StepExecutionListener;
import org.springframework.batch.core.annotation.BeforeStep;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.ClientRequest;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.WebClient;

import com.cdg.krispay.dto.KrisPaymentStatus;
import com.cdg.krispay.repo.PaymentStatusRepo;
import com.cdg.krispay.repo.ProcessorTxnLogRepo;
import com.cdg.krispay.repo.TxnLogRepo;

import io.netty.channel.ChannelOption;
import io.netty.handler.logging.LogLevel;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.netty.http.client.HttpClient;
import reactor.netty.transport.ProxyProvider;
import reactor.netty.transport.logging.AdvancedByteBufFormat;

@Slf4j
@Component
public class RetryPaymentStatus implements Tasklet, StepExecutionListener {
	WebClient h5dcpWebClient;

	@Value("${krispay.h5dcpBaseUrl}")
	private String h5dcpBaseUrl;

	@Value("${krispay.response.timeout}")
	int responseTimeout;

	@Value("${krispay.connection.timeout}")
	int connectionTimeout;

	@Value("${krispay.proxyHost}")
	String proxyHost;

	@Value("${krispay.proxyPort}")
	String proxyPort;

	@Autowired
	ProcessorTxnLogRepo processorTxnLogRepo;
	
	@Autowired
	TxnLogRepo txnLogRepo;
	
	@Autowired
	PaymentStatusRepo paymentStatusRepo;

	@Override
	public void beforeStep(StepExecution stepExecution) {
		log.info("-------RetryPaymentStatus beforeStep-------");

		HttpClient httpClient = HttpClient.create().option(ChannelOption.CONNECT_TIMEOUT_MILLIS, connectionTimeout)
				.responseTimeout(Duration.ofSeconds(responseTimeout))
				.wiretap("reactor.netty.http.client.HttpClient", LogLevel.DEBUG, AdvancedByteBufFormat.TEXTUAL);

		if (!StringUtils.isEmpty(proxyHost) && !StringUtils.isEmpty(proxyPort)) {
			httpClient.proxy(
					proxy -> proxy.type(ProxyProvider.Proxy.HTTP).host(proxyHost).port(Integer.parseInt(proxyPort)));
		}

		// init H5 DCP webclient
		this.h5dcpWebClient = WebClient.builder().baseUrl(h5dcpBaseUrl)
				.filter(ExchangeFilterFunction
						.ofRequestProcessor(request -> Mono.just(ClientRequest.from(request).build())))
				.clientConnector(new ReactorClientHttpConnector(httpClient)).build();

	}

	public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
		System.out.println("RetryPaymentStatus start..");
		try {
			List<KrisPaymentStatus> paymentStatusList = paymentStatusRepo.findRetryPaymentStatus();
			System.out.println(1111);
			System.out.println(paymentStatusList.size());
			paymentStatusList.stream().forEach(System.out::println);
			System.out.println("RetryPaymentStatus done..");
		} catch (Exception e) {
			// TODO: handle exception
			log.error("RetryPaymentStatus execute error :: " + e);
		}
		return RepeatStatus.FINISHED;
	}

	@Override
	public ExitStatus afterStep(StepExecution stepExecution) {
		// TODO Auto-generated method stub
		return null;
	}
}
