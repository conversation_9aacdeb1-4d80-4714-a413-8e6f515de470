package com.cdg.krispay.batch;

import java.io.InputStream;
import java.nio.ByteBuffer;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Properties;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import javax.net.ssl.SSLException;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.annotation.BeforeStep;
import org.springframework.batch.core.annotation.BeforeWrite;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.item.ItemWriter;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ComponentScans;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.ClientRequest;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.ExchangeFilterFunctions;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;

import com.cdg.krispay.dto.CaptureRequest;
import com.cdg.krispay.dto.CaptureResponse;
import com.cdg.krispay.dto.DcpDeclinedPaymentRequest;
import com.cdg.krispay.dto.DcpDeclinedPaymentResponse;
import com.cdg.krispay.dto.KrisPayMessageType;
import com.cdg.krispay.dto.KrisPayProcessorTransactionLog;
import com.cdg.krispay.dto.KrisPayRequestStatus;
import com.cdg.krispay.dto.KrisPayTransaction;
import com.cdg.krispay.dto.KrisPaymentStatus;
import com.cdg.krispay.dto.ResponseStatus;
import com.cdg.krispay.exception.ServiceException;
import com.cdg.krispay.repo.PaymentStatusRepo;
import com.cdg.krispay.repo.ProcessorTxnLogRepo;
import com.cdg.krispay.repo.TxnLogRepo;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import io.netty.channel.ChannelOption;
import io.netty.handler.logging.LogLevel;
import io.netty.handler.ssl.SslContext;
import io.netty.handler.ssl.SslContextBuilder;
import io.netty.handler.ssl.util.InsecureTrustManagerFactory;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.netty.http.client.HttpClient;
import reactor.netty.resources.ConnectionProvider;
import reactor.netty.transport.ProxyProvider;
import reactor.netty.transport.logging.AdvancedByteBufFormat;
import reactor.util.retry.Retry;

@Slf4j
@Component
public class CaptureWriter implements ItemWriter<KrisPayTransaction> {

	WebClient webClient;
	
	WebClient h5dcpWebClient;

	@Value("${krispay.url}")
	private String baseUrl;

	@Value("${krispay.h5dcpBaseUrl}")
	private String h5dcpBaseUrl;

	@Value("${krispay.api-key}")
	private String apiKey;

	@Value("${krispay.secret}")
	private String secret;

	@Value("${krispay.response.timeout}")
	int responseTimeout;
	
	@Value("${krispay.connection.timeout}")
	int connectionTimeout;
	
	@Value("${krispay.proxyHost}")
	String proxyHost;
	
	@Value("${krispay.proxyPort}")
	String proxyPort;
	
	@Autowired
	ProcessorTxnLogRepo processorTxnLogRepo;
	
	@Autowired
	TxnLogRepo txnLogRepo;
	
	@Autowired
	PaymentStatusRepo paymentStatusRepo;

	public String getSignature() {
		Long timestamp = System.currentTimeMillis() / 1000;
		String hashData = apiKey + secret + timestamp;
		String signature = DigestUtils.sha256Hex(hashData);

		return signature;
	}


	@BeforeStep
	public void beforeStep() throws SSLException {
		log.info("-------before Step 1-------");
		
		//init SIA webclient
		log.info("secret:" + secret);
		log.info("proxyHost:" + proxyHost);
		log.info("connectionTimeout:" + connectionTimeout);
		HttpClient httpClient;
		HttpClient httpClient2;
		
		if(!StringUtils.isEmpty(proxyHost) && !StringUtils.isEmpty(proxyPort)) {
			httpClient = HttpClient.create()
					.option(ChannelOption.CONNECT_TIMEOUT_MILLIS, connectionTimeout)
					.responseTimeout(Duration.ofSeconds(responseTimeout))
					.wiretap("reactor.netty.http.client.HttpClient", LogLevel.DEBUG, AdvancedByteBufFormat.TEXTUAL)
					.proxy(proxy -> proxy.type(ProxyProvider.Proxy.HTTP).host(proxyHost).port(Integer.parseInt(proxyPort)));
			
			httpClient2 = HttpClient.create()
					.option(ChannelOption.CONNECT_TIMEOUT_MILLIS, connectionTimeout)
					.responseTimeout(Duration.ofSeconds(responseTimeout))
					.wiretap("reactor.netty.http.client.HttpClient", LogLevel.DEBUG, AdvancedByteBufFormat.TEXTUAL);
//					.proxy(proxy -> proxy.type(ProxyProvider.Proxy.HTTP).host(proxyHost).port(Integer.parseInt(proxyPort)));
		} else {
			httpClient = HttpClient.create()
					.option(ChannelOption.CONNECT_TIMEOUT_MILLIS, connectionTimeout)
					.responseTimeout(Duration.ofSeconds(responseTimeout))
					.wiretap("reactor.netty.http.client.HttpClient", LogLevel.DEBUG, AdvancedByteBufFormat.TEXTUAL);
			
			httpClient2 = HttpClient.create()
					.option(ChannelOption.CONNECT_TIMEOUT_MILLIS, connectionTimeout)
					.responseTimeout(Duration.ofSeconds(responseTimeout))
					.wiretap("reactor.netty.http.client.HttpClient", LogLevel.DEBUG, AdvancedByteBufFormat.TEXTUAL);
		}
		
		this.webClient = WebClient.builder().baseUrl(baseUrl)
				.filter(ExchangeFilterFunction.ofRequestProcessor(
						request -> Mono.just(ClientRequest.from(request).header("api-key", apiKey).build())))
				.clientConnector(new ReactorClientHttpConnector(httpClient)).build();
		
		
		//init H5 DCP webclient
		this.h5dcpWebClient = WebClient.builder().baseUrl(h5dcpBaseUrl)
				.filter(ExchangeFilterFunction.ofRequestProcessor(
						request -> Mono.just(ClientRequest.from(request).build())))
				.clientConnector(new ReactorClientHttpConnector(httpClient2)).build();
		

	}

	@Override
	public void write(List<? extends KrisPayTransaction> tranxList) throws Exception {
		// TODO Auto-generated method stub
		log.info("CaptureWriter start... tranxList size()=" + tranxList.size());

		for (int i = 0; i < tranxList.size(); i++) {
			KrisPayProcessorTransactionLog krisPayProcessorTransactionlog = new KrisPayProcessorTransactionLog(KrisPayMessageType.CAPTURE);
			KrisPayTransaction tranx = tranxList.get(i);
			KrisPayTransaction krisPayTransaction = null;
			CaptureResponse response = null;
			try {
				
				krisPayTransaction = txnLogRepo.findById(tranx.getId()).get();
				
				//if KrisPay tranx already capture then ignore
				if(krisPayTransaction.getCaptureRequest().equals("Y")) {
					continue;
				}
				
				log.info("CaptureWriter--" + krisPayTransaction);
				String signature = getSignature();
				String kpRequest = UUID.randomUUID().toString();

				krisPayProcessorTransactionlog.setPartnerOrderId(krisPayTransaction.getBookingRef());
				krisPayProcessorTransactionlog.setSessionId(krisPayTransaction.getSessionId());
				krisPayProcessorTransactionlog.setTxnId(krisPayTransaction.getId());
				
				processorTxnLogRepo.save(krisPayProcessorTransactionlog);
				
				StringBuilder orderMetaData = new StringBuilder();
				orderMetaData.append(krisPayTransaction.getJobNumber()).append("|");
				orderMetaData.append(krisPayTransaction.getEntryMode()).append("|");
				orderMetaData.append(krisPayTransaction.getVehicleId()).append("|");
				orderMetaData.append(krisPayTransaction.getDriverId()).append("|");
				orderMetaData.append(krisPayTransaction.getEntity()).append("|");
				orderMetaData.append(krisPayTransaction.getAdminAmount()).append("|");
				orderMetaData.append(krisPayTransaction.getGstAmount()).append("|");
				orderMetaData.append(krisPayTransaction.getFareAmount());
				
				krisPayTransaction.setCountCapture(krisPayTransaction.getCountCapture() + 1);

				CaptureRequest createOrder = new CaptureRequest(krisPayTransaction.getBookingRef(),
						orderMetaData.toString(), krisPayTransaction.getCaptureAmount());
				krisPayProcessorTransactionlog.setRequest(createOrder.toString());

				response = this.webClient.put()
						.uri("/orders/{orderId}/capture", krisPayTransaction.getBookingRef())
						.header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
						.header("x-signature", signature)
						.header("kp-request-id", kpRequest)
						.body(Mono.just(createOrder), CaptureRequest.class).retrieve()
						.onStatus(HttpStatus::is5xxServerError, resp -> {
							log.error("ServerError {}", resp.statusCode());
							return Mono.error(new ServiceException("Server error", resp.rawStatusCode()));
						}).onStatus(HttpStatus::is4xxClientError, resp -> {
							log.error("ClientError {}", resp.statusCode());
							return Mono.error(new ServiceException("Client error", resp.rawStatusCode()));
						}).bodyToMono(CaptureResponse.class)
//						.retryWhen(Retry.backoff(3, Duration.ofSeconds(2)).jitter(0.75))
						.block();

				if (response == null) {
					log.error("CaptureResponse null");
					krisPayProcessorTransactionlog.setRequestStatus(KrisPayRequestStatus.ERROR);
				} else {
					log.info("CaptureWriter-response:: " + response.toString());
					krisPayProcessorTransactionlog.setRequestStatus(KrisPayRequestStatus.SUCCESS);
					krisPayProcessorTransactionlog.setResponseStatus(response.getStatus());
					
					ResponseStatus responseStatus = ResponseStatus.valueOf(response.getStatus().toUpperCase());
					
					if(responseStatus == ResponseStatus.SUCCESS) {
						krisPayTransaction.setCaptureRequest("Y");
						krisPayProcessorTransactionlog.setPaymentAt(LocalDateTime.parse(response.getData().getPaymentAt(), DateTimeFormatter.ISO_DATE_TIME));
						krisPayProcessorTransactionlog.setKrisTxnId(response.getData().getTransactionId());
						if(!StringUtils.isEmpty(krisPayTransaction.getErrorCode())) {
							krisPayTransaction.setErrorCode("");
						}
					} else {
						krisPayProcessorTransactionlog.setCode(response.getCode());
						krisPayProcessorTransactionlog.setMessage(response.getMessage());
						krisPayTransaction.setErrorCode(response.getCode());
					}
				}
				
			} catch (ServiceException e) {
				krisPayProcessorTransactionlog.setRequestStatus(KrisPayRequestStatus.ERROR);
				krisPayProcessorTransactionlog.setResponse(e.getMessage() + ": " + e.getStatusCode());
				log.error("ServiceException", e);
				// TODO: handle exception
			} catch (Exception e) {
				// TODO: handle exception
				log.error("Exception:: " + e.toString());
				krisPayProcessorTransactionlog.setRequestStatus(KrisPayRequestStatus.ERROR);
				krisPayProcessorTransactionlog.setResponse(e.getMessage());
			} finally {
				processorTxnLogRepo.save(krisPayProcessorTransactionlog);
				txnLogRepo.save(krisPayTransaction);
				log.debug("saving krisPayProcessorTransactionlog & krisPayTransaction done");
			}
			
			log.info("----------Send update reponse from SIA to h5 DCP------------");
			updatePaymentStatusToH5dcp(krisPayTransaction, response);
		}

		log.info("CaptureWriter done..");
	}

	public void updatePaymentStatusToH5dcp(KrisPayTransaction krisPayTransaction, CaptureResponse response) {
		KrisPaymentStatus paymentStatus = null;
		try {
//			response = new CaptureResponse();
//			response.setStatus("SUCCESS");
//			response.setCode("KPPR001");
			if (response != null) {
				ResponseStatus responseStatus = ResponseStatus.valueOf(response.getStatus().toUpperCase());
				String responseCode;
				if (responseStatus == ResponseStatus.SUCCESS) {
					responseCode = "00";
				} else {
					responseCode = response.getCode();
					log.info("updatePaymentStatusToH5dcp:: Response code from SIA - " + responseCode);
					if (responseCode.equals("KPPR005")) {
						return;
					}
				}
				
				//insert KrisPaymentStatus
				paymentStatus = new KrisPaymentStatus(krisPayTransaction.getJobNumber(), krisPayTransaction.getCaptureAmount(), krisPayTransaction.getFareAmount(), krisPayTransaction.getGstAmount(),
						krisPayTransaction.getAdminAmount(), responseCode);
				paymentStatusRepo.save(paymentStatus);
				
				DcpDeclinedPaymentRequest paymentRequest = new DcpDeclinedPaymentRequest(krisPayTransaction.getJobNumber(), krisPayTransaction.getCaptureAmount(), krisPayTransaction.getFareAmount(), krisPayTransaction.getGstAmount(),
						krisPayTransaction.getAdminAmount(), 8, responseCode);
				
				log.info("Update payment status to h5 with responseCode: " + responseCode);
				log.info("DcpDeclinedPaymentRequest: " + paymentRequest.toString());
				ByteBuffer paymentResponseBuffer = this.h5dcpWebClient.post()
						.header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
						.body(Mono.just(paymentRequest), DcpDeclinedPaymentRequest.class).retrieve()
						.onStatus(HttpStatus::is5xxServerError, resp -> {
							log.error("ServerError {}", resp.statusCode());
							return Mono.error(new ServiceException("Server error", resp.rawStatusCode()));
						}).onStatus(HttpStatus::is4xxClientError, resp -> {
							log.error("ClientError {}", resp.statusCode());
							return Mono.error(new ServiceException("Client error", resp.rawStatusCode()));
						}).bodyToMono(ByteBuffer.class)
						.retryWhen(Retry.backoff(3, Duration.ofSeconds(2)).jitter(0.75))
						.block();
				
				if (paymentResponseBuffer != null) {
					
					ObjectMapper objectMapper = new ObjectMapper();
					DcpDeclinedPaymentResponse paymentResponse = objectMapper.readValue(paymentResponseBuffer.array(), DcpDeclinedPaymentResponse.class);
					log.info("paymentResponse:: " + paymentResponse);
					paymentStatus.setH5ResponseCode(String.valueOf(paymentResponse.getResponseCode()));
					paymentStatus.setMessage(paymentResponse.getMessage());
					
					//will retry payment status to h5 in next schedule
					if (!paymentResponse.getResponseCode().equals(0)) {
						paymentStatus.setRetry("Y");
					} 
				} else {
					log.error("Payment Response from h5 is empty");
					paymentStatus.setRetry("Y");
				}
			}
		} catch (ServiceException e) {
			log.error("ServiceException update Payment Status To H5dcp ", e);
			paymentStatus.setRetry("Y");
			// TODO: handle exception
		} catch (Exception e) {
			// TODO: handle exception
			log.error("Exception update Payment Status To H5dcp:: " + e.toString());
			paymentStatus.setRetry("Y");
		} finally {
			if (paymentStatus != null) {
				paymentStatusRepo.save(paymentStatus);
			}
		}
	}

}
