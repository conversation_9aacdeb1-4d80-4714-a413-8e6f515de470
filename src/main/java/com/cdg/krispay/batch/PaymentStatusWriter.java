package com.cdg.krispay.batch;

import java.io.InputStream;
import java.nio.ByteBuffer;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Properties;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import javax.net.ssl.SSLException;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.annotation.BeforeStep;
import org.springframework.batch.core.annotation.BeforeWrite;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.item.ItemWriter;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ComponentScans;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.ClientRequest;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.ExchangeFilterFunctions;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;

import com.cdg.krispay.dto.CaptureRequest;
import com.cdg.krispay.dto.CaptureResponse;
import com.cdg.krispay.dto.DcpDeclinedPaymentRequest;
import com.cdg.krispay.dto.DcpDeclinedPaymentResponse;
import com.cdg.krispay.dto.KrisPayMessageType;
import com.cdg.krispay.dto.KrisPayProcessorTransactionLog;
import com.cdg.krispay.dto.KrisPayRequestStatus;
import com.cdg.krispay.dto.KrisPayTransaction;
import com.cdg.krispay.dto.KrisPaymentStatus;
import com.cdg.krispay.dto.ResponseStatus;
import com.cdg.krispay.exception.ServiceException;
import com.cdg.krispay.repo.PaymentStatusRepo;
import com.cdg.krispay.repo.ProcessorTxnLogRepo;
import com.cdg.krispay.repo.TxnLogRepo;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import io.netty.channel.ChannelOption;
import io.netty.handler.logging.LogLevel;
import io.netty.handler.ssl.SslContext;
import io.netty.handler.ssl.SslContextBuilder;
import io.netty.handler.ssl.util.InsecureTrustManagerFactory;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.netty.http.client.HttpClient;
import reactor.netty.resources.ConnectionProvider;
import reactor.netty.transport.ProxyProvider;
import reactor.netty.transport.logging.AdvancedByteBufFormat;
import reactor.util.retry.Retry;

@Slf4j
@Component
public class PaymentStatusWriter implements ItemWriter<KrisPaymentStatus> {

	WebClient h5dcpWebClient;

	@Value("${krispay.url}")
	private String baseUrl;

	@Value("${krispay.h5dcpBaseUrl}")
	private String h5dcpBaseUrl;

	@Value("${krispay.api-key}")
	private String apiKey;

	@Value("${krispay.secret}")
	private String secret;

	@Value("${krispay.response.timeout}")
	int responseTimeout;
	
	@Value("${krispay.connection.timeout}")
	int connectionTimeout;
	
	@Value("${krispay.proxyHost}")
	String proxyHost;
	
	@Value("${krispay.proxyPort}")
	String proxyPort;
	
	@Autowired
	ProcessorTxnLogRepo processorTxnLogRepo;
	
	@Autowired
	TxnLogRepo txnLogRepo;
	
	@Autowired
	PaymentStatusRepo paymentStatusRepo;

	public String getSignature() {
		Long timestamp = System.currentTimeMillis() / 1000;
		String hashData = apiKey + secret + timestamp;
		String signature = DigestUtils.sha256Hex(hashData);

		return signature;
	}


	@BeforeStep
	public void beforeStep() throws SSLException {
		log.info("-------before Step 2-------");
		
		//init SIA webclient
		log.info("proxyHost:" + proxyHost);
		log.info("connectionTimeout:" + connectionTimeout);
		
		HttpClient httpClient2;
		
		if(!StringUtils.isEmpty(proxyHost) && !StringUtils.isEmpty(proxyPort)) {
			httpClient2 = HttpClient.create()
					.option(ChannelOption.CONNECT_TIMEOUT_MILLIS, connectionTimeout)
					.responseTimeout(Duration.ofSeconds(responseTimeout))
					.wiretap("reactor.netty.http.client.HttpClient", LogLevel.DEBUG, AdvancedByteBufFormat.TEXTUAL);
//					.proxy(proxy -> proxy.type(ProxyProvider.Proxy.HTTP).host(proxyHost).port(Integer.parseInt(proxyPort)));
		} else {
			httpClient2 = HttpClient.create()
					.option(ChannelOption.CONNECT_TIMEOUT_MILLIS, connectionTimeout)
					.responseTimeout(Duration.ofSeconds(responseTimeout))
					.wiretap("reactor.netty.http.client.HttpClient", LogLevel.DEBUG, AdvancedByteBufFormat.TEXTUAL);
		}
		
		//init H5 DCP webclient
		this.h5dcpWebClient = WebClient.builder().baseUrl(h5dcpBaseUrl)
				.filter(ExchangeFilterFunction.ofRequestProcessor(
						request -> Mono.just(ClientRequest.from(request).build())))
				.clientConnector(new ReactorClientHttpConnector(httpClient2)).build();
		

	}

	@Override
	public void write(List<? extends KrisPaymentStatus> tranxList) throws Exception {
		// TODO Auto-generated method stub
		log.info("PaymentStatusWriter start... tranxList size()=" + tranxList.size());

		for (int i = 0; i < tranxList.size(); i++) {
			KrisPaymentStatus paymentStatus = paymentStatusRepo.findById(tranxList.get(i).getId()).get();
			
			try {
				DcpDeclinedPaymentRequest paymentRequest = new DcpDeclinedPaymentRequest(paymentStatus.getJobNumber(), paymentStatus.getRequestedAmt(), paymentStatus.getFareAmt(), paymentStatus.getGstAmt(),
						paymentStatus.getAdminFee(), 8, paymentStatus.getSiaResponseCode());
				paymentStatus.setCountRequest(paymentStatus.getCountRequest()+1);
				
				log.info("Update payment status to h5 with responseCode: " + paymentStatus.getSiaResponseCode());
				log.info("DcpDeclinedPaymentRequest: " + paymentRequest.toString());
				ByteBuffer paymentResponseBuffer = this.h5dcpWebClient.post()
						.header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
						.body(Mono.just(paymentRequest), DcpDeclinedPaymentRequest.class).retrieve()
						.onStatus(HttpStatus::is5xxServerError, resp -> {
							log.error("ServerError {}", resp.statusCode());
							return Mono.error(new ServiceException("Server error", resp.rawStatusCode()));
						}).onStatus(HttpStatus::is4xxClientError, resp -> {
							log.error("ClientError {}", resp.statusCode());
							return Mono.error(new ServiceException("Client error", resp.rawStatusCode()));
						}).bodyToMono(ByteBuffer.class)
						.retryWhen(Retry.backoff(3, Duration.ofSeconds(2)).jitter(0.75))
						.block();
				
				if (paymentResponseBuffer != null) {
					
					ObjectMapper objectMapper = new ObjectMapper();
					DcpDeclinedPaymentResponse paymentResponse = objectMapper.readValue(paymentResponseBuffer.array(), DcpDeclinedPaymentResponse.class);
					log.info("paymentResponse:: " + paymentResponse);
					paymentStatus.setH5ResponseCode(String.valueOf(paymentResponse.getResponseCode()));
					paymentStatus.setMessage(paymentResponse.getMessage());
					
					//will retry payment status to h5 in next schedule
					if (paymentResponse.getResponseCode().equals(0)) {
						paymentStatus.setRetry("N");
					} 
				} else {
					log.error("Payment Response from h5 is empty");
				}
			} catch (ServiceException e) {
				log.error("ServiceException update Payment Status To H5dcp ", e);
				// TODO: handle exception
			} catch (Exception e) {
				// TODO: handle exception
				log.error("Exception update Payment Status To H5dcp:: " + e.toString());
			} finally {
				paymentStatusRepo.save(paymentStatus);
			}
			
		}

		log.info("PaymentStatusWriter done..");
	}

}
