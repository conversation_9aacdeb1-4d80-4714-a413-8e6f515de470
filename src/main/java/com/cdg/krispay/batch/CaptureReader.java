package com.cdg.krispay.batch;

import java.sql.ResultSet;
import java.sql.SQLException;

import javax.sql.DataSource;

import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.database.JdbcCursorItemReader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;

import com.cdg.krispay.dto.KrisPayTransaction;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class CaptureReader extends Jdbc<PERSON>ursorItemReader<KrisPayTransaction> implements ItemReader<KrisPayTransaction> {

	public CaptureReader(@Autowired DataSource dataSource, @Value("${krispay.maxrows:10}") int maxrows) {
		log.info("CaptureReader");
		setDataSource(dataSource);
		String sqlQuery = "SELECT ID, BOOKING_REF, SESSION_ID, JOB_NUMBER, ENTRY_MODE, VEHICLE_ID, DRIVER_ID, ENTITY, ADMIN_AMOUNT, GST_AMOUNT, COUNT_CAPTURE, "
//				+ "FARE_AMOUNT, CAPTURE_AMOUNT FROM TMTB_KRIS_TXN WHERE KRIS_PAY_MESSAGE_TYPE = 'CREATE_ORDER' "
				+ "FARE_AMOUNT, CAPTURE_AMOUNT FROM TMTB_KRIS_TXN WHERE KRIS_PAY_MESSAGE_TYPE = 'CAPTURE' "
				+ "AND CREATED_AT >= TRUNC(SYSDATE - 7) AND CAPTURE_REQUEST = 'N' AND (ERROR_CODE IS NULL OR ERROR_CODE = 'KPPR005') ORDER BY CREATED_AT DESC";
//		setSql("SELECT ID, BOOKING_REF, SESSION_ID, JOB_NUMBER, ENTRY_MODE, VEHICLE_ID, DRIVER_ID, ENTITY, ADMIN_AMOUNT, GST_AMOUNT, COUNT_CAPTURE,FARE_AMOUNT, CAPTURE_AMOUNT  FROM TMTB_KRIS_TXN");
		setSql(sqlQuery);
//		setFetchSize(fetchSize);
		setMaxRows(maxrows);
		setRowMapper(new KrisPayTransactionRowMapper());
	}
	
	public class KrisPayTransactionRowMapper implements RowMapper<KrisPayTransaction> {
		@Override
		public KrisPayTransaction mapRow(ResultSet rs, int rowNum) throws SQLException {
			KrisPayTransaction tranx  = new KrisPayTransaction();
			tranx.setId(rs.getLong("ID"));
			tranx.setBookingRef(rs.getString("BOOKING_REF"));
			tranx.setSessionId(rs.getString("SESSION_ID"));
			tranx.setJobNumber(rs.getString("JOB_NUMBER"));
			tranx.setEntryMode(rs.getString("ENTRY_MODE"));
			tranx.setVehicleId(rs.getString("VEHICLE_ID"));
			tranx.setDriverId(rs.getString("DRIVER_ID"));
			tranx.setEntity(rs.getString("ENTITY"));
			tranx.setAdminAmount(rs.getDouble("ADMIN_AMOUNT"));
			tranx.setGstAmount(rs.getDouble("GST_AMOUNT"));
			tranx.setCountCapture(rs.getInt("COUNT_CAPTURE"));
			tranx.setFareAmount(rs.getDouble("FARE_AMOUNT"));
			tranx.setCaptureAmount(rs.getDouble("CAPTURE_AMOUNT"));
			System.out.println("Clark test............................");
			System.out.println(tranx);
			return tranx;
		}
	}
	
//	@Override
//	public KrisPayTransaction read()
//			throws Exception, UnexpectedInputException, ParseException, NonTransientResourceException {
//		// TODO Auto-generated method stub
//		
//		JdbcCursorItemReader<KrisPayTransaction> reader = new JdbcCursorItemReader<>();
//		reader.setSql("SELECT id, firstName, lastName, birthdate FROM customer ORDER BY lastName, firstName");
////		reader.setDataSource(dataSource);
//		reader.setFetchSize(100);
////		reader.setRowMapper(new CustomerRowMapper());
//		
//		return reader;
//		
//		if (count < 10) {
//			log.info("\nCaptureReader-----count=" + count);
//			count++;
//			KrisPayTransaction krisPayTransaction = new KrisPayTransaction();
//			krisPayTransaction.setBookingRef("d7d242d5-a4ef-4ea0-ba81-f61e5d1a06f9");
//			krisPayTransaction.setCaptureAmount(new Double(3.5));
//			return krisPayTransaction;
//		}
//		return null;
//	}

}
