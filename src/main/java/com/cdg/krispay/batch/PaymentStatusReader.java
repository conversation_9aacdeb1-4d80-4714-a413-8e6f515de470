package com.cdg.krispay.batch;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.sql.DataSource;

import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.NonTransientResourceException;
import org.springframework.batch.item.ParseException;
import org.springframework.batch.item.UnexpectedInputException;
import org.springframework.batch.item.database.JdbcCursorItemReader;
import org.springframework.batch.item.database.support.ListPreparedStatementSetter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.ArgumentPreparedStatementSetter;
import org.springframework.jdbc.core.PreparedStatementSetter;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;

import com.cdg.krispay.dto.CaptureRequest;
import com.cdg.krispay.dto.KrisPayTransaction;
import com.cdg.krispay.dto.KrisPaymentStatus;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class PaymentStatusReader extends JdbcCursorItemReader<KrisPaymentStatus> implements ItemReader<KrisPaymentStatus> {

	public PaymentStatusReader(@Autowired DataSource dataSource, @Value("${krispay.maxrows:10}") int maxrows) {
		log.info("PaymentStatusReader");
		setDataSource(dataSource);
		String sqlQuery = "SELECT ID FROM TMTB_KRIS_PAYMENT_STATUS WHERE retry = 'Y' AND created_date >= TRUNC(SYSDATE - 7) ";
		setSql(sqlQuery);
//		setFetchSize(fetchSize);
		setMaxRows(maxrows);
		setRowMapper(new KrisPaymentStatusRowMapper());
	}
	
	public class KrisPaymentStatusRowMapper implements RowMapper<KrisPaymentStatus> {
		@Override
		public KrisPaymentStatus mapRow(ResultSet rs, int rowNum) throws SQLException {
			KrisPaymentStatus tranx  = new KrisPaymentStatus();
			tranx.setId(rs.getLong("ID"));
			log.info("PaymentStatusReader: " + tranx);
			return tranx;
		}
	}
	

}
