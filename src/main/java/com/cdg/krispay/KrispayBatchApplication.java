package com.cdg.krispay;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication
public class KrispayBatchApplication 
{
	   
	  public static void main(String[] args) 
	  {
	    SpringApplication.run(KrispayBatchApplication.class, args);
	  }
	 
	}