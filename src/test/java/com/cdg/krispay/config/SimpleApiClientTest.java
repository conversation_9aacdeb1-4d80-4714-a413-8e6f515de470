package com.cdg.krispay.config;

import static org.junit.Assert.assertEquals;

import java.io.IOException;
import java.time.Duration;

import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.test.web.reactive.server.WebTestClient;
import org.springframework.web.reactive.function.client.ClientRequest;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.WebClient;

import com.cdg.krispay.batch.CaptureWriter;
import com.cdg.krispay.dto.CaptureResponse;
import com.cdg.krispay.dto.DcpDeclinedPaymentRequest;
import com.cdg.krispay.dto.KrisPayTransaction;
import com.fasterxml.jackson.databind.JsonNode;

import io.netty.channel.ChannelOption;
import io.netty.handler.logging.LogLevel;
import reactor.core.publisher.Mono;
import reactor.netty.http.client.HttpClient;
import reactor.netty.transport.logging.AdvancedByteBufFormat;

//@SpringBootTest
public class SimpleApiClientTest {
//	private static MockWebServer mockWebServer;
	private static WebClient h5dcpWebClient;
	
	@Autowired
	CaptureWriter captureWriter;
	
	@Autowired
	private WebTestClient webTestClient;

	/*
	 * @Before public void setup() { MockitoAnnotations.openMocks(this); }
	 * 
	 * @BeforeAll static void setUp() throws IOException { mockWebServer = new
	 * MockWebServer(); mockWebServer.start(); }
	 * 
	 * @AfterAll static void tearDown() throws IOException {
	 * mockWebServer.shutdown(); }
	 * 
	 * @BeforeEach void initialize() { // String baseUrl =
	 * String.format("http://localhost:%s", mockBackEnd.getPort()); String baseUrl =
	 * "http://***********:2000/dcp-payment/rest/v3/h5Payment";
	 * mockWebServer.url(baseUrl); HttpClient httpClient2 = HttpClient.create()
	 * .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 2000)
	 * .responseTimeout(Duration.ofSeconds(5))
	 * .wiretap("reactor.netty.http.client.HttpClient", LogLevel.DEBUG,
	 * AdvancedByteBufFormat.TEXTUAL); this.h5dcpWebClient =
	 * WebClient.builder().baseUrl(baseUrl)
	 * .filter(ExchangeFilterFunction.ofRequestProcessor( request ->
	 * Mono.just(ClientRequest.from(request).build()))) .clientConnector(new
	 * ReactorClientHttpConnector(httpClient2)).build(); }
	 */
//    @Test
    public void testH5() {
    	captureWriter = new CaptureWriter();
    	KrisPayTransaction krisPayTransaction = new KrisPayTransaction();
    	krisPayTransaction.setJobNumber(null);
    	krisPayTransaction.setCaptureAmount(0);
    	krisPayTransaction.setFareAmount(0);
    	krisPayTransaction.setGstAmount(0);
    	krisPayTransaction.setAdminAmount(0);
    	
    	CaptureResponse response = new CaptureResponse();
    	response.setStatus("SUCCESS");
    	captureWriter.updatePaymentStatusToH5dcp(krisPayTransaction, response);
    }
//	@Test
//	public void shouldReturnCustomerOne() {
//		this.webTestClient.get().uri("http://dummy.restapiexample.com/api/v1/employees") // the base URL is already configured for us
//				.accept(MediaType.APPLICATION_JSON).exchange().expectStatus().isOk().expectHeader()
//				.contentType(MediaType.APPLICATION_JSON).expectBody().jsonPath("$.customerId").isNotEmpty()
//				.jsonPath("$.name").isNotEmpty();
//	}

//	@Test
	public void testGetUserById() throws InterruptedException, IOException {
//		MockResponse mockResponse = new MockResponse().addHeader("Content-Type", "application/json; charset=utf-8")
//				.setBody("{ \"name\": \"Jenette Caldwell\",\"salary\": \"2000\",\"age\": \"15\"}");
//
//		this.mockWebServer = new MockWebServer();
//		this.mockWebServer.start();
//		mockWebServer.enqueue(mockResponse);
//		this.cut = new SimpleApiClient(WebClient.builder()
//				.baseUrl(mockWebServer.url("http://dummy.restapiexample.com/api/v1/employees").toString()).build());
//		JsonNode result = cut.getTodoFromAPI();
//
////	    assertEquals(1, result.get("id").asInt());
//		assertEquals("Jenette Caldwell", result.get("name").asText());
//
//		RecordedRequest request = mockWebServer.takeRequest();
//		assertEquals("/todos/1", request.getPath());
	}
}
