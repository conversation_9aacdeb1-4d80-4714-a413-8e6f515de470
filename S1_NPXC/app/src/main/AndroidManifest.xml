<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.Source.S1_NPX.NPX"
    android:sharedUserId="user.castles">

    <uses-permission android:name="android.permission.INTERNET" />
<!--     <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" /> -->
<!--     <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" /> -->
    <uses-permission android:name="android.permission.ACCESS_SUPERUSER" />
    <uses-permission android:name="com.android.alarm.permission.SET_ALARM" />
    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />

    <application
        android:name=".SysApplication"
        android:hardwareAccelerated="false"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="false"
        android:allowBackup="false"
        android:debuggable="false"          
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme">
        <activity
            android:name="com.Source.S1_NPX.NPX.Splash"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:icon="@drawable/icon"
            android:label="@string/app_name"
            android:theme="@style/FullscreenTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

 <!--               <category android:name="android.intent.category.LAUNCHER" /> -->
                <category android:name="android.intent.category.LEANBACK_LAUNCHER"/>
            </intent-filter>
        </activity>
        <activity
            android:name="com.Source.S1_NPX.NPX.Main.MainActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/FullscreenTheme">
        </activity>
        <activity
            android:name="com.Source.S1_NPX.NPX.OrderCheck"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/app_name"
            android:theme="@style/FullscreenTheme">
        </activity>
        <activity
            android:name="com.Source.S1_NPX.NPX.PayChoose"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/app_name"
            android:theme="@style/FullscreenTheme">
        </activity>
        <activity
            android:name="com.Source.S1_NPX.NPX.PayCheck"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/app_name"
            android:theme="@style/FullscreenTheme">
        </activity>
        <activity
            android:name="com.Source.S1_NPX.NPX.HelloJni"
            android:label="@string/app_name"
            android:screenOrientation="portrait"
            android:theme="@style/FullscreenTheme">
        </activity>
        <activity
            android:name="com.Source.S1_NPX.NPX.PayApprove"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/app_name"
            android:theme="@style/FullscreenTheme">
        </activity>
        <activity
            android:name="com.Source.S1_NPX.NPX.Recipy"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/app_name"
            android:theme="@style/FullscreenTheme">
        </activity>
        <activity
            android:name="com.Source.S1_NPX.NPX.Recipy2"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/app_name"
            android:theme="@style/FullscreenTheme">
        </activity>
        <activity
            android:name="com.Source.S1_NPX.NPX.ICcard"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/app_name"
            android:theme="@style/FullscreenTheme">
        </activity>
        <activity
            android:name="com.Source.S1_NPX.NPX.MSRcard"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/app_name"
            android:theme="@style/FullscreenTheme">
        </activity>
        <activity
            android:name="com.Source.S1_NPX.NPX.Load"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/app_name"
            android:theme="@style/FullscreenTheme">
        </activity>
        <activity
            android:name="com.Source.S1_NPX.NPX.Load2"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/app_name"
            android:theme="@style/FullscreenTheme">
        </activity>
        <activity
            android:name="com.Source.S1_NPX.NPX.Trans.NETSprompt_amount"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/app_name"
            android:theme="@style/FullscreenTheme">
        </activity>
        <activity
            android:name="com.Source.S1_NPX.NPX.Trans.NETSprompt_refno"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/app_name"
            android:theme="@style/FullscreenTheme">
        </activity>
        <activity
            android:name="com.Source.S1_NPX.NPX.SocketActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/app_name"
            android:theme="@style/FullscreenTheme">
        </activity>
        <activity
            android:name="com.Source.S1_NPX.NPX.model.DispayText"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/app_name"
            android:theme="@style/FullscreenTheme">
        </activity>
        <activity
            android:name="com.Source.S1_NPX.NPX.Trans.GetAmount"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/app_name"
            android:theme="@style/FullscreenTheme">
        </activity>
        <activity
            android:name="com.Source.S1_NPX.NPX.db.AndroidDatabaseManager"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/app_name"
            android:theme="@style/FullscreenTheme">
        </activity>
        <activity
            android:name="com.Source.S1_NPX.NPX.db.ChooseFileActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/app_name"
            android:theme="@style/FullscreenTheme">
        </activity>
        <activity
            android:name="com.Source.S1_NPX.NPX.db.DatabaseManager"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/app_name"
            android:theme="@style/FullscreenTheme">
        </activity>
        <activity
            android:name="com.Source.S1_NPX.NPX.EditDatabaseActivity">
        </activity>
        <activity
            android:name="com.Source.S1_NPX.NPX.model.UserInputString"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/app_name"
            android:theme="@style/FullscreenTheme">
        </activity>
        <activity
            android:name="com.Source.S1_NPX.NPX.model.DOptionMenu"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/app_name"
            android:theme="@style/FullscreenTheme">
        </activity>
        <activity
            android:name="com.Source.S1_NPX.NPX.model.UpDownMenu"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/app_name"
            android:theme="@style/FullscreenTheme">
        </activity>
        <activity
            android:name="com.Source.S1_NPX.NPX.model.DPopupMenuActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/app_name"
            android:theme="@style/FullscreenTheme">
        </activity>
        <activity
            android:name="com.Source.S1_NPX.NPX.model.NormalKeyBoard"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/app_name"
            android:theme="@style/FullscreenTheme">
        </activity>
        <activity
            android:name="com.Source.S1_NPX.NPX.model.ConfirmAmount"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="NPX"
            android:theme="@style/FullscreenTheme" >
        </activity>
        <activity
            android:name="com.Source.S1_NPX.NPX.model.ConfirmAmountVoid"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="NPX"
            android:theme="@style/FullscreenTheme" >
        </activity>
        <activity
            android:name=".model.ProcessingOnlineUI"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/app_name"
            android:theme="@style/FullscreenTheme">
        </activity>
    </application>

</manifest>