package com.Source.S1_NPX.NPX;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Stack;

import android.app.Activity;
import android.app.ActivityManager;
import android.app.Application;
import android.content.Context;
import android.os.CountDownTimer;
import android.util.Log;

// import com.squareup.leakcanary.LeakCanary;
import CTOS.CtSystem;

/**
 * 一个类 用来结束所有后台activity
 * <AUTHOR>
 *
 */
public class SysApplication extends Application {
    private static final String TAG = "NPXSysApp";

    public static ArrayList <Activity> getActivityStack() {
        return activityStack;
    }

    public static void setActivityStack(ArrayList <Activity> activityStack) {
        SysApplication.activityStack = activityStack;
    }

    //运用list来保存们每一个activity是关键
//    private List<Activity> mList = new LinkedList<Activity>();
    private static ArrayList<Activity> activityStack;
    //为了实现每次使用该类时不创建新的对象而创建的静态对象
    private static SysApplication instance;
    //构造方法
    public SysApplication(){
        Log.i("SysApplication", "start isInAnalyzerProcess: ");
    }
    //实例化一次
    public synchronized static SysApplication getInstance(){
        if (null == instance) {
            instance = new SysApplication();
        }
        return instance;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        Log.i("SysApplication", "oncreate isInAnalyzerProcess: ");
//       if (LeakCanary.isInAnalyzerProcess(this)) {
//            Log.i("SysApplication", "isInAnalyzerProcess: ");
//
//            return;
//       }
//        LeakCanary.install(this);
    }
    // add Activity
//    public void addActivity(Activity activity) {
//        mList.add(activity);
//    }
    //关闭每一个list内的activity
//    public void exit() {
//        try {
//            for (Activity activity:mList) {
//                if (activity != null)
//                    activity.finish();
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        } finally {
//            System.exit(0);
//        }
//    }

    /**
     * 添加Activity到堆栈
     */
    public void addActivity(Activity activity){
        if(activityStack==null){
            setActivityStack(new ArrayList<Activity>());
        }
        activityStack.add(activity);
    }
    /**
     * 获取当前Activity（堆栈中最后一个压入的）
     */
    public Activity currentActivity(){
        Activity activity=activityStack.get(activityStack.size() - 1);;
        return activity;
    }
    /**
     * 结束当前Activity（堆栈中最后一个压入的）
     * @param
     */
    public void finishActivity(){
        Activity activity=activityStack.get(activityStack.size() - 1);
        finishActivity(activity);
    }
    /**
     * 结束指定的Activity
     */
    public void finishActivity(Activity activity){
        if(activity!=null){
            activityStack.remove(activity);
            activity.finish();
            activity=null;
        }
    }
    /**
     * 结束指定类名的Activity
     */
    public void finishActivity(Class<?> cls){
        for (Activity activity : activityStack) {
            if(activity.getClass().equals(cls) ){
                finishActivity(activity);
            }
        }
    }
    /**
     * 结束所有Activity
     */
    public void finishAllActivity(){
        for (int i = 0, size = activityStack.size(); i < size; i++){
            if (null != activityStack.get(i)){
                activityStack.get(i).finish();
            }
        }
        activityStack.clear();
    }
    /**
     * 退出应用程序
     */
    public void AppExit(Context context) {
        try {
            finishAllActivity();
            ActivityManager activityMgr= (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
            activityMgr.restartPackage(context.getPackageName());
            System.exit(0);
        } catch (Exception e) {
            Log.e(TAG, "AppExit: " + e.getMessage());
        }
    }
    //杀进程
    @Override
    public void onLowMemory() {
        super.onLowMemory();
    }
}
