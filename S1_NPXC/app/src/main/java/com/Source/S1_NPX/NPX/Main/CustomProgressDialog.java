package com.Source.S1_NPX.NPX.Main;

import android.app.AlertDialog;
import android.app.Dialog;
import android.app.ProgressDialog;
import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.widget.ProgressBar;
import android.widget.TextView;

import com.Source.S1_NPX.NPX.R;

import static android.view.View.GONE;

/**
 * Created by <PERSON>
 */

public class CustomProgressDialog extends AlertDialog {
    TextView pd_title, pd_message, pd_detail1, pd_detail2, pd_text1, pd_text2, pd_text3;
    String txn_message;
    private static final String TAG = "CustomProgressDialog";

    public CustomProgressDialog(Context context, String txn_message) {

        super(context);
        if(context == null) {
            Log.i(TAG, "context == null: ");
        }
        else {
            Log.i(TAG, "CustomProgressDialog: " + txn_message + context.getPackageName());
        }


        getWindow().setBackgroundDrawable(new ColorDrawable(android.graphics.Color.TRANSPARENT));
        getWindow().setGravity(Gravity.BOTTOM);
        this.txn_message = txn_message;
    }

    @Override
    public void show() {
        super.show();
        setContentView(R.layout.abc_screen_simple);
        pd_title = (TextView) findViewById(R.id.title);
        pd_message = (TextView) findViewById(R.id.progress_horizontal);

        String[] dispmsginfo = txn_message.split("\\|");
        int msgcnt = dispmsginfo.length;
        for (int inIdx = 0; inIdx < msgcnt; inIdx++) {
            Log.i(TAG,"split msg [" + inIdx + "][" + dispmsginfo[inIdx] + "]");
            switch (inIdx) {
                case 0:
                    pd_title.setText(dispmsginfo[inIdx]);
                    break;
                case 1:
                    pd_message.setText(dispmsginfo[inIdx]);
                    break;
                default:
                    break;
            }
        }
        Log.i(TAG, " check sMainActivity.LOCK: ");
        synchronized (MainActivity.LOCK) {
            MainActivity.LOCK.setCondition(true);
            MainActivity.LOCK.notifyAll();

        }
    }


    public void showProgress(Context context, String message) {
        Log.i(TAG, "start showProgress");
        if(context == null) {
            Log.i(TAG, "context == null: ");
        }
        else {
            Log.i(TAG, "showProgress: " + txn_message + context.getPackageName());
        }
        pd_title = (TextView) findViewById(R.id.title);

        pd_message = (TextView) findViewById(R.id.progress_horizontal);


        String[] dispmsginfo = message.split("\\|");
        int msgcnt = dispmsginfo.length;
        for (int inIdx = 0; inIdx < msgcnt; inIdx++) {
            Log.i(TAG,"split msg [" + inIdx + "][" + dispmsginfo[inIdx] + "]");
            switch (inIdx) {
                case 0:
                    pd_message.setText(dispmsginfo[inIdx]);
                    break;
                case 1:
                    pd_text1 = (TextView) findViewById(R.id.text);
                    pd_text1.setVisibility(View.VISIBLE);
                    pd_text1.setText(dispmsginfo[inIdx]);
                    break;
                case 2:
                    pd_detail1 = (TextView) findViewById(R.id.progress_circular);
                    pd_detail1.setVisibility(View.VISIBLE);
                    pd_detail1.setText(dispmsginfo[inIdx]);
                    break;
                case 3:
                    pd_text2 = (TextView) findViewById(R.id.text2);
                    pd_text2.setVisibility(View.VISIBLE);
                    pd_text2.setText(dispmsginfo[inIdx]);
                    break;
                default:
                    break;
            }
        }

        synchronized (MainActivity.LOCK) {
            MainActivity.LOCK.setCondition(true);
            MainActivity.LOCK.notifyAll();

        }

    }

    public void showCompleted(Context context) {
        Log.i(TAG, "start showProgress");
        setContentView(R.layout.abc_screen_simple);
        if(context == null) {
            Log.i(TAG, "context == null: ");
        }
        else {
            Log.i(TAG, "showCompleted: " + txn_message + context.getPackageName());
        }

    }


}
