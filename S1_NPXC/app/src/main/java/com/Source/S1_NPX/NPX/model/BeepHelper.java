package com.Source.S1_NPX.NPX.model;

import android.media.AudioManager;
import android.media.ToneGenerator;

/**
 * Created by 15-z960 on 3/12/2018.
 */

public class BeepHelper {
    private ToneGenerator toneGenerator;

    public BeepHelper() {
        toneGenerator = new ToneGenerator(AudioManager.STREAM_NOTIFICATION, 100);
    }

    public void startTone(String name) {
        int tone = 0;
        int duration = 1000;

        if (name.equals("beep")) {
            tone = ToneGenerator.TONE_PROP_BEEP;
        } else if (name.equals("beep_beep_beep")) {
            tone = ToneGenerator.TONE_CDMA_CONFIRM;
        } else if (name.equals("long_beep")) {
            tone = ToneGenerator.TONE_CDMA_ABBR_ALERT;
        } else if (name.equals("doodly_doo")) {
            tone = ToneGenerator.TONE_CDMA_ALERT_NETWORK_LITE;
        } else if (name.equals("chirp_chirp_chirp")) {
            tone = ToneGenerator.TONE_CDMA_ALERT_CALL_GUARD;
        } else if (name.equals("dialtone")) {
            tone = ToneGenerator.TONE_SUP_RINGTONE;
        } else
            tone = ToneGenerator.TONE_PROP_BEEP;
        toneGenerator.startTone(tone, duration);

        toneGenerator.release();
    }

}