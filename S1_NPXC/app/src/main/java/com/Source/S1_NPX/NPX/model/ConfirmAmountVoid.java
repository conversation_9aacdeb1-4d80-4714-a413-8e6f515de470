package com.Source.S1_NPX.NPX.model;

import android.content.Intent;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.text.Html;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import com.Source.S1_NPX.NPX.CloseActivityClass;
import com.Source.S1_NPX.NPX.Main.MainActivity;
import com.Source.S1_NPX.NPX.R;
import com.Source.S1_NPX.NPX.SysApplication;
import android.widget.ImageView;
import android.view.WindowManager;
public class ConfirmAmountVoid extends DemoAppActivity {
    private static final String TAG = "ConfirmAmountVoid";

    Button buOK;
    Button buCancel;
    private CountDownTimer mCountDownTimer;

    public static String getFinal_string() {
        return final_string;
    }

    public static void setFinal_string(String final_string) {
        ConfirmAmountVoid.final_string = final_string;
    }

    public static String final_string;
    public static String input_type;

    private long inTimeOut = 60*60*24*365;
    private long backpressed = 0;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        overridePendingTransition(0, 0); // disable the animation, faster
        getWindow().clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN);
        setContentView(R.layout.confirm_amt_void);

        Intent intent=getIntent();
        String dispmsg=intent.getStringExtra("pass_in_string");
        Log.i("dispmsg", dispmsg);


        final TextView msg_text_01;
        final TextView msg_text_02;
        final TextView msg_text_03;
        final TextView msg_text_04;
        final TextView msg_text_05;
        final ImageView imgview;

        msg_text_01 = findViewById(R.id.tv_input_amt_msg1);
        msg_text_02 = findViewById(R.id.tv_input_amt_msg2);
        msg_text_03 = findViewById(R.id.tv_input_amt_msg3);
        msg_text_04 = findViewById(R.id.tv_input_amt_msg4);
        msg_text_05 = findViewById(R.id.tv_input_amt_msg8);
        imgview = findViewById(R.id.imageView);

        String[] dispmsginfo = dispmsg.split("\\|");
        int msgcnt = dispmsginfo.length;
        for (int inIdx = 0 ; inIdx < msgcnt; inIdx++)
        {
            Log.i(TAG,"split msg [" + inIdx + "][" + dispmsginfo[inIdx] + "]");
            switch (inIdx)
            {
                case 0:
                    msg_text_01.setText(dispmsginfo[inIdx]);
                    break;
                case 1:
                    String[] dispmsginfo1 = dispmsginfo[inIdx].split("\\:");

                    String text = "<b>"+dispmsginfo1[0]+"</b>"
                            + ":" + "<font color= \"#e35205\"> "+ dispmsginfo1[1] + " </font>";
                    msg_text_02.setText(Html.fromHtml(text));                    break;
                case 2:
                    msg_text_03.setText(dispmsginfo[inIdx]);
                    break;
                case 3:
                    msg_text_04.setText(dispmsginfo[inIdx]);
                    break;
                case 4:
                    msg_text_05.setText(dispmsginfo[inIdx]);
                    break;
                case 5:
                    Log.i(TAG, dispmsginfo[inIdx].toString());
                    if(dispmsginfo[inIdx].equals("VISA"))
                    {
                        Log.i(TAG, "VISA");
                        imgview.setImageDrawable(getResources().getDrawable(R.drawable.visa));
                    }else if(dispmsginfo[inIdx].equals("MASTER")){
                        Log.i(TAG, "MASTER");
                        imgview.setImageDrawable(getResources().getDrawable(R.drawable.master));
                    }else if(dispmsginfo[inIdx].equals("JCB")){
                        Log.i(TAG, "JCB");
                        imgview.setImageDrawable(getResources().getDrawable(R.drawable.jcb));
                    } else if(dispmsginfo[inIdx].equals("AMEX")){
                        Log.i(TAG, "AMEX");
                        imgview.setImageDrawable(getResources().getDrawable(R.drawable.amex));
                    }
                    break;
                case 6:
                    try {
                        inTimeOut = Long.parseLong(dispmsginfo[inIdx]);
                    } catch (NumberFormatException nfe) {
                        Log.e(TAG, "NumberFormatException: " + nfe.getMessage());
                    }

                    Log.i(TAG, "inTimeOut: " + inTimeOut);
                    break;
                default:
                    break;
            }
        }

        buOK = (Button) findViewById(R.id.IPT_OKButton);
        buCancel = (Button) findViewById(R.id.IPT_CancelButton);
        Log.i(TAG, "onCreate: inTimeOut " + inTimeOut);
        /*Start timer*/
//        getTimerRestart();
        mCountDownTimer = new CountDownTimer(inTimeOut*1000, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
//                Log.i("Timer", "Timer onTick");
            }
            @Override
            public void onFinish() {
                Log.i(TAG, "Timer onFinish");
                setFinal_string("TO");
                // do whatever you need in child activity, but once you want to finish, do this and continue in parent activity
                synchronized (MainActivity.LOCK) {
                    MainActivity.LOCK.setCondition(true);
                    MainActivity.LOCK.notifyAll();

                }
                ConfirmAmountVoid.this.finish();

            }
        };
        mCountDownTimer.start();

        buOK.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {

                /*cancel timer first*/
//                getTimerCancel();
                mCountDownTimer.cancel();

                setFinal_string("CONFIRM");


                ConfirmAmountVoid.this.finish();

                Log.i(TAG, "Get Amount KeyBoard buOK");

                // do whatever you need in child activity, but once you want to finish, do this and continue in parent activity
                synchronized (MainActivity.LOCK) {
                    MainActivity.LOCK.setCondition(true);
                    MainActivity.LOCK.notifyAll();

                }

            }
        });

        buCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {

                /*cancel timer first*/
//                getTimerCancel();
                mCountDownTimer.cancel();
                setFinal_string("CANCEL");
                //startActivity(new Intent(ConfirmAmountVoid.this,MainActivity.class));
                ConfirmAmountVoid.this.finish();

                Log.i(TAG, "Get Amount KeyBoard buCancel");

                // do whatever you need in child activity, but once you want to finish, do this and continue in parent activity
                synchronized (MainActivity.LOCK) {
                    MainActivity.LOCK.setCondition(true);
                    MainActivity.LOCK.notifyAll();

                }
            }
        });

    }


    /**
     * 取消倒计时
     */
    public void getTimerCancel() {
        timer.cancel();
    }

    /**
     * 开始倒计时
     */
    public void getTimerRestart()
    {
        timer.start();
    }

    private CountDownTimer timer = new CountDownTimer(inTimeOut*1000L, 1000L) {

        @Override
        public void onTick(long millisUntilFinished) {
            Log.i(TAG, "Timer onTick");
        }

        @Override
        public void onFinish() {
            Log.i(TAG, "Timer onFinish");


            setFinal_string("TO");

            startActivity(new Intent(ConfirmAmountVoid.this,MainActivity.class));

            Log.i(TAG, "Timeout UserInputString");
            ConfirmAmountVoid.this.finish();

            // do whatever you need in child activity, but once you want to finish, do this and continue in parent activity
            synchronized (MainActivity.LOCK) {
                MainActivity.LOCK.setCondition(true);
                MainActivity.LOCK.notifyAll();
            }
        }
    };

    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if(keyCode==KeyEvent.KEYCODE_BACK&&event.getRepeatCount()==0){
            if ( backpressed == 0) {
                onBackPressed();
                backpressed = 1;
            } else {
                Log.i("backpressed", backpressed + "");
            }

            return true;
        }
        Log.i("onKeyDown", "onKeyDown");
        return false;
    }

    @Override
    public void onBackPressed() {
        //super.onBackPressed();
        Log.i("onBackPressed", "onBackPressed");

//        getTimerCancel();
        mCountDownTimer.cancel();
        setFinal_string("BACK");


        // do whatever you need in child activity, but once you want to finish, do this and continue in parent activity
        synchronized (MainActivity.LOCK) {
            MainActivity.LOCK.setCondition(true);
            MainActivity.LOCK.notifyAll();

        }
        ConfirmAmountVoid.this.finish();

    }

    @Override
    protected void onDestroy() {
        Log.i(TAG, "onDestroy: ");
        super.onDestroy();
        if (mCountDownTimer != null) {
            mCountDownTimer.cancel();
            mCountDownTimer = null;
        }

        setFinal_string(null);
    }

    public String startBeepTone(String beepStr) {
        Log.i("beepStr", beepStr);
        BeepHelper myBeep = new BeepHelper();
        myBeep.startTone(beepStr);

        return "OK";
    }

}

