package com.Source.S1_NPX.NPX.Kms;

import android.app.Activity;
import android.content.Intent;
import android.os.Handler;
import android.os.HandlerThread;

import android.util.Log;
import android.view.View;

import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;

import com.Source.S1_NPX.NPX.R;

public class KMSUI {
    private Activity mActivity;
  
    Button startButton;
    Button startButton2;
    TextView PinDigitTV;
    EditText PinDigitET;
    EditText FunctionKeyET;


    private static final String TAG_A = "KMS";
    private static final String TAG_C = "KMSUI";

    private PINPADFIXED MovingEncrythread;
    private PINPADDUKPT ScramblingEncrythread;
    private int inKeySet = 0;
    private int inKeyIndex= 0;
    private String StrInData= "";
    private int inKeyType = 0;
    public static String final_string;
    public static String PinBlock;
    public static String KSN;
    private int inPinBypassAllow = 0;
    private byte[] bSKey={};


    private Handler background_ON_Handler;
    private Runnable background_on = new Runnable () {
        public void run() {
            KMSUI.this.mActivity.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    PinDigitTV.setEnabled(false);
                    PinDigitTV.setAlpha(0.0f);
                    PinDigitET.setEnabled(false);
                    PinDigitET.setAlpha(0.0f);
                    FunctionKeyET.setEnabled(false);
                    FunctionKeyET.setAlpha(0.0f);
                    startButton.setEnabled(true);
                    startButton.setAlpha(1.0f);
                    startButton2.setEnabled(true);
                    startButton2.setAlpha(1.0f);
                }
            });
        }
    };


    public KMSUI() {
           //Do nothing
    }

    public void init(Activity activity, int KeySet, int KeyIndex, String pInData, int KeyType, int PinBypassAllow, byte[] bSessionKey) {
        this.mActivity = activity;
        this.inKeySet = KeySet;
        this.inKeyIndex = KeyIndex;
        this.StrInData = pInData;
        this.inKeyType = KeyType;
        this.inPinBypassAllow = PinBypassAllow;
        this.bSKey = bSessionKey;
        this.mActivity.runOnUiThread(new Runnable() {
            public void run() {
                KMSUI.this.mActivity.setContentView(R.layout.activity_kms);;

                startButton = (Button) KMSUI.this.mActivity.findViewById(R.id.button_start);
                startButton2 = (Button) KMSUI.this.mActivity.findViewById(R.id.button_start2);
                PinDigitTV = (TextView) KMSUI.this.mActivity.findViewById(R.id.PinDigitTextView);
                PinDigitET = (EditText) KMSUI.this.mActivity.findViewById(R.id.PinDigitEditText);
                FunctionKeyET = (EditText) KMSUI.this.mActivity.findViewById(R.id.FunctionkeyText);



                Intent intent=KMSUI.this.mActivity.getIntent();


                 Log.i("inKeySet", inKeySet+"");
                 Log.i("inKeyIndex", inKeyIndex+"");
                 Log.i("StrInData", StrInData);


                startButton.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {

                        Log.d(TAG_A, TAG_C + "On Click startButton Listener.");
                        MovingEncrythread = new PINPADFIXED(KMSUI.this.mActivity, inKeySet, inKeyIndex, StrInData, inPinBypassAllow, bSKey);
                        MovingEncrythread.start();

                    }
                });

                startButton2.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {

                        Log.d(TAG_A, TAG_C + "On Click startButton2 Listener.");
                        ScramblingEncrythread = new PINPADDUKPT(KMSUI.this.mActivity, inKeySet, inKeyIndex, StrInData, inPinBypassAllow);
                        ScramblingEncrythread.start();

                    }
                });
                PinDigitTV.setEnabled(false);
                PinDigitTV.setAlpha(0.0f);
                PinDigitET.setEnabled(false);
                PinDigitET.setAlpha(0.0f);
                FunctionKeyET.setEnabled(false);
                FunctionKeyET.setAlpha(0.0f);

                HandlerThread background_ON_Thread1 = new HandlerThread("background_on");
                background_ON_Thread1.start();
                background_ON_Handler = new Handler (background_ON_Thread1.getLooper());



                if(inKeyType == 1)
                {
                    startButton.callOnClick();
                }

                if(inKeyType == 2)
                {
                    startButton2.callOnClick();
                }
            }
        });






    }


    public void exit() {
        this.mActivity.runOnUiThread(new Runnable() {
            public void run() {
				//Do nothing
            }
        });
    }
}
