package com.Source.S1_NPX.NPX;

import android.content.Intent;
import android.support.v7.app.AppCompatActivity;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.util.Log;
import com.Source.S1_NPX.NPX.Main.MainActivity;
import com.Source.S1_NPX.NPX.R;
import com.Source.S1_NPX.NPX.db.AndroidDatabaseManager;

import java.io.File;
import java.lang.reflect.Method;

import CTOS.CtSystem;

import static android.system.Os.chmod;

public class EditDatabaseActivity extends AppCompatActivity implements View.OnClickListener{
    private Button btnterminal;
    private Button btnwave;
    private Button btnenv;
    private Button btnexit;
    private Button btnCom;
    private Button btnemv;
    private Button btnEftNac;
    private Button btnErm;
    private static final String TAG = "EditDatabaseActivity";
    private static final String DBNAME = "dbname";
    private static final String BTNEDIT = "btn_edit_env";
    private String TerminalDb = "/data/data/pub/TERMINAL.S3DB";
    private String WaveDb = "/data/data/pub/WAVE.S3DB";
    private String EmvDb = "/data/data/pub/EMV.S3DB";
    private String ComDb = "/data/data/pub/COM.S3DB";
    private String EnvDb = "/data/data/pub/ENV.S3DB";
    private String EnvEftNAC = "/data/data/pub/EFTNAC.S3DB";
    private String ErmDb = "/data/data/pub/ERMTCT.S3DB";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        Log.i(TAG, "onCreate: ");
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_edit_database);

        btnterminal = (Button) findViewById(R.id.btn_edit_terminal);
        btnwave = (Button) findViewById(R.id.btn_edit_wave);
        btnemv = (Button) findViewById(R.id.btn_edit_emv);
        btnenv = (Button) findViewById(R.id.btn_edit_env);
        btnexit = (Button) findViewById(R.id.btn_exit);
        btnCom = (Button) findViewById(R.id.btn_edit_com);
        btnEftNac = (Button) findViewById(R.id.btn_edit_eftnac);
        btnErm = (Button) findViewById(R.id.btn_edit_erm);

        btnterminal.setOnClickListener(this);
        btnwave.setOnClickListener(this);
        btnemv.setOnClickListener(this);
        btnenv.setOnClickListener(this);
        btnexit.setOnClickListener(this);
        btnCom.setOnClickListener(this);
        btnEftNac.setOnClickListener(this);
        btnErm.setOnClickListener(this);
    }
    public void setNavigationBarFlag(boolean flag) {
        new CtSystem().setNavigationbar(flag);
    }
    @Override
    public void onClick(View view) {
        Intent intent = new Intent();
        Bundle bundle = new Bundle();

        switch (view.getId())
        {
            case R.id.btn_edit_terminal:
                Log.d(TAG, "btn_edit_terminal");
                intent.setClass(EditDatabaseActivity.this, AndroidDatabaseManager.class);
                bundle.putString(DBNAME, TerminalDb);
                intent.putExtras(bundle);
                startActivity(intent);
                break;
            case R.id.btn_edit_wave:
                Log.d(TAG, "btn_edit_wave");
                intent.setClass(EditDatabaseActivity.this, AndroidDatabaseManager.class);
                bundle.putString(DBNAME, WaveDb);
                intent.putExtras(bundle);
                startActivity(intent);
                break;
            case R.id.btn_edit_emv:
                Log.d(TAG, "btn_edit_emv");
                intent.setClass(EditDatabaseActivity.this, AndroidDatabaseManager.class);
                bundle.putString(DBNAME, EmvDb);
                intent.putExtras(bundle);
                startActivity(intent);
                break;
            case R.id.btn_edit_env:
                Log.d(TAG, BTNEDIT);
                intent.setClass(EditDatabaseActivity.this, AndroidDatabaseManager.class);
                bundle.putString(DBNAME, EnvDb);
                intent.putExtras(bundle);
                startActivity(intent);
                break;
            case R.id.btn_edit_com:
                Log.d(TAG, BTNEDIT);
                intent.setClass(EditDatabaseActivity.this, AndroidDatabaseManager.class);
                bundle.putString(DBNAME, ComDb);
                intent.putExtras(bundle);
                startActivity(intent);
                break;
            case R.id.btn_edit_eftnac:
                Log.d(TAG, "btn_edit_eftnac");
                intent.setClass(EditDatabaseActivity.this, AndroidDatabaseManager.class);
                bundle.putString(DBNAME, EnvEftNAC);
                intent.putExtras(bundle);
                startActivity(intent);
                break;
            case R.id.btn_edit_erm:
                Log.d(TAG, "btn_edit_erm");
                intent.setClass(EditDatabaseActivity.this, AndroidDatabaseManager.class);
                bundle.putString(DBNAME, ErmDb);
                intent.putExtras(bundle);
                startActivity(intent);
                break;
            case R.id.btn_exit:
                Log.d(TAG, BTNEDIT);
                intent.setClass(EditDatabaseActivity.this, MainActivity.class);
                startActivity(intent);
                EditDatabaseActivity.this.finish();
                setNavigationBarFlag(true);
                break;
            default:
                break;

        }



    }

    @Override
    public void onBackPressed() {
        Log.i(TAG, "onBackPressed: ");
        super.onBackPressed();
    }

    @Override
    protected void onResume() {
        Log.i(TAG, "onResume: ");
        super.onResume();
    }

    @Override
    protected void onDestroy() {
        Log.i(TAG, "onDestroy: ");
        super.onDestroy();
    }
}
