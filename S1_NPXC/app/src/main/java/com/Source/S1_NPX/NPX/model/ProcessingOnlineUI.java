package com.Source.S1_NPX.NPX.model;

import android.os.Bundle;
import android.support.annotation.Nullable;
import android.util.Log;
import android.widget.ImageView;
import android.content.Intent;
import android.widget.TextView;
import android.text.Html;
import com.Source.S1_NPX.NPX.R;
import android.view.WindowManager;
public class ProcessingOnlineUI extends DemoAppActivity{
    private static final String TAG = "ProcessingOnlineUI";
    private static ProcessingOnlineUI sInstance = null;
    private TextView msg_text_01;
    private TextView msg_text_02;
    private TextView msg_text_03;
    private TextView msg_text_06;
    private TextView msg_text_07;
    private ImageView ivHeaderlogo;

    public static void setsInstance(ProcessingOnlineUI sInstance) {
        ProcessingOnlineUI.sInstance = sInstance;
    }

    public static ProcessingOnlineUI getsInstance() {
        return sInstance;
    }


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        Log.i(TAG, "onCreate: ");
        super.onCreate(savedInstanceState);
        getWindow().clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN);
//        getWindow().addFlags(Intent.FLAG_ACTIVITY_BROUGHT_TO_FRONT);
        setContentView(R.layout.processing);


        ivHeaderlogo = findViewById(R.id.ivHeaderlogo);
        msg_text_01 =  findViewById(R.id.tv_input_amt_msg1);
        msg_text_02 =  findViewById(R.id.tv_input_amt_msg2);
        msg_text_03 =  findViewById(R.id.tv_input_amt_msg3);
        msg_text_06 =  findViewById(R.id.tv_input_amt_msg6);
        msg_text_07 =  findViewById(R.id.tv_input_amt_msg7);


        Intent intent=getIntent();
        String dispmsg=intent.getStringExtra("pass_in_string");
        Log.i("dispmsg", dispmsg);
        String[] dispmsginfo = dispmsg.split("\\|");
        int msgcnt = dispmsginfo.length;
        for (int inIdx = 0 ; inIdx < msgcnt; inIdx++)
        {
            Log.i(TAG, "split msg [" + inIdx + "][" + dispmsginfo[inIdx] + "]");
            switch (inIdx)
            {
                case 0:
                    msg_text_01.setText(dispmsginfo[inIdx]);
                    break;
                case 1:
                    msg_text_02.setText(dispmsginfo[inIdx]);
                    break;
                case 2:
                    String[] dispmsginfo1 = dispmsginfo[inIdx].split("\\:");

                    String text = "<b>"+dispmsginfo1[0]+"</b>"
                            + ":" + "<font color= \"#e35205\"> "+ dispmsginfo1[1] + " </font>";
                    msg_text_06.setText(Html.fromHtml(text));
                    break;
                case 3:
                    msg_text_03.setText(dispmsginfo[inIdx]);
                    break;
                case 4:
                    msg_text_07.setText(dispmsginfo[inIdx]);
                    break;
                case 5:
                    Log.i(TAG, dispmsginfo[inIdx]);
                    if(dispmsginfo[inIdx].equals("VISA"))
                    {
                        Log.i(TAG, "VISA");
                        ivHeaderlogo.setImageDrawable(getResources().getDrawable(R.drawable.visa));
                    }else if(dispmsginfo[inIdx].equals("MASTER")){
                        Log.i(TAG, "MASTER");
                        ivHeaderlogo.setImageDrawable(getResources().getDrawable(R.drawable.master));
                    }else if(dispmsginfo[inIdx].equals("JCB")){
                        Log.i(TAG, "JCB");
                        ivHeaderlogo.setImageDrawable(getResources().getDrawable(R.drawable.jcb));
                    } else if(dispmsginfo[inIdx].equals("AMEX")){
                        Log.i(TAG, "AMEX");
                        ivHeaderlogo.setImageDrawable(getResources().getDrawable(R.drawable.amex));
                    }
                    break;
                default:
                    break;
            }
        }

        setsInstance(ProcessingOnlineUI.this);
    }

    @Override
    protected void onStop() {
        Log.i(TAG, "onStop: ");
        super.onStop();
    }

    @Override
    protected void onResume() {
        Log.i(TAG, "onResume: ");
        super.onResume();
    }

    @Override
    protected void onDestroy() {
        Log.i(TAG, "onDestroy: ");
        setsInstance(null);
        super.onDestroy();
    }
}
