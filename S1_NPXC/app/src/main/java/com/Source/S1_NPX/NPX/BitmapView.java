package com.Source.S1_NPX.NPX;


import java.io.File;
import java.io.FileOutputStream;
import android.content.Context;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.Bitmap.Config;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Matrix;
import android.util.Log;
import android.view.View;

public class BitmapView extends View
{
    private static final String TAG = "BitmapView";
    private Matrix matrixex = null;
    private static final String Path = "/mnt/sdcard/DCIM/";
    public BitmapView(Context context)
    {
        super(context);
    }

    @Override
    public void onDraw(Canvas canvas)
    {
        // 獲取資源檔案的引用res
        Resources res = getResources();
        // 獲取圖形資源檔案
        Bitmap bmp = BitmapFactory.decodeResource(res, R.drawable.sign);
        // 設定canvas畫布背景為白色
        canvas.drawColor(Color.BLACK);
        canvas.drawBitmap(bmp, 0, 0, null);
        // 定義矩陣物件
        matrixex = new Matrix();
        Bitmap bitmap = Bitmap.createBitmap(bmp, 0, 50, bmp.getWidth(), bmp.getHeight()/2, matrixex, true);
        canvas.drawBitmap(bitmap, 0, 250, null);
        saveBitmap(bitmap);
    }
    //儲存到本地
    public void saveBitmap(Bitmap bmp)
    {
        Bitmap bitmap = Bitmap.createBitmap(800, 600, Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        //載入背景圖片
        Bitmap bmps = BitmapFactory.decodeResource(getResources(), R.drawable.sign);
        canvas.drawBitmap(bmps, 0, 0, null);
        //載入要儲存的畫面
        canvas.drawBitmap(bmp, 10, 100, null);
        //儲存全部圖層
        canvas.restore();
        //存儲路徑
        File file = new File(Path);
        if(!file.exists())
            file.mkdirs();
        try {
            FileOutputStream fileOutputStream = new FileOutputStream(file.getPath() + "/gaozhen_08189.jpg");
            bitmap.compress(Bitmap.CompressFormat.JPEG, 100, fileOutputStream);
            fileOutputStream.close();
            Log.i(TAG, "saveBmp is here");
        } catch (Exception e)
        {
            Log.e(TAG, ": " + e.getMessage());
        }
    }
}