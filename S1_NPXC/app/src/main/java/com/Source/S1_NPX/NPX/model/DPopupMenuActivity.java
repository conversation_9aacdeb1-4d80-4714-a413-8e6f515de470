package com.Source.S1_NPX.NPX.model;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.Handler;
import android.util.Log;
import android.view.View;
import android.widget.AdapterView;
import android.widget.Button;

import com.Source.S1_NPX.NPX.Main.MainActivity;
import com.Source.S1_NPX.NPX.R;

public class DPopupMenuActivity extends Activity {

    private boolean isFirstTrigger=true;
    private int flag=1;
    private Button button1=null;
    private String TAG="Menu";

    private static Handler mHandler;

    public static String getSelect_item() {
        return select_item;
    }

    public static void setSelect_item(String select_item) {
        DPopupMenuActivity.select_item = select_item;
    }
    public static String select_item;

    private long inTimeOut = (long)60;

    private DPopupMenu popMenu;
    private String MenuTitle;
    private String FullMenuTimes;
    private String[] MenuItmes = new String[100];

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.i(TAG, "Popup Menu onCreate");

        overridePendingTransition(0, 0); // disable the animation, faster

        setContentView(R.layout.activity_popup_menu);

        Intent intent=getIntent();
        String pass_in_value=intent.getStringExtra("pass_in_string");
        Log.i("main pass in value", pass_in_value);

        MenuTitle = pass_in_value.substring(0, pass_in_value.indexOf("|"));
        FullMenuTimes = pass_in_value.substring(pass_in_value.indexOf("|")+1);
        Log.i("MenuTitle", MenuTitle);
        Log.i("FullMenuTimes", FullMenuTimes);

        /*split out the menu items*/
        MenuItmes = FullMenuTimes.split(" \n");

        button1=(Button)findViewById(R.id.popup_menu_button11);

        button1.setText(MenuTitle);

        /*Start timer*/
        getTimerRestart();

        button1.setOnClickListener(new Button.OnClickListener() {

            @Override
            public void onClick(View v) {
                flag=1;

                popMenu = new DPopupMenu(getApplicationContext());

                /*
                popMenu.addItem(new DPopupMenu.Item("选项一",0));
                popMenu.addItem(new DPopupMenu.Item("选项二",0));
                popMenu.addItem(new DPopupMenu.Item("选项三",0));
                popMenu.addItem(new DPopupMenu.Item("选项四",0));
                */

                for (int i = 0; i < MenuItmes.length; i++) {
                    Log.i(TAG,MenuItmes[i]);
                    popMenu.addItem(new DPopupMenu.Item(MenuItmes[i],0));
                }

                // 菜单项点击监听器
                popMenu.setOnItemClickListener(popmenuItemClickListener);
                popMenu.showAsDropDown(button1);

            }
        });

        /*don't want change this sample code, so set auto click.*/
        new Handler().postDelayed(new Runnable() {

            public void run() {
                //button1.dispatchTouchEvent(MotionEvent.obtain(SystemClock.uptimeMillis(), SystemClock.uptimeMillis(), MotionEvent.ACTION_BUTTON_PRESS , 0, 0, 0));
                //button1.dispatchTouchEvent(MotionEvent.obtain(SystemClock.uptimeMillis(), SystemClock.uptimeMillis(), MotionEvent.ACTION_BUTTON_RELEASE , 0, 0, 0));
                button1.callOnClick();
            }
        }, 100);
    }

    private AdapterView.OnItemClickListener popmenuItemClickListener = new AdapterView.OnItemClickListener() {
        @Override
        public void onItemClick(AdapterView<?> parent, View view, int position, long id) {

            /*
            switch (position) {
                case 0:
                    popMenu.dismiss();
                    break;
                case 1:
                    popMenu.dismiss();
                    break;
                case 2:
                    popMenu.dismiss();
                    break;
                case 3:
                    popMenu.dismiss();
                    break;
            }
            */

            popMenu.dismiss();
            setSelect_item (String.valueOf(position));
            //startActivity(new Intent(DPopupMenuActivity.this,MainActivity.class));

            Log.i("PATRICK", "FINISH DMenuList");
            DPopupMenuActivity.this.finish();

            // do whatever you need in child activity, but once you want to finish, do this and continue in parent activity
            synchronized (MainActivity.LOCK) {
                MainActivity.LOCK.setCondition(true);
                MainActivity.LOCK.notifyAll();

            }
        }
    };
    @Override
    protected void onDestroy() {
        super.onDestroy();
        
		if (timer != null) {
			timer.cancel();
			timer = null;
		}
		
        if (popMenu != null) {
            popMenu.dismiss();
        }
    }

    /**
     * 取消倒计时
     */
    public void getTimerCancel() {
        timer.cancel();
    }

    /**
     * 开始倒计时
     */
    public void getTimerRestart()
    {
        timer.start();
    }

    private CountDownTimer timer = new CountDownTimer(inTimeOut*1000, 1000) {

        @Override
        public void onTick(long millisUntilFinished) {
            Log.i("Timer", "Timer onTick");
        }

        @Override
        public void onFinish() {
            Log.i("Timer", "Timer onFinish");

            popMenu.dismiss();
            setSelect_item ("TO");

            startActivity(new Intent(DPopupMenuActivity.this,MainActivity.class));

            Log.i("PATRICK", "Timeout UserInputString");
            DPopupMenuActivity.this.finish();

            // do whatever you need in child activity, but once you want to finish, do this and continue in parent activity
            synchronized (MainActivity.LOCK) {
                MainActivity.LOCK.setCondition(true);
                MainActivity.LOCK.notifyAll();
            }
        }
    };

}