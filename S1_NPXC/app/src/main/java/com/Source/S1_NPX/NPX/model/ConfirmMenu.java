package com.Source.S1_NPX.NPX.model;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;

import com.Source.S1_NPX.NPX.CloseActivityClass;
import com.Source.S1_NPX.NPX.Main.MainActivity;
import com.Source.S1_NPX.NPX.R;
import com.Source.S1_NPX.NPX.SysApplication;

public class ConfirmMenu extends DemoAppActivity {
    private static final String TAG = "ConfirmMenu";
    private Context mContext;

    private Button mButton;
    final Context c = this;

    EditText etInputStr;
    Button buOK;
    Button buCancel;

    TextView textView01;
    TextView textView02;
    TextView textView03;
    TextView textView04;
    TextView textView05;
    TextView textView06;
    TextView textView07;
    TextView textView08;


    public static String getFinal_string() {
        return final_string;
    }

    public static void setFinal_string(String final_string) {
        ConfirmMenu.final_string = final_string;
    }
    public static String final_string;
    public static String input_type;

    private long inTimeOut = (long)60*60*24*365;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        SysApplication.getInstance().addActivity(this);
        super.onCreate(savedInstanceState);

        overridePendingTransition(0, 0); // disable the animation, faster

        setContentView(R.layout.confirm_menu);

        Intent intent=getIntent();
        String dispmsg=intent.getStringExtra("pass_in_string");
        Log.i("dispmsg", dispmsg);


        textView01 = (TextView) findViewById(R.id.msg_text_01);
        textView02 = (TextView) findViewById(R.id.msg_text_02);
        textView03 = (TextView) findViewById(R.id.msg_text_03);
        textView04 = (TextView) findViewById(R.id.msg_text_04);
        textView05 = (TextView) findViewById(R.id.msg_text_05);
        textView06 = (TextView) findViewById(R.id.msg_text_06);
        textView07 = (TextView) findViewById(R.id.msg_text_07);
        textView08 = (TextView) findViewById(R.id.msg_text_08);

        String[] dispmsginfo = dispmsg.split("\\|");
		int msgcnt = dispmsginfo.length;

        for (int inIdx = 0 ; inIdx < msgcnt; inIdx++)
        {
            switch (inIdx)
            {
                case 0:
                    textView01.setText(dispmsginfo[inIdx]);
                    break;
                case 1:
                    textView02.setText(dispmsginfo[inIdx]);
                    break;
                case 2:
                    textView03.setText(dispmsginfo[inIdx]);
                    break;
                case 3:
                    textView04.setText(dispmsginfo[inIdx]);
                    break;
                case 4:
                    textView05.setText(dispmsginfo[inIdx]);
                    break;
                case 5:
                    textView06.setText(dispmsginfo[inIdx]);
                    break;
                case 6:
                    textView07.setText(dispmsginfo[inIdx]);
                    break;
                case 7:
                    textView08.setText(dispmsginfo[inIdx]);
                    break;
                default:
                    break;
            }
        }


        buOK = (Button) findViewById(R.id.IPT_OKButton);
        buCancel = (Button) findViewById(R.id.IPT_CancelButton);

        /*Start timer*/
        getTimerRestart();


        buOK.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {

                /*cancel timer first*/
                getTimerCancel();


                setFinal_string("CONFIRM");


                ConfirmMenu.this.finish();

                Log.i(TAG, "Get Amount KeyBoard buOK");

                // do whatever you need in child activity, but once you want to finish, do this and continue in parent activity
                synchronized (MainActivity.LOCK) {
                    MainActivity.LOCK.setCondition(true);
                    MainActivity.LOCK.notifyAll();

                }

            }
        });

        buCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {

                /*cancel timer first*/
                getTimerCancel();


                setFinal_string("CANCEL");
                startActivity(new Intent(ConfirmMenu.this,MainActivity.class));
                ConfirmMenu.this.finish();

                Log.i("PATRICK", "Get Amount KeyBoard buCancel");

                // do whatever you need in child activity, but once you want to finish, do this and continue in parent activity
                synchronized (MainActivity.LOCK) {
                    MainActivity.LOCK.setCondition(true);
                    MainActivity.LOCK.notifyAll();

                }
            }
        });

    }

    /**
     * 取消倒计时
     */
    public void getTimerCancel() {
        timer.cancel();
    }

    /**
     * 开始倒计时
     */
    public void getTimerRestart()
    {
        timer.start();
    }

    private CountDownTimer timer = new CountDownTimer(inTimeOut*1000, 1000) {

        @Override
        public void onTick(long millisUntilFinished) {
            Log.i("Timer", "Timer onTick");
        }

        @Override
        public void onFinish() {
            Log.i("Timer", "Timer onFinish");



            setFinal_string("TO");
            startActivity(new Intent(ConfirmMenu.this,MainActivity.class));

            Log.i(TAG, "Timeout UserInputString");
            ConfirmMenu.this.finish();

            // do whatever you need in child activity, but once you want to finish, do this and continue in parent activity
            synchronized (MainActivity.LOCK) {
                MainActivity.LOCK.setCondition(true);
                MainActivity.LOCK.notifyAll();
            }
        }
    };

    @Override
    protected void onDestroy() {
        super.onDestroy();
        setFinal_string(null);
    }

}

