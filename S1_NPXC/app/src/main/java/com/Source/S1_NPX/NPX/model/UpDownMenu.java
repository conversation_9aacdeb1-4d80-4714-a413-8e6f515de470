package com.Source.S1_NPX.NPX.model;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;

import com.Source.S1_NPX.NPX.CloseActivityClass;
import com.Source.S1_NPX.NPX.Main.MainActivity;
import com.Source.S1_NPX.NPX.R;
import com.Source.S1_NPX.NPX.SysApplication;

public class UpDownMenu extends DemoAppActivity {
    private static final String TAG = "UpDownMenu";
    Button button_up;
    Button button_down;
    Button button_exit;

    TextView textView01;
    TextView textView02;
    TextView textView03;
    TextView textView04;
    TextView textView05;
    TextView textView06;
    TextView textView07;
    TextView textView08;

    public static String getFinal_string() {
        return final_string;
    }

    public static void setFinal_string(String final_string) {
        UpDownMenu.final_string = final_string;
    }

    public static String final_string;


    private int inTimeOut = 60*8;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
       
        super.onCreate(savedInstanceState);

        overridePendingTransition(0, 0); // disable the animation, faster

        setContentView(R.layout.updown_menu);

        Intent intent=getIntent();
        String dispmsg=intent.getStringExtra("pass_in_string");
        Log.i("dispmsg", dispmsg);
       
        textView01 = (TextView) findViewById(R.id.msg_text_01);
        textView02 = (TextView) findViewById(R.id.msg_text_02);
        textView03 = (TextView) findViewById(R.id.msg_text_03);
        textView04 = (TextView) findViewById(R.id.msg_text_04);
        textView05 = (TextView) findViewById(R.id.msg_text_05);
        textView06 = (TextView) findViewById(R.id.msg_text_06);
        textView07 = (TextView) findViewById(R.id.msg_text_07);
        textView08 = (TextView) findViewById(R.id.msg_text_08);

        String[] dispmsginfo = dispmsg.split("\\|");
		int msgcnt = dispmsginfo.length;

        Log.i(TAG,"msgcnt [" + msgcnt + "]");

        for (int inIdx = 0 ; inIdx < msgcnt; inIdx++)
        {
            Log.i(TAG,"split msg [" + inIdx + "][" + dispmsginfo[inIdx] + "]");
            switch (inIdx)
            {
                case 0:
                    textView01.setText(dispmsginfo[inIdx]);
                    break;
                case 1:
                    textView02.setText(dispmsginfo[inIdx]);
                    break;
                case 2:
                    textView03.setText(dispmsginfo[inIdx]);
                    break;
                case 3:
                    textView04.setText(dispmsginfo[inIdx]);
                    break;
                case 4:
                    textView05.setText(dispmsginfo[inIdx]);
                    break;
                case 5:
                    textView06.setText(dispmsginfo[inIdx]);
                    break;
                case 6:
                    textView07.setText(dispmsginfo[inIdx]);
                    break;
                case 7:
                    textView08.setText(dispmsginfo[inIdx]);
                    break;
                default:
                    break;
            }
        }

        button_down = findViewById(R.id.button_down);
        button_up = findViewById(R.id.button_up);
        button_exit = findViewById(R.id.button_exit);
        /*Start timer*/
        getTimerRestart();


        button_down.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {

                /*cancel timer first*/
                getTimerCancel();

                setFinal_string("DOWN");

                UpDownMenu.this.finish();

                // do whatever you need in child activity, but once you want to finish, do this and continue in parent activity
                synchronized (MainActivity.LOCK) {
                    MainActivity.LOCK.setCondition(true);
                    MainActivity.LOCK.notifyAll();

                }

            }
        });

        button_up.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {

                /*cancel timer first*/
                getTimerCancel();

                setFinal_string("UP");
                //startActivity(new Intent(UpDownMenu.this,MainActivity.class));
                UpDownMenu.this.finish();

                // do whatever you need in child activity, but once you want to finish, do this and continue in parent activity
                synchronized (MainActivity.LOCK) {
                    MainActivity.LOCK.setCondition(true);
                    MainActivity.LOCK.notifyAll();

                }
            }
        });

        button_exit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {

                /*cancel timer first*/
                getTimerCancel();

                setFinal_string("EXIT");
                //startActivity(new Intent(UpDownMenu.this,MainActivity.class));
                UpDownMenu.this.finish();

                // do whatever you need in child activity, but once you want to finish, do this and continue in parent activity
                synchronized (MainActivity.LOCK) {
                    MainActivity.LOCK.setCondition(true);
                    MainActivity.LOCK.notifyAll();

                }
            }
        });

    }

    /**
     * 取消倒计时
     */
    public void getTimerCancel() {
        timer.cancel();
    }

    /**
     * 开始倒计时
     */
    public void getTimerRestart()
    {
        timer.start();
    }

    private CountDownTimer timer = new CountDownTimer(inTimeOut*1000L, 1000L) {

        @Override
        public void onTick(long millisUntilFinished) {
            Log.i("Timer", "Timer onTick");
        }

        @Override
        public void onFinish() {
            Log.i("Timer", "Timer onFinish");

            setFinal_string("TO");

            UpDownMenu.this.finish();

            // do whatever you need in child activity, but once you want to finish, do this and continue in parent activity
            synchronized (MainActivity.LOCK) {
                MainActivity.LOCK.setCondition(true);
                MainActivity.LOCK.notifyAll();
            }
        }
    };

    @Override
    public void onBackPressed() {
      //do nothing
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

		if (timer != null) {
			timer.cancel();
			timer = null;
		}
		
        setFinal_string(null);
    }

}

