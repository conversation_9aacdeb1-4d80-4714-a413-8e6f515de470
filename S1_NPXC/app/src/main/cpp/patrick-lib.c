//
// Created by patri on 6/3/2018.
//

#include "patrick-lib.h"
#include <sqlite3.h>
#include "Database/DatabaseFunc.h"
#include "Debug/debug.h"
#include "Aptrans/MultiAptrans.h"
#include "Ctls/POSWave.h"
#include "Includes/POSSale.h"
#include "Ctls/POSCtls.h"
#include "Includes/POSVoid.h"
#include "Includes/POSSetting.h"
#include "Comm/V5Comm.h"
#include "Includes/POSOffline.h"
#include "Includes/POSRefund.h"
#include "Includes/POSTipAdjust.h"
#include "Includes/POSAuth.h"
#include "Includes/POSSaleComplete.h"
#include "Includes/POSEPPSale.h"
#include "Includes/POSEPPVoid.h"
#include "Includes/POSSettlement.h"
#include "Print/Print.h"
#include <ctosapi.h>
#include <stdlib.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <stdio.h>
#include <sys/un.h>
#include <unistd.h>
#include <dirent.h>
#include <vegaapi.h>
#include <android_jni_log.h>
#include <sbdf_kms2.h>
#include <stdbool.h>
#include <linux/termios.h>
#include <sys/ioctl.h>
#include <termios.h>

sqlite3 * db;
sqlite3_stmt *stmt;

extern JavaVM *jvm;
extern jclass activityClass;
extern jobject activityObj;
extern JNIEnv *g_env;

extern int g_inAPPAotoTest;

JavaVM *g_JavaVM;
extern jobject g_callback_obj_ctos;
extern jmethodID  g_callback_mid_bring2Front;

jmethodID g_callback_mid_lcd;
jmethodID  g_callback_mid_clear;
jmethodID  g_callback_mid_aligned;

jobject g_callback_obj_input;
jmethodID  g_callback_mid_showkeypad;
jmethodID  g_callback_mid_keypadmsg;
jmethodID  g_callback_mid_keypaddone;
jmethodID  g_callback_mid_InputAmountEx;

JavaVM *g_vm;

JNIEXPORT jint
JNICALL
Java_com_Source_S1_1NPX_NPX_Main_MainActivity_inCTOSS_1Sale(JNIEnv *env, jobject instance) {
    // TODO

    int inRetVal = 0;
    BYTE outbuf[d_MAX_IPC_BUFFER];
    USHORT out_len = 0;
    EMVCL_RC_DATA_EX stRCDataEx;

    char cwd[1024];

	int inPID=0;

// patrick test public folder 20190104 start	
/*
    inRetVal = inTCTRead(1);
    vdDebug_LogPrintf("inTCTRead inRetVal[%d] strTCT.fDemo [%d]", inRetVal, strTCT.fDemo);
    strTCT.byRS232DebugPort = 8;
    strTCT.fDemo= 0;
    strTCT.fShareComEnable = 1;
		
    inRetVal = inTCTSave(1);
    vdDebug_LogPrintf("inTCTSave inRetVal[%d]", inRetVal);


    jint rs = (*env)->GetJavaVM(env, &jvm);

    jclass cls = (*env)->GetObjectClass(env, instance);
    activityClass = (jclass) (*env)->NewGlobalRef(env, cls);
    activityObj = (*env)->NewGlobalRef(env, instance);
*/
// patrick test public folder 20190104 end

    vdDebug_LogPrintf("inCTOSS_Sale.............");

//	inCTOSS_CheckBatteryChargeStatus();
//    vdDebug_LogPrintf("inCTOSS_CheckBatteryChargeStatus");
	
//	inCTOS_GetCardFields();
    //inCTOSS_COMM_Connect_Test();

/*
	vdCTOS_TxnsBeginInit();
	vdCTOS_SetTransType(SALE);
	inHDTRead(1);
	srTransRec.HDTid = 17;
	srTransRec.byTransType = SALE;
	strTCT.fDemo = 0;
	
	// do testing for test environment
	vdCTOSS_EFT_TestNetsHardcodeKey();

	inBuildAndSendIsoData();

    return 0;		
*/
//	vdCTOS_InitWaveData();

	//usCTOSS_CtlsV3Trans("000000008888", "000000000000", "00", "0702", "0702",&stRCDataEx);

//	inPrintTesting();

	//inCTOS_PrintSettleReport();


    //inRetVal = find_pid_of("com.Source.S1_VisaMaster.VisaMaster");
    //vdDebug_LogPrintf("process id %d\n", inRetVal);

    //   inMultiAP_RunIPCCmdTypes("com.Source.S1_Amex.Amex",d_IPC_CMD_ONLINES_SALE,"", 0, outbuf,&out_len);
//	inCallJAVA_ForkSubAP("com.Source.S1_Amex.Amex");
//	inRetVal = inMTAClient();
/*
	inCPTRead(17);

	strCPT.inCommunicationMode = GPRS_MODE;

    if (inCTOS_InitComm(strCPT.inCommunicationMode) != d_OK) 
    {
        return(d_NO);
    }

	inCTOS_CheckInitComm(strCPT.inCommunicationMode); 
	if (srCommFuncPoint.inCheckComm(&srTransRec) != d_OK)
	{
		inCTOS_inDisconnect();
		return(d_NO);
	}

	if (srCommFuncPoint.inConnect(&srTransRec) != ST_SUCCESS)
	{
		inCTOS_inDisconnect();
		return ST_ERROR;
	}

	inCTOS_inDisconnect();
*/
	UCHAR bKey[20];

//    inCTOS_SALE();

//	vdCTOS_InitWaveData();
	inCTOS_WAVE_SALE();
	
    return 0;
}

JNIEXPORT jint
JNICALL
Java_com_Source_S1_1NPX_NPX_Main_MainActivity_inCTOSS_1Sale1(JNIEnv *env, jobject instance) {
    // TODO

    int inRetVal = 0;
    BYTE outbuf[d_MAX_IPC_BUFFER];
    USHORT out_len = 0;
    EMVCL_RC_DATA_EX stRCDataEx;

    char cwd[1024];

    inRetVal = inTCTRead(1);
    vdDebug_LogPrintf("inTCTRead inRetVal[%d] strTCT.fDemo [%d]", inRetVal, strTCT.fDemo);
    strTCT.byRS232DebugPort = 8;
    strTCT.fDemo= 1;
    /*Here do test init, load TCT PRM*/
    strTCT.fShareComEnable = 1;
		
    inRetVal = inTCTSave(1);
    vdDebug_LogPrintf("inTCTSave inRetVal[%d]", inRetVal);

    
    inCTOS_SALE();
    return 0;
}

JNIEXPORT jint
JNICALL
Java_com_Source_S1_1NPX_NPX_Main_MainActivity_inDisplayMsg1(JNIEnv *env, jobject instance) {


    int inRetVal = 0;
    BYTE outbuf[d_MAX_IPC_BUFFER];
    USHORT out_len = 0;

    char cwd[1024];

    inTCTRead(1);
    strTCT.byRS232DebugPort = 8;
    inTCTSave(1);

    jint rs = (*env)->GetJavaVM(env, &jvm);

    jclass cls = (*env)->GetObjectClass(env, instance);
    activityClass = (jclass) (*env)->NewGlobalRef(env, cls);
    activityObj = (*env)->NewGlobalRef(env, instance);

    vdDebug_LogPrintf("inDisplayMsg");
//    inDisplayMsg1();


	if(activityClass != NULL)
		 (*env)->DeleteGlobalRef(env, activityClass);
	 if(activityObj != NULL)
		 (*env)->DeleteGlobalRef(env, activityObj);

     (*env)->DeleteLocalRef(env, cls);


    return 0;
}

int inDisplayImage(void)
{
   char szFileName[50+1];
   memset(szFileName, 0x00, sizeof(szFileName));
 
   sprintf(szFileName, "%s" , "p1.png");
   CTOS_LCDGShowBMPPic(0, 800, szFileName);

    return d_OK;
}

JNIEXPORT jint
JNICALL
Java_com_Source_S1_1NPX_NPX_Main_MainActivity_inDisplayImage(JNIEnv *env, jobject instance) {

    int inRetVal = 0;
    BYTE outbuf[d_MAX_IPC_BUFFER];
    USHORT out_len = 0;

    char cwd[1024];

    inTCTRead(1);
    strTCT.byRS232DebugPort = 8;
    inTCTSave(1);

    jint rs = (*env)->GetJavaVM(env, &jvm);

    jclass cls = (*env)->GetObjectClass(env, instance);
    activityClass = (jclass) (*env)->NewGlobalRef(env, cls);
    activityObj = (*env)->NewGlobalRef(env, instance);

    vdDebug_LogPrintf("inDisplayImage");
    inDisplayImage();

	
	if(activityClass != NULL)
		 (*env)->DeleteGlobalRef(env, activityClass);
	 if(activityObj != NULL)
		 (*env)->DeleteGlobalRef(env, activityObj);

     (*env)->DeleteLocalRef(env, cls);

    return 0;
}

int inCallJAVA_LCDTPrintAligned(char *pszMsg)
{
    unsigned char uszBuffer[100+1];
    int inRet = 0;

    vdDebug_LogPrintf("=====LCDTPrintAligned=====");

    JNIEnv *env;
    jint rs = (*jvm)->AttachCurrentThread(jvm, &env, NULL);
    // Use the env pointer...
    vdDebug_LogPrintf("jint[%d] *env[%x]", rs, *env);

    vdDebug_LogPrintf("jstring[%s]", "This comes from LCDTPrintAligned.");

    jstring jstr = (*env)->NewStringUTF(env, pszMsg);
    vdDebug_LogPrintf("jstring[%s][%s]", "This this Pass in string data to Java", pszMsg);

    jclass clazz = (*env)->FindClass(env, "com/Source/S1_NPX/NPX/Main/MainActivity");
    vdDebug_LogPrintf("jstring[%s]", "com/Source/S1_NPX/NPX/Main/MainActivity");

//	jmethodID methodID = (*env)->GetMethodID(env, clazz, "rndGetNumStr", "(Ljava/lang/String;)Ljava/lang/String;");
    jmethodID methodID = (*env)->GetMethodID(env, activityClass, "displayMultipleMsg", "(Ljava/lang/String;)Ljava/lang/String;");

    vdDebug_LogPrintf("jstring[%s]", "LCDTPrintAligned");

    jobject result = (*env)->CallObjectMethod(env, activityObj, methodID, jstr);
    //rs = (*jvm)->DetachCurrentThread(jvm);

    jbyte* str = NULL; //(*env)->GetStringUTFChars(env,(jstring) result, NULL); // should be released but what a heck, it's a tutorial :)
    str = (*env)->GetStringUTFChars(env,(jstring) result, NULL); // should be released but what a heck, it's a tutorial :)
    if (str!=NULL)
    {
        vdDebug_LogPrintf("%s", str);
        vdDebug_LogPrintf("strcpy");

        memset(uszBuffer, 0x00, sizeof(uszBuffer));
        strcpy(uszBuffer, str);

        vdDebug_LogPrintf("ReleaseStringUTFChars");
        (*env)->ReleaseStringUTFChars(env, result, str);

    }
    else
    {
        vdDebug_LogPrintf("get nothing...");
    }

    (*env)->DeleteLocalRef(env, jstr);
    (*env)->DeleteLocalRef(env, clazz);
    (*env)->DeleteLocalRef(env, result);	

    vdDebug_LogPrintf("end LCDTPrintAligned");

    if(0 == strcmp(uszBuffer, "OK"))
        return d_OK;
    else
        return d_NO;
}


int inCallJAVA_LCDTPrintXY(int x, int y, char *pszMsg)
{
    unsigned char uszBuffer[100+1];
    int inRet = 0;

    vdDebug_LogPrintf("=====LCDTPrintAligned=====");

    JNIEnv *env;
    jint rs = (*jvm)->AttachCurrentThread(jvm, &env, NULL);
    // Use the env pointer...
    vdDebug_LogPrintf("jint[%d] *env[%x]", rs, *env);

    vdDebug_LogPrintf("jstring[%s]", "This comes from LCDTPrintAligned.");

    jstring jstr = (*env)->NewStringUTF(env, pszMsg);
    vdDebug_LogPrintf("jstring[%s][%s]", "This this Pass in string data to Java", pszMsg);

    jclass clazz = (*env)->FindClass(env, "com/Source/S1_NPX/NPX/Main/MainActivity");
    vdDebug_LogPrintf("jstring[%s]", "com/Source/S1_NPX/NPX/Main/MainActivity");

//	jmethodID methodID = (*env)->GetMethodID(env, clazz, "rndGetNumStr", "(Ljava/lang/String;)Ljava/lang/String;");
    jmethodID methodID = (*env)->GetMethodID(env, activityClass, "printbyXY", "(IILjava/lang/String;)Ljava/lang/String;");

    vdDebug_LogPrintf("jstring[%s]", "LCDTPrintXY");

    jobject result = (*env)->CallObjectMethod(env, activityObj, methodID, x, y, jstr);
    //rs = (*jvm)->DetachCurrentThread(jvm);

    jbyte* str = NULL; //(*env)->GetStringUTFChars(env,(jstring) result, NULL); // should be released but what a heck, it's a tutorial :)
    str = (*env)->GetStringUTFChars(env,(jstring) result, NULL); // should be released but what a heck, it's a tutorial :)
    if (str!=NULL)
    {
        vdDebug_LogPrintf("%s", str);
        vdDebug_LogPrintf("strcpy");

        memset(uszBuffer, 0x00, sizeof(uszBuffer));
        strcpy(uszBuffer, str);

        vdDebug_LogPrintf("ReleaseStringUTFChars");
        (*env)->ReleaseStringUTFChars(env, result, str);

    }
    else
    {
        vdDebug_LogPrintf("get nothing...");
    }

    (*env)->DeleteLocalRef(env, jstr);
    (*env)->DeleteLocalRef(env, clazz);
    (*env)->DeleteLocalRef(env, result);

    vdDebug_LogPrintf("end LCDTPrintXY");

    if(0 == strcmp(uszBuffer, "OK"))
        return d_OK;
    else
        return d_NO;
}



int inCallJAVA_DisplayMultipleMessage(char *pszMsg)
{
    unsigned char uszBuffer[100+1];
    int inRet = 0;

    vdDebug_LogPrintf("=====inCallJAVA_DisplayMultipleMessage=====");

    JNIEnv *env;
    jint rs = (*jvm)->AttachCurrentThread(jvm, &env, NULL);
    // Use the env pointer...
    vdDebug_LogPrintf("jint[%d] *env[%x]", rs, *env);

    vdDebug_LogPrintf("jstring[%s]", "This comes from inCallJAVA_DisplayMultipleMessage.");

    jstring jstr = (*env)->NewStringUTF(env, pszMsg);
    vdDebug_LogPrintf("jstring[%s][%s]", "This this Pass in string data to Java", pszMsg);

    jclass clazz = (*env)->FindClass(env, "com/Source/S1_NPX/NPX/Main/MainActivity");
    vdDebug_LogPrintf("jstring[%s]", "com/Source/S1_NPX/NPX/Main/MainActivity");

//	jmethodID methodID = (*env)->GetMethodID(env, clazz, "rndGetNumStr", "(Ljava/lang/String;)Ljava/lang/String;");
    jmethodID methodID = (*env)->GetMethodID(env, activityClass, "displayMultipleMsg", "(Ljava/lang/String;)Ljava/lang/String;");

    vdDebug_LogPrintf("jstring[%s]", "displayMultipleMsg");

    jobject result = (*env)->CallObjectMethod(env, activityObj, methodID, jstr);
    //rs = (*jvm)->DetachCurrentThread(jvm);

    jbyte* str = NULL; //(*env)->GetStringUTFChars(env,(jstring) result, NULL); // should be released but what a heck, it's a tutorial :)
    str = (*env)->GetStringUTFChars(env,(jstring) result, NULL); // should be released but what a heck, it's a tutorial :)
    if (str!=NULL)
    {
        vdDebug_LogPrintf("%s", str);
        vdDebug_LogPrintf("strcpy");

        memset(uszBuffer, 0x00, sizeof(uszBuffer));
        strcpy(uszBuffer, str);

        vdDebug_LogPrintf("ReleaseStringUTFChars");
        (*env)->ReleaseStringUTFChars(env, result, str);

    }
    else
    {
        vdDebug_LogPrintf("get nothing...");
    }

    (*env)->DeleteLocalRef(env, jstr);
    (*env)->DeleteLocalRef(env, clazz);
    (*env)->DeleteLocalRef(env, result);

    vdDebug_LogPrintf("end inCallJAVA_DisplayMultipleMessage");

    if(0 == strcmp(uszBuffer, "OK"))
        return d_OK;
    else
        return d_NO;
}

int inCallJAVA_DisplayImage(int x, int y, char *pszMsg_Img)
{
    unsigned char uszBuffer[100+1];
    int inRet = 0;

    vdDebug_LogPrintf("=====inCallJAVA_DisplayImage=====");

    JNIEnv *env;
    jint rs = (*jvm)->AttachCurrentThread(jvm, &env, NULL);
    // Use the env pointer...
    vdDebug_LogPrintf("jint[%d] *env[%x]", rs, *env);

    vdDebug_LogPrintf("jstring[%s]", "This comes from inCallJAVA_DisplayImage.");

    jstring jstr = (*env)->NewStringUTF(env, pszMsg_Img);
    vdDebug_LogPrintf("jstring[%s][%s]", "This this Pass in string data to Java", pszMsg_Img);

    jclass clazz = (*env)->FindClass(env, "com/Source/S1_NPX/NPX/Main/MainActivity");
    vdDebug_LogPrintf("jstring[%s]", "com/Source/S1_NPX/NPX/Main/MainActivity");

    jmethodID methodID = (*env)->GetMethodID(env, activityClass, "displayImage", "(IILjava/lang/String;)Ljava/lang/String;");
//    jmethodID methodID = (*env)->GetMethodID(env, activityClass, "displayImage", "(Ljava/lang/String;)Ljava/lang/String;");

    vdDebug_LogPrintf("jstring[%s]", "inCallJAVA_DisplayImage");

    jobject result = (*env)->CallObjectMethod(env, activityObj, methodID, x, y, jstr);
    //rs = (*jvm)->DetachCurrentThread(jvm);

    jbyte* str = NULL; //(*env)->GetStringUTFChars(env,(jstring) result, NULL); // should be released but what a heck, it's a tutorial :)
    str = (*env)->GetStringUTFChars(env,(jstring) result, NULL); // should be released but what a heck, it's a tutorial :)
    if (str!=NULL)
    {
        vdDebug_LogPrintf("%s", str);
        vdDebug_LogPrintf("strcpy");

        memset(uszBuffer, 0x00, sizeof(uszBuffer));
        strcpy(uszBuffer, str);

        vdDebug_LogPrintf("ReleaseStringUTFChars");
        (*env)->ReleaseStringUTFChars(env, result, str);

    }
    else
    {
        vdDebug_LogPrintf("get nothing...");
    }

    (*env)->DeleteLocalRef(env, jstr);
    (*env)->DeleteLocalRef(env, clazz);
    (*env)->DeleteLocalRef(env, result);


    vdDebug_LogPrintf("end inCallJAVA_DisplayImage");

    if(0 == strcmp(uszBuffer, "OK"))
        return d_OK;
    else
        return d_NO;
}

int inCallJAVA_GetAmountString(BYTE *pbDispMsg, BYTE *pbAmtStr, BYTE *pbAmtLen)
{
    unsigned char uszBuffer[100+1];
    int inRet = 0;


    vdDebug_LogPrintf("=====inCallJAVA_GetAmountString=====");

    JNIEnv *env;
    jint rs = (*jvm)->AttachCurrentThread(jvm, &env, NULL);
    // Use the env pointer...
    vdDebug_LogPrintf("jint[%d] *env[%x]", rs, *env);

    jstring jstr = (*env)->NewStringUTF(env, "This this Pass in string data to Java");
    vdDebug_LogPrintf("jstring[%s]", "This this Pass in string data to Java");

    jclass clazz = (*env)->FindClass(env, "com/Source/S1_NPX/NPX/Main/MainActivity");
    vdDebug_LogPrintf("jstring[%s]", "com/Source/S1_NPX/NPX/Main/MainActivity");

//	jmethodID methodID = (*env)->GetMethodID(env, clazz, "rndGetNumStr", "(Ljava/lang/String;)Ljava/lang/String;");
    jmethodID methodID = (*env)->GetMethodID(env, activityClass, "GetAmountString", "(Ljava/lang/String;)Ljava/lang/String;");

    vdDebug_LogPrintf("jstring[%s]", "GetAmountString");

    jobject result = (*env)->CallObjectMethod(env, activityObj, methodID, jstr);
    //rs = (*jvm)->DetachCurrentThread(jvm);

    jbyte* str = NULL; //(*env)->GetStringUTFChars(env,(jstring) result, NULL); // should be released but what a heck, it's a tutorial :)
    str = (*env)->GetStringUTFChars(env,(jstring) result, NULL); // should be released but what a heck, it's a tutorial :)
    if (str!=NULL)
    {
        vdDebug_LogPrintf("%s", str);
        *pbAmtLen = strlen(str);
        vdDebug_LogPrintf("strcpy");
        strcpy(pbAmtStr, str);

        vdDebug_LogPrintf("ReleaseStringUTFChars");
        (*env)->ReleaseStringUTFChars(env, result, str);

    }
    else
        *pbAmtLen = 0;

    (*env)->DeleteLocalRef(env, jstr);
    (*env)->DeleteLocalRef(env, clazz);
    (*env)->DeleteLocalRef(env, result);

    vdDebug_LogPrintf("end inCallJAVA_GetAmountString");
    return d_OK;
}


int inCallJAVA_UserConfirmMenu(BYTE *pbDispMsg, BYTE *pbOutStr, BYTE *pbOutLen)
{
    unsigned char uszBuffer[100+1];
    int inRet = 0;


    vdDebug_LogPrintf("=====inCallJAVA_UserConfirmMenu=====");

    JNIEnv *env;
    jint rs = (*jvm)->AttachCurrentThread(jvm, &env, NULL);
    // Use the env pointer...
    vdDebug_LogPrintf("jint[%d] *env[%x]", rs, *env);


  
    if (strlen(pbDispMsg)>0)
        strcpy(uszBuffer, pbDispMsg);

	
	vdDebug_LogPrintf("uszBuffer[%s]", uszBuffer);

    jstring jstr = (*env)->NewStringUTF(env, uszBuffer);
    vdDebug_LogPrintf("jstring[%s]", uszBuffer);

    jclass clazz = (*env)->FindClass(env, "com/Source/S1_NPX/NPX/Main/MainActivity");
    vdDebug_LogPrintf("jstring[%s]", "com/Source/S1_NPX/NPX/Main/MainActivity");

    jmethodID methodID = (*env)->GetMethodID(env, activityClass, "UserConfirmMenu", "(Ljava/lang/String;)Ljava/lang/String;");

    vdDebug_LogPrintf("jstring[%s]", "UserConfirmMenu");

    jobject result = (*env)->CallObjectMethod(env, activityObj, methodID, jstr);

    jbyte* str = NULL; 
    str = (*env)->GetStringUTFChars(env,(jstring) result, NULL); 
    if (str!=NULL)
    {
        vdDebug_LogPrintf("%s", str);
        *pbOutLen = strlen(str);
        vdDebug_LogPrintf("strcpy");
        strcpy(pbOutStr, str);

        vdDebug_LogPrintf("ReleaseStringUTFChars");
        (*env)->ReleaseStringUTFChars(env, result, str);

    }
    else
        *pbOutLen = 0;

    (*env)->DeleteLocalRef(env, jstr);
    (*env)->DeleteLocalRef(env, clazz);
    (*env)->DeleteLocalRef(env, result);

    vdDebug_LogPrintf("end inCallJAVA_UserConfirmMenu");
    return d_OK;
}


int inCallJAVA_UserSelectUpDown(BYTE *pbDispMsg, BYTE *pbOutStr, BYTE *pbOutLen)
{
    unsigned char uszBuffer[100+1];
    int inRet = 0;


    vdDebug_LogPrintf("=====inCallJAVA_UserSelectUpDown=====");

    JNIEnv *env;
    jint rs = (*jvm)->AttachCurrentThread(jvm, &env, NULL);
    // Use the env pointer...
    vdDebug_LogPrintf("jint[%d] *env[%x]", rs, *env);


  
    if (strlen(pbDispMsg)>0)
        strcpy(uszBuffer, pbDispMsg);

	
	vdDebug_LogPrintf("uszBuffer[%s]", uszBuffer);

    jstring jstr = (*env)->NewStringUTF(env, uszBuffer);
    vdDebug_LogPrintf("jstring[%s]", uszBuffer);

    jclass clazz = (*env)->FindClass(env, "com/Source/S1_NPX/NPX/Main/MainActivity");
    vdDebug_LogPrintf("jstring[%s]", "com/Source/S1_NPX/NPX/Main/MainActivity");

    jmethodID methodID = (*env)->GetMethodID(env, activityClass, "UserSelectUpDown", "(Ljava/lang/String;)Ljava/lang/String;");

    vdDebug_LogPrintf("jstring[%s]", "inCallJAVA_UserSelectUpDown");

    jobject result = (*env)->CallObjectMethod(env, activityObj, methodID, jstr);

    jbyte* str = NULL; 

	if(result != NULL)
	    str = (*env)->GetStringUTFChars(env,(jstring) result, NULL); 
    if (str!=NULL)
    {
        vdDebug_LogPrintf("%s", str);
        *pbOutLen = strlen(str);
        vdDebug_LogPrintf("strcpy");
        strcpy(pbOutStr, str);

        vdDebug_LogPrintf("ReleaseStringUTFChars");
        (*env)->ReleaseStringUTFChars(env, result, str);

    }
    else
        *pbOutLen = 0;

    (*env)->DeleteLocalRef(env, jstr);
    (*env)->DeleteLocalRef(env, clazz);
    (*env)->DeleteLocalRef(env, result);

    vdDebug_LogPrintf("end inCallJAVA_UserSelectUpDown");
    return d_OK;
}


int inCallJAVA_UserInputString(BYTE *pbDispMsg, BYTE *pbOutStr, BYTE *pbOutLen)
{
    unsigned char uszBuffer[100+1];
    int inRet = 0;


    vdDebug_LogPrintf("=====inCallJAVA_UserInputString=====");

    JNIEnv *env;
    jint rs = (*jvm)->AttachCurrentThread(jvm, &env, NULL);
    // Use the env pointer...
    vdDebug_LogPrintf("jint[%d] *env[%x]", rs, *env);

    vdDebug_LogPrintf("pbDispMsg[%s]", pbDispMsg);

    if (strlen(pbDispMsg)>0)
        strcpy(uszBuffer, pbDispMsg);
    else
        strcpy(uszBuffer, "PASS IN MSG");

	
	vdDebug_LogPrintf("uszBuffer[%s]", uszBuffer);

    jstring jstr = (*env)->NewStringUTF(env, uszBuffer);
    vdDebug_LogPrintf("jstring[%s]", uszBuffer);

    jclass clazz = (*env)->FindClass(env, "com/Source/S1_NPX/NPX/Main/MainActivity");
    vdDebug_LogPrintf("jstring[%s]", "com/Source/S1_NPX/NPX/Main/MainActivity");

//	jmethodID methodID = (*env)->GetMethodID(env, clazz, "rndGetNumStr", "(Ljava/lang/String;)Ljava/lang/String;");
    jmethodID methodID = (*env)->GetMethodID(env, activityClass, "UserInputString", "(Ljava/lang/String;)Ljava/lang/String;");

    vdDebug_LogPrintf("jstring[%s]", "UserInputString");

    jobject result = (*env)->CallObjectMethod(env, activityObj, methodID, jstr);
    //rs = (*jvm)->DetachCurrentThread(jvm);

    jbyte* str = NULL; //(*env)->GetStringUTFChars(env,(jstring) result, NULL); // should be released but what a heck, it's a tutorial :)
    str = (*env)->GetStringUTFChars(env,(jstring) result, NULL); // should be released but what a heck, it's a tutorial :)
    if (str!=NULL)
    {
        vdDebug_LogPrintf("%s", str);
        *pbOutLen = strlen(str);
        vdDebug_LogPrintf("strcpy");
        strcpy(pbOutStr, str);

        vdDebug_LogPrintf("ReleaseStringUTFChars");
        (*env)->ReleaseStringUTFChars(env, result, str);

    }
    else
        *pbOutLen = 0;

    (*env)->DeleteLocalRef(env, jstr);
    (*env)->DeleteLocalRef(env, clazz);
    (*env)->DeleteLocalRef(env, result);

    vdDebug_LogPrintf("end inCallJAVA_UserInputString");
    return d_OK;
}


int inCallJAVA_EnterAnyNum(char *pbNumLen, char *pbaNum)
{
    unsigned char uszBuffer[100+1];
    int inRet = 0;

    char baPINBlk[8];

    vdDebug_LogPrintf("=====inCallJAVA_EnterAnyNum=====");

    JNIEnv *env;
    jint rs = (*jvm)->AttachCurrentThread(jvm, &env, NULL);
    // Use the env pointer...
    vdDebug_LogPrintf("jint[%d] *env[%x]", rs, *env);

    vdDebug_LogPrintf("jstring[%s]", "This comes from CallJavaForNumString.");

    jstring jstr = (*env)->NewStringUTF(env, "This this Pass in string data to Java");
    vdDebug_LogPrintf("jstring[%s]", "This this Pass in string data to Java");

    jclass clazz = (*env)->FindClass(env, "com/Source/S1_NPX/NPX/Main/MainActivity");
    vdDebug_LogPrintf("jstring[%s]", "com/Source/S1_NPX/NPX/Main/MainActivity");

//	jmethodID methodID = (*env)->GetMethodID(env, clazz, "rndGetNumStr", "(Ljava/lang/String;)Ljava/lang/String;");
    jmethodID methodID = (*env)->GetMethodID(env, activityClass, "getAnyNumStr", "(Ljava/lang/String;)Ljava/lang/String;");

    vdDebug_LogPrintf("jstring[%s]", "getAnyNumStr");

    jobject result = (*env)->CallObjectMethod(env, activityObj, methodID, jstr);
    //rs = (*jvm)->DetachCurrentThread(jvm);

    jbyte* str = NULL; //(*env)->GetStringUTFChars(env,(jstring) result, NULL); // should be released but what a heck, it's a tutorial :)
    str = (*env)->GetStringUTFChars(env,(jstring) result, NULL); // should be released but what a heck, it's a tutorial :)
    if (str!=NULL)
    {
        vdDebug_LogPrintf("%s", str);
        *pbNumLen = strlen(str);
        vdDebug_LogPrintf("strcpy");
        strcpy(pbaNum, str);

        vdDebug_LogPrintf("ReleaseStringUTFChars");
        (*env)->ReleaseStringUTFChars(env, result, str);


        vdDebug_LogPrintf("Copy data back");
        if (0 == strcmp(str, "BYPASS"))
            *pbNumLen = 0;
    }
    else
        *pbNumLen = 0;

    (*env)->DeleteLocalRef(env, jstr);
    (*env)->DeleteLocalRef(env, clazz);
    (*env)->DeleteLocalRef(env, result);

    vdDebug_LogPrintf("end inCallJAVA_EnterAnyNum");
    return d_OK;
}

int inCallJAVA_DOptionMenuDisplay(BYTE *pbDispMsg, BYTE *pbOutStr, BYTE *pbOutLen)
{
    unsigned char uszBuffer[100+1];
    int inRet = 0;


    vdDebug_LogPrintf("=====inCallJAVA_DOptionMenuDisplay=====");

    JNIEnv *env;
    jint rs = (*jvm)->AttachCurrentThread(jvm, &env, NULL);
    // Use the env pointer...
    vdDebug_LogPrintf("jint[%d] *env[%x]", rs, *env);

    vdDebug_LogPrintf("pbDispMsg[%s]", pbDispMsg);

    if (strlen(pbDispMsg)>0)
        strcpy(uszBuffer, pbDispMsg);
    else
        strcpy(uszBuffer, "PASS IN MSG");

    jstring jstr = (*env)->NewStringUTF(env, uszBuffer);
    vdDebug_LogPrintf("jstring[%s]", uszBuffer);

    jclass clazz = (*env)->FindClass(env, "com/Source/S1_NPX/NPX/Main/MainActivity");
    vdDebug_LogPrintf("jstring[%s]", "com/Source/S1_NPX/NPX/Main/MainActivity");

//	jmethodID methodID = (*env)->GetMethodID(env, clazz, "rndGetNumStr", "(Ljava/lang/String;)Ljava/lang/String;");
    jmethodID methodID = (*env)->GetMethodID(env, activityClass, "DOptionMenuDisplay", "(Ljava/lang/String;)Ljava/lang/String;");

    vdDebug_LogPrintf("jstring[%s]", "DOptionMenuDisplay");

    jobject result = (*env)->CallObjectMethod(env, activityObj, methodID, jstr);
    //rs = (*jvm)->DetachCurrentThread(jvm);

    jbyte* str = NULL; //(*env)->GetStringUTFChars(env,(jstring) result, NULL); // should be released but what a heck, it's a tutorial :)
    str = (*env)->GetStringUTFChars(env,(jstring) result, NULL); // should be released but what a heck, it's a tutorial :)
    if (str!=NULL)
    {
        vdDebug_LogPrintf("%s", str);
        *pbOutLen = strlen(str);
        vdDebug_LogPrintf("strcpy");
        strcpy(pbOutStr, str);

        vdDebug_LogPrintf("ReleaseStringUTFChars");
        (*env)->ReleaseStringUTFChars(env, result, str);

    }
    else
        *pbOutLen = 0;

    (*env)->DeleteLocalRef(env, jstr);
    (*env)->DeleteLocalRef(env, clazz);
    (*env)->DeleteLocalRef(env, result);

    vdDebug_LogPrintf("end inCallJAVA_DOptionMenuDisplay");
    return d_OK;
}

int inCallJAVA_DPopupMenuDisplay(BYTE *pbDispMsg, BYTE *pbOutStr, BYTE *pbOutLen)
{
    unsigned char uszBuffer[100+1];
    int inRet = 0;


    vdDebug_LogPrintf("=====inCallJAVA_DPopupMenuDisplay=====");

    JNIEnv *env;
    jint rs = (*jvm)->AttachCurrentThread(jvm, &env, NULL);
    // Use the env pointer...
    vdDebug_LogPrintf("jint[%d] *env[%x]", rs, *env);

    vdDebug_LogPrintf("pbDispMsg[%s]", pbDispMsg);

    if (strlen(pbDispMsg)>0)
        strcpy(uszBuffer, pbDispMsg);
    else
        strcpy(uszBuffer, "PASS IN MSG");

    jstring jstr = (*env)->NewStringUTF(env, uszBuffer);
    vdDebug_LogPrintf("jstring[%s]", uszBuffer);

    jclass clazz = (*env)->FindClass(env, "com/Source/S1_NPX/NPX/Main/MainActivity");
    vdDebug_LogPrintf("jstring[%s]", "com/Source/S1_NPX/NPX/Main/MainActivity");

//	jmethodID methodID = (*env)->GetMethodID(env, clazz, "rndGetNumStr", "(Ljava/lang/String;)Ljava/lang/String;");
    jmethodID methodID = (*env)->GetMethodID(env, activityClass, "DPopupMenuDisplay", "(Ljava/lang/String;)Ljava/lang/String;");

    vdDebug_LogPrintf("jstring[%s]", "DPopupMenuDisplay");

    jobject result = (*env)->CallObjectMethod(env, activityObj, methodID, jstr);
    //rs = (*jvm)->DetachCurrentThread(jvm);

    jbyte* str = NULL; //(*env)->GetStringUTFChars(env,(jstring) result, NULL); // should be released but what a heck, it's a tutorial :)
    str = (*env)->GetStringUTFChars(env,(jstring) result, NULL); // should be released but what a heck, it's a tutorial :)
    if (str!=NULL)
    {
        vdDebug_LogPrintf("%s", str);
        *pbOutLen = strlen(str);
        vdDebug_LogPrintf("strcpy");
        strcpy(pbOutStr, str);

        vdDebug_LogPrintf("ReleaseStringUTFChars");
        (*env)->ReleaseStringUTFChars(env, result, str);

    }
    else
        *pbOutLen = 0;

    (*env)->DeleteLocalRef(env, jstr);
    (*env)->DeleteLocalRef(env, clazz);
    (*env)->DeleteLocalRef(env, result);

    vdDebug_LogPrintf("end inCallJAVA_DPopupMenuDisplay");
    return d_OK;
}

/*below is the new way to display UI*/

USHORT CTOS_LCDTClearDisplay(void)
{
    JNIEnv *tenv;
    LOGD("---JNI AP CB--- CTOS_LCDTClearDisplay\n");
    if((*g_JavaVM)->AttachCurrentThread(g_JavaVM, &tenv, NULL) != JNI_OK)
    {
        // attachCurrentThread() failed.
    }
    else {
        (*tenv)->CallLongMethod(tenv, g_callback_obj_ctos, g_callback_mid_clear);
    }
    return 0;
}


USHORT CTOS_LCDTPrintXY(USHORT usX, USHORT usY, UCHAR* pbBuf)
{
    JNIEnv *tenv;
    jshort jsusX;
    jshort jsusY;

    LOGD("---JNI AP CB--- CTOS_LCDTPrintXY\n");
    if((*g_JavaVM)->AttachCurrentThread(g_JavaVM, &tenv, NULL) != JNI_OK)
    {
        // attachCurrentThread() failed.
    }
    else {
        jsusX = (jshort) usX;
        jsusY = (jshort) usY;
        jstring str = (*tenv)->NewStringUTF(tenv, (char*)pbBuf);
        (*tenv)->CallLongMethod(tenv, g_callback_obj_ctos, g_callback_mid_lcd, jsusX, jsusY, str);
	 (*tenv)->DeleteLocalRef(tenv, str);
    }



    return 0;
}


USHORT CTOS_LCDTPrintAligned(USHORT usY, UCHAR* pbBuf, BYTE bMode)
{
    JNIEnv *tenv;
    jshort jsusY;
    jbyte jbbMode;

    LOGD("---JNI AP CB--- CTOS_LCDTPrintAligned\n");
    if((*g_JavaVM)->AttachCurrentThread(g_JavaVM, &tenv, NULL) != JNI_OK)
    {
        // attachCurrentThread() failed.
    }
    else {
        jsusY = (jshort) usY;
        jbbMode = (jbyte) bMode;
        jstring str = (*tenv)->NewStringUTF(tenv, (char*)pbBuf);
        (*tenv)->CallLongMethod(tenv, g_callback_obj_ctos, g_callback_mid_aligned, jsusY, str, jbbMode);
	 (*tenv)->DeleteLocalRef(tenv, str);
    }
    return 0;
}



JNIEXPORT jint JNICALL JNI_OnLoad(JavaVM *vm, void *reserved)
{
    JNIEnv *env;
    g_vm = vm;
    if ((*vm)->GetEnv(vm, (void **) &env, JNI_VERSION_1_6) != JNI_OK) {
        return JNI_ERR; // JNI version not supported.
    }

    return JNI_VERSION_1_6;
}

JNIEXPORT jlong JNICALL Java_com_Source_S1_1NPX_NPX_Main_JNI_1Lib_REG_1CB_1CTOS(
        JNIEnv *env, jobject obj, jobject instance)
{
    int Rtn = 0;

    (*env)->GetJavaVM(env, &g_JavaVM);
    g_callback_obj_ctos = (*env)->NewGlobalRef(env,instance);
    jclass clz = (*env)->GetObjectClass(env,g_callback_obj_ctos);

    if(clz == NULL)
    {
        //failed to find class
    }

    g_callback_mid_lcd = (*env)->GetMethodID(env, clz, "CTOS_LCDTPrintXY", "(SSLjava/lang/String;)J");
    g_callback_mid_aligned = (*env)->GetMethodID(env, clz, "CTOS_LCDTPrintAligned", "(SLjava/lang/String;B)J");
    g_callback_mid_clear = (*env)->GetMethodID(env, clz, "CTOS_LCDTClearDisplay", "()J");

    //g_callback_mid_pin_result = env->GetMethodID(clz, "CTOS_PINGetResult", "([B)J");

    JavaVM *javaVM = g_vm;
    jint res = (*javaVM)->GetEnv(javaVM, (void **) &env, JNI_VERSION_1_6);
    if (res != JNI_OK) {
        res = (*javaVM)->AttachCurrentThread(javaVM, &env, NULL);
        if (JNI_OK != res) {
            LOGE("Failed to AttachCurrentThread, ErrorCode = %d", res);

        }
    }


    return Rtn;
}


int ShowVirtualKeypPad(OUT USHORT *pusKeyPadButtonNum, OUT BYTE *pbKeyPadButtonInfo, OUT USHORT *pusKeyPadButtonInfoLen)
{
    JNIEnv* env = NULL;

    LOGD("---JNI AP CB--- ShowVirtualKeypPad\n");

    //inCallJAVA_GetAmountString(pusKeyPadButtonNum, pbKeyPadButtonInfo, pusKeyPadButtonInfoLen);
    //return d_OK;

    if((*g_JavaVM)->AttachCurrentThread(g_JavaVM, &env, NULL) != JNI_OK)
    {
        // attachCurrentThread() failed.
    }
    else {

    }

    int buffsize = 1024;
    int i = 0;

    BYTE baInputbuffer[1024];
    BYTE baOutputbuffer[1024];

    //jbyte *by = (jbyte*)baInputbuffer;

    jbyteArray jarray = (*env)->NewByteArray(env, buffsize);

    (*env)->SetByteArrayRegion(env, jarray, 0, buffsize, (jbyte*)baInputbuffer);

    LOGE("buffsize= %d", buffsize);

    (*env)->CallLongMethod(env, g_callback_obj_input, g_callback_mid_showkeypad, jarray);

    //jsize len = env->GetArrayLength (jarray);
    //unsigned char* buf = new unsigned char[len];

    (*env)->GetByteArrayRegion (env, jarray, 0, buffsize, (jbyte*)(baOutputbuffer));

    *pusKeyPadButtonNum = baOutputbuffer[0];

    LOGE("baOutputbuffer[0]= %02X", baOutputbuffer[0]);
    LOGE("baOutputbuffer[1]= %02X", baOutputbuffer[1]);
    LOGE("baOutputbuffer[2]= %02X", baOutputbuffer[2]);

    *pusKeyPadButtonInfoLen = 0;
    *pusKeyPadButtonInfoLen += (baOutputbuffer[1]*256);
    *pusKeyPadButtonInfoLen += (baOutputbuffer[2]);

    LOGE("pusPINPadButtonNum= %d", *pusKeyPadButtonNum);
    LOGE("pusPINPadButtonInfoLen= %d", *pusKeyPadButtonInfoLen);


    //memcpy(pbKeyPadButtonInfo, &baOutputbuffer[3], *pusKeyPadButtonInfoLen);

    return 0;
}


int GetKeyPadDone(void)
{
    JNIEnv *tenv;
    jshort jsusX;
    jshort jsusY;

    LOGD("---JNI AP CB--- GetKeyPadDone\n");
    if((*g_JavaVM)->AttachCurrentThread(g_JavaVM, &tenv, NULL) != JNI_OK)
    {
        // attachCurrentThread() failed.
    }
    else {
        (*tenv)->CallLongMethod(tenv, g_callback_obj_input, g_callback_mid_keypaddone);
    }
    return 0;

}


int ShowKeyPadMsg(BYTE digitsNum, BYTE bPINType, BYTE bRemainingCounter)
{
    JNIEnv *tenv;
    jbyte digits;


    LOGD("---JNI AP CB--- ShowKeyPadMsg\n");
    if((*g_JavaVM)->AttachCurrentThread(g_JavaVM, &tenv, NULL) != JNI_OK)
    {
        // attachCurrentThread() failed.
    }
    else {
        digits = (jbyte) digitsNum;
        (*tenv)->CallLongMethod(g_callback_obj_input, g_callback_mid_keypadmsg, digits, bPINType, bRemainingCounter);
    }
    return 0;

}


BYTE IPT_InputAmountEx(USHORT usX, USHORT usY, BYTE *szCurSymbol, BYTE exponent, BYTE first_key, BYTE *baAmount, ULONG *ulAmount, USHORT usTimeOutMS, BYTE bIgnoreEnter)
{
    int inLen = 0;
    int inRet = 0;

    BYTE szInBuf[128];
    BYTE szOutBuf[128];

    /*Still use the old way make it work first*/
    inRet = inCallJAVA_GetAmountString(szInBuf, szOutBuf, &inLen);

    if (strlen(baAmount)>0 && inLen>0)
        *ulAmount = 5000;
    else
        *ulAmount = 0;

    return d_OK;

}


JNIEXPORT jlong JNICALL
Java_com_Source_S1_1NPX_NPX_Main_JNI_1Lib_REG_1CB_1IPT(JNIEnv *env,jobject obj,  jobject instance)
{
    int Rtn = 0;

    (*env)->GetJavaVM(env, &g_JavaVM);
    g_callback_obj_input = (*env)->NewGlobalRef(env,instance);
    jclass clz = (*env)->GetObjectClass(env,g_callback_obj_input);

    if(clz == NULL)
    {
        //failed to find class
    }

    g_callback_mid_showkeypad = (*env)->GetMethodID(env, clz, "ShowVirtualKeyPad", "([B)J");
    g_callback_mid_keypadmsg= (*env)->GetMethodID(env, clz, "ShowKeyPadMsg", "(BBB)J");
    g_callback_mid_keypaddone = (*env)->GetMethodID(env, clz, "GetKeyPadDone", "()J");
    g_callback_mid_InputAmountEx = (*env)->GetMethodID(env, clz, "IPT_InputAmountEx", "([B)J");



    JavaVM *javaVM = g_vm;
    jint res = (*javaVM)->GetEnv(javaVM, (void **) &env, JNI_VERSION_1_6);
    if (res != JNI_OK) {
        res = (*javaVM)->AttachCurrentThread(javaVM, &env, NULL);
        if (JNI_OK != res) {
            LOGE("Failed to AttachCurrentThread, ErrorCode = %d", res);

        }
    }

    return Rtn;
}

JNIEXPORT jint
JNICALL
Java_com_Source_S1_1NPX_NPX_Main_MainActivity_inCTOSS_1WAVE_1SALE(JNIEnv *env, jobject instance) {
    int inRetVal = 0;

  //  jint rs = (*env)->GetJavaVM(env, &jvm);

  //  jclass cls = (*env)->GetObjectClass(env, instance);
  //  activityClass = (jclass) (*env)->NewGlobalRef(env, cls);
   // activityObj = (*env)->NewGlobalRef(env, instance);

// patrick test public folder 20190104 start
    strTCT.byRS232DebugPort = 8;

    vdDebug_LogPrintf("inCTOS_WAVE_SALE.............");
    inRetVal = inTCTRead(1);
    vdDebug_LogPrintf("inTCTRead inRetVal[%d] strTCT.fDemo [%d] strTCT.fECR[%d]", inRetVal, strTCT.fDemo, strTCT.fECR);

    strTCT.byRS232DebugPort = 8;
    strTCT.fDemo= 0;
    /*Here do test init, load TCT PRM*/
    strTCT.fShareComEnable = 1;

    inRetVal = inTCTSave(1);
    vdDebug_LogPrintf("inTCTSave inRetVal[%d]", inRetVal);
// patrick test public folder 20190104 end

//	g_inAPPAotoTest = get_env_int("AUTOTEST");
//	g_inAPPAotoTest = 1;
	vdSetAppRunBySelf(1);

    if (inCTOS_GetAutoTestCnt() > 1)
    {
        do
        {
            inCTOS_WAVE_SALE();
        }while(1);
    }
    else
    {
        inCTOS_WAVE_SALE();
    }

	
	vdSetAppRunBySelf(0);

    return 0;
}

JNIEXPORT jint
JNICALL
Java_com_Source_S1_1NPX_NPX_Main_MainActivity_inCTOSS_1BATCH_1REVIEW(JNIEnv *env, jobject instance) {
    int inRetVal = 0;

   // jint rs = (*env)->GetJavaVM(env, &jvm);

   // jclass cls = (*env)->GetObjectClass(env, instance);
   // activityClass = (jclass) (*env)->NewGlobalRef(env, cls);
   // activityObj = (*env)->NewGlobalRef(env, instance);

// patrick test public folder 20190104 start
    strTCT.byRS232DebugPort = 8;

    vdDebug_LogPrintf("inCTOSS_BATCH_REVIEW.............");
    inRetVal = inTCTRead(1);
    vdDebug_LogPrintf("inTCTRead inRetVal[%d] strTCT.fDemo [%d]", inRetVal, strTCT.fDemo);
    strTCT.byRS232DebugPort = 8;
    strTCT.fDemo= 0;
    /*Here do test init, load TCT PRM*/
    strTCT.fShareComEnable = 1;

    inRetVal = inTCTSave(1);
    vdDebug_LogPrintf("inTCTSave inRetVal[%d]", inRetVal);
// patrick test public folder 20190104 end

//	g_inAPPAotoTest = get_env_int("AUTOTEST");
//	g_inAPPAotoTest = 1;


    inCTOS_BATCH_REVIEW();


    return 0;
}

JNIEXPORT jint
JNICALL
Java_com_Source_S1_1NPX_NPX_Main_MainActivity_inCTOSS_1VOID(JNIEnv *env, jobject instance) {

   // jint rs = (*env)->GetJavaVM(env, &jvm);

    //jclass cls = (*env)->GetObjectClass(env, instance);
   // activityClass = (jclass) (*env)->NewGlobalRef(env, cls);
   // activityObj = (*env)->NewGlobalRef(env, instance);

    vdDebug_LogPrintf("inCTOS_VOID.............");
    inCTOS_VOID();

    return 0;
}

JNIEXPORT jint
JNICALL
Java_com_Source_S1_1NPX_NPX_Main_MainActivity_inCTOSS_1SALE_1OFFLINE(JNIEnv *env, jobject instance) {

   // jint rs = (*env)->GetJavaVM(env, &jvm);

   // jclass cls = (*env)->GetObjectClass(env, instance);
    //activityClass = (jclass) (*env)->NewGlobalRef(env, cls);
   // activityObj = (*env)->NewGlobalRef(env, instance);

    vdDebug_LogPrintf("inCTOS_SALE_OFFLINE.............");
    inCTOS_SALE_OFFLINE();

    return 0;
}

JNIEXPORT jint
JNICALL
Java_com_Source_S1_1NPX_NPX_Main_MainActivity_inCTOSS_1REFUND(JNIEnv *env, jobject instance) {

   // jint rs = (*env)->GetJavaVM(env, &jvm);

    //jclass cls = (*env)->GetObjectClass(env, instance);
    //activityClass = (jclass) (*env)->NewGlobalRef(env, cls);
   // activityObj = (*env)->NewGlobalRef(env, instance);

    vdDebug_LogPrintf("inCTOS_REFUND.............");
    inCTOS_REFUND();

    return 0;
}

JNIEXPORT jint
JNICALL
Java_com_Source_S1_1NPX_NPX_Main_MainActivity_inCTOSS_1TIPADJUST(JNIEnv *env, jobject instance) {

    //jint rs = (*env)->GetJavaVM(env, &jvm);

   // jclass cls = (*env)->GetObjectClass(env, instance);
   // activityClass = (jclass) (*env)->NewGlobalRef(env, cls);
    //activityObj = (*env)->NewGlobalRef(env, instance);

    vdDebug_LogPrintf("inCTOS_TIPADJUST.............");
    inCTOS_TIPADJUST();

    return 0;
}

JNIEXPORT jint
JNICALL
Java_com_Source_S1_1NPX_NPX_Main_MainActivity_inCTOSS_1PREAUTH(JNIEnv *env, jobject instance) {

   // jint rs = (*env)->GetJavaVM(env, &jvm);

   // jclass cls = (*env)->GetObjectClass(env, instance);
   // activityClass = (jclass) (*env)->NewGlobalRef(env, cls);
    //activityObj = (*env)->NewGlobalRef(env, instance);

    vdDebug_LogPrintf("inCTOS_PREAUTH.............");
    inCTOS_PREAUTH();

    return 0;
}

JNIEXPORT jint
JNICALL
Java_com_Source_S1_1NPX_NPX_Main_MainActivity_inCTOSS_1SALECOMPLETE(JNIEnv *env, jobject instance) {

   // jint rs = (*env)->GetJavaVM(env, &jvm);

    //jclass cls = (*env)->GetObjectClass(env, instance);
   // activityClass = (jclass) (*env)->NewGlobalRef(env, cls);
   // activityObj = (*env)->NewGlobalRef(env, instance);

    vdDebug_LogPrintf("inCTOS_SALECOMPLETE.............");
    inCTOS_SALECOMPLETE();

    return 0;
}

JNIEXPORT jint
JNICALL
Java_com_Source_S1_1NPX_NPX_Main_MainActivity_inCTOSS_1EPP_1SALE(JNIEnv *env, jobject instance) {

   // jint rs = (*env)->GetJavaVM(env, &jvm);

   // jclass cls = (*env)->GetObjectClass(env, instance);
   // activityClass = (jclass) (*env)->NewGlobalRef(env, cls);
    //activityObj = (*env)->NewGlobalRef(env, instance);

    vdDebug_LogPrintf("inCTOS_EPP_SALE.............");
    inCTOS_EPP_SALE();

    return 0;
}

JNIEXPORT jint
JNICALL
Java_com_Source_S1_1NPX_NPX_Main_MainActivity_inCTOSS_1EPP_1VOID(JNIEnv *env, jobject instance) {

   // jint rs = (*env)->GetJavaVM(env, &jvm);

    //jclass cls = (*env)->GetObjectClass(env, instance);
    //activityClass = (jclass) (*env)->NewGlobalRef(env, cls);
   // activityObj = (*env)->NewGlobalRef(env, instance);

    vdDebug_LogPrintf("inCTOS_EPP_VOID.............");
    inCTOS_EPP_VOID();

    return 0;
}

JNIEXPORT jint
JNICALL
Java_com_Source_S1_1NPX_NPX_Main_MainActivity_inCTOSS_1SETTLEMENT(JNIEnv *env, jobject instance) {

   // jint rs = (*env)->GetJavaVM(env, &jvm);

   // jclass cls = (*env)->GetObjectClass(env, instance);
    //activityClass = (jclass) (*env)->NewGlobalRef(env, cls);
    //activityObj = (*env)->NewGlobalRef(env, instance);

    vdDebug_LogPrintf("inCTOS_SETTLEMENT.............");
    vdSetAppRunBySelf(1);

    inCTOS_SETTLEMENT();
    vdSetAppRunBySelf(0);

    return 0;
}

JNIEXPORT jint
JNICALL
Java_com_Source_S1_1NPX_NPX_Main_MainActivity_vdCTOSS_1DeleteReversal(JNIEnv *env, jobject instance) {

    //jint rs = (*env)->GetJavaVM(env, &jvm);

   // jclass cls = (*env)->GetObjectClass(env, instance);
   // activityClass = (jclass) (*env)->NewGlobalRef(env, cls);
   // activityObj = (*env)->NewGlobalRef(env, instance);

    vdDebug_LogPrintf("vdCTOS_DeleteReversal.............");
    vdCTOS_DeleteReversal();

    return 0;
}

JNIEXPORT jint
JNICALL
Java_com_Source_S1_1NPX_NPX_Main_MainActivity_inCTOSS_1REPRINT_1LAST(JNIEnv *env, jobject instance) {

    //jint rs = (*env)->GetJavaVM(env, &jvm);

    //jclass cls = (*env)->GetObjectClass(env, instance);
    //activityClass = (jclass) (*env)->NewGlobalRef(env, cls);
    //activityObj = (*env)->NewGlobalRef(env, instance);

    vdDebug_LogPrintf("inCTOS_REPRINT_LAST.............");
    inCTOS_REPRINT_LAST();

    return 0;
}

JNIEXPORT jint
JNICALL
Java_com_Source_S1_1NPX_NPX_Main_MainActivity_inCTOSS_1REPRINTF_1LAST_1SETTLEMENT(JNIEnv *env, jobject instance) {

    //jint rs = (*env)->GetJavaVM(env, &jvm);

    //jclass cls = (*env)->GetObjectClass(env, instance);
    //activityClass = (jclass) (*env)->NewGlobalRef(env, cls);
    //activityObj = (*env)->NewGlobalRef(env, instance);

    vdDebug_LogPrintf("inCTOS_REPRINTF_LAST_SETTLEMENT.............");
    inCTOS_REPRINTF_LAST_SETTLEMENT();

    return 0;
}

JNIEXPORT jint
JNICALL
Java_com_Source_S1_1NPX_NPX_Main_MainActivity_inCTOSS_1InitWaveData(JNIEnv *env, jobject instance) {

    //jint rs = (*env)->GetJavaVM(env, &jvm);

    //jclass cls = (*env)->GetObjectClass(env, instance);
    //activityClass = (jclass) (*env)->NewGlobalRef(env, cls);
    //activityObj = (*env)->NewGlobalRef(env, instance);
    vdDebug_LogPrintf("vdCTOS_InitWaveData.............");
    inTCTRead(1);
    inCTOSS_ProcessCfgExpress();
    vdDebug_LogPrintf("vdCTOS_InitWaveData.............");


    vdCTOS_InitWaveData();

	// do testing for test environment
	//vdCTOSS_EFT_TestNetsHardcodeKey();

    return 0;
}

JNIEXPORT jint
JNICALL
Java_com_Source_S1_1NPX_NPX_Main_MainActivity_inCTOSS_1InjectKey(JNIEnv *env, jobject instance) {

    //jint rs = (*env)->GetJavaVM(env, &jvm);

    //jclass cls = (*env)->GetObjectClass(env, instance);
    //activityClass = (jclass) (*env)->NewGlobalRef(env, cls);
    //activityObj = (*env)->NewGlobalRef(env, instance);
    vdDebug_LogPrintf("vdCTOS_InitWaveData.............");
    inTCTRead(1);
    //inCTOSS_ProcessCfgExpress();
    vdDebug_LogPrintf("vdCTOS_InitWaveData.............");


    //vdCTOS_InitWaveData();

    // do testing for test environment
    //vdCTOSS_EFT_TestNetsHardcodeKey();

    return 0;
}

JNIEXPORT jint
JNICALL
Java_com_Source_S1_1NPX_NPX_Main_MainActivity_inCTOSS_1TerminalStartUp(JNIEnv *env, jobject instance) {

    BYTE bInBuf[40];
    BYTE bOutBuf[40];
    USHORT usInLen = 0;
    USHORT usOutLen = 0;
    int inRetVal = 0;
    int inRet = 0;
    inTCTRead(1);
    //inDCCRead(1);
    //inCTMSRead(1);
    //inSKDRead(1);

   // if(activityClass != NULL)
       // (*env)->DeleteGlobalRef(env, activityClass);
    //if(activityObj != NULL)
       // (*env)->DeleteGlobalRef(env, activityObj);

    //jint rs = (*env)->GetJavaVM(env, &jvm);

   // jclass cls = (*env)->GetObjectClass(env, instance);
    //activityClass = (jclass) (*env)->NewGlobalRef(env, cls);
   // activityObj = (*env)->NewGlobalRef(env, instance);

    vdDebug_LogPrintf(".inCTOSS_TerminalStartUp.............");

	
    vdDebug_LogPrintf(".inCTOSS_TerminalStartUp............strTCT.fECR[%d]", strTCT.fECR);

//    inCTOSS_ProcessCfgExpress();//only when set FIRSTRUN to true will do this
/*
    vdDebug_LogPrintf("inCTOSS_TerminalStartUp.............");
    vdCTOS_InitWaveData();

// here put call all subAP 0x99 events
	inMultiAP_RunIPCCmdTypesEx("com.Source.SHARLS_EMV.SHARLS_EMV", 0x99, bInBuf, usInLen, bOutBuf, &usOutLen);*/
//    if(strTCT.fECR)
//    {
//        if(inCallJAVA_checkAppRunning("com.Source.SHARLS_ECR.SHARLS_ECR") == 0)
//            inMultiAP_RunIPCCmdTypesEx("com.Source.SHARLS_ECR.SHARLS_ECR", 0x99, bInBuf, usInLen, bOutBuf, &usOutLen);//Add to support ecr
//    }

    //TODO: set strTCT.fFirstInit = 0;
    if(strTCT.fFirstInit == 1)
    {
        strTCT.fFirstInit = 0;
        inTCTSave(1);
    }
    vdDebug_LogPrintf("-inCTOSS_TerminalStartUp exit");
    return 0;
}

// copy from S1_UOB
/***Start Offline Pin***/
jobject g_callback_obj_emv = NULL;
jmethodID g_callback_mid_pinpad;
jmethodID g_callback_mid_pindone;
jmethodID g_callback_mid_pindigi;




int ShowVirtualPIN(OUT USHORT *pusPINPadButtonNum, OUT BYTE *pbPINPadButtonInfo, OUT USHORT *pusPINPadButtonInfoLen)
{
	BYTE baInputbuffer[1024];
	BYTE baOutputbuffer[1024];

	JNIEnv* env = NULL;
	if((*g_JavaVM)->AttachCurrentThread(g_JavaVM, &env, NULL) != JNI_OK)
	{
		// attachCurrentThread() failed.
	}
	else {

	}

	int buffsize = 1024;
	int i = 0;

	//jbyte *by = (jbyte*)baInputbuffer;

	jbyteArray jarray = (*env)->NewByteArray(env, buffsize);

	(*env)->SetByteArrayRegion(env, jarray, 0, buffsize, (jbyte*)baInputbuffer);

	LOGE("buffsize= %d", buffsize);

	(*env)->CallLongMethod(env, g_callback_obj_emv, g_callback_mid_pinpad, jarray);

	//jsize len = env->GetArrayLength (jarray);
	//unsigned char* buf = new unsigned char[len];

	(*env)->GetByteArrayRegion (env, jarray, 0, buffsize, (jbyte*)(baOutputbuffer));

	*pusPINPadButtonNum = baOutputbuffer[0];

	LOGE("baOutputbuffer[0]= %02X", baOutputbuffer[0]);
	LOGE("baOutputbuffer[1]= %02X", baOutputbuffer[1]);
	LOGE("baOutputbuffer[2]= %02X", baOutputbuffer[2]);

	*pusPINPadButtonInfoLen = 0;
	*pusPINPadButtonInfoLen += (baOutputbuffer[1]*256);
	*pusPINPadButtonInfoLen += (baOutputbuffer[2]);

	LOGE("pusPINPadButtonNum= %d", *pusPINPadButtonNum);
	LOGE("pusPINPadButtonInfoLen= %d", *pusPINPadButtonInfoLen);


	memcpy(pbPINPadButtonInfo, &baOutputbuffer[3], *pusPINPadButtonInfoLen);

	return 0;
}



int GetPINDone(void)
{
    JNIEnv *tenv;
    jshort jsusX;
    jshort jsusY;

    //LOGD("---JNI AP CB--- GetPINDone\n");
    if((*g_JavaVM)->AttachCurrentThread(g_JavaVM, &tenv, NULL) != JNI_OK)
    {
        // attachCurrentThread() failed.
    }
    else {
        (*tenv)->CallLongMethod(tenv,g_callback_obj_emv, g_callback_mid_pindone);
    }
    return 0;

}


int ShowPINDigit(BYTE digitsNum, BYTE bPINType, BYTE bRemainingCounter)
{
    JNIEnv *tenv;
    jbyte digits;


    LOGD("---JNI AP CB--- ShowPINDigit\n");
    if((*g_JavaVM)->AttachCurrentThread(g_JavaVM, &tenv, NULL) != JNI_OK)
    {
        // attachCurrentThread() failed.
    }
    else {
        digits = (jbyte) digitsNum;
        (*tenv)->CallLongMethod(tenv, g_callback_obj_emv, g_callback_mid_pindigi, digits, bPINType, bRemainingCounter);
    }
    return 0;

}


JNIEXPORT jlong JNICALL

Java_com_Source_S1_1NPX_NPX_Main_JNI_1offlinepin_REG_1CB_1EMV(JNIEnv *env,jobject obj,  jobject instance)
{
    int Rtn = 0;
    LOGD("REG_1CB_1EMV[%d]", (g_callback_obj_emv != NULL));

// patrick said this must remark which code for testing, if not will happen crash om offline PIN 20210121
//    if(FALSE)//For my testing
//        return 0;
    if(g_callback_obj_emv != NULL)
        (*env)->DeleteGlobalRef(env, g_callback_obj_emv);

    LOGD("REG_1CB_1EMV OFFLINE PIN PAT");
    (*env)->GetJavaVM(env, &g_JavaVM);
    g_callback_obj_emv = (*env)->NewGlobalRef(env,instance);;
    jclass clz = (*env)->GetObjectClass(env, g_callback_obj_emv);



    if(clz == NULL)
    {
        //failed to find class
    }

	LOGE("ShowVirtualPIN");
	LOGE("ShowPINDigit");
	LOGE("GetPINDone");

    g_callback_mid_pinpad = (*env)->GetMethodID(env,clz, "ShowVirtualPIN", "([B)J");
    g_callback_mid_pindigi = (*env)->GetMethodID(env, clz, "ShowPINDigit", "(BBB)J");
    g_callback_mid_pindone = (*env)->GetMethodID(env, clz, "GetPINDone", "()J");
   


    JavaVM *javaVM = g_vm;
    jint res = (*javaVM)->GetEnv(javaVM,(void **) &env, JNI_VERSION_1_6);
    if (res != JNI_OK) {
        res = (*javaVM)->AttachCurrentThread(javaVM, &env, NULL);
        if (JNI_OK != res) {
            LOGE("Failed to AttachCurrentThread, ErrorCode = %d", res);

        }
    }
    if(clz != NULL)
        (*env)->DeleteLocalRef(env, clz);
    return Rtn;
}


#define d_EXTERNAL_PINPAD           0

BYTE g_bPINType;
BYTE g_bRemainingCounter;
BOOL ispinpadalone=true;





USHORT OnShowVirtualPIN(OUT USHORT *pusPINPadButtonNum, OUT BYTE *pbPINPadButtonInfo, OUT USHORT *pusPINPadButtonInfoLen)
{
	vdDebug_LogPrintf((BYTE*)"OnShowVirtualPIN() is triggered -->");
	USHORT usPINPadButtonNum;
	BYTE bPINPadButtonInfo[1024];
	USHORT usPINPadButtonInfoLen;

    LOGE("ShowVirtualPIN");

	ShowVirtualPIN(&usPINPadButtonNum, bPINPadButtonInfo, &usPINPadButtonInfoLen);

	*pusPINPadButtonNum= usPINPadButtonNum;
	*pusPINPadButtonInfoLen = usPINPadButtonInfoLen;
	memcpy(pbPINPadButtonInfo, bPINPadButtonInfo, usPINPadButtonInfoLen);

    DebugAddINT((BYTE*)"  pusPINPadButtonNum",  *pusPINPadButtonNum);
    DebugAddINT((BYTE*)"  pusPINPadButtonInfoLen", *pusPINPadButtonInfoLen);
    DebugAddHEX((BYTE*)"  pbPINPadButtonInfo", pbPINPadButtonInfo, *pusPINPadButtonInfoLen);
	return 0;
}

USHORT OnGetPINDone(void)
{
	vdDebug_LogPrintf((BYTE*)"OnGetPINDone() is triggered -->");
	if(ispinpadalone == true)
	{
		GetPINDone();
	}
	CTOS_LCDTPrintXY(1, 3, (BYTE*)"Get PIN Done");

	return 0;
}

void OnShowPinDigit(IN BYTE bDigits,int reEnterPin)
{
	BYTE baStr[21];
	vdDebug_LogPrintf((BYTE*)"OnShowPinDigit() is triggered -->");
    DebugAddINT((BYTE*)"  bDigits", bDigits);

    DebugAddINT((BYTE*)"  ispinpadalone", ispinpadalone);
	if(ispinpadalone == true)
	{
		ShowPINDigit(bDigits, 0x1, reEnterPin/*0xF*/);
	}
	else
	{
		memset(baStr, 0x20, 20);
		baStr[20] = 0;

		memset(baStr, '*', bDigits);
		CTOS_LCDTPrintXY(1, 2, baStr);
	}
}
/***End Offline Pin***/

int inCallJAVA_DisplayUI(BYTE *pbDispMsg, BYTE *pbOutStr, BYTE *pbOutLen)
{
    unsigned char uszBuffer[100+1];
    int inRet = 0;


    vdDebug_LogPrintf("=====inCallJAVA_DisplayUI=====");

    JNIEnv *env;
    jint rs = (*jvm)->AttachCurrentThread(jvm, &env, NULL);
    // Use the env pointer...
    vdDebug_LogPrintf("jint[%d] *env[%x]", rs, *env);


  
    if (strlen(pbDispMsg)>0)
        strcpy(uszBuffer, pbDispMsg);

	
	vdDebug_LogPrintf("uszBuffer[%s]", uszBuffer);

    jstring jstr = (*env)->NewStringUTF(env, uszBuffer);
    vdDebug_LogPrintf("jstring[%s]", uszBuffer);

    vdDebug_LogPrintf("jstring[%s]", "com/Source/S1_NPX/NPX/Main/MainActivity");

    jmethodID methodID = (*env)->GetMethodID(env, activityClass, "DisplayUI", "(Ljava/lang/String;)Ljava/lang/String;");

    vdDebug_LogPrintf("jstring[%s]", "DisplayUI");

    jobject result = (*env)->CallObjectMethod(env, activityObj, methodID, jstr);

    jbyte* str = NULL; 
    str = (*env)->GetStringUTFChars(env,(jstring) result, NULL); 
    if (str!=NULL)
    {
        vdDebug_LogPrintf("%s", str);
        *pbOutLen = strlen(str);
        vdDebug_LogPrintf("strcpy");
        strcpy(pbOutStr, str);

        vdDebug_LogPrintf("ReleaseStringUTFChars");
        (*env)->ReleaseStringUTFChars(env, result, str);

    }
    else
        *pbOutLen = 0;

    (*env)->DeleteLocalRef(env, jstr);
//    (*env)->DeleteLocalRef(env, jclazz);
    (*env)->DeleteLocalRef(env, result);

    vdDebug_LogPrintf("end inCallJAVA_DisplayUI");
    return d_OK;
}

int inCallJAVA_BackToProgress(BYTE *pbDispMsg, BYTE *pbOutStr, BYTE *pbOutLen)
{
    unsigned char uszBuffer[100+1];
    int inRet = 0;


    vdDebug_LogPrintf("=====BackToProgress=====");

    JNIEnv *env;
    jint rs = (*jvm)->AttachCurrentThread(jvm, &env, NULL);
    // Use the env pointer...
    vdDebug_LogPrintf("jint[%d] *env[%x]", rs, *env);



    if (strlen(pbDispMsg)>0)
        strcpy(uszBuffer, pbDispMsg);


    vdDebug_LogPrintf("uszBuffer[%s]", uszBuffer);

    jstring jstr = (*env)->NewStringUTF(env, uszBuffer);
    vdDebug_LogPrintf("jstring[%s]", uszBuffer);

    vdDebug_LogPrintf("jstring[%s]", "com/Source/S1_NPX/NPX/Main/MainActivity");

    jmethodID methodID = (*env)->GetMethodID(env, activityClass, "BackToProgress", "(Ljava/lang/String;)Ljava/lang/String;");

    vdDebug_LogPrintf("jstring[%s]", "BackToProgress");

    jobject result = (*env)->CallObjectMethod(env, activityObj, methodID, jstr);

    jbyte* str = NULL;
    str = (*env)->GetStringUTFChars(env,(jstring) result, NULL);
    if (str!=NULL)
    {
        vdDebug_LogPrintf("%s", str);
        *pbOutLen = strlen(str);
        vdDebug_LogPrintf("strcpy");
        strcpy(pbOutStr, str);

        vdDebug_LogPrintf("ReleaseStringUTFChars");
        (*env)->ReleaseStringUTFChars(env, result, str);

    }
    else
        *pbOutLen = 0;

    (*env)->DeleteLocalRef(env, jstr);
    //(*env)->DeleteLocalRef(env, clazz);
    (*env)->DeleteLocalRef(env, result);

    vdDebug_LogPrintf("end BackToProgress");
    return d_OK;
}



int inCallJAVA_CardEntryUI(BYTE *pbDispMsg, BYTE *pbOutStr, BYTE *pbOutLen)
{
    unsigned char uszBuffer[100+1];
    int inRet = 0;


    vdDebug_LogPrintf("=====inCallJAVA_CardEntryUI=====");

    JNIEnv *env;
    jint rs = (*jvm)->AttachCurrentThread(jvm, &env, NULL);
    // Use the env pointer...
    vdDebug_LogPrintf("jint[%d] *env[%x]", rs, *env);


  
    if (strlen(pbDispMsg)>0)
        strcpy(uszBuffer, pbDispMsg);

	
	vdDebug_LogPrintf("uszBuffer[%s]", uszBuffer);

    jstring jstr = (*env)->NewStringUTF(env, uszBuffer);
    vdDebug_LogPrintf("jstring[%s]", uszBuffer);

    vdDebug_LogPrintf("jstring[%s]", "com/Source/S1_NPX/NPX/Main/MainActivity");

    jmethodID methodID = (*env)->GetMethodID(env, activityClass, "CardEntryUI", "(Ljava/lang/String;)Ljava/lang/String;");

    vdDebug_LogPrintf("jstring[%s]", "CardEntryUI");

    jobject result = (*env)->CallObjectMethod(env, activityObj, methodID, jstr);

    jbyte* str = NULL; 
    str = (*env)->GetStringUTFChars(env,(jstring) result, NULL); 
    if (str!=NULL)
    {
        vdDebug_LogPrintf("%s", str);
        *pbOutLen = strlen(str);
        vdDebug_LogPrintf("strcpy");
        strcpy(pbOutStr, str);

        vdDebug_LogPrintf("ReleaseStringUTFChars");
        (*env)->ReleaseStringUTFChars(env, result, str);

    }
    else
        *pbOutLen = 0;

    (*env)->DeleteLocalRef(env, jstr);
//    (*env)->DeleteLocalRef(env, jclazz);
    (*env)->DeleteLocalRef(env, result);

    vdDebug_LogPrintf("end inCallJAVA_CardEntryUI");
    return d_OK;
}


JNIEXPORT jint
JNICALL
Java_com_Source_S1_1NPX_NPX_Alarm_AlarmReceiver_inCTOSS_1IdleRunningFunctions(JNIEnv *env, jobject instance) {
    vdDebug_LogPrintf("=====inCTOSS_1IdleRunningFunctions=====");
/*
    if(activityClass != NULL)
        (*env)->DeleteGlobalRef(env, activityClass);
    if(activityObj != NULL)
        (*env)->DeleteGlobalRef(env, activityObj);

    jint rs = (*env)->GetJavaVM(env, &jvm);
    jclass cls = (*env)->GetObjectClass(env, instance);
    activityClass = (jclass) (*env)->NewGlobalRef(env, cls);
    activityObj = (*env)->NewGlobalRef(env, instance);
    */
    vdDebug_LogPrintf("inCTOSS_IdleRunningFunctions.............");
    inIdleRunningFunctions();

    //if(cls != NULL)
      //  (*env)->DeleteLocalRef(env, cls);

    return 0;
}

JNIEXPORT jint
JNICALL
Java_com_Source_S1_1NPX_NPX_Main_MainActivity_inCTOSS_1MultiAP_1GetMainroutine(JNIEnv *env, jobject instance) {
    int inRetVal = 0;

/*
    jint rs = (*env)->GetJavaVM(env, &jvm);

    jclass cls = (*env)->GetObjectClass(env, instance);
    activityClass = (jclass) (*env)->NewGlobalRef(env, cls);
    activityObj = (*env)->NewGlobalRef(env, instance);
*/
// patrick test public folder 20190104 start
    strTCT.byRS232DebugPort = 8;

    vdDebug_LogPrintf("inCTOSS_MultiAP_GetMainroutine.............");
    inRetVal = inTCTRead(1);

    strTCT.byRS232DebugPort = 8;
    strTCT.fDemo= 0;
    /*Here do test init, load TCT PRM*/
    strTCT.fShareComEnable = 1;

    inRetVal = inTCTSave(1);

    inMultiAP_GetMainroutine();


    return 0;
}

JNIEXPORT jint JNICALL
Java_com_Source_S1_1NPX_NPX_Main_MainActivity_inCTOSS_1MultiAP_1CheckMainAPStatus(JNIEnv *env, jobject instance)
{
    inTCTRead(1);

    if (inMultiAP_CheckMainAPStatus() == d_OK)
    {
        vdDebug_LogPrintf("inMultiAP_CheckMainAPStatus d_OK[%d]",1);
        return 1;
    }
    else
    {
        vdDebug_LogPrintf("inMultiAP_CheckMainAPStatus d_NO[%d]",0);
        return 0;
    }
}

JNIEXPORT jint JNICALL
Java_com_Source_S1_1NPX_NPX_Main_MainActivity_inCTOSS_1SubAPMain(JNIEnv *env, jobject instance) {
    int inRetVal = 0;

    char szDisplayBuf[30];
    BYTE baAmount[20];
    ULONG ulAmount = 0L;

    inTCTRead(1);
//    strTCT.byRS232DebugPort = 8;

    jint rs = (*env)->GetJavaVM(env, &jvm);

    jclass cls = (*env)->GetObjectClass(env, instance);
    activityClass = (jclass) (*env)->NewGlobalRef(env, cls);
    activityObj = (*env)->NewGlobalRef(env, instance);

    vdDebug_LogPrintf("inCTOSS_SubAPMain");
    inRetVal = main(0, NULL);

    return 0;
}


int inCallJAVA_PresentCard(BYTE *pbDispMsg,  BYTE *pbOutStr, BYTE *pbOutStrLen)
{
    unsigned char uszBuffer[100+1];
    int inRet = 0;

    JNIEnv *env;
    jint rs = (*jvm)->AttachCurrentThread(jvm, &env, NULL);

    unsigned char uszAPName[100+1];
    unsigned char *puszAPName = NULL;
    unsigned char uszJClassName[100+1];

	

    vdDebug_LogPrintf("=====inCallJAVA_PresentCard Start=====");

	if (strlen(pbDispMsg)>0)
       strcpy(uszBuffer, pbDispMsg);
    vdDebug_LogPrintf("uszBuffer[%s]", uszBuffer);
    jstring jstr = (*env)->NewStringUTF(env, uszBuffer);
    vdDebug_LogPrintf("jstring[%s]", uszBuffer);
	
    jclass clazz = (*env)->FindClass(env, "com/Source/S1_NPX/NPX/Main/MainActivity");

    jmethodID methodID = (*env)->GetMethodID(env, activityClass, "PresentCardUI", "(Ljava/lang/String;)Ljava/lang/String;");
	
    //jobject result = (*env)->CallObjectMethod(env, activityObj, methodID);
    jobject result = (*env)->CallObjectMethod(env, activityObj, methodID, jstr);

    jbyte* str = NULL;
    str = (*env)->GetStringUTFChars(env,(jstring) result, NULL);

    if (str!=NULL)
    {
        *pbOutStrLen = strlen(str);
        strcpy(pbOutStr, str);

        (*env)->ReleaseStringUTFChars(env, result, str);

    }
    else
        *pbOutStrLen = 0;

    (*env)->DeleteLocalRef(env, clazz);
    (*env)->DeleteLocalRef(env, result);	
    (*env)->DeleteLocalRef(env, jstr);
	
    vdDebug_LogPrintf("=====inCallJAVA_PresentCard End=====");

    return d_OK;
}


int inCallJAVA_ApprovedTrans(BYTE *pbDispMsg,  BYTE *pbOutStr, BYTE *pbOutStrLen)
{
    unsigned char uszBuffer[200+1];
    int inRet = 0;

    JNIEnv *env;
    jint rs = (*jvm)->AttachCurrentThread(jvm, &env, NULL);

    unsigned char uszAPName[100+1];
    unsigned char *puszAPName = NULL;
    unsigned char uszJClassName[100+1];

	

    vdDebug_LogPrintf("=====inCallJAVA_ApprovedTrans Start=====");

	if (strlen(pbDispMsg)>0)
       strcpy(uszBuffer, pbDispMsg);
    vdDebug_LogPrintf("uszBuffer[%s]", uszBuffer);
    jstring jstr = (*env)->NewStringUTF(env, uszBuffer);
    vdDebug_LogPrintf("jstring[%s]", uszBuffer);
	
    jclass clazz = (*env)->FindClass(env, "com/Source/S1_NPX/NPX/Main/MainActivity");

    jmethodID methodID = (*env)->GetMethodID(env, activityClass, "ApprovedUI", "(Ljava/lang/String;)Ljava/lang/String;");
	
    //jobject result = (*env)->CallObjectMethod(env, activityObj, methodID);
    jobject result = (*env)->CallObjectMethod(env, activityObj, methodID, jstr);

    jbyte* str = NULL;
    str = (*env)->GetStringUTFChars(env,(jstring) result, NULL);

    if (str!=NULL)
    {
        *pbOutStrLen = strlen(str);
        strcpy(pbOutStr, str);

        (*env)->ReleaseStringUTFChars(env, result, str);

    }
    else
        *pbOutStrLen = 0;

    (*env)->DeleteLocalRef(env, clazz);
    (*env)->DeleteLocalRef(env, result);
    (*env)->DeleteLocalRef(env, jstr);
	
    vdDebug_LogPrintf("=====inCallJAVA_ApprovedTrans End=====");

    return d_OK;
}


int inCallJAVA_DeclinedTrans(BYTE *pbDispMsg,  BYTE *pbOutStr, BYTE *pbOutStrLen)
{
    unsigned char uszBuffer[200+1];
    int inRet = 0;

    JNIEnv *env;
    jint rs = (*jvm)->AttachCurrentThread(jvm, &env, NULL);

    unsigned char uszAPName[100+1];
    unsigned char *puszAPName = NULL;
    unsigned char uszJClassName[100+1];

	

    vdDebug_LogPrintf("=====inCallJAVA_DeclinedTrans Start=====");

	if (strlen(pbDispMsg)>0)
       strcpy(uszBuffer, pbDispMsg);
    vdDebug_LogPrintf("uszBuffer[%s]", uszBuffer);
    jstring jstr = (*env)->NewStringUTF(env, uszBuffer);
    vdDebug_LogPrintf("jstring[%s]", uszBuffer);
	
    jclass clazz = (*env)->FindClass(env, "com/Source/S1_NPX/NPX/Main/MainActivity");

    jmethodID methodID = (*env)->GetMethodID(env, activityClass, "DeclinedUI", "(Ljava/lang/String;)Ljava/lang/String;");
	
    //jobject result = (*env)->CallObjectMethod(env, activityObj, methodID);
    jobject result = (*env)->CallObjectMethod(env, activityObj, methodID, jstr);

    jbyte* str = NULL;
    str = (*env)->GetStringUTFChars(env,(jstring) result, NULL);

    if (str!=NULL)
    {
        *pbOutStrLen = strlen(str);
        strcpy(pbOutStr, str);

        (*env)->ReleaseStringUTFChars(env, result, str);

    }
    else
        *pbOutStrLen = 0;

    (*env)->DeleteLocalRef(env, clazz);
    (*env)->DeleteLocalRef(env, result);
    (*env)->DeleteLocalRef(env, jstr);
	
    vdDebug_LogPrintf("=====inCallJAVA_DeclinedTrans End=====");

    return d_OK;
}


int inCallJAVA_TransCancel(BYTE *pbDispMsg,  BYTE *pbOutStr, BYTE *pbOutStrLen)
{
	unsigned char uszBuffer[300+1];
	int inRet = 0;

	JNIEnv *env;
	jint rs = (*jvm)->AttachCurrentThread(jvm, &env, NULL);

	unsigned char uszAPName[100+1];
	unsigned char *puszAPName = NULL;
	unsigned char uszJClassName[100+1];

	

	vdDebug_LogPrintf("=====inCallJAVA_TransCancel Start=====");

	if (strlen(pbDispMsg)>0)
	   strcpy(uszBuffer, pbDispMsg);
	vdDebug_LogPrintf("uszBuffer[%s]", uszBuffer);
	jstring jstr = (*env)->NewStringUTF(env, uszBuffer);
	vdDebug_LogPrintf("jstring[%s]", uszBuffer);
	
	jclass clazz = (*env)->FindClass(env, "com/Source/S1_NPX/NPX/Main/MainActivity");

	jmethodID methodID = (*env)->GetMethodID(env, activityClass, "CancelTransUI", "(Ljava/lang/String;)Ljava/lang/String;");
	
	//jobject result = (*env)->CallObjectMethod(env, activityObj, methodID);
	jobject result = (*env)->CallObjectMethod(env, activityObj, methodID, jstr);

	jbyte* str = NULL;
	str = (*env)->GetStringUTFChars(env,(jstring) result, NULL);

	if (str!=NULL)
	{
		*pbOutStrLen = strlen(str);
		strcpy(pbOutStr, str);

		(*env)->ReleaseStringUTFChars(env, result, str);

	}
	else
		*pbOutStrLen = 0;

	(*env)->DeleteLocalRef(env, clazz);
	(*env)->DeleteLocalRef(env, result);
    (*env)->DeleteLocalRef(env, jstr);
	
	vdDebug_LogPrintf("=====inCallJAVA_TransCancel End=====");

	return d_OK;
}



int inCallJAVA_usSetDateTime(BYTE *pbDispMsg, BYTE *pbOutStr_datetime, BYTE *pbOutLen)
{
    unsigned char uszBuffer[512+1];
    int inRet = 0;
    vdDebug_LogPrintf("=====inCallJAVA_usSetDateTime=====");
    JNIEnv *env;
    jint rs = (*jvm)->AttachCurrentThread(jvm, &env, NULL);
// Use the env pointer...
    vdDebug_LogPrintf("jint[%d] *env[%x]", rs, *env);
    if (strlen(pbDispMsg)>0)
        strcpy(uszBuffer, pbDispMsg);
    vdDebug_LogPrintf("uszBuffer[%s]", uszBuffer);
    jstring jstr = (*env)->NewStringUTF(env, uszBuffer);
    vdDebug_LogPrintf("jstring[%s]", uszBuffer);

    jmethodID methodID = (*env)->GetMethodID(env, activityClass, "usSetDateAndTime", "(Ljava/lang/String;)Ljava/lang/String;");
    vdDebug_LogPrintf("jstring[%s]", "usSetDateTime");
    jobject result = (*env)->CallObjectMethod(env, activityObj, methodID, jstr);
    jbyte* str = NULL;
    str = (*env)->GetStringUTFChars(env,(jstring) result, NULL);
    if (str!=NULL)
    {
        vdDebug_LogPrintf("%s", str);
        *pbOutLen = strlen(str);
        vdDebug_LogPrintf("strcpy");
        strcpy(pbOutStr_datetime, str);
        vdDebug_LogPrintf("ReleaseStringUTFChars");
        (*env)->ReleaseStringUTFChars(env, result, str);
    }
    else
    {
        *pbOutLen = 0;
//strcpy(pbOutStr, "OK");
    }
    (*env)->DeleteLocalRef(env, jstr);
//(*env)->DeleteLocalRef(env, clazz);
    (*env)->DeleteLocalRef(env, result);
    vdDebug_LogPrintf("end inCallJAVA_usSetDateTime");
    return d_OK;
}


int inCallJAVA_ConfirmAmountUI(BYTE *pbDispMsg,  BYTE *pbOutStr, BYTE *pbOutStrLen)
{
    unsigned char uszBuffer[100+1];
    int inRet = 0;

    JNIEnv *env;
    jint rs = (*jvm)->AttachCurrentThread(jvm, &env, NULL);

    unsigned char uszAPName[100+1];
    unsigned char *puszAPName = NULL;
    unsigned char uszJClassName[100+1];

	

    vdDebug_LogPrintf("=====inCallJAVA_ConfirmAmountUI Start=====");

	if (strlen(pbDispMsg)>0)
       strcpy(uszBuffer, pbDispMsg);
    vdDebug_LogPrintf("uszBuffer[%s]", uszBuffer);
    jstring jstr = (*env)->NewStringUTF(env, uszBuffer);
    vdDebug_LogPrintf("jstring[%s]", uszBuffer);
	
    jclass clazz = (*env)->FindClass(env, "com/Source/S1_NPX/NPX/Main/MainActivity");

    jmethodID methodID = (*env)->GetMethodID(env, activityClass, "ConfirmAmountUI", "(Ljava/lang/String;)Ljava/lang/String;");
	
    //jobject result = (*env)->CallObjectMethod(env, activityObj, methodID);
    jobject result = (*env)->CallObjectMethod(env, activityObj, methodID, jstr);

    jbyte* str = NULL;
    str = (*env)->GetStringUTFChars(env,(jstring) result, NULL);

    if (str!=NULL)
    {
        *pbOutStrLen = strlen(str);
        strcpy(pbOutStr, str);

        (*env)->ReleaseStringUTFChars(env, result, str);

    }
    else
        *pbOutStrLen = 0;

    (*env)->DeleteLocalRef(env, clazz);
    (*env)->DeleteLocalRef(env, result);	
    (*env)->DeleteLocalRef(env, jstr);
	
    vdDebug_LogPrintf("=====inCallJAVA_ConfirmAmountUI End=====");

    return d_OK;
}

int inCallJAVA_ProcessingUI(BYTE *pbDispMsg,  BYTE *pbOutStr, BYTE *pbOutStrLen)
{
    unsigned char uszBuffer[100+1];
    int inRet = 0;

    JNIEnv *env;
    jint rs = (*jvm)->AttachCurrentThread(jvm, &env, NULL);

    unsigned char uszAPName[100+1];
    unsigned char *puszAPName = NULL;
    unsigned char uszJClassName[100+1];



    vdDebug_LogPrintf("=====inCallJAVA_ProcessingUI Start=====");

    if (strlen(pbDispMsg)>0)
        strcpy(uszBuffer, pbDispMsg);
    vdDebug_LogPrintf("uszBuffer[%s]", uszBuffer);
    jstring jstr = (*env)->NewStringUTF(env, uszBuffer);
    vdDebug_LogPrintf("jstring[%s]", uszBuffer);

    jclass clazz = (*env)->FindClass(env, "com/Source/S1_NPX/NPX/Main/MainActivity");

    jmethodID methodID = (*env)->GetMethodID(env, activityClass, "ProcessingUI", "(Ljava/lang/String;)Ljava/lang/String;");

    //jobject result = (*env)->CallObjectMethod(env, activityObj, methodID);
    jobject result = (*env)->CallObjectMethod(env, activityObj, methodID, jstr);

    jbyte* str = NULL;
    str = (*env)->GetStringUTFChars(env,(jstring) result, NULL);

    if (str!=NULL)
    {
        *pbOutStrLen = strlen(str);
        strcpy(pbOutStr, str);

        (*env)->ReleaseStringUTFChars(env, result, str);

    }
    else
        *pbOutStrLen = 0;

    (*env)->DeleteLocalRef(env, clazz);
    (*env)->DeleteLocalRef(env, result);	
    (*env)->DeleteLocalRef(env, jstr);

    vdDebug_LogPrintf("=====inCallJAVA_ProcessingUI End=====");

    return d_OK;
}

int inCallJAVA_ApproveUI(BYTE *pbDispMsg,  BYTE *pbOutStr, BYTE *pbOutStrLen)
{
    unsigned char uszBuffer[100+1];
    int inRet = 0;

    JNIEnv *env;
    jint rs = (*jvm)->AttachCurrentThread(jvm, &env, NULL);

    unsigned char uszAPName[100+1];
    unsigned char *puszAPName = NULL;
    unsigned char uszJClassName[100+1];

	

    vdDebug_LogPrintf("=====inCallJAVA_ApproveUI Start=====");

	if (strlen(pbDispMsg)>0)
       strcpy(uszBuffer, pbDispMsg);
    vdDebug_LogPrintf("uszBuffer[%s]", uszBuffer);
    jstring jstr = (*env)->NewStringUTF(env, uszBuffer);
    vdDebug_LogPrintf("jstring[%s]", uszBuffer);
	
    jclass clazz = (*env)->FindClass(env, "com/Source/S1_NPX/NPX/Main/MainActivity");

    jmethodID methodID = (*env)->GetMethodID(env, activityClass, "ApproveUI", "(Ljava/lang/String;)Ljava/lang/String;");
	
    //jobject result = (*env)->CallObjectMethod(env, activityObj, methodID);
    jobject result = (*env)->CallObjectMethod(env, activityObj, methodID, jstr);

    jbyte* str = NULL;
    str = (*env)->GetStringUTFChars(env,(jstring) result, NULL);

    if (str!=NULL)
    {
        *pbOutStrLen = strlen(str);
        strcpy(pbOutStr, str);

        (*env)->ReleaseStringUTFChars(env, result, str);

    }
    else
        *pbOutStrLen = 0;

    (*env)->DeleteLocalRef(env, clazz);
    (*env)->DeleteLocalRef(env, result);	
    (*env)->DeleteLocalRef(env, jstr);
	
    vdDebug_LogPrintf("=====inCallJAVA_ApproveUI End=====");

    return d_OK;
}


int inCallJAVA_NotApproveUI(BYTE *pbDispMsg,  BYTE *pbOutStr, BYTE *pbOutStrLen)
{
    unsigned char uszBuffer[100+1];
    int inRet = 0;

    JNIEnv *env;
    jint rs = (*jvm)->AttachCurrentThread(jvm, &env, NULL);

    unsigned char uszAPName[100+1];
    unsigned char *puszAPName = NULL;
    unsigned char uszJClassName[100+1];

	

    vdDebug_LogPrintf("=====inCallJAVA_NotApproveUI Start=====");

	if (strlen(pbDispMsg)>0)
       strcpy(uszBuffer, pbDispMsg);
    vdDebug_LogPrintf("uszBuffer[%s]", uszBuffer);
    jstring jstr = (*env)->NewStringUTF(env, uszBuffer);
    vdDebug_LogPrintf("jstring[%s]", uszBuffer);
	
    jclass clazz = (*env)->FindClass(env, "com/Source/S1_NPX/NPX/Main/MainActivity");

    jmethodID methodID = (*env)->GetMethodID(env, activityClass, "NotApproveUI", "(Ljava/lang/String;)Ljava/lang/String;");
	
    //jobject result = (*env)->CallObjectMethod(env, activityObj, methodID);
    jobject result = (*env)->CallObjectMethod(env, activityObj, methodID, jstr);

    jbyte* str = NULL;
    str = (*env)->GetStringUTFChars(env,(jstring) result, NULL);

    if (str!=NULL)
    {
        *pbOutStrLen = strlen(str);
        strcpy(pbOutStr, str);

        (*env)->ReleaseStringUTFChars(env, result, str);

    }
    else
        *pbOutStrLen = 0;

    (*env)->DeleteLocalRef(env, clazz);
    (*env)->DeleteLocalRef(env, result);	
    (*env)->DeleteLocalRef(env, jstr);
	
    vdDebug_LogPrintf("=====inCallJAVA_NotApproveUI End=====");

    return d_OK;
}


int inCallJAVA_TransCancelUI(BYTE *pbDispMsg,  BYTE *pbOutStr, BYTE *pbOutStrLen)
{
    unsigned char uszBuffer[100+1];
    int inRet = 0;

    JNIEnv *env;
    jint rs = (*jvm)->AttachCurrentThread(jvm, &env, NULL);

    unsigned char uszAPName[100+1];
    unsigned char *puszAPName = NULL;
    unsigned char uszJClassName[100+1];

	

    vdDebug_LogPrintf("=====inCallJAVA_TransCancelUI Start=====");

	if (strlen(pbDispMsg)>0)
       strcpy(uszBuffer, pbDispMsg);
    vdDebug_LogPrintf("uszBuffer[%s]", uszBuffer);
    jstring jstr = (*env)->NewStringUTF(env, uszBuffer);
    vdDebug_LogPrintf("jstring[%s]", uszBuffer);
	
    jclass clazz = (*env)->FindClass(env, "com/Source/S1_NPX/NPX/Main/MainActivity");

    jmethodID methodID = (*env)->GetMethodID(env, activityClass, "TransCancelUI", "(Ljava/lang/String;)Ljava/lang/String;");
	
    //jobject result = (*env)->CallObjectMethod(env, activityObj, methodID);
    jobject result = (*env)->CallObjectMethod(env, activityObj, methodID, jstr);

    jbyte* str = NULL;
    str = (*env)->GetStringUTFChars(env,(jstring) result, NULL);

    if (str!=NULL)
    {
        *pbOutStrLen = strlen(str);
        strcpy(pbOutStr, str);

        (*env)->ReleaseStringUTFChars(env, result, str);

    }
    else
        *pbOutStrLen = 0;

    (*env)->DeleteLocalRef(env, clazz);
    (*env)->DeleteLocalRef(env, result);	
    (*env)->DeleteLocalRef(env, jstr);
	
    vdDebug_LogPrintf("=====inCallJAVA_TransCancelUI End=====");

    return d_OK;
}


int inCallJAVA_ShowTxnMainLayoutEx()
{
    JNIEnv *env;
    jint rs = (*jvm)->AttachCurrentThread(jvm, &env, NULL);

    jstring jstr = (*env)->NewStringUTF(env, "dummy");

//    jclass clazz = (*env)->FindClass(env, "com/Source/S1_UOB/UOB/Main/MainActivity");

    jmethodID methodID = (*env)->GetMethodID(env, activityClass, "showTransactionMainLayoutEx", "(Ljava/lang/String;)V");

    (*env)->CallVoidMethod(env, activityObj, methodID, jstr);
    (*env)->DeleteLocalRef(env, jstr);
    return d_OK;

}

int inCallJAVA_DisplayErrorMsg(BYTE *pbDispMsg, BYTE *pbOutStr_dispbox, BYTE *pbOutLen)
{
    unsigned char uszBuffer[528];
    int inRet = 0;


    vdDebug_LogPrintf("=====inCallJAVA_DisplayErrorMsg=====");

    JNIEnv *env;
    jint rs = (*jvm)->AttachCurrentThread(jvm, &env, NULL);
    // Use the env pointer...
    vdDebug_LogPrintf("jint[%d] *env[%x]", rs, *env);


    if (strlen(pbDispMsg)>0)
        strcpy(uszBuffer, pbDispMsg);

    vdDebug_LogPrintf("uszBuffer[%s]", uszBuffer);

    jstring jstr = (*env)->NewStringUTF(env, uszBuffer);
    vdDebug_LogPrintf("jstring[%s]", uszBuffer);

    jmethodID methodID = (*env)->GetMethodID(env, activityClass, "DisplayErrorMsg", "(Ljava/lang/String;)Ljava/lang/String;");

    jobject result = (*env)->CallObjectMethod(env, activityObj, methodID, jstr);

    jbyte* str = NULL;
    str = (*env)->GetStringUTFChars(env,(jstring) result, NULL);
    if (str!=NULL)
    {
        vdDebug_LogPrintf("%s", str);
        *pbOutLen = strlen(str);
        vdDebug_LogPrintf("strcpy");
        strcpy(pbOutStr_dispbox, str);

        vdDebug_LogPrintf("ReleaseStringUTFChars");
        (*env)->ReleaseStringUTFChars(env, result, str);

    }
    else
        *pbOutLen = 0;

    (*env)->DeleteLocalRef(env, jstr);
    //(*env)->DeleteLocalRef(env, clazz);
    (*env)->DeleteLocalRef(env, result);

    vdDebug_LogPrintf("end inCallJAVA_DisplayErrorMsg");
    return d_OK;
}

int inCallJAVA_CopyFileUI()
{
    unsigned char uszBuffer[128];
    int inRet = 0;

    vdDebug_LogPrintf("=====inCallJAVA_CopyFileUI=====");

    JNIEnv *env;
    jint rs = (*jvm)->AttachCurrentThread(jvm, &env, NULL);
    // Use the env pointer...
    vdDebug_LogPrintf("jint[%d] *env[%x]", rs, *env);
    strcpy(uszBuffer, "Copy Asset");
    vdDebug_LogPrintf("uszBuffer[%s]", uszBuffer);

    jstring jstr = (*env)->NewStringUTF(env, uszBuffer);
    vdDebug_LogPrintf("jstring[%s]", uszBuffer);

    jmethodID methodID = (*env)->GetMethodID(env, activityClass, "ReinitCopyFiles", "(Ljava/lang/String;)Ljava/lang/String;");

    jobject result = (*env)->CallObjectMethod(env, activityObj, methodID, jstr);

    jbyte* str = NULL;
    str = (*env)->GetStringUTFChars(env,(jstring) result, NULL);
    if (str!=NULL)
    {
        vdDebug_LogPrintf("%s", str);

        vdDebug_LogPrintf("ReleaseStringUTFChars");
        (*env)->ReleaseStringUTFChars(env, result, str);

    }


    (*env)->DeleteLocalRef(env, jstr);
    //(*env)->DeleteLocalRef(env, clazz);
    (*env)->DeleteLocalRef(env, result);

    vdDebug_LogPrintf("end inCallJAVA_CopyFileUI");
    return d_OK;
}

JNIEXPORT jint
JNICALL
Java_com_Source_S1_1NPX_NPX_Main_MainActivity_inPutCancel(JNIEnv *env, jobject instance) {

	/*

    inTCTRead(1);
    strTCT.byRS232DebugPort = 8;
    //inTCTSave(1);
    vdDebug_LogPrintf("inTCTRead strTCT.fDemo[%d]", strTCT.fDemo);
    strTCT.fDemo = 0;

    jint rs = (*env)->GetJavaVM(env, &jvm);

    jclass cls = (*env)->GetObjectClass(env, instance);
    activityClass = (jclass) (*env)->NewGlobalRef(env, cls);
    activityObj = (*env)->NewGlobalRef(env, instance);
  

    pid_t pid = getpid();

    vdDebug_LogPrintf("call NETS strTCT.fDemo[%d]", strTCT.fDemo);
	*/

    vdDebug_LogPrintf("CTOS_KBDBufPut B");
    CTOS_KBDBufPut('B');
    CTOS_Delay(50);

    return 0;
}

int inCallJAVA_VoidMenu(BYTE *pbDispMsg,  BYTE *pbOutStr, BYTE *pbOutStrLen)
{
    unsigned char uszBuffer[100+1];
    int inRet = 0;

    JNIEnv *env;
    jint rs = (*jvm)->AttachCurrentThread(jvm, &env, NULL);

    unsigned char uszAPName[100+1];
    unsigned char *puszAPName = NULL;
    unsigned char uszJClassName[100+1];

	

    vdDebug_LogPrintf("=====inCallJAVA_VoidMenu Start=====");

	if (strlen(pbDispMsg)>0)
       strcpy(uszBuffer, pbDispMsg);
    vdDebug_LogPrintf("uszBuffer[%s]", uszBuffer);
    jstring jstr = (*env)->NewStringUTF(env, uszBuffer);
    vdDebug_LogPrintf("jstring[%s]", uszBuffer);
	
    jclass clazz = (*env)->FindClass(env, "com/Source/S1_NPX/NPX/Main/MainActivity");

    jmethodID methodID = (*env)->GetMethodID(env, activityClass, "VoidMenu", "(Ljava/lang/String;)Ljava/lang/String;");
	
    //jobject result = (*env)->CallObjectMethod(env, activityObj, methodID);
    jobject result = (*env)->CallObjectMethod(env, activityObj, methodID, jstr);

    jbyte* str = NULL;
    str = (*env)->GetStringUTFChars(env,(jstring) result, NULL);

    if (str!=NULL)
    {
        *pbOutStrLen = strlen(str);
        strcpy(pbOutStr, str);

        (*env)->ReleaseStringUTFChars(env, result, str);

    }
    else
        *pbOutStrLen = 0;

    (*env)->DeleteLocalRef(env, clazz);
    (*env)->DeleteLocalRef(env, result);	
    (*env)->DeleteLocalRef(env, jstr);
	
    vdDebug_LogPrintf("=====inCallJAVA_VoidMenu End=====");

    return d_OK;
}



int inCallJAVA_PresentCardVoid(BYTE *pbDispMsg,  BYTE *pbOutStr, BYTE *pbOutStrLen)
{
    unsigned char uszBuffer[100+1];
    int inRet = 0;

    JNIEnv *env;
    jint rs = (*jvm)->AttachCurrentThread(jvm, &env, NULL);

    unsigned char uszAPName[100+1];
    unsigned char *puszAPName = NULL;
    unsigned char uszJClassName[100+1];

	

    vdDebug_LogPrintf("=====inCallJAVA_PresentCardVoid Start=====");

	if (strlen(pbDispMsg)>0)
       strcpy(uszBuffer, pbDispMsg);
    vdDebug_LogPrintf("uszBuffer[%s]", uszBuffer);
    jstring jstr = (*env)->NewStringUTF(env, uszBuffer);
    vdDebug_LogPrintf("jstring[%s]", uszBuffer);
	
    jclass clazz = (*env)->FindClass(env, "com/Source/S1_NPX/NPX/Main/MainActivity");

    jmethodID methodID = (*env)->GetMethodID(env, activityClass, "PresentCardUIVoid", "(Ljava/lang/String;)Ljava/lang/String;");
	
    //jobject result = (*env)->CallObjectMethod(env, activityObj, methodID);
    jobject result = (*env)->CallObjectMethod(env, activityObj, methodID, jstr);

    jbyte* str = NULL;
    str = (*env)->GetStringUTFChars(env,(jstring) result, NULL);

    if (str!=NULL)
    {
        *pbOutStrLen = strlen(str);
        strcpy(pbOutStr, str);

        (*env)->ReleaseStringUTFChars(env, result, str);

    }
    else
        *pbOutStrLen = 0;

    (*env)->DeleteLocalRef(env, clazz);
    (*env)->DeleteLocalRef(env, result);	
    (*env)->DeleteLocalRef(env, jstr);
	
    vdDebug_LogPrintf("=====inCallJAVA_PresentCardVoid End=====");

    return d_OK;
}



int inCallJAVA_ConfirmAmountUIVoid(BYTE *pbDispMsg,  BYTE *pbOutStr, BYTE *pbOutStrLen)
{
    unsigned char uszBuffer[100+1];
    int inRet = 0;

    JNIEnv *env;
    jint rs = (*jvm)->AttachCurrentThread(jvm, &env, NULL);

    unsigned char uszAPName[100+1];
    unsigned char *puszAPName = NULL;
    unsigned char uszJClassName[100+1];

	

    vdDebug_LogPrintf("=====inCallJAVA_ConfirmAmountUIVoid Start=====");

	if (strlen(pbDispMsg)>0)
       strcpy(uszBuffer, pbDispMsg);
    vdDebug_LogPrintf("uszBuffer[%s]", uszBuffer);
    jstring jstr = (*env)->NewStringUTF(env, uszBuffer);
    vdDebug_LogPrintf("jstring[%s]", uszBuffer);
	
    jclass clazz = (*env)->FindClass(env, "com/Source/S1_NPX/NPX/Main/MainActivity");

    jmethodID methodID = (*env)->GetMethodID(env, activityClass, "ConfirmAmountUIVoid", "(Ljava/lang/String;)Ljava/lang/String;");
	
    //jobject result = (*env)->CallObjectMethod(env, activityObj, methodID);
    jobject result = (*env)->CallObjectMethod(env, activityObj, methodID, jstr);

    jbyte* str = NULL;
    str = (*env)->GetStringUTFChars(env,(jstring) result, NULL);

    if (str!=NULL)
    {
        *pbOutStrLen = strlen(str);
        strcpy(pbOutStr, str);

        (*env)->ReleaseStringUTFChars(env, result, str);

    }
    else
        *pbOutStrLen = 0;

    (*env)->DeleteLocalRef(env, clazz);
    (*env)->DeleteLocalRef(env, result);	
    (*env)->DeleteLocalRef(env, jstr);
	
    vdDebug_LogPrintf("=====inCallJAVA_ConfirmAmountUIVoid End=====");

    return d_OK;
}




int inCallJAVA_ApproveUIVoid(BYTE *pbDispMsg,  BYTE *pbOutStr, BYTE *pbOutStrLen)
{
    unsigned char uszBuffer[100+1];
    int inRet = 0;

    JNIEnv *env;
    jint rs = (*jvm)->AttachCurrentThread(jvm, &env, NULL);

    unsigned char uszAPName[100+1];
    unsigned char *puszAPName = NULL;
    unsigned char uszJClassName[100+1];

	

    vdDebug_LogPrintf("=====inCallJAVA_ApproveUIVoid Start=====");

	if (strlen(pbDispMsg)>0)
       strcpy(uszBuffer, pbDispMsg);
    vdDebug_LogPrintf("uszBuffer[%s]", uszBuffer);
    jstring jstr = (*env)->NewStringUTF(env, uszBuffer);
    vdDebug_LogPrintf("jstring[%s]", uszBuffer);
	
    jclass clazz = (*env)->FindClass(env, "com/Source/S1_NPX/NPX/Main/MainActivity");

    jmethodID methodID = (*env)->GetMethodID(env, activityClass, "ApproveUIVoid", "(Ljava/lang/String;)Ljava/lang/String;");
	
    //jobject result = (*env)->CallObjectMethod(env, activityObj, methodID);
    jobject result = (*env)->CallObjectMethod(env, activityObj, methodID, jstr);

    jbyte* str = NULL;
    str = (*env)->GetStringUTFChars(env,(jstring) result, NULL);

    if (str!=NULL)
    {
        *pbOutStrLen = strlen(str);
        strcpy(pbOutStr, str);

        (*env)->ReleaseStringUTFChars(env, result, str);

    }
    else
        *pbOutStrLen = 0;

    (*env)->DeleteLocalRef(env, clazz);
    (*env)->DeleteLocalRef(env, result);	
    (*env)->DeleteLocalRef(env, jstr);
	
    vdDebug_LogPrintf("=====inCallJAVA_ApproveUIVoid End=====");

    return d_OK;
}





int inCallJAVA_ReadCardError(BYTE *pbDispMsg,  BYTE *pbOutStr, BYTE *pbOutStrLen)
{
    unsigned char uszBuffer[100+1];
    int inRet = 0;

    JNIEnv *env;
    jint rs = (*jvm)->AttachCurrentThread(jvm, &env, NULL);

    unsigned char uszAPName[100+1];
    unsigned char *puszAPName = NULL;
    unsigned char uszJClassName[100+1];

	

    vdDebug_LogPrintf("=====inCallJAVA_ReadCardError Start=====");

	if (strlen(pbDispMsg)>0)
       strcpy(uszBuffer, pbDispMsg);
    vdDebug_LogPrintf("uszBuffer[%s]", uszBuffer);
    jstring jstr = (*env)->NewStringUTF(env, uszBuffer);
    vdDebug_LogPrintf("jstring[%s]", uszBuffer);
	
    jclass clazz = (*env)->FindClass(env, "com/Source/S1_NPX/NPX/Main/MainActivity");

    jmethodID methodID = (*env)->GetMethodID(env, activityClass, "ReadCardError", "(Ljava/lang/String;)Ljava/lang/String;");
	
    //jobject result = (*env)->CallObjectMethod(env, activityObj, methodID);
    jobject result = (*env)->CallObjectMethod(env, activityObj, methodID, jstr);

    jbyte* str = NULL;
    str = (*env)->GetStringUTFChars(env,(jstring) result, NULL);

    if (str!=NULL)
    {
        *pbOutStrLen = strlen(str);
        strcpy(pbOutStr, str);

        (*env)->ReleaseStringUTFChars(env, result, str);

    }
    else
        *pbOutStrLen = 0;

    (*env)->DeleteLocalRef(env, clazz);
    (*env)->DeleteLocalRef(env, result);	
    (*env)->DeleteLocalRef(env, jstr);
	
    vdDebug_LogPrintf("=====inCallJAVA_ReadCardError End=====");

    return d_OK;
}




int inCallJAVA_TransCancellVoid(BYTE *pbDispMsg,  BYTE *pbOutStr, BYTE *pbOutStrLen)
{
    unsigned char uszBuffer[100+1];
    int inRet = 0;

    JNIEnv *env;
    jint rs = (*jvm)->AttachCurrentThread(jvm, &env, NULL);

    unsigned char uszAPName[100+1];
    unsigned char *puszAPName = NULL;
    unsigned char uszJClassName[100+1];

	

    vdDebug_LogPrintf("=====inCallJAVA_TransCancellVoid Start=====");

	if (strlen(pbDispMsg)>0)
       strcpy(uszBuffer, pbDispMsg);
    vdDebug_LogPrintf("uszBuffer[%s]", uszBuffer);
    jstring jstr = (*env)->NewStringUTF(env, uszBuffer);
    vdDebug_LogPrintf("jstring[%s]", uszBuffer);
	
    jclass clazz = (*env)->FindClass(env, "com/Source/S1_NPX/NPX/Main/MainActivity");

    jmethodID methodID = (*env)->GetMethodID(env, activityClass, "TransCancellVoid", "(Ljava/lang/String;)Ljava/lang/String;");
	
    //jobject result = (*env)->CallObjectMethod(env, activityObj, methodID);
    jobject result = (*env)->CallObjectMethod(env, activityObj, methodID, jstr);

    jbyte* str = NULL;
    str = (*env)->GetStringUTFChars(env,(jstring) result, NULL);

    if (str!=NULL)
    {
        *pbOutStrLen = strlen(str);
        strcpy(pbOutStr, str);

        (*env)->ReleaseStringUTFChars(env, result, str);

    }
    else
        *pbOutStrLen = 0;

    (*env)->DeleteLocalRef(env, clazz);
    (*env)->DeleteLocalRef(env, result);	
    (*env)->DeleteLocalRef(env, jstr);
	
    vdDebug_LogPrintf("=====inCallJAVA_TransCancellVoid End=====");

    return d_OK;
}



int inCallJAVA_NotApproveUIVoid(BYTE *pbDispMsg,  BYTE *pbOutStr, BYTE *pbOutStrLen)
{
    unsigned char uszBuffer[100+1];
    int inRet = 0;

    JNIEnv *env;
    jint rs = (*jvm)->AttachCurrentThread(jvm, &env, NULL);

    unsigned char uszAPName[100+1];
    unsigned char *puszAPName = NULL;
    unsigned char uszJClassName[100+1];

	

    vdDebug_LogPrintf("=====inCallJAVA_NotApproveUIVoid Start=====");

	if (strlen(pbDispMsg)>0)
       strcpy(uszBuffer, pbDispMsg);
    vdDebug_LogPrintf("uszBuffer[%s]", uszBuffer);
    jstring jstr = (*env)->NewStringUTF(env, uszBuffer);
    vdDebug_LogPrintf("jstring[%s]", uszBuffer);
	
    jclass clazz = (*env)->FindClass(env, "com/Source/S1_NPX/NPX/Main/MainActivity");

    jmethodID methodID = (*env)->GetMethodID(env, activityClass, "NotApproveUIVoid", "(Ljava/lang/String;)Ljava/lang/String;");
	
    //jobject result = (*env)->CallObjectMethod(env, activityObj, methodID);
    jobject result = (*env)->CallObjectMethod(env, activityObj, methodID, jstr);

    jbyte* str = NULL;
    str = (*env)->GetStringUTFChars(env,(jstring) result, NULL);

    if (str!=NULL)
    {
        *pbOutStrLen = strlen(str);
        strcpy(pbOutStr, str);

        (*env)->ReleaseStringUTFChars(env, result, str);

    }
    else
        *pbOutStrLen = 0;

    (*env)->DeleteLocalRef(env, clazz);
    (*env)->DeleteLocalRef(env, result);	
    (*env)->DeleteLocalRef(env, jstr);
	
    vdDebug_LogPrintf("=====inCallJAVA_NotApproveUIVoid End=====");

    return d_OK;
}


int inCallJAVA_TransNotFound(BYTE *pbDispMsg,  BYTE *pbOutStr, BYTE *pbOutStrLen)
{
    unsigned char uszBuffer[100+1];
    int inRet = 0;

    JNIEnv *env;
    jint rs = (*jvm)->AttachCurrentThread(jvm, &env, NULL);

    unsigned char uszAPName[100+1];
    unsigned char *puszAPName = NULL;
    unsigned char uszJClassName[100+1];

	

    vdDebug_LogPrintf("=====inCallJAVA_PresentCardVoid Start=====");

	if (strlen(pbDispMsg)>0)
       strcpy(uszBuffer, pbDispMsg);
    vdDebug_LogPrintf("uszBuffer[%s]", uszBuffer);
    jstring jstr = (*env)->NewStringUTF(env, uszBuffer);
    vdDebug_LogPrintf("jstring[%s]", uszBuffer);
	
    jclass clazz = (*env)->FindClass(env, "com/Source/S1_NPX/NPX/Main/MainActivity");

    jmethodID methodID = (*env)->GetMethodID(env, activityClass, "TransNotFound", "(Ljava/lang/String;)Ljava/lang/String;");
	
    //jobject result = (*env)->CallObjectMethod(env, activityObj, methodID);
    jobject result = (*env)->CallObjectMethod(env, activityObj, methodID, jstr);

    jbyte* str = NULL;
    str = (*env)->GetStringUTFChars(env,(jstring) result, NULL);

    if (str!=NULL)
    {
        *pbOutStrLen = strlen(str);
        strcpy(pbOutStr, str);

        (*env)->ReleaseStringUTFChars(env, result, str);

    }
    else
        *pbOutStrLen = 0;

    (*env)->DeleteLocalRef(env, clazz);
    (*env)->DeleteLocalRef(env, result);	
    (*env)->DeleteLocalRef(env, jstr);
	
    vdDebug_LogPrintf("=====inCallJAVA_PresentCardVoid End=====");

    return d_OK;
}


int inCallJAVA_DisplayRemoveCardMsg(BYTE *pbDispMsg,  BYTE *pbOutStr, BYTE *pbOutStrLen)
{
    unsigned char uszBuffer[100+1];
    int inRet = 0;

    unsigned char uszAPName[100+1];
    unsigned char *puszAPName = NULL;
    unsigned char uszJClassName[100+1];

    // JNIEnv *env;
    // jint rs = (*jvm)->AttachCurrentThread(jvm, &env, NULL);
    JNIEnv *g_env = NULL;
    // double check it's all ok
	int getEnvStat;
	getEnvStat = (*jvm)->GetEnv(jvm, (void **) &g_env, JNI_VERSION_1_6);
    if (getEnvStat == JNI_EDETACHED) {
		vdDebug_LogPrintf("GetEnv: not attached");
        if ((*jvm)->AttachCurrentThread(jvm, (void **) &g_env, NULL) != 0) {
			vdDebug_LogPrintf("Failed to attach");
        }
    } else if (getEnvStat == JNI_OK) {
        //
    } else if (getEnvStat == JNI_EVERSION) {
    	vdDebug_LogPrintf("GetEnv: version not supported");
    }

    vdDebug_LogPrintf("=====inCallJAVA_DisplayMsg Start=====");

    if (strlen(pbDispMsg)>0)
        strcpy(uszBuffer, pbDispMsg);
    vdDebug_LogPrintf("uszBuffer[%s]", uszBuffer);
    jstring jstr = (*g_env)->NewStringUTF(g_env, uszBuffer);
    vdDebug_LogPrintf("jstring[%s]", uszBuffer);

    jclass clazz = (*g_env)->FindClass(g_env, "com/Source/S1_NPX/NPX/Main/MainActivity");

    jmethodID methodID = (*g_env)->GetMethodID(g_env, activityClass, "DisplayMsg", "(Ljava/lang/String;)Ljava/lang/String;");

    jobject result = (*g_env)->CallObjectMethod(g_env, activityObj, methodID, jstr);

    jbyte* str = NULL;
    str = (*g_env)->GetStringUTFChars(g_env,(jstring) result, NULL);

    if (str!=NULL)
    {
        *pbOutStrLen = strlen(str);
        strcpy(pbOutStr, str);

        (*g_env)->ReleaseStringUTFChars(g_env, result, str);

    }
    else
        *pbOutStrLen = 0;

    (*g_env)->DeleteLocalRef(g_env, clazz);
    (*g_env)->DeleteLocalRef(g_env, result);	
    (*g_env)->DeleteLocalRef(g_env, jstr);

    vdDebug_LogPrintf("=====inCallJAVA_DisplayMsg End=====");

    return d_OK;
}

