/*******************************************************************************

*******************************************************************************/
#include <string.h>
#include <stdio.h>
#include <stdlib.h>
#include <ctosapi.h>
#include <ctoskia.h>

#include "../Includes/DMenu.h"

#include "../Includes/myEZLib.h"
#include "../Includes/msg.h"
#include "../Includes/wub_lib.h"


#include "..\Includes\CTOSInput.h"
#include "../Includes/POSTypedef.h"
#include "../FileModule/myFileFunc.h"

#include "../print/Print.h"
#include "../FileModule/myFileFunc.h"
#include "../Comm/V5Comm.h"


#include "..\Includes\CTOSInput.h"
#include "..\Includes\CfgExpress.h"
#include "..\Includes\epad.h"
#include "..\Includes\EFTSec.h"


#include "../Accum/accum.h"
#include "../DataBase/DataBaseFunc.h"
#include "..\Includes\POSSetting.h"
#include "..\ui\Display.h"
#include "..\Database\DatabaseFunc.h"
#include "..\Database\NPX.h"

#include "..\debug\debug.h"
#include "..\Includes\POSTrans.h"
#include "..\Accum\Accum.h"
#include "..\filemodule\myFileFunc.h"
#include "..\Includes\MultiApLib.h"
#include "..\Aptrans\MultiAptrans.h"
#include "..\Aptrans\MultiShareEMV.h"
#include "..\Aptrans\MultiSharecom.h"
#include "..\PCI100\PCI100.h"
#include "..\tms\tms.h"


#define DISPLAY_POSITION_LEFT 0
#define DISPLAY_POSITION_CENTER 1
#define DISPLAY_POSITION_RIGHT 2
#define DISPLAY_LINE_SIZE 16

static int inSleepflag = 0;
BOOL BolDetachGPRSChangeSetting=FALSE, BolDetachDIALChangeSetting=FALSE;

extern int inEthernet_GetTerminalIP(char *szTerminalIP);

void vdCTOS_uiIDLEPowerOff(void)
{
    USHORT usRet1;
    BYTE  bPercentage ;

    usRet1 = CTOS_BatteryGetCapacity(&bPercentage);
    if(d_BATTERY_NOT_EXIST == usRet1 || d_BATTERY_NOT_SUPPORT == usRet1)
    {
		return;
    }

	vdCTOS_uiPowerOff();
}

void vdCTOS_uiIDLESleepMode(void)
{
    USHORT usRet1;
    BYTE  bPercentage ;
	UCHAR usRet;
    DWORD pdwStatus ;

	usRet1 = CTOS_PowerSource(&usRet);
	vdDebug_LogPrintf("CTOS_PowerSource=[%x]..usRet=[%d]..inSleepflag=[%d]....",usRet1,usRet,inSleepflag);
	if ((d_OK == usRet1) && (usRet == d_PWRSRC_BATTERY))
	{
		if (inSleepflag == 0)
		{
			vdDebug_LogPrintf("vdSetsysPowerLowSpeed......");
			CTOS_BackLightSet (d_BKLIT_LCD, d_OFF);
			CTOS_BackLightSet (d_BKLIT_KBD, d_OFF);
			vdSetsysPowerLowSpeed();
			inSleepflag = 1;
		}
	}
	else
		vdCTOS_uiIDLEWakeUpSleepMode();
	return;
	
}

void vdCTOS_uiIDLEWakeUpSleepMode(void)
{
	//vdDebug_LogPrintf("vdCTOS_uiIDLEWakeUpSleepMode,inSleepflag=[%d]......",inSleepflag);
	if (inSleepflag == 1)
	{
		vdDebug_LogPrintf("vdSetsysPowerHighSpeed......");
		vdSetsysPowerHighSpeed();

		CTOS_BackLightSet (d_BKLIT_LCD, d_ON);
		CTOS_BackLightSet (d_BKLIT_KBD, d_ON);
		inSleepflag = 0;
	}
	return;
}



int inCTOSS_CheckBatteryChargeStatus(void)
{
	USHORT usRet1;
	BYTE  bPercentage ;
	UCHAR usRet;
	DWORD pdwStatus ;

	usRet1 = CTOS_PowerSource(&usRet);
	//vdDebug_LogPrintf("CTOS_PowerSource=[%x]..usRet=[%d]......",usRet1,usRet);
	if ((d_OK == usRet1) && (usRet == d_PWRSRC_BATTERY))
	{
		return d_NO;
	}
	return d_OK;
	
}


void vdCTOS_uiPowerOff(void)
{
    BYTE block[6] = {0xff,0xff,0xff,0xff,0xff,0xff};
    USHORT ya,yb,xa,xb;
    unsigned char c;
        
    CTOS_LCDTClearDisplay();

    vduiDisplayStringCenter(3,"ARE YOU SURE");
    vduiDisplayStringCenter(4,"WANT TO POWER");
    vduiDisplayStringCenter(5,"OFF TERMINAL");
    vduiDisplayStringCenter(6,"   REBOOT[<-]");
    vduiDisplayStringCenter(7,"NO[X]   YES[OK] ");

    c=WaitKey(60);

    if (c==d_KBD_CLEAR)
    {
		CTOS_SystemReset();
    }
	
    if(c!=d_KBD_ENTER)
    {
        return;
    }
    
    for(ya =1; ya<5; ya++)
    {
        CTOS_Delay(100);
        CTOS_LCDTGotoXY(1,ya);
        CTOS_LCDTClear2EOL();
    }
    for(yb=8; yb>4; yb--)
    {
        CTOS_Delay(100);
        CTOS_LCDTGotoXY(1,yb);
        CTOS_LCDTClear2EOL();
    }
    CTOS_LCDTPrintXY(1,4,"----------------");
    for(xa=1; xa<8; xa++)
    {
        CTOS_Delay(25);
        CTOS_LCDTPrintXY(xa,4," ");
    }
    for(xb=16; xb>7; xb--)
    {
        CTOS_Delay(25);
        CTOS_LCDTPrintXY(xb,4," ");
    }
            
    CTOS_LCDGShowPic(58, 6, block, 0, 6);
    CTOS_Delay(250);
    CTOS_LCDTGotoXY(7,4);
    CTOS_LCDTClear2EOL();
    CTOS_Delay(250);

    CTOS_PowerOff();
}

int inCTOS_SelectHostSetting(void)
{
    BYTE bHeaderAttr = 0x01+0x04, iCol = 1;
    BYTE  x = 1;
    BYTE key;
    char szHeaderString[50] = "SELECT HOST";
    char szHostMenu[1024];
    char szHostName[50][100];
    int inCPTID[50];
    int inLoop = 0;

	
    vdDebug_LogPrintf("inCTOS_SelectHostSetting");
    
    if (0 == fGetAppRunBySelf() && inMultiAP_CheckSubAPStatus() == d_OK)
        return d_OK;


	/*if it is sub App, just hardcode the NPX host, no need menu*/
	if(inMultiAP_CheckSubAPStatus() == d_OK)
	{
		vdDebug_LogPrintf("if it is sub App, just hardcode the NPX host, no need menu");
		//return inCTOS_SelectNPXHost();
		key = inCTOS_SelectNPXHost();
		if (key == d_OK)
			return strHDT.inHostIndex;
		return -1;
	}
    
	
	vdDebug_LogPrintf("inCTOS_SelectHostSetting");

    memset(szHostMenu, 0x00, sizeof(szHostMenu));
    memset(szHostName, 0x00, sizeof(szHostName));
    memset((char*)inCPTID, 0x00, sizeof(inCPTID));

    inHDTReadHostName(szHostName, inCPTID);

    for (inLoop = 0; inLoop < 50; inLoop++)
    {
        if (szHostName[inLoop][0]!= 0)
        {
            strcat((char *)szHostMenu, szHostName[inLoop]);
            if (szHostName[inLoop+1][0]!= 0)
                strcat((char *)szHostMenu, (char *)" \n");      
        }
        else
            break;
    }
   
    key = MenuDisplay(szHeaderString, strlen(szHeaderString), bHeaderAttr, iCol, x, szHostMenu, TRUE);

    if (key == 0xFF) 
    {
        CTOS_LCDTClearDisplay();
        setLCDPrint(1, DISPLAY_POSITION_CENTER, "WRONG INPUT!!!");
        vduiWarningSound();
        return -1;  
    }

    if(key > 0)
    {
        if(d_KBD_CANCEL == key)
            return -1;
        
        vdDebug_LogPrintf("key[%d] HostID[%d]", key, inCPTID[key-1]);
        srTransRec.HDTid = inCPTID[key-1];
        strHDT.inHostIndex = inCPTID[key-1];
        inHDTRead(inCPTID[key-1]);
        inCPTRead(inCPTID[key-1]);
        strcpy(srTransRec.szHostLabel, strHDT.szHostLabel);
    }
    
    return inCPTID[key-1];
}

void vdCTOS_MdmPPPConfig(void)
{
    BYTE bRet,strOut[30],strtemp[17],key;
    BYTE szInputBuf[24+1];
    BYTE szIntComBuf[2];
    BYTE szPhNoBuf[9];
    BYTE szExtNoBuf[4];
    int inResult;
    USHORT ret;
    USHORT usLen;
    int shHostIndex = 1;
        
    inResult = inMPTRead(shHostIndex);  
    if (inResult != d_OK)
        return;
    
    CTOS_LCDTClearDisplay();
    vdDispTitleString("MDM PPP Setting");
    
	while(1)
	{
		vduiClearBelow(3);
		setLCDPrint(3, DISPLAY_POSITION_LEFT, "Primary ISP Line");
		setLCDPrint(4, DISPLAY_POSITION_LEFT, strMPT.szPriISPPhoneNumber);

            strcpy(strtemp,"New:") ; 
            CTOS_LCDTPrintXY(1, 7, strtemp);
            memset(strOut,0x00, sizeof(strOut));
            usLen = 18;
            ret = InputStringAlpha(1, 8, 0x00, 0x02, strOut, &usLen, 1, d_INPUT_TIMEOUT);
            if (ret == d_KBD_CANCEL )
                break;
            else if(0 == ret )
                break;
            else if(ret>= 1)
            {
                memcpy(strMPT.szPriISPPhoneNumber,strOut,strlen(strOut));
               	strMPT.szPriISPPhoneNumber[strlen(strOut)]=0;
                break;
            }
            if(ret == d_KBD_CANCEL)
                break;

        }

        while(1)
        {
            vduiClearBelow(3);
            setLCDPrint(3, DISPLAY_POSITION_LEFT, "Second ISP Line");
            setLCDPrint(4, DISPLAY_POSITION_LEFT, strMPT.szSecISPPhoneNumber);
            
            strcpy(strtemp,"New:") ; 
            CTOS_LCDTPrintXY(1, 7, strtemp);
            memset(strOut,0x00, sizeof(strOut));
            usLen = 18;
            ret = InputStringAlpha(1, 8, 0x00, 0x02, strOut, &usLen, 1, d_INPUT_TIMEOUT);
            if (ret == d_KBD_CANCEL )
                break;
            else if(0 == ret )
                break;
            else if(ret>= 1)
            {
                //BolDetachDIALChangeSetting = TRUE;
                memcpy(strMPT.szSecISPPhoneNumber,strOut,strlen(strOut));
                strMPT.szSecISPPhoneNumber[strlen(strOut)]=0;
                break;
            }
            if(ret == d_KBD_CANCEL)
                break;

        }
		
		while(1)
		{
			vduiClearBelow(3);
			
			setLCDPrint(3, DISPLAY_POSITION_LEFT, "ISP USER NAME");
			setLCDPrint(4, DISPLAY_POSITION_LEFT, strMPT.szUserName);
		
			strcpy(strtemp,"New:") ;
			CTOS_LCDTPrintXY(1, 7, strtemp);
			memset(strOut,0x00, sizeof(strOut));
			usLen = 18;
			ret= InputStringAlpha(1, 8, 0x00, 0x02, strOut, &usLen, 1, d_INPUT_TIMEOUT);
			if(ret==d_KBD_ENTER)
			{
					BolDetachGPRSChangeSetting=TRUE;
					memcpy(strMPT.szUserName, strOut,strlen(strOut));
					strMPT.szUserName[strlen(strOut)]=0;
					inResult = inTCPSave(1);
					break;
			}	
			if(ret == d_KBD_CANCEL)
				break;
		}				
		
		while(1)
		{
			vduiClearBelow(3);
			setLCDPrint(3, DISPLAY_POSITION_LEFT, "ISP PASSWORD");
			setLCDPrint(4, DISPLAY_POSITION_LEFT, strMPT.szPassword);
		
			strcpy(strtemp,"New:") ;
			CTOS_LCDTPrintXY(1, 7, strtemp);
			memset(strOut,0x00, sizeof(strOut));
			usLen = 18;
			ret= InputStringAlpha(1, 8, 0x00, 0x02, strOut, &usLen, 1, d_INPUT_TIMEOUT);
				
			if(ret==d_KBD_ENTER)
			{
					BolDetachGPRSChangeSetting=TRUE;
					memcpy(strMPT.szPassword, strOut,strlen(strOut));
					strMPT.szPassword[strlen(strOut)]=0;
					inResult = inTCPSave(1);
					break;
			}	
			if(ret == d_KBD_CANCEL)
				break;
		}	

		while(1)
        {   
            vduiClearBelow(3);
            setLCDPrint(3, DISPLAY_POSITION_LEFT, "MDM TxRx Blk");
            memset(szInputBuf, 0x00, sizeof(szInputBuf));
            sprintf(szInputBuf, "%d", strMPT.inTxRxBlkSize);
            setLCDPrint(4, DISPLAY_POSITION_LEFT, szInputBuf);
            
            strcpy(strtemp,"New:") ;
            CTOS_LCDTPrintXY(1, 7, strtemp);
            memset(strOut,0x00, sizeof(strOut));
			usLen = 4;
            ret= shCTOS_GetNum(8, 0x01,  strOut, &usLen, 1, 5, 0, d_INPUT_TIMEOUT);
            if (ret == d_KBD_CANCEL )
                break;
            else if(0 == ret )
                break;
            else if(ret>=1)
            {
                strMPT.inTxRxBlkSize = atoi(strOut);      
                vdMyEZLib_LogPrintf("inTxRxBlkSize %d", strMPT.inTxRxBlkSize);
                break;
            }   
            if(ret == d_KBD_CANCEL)
                break;
        }


    	inResult = inMPTSave(shHostIndex);      
    	inResult = inMPTRead(shHostIndex);      
        srTransRec.usTerminalCommunicationMode = strCPT.inCommunicationMode;
}

/*configuration functions*/
//for COM&USB communication
void vdCTOS_IPConfig(void)
{
    BYTE bRet, key;
    BYTE szInputBuf[5];
    int inResult;
    BYTE strOut[30],strtemp[17];
    USHORT ret;
    USHORT usLen;
    BOOL BolDetachLANChange=FALSE;
    int shHostIndex = 1;

    shHostIndex = inCTOS_SelectHostSetting();
    if (shHostIndex == -1)
        return;
                
    inResult = inCPTRead(shHostIndex);
    if(inResult != d_OK)
        return;

    CTOS_LCDTClearDisplay();
    vdDispTitleString("HOST SETTING");
    while(1)
    {
        vduiClearBelow(2);//for COM&USB communication
        setLCDPrint(2, DISPLAY_POSITION_LEFT, "Pri Connection Type");
        if((strCPT.inCommunicationMode)== DIAL_UP_MODE)
            setLCDPrint(3, DISPLAY_POSITION_LEFT, "0");        
        else if((strCPT.inCommunicationMode)== ETHERNET_MODE)
            setLCDPrint(3, DISPLAY_POSITION_LEFT, "1");
        else if((strCPT.inCommunicationMode)== GPRS_MODE)
            setLCDPrint(3, DISPLAY_POSITION_LEFT, "2");
        else if((strCPT.inCommunicationMode)== MDM_PPP_MODE)
            setLCDPrint(3, DISPLAY_POSITION_LEFT, "3");
        else if((strCPT.inCommunicationMode)== WIFI_MODE)
            setLCDPrint(3, DISPLAY_POSITION_LEFT, "4");
        else if((strCPT.inCommunicationMode)== USB_MODE)
            setLCDPrint(3, DISPLAY_POSITION_LEFT, "5");		
		else if((strCPT.inCommunicationMode)== COM1_MODE)
            setLCDPrint(3, DISPLAY_POSITION_LEFT, "6");
        else if((strCPT.inCommunicationMode)== COM2_MODE)
            setLCDPrint(3, DISPLAY_POSITION_LEFT, "7");
		else if((strCPT.inCommunicationMode)== FORWARD_MODE)
            setLCDPrint(3, DISPLAY_POSITION_LEFT, "8");
		else if((strCPT.inCommunicationMode)== BT_MODE)
            setLCDPrint(3, DISPLAY_POSITION_LEFT, "10");
		else
            setLCDPrint(3, DISPLAY_POSITION_LEFT, "0");        
        
        CTOS_LCDTPrintXY(1, 4, "0-DIAL-UP1-LAN 2-GPRS");
        CTOS_LCDTPrintXY(1, 5, "3-MODEM PPP 4-WIFI");
		CTOS_LCDTPrintXY(1, 6, "5-USB 6-COM1 7-COM2");
		CTOS_LCDTPrintXY(1, 7, "8-FORWARD 10-BT");
        
        strcpy(strtemp,"New:") ;
        CTOS_LCDTPrintXY(1, 8, strtemp);
        memset(strOut,0x00, sizeof(strOut));
        ret= shCTOS_GetNum(8, 0x01,  strOut, &usLen, 1, 2, 0, d_INPUT_TIMEOUT);
        if (ret == d_KBD_CANCEL )
            break;
        else if(0 == ret )
            break;
        else if(ret==1)
        {
            if (strOut[0]==0x30 || strOut[0]==0x31 || strOut[0]==0x32 || strOut[0]==0x33 || strOut[0]==0x34 || strOut[0]==0x35 || strOut[0]==0x36 || strOut[0]==0x37 || strOut[0]==0x38)
            {
                 if(strOut[0] == 0x30)
                 {
                        strCPT.inCommunicationMode = DIAL_UP_MODE;
                        BolDetachDIALChangeSetting = TRUE;
                 }
                 else if(strOut[0] == 0x31)
                 {
                        strCPT.inCommunicationMode = ETHERNET_MODE;
                        BolDetachLANChange = TRUE;
                 }
                 else if(strOut[0] == 0x32)
                 {
                        strCPT.inCommunicationMode = GPRS_MODE;
                        BolDetachGPRSChangeSetting = TRUE;
                 }
				 else if(strOut[0] == 0x33)
                 {
                        strCPT.inCommunicationMode = MDM_PPP_MODE;
                 }
				 else if(strOut[0] == 0x34)
                 {
                        strCPT.inCommunicationMode = WIFI_MODE;
				 }
                 else if(strOut[0] == 0x35)
                 {
                        strCPT.inCommunicationMode = USB_MODE;
                 }
				 else if(strOut[0] == 0x36)////for COM&USB communication
                 {
                        strCPT.inCommunicationMode = COM1_MODE;
                 }
                 else if(strOut[0] == 0x37)
                 {
                        strCPT.inCommunicationMode = COM2_MODE;
                 }
				 else if(strOut[0] == 0x38)
                 {
                        strCPT.inCommunicationMode = FORWARD_MODE;
                 }
                 break;
             }
             else
             {
                vduiWarningSound();
                vduiClearBelow(6);
                vduiDisplayStringCenter(6,"PLEASE SELECT");
                vduiDisplayStringCenter(7,"A VALID");
                vduiDisplayStringCenter(8,"CONNECTION MODE");
                CTOS_Delay(2000);       
            }
        }
		else if (ret == 2)
		{
			if (strcmp(strOut, "10") == 0)
				strCPT.inCommunicationMode = BT_MODE;
			
			break;
		}
    }
    inResult = inCPTSave(shHostIndex);
    inResult = inCPTRead(shHostIndex);

	while(1)
    {
        vduiClearBelow(2);////for COM&USB communication
        setLCDPrint(2, DISPLAY_POSITION_LEFT, "Sec Connection Type");
        if((strCPT.inSecCommunicationMode)== DIAL_UP_MODE)
            setLCDPrint(3, DISPLAY_POSITION_LEFT, "0");        
        else if((strCPT.inSecCommunicationMode)== ETHERNET_MODE)
            setLCDPrint(3, DISPLAY_POSITION_LEFT, "1");
        else if((strCPT.inSecCommunicationMode)== GPRS_MODE)
            setLCDPrint(3, DISPLAY_POSITION_LEFT, "2");
        else if((strCPT.inSecCommunicationMode)== MDM_PPP_MODE)
            setLCDPrint(3, DISPLAY_POSITION_LEFT, "3");
        else if((strCPT.inSecCommunicationMode)== WIFI_MODE)
            setLCDPrint(3, DISPLAY_POSITION_LEFT, "4");
        else if((strCPT.inSecCommunicationMode)== USB_MODE)
            setLCDPrint(3, DISPLAY_POSITION_LEFT, "5");		
		else if((strCPT.inSecCommunicationMode)== COM1_MODE)
            setLCDPrint(3, DISPLAY_POSITION_LEFT, "6");
        else if((strCPT.inSecCommunicationMode)== COM2_MODE)
            setLCDPrint(3, DISPLAY_POSITION_LEFT, "7");
		else if((strCPT.inSecCommunicationMode)== FORWARD_MODE)
            setLCDPrint(3, DISPLAY_POSITION_LEFT, "8");
		else if((strCPT.inSecCommunicationMode)== NULL_MODE)
				setLCDPrint(3, DISPLAY_POSITION_LEFT, "9");
		else if((strCPT.inSecCommunicationMode)== BT_MODE)
            setLCDPrint(3, DISPLAY_POSITION_LEFT, "10");

        CTOS_LCDTPrintXY(1, 4, "0-DIAL-UP1-LAN2-GPRS");
        CTOS_LCDTPrintXY(1, 5, "3-MODEM PPP 4-WIFI");
		CTOS_LCDTPrintXY(1, 6, "5-USB 6-COM1 7-COM2");
		CTOS_LCDTPrintXY(1, 7, "8-FORWARD 9-NA 10-BT");
   
        strcpy(strtemp,"New:") ;
        CTOS_LCDTPrintXY(1, 8, strtemp);
        memset(strOut,0x00, sizeof(strOut));
        ret= shCTOS_GetNum(8, 0x01,  strOut, &usLen, 1, 2, 0, d_INPUT_TIMEOUT);
        if (ret == d_KBD_CANCEL )
            break;
        else if(0 == ret )
            break;
        else if(ret==1)
        {
            if (strOut[0]==0x30 || strOut[0]==0x31 || strOut[0]==0x32 || strOut[0]==0x33 || strOut[0]==0x34 || strOut[0]==0x35 || strOut[0]==0x36 || strOut[0]==0x37 || strOut[0]==0x38 || strOut[0]==0x39)
            {
				if(strOut[0] == 0x30)
				{
					   strCPT.inSecCommunicationMode = DIAL_UP_MODE;
					   BolDetachDIALChangeSetting = TRUE;
				}
				else if(strOut[0] == 0x31)
				{
					   strCPT.inSecCommunicationMode = ETHERNET_MODE;
					   BolDetachLANChange = TRUE;
				}
				else if(strOut[0] == 0x32)
				{
					   strCPT.inSecCommunicationMode = GPRS_MODE;
					   BolDetachGPRSChangeSetting = TRUE;
				}
				else if(strOut[0] == 0x33)
				{
					   strCPT.inSecCommunicationMode = MDM_PPP_MODE;
				}
				else if(strOut[0] == 0x34)
				{
					   strCPT.inSecCommunicationMode = WIFI_MODE;
				}
				else if(strOut[0] == 0x35)
				{
					   strCPT.inSecCommunicationMode = USB_MODE;
				}
				else if(strOut[0] == 0x36)////for COM&USB communication
				{
					   strCPT.inSecCommunicationMode = COM1_MODE;
				}
				else if(strOut[0] == 0x37)
				{
					   strCPT.inSecCommunicationMode = COM2_MODE;
				}
				else if(strOut[0] == 0x38)
				{
					   strCPT.inSecCommunicationMode = FORWARD_MODE;
				}
				else if(strOut[0] == 0x39)
				{
					   strCPT.inSecCommunicationMode = NULL_MODE;
				}
                 break;
             }
             else
             {
                vduiWarningSound();
                vduiClearBelow(6);
                vduiDisplayStringCenter(6,"PLEASE SELECT");
                vduiDisplayStringCenter(7,"A VALID");
                vduiDisplayStringCenter(8,"CONNECTION MODE");
                CTOS_Delay(2000);       
            }
        }
		else if (ret == 2)
		{
			if (strcmp(strOut, "10") == 0)
				strCPT.inSecCommunicationMode = BT_MODE;

			break;
		}
    }
    inResult = inCPTSave(shHostIndex);

    if(((strCPT.inCommunicationMode)== COM1_MODE) || ((strCPT.inCommunicationMode)== COM2_MODE)
		|| ((strCPT.inSecCommunicationMode)== COM1_MODE) || ((strCPT.inSecCommunicationMode)== COM2_MODE)
		|| ((strCPT.inCommunicationMode)== USB_MODE) || ((strCPT.inSecCommunicationMode)== USB_MODE))
    {
		while(1)
	    {
	        vduiClearBelow(2);
	        setLCDPrint(2, DISPLAY_POSITION_LEFT, "Header Type");
	        if((strCPT.inIPHeader)== NO_HEADER_LEN)
	            setLCDPrint(3, DISPLAY_POSITION_LEFT, "0");
	        if((strCPT.inIPHeader)== HEX_EXCLUDE_LEN)
	            setLCDPrint(3, DISPLAY_POSITION_LEFT, "1");        
	        if((strCPT.inIPHeader)== BCD_EXCLUDE_LEN)
	            setLCDPrint(3, DISPLAY_POSITION_LEFT, "2");
			if((strCPT.inIPHeader)== HEX_INCLUDE_LEN)
	            setLCDPrint(3, DISPLAY_POSITION_LEFT, "3");
	        if((strCPT.inIPHeader)== BCD_INCLUDE_LEN)
	            setLCDPrint(3, DISPLAY_POSITION_LEFT, "4");        
	        
	        CTOS_LCDTPrintXY(1, 4, "0-NO LEN 1-HEX");
	        CTOS_LCDTPrintXY(1, 5, "2-BCD 3-HEX+2 LEN");
			CTOS_LCDTPrintXY(1, 6, "4-BCD+2 LEN");
	        
	        strcpy(strtemp,"New:") ;
	        CTOS_LCDTPrintXY(1, 7, strtemp);
	        memset(strOut,0x00, sizeof(strOut));
	        ret= shCTOS_GetNum(8, 0x01,  strOut, &usLen, 1, 1, 0, d_INPUT_TIMEOUT);
	        if (ret == d_KBD_CANCEL )
	            break;
	        else if(0 == ret )
	            break;
	        else if(ret==1)
	        {
	            if (strOut[0]==0x30 || strOut[0]==0x31 || strOut[0]==0x32 || strOut[0]==0x33 || strOut[0]==0x34)
	            {
	                 if(strOut[0] == 0x31)
	                 {
	                        strCPT.inIPHeader = HEX_EXCLUDE_LEN;
	                 }
	                 if(strOut[0] == 0x30)
	                 {
	                        strCPT.inIPHeader = NO_HEADER_LEN;
	                 }
	                 if(strOut[0] == 0x32)
	                 {
	                        strCPT.inIPHeader = BCD_EXCLUDE_LEN;
	                 }
					 if(strOut[0] == 0x33)
	                 {
	                        strCPT.inIPHeader = HEX_INCLUDE_LEN;
	                 }
	                 if(strOut[0] == 0x34)
	                 {
	                        strCPT.inIPHeader = BCD_INCLUDE_LEN;
	                 }
	                 break;
	             }
	             else
	             {
	                vduiWarningSound();
	                vduiClearBelow(6);
	                vduiDisplayStringCenter(6,"PLEASE SELECT");
	                vduiDisplayStringCenter(7,"A VALID");
	                vduiDisplayStringCenter(8,"CONNECTION MODE");
	                CTOS_Delay(2000);       
	            }
	        }
	    }
    	inResult = inCPTSave(shHostIndex);
	
    	if(((strCPT.inCommunicationMode)== COM1_MODE) || ((strCPT.inCommunicationMode)== COM2_MODE))
	    {
	    	inResult = inCPTRead(shHostIndex);
	        if(inResult != ST_SUCCESS)
	            return;
	        while(1)
		    {   
	            vduiClearBelow(2);
	            setLCDPrint(3, DISPLAY_POSITION_LEFT, "PRI Baud Rate");
	            memset(szInputBuf, 0x00, sizeof(szInputBuf));
	            sprintf(szInputBuf, "%d", strCPT.inPriTxnComBaudRate);
	            setLCDPrint(4, DISPLAY_POSITION_LEFT, szInputBuf);
	            
	            strcpy(strtemp,"New:") ;
	            CTOS_LCDTPrintXY(1, 7, strtemp);
	            memset(strOut,0x00, sizeof(strOut));
	            ret= shCTOS_GetNum(8, 0x01,  strOut, &usLen, 1, 6, 0, d_INPUT_TIMEOUT);
	            if (ret == d_KBD_CANCEL )
	                break;
	            else if(0 == ret )
	                break;
	            else if(ret>=1)
	            {
	                BolDetachLANChange=TRUE;
	                strCPT.inPriTxnComBaudRate = atoi(strOut);      
	                vdMyEZLib_LogPrintf("new host port %d",strCPT.inPriTxnComBaudRate);
	                break;
	            }   
	            if(ret == d_KBD_CANCEL)
	                break;
	        }
	        inResult = inCPTSave(shHostIndex);
	        if(inResult != ST_SUCCESS)
	            return;
	    }

		if(((strCPT.inSecCommunicationMode)== COM1_MODE) || ((strCPT.inSecCommunicationMode)== COM2_MODE))
	    {
	    	inResult = inCPTRead(shHostIndex);
	        if(inResult != ST_SUCCESS)
	            return;
	        while(1)
	        {   
	            vduiClearBelow(2);
	            setLCDPrint(3, DISPLAY_POSITION_LEFT, "SEC Baud Rate");
	            memset(szInputBuf, 0x00, sizeof(szInputBuf));
	            sprintf(szInputBuf, "%d", strCPT.inSecTxnComBaudRate);
	            setLCDPrint(4, DISPLAY_POSITION_LEFT, szInputBuf);
	            
	            strcpy(strtemp,"New:") ;
	            CTOS_LCDTPrintXY(1, 7, strtemp);
	            memset(strOut,0x00, sizeof(strOut));
	            ret= shCTOS_GetNum(8, 0x01,  strOut, &usLen, 1, 6, 0, d_INPUT_TIMEOUT);
	            if (ret == d_KBD_CANCEL )
	                break;
	            else if(0 == ret )
	                break;
	            else if(ret>=1)
	            {
	                BolDetachLANChange=TRUE;
	                strCPT.inSecTxnComBaudRate = atoi(strOut);      
	                vdMyEZLib_LogPrintf("new host port %d",strCPT.inSecTxnComBaudRate);
	                break;
	            }   
	            if(ret == d_KBD_CANCEL)
	                break;
	        }
	        inResult = inCPTSave(shHostIndex);
	        inResult = inCPTRead(shHostIndex);
	        if(inResult != ST_SUCCESS)
	            return;
	    }
    }
	
	vduiClearBelow(2);
    if(((strCPT.inCommunicationMode)== ETHERNET_MODE) || ((strCPT.inCommunicationMode)== WIFI_MODE) || ((strCPT.inCommunicationMode)== GPRS_MODE)|| ((strCPT.inCommunicationMode)== MDM_PPP_MODE)
		 || ((strCPT.inSecCommunicationMode)== ETHERNET_MODE) || ((strCPT.inSecCommunicationMode)== WIFI_MODE) ||  ((strCPT.inSecCommunicationMode)== GPRS_MODE) ||  ((strCPT.inSecCommunicationMode)== MDM_PPP_MODE))
    {
        inResult = inTCPRead(1);
        if(inResult != ST_SUCCESS)
            return;

        while(1)
        {
            vduiClearBelow(3);
            setLCDPrint(3, DISPLAY_POSITION_LEFT, "IP Config");
            if (strTCP.fDHCPEnable == IPCONFIG_DHCP)
                setLCDPrint(4, DISPLAY_POSITION_LEFT, "1");
            if (strTCP.fDHCPEnable == IPCONFIG_STATIC)  
                setLCDPrint(4, DISPLAY_POSITION_LEFT, "0");
            
            CTOS_LCDTPrintXY(1, 5, "0-STATIC     1-DHCP");
            CTOS_LCDTPrintXY(1, 6, "                   ");
            
            strcpy(strtemp,"New:") ;
            CTOS_LCDTPrintXY(1, 7, strtemp);
            memset(strOut,0x00, sizeof(strOut));
            ret= shCTOS_GetNum(8, 0x01,  strOut, &usLen, 1, 1, 0, d_INPUT_TIMEOUT);
            if (ret == d_KBD_CANCEL )
                break;
            else if(0 == ret )
                break;
            else if(ret==1)
            {
                if (strOut[0]==0x30 || strOut[0]==0x31)
                {
                    BolDetachLANChange=TRUE;
                    
                    if(strOut[0] == 0x30)  
                            strTCP.fDHCPEnable = IPCONFIG_STATIC;
                    if(strOut[0] == 0x31)
                            strTCP.fDHCPEnable = IPCONFIG_DHCP;
                    break;
                }
                else
                {
                    vduiWarningSound();
                    vduiClearBelow(6);
                    vduiDisplayStringCenter(6,"PLEASE SELECT");
                    vduiDisplayStringCenter(7,"A VALID");
                    vduiDisplayStringCenter(8,"IP Config");
                    CTOS_Delay(2000);       
                }
            }
        }               
        inResult = inTCPSave(1);
        inResult = inTCPRead(1);

		if ((strCPT.inCommunicationMode)== GPRS_MODE)
		{
	        inResult = inGCPTRead(shHostIndex);
	        if(inResult != ST_SUCCESS)
	            return;
	        while(1)
	        {
	            vduiClearBelow(3);
	            setLCDPrint(3, DISPLAY_POSITION_LEFT, "HOST PRI IP");
	            setLCDPrint(4, DISPLAY_POSITION_LEFT, strGCPT.szPriTxnHostIP);
	            
	            strcpy(strtemp,"New:") ;
	            CTOS_LCDTPrintXY(1, 7, strtemp);
	            memset(strtemp, 0x00, sizeof(strtemp));
	            memset(strOut,0x00, sizeof(strOut));
	            ret= struiApiGetStringSub(strtemp, 1,  8, strOut, MODE_IPADDRESS, 7, IP_LEN);
	            if(ret==d_KBD_ENTER)
	            {
	                BolDetachLANChange=TRUE;
	                memcpy(strGCPT.szPriTxnHostIP,strOut,strlen(strOut));
	                strGCPT.szPriTxnHostIP[strlen(strOut)]=0;
	                vdMyEZLib_LogPrintf("new gprs host ip %s",strGCPT.szPriTxnHostIP);
	                break;
	            }   
	            if(ret == d_KBD_CANCEL)
	                        break;
	            }
	        inResult = inGCPTSave(shHostIndex);
	        inResult = inGCPTRead(shHostIndex);
	        if(inResult != ST_SUCCESS)
	            return;
	        
	        while(1)
	        {   
	            vduiClearBelow(3);
	            setLCDPrint(3, DISPLAY_POSITION_LEFT, "HOST PRI PORT");
	            memset(szInputBuf, 0x00, sizeof(szInputBuf));
	            sprintf(szInputBuf, "%d", strGCPT.inPriTxnHostPortNum);
	            setLCDPrint(4, DISPLAY_POSITION_LEFT, szInputBuf);
	            
	            strcpy(strtemp,"New:") ;
	            CTOS_LCDTPrintXY(1, 7, strtemp);
	            memset(strOut,0x00, sizeof(strOut));
	            ret= shCTOS_GetNum(8, 0x01,  strOut, &usLen, 1, 5, 0, d_INPUT_TIMEOUT);
	            if (ret == d_KBD_CANCEL )
	                break;
	            else if(0 == ret )
	                break;
	            else if(ret>=1)
	            {
	                BolDetachLANChange=TRUE;
	                strGCPT.inPriTxnHostPortNum = atoi(strOut);      
	                vdMyEZLib_LogPrintf("new gprs host port %d",strGCPT.inPriTxnHostPortNum);
	                break;
	            }   
	            if(ret == d_KBD_CANCEL)
	                break;
	        }
	        inResult = inGCPTSave(shHostIndex);

	        inResult = inGCPTRead(shHostIndex);
	        if(inResult != ST_SUCCESS)
	            return;
	        while(1)
	        {
	            vduiClearBelow(3);
	            setLCDPrint(3, DISPLAY_POSITION_LEFT, "HOST SEC IP");
	            setLCDPrint(4, DISPLAY_POSITION_LEFT, strGCPT.szSecTxnHostIP);
	            
	            strcpy(strtemp,"New:") ;
	            CTOS_LCDTPrintXY(1, 7, strtemp);
	            memset(strtemp, 0x00, sizeof(strtemp));
	            memset(strOut,0x00, sizeof(strOut));
	            ret= struiApiGetStringSub(strtemp, 1,  8, strOut, MODE_IPADDRESS, 7, IP_LEN);
	            if(ret==d_KBD_ENTER)
	            {
	                BolDetachLANChange=TRUE;
	                memcpy(strGCPT.szSecTxnHostIP,strOut,strlen(strOut));
	                strGCPT.szSecTxnHostIP[strlen(strOut)]=0;
	                vdMyEZLib_LogPrintf("new gprs sec host ip %s",strGCPT.szSecTxnHostIP);
	                break;
	            }   
	            if(ret == d_KBD_CANCEL)
	                        break;
	            }
	        inResult = inGCPTSave(shHostIndex);
	        inResult = inGCPTRead(shHostIndex);
	        if(inResult != ST_SUCCESS)
	            return;
	        
	        while(1)
	        {   
	            vduiClearBelow(3);
	            setLCDPrint(3, DISPLAY_POSITION_LEFT, "HOST SEC PORT");
	            memset(szInputBuf, 0x00, sizeof(szInputBuf));
	            sprintf(szInputBuf, "%d", strGCPT.inSecTxnHostPortNum);
	            setLCDPrint(4, DISPLAY_POSITION_LEFT, szInputBuf);
	            
	            strcpy(strtemp,"New:") ;
	            CTOS_LCDTPrintXY(1, 7, strtemp);
	            memset(strOut,0x00, sizeof(strOut));
	            ret= shCTOS_GetNum(8, 0x01,  strOut, &usLen, 1, 5, 0, d_INPUT_TIMEOUT);
	            if (ret == d_KBD_CANCEL )
	                break;
	            else if(0 == ret )
	                break;
	            else if(ret>=1)
	            {
	                BolDetachLANChange=TRUE;
	                strGCPT.inSecTxnHostPortNum = atoi(strOut);      
	                vdMyEZLib_LogPrintf("new gprs sec host port %d",strGCPT.inSecTxnHostPortNum);
	                break;
	            }   
	            if(ret == d_KBD_CANCEL)
	                break;
	        }
	        inResult = inGCPTSave(shHostIndex);
			
	        inResult = inTCPRead(1);
	        inResult = inGCPTRead(shHostIndex);
	        if(inResult != ST_SUCCESS)
	            return;
	        while(1)
	        {
	            vduiClearBelow(3);
	            setLCDPrint(3, DISPLAY_POSITION_LEFT, "SETTLE HOST PRI IP");
	            setLCDPrint(4, DISPLAY_POSITION_LEFT, strGCPT.szPriSettlementHostIP);
	            
	            strcpy(strtemp,"New:") ;
	            CTOS_LCDTPrintXY(1, 7, strtemp);
	            memset(strtemp, 0x00, sizeof(strtemp));
	            memset(strOut,0x00, sizeof(strOut));
	            ret= struiApiGetStringSub(strtemp, 1,  8, strOut, MODE_IPADDRESS, 7, IP_LEN);
	            if(ret==d_KBD_ENTER)
	            {
	                BolDetachLANChange=TRUE;
	                memcpy(strGCPT.szPriSettlementHostIP,strOut,strlen(strOut));
	                strGCPT.szPriSettlementHostIP[strlen(strOut)]=0;
	                vdMyEZLib_LogPrintf("new gprs settle host ip %s",strGCPT.szPriSettlementHostIP);
	                break;
	            }   
	            if(ret == d_KBD_CANCEL)
	                        break;
	            }
	        inResult = inGCPTSave(shHostIndex);
	        inResult = inGCPTRead(shHostIndex);
	        if(inResult != ST_SUCCESS)
	            return;
	        
	        while(1)
	        {   
	            vduiClearBelow(3);
	            setLCDPrint(3, DISPLAY_POSITION_LEFT, "SETTLE HOST PRI PORT");
	            memset(szInputBuf, 0x00, sizeof(szInputBuf));
	            sprintf(szInputBuf, "%d", strGCPT.inPriSettlementHostPort);
	            setLCDPrint(4, DISPLAY_POSITION_LEFT, szInputBuf);
	            
	            strcpy(strtemp,"New:") ;
	            CTOS_LCDTPrintXY(1, 7, strtemp);
	            memset(strOut,0x00, sizeof(strOut));
	            ret= shCTOS_GetNum(8, 0x01,  strOut, &usLen, 1, 5, 0, d_INPUT_TIMEOUT);
	            if (ret == d_KBD_CANCEL )
	                break;
	            else if(0 == ret )
	                break;
	            else if(ret>=1)
	            {
	                BolDetachLANChange=TRUE;
	                strGCPT.inPriSettlementHostPort = atoi(strOut);      
	                vdMyEZLib_LogPrintf("new gprs settle host port %d",strGCPT.inPriSettlementHostPort);
	                break;
	            }   
	            if(ret == d_KBD_CANCEL)
	                break;
	        }
	        inResult = inGCPTSave(shHostIndex);

			inResult = inGCPTRead(shHostIndex);
	        if(inResult != ST_SUCCESS)
	            return;
	        while(1)
	        {
	            vduiClearBelow(3);
	            setLCDPrint(3, DISPLAY_POSITION_LEFT, "SETTLE HOST SEC IP");
	            setLCDPrint(4, DISPLAY_POSITION_LEFT, strGCPT.szSecSettlementHostIP);
	            
	            strcpy(strtemp,"New:") ;
	            CTOS_LCDTPrintXY(1, 7, strtemp);
	            memset(strtemp, 0x00, sizeof(strtemp));
	            memset(strOut,0x00, sizeof(strOut));
	            ret= struiApiGetStringSub(strtemp, 1,  8, strOut, MODE_IPADDRESS, 7, IP_LEN);
	            if(ret==d_KBD_ENTER)
	            {
	                BolDetachLANChange=TRUE;
	                memcpy(strGCPT.szSecSettlementHostIP,strOut,strlen(strOut));
	                strGCPT.szSecSettlementHostIP[strlen(strOut)]=0;
	                vdMyEZLib_LogPrintf("new host ip %s",strGCPT.szSecSettlementHostIP);
	                break;
	            }   
	            if(ret == d_KBD_CANCEL)
	                        break;
	            }
	        inResult = inGCPTSave(shHostIndex);
	        inResult = inGCPTRead(shHostIndex);
	        if(inResult != ST_SUCCESS)
	            return;
	        
	        while(1)
	        {   
	            vduiClearBelow(3);
	            setLCDPrint(3, DISPLAY_POSITION_LEFT, "SETTLE HOST SEC PORT");
	            memset(szInputBuf, 0x00, sizeof(szInputBuf));
	            sprintf(szInputBuf, "%d", strGCPT.inSecSettlementHostPort);
	            setLCDPrint(4, DISPLAY_POSITION_LEFT, szInputBuf);
	            
	            strcpy(strtemp,"New:") ;
	            CTOS_LCDTPrintXY(1, 7, strtemp);
	            memset(strOut,0x00, sizeof(strOut));
	            ret= shCTOS_GetNum(8, 0x01,  strOut, &usLen, 1, 5, 0, d_INPUT_TIMEOUT);
	            if (ret == d_KBD_CANCEL )
	                break;
	            else if(0 == ret )
	                break;
	            else if(ret>=1)
	            {
	                BolDetachLANChange=TRUE;
	                strGCPT.inSecSettlementHostPort = atoi(strOut);      
	                vdMyEZLib_LogPrintf("new host port %d",strGCPT.inSecSettlementHostPort);
	                break;
	            }   
	            if(ret == d_KBD_CANCEL)
	                break;
	        }
	        inResult = inGCPTSave(shHostIndex);
    	}
		else
		{
	        inResult = inCPTRead(shHostIndex);
	        if(inResult != ST_SUCCESS)
	            return;
	        while(1)
	        {
	            vduiClearBelow(3);
	            setLCDPrint(3, DISPLAY_POSITION_LEFT, "HOST PRI IP");
	            setLCDPrint(4, DISPLAY_POSITION_LEFT, strCPT.szPriTxnHostIP);
	            
	            strcpy(strtemp,"New:") ;
	            CTOS_LCDTPrintXY(1, 7, strtemp);
	            memset(strtemp, 0x00, sizeof(strtemp));
	            memset(strOut,0x00, sizeof(strOut));
	            ret= struiApiGetStringSub(strtemp, 1,  8, strOut, MODE_IPADDRESS, 7, IP_LEN);
	            if(ret==d_KBD_ENTER)
	            {
	                BolDetachLANChange=TRUE;
	                memcpy(strCPT.szPriTxnHostIP,strOut,strlen(strOut));
	                strCPT.szPriTxnHostIP[strlen(strOut)]=0;
	                vdMyEZLib_LogPrintf("new host ip %s",strCPT.szPriTxnHostIP);
	                break;
	            }   
	            if(ret == d_KBD_CANCEL)
	                        break;
	            }
	        inResult = inCPTSave(shHostIndex);
	        inResult = inCPTRead(shHostIndex);
	        if(inResult != ST_SUCCESS)
	            return;
	        
	        while(1)
	        {   
	            vduiClearBelow(3);
	            setLCDPrint(3, DISPLAY_POSITION_LEFT, "HOST PRI PORT");
	            memset(szInputBuf, 0x00, sizeof(szInputBuf));
	            sprintf(szInputBuf, "%d", strCPT.inPriTxnHostPortNum);
	            setLCDPrint(4, DISPLAY_POSITION_LEFT, szInputBuf);
	            
	            strcpy(strtemp,"New:") ;
	            CTOS_LCDTPrintXY(1, 7, strtemp);
	            memset(strOut,0x00, sizeof(strOut));
	            ret= shCTOS_GetNum(8, 0x01,  strOut, &usLen, 1, 5, 0, d_INPUT_TIMEOUT);
	            if (ret == d_KBD_CANCEL )
	                break;
	            else if(0 == ret )
	                break;
	            else if(ret>=1)
	            {
	                BolDetachLANChange=TRUE;
	                strCPT.inPriTxnHostPortNum = atoi(strOut);      
	                vdMyEZLib_LogPrintf("new host port %d",strCPT.inPriTxnHostPortNum);
	                break;
	            }   
	            if(ret == d_KBD_CANCEL)
	                break;
	        }
	        inResult = inCPTSave(shHostIndex);

	        inResult = inCPTRead(shHostIndex);
	        if(inResult != ST_SUCCESS)
	            return;
	        while(1)
	        {
	            vduiClearBelow(3);
	            setLCDPrint(3, DISPLAY_POSITION_LEFT, "HOST SEC IP");
	            setLCDPrint(4, DISPLAY_POSITION_LEFT, strCPT.szSecTxnHostIP);
	            
	            strcpy(strtemp,"New:") ;
	            CTOS_LCDTPrintXY(1, 7, strtemp);
	            memset(strtemp, 0x00, sizeof(strtemp));
	            memset(strOut,0x00, sizeof(strOut));
	            ret= struiApiGetStringSub(strtemp, 1,  8, strOut, MODE_IPADDRESS, 7, IP_LEN);
	            if(ret==d_KBD_ENTER)
	            {
	                BolDetachLANChange=TRUE;
	                memcpy(strCPT.szSecTxnHostIP,strOut,strlen(strOut));
	                strCPT.szSecTxnHostIP[strlen(strOut)]=0;
	                vdMyEZLib_LogPrintf("new host ip %s",strCPT.szSecTxnHostIP);
	                break;
	            }   
	            if(ret == d_KBD_CANCEL)
	                        break;
	            }
	        inResult = inCPTSave(shHostIndex);
	        inResult = inCPTRead(shHostIndex);
	        if(inResult != ST_SUCCESS)
	            return;
	        
	        while(1)
	        {   
	            vduiClearBelow(3);
	            setLCDPrint(3, DISPLAY_POSITION_LEFT, "HOST SEC PORT");
	            memset(szInputBuf, 0x00, sizeof(szInputBuf));
	            sprintf(szInputBuf, "%d", strCPT.inSecTxnHostPortNum);
	            setLCDPrint(4, DISPLAY_POSITION_LEFT, szInputBuf);
	            
	            strcpy(strtemp,"New:") ;
	            CTOS_LCDTPrintXY(1, 7, strtemp);
	            memset(strOut,0x00, sizeof(strOut));
	            ret= shCTOS_GetNum(8, 0x01,  strOut, &usLen, 1, 5, 0, d_INPUT_TIMEOUT);
	            if (ret == d_KBD_CANCEL )
	                break;
	            else if(0 == ret )
	                break;
	            else if(ret>=1)
	            {
	                BolDetachLANChange=TRUE;
	                strCPT.inSecTxnHostPortNum = atoi(strOut);      
	                vdMyEZLib_LogPrintf("new host port %d",strCPT.inSecTxnHostPortNum);
	                break;
	            }   
	            if(ret == d_KBD_CANCEL)
	                break;
	        }
	        inResult = inCPTSave(shHostIndex);
			
	        inResult = inTCPRead(1);
	        inResult = inCPTRead(shHostIndex);
	        if(inResult != ST_SUCCESS)
	            return;
	        while(1)
	        {
	            vduiClearBelow(3);
	            setLCDPrint(3, DISPLAY_POSITION_LEFT, "SETTLE HOST PRI IP");
	            setLCDPrint(4, DISPLAY_POSITION_LEFT, strCPT.szPriSettlementHostIP);
	            
	            strcpy(strtemp,"New:") ;
	            CTOS_LCDTPrintXY(1, 7, strtemp);
	            memset(strtemp, 0x00, sizeof(strtemp));
	            memset(strOut,0x00, sizeof(strOut));
	            ret= struiApiGetStringSub(strtemp, 1,  8, strOut, MODE_IPADDRESS, 7, IP_LEN);
	            if(ret==d_KBD_ENTER)
	            {
	                BolDetachLANChange=TRUE;
	                memcpy(strCPT.szPriSettlementHostIP,strOut,strlen(strOut));
	                strCPT.szPriSettlementHostIP[strlen(strOut)]=0;
	                vdMyEZLib_LogPrintf("new host ip %s",strCPT.szPriSettlementHostIP);
	                break;
	            }   
	            if(ret == d_KBD_CANCEL)
	                        break;
	            }
	        inResult = inCPTSave(shHostIndex);
	        inResult = inCPTRead(shHostIndex);
	        if(inResult != ST_SUCCESS)
	            return;
	        
	        while(1)
	        {   
	            vduiClearBelow(3);
	            setLCDPrint(3, DISPLAY_POSITION_LEFT, "SETTLE HOST PRI PORT");
	            memset(szInputBuf, 0x00, sizeof(szInputBuf));
	            sprintf(szInputBuf, "%d", strCPT.inPriSettlementHostPort);
	            setLCDPrint(4, DISPLAY_POSITION_LEFT, szInputBuf);
	            
	            strcpy(strtemp,"New:") ;
	            CTOS_LCDTPrintXY(1, 7, strtemp);
	            memset(strOut,0x00, sizeof(strOut));
	            ret= shCTOS_GetNum(8, 0x01,  strOut, &usLen, 1, 5, 0, d_INPUT_TIMEOUT);
	            if (ret == d_KBD_CANCEL )
	                break;
	            else if(0 == ret )
	                break;
	            else if(ret>=1)
	            {
	                BolDetachLANChange=TRUE;
	                strCPT.inPriSettlementHostPort = atoi(strOut);      
	                vdMyEZLib_LogPrintf("new host port %d",strCPT.inPriSettlementHostPort);
	                break;
	            }   
	            if(ret == d_KBD_CANCEL)
	                break;
	        }
	        inResult = inCPTSave(shHostIndex);

			inResult = inCPTRead(shHostIndex);
	        if(inResult != ST_SUCCESS)
	            return;
	        while(1)
	        {
	            vduiClearBelow(3);
	            setLCDPrint(3, DISPLAY_POSITION_LEFT, "SETTLE HOST SEC IP");
	            setLCDPrint(4, DISPLAY_POSITION_LEFT, strCPT.szSecSettlementHostIP);
	            
	            strcpy(strtemp,"New:") ;
	            CTOS_LCDTPrintXY(1, 7, strtemp);
	            memset(strtemp, 0x00, sizeof(strtemp));
	            memset(strOut,0x00, sizeof(strOut));
	            ret= struiApiGetStringSub(strtemp, 1,  8, strOut, MODE_IPADDRESS, 7, IP_LEN);
	            if(ret==d_KBD_ENTER)
	            {
	                BolDetachLANChange=TRUE;
	                memcpy(strCPT.szSecSettlementHostIP,strOut,strlen(strOut));
	                strCPT.szSecSettlementHostIP[strlen(strOut)]=0;
	                vdMyEZLib_LogPrintf("new host ip %s",strCPT.szSecSettlementHostIP);
	                break;
	            }   
	            if(ret == d_KBD_CANCEL)
	                        break;
	            }
	        inResult = inCPTSave(shHostIndex);
	        inResult = inCPTRead(shHostIndex);
	        if(inResult != ST_SUCCESS)
	            return;
	        
	        while(1)
	        {   
	            vduiClearBelow(3);
	            setLCDPrint(3, DISPLAY_POSITION_LEFT, "SETTLE HOST SEC PORT");
	            memset(szInputBuf, 0x00, sizeof(szInputBuf));
	            sprintf(szInputBuf, "%d", strCPT.inSecSettlementHostPort);
	            setLCDPrint(4, DISPLAY_POSITION_LEFT, szInputBuf);
	            
	            strcpy(strtemp,"New:") ;
	            CTOS_LCDTPrintXY(1, 7, strtemp);
	            memset(strOut,0x00, sizeof(strOut));
	            ret= shCTOS_GetNum(8, 0x01,  strOut, &usLen, 1, 5, 0, d_INPUT_TIMEOUT);
	            if (ret == d_KBD_CANCEL )
	                break;
	            else if(0 == ret )
	                break;
	            else if(ret>=1)
	            {
	                BolDetachLANChange=TRUE;
	                strCPT.inSecSettlementHostPort = atoi(strOut);      
	                vdMyEZLib_LogPrintf("new host port %d",strCPT.inSecSettlementHostPort);
	                break;
	            }   
	            if(ret == d_KBD_CANCEL)
	                break;
	        }
	        inResult = inCPTSave(shHostIndex);
    	}
 
        inResult = inCPTRead(shHostIndex);
		inResult = inTCPRead(1);
        if(strTCP.fDHCPEnable == IPCONFIG_STATIC)
        {
        	vdDebug_LogPrintf("old Client ip %s",strTCP.szTerminalIP);
            while(1)
            {
                vduiClearBelow(3);
                setLCDPrint(3, DISPLAY_POSITION_LEFT, "TERMINAL IP");
                setLCDPrint(4, DISPLAY_POSITION_LEFT, strTCP.szTerminalIP);
                
                strcpy(strtemp,"New:") ;     
                CTOS_LCDTPrintXY(1, 7, strtemp);
                memset(strtemp, 0x00, sizeof(strtemp));
                memset(strOut,0x00, sizeof(strOut));
                ret= struiApiGetStringSub(strtemp, 1,  8, strOut, MODE_IPADDRESS, 7, IP_LEN);
				vdDebug_LogPrintf("ret %d strOut[%s]", ret, strOut);
				if(ret==d_KBD_ENTER)
                {
                    BolDetachLANChange=TRUE;
                    memcpy(strTCP.szTerminalIP, strOut, strlen(strOut));
                    strTCP.szTerminalIP[strlen(strOut)]=0;
                    vdDebug_LogPrintf("new Client ip %s",strTCP.szTerminalIP);
                    break;
                }   
                if(ret == d_KBD_CANCEL)
                    break;
            }
            inResult = inTCPSave(1);
            inResult = inTCPRead(1);    
            
            while(1)
            {
                vduiClearBelow(3);
                setLCDPrint(3, DISPLAY_POSITION_LEFT, "Sub. Mask IP");
                setLCDPrint(4, DISPLAY_POSITION_LEFT, strTCP.szSubNetMask);
                vdDebug_LogPrintf("old sub mask[%s]", strTCP.szSubNetMask);
                strcpy(strtemp,"New:") ;   
                CTOS_LCDTPrintXY(1, 7, strtemp);
                memset(strtemp, 0x00, sizeof(strtemp));
                memset(strOut,0x00, sizeof(strOut));
                ret= struiApiGetStringSub(strtemp, 1,  8, strOut, MODE_IPADDRESS, 7, IP_LEN);
                if(ret==d_KBD_ENTER)
                {
                    BolDetachLANChange=TRUE;
                    memcpy(strTCP.szSubNetMask, strOut,strlen(strOut));
                    strTCP.szSubNetMask[strlen(strOut)]=0;
                    vdDebug_LogPrintf("new Sub. Mask ip %s", strTCP.szSubNetMask);
                    break;
                }   
                if(ret == d_KBD_CANCEL)
                    break;
            }               
                inResult = inTCPSave(1);
                inResult = inTCPRead(1);
                while(1)
                {
                    vduiClearBelow(3);
                    setLCDPrint(3, DISPLAY_POSITION_LEFT, "Gateway IP");
                    setLCDPrint(4, DISPLAY_POSITION_LEFT, strTCP.szGetWay);
                    
					vdDebug_LogPrintf("old gateway[%s]", strTCP.szGetWay);
                    strcpy(strtemp,"New:") ;  
                    CTOS_LCDTPrintXY(1, 7, strtemp);
                    memset(strtemp, 0x00, sizeof(strtemp));
                    memset(strOut,0x00, sizeof(strOut));
                    ret= struiApiGetStringSub(strtemp, 1,  8, strOut, MODE_IPADDRESS, 7, IP_LEN);
                    if(ret==d_KBD_ENTER)
                    {
                        BolDetachLANChange=TRUE;
                        memcpy(strTCP.szGetWay, strOut,strlen(strOut));
                        strTCP.szGetWay[strlen(strOut)]=0;
                        vdDebug_LogPrintf("new Gateway ip %s", strTCP.szGetWay);
                        break;
                    }   
                    if(ret == d_KBD_CANCEL)
                        break;
                }
                inResult = inTCPSave(1);
                inResult = inTCPRead(1);
                
                while(1)
                {
                    vduiClearBelow(3);
                    setLCDPrint(3, DISPLAY_POSITION_LEFT, "DNS1 IP");
                    if (wub_strlen(strTCP.szHostDNS1)<=0)
                        setLCDPrint(4, DISPLAY_POSITION_LEFT, "[NOT SET]");
                    else
                        setLCDPrint(4, DISPLAY_POSITION_LEFT, strTCP.szHostDNS1);
                    
                    strcpy(strtemp,"New:");     
                    CTOS_LCDTPrintXY(1, 7, strtemp);
                    memset(strtemp, 0x00, sizeof(strtemp));
                    memset(strOut,0x00, sizeof(strOut));
                    ret= struiApiGetStringSub(strtemp, 1,  8, strOut, MODE_IPADDRESS, 0, IP_LEN);
                    if(ret==d_KBD_ENTER)
                    {
                        if(strlen(strOut)>0)
                        {
                            BolDetachLANChange=TRUE;
                            memcpy(strTCP.szHostDNS1,strOut,strlen(strOut));
                            strTCP.szHostDNS1[strlen(strOut)]=0;
                            vdMyEZLib_LogPrintf("new DNS ip %s", strTCP.szHostDNS1);
                            break;
                        }
                        else
                        {
                            vduiClearBelow(3);
                            vduiDisplayStringCenter(4,"NO DNS IP");
                            vduiDisplayStringCenter(5,"ENTERED,DISABLE");
                            vduiDisplayStringCenter(6,"DNS IP?");
                            vduiDisplayStringCenter(8,"NO[X] YES[OK]");
                            key=struiGetchWithTimeOut();
                            if(key==d_KBD_ENTER)
                            {
                                memset(strTCP.szHostDNS1, 0, sizeof(strTCP.szHostDNS1));
                                break;
                            }
                        }
                    }                       
                    if(ret == d_KBD_CANCEL)
                        break;
                }
                inResult = inTCPSave(1);
                inResult = inTCPRead(1);

                while(1)
                {
                    vduiClearBelow(3);
                    setLCDPrint(3, DISPLAY_POSITION_LEFT, "DNS2 IP");
                    if (wub_strlen(strTCP.szHostDNS2)<=0)
                        setLCDPrint(4, DISPLAY_POSITION_LEFT, "[NOT SET]");
                    else
                        setLCDPrint(4, DISPLAY_POSITION_LEFT, strTCP.szHostDNS2);
                    
                    strcpy(strtemp,"New:");     
                    CTOS_LCDTPrintXY(1, 7, strtemp);
                    memset(strtemp, 0x00, sizeof(strtemp));
                    memset(strOut,0x00, sizeof(strOut));
                    ret= struiApiGetStringSub(strtemp, 1,  8, strOut, MODE_IPADDRESS, 0, IP_LEN);
                    if(ret==d_KBD_ENTER)
                    {
                        if(strlen(strOut)>0)
                        {
                            BolDetachLANChange=TRUE;
                            memcpy(strTCP.szHostDNS2,strOut,strlen(strOut));
                            strTCP.szHostDNS2[strlen(strOut)]=0;
                            vdMyEZLib_LogPrintf("new DNS ip %s", strTCP.szHostDNS2);
                            break;
                        }
                        else
                        {
                            vduiClearBelow(3);
                            vduiDisplayStringCenter(4,"NO DNS IP");
                            vduiDisplayStringCenter(5,"ENTERED,DISABLE");
                            vduiDisplayStringCenter(6,"DNS IP?");
                            vduiDisplayStringCenter(8,"NO[X] YES[OK]");
                            key=struiGetchWithTimeOut();
                            if(key==d_KBD_ENTER)
                            {
                                memset(strTCP.szHostDNS2, 0, sizeof(strTCP.szHostDNS2));
                                break;
                            }
                        }
                    }                       
                    if(ret == d_KBD_CANCEL)
                        break;
                }
                inResult = inTCPSave(1);
                inResult = inTCPRead(1);
            }

			if(strCPT.inCommunicationMode == WIFI_MODE)
			{
				inCTOSS_COMMWIFISCAN();
                if (inCTOS_InitComm(srTransRec.usTerminalCommunicationMode) != d_OK) 
                {
                   
                    vdDisplayErrorMsg(1, 8, "COMM INIT ERR");
                    return;
                }				
			}

			vdDebug_LogPrintf("BolDetachLANChange %d", BolDetachLANChange);
       
            if(BolDetachLANChange==TRUE)
            {
                srTransRec.usTerminalCommunicationMode = strCPT.inCommunicationMode;
                vduiClearBelow(3);
                CTOS_LCDTPrintXY (1,7, "Please Wait     ");       
                if(strCPT.inCommunicationMode == ETHERNET_MODE)
                    CTOS_LCDTPrintXY(1, 8, "Init LAN...     ");
                else if(strCPT.inCommunicationMode == GPRS_MODE)
                    CTOS_LCDTPrintXY(1, 8, "Init GPRS...     ");
                if (inCTOS_InitComm(srTransRec.usTerminalCommunicationMode) != d_OK) 
                {
                   
                    vdDisplayErrorMsg(1, 8, "COMM INIT ERR");
                    return;
                }

				vdDebug_LogPrintf("start init [%d]", strCPT.inCommunicationMode);
                
                srCommFuncPoint.inInitComm(&srTransRec,strCPT.inCommunicationMode);
                srCommFuncPoint.inGetCommConfig(&srTransRec);
                srCommFuncPoint.inSetCommConfig(&srTransRec);

				srCommFuncPoint.inDisconnect(&srTransRec);

            }          
    }
	
    if(strCPT.inCommunicationMode == ETHERNET_MODE)
    {
       if(strTCP.fDHCPEnable != IPCONFIG_STATIC)
       {
//           inEthernet_GetTerminalIP(strtemp);
//           if (strcmp(strtemp,"0.0.0.0") == 0)
           {
                srTransRec.usTerminalCommunicationMode = strCPT.inCommunicationMode;
                if (inCTOS_InitComm(srTransRec.usTerminalCommunicationMode) != d_OK) 
                {
                   
                    vdDisplayErrorMsg(1, 8, "COMM INIT ERR");
                    return;
                }
                srCommFuncPoint.inInitComm(&srTransRec,strCPT.inCommunicationMode);
                srCommFuncPoint.inGetCommConfig(&srTransRec);
                srCommFuncPoint.inSetCommConfig(&srTransRec); 
           }

			srCommFuncPoint.inDisconnect(&srTransRec);

			CTOS_EthernetOpen();
			CTOS_EthernetClose();
			CTOS_EthernetOpen();
			inEthernet_GetTerminalIP(strtemp);
			// tang jing fix incorrect IP address
			//CTOS_EthernetClose();
			
           vduiClearBelow(3);
           setLCDPrint(3, DISPLAY_POSITION_LEFT, "DHCP TERMINAL IP");
           setLCDPrint(4, DISPLAY_POSITION_LEFT, strtemp);
           WaitKey(30);
       }
    }

    if ((strCPT.inCommunicationMode)== MDM_PPP_MODE)
	{
		vdCTOS_MdmPPPConfig();
	}

    return ;
}

void vdCTOS_DialConfig(void)
{
    BYTE bRet,strOut[30],strtemp[17],key;
    BYTE szInputBuf[24+1];
    BYTE szIntComBuf[2];
    BYTE szPhNoBuf[9];
    BYTE szExtNoBuf[4];
    int inResult;
    USHORT ret;
    USHORT usLen;
    int shHostIndex = 1;
    
    shHostIndex = inCTOS_SelectHostSetting();
    if (shHostIndex == -1)
        return;
    
    inResult = inCPTRead(shHostIndex);  
    if (inResult != d_OK)
        return;
    
    inResult = inTCTRead(1);
    if (inResult != d_OK)
        return;
    
    CTOS_LCDTClearDisplay();
    vdDispTitleString("Dial Up Setting");
    
    while(1)
    {
        vduiClearBelow(3);
        setLCDPrint(3, DISPLAY_POSITION_LEFT, "Primary PABX");
        if (wub_strlen(strTCT.szPabx)<=0)
            setLCDPrint(4,DISPLAY_POSITION_LEFT, "[DISABLED]");
        else
            setLCDPrint(4, DISPLAY_POSITION_LEFT, strTCT.szPabx);

        
        strcpy(strtemp,"New:") ; 
        CTOS_LCDTPrintXY(1, 7, strtemp);
        memset(strOut,0x00, sizeof(strOut));
        memset(strtemp, 0x00, sizeof(strtemp));
        usLen = 10;
        ret = InputStringAlpha(1, 8, 0x00, 0x02, strOut, &usLen, 0, d_INPUT_TIMEOUT);
        if(ret==d_KBD_ENTER)
        {
                BolDetachDIALChangeSetting = TRUE;
                if(strlen(strOut)>0)
                {
                    memcpy(strTCT.szPabx,strOut,strlen(strOut));
                    strTCT.szPabx[strlen(strOut)]=',';
                    strTCT.szPabx[strlen(strOut)+1]=0;
                    break;
                }
                else
                {
                    vduiClearBelow(4);
                    vduiDisplayStringCenter(5,"NO PABX VALUE");
                    vduiDisplayStringCenter(6,"ENTERED,DISABLE");
                    vduiDisplayStringCenter(7,"PABX?");
                    vduiDisplayStringCenter(8,"NO[X] YES[OK]");
                    key=struiGetchWithTimeOut();
                    if(key==d_KBD_ENTER)
                    {
                        memset(strTCT.szPabx,0,sizeof(strTCT.szPabx));
                                                break;
                    }
                }
        }
            if(ret == d_KBD_CANCEL)
                break;
        }

        inTCTSave(1);
        
        while(1)
        {
            vduiClearBelow(3);
            setLCDPrint(3, DISPLAY_POSITION_LEFT, "Primary Line");
            setLCDPrint(4, DISPLAY_POSITION_LEFT, strCPT.szPriTxnPhoneNumber);

            strcpy(strtemp,"New:") ; 
            CTOS_LCDTPrintXY(1, 7, strtemp);
            memset(strOut,0x00, sizeof(strOut));
            usLen = 18;
            ret = InputStringAlpha(1, 8, 0x00, 0x02, strOut, &usLen, 1, d_INPUT_TIMEOUT);
            if (ret == d_KBD_CANCEL )
                break;
            else if(0 == ret )
                break;
            else if(ret>= 1)
            {
                BolDetachDIALChangeSetting = TRUE;
                memcpy(strCPT.szPriTxnPhoneNumber,strOut,strlen(strOut));
                strCPT.szPriTxnPhoneNumber[strlen(strOut)]=0;
                break;
            }
            if(ret == d_KBD_CANCEL)
                break;

        }
            
        while(1)
        {
            vduiClearBelow(3);
            setLCDPrint(3, DISPLAY_POSITION_LEFT, "Second Line");
            setLCDPrint(4, DISPLAY_POSITION_LEFT, strCPT.szSecTxnPhoneNumber);
            
            strcpy(strtemp,"New:") ; 
            CTOS_LCDTPrintXY(1, 7, strtemp);
            memset(strOut,0x00, sizeof(strOut));
            usLen = 18;
            ret = InputStringAlpha(1, 8, 0x00, 0x02, strOut, &usLen, 1, d_INPUT_TIMEOUT);
            if (ret == d_KBD_CANCEL )
                break;
            else if(0 == ret )
                break;
            else if(ret>= 1)
            {
                BolDetachDIALChangeSetting = TRUE;
                memcpy(strCPT.szSecTxnPhoneNumber,strOut,strlen(strOut));
                strCPT.szSecTxnPhoneNumber[strlen(strOut)]=0;
                break;
            }
            if(ret == d_KBD_CANCEL)
                break;

        }
            
        while(1)
        {
            vduiClearBelow(3);
            setLCDPrint(3, DISPLAY_POSITION_LEFT, "SETTLEMENT PRI LINE");
            setLCDPrint(4, DISPLAY_POSITION_LEFT, strCPT.szPriSettlePhoneNumber);
            
            strcpy(strtemp,"New:") ; 
            CTOS_LCDTPrintXY(1, 7, strtemp);
            memset(strOut,0x00, sizeof(strOut));
            usLen = 18;
            ret = InputStringAlpha(1, 8, 0x00, 0x02, strOut, &usLen, 1, d_INPUT_TIMEOUT);
            if (ret == d_KBD_CANCEL )
                break;
            else if(0 == ret )
                break;
            else if(ret>= 1)
            {
                BolDetachDIALChangeSetting = TRUE;
                memcpy(strCPT.szPriSettlePhoneNumber,strOut,strlen(strOut));
                strCPT.szPriSettlePhoneNumber[strlen(strOut)]=0;
                break;
            }
            if(ret == d_KBD_CANCEL)
                break;

        }
            
        while(1)
        {
            vduiClearBelow(3);
            setLCDPrint(3, DISPLAY_POSITION_LEFT, "SETTLEMENT SEC LINE");
            setLCDPrint(4, DISPLAY_POSITION_LEFT, strCPT.szSecSettlePhoneNumber);
            
            strcpy(strtemp,"New:") ; 
            CTOS_LCDTPrintXY(1, 7, strtemp);
            memset(strOut,0x00, sizeof(strOut));
            usLen = 18;
            ret = InputStringAlpha(1, 8, 0x00, 0x02, strOut, &usLen, 1, d_INPUT_TIMEOUT);
            if (ret == d_KBD_CANCEL )
                break;
            else if(0 == ret )
                break;
            else if(ret>= 1)
            {
                BolDetachDIALChangeSetting = TRUE;
                memcpy(strCPT.szSecSettlePhoneNumber,strOut,strlen(strOut));
                strCPT.szSecSettlePhoneNumber[strlen(strOut)]=0;
                break;
            }
            if(ret == d_KBD_CANCEL)
                break;

        }

    inResult = inCPTSave(shHostIndex);      
    inResult = inCPTRead(shHostIndex);      
            
    if(BolDetachDIALChangeSetting==TRUE)
    {
        srTransRec.usTerminalCommunicationMode = strCPT.inCommunicationMode;
        vduiClearBelow(3);
        CTOS_LCDTPrintXY (1,7, "Please Wait     ");       
        CTOS_LCDTPrintXY(1, 8, "Init Modem...     ");

        if (inCTOS_InitComm(srTransRec.usTerminalCommunicationMode) != d_OK) 
        {
           
            vdDisplayErrorMsg(1, 8, "COMM INIT ERR");
            return;
        }
        
        srCommFuncPoint.inInitComm(&srTransRec,strCPT.inCommunicationMode);
        srCommFuncPoint.inGetCommConfig(&srTransRec);
        srCommFuncPoint.inSetCommConfig(&srTransRec); 

    }
}

void vdCTOS_ModifyEdcSetting(void)
{
    BYTE bRet;
    BYTE szInputBuf[15+1];
    int inResult,inResult1;
    TRANS_TOTAL stBankTotal;
    BYTE strOut[30],strtemp[17],key;
    USHORT ret;
    USHORT usLen;
    BYTE szTempBuf[12+1];
    BOOL isKey;
    int shHostIndex = 1;
    int inNum = 0;
    
    shHostIndex = inCTOS_SelectHostSetting();
    if (shHostIndex == -1)
        return;

    strHDT.inHostIndex = shHostIndex;

    inResult = inCTOS_CheckAndSelectMutipleMID();
    if(d_OK != inResult)
        return;
    
    CTOS_LCDTClearDisplay();
    vdDispTitleString("EDC SETTING");
    while(1)
    {
        vduiClearBelow(3);
        setLCDPrint(3, DISPLAY_POSITION_LEFT, "TERMINAL ID");
        setLCDPrint(4, DISPLAY_POSITION_LEFT, strMMT[0].szTID);
    
        strcpy(strtemp,"New:");
        CTOS_LCDTPrintXY(1, 7, strtemp);
        memset(strOut,0x00, sizeof(strOut));
        ret= shCTOS_GetNum(8, 0x01,  strOut, &usLen, 1, 8, 0, d_INPUT_TIMEOUT);
        if (ret == d_KBD_CANCEL )
            break;
        else if(0 == ret )
            break;
        else if(ret>=1)
        {
            inNum = inBatchNumRecord();
            if (inNum == 0)
            {
                memset(strMMT[0].szTID, 0x00, sizeof(strMMT[0].szTID));
                memcpy(strMMT[0].szTID, strOut, TERMINAL_ID_BYTES);
                strMMT[0].szTID[TERMINAL_ID_BYTES]=0;
                inMMTSave(strMMT[0].MMTid);
                break;
            }
            else
            {
                vduiWarningSound();
                vduiClearBelow(5);
                vduiDisplayStringCenter(6,"BATCH NOT");
                vduiDisplayStringCenter(7,"EMPTY,SKIPPED.");
                CTOS_Delay(2000);
                break;
            }
       }
   if (ret == d_KBD_CANCEL )
        break ;
    }
    
    while(1)
    {
        vduiClearBelow(3);
        setLCDPrint(3, DISPLAY_POSITION_LEFT, "MERCHANT ID");
        setLCDPrint(4, DISPLAY_POSITION_LEFT, strMMT[0].szMID);
        
        strcpy(strtemp,"New:");
        CTOS_LCDTPrintXY(1, 7, strtemp);
        memset(strOut,0x00, sizeof(strOut));
        ret= shCTOS_GetNum(8, 0x01,  strOut, &usLen, 1, 15, 0, d_INPUT_TIMEOUT);
        if (ret == d_KBD_CANCEL )
            break;
        else if(0 == ret )
            break;
        else if(ret>=1)
        {
            inNum = inBatchNumRecord();
            if (inNum == 0)
            {
                memset(strMMT[0].szMID, 0x00, sizeof(strMMT[0].szMID));
                memcpy(strMMT[0].szMID, strOut, MERCHANT_ID_BYTES);
                strMMT[0].szMID[MERCHANT_ID_BYTES]=0;
                inMMTSave(strMMT[0].MMTid);
                break;
            }
            else
            {
                vduiWarningSound();
                vduiClearBelow(5);
                vduiDisplayStringCenter(6,"BATCH NOT");
                vduiDisplayStringCenter(7,"EMPTY,SKIPPED.");
                CTOS_Delay(2000);
                break;
            }
       }
       if (ret == d_KBD_CANCEL )
            break ;
    }

    while(1)
    {
        vduiClearBelow(3);
        setLCDPrint(3, DISPLAY_POSITION_LEFT, "TPDU");
        wub_hex_2_str(strHDT.szTPDU,szTempBuf,5);
        setLCDPrint(4, DISPLAY_POSITION_LEFT, szTempBuf);
        
        strcpy(strtemp,"New:");
        CTOS_LCDTPrintXY(1, 7, strtemp);
        memset(strOut,0x00, sizeof(strOut));
        ret= shCTOS_GetNum(8, 0x01,  strOut, &usLen, 10, 10, 0, d_INPUT_TIMEOUT);
        if (ret == d_KBD_CANCEL )
            break;
        else if(0 == ret )
            break;
        else if(ret==10)
        {
            inNum = inBatchNumRecord();
            if (inNum == 0)
            {
                memset(strHDT.szTPDU, 0x00, sizeof(strHDT.szTPDU)); 
                wub_str_2_hex(strOut,strtemp,TPDU_BYTES);
                memcpy(strHDT.szTPDU, strtemp, TPDU_BYTES/2);
                inHDTSave(strHDT.inHostIndex);
                break;
            }
            else
            {
                vduiWarningSound();
                vduiClearBelow(5);
                vduiDisplayStringCenter(6,"BATCH NOT");
                vduiDisplayStringCenter(7,"EMPTY,SKIPPED.");
                CTOS_Delay(2000);
                break;
            }
       }
       if (ret == d_KBD_CANCEL )
            break ;
    }
    
    while(1)
    {
        vduiClearBelow(3);
        setLCDPrint(3, DISPLAY_POSITION_LEFT, "NII");
        wub_hex_2_str(strHDT.szNII,szTempBuf,2);
        setLCDPrint(4, DISPLAY_POSITION_LEFT, szTempBuf);
        
        strcpy(strtemp,"New:");
        CTOS_LCDTPrintXY(1, 7, strtemp);
        memset(strOut,0x00, sizeof(strOut));
        ret= shCTOS_GetNum(8, 0x01,  strOut, &usLen, 4, 4, 0, d_INPUT_TIMEOUT);
        if (ret == d_KBD_CANCEL )
            break;
        else if(0 == ret )
            break;
        else if(ret==4)
        {
            inNum = inBatchNumRecord();
            if (inNum == 0)
            {
                memset(strHDT.szNII, 0x00, sizeof(strHDT.szNII));
                wub_str_2_hex(strOut, strtemp, NII_BYTES);
                memcpy(strHDT.szNII, strtemp, NII_BYTES/2);
                inHDTSave(strHDT.inHostIndex);
                break;
            }
            else
            {
                vduiWarningSound();
                vduiClearBelow(5);
                vduiDisplayStringCenter(6,"BATCH NOT");
                vduiDisplayStringCenter(7,"EMPTY,SKIPPED.");
                CTOS_Delay(2000);
                break;
            }
       }
       if (ret == d_KBD_CANCEL )
            break ;
        }

    inTCTRead(1);    
    while(1)
    {
        vduiClearBelow(3);
        setLCDPrint(3, DISPLAY_POSITION_LEFT, "System PWD");
        setLCDPrint(4, DISPLAY_POSITION_LEFT, strTCT.szSystemPW);
        
        strcpy(strtemp,"New:");
        CTOS_LCDTPrintXY(1, 7, strtemp);
        memset(strOut,0x00, sizeof(strOut));
        ret= shCTOS_GetNum(8, 0x01,  strOut, &usLen, 1, 6, 0, d_INPUT_TIMEOUT);
        if (ret == d_KBD_CANCEL )
            break;
        else if(0 == ret )
            break;
        else if(ret>=1)
        {
            if (strlen(strOut)>0)
            {
                memset(strTCT.szSystemPW,0x00,PASSWORD_LEN);
                memcpy(strTCT.szSystemPW,strOut, strlen(strOut));
                strTCT.szSystemPW[strlen(strOut)]=0;
                inTCTSave(1);
                break;
            }
            else
            {
                vduiClearBelow(3);
                vduiDisplayStringCenter(4,"NO PASSWORD");
                vduiDisplayStringCenter(5,"ENTERED,DISABLE");
                vduiDisplayStringCenter(6,"SYSTEM PWD?");
                vduiDisplayStringCenter(8,"NO[X] YES[OK]");
                key=WaitKey(60);
                if(key==d_KBD_ENTER)
                {
                    memset(strTCT.szSystemPW,0x00,PASSWORD_LEN);
                    inTCTSave(1);
                    break;
                }
            }
    }
    if (ret == d_KBD_CANCEL )
        break ;
    }
  
    inTCTRead(1);
    while(1)
    {    
        vduiClearBelow(3);
        setLCDPrint(3, DISPLAY_POSITION_LEFT, "Engineer PWD");
        setLCDPrint(4, DISPLAY_POSITION_LEFT, strTCT.szEngineerPW);
        
        strcpy(strtemp,"New:");
        CTOS_LCDTPrintXY(1, 7, strtemp);
        memset(strOut,0x00, sizeof(strOut));
        ret= shCTOS_GetNum(8, 0x01,  strOut, &usLen, 1, 6, 0, d_INPUT_TIMEOUT);
        if (ret == d_KBD_CANCEL )
            break;
        else if(0 == ret )
            break;
        else if(ret>=1)
        {
            if (strlen(strOut)>0)
            {
                memset(strTCT.szEngineerPW,0x00,PASSWORD_LEN);
                memcpy(strTCT.szEngineerPW,strOut, strlen(strOut));
                strTCT.szEngineerPW[strlen(strOut)]=0;
                inTCTSave(1);
                break;
            }
            else
            {
                vduiClearBelow(3);
                vduiDisplayStringCenter(4,"NO PASSWORD");
                vduiDisplayStringCenter(5,"ENTERED,DISABLE");
                vduiDisplayStringCenter(6,"ENGINEER PWD?");
                vduiDisplayStringCenter(8,"NO[X] YES[OK]");
                key=WaitKey(60);
                if(key==d_KBD_ENTER)
                {
                    memset(strTCT.szEngineerPW,0x00,PASSWORD_LEN);
                    inTCTSave(1);
                    break;
                }
            }
        }
        if (ret == d_KBD_CANCEL )
            break ;
        }

	inTCTRead(1);
    while(1)
	{    
        vduiClearBelow(3);
        setLCDPrint(3, DISPLAY_POSITION_LEFT, "PM password");
        setLCDPrint(4, DISPLAY_POSITION_LEFT, strTCT.szPMpassword);
        
        strcpy(strtemp,"New:");
        CTOS_LCDTPrintXY(1, 7, strtemp);
        memset(strOut,0x00, sizeof(strOut));
        ret= shCTOS_GetNum(8, 0x01,  strOut, &usLen, 1, 8, 0, d_INPUT_TIMEOUT);
        if (ret == d_KBD_CANCEL )
            break;
        else if(0 == ret )
            break;
        else if(ret>=1)
        {
            if (strlen(strOut)>0)
            {
                memset(strTCT.szPMpassword,0x00,8);
                memcpy(strTCT.szPMpassword,strOut, strlen(strOut));
                strTCT.szPMpassword[strlen(strOut)]=0;
                inTCTSave(1);
                break;
            }
            else
            {
                vduiClearBelow(3);
                vduiDisplayStringCenter(4,"NO PASSWORD");
                vduiDisplayStringCenter(5,"ENTERED,DISABLE");
                vduiDisplayStringCenter(6,"ENGINEER PWD?");
                vduiDisplayStringCenter(8,"NO[X] YES[OK]");
                key=WaitKey(60);
                if(key==d_KBD_ENTER)
                {
                    memset(strTCT.szPMpassword,0x00,8);
                    inTCTSave(1);
                    break;
                }
            }
        }
        if (ret == d_KBD_CANCEL )
            break ;
	}

	inTCTRead(1);
    while(1)
	{    
        vduiClearBelow(3);
        setLCDPrint(3, DISPLAY_POSITION_LEFT, "FunKey PWD");
        setLCDPrint(4, DISPLAY_POSITION_LEFT, strTCT.szFunKeyPW);
        
        strcpy(strtemp,"New:");
        CTOS_LCDTPrintXY(1, 7, strtemp);
        memset(strOut,0x00, sizeof(strOut));
        ret= shCTOS_GetNum(8, 0x01,  strOut, &usLen, 1, 8, 0, d_INPUT_TIMEOUT);
        if (ret == d_KBD_CANCEL )
            break;
        else if(0 == ret )
            break;
        else if(ret>=1)
        {
            if (strlen(strOut)>0)
            {
                memset(strTCT.szFunKeyPW,0x00,8);
                memcpy(strTCT.szFunKeyPW,strOut, strlen(strOut));
                strTCT.szFunKeyPW[strlen(strOut)]=0;
                inTCTSave(1);
                break;
            }
            else
            {
                vduiClearBelow(3);
                vduiDisplayStringCenter(4,"NO PASSWORD");
                vduiDisplayStringCenter(5,"ENTERED,DISABLE");
                vduiDisplayStringCenter(6,"ENGINEER PWD?");
                vduiDisplayStringCenter(8,"NO[X] YES[OK]");
                key=WaitKey(60);
                if(key==d_KBD_ENTER)
                {
                    memset(strTCT.szFunKeyPW,0x00,8);
                    inTCTSave(1);
                    break;
                }
            }
        }
        if (ret == d_KBD_CANCEL )
            break ;
	}
            
	return ;
}

void vdCTOS_DeleteBatch(void)
{
    int         shHostIndex = 1;
    int         inResult,inRet;
    ACCUM_REC srAccumRec;
    STRUCT_FILE_SETTING strFile;

    
    vduiLightOn();                

    shHostIndex = inCTOS_SelectHostSetting();
    if (shHostIndex == -1)
        return;

    if (inMultiAP_CheckMainAPStatus() == d_OK)
    {
        inRet = inCTOS_MultiAPSaveData(d_IPC_CMD_DELETE_BATCH);
        if(d_OK != inRet)
            return ;
    }
    else
    {
        if (0 == fGetAppRunBySelf() && inMultiAP_CheckSubAPStatus() == d_OK)
        {
            inRet = inCTOS_MultiAPGetData();
            if(d_OK != inRet)
                return ;

            inRet = inCTOS_MultiAPReloadHost();
            if(d_OK != inRet)
                return ;
        }
    }

    inResult = inCTOS_CheckAndSelectMutipleMID();
    if(d_OK != inResult)
        return;
            
    inResult = vduiAskConfirmContinue();
    if(inResult == d_OK)
    {

        if(CN_TRUE == strMMT[0].fMustSettFlag)
        {
            strMMT[0].fMustSettFlag = CN_FALSE;
            inMMTSave(strMMT[0].MMTid);
        }
    
        // delete batch where hostid and mmtid is matcj
        inDatabase_BatchDelete();

        memset(&srAccumRec, 0x00, sizeof(ACCUM_REC));
        memset(&strFile,0,sizeof(strFile));
        vdCTOS_GetAccumName(&strFile, &srAccumRec);
    
        if((inResult = CTOS_FileDelete(strFile.szFileName)) != d_OK)
        {
            vdDebug_LogPrintf("[inMyFile_SettleRecordDelete]---Delete Record error[%04x]", inResult);
        }
        //create the accum file
		memset(&srAccumRec, 0x00, sizeof(ACCUM_REC));
    	inCTOS_ReadAccumTotal(&srAccumRec);
		
        inCTOS_DeleteBKAccumTotal(&srAccumRec,strHDT.inHostIndex,srTransRec.MITid);

        inMyFile_ReversalDelete();

        inMyFile_AdviceDelete();
        
        inMyFile_TCUploadDelete();
    
        CTOS_LCDTClearDisplay();
        setLCDPrint(5, DISPLAY_POSITION_CENTER, "CLEAR BATCH");
        setLCDPrint(6, DISPLAY_POSITION_CENTER, "RECORD DONE");
        CTOS_Delay(1000); 
    }                
}

void vdCTOS_DeleteReversal(void)
{
    CHAR szFileName[d_BUFF_SIZE];
    int         shHostIndex = 1;
    int         inResult,inRet;
    ACCUM_REC srAccumRec;
    STRUCT_FILE_SETTING strFile;

    
    vduiLightOn();                

    shHostIndex = inCTOS_SelectHostSetting();
    if (shHostIndex == -1)
        return;

    if (inMultiAP_CheckMainAPStatus() == d_OK)
    {
        inRet = inCTOS_MultiAPSaveData(d_IPC_CMD_DELETE_REVERSAL);
        if(d_OK != inRet)
            return ;
    }
    else
    {
        if (0 == fGetAppRunBySelf() && inMultiAP_CheckSubAPStatus() == d_OK)
        {
            inRet = inCTOS_MultiAPGetData();
            if(d_OK != inRet)
                return ;

            inRet = inCTOS_MultiAPReloadHost();
            if(d_OK != inRet)
                return ;
        }
    }

    inResult = inCTOS_CheckAndSelectMutipleMID();
    if(d_OK != inResult)
        return;

    memset(szFileName,0,sizeof(szFileName));
    sprintf(szFileName, "%s%02d%02drev"
                    , strHDT.szHostLabel
                    , strHDT.inHostIndex
                    , srTransRec.MITid);

    vdDebug_LogPrintf("Rever Name %s",szFileName);
    
    if((inResult = inMyFile_CheckFileExist(szFileName)) < 0)
    {
        vdDebug_LogPrintf("inMyFile_CheckFileExist <0");
        vduiClearBelow(2);
        vdDisplayErrorMsg(1, 8, "NO REVERSAL EXIST");
        return ;
    }
            
    inResult = vduiAskConfirmContinue();
    if(inResult == d_OK)
    {
        inMyFile_ReversalDelete();
    
        CTOS_LCDTClearDisplay();
        setLCDPrint(5, DISPLAY_POSITION_CENTER, "CLEAR REVERSAL");
        setLCDPrint(6, DISPLAY_POSITION_CENTER, "RECORD DONE");
        CTOS_Delay(1000); 
    }                
}

void vdCTOS_PrintEMVTerminalConfig(void)
{
    CTOS_LCDTClearDisplay();
    vdPrintEMVTags();
    
    return;
}

void vdCTOSS_PrintTerminalConfig(void)
{
    CTOS_LCDTClearDisplay();
    vdPrintTerminalConfig();
    
    return;
}


void vdCTOS_ThemesSetting(void)
{
    BYTE bRet;
    BYTE szInputBuf[15+1];
    int inResult,inResult1;
    TRANS_TOTAL stBankTotal;
    BYTE strOut[30],strtemp[17],key;
    USHORT ret;
    USHORT usLen;
    BYTE szTempBuf[12+1];
    BOOL isKey;
    int shHostIndex = 1;
    int inNum = 0;
    int inRet = 0;
	// patrick test ******** start
	char szDatabaseName[100+1];
	strcpy(szDatabaseName, "DMENGTHAI.S3DB");

//    inRet = inTCTRead(1);  
//    vdDebug_LogPrintf(". inTCTRead(%d)",inRet);

    CTOS_LCDTClearDisplay();
    vdDispTitleString("SETTING");
    while(1)
    {
        clearLine(3);
        clearLine(4);
        clearLine(5);
        clearLine(6);
        clearLine(7);
        clearLine(8);
        setLCDPrint(3, DISPLAY_POSITION_LEFT, "THEMES SELECTION");
//        if(strTCT.byRS232DebugPort == 0)
//            setLCDPrint(4, DISPLAY_POSITION_LEFT, "0");
//        if(strTCT.byRS232DebugPort == 1)
//            setLCDPrint(4, DISPLAY_POSITION_LEFT, "1");        
//        if(strTCT.byRS232DebugPort == 2)
//            setLCDPrint(4, DISPLAY_POSITION_LEFT, "2");
//        if(strTCT.byRS232DebugPort == 8)
//            setLCDPrint(4, DISPLAY_POSITION_LEFT, "8");  
        
        CTOS_LCDTPrintXY(1, 5, "0-SPRING       1-SUMMER");
//		CTOS_LCDTPrintXY(1, 6, "2-AUTUMN       3-WINTER");

//        CTOS_LCDTPrintXY(1, 6, "2-COM2    8-USB");
        
//        strcpy(strtemp,"New:") ;
//        CTOS_LCDTPrintXY(1, 7, strtemp);
        memset(strOut,0x00, sizeof(strOut));
        ret= shCTOS_GetNum(8, 0x01,  strOut, &usLen, 1, 1, 0, d_INPUT_TIMEOUT);
        if (ret == d_KBD_CANCEL )
            break;
        else if(0 == ret )
            break;
        else if(ret==1)
        {
            if (strOut[0]==0x30 || strOut[0]==0x31 || strOut[0]==0x32 || strOut[0]==0x38)
            {
                 if(strOut[0] == 0x31)
                 {
					 // patrick test ******** start
                     CTOS_LCDTTFSelect("tahoma.ttf", 0);
					 CTOS_LCDTTFSwichDisplayMode(d_TTF_MODE_VIETNAM);

					 CTOS_PrinterTTFSelect("tahoma.ttf", 0);
					 inCTOSS_SetERMFontType("tahoma.ttf", 0);
					 CTOS_PrinterTTFSwichDisplayMode(d_TTF_MODE_VIETNAM);

					 inSetLanguageDatabase(szDatabaseName);
					 if ((strTCT.byTerminalType%2) == 0)
					 {
						 vdCTOSS_CombineMenuBMP("BG_SUMMER.BMP");
						 vdBackUpWhiteBMP("SUMMER.BMP","WHITE.BMP");
						 vdBackUpWhiteBMP("SUMMERTOUCH.BMP","menutouch.bmp");
						 vdBackUpWhiteBMP("SUMMERITEM.BMP","menuitem.bmp");
					 }
					 CTOS_LCDTClearDisplay();
					 inCTOSS_SetALLApFont("tahoma.ttf");
					 strTCT.inThemesType = 1;
					 inTCTSave(1);
					 break;
					 // patrick test ******** end
                 }
                 if(strOut[0] == 0x30)
                 {
					 // patrick test ******** start
					 CTOS_LCDTTFSelect(d_FONT_DEFAULT_TTF, 0);
					 CTOS_LCDTTFSwichDisplayMode(d_TTF_MODE_DEFAULT);					 

					 CTOS_PrinterTTFSelect(d_FONT_DEFAULT_TTF, 0);
					 inCTOSS_SetERMFontType(d_FONT_DEFAULT_TTF, 0);
					 CTOS_PrinterTTFSwichDisplayMode(d_TTF_MODE_DEFAULT);

					 inSetLanguageDatabase("");
					 if ((strTCT.byTerminalType%2) == 0)
					 {
						 vdCTOSS_CombineMenuBMP("BG_SPRING.BMP");
						 vdBackUpWhiteBMP("SPRING.BMP","WHITE.BMP");
						 vdBackUpWhiteBMP("SPRINGTOUCH.BMP","menutouch.bmp");
						 vdBackUpWhiteBMP("SPRINGITEM.BMP","menuitem.bmp");
					 }
					 CTOS_LCDTClearDisplay();
					 inCTOSS_SetALLApFont(d_FONT_DEFAULT_TTF);
					 strTCT.inThemesType = 0;
					 inTCTSave(1);
					 break;
					 // patrick test ******** end
                 }
				 /*
	                 if(strOut[0] == 0x32)
	                 {
	                        strTCT.byRS232DebugPort = 2;
	                 }
	                 if(strOut[0] == 0x38)
	                 {
	                        strTCT.byRS232DebugPort = 8;
	                 }
	                
	                 inRet = inTCTSave(1);
	                 
	                 vdDebug_LogPrintf(". inTCTSave(%d)",inRet);
				*/
                 break;
             }
             else
             {
                vduiWarningSound();
                vduiDisplayStringCenter(6,"PLEASE SELECT");
                vduiDisplayStringCenter(7,"A VALID");
                vduiDisplayStringCenter(8,"THEMES");
                CTOS_Delay(2000);       
            }
        }
        if (ret == d_KBD_CANCEL )
            break ;
    }
       
    return ;
}

void vdCTOS_Debugmode(void)
{
    BYTE bRet;
    BYTE szInputBuf[15+1];
    int inResult,inResult1;
    TRANS_TOTAL stBankTotal;
    BYTE strOut[30],strtemp[17],key;
    USHORT ret;
    USHORT usLen;
    BYTE szTempBuf[12+1];
    BOOL isKey;
    int shHostIndex = 1;
    int inNum = 0;
    int inRet = 0;

    inRet = inTCTRead(1);  
    vdDebug_LogPrintf(". inTCTRead(%d)",inRet);

    CTOS_LCDTClearDisplay();
    vdDispTitleString("SETTING");
    while(1)
    {
        clearLine(3);
        clearLine(4);
        clearLine(5);
        clearLine(6);
        clearLine(7);
        clearLine(8);
        setLCDPrint(3, DISPLAY_POSITION_LEFT, "DEBUG MODE");
        if(strTCT.byRS232DebugPort == 0)
            setLCDPrint(4, DISPLAY_POSITION_LEFT, "0");
        if(strTCT.byRS232DebugPort == 1)
            setLCDPrint(4, DISPLAY_POSITION_LEFT, "1");        
        if(strTCT.byRS232DebugPort == 2)
            setLCDPrint(4, DISPLAY_POSITION_LEFT, "2");      
        if(strTCT.byRS232DebugPort == 3)
            setLCDPrint(4, DISPLAY_POSITION_LEFT, "3");
        if(strTCT.byRS232DebugPort == 8)
            setLCDPrint(4, DISPLAY_POSITION_LEFT, "8");  
        
        CTOS_LCDTPrintXY(1, 5, "0-NO        1-COM1");
        CTOS_LCDTPrintXY(1, 6, "2-COM2 3-COM3 8-USB");
        
        strcpy(strtemp,"New:") ;
        CTOS_LCDTPrintXY(1, 7, strtemp);
        memset(strOut,0x00, sizeof(strOut));
        ret= shCTOS_GetNum(8, 0x01,  strOut, &usLen, 1, 1, 0, d_INPUT_TIMEOUT);
		vdDebug_LogPrintf("shCTOS_GetNum(%d),usLen=[%d],strOut=[%s]",ret,usLen,strOut);
		if (ret == d_KBD_CANCEL )
            break;
        else if(0 == ret )
            break;
        else if(ret==1)
        {
            if (strOut[0]==0x30 || strOut[0]==0x31 || strOut[0]==0x32 || strOut[0]==0x33 || strOut[0]==0x38)
            {
                 if(strOut[0] == 0x31)
                 {
                        strTCT.byRS232DebugPort = 1;
                 }
                 if(strOut[0] == 0x30)
                 {
                        strTCT.byRS232DebugPort = 0;
                 }
                 if(strOut[0] == 0x32)
                 {
                        strTCT.byRS232DebugPort = 2;
                 }
                 if(strOut[0] == 0x33)
                 {
                        strTCT.byRS232DebugPort = 3;
                 }
                 if(strOut[0] == 0x38)
                 {
                        strTCT.byRS232DebugPort = 8;
                 }
                
                 inRet = inTCTSave(1);
                 
                 vdDebug_LogPrintf(". inTCTSave(%d)",inRet);
                 break;
             }
             else
             {
                vduiWarningSound();
                vduiDisplayStringCenter(6,"PLEASE SELECT");
                vduiDisplayStringCenter(7,"A VALID");
                vduiDisplayStringCenter(8,"DEBUG MODE");
                CTOS_Delay(2000);       
            }
        }
        if (ret == d_KBD_CANCEL )
            break ;
    }
       
    return ;
}

void vdCTOSS_InjectMKKey(void)
{
	BYTE bInBuf[250];
	BYTE bOutBuf[250];
	BYTE *ptr = NULL;
	USHORT usInLen = 0;
	USHORT usOutLen = 0;
	USHORT usResult;
	 USHORT usKeySet;
	USHORT usKeyIndex;

	BYTE bHeaderAttr = 0x01+0x04, iCol = 1;
  BYTE	x = 1;
  BYTE key;
	char szHeaderString[50] = "SELECT TO LOAD KEY";
	char szInjectKeyMenu[1024] = {0};


	CTOS_LCDTClearDisplay();
	CTOS_LCDTPrintXY(1, 1, "INJECT MK");
	put_env_int("KEYSET",0);
	put_env_int("KEYINDEX",0);

	/*********************************************/
	//sidumili: [Select Pinpad Type for Injection]

	memset(szInjectKeyMenu, 0x00, sizeof(szInjectKeyMenu));
	strcpy((char*)szInjectKeyMenu, "IPP \nPCi100 \nIPP/PCi100 \nHKLM DUKPT \nCANCEL");
	key = MenuDisplay(szHeaderString, strlen(szHeaderString), bHeaderAttr, iCol, x, szInjectKeyMenu, TRUE);

	if (key == 0xFF) 
	{	
		CTOS_LCDTClearDisplay();
		setLCDPrint(1, DISPLAY_POSITION_CENTER, "WRONG INPUT!!!");
		vduiWarningSound();
		return; 
	}

	if (key > 0){
		
		if(d_KBD_CANCEL == key)
		return;

		memset(bOutBuf, 0x00, sizeof(bOutBuf));
		memset(bInBuf, 0x00, sizeof(bInBuf));

		// --->> IPP
		if (key == 1){
	
			// --- Inject to IPP
			CTOS_LCDTClearDisplay();
			CTOS_LCDTPrintXY(1, 1, "INJECT MK");
		
			usResult = inMultiAP_RunIPCCmdTypes("com.Source.SHARLS_EMV.SHARLS_EMV", d_IPC_CMD_EMV_INJECTMK, bInBuf, usInLen, bOutBuf, &usOutLen);

			// -- sidumili: debugging of key injected
			//vdPrintPCIDebug_HexPrintf(TRUE, "INJECT IN", bInBuf, usInLen);
			//vdPrintPCIDebug_HexPrintf(TRUE, "INJECT OUT", bOutBuf, usOutLen);
			
			if (usResult != d_OK)		
				vdDisplayErrorMsg(1, 8, "Save MK error");
			else
				vdDisplayErrorMsg(1, 8, "Save MK OK");
	
			return ;
		}
			
		// --->> PCi100 / BOTH
		if ((key == 2) || (key == 3)){
			
			// --- Inject to IPP
			CTOS_LCDTClearDisplay();
			CTOS_LCDTPrintXY(1, 1, "INJECT MK");
		
			usResult = inMultiAP_RunIPCCmdTypes("com.Source.SHARLS_EMV.SHARLS_EMV", d_IPC_CMD_EMV_INJECTMK, bInBuf, usInLen, bOutBuf, &usOutLen);

			// -- sidumili: debugging of key injected
			//vdPrintPCIDebug_HexPrintf(TRUE, "INJECT IN", bInBuf, usInLen);
			//vdPrintPCIDebug_HexPrintf(TRUE, "INJECT OUT", bOutBuf, usOutLen);
			
			if (usResult != d_OK)
			return ;

			// --- Inject to PCi100
			CTOS_LCDTClearDisplay();
			CTOS_LCDTPrintXY(1, 1, "INJECT MK");
 			usKeySet = get_env_int("KEYSET");
			usKeyIndex = get_env_int("KEYINDEX");
			vdDebug_LogPrintf("usKeySet=[%x]",usKeySet);
			vdDebug_LogPrintf("usKeyIndex=[%x]",usKeyIndex);
			
			if (usOutLen == 8)
			usResult = inPCI100_SaveMKProcess(MK_DES_KEY,usOutLen,bOutBuf);
			if (usOutLen == 16)
			usResult = inPCI100_SaveMKProcess(MK_3DES_DOUBLE_KEY,usOutLen,bOutBuf);
			if (usOutLen == 24)
			usResult = inPCI100_SaveMKProcess(MK_3DES_TRIPLE_KEY,usOutLen,bOutBuf);

			if (usResult != d_OK)
			return ;

		}
		if (key == 4){
			inCTOSS_HKLMInjectDUKPTKey(NPX_DUKPT_KEYSET,NPX_DUKPT_KEYINDEX);//(0xC000,0x0004);
			return;
		}

		// --->> CANCEL
		if (key == 5){
			vdDisplayErrorMsg(1, 8, "Inject cancelled");
			return;
		}
	}
	/*********************************************/

	
#if 0
	memset(bOutBuf, 0x00, sizeof(bOutBuf));
	memset(bInBuf, 0x00, sizeof(bInBuf));
	
	vdDebug_LogPrintf("d_IPC_CMD_EMV_INJECTMK usInLen[%d] ",usInLen);
	
	usResult = inMultiAP_RunIPCCmdTypes("com.Source.SHARLS_EMV.SHARLS_EMV", d_IPC_CMD_EMV_INJECTMK, bInBuf, usInLen, bOutBuf, &usOutLen);

	if (usResult != d_OK)
		return ;

	CTOS_LCDTPrintXY(1, 1, "INJECT MK");
	vdDebug_LogPrintf("d_IPC_CMD_EMV_INJECTMK usOutLen[%d] ",usOutLen);
	DebugAddHEX("MK",bOutBuf,usOutLen);
	
	inTCTRead(1);
	vdDebug_LogPrintf("InjectMKKey..byPinPadType=[%d],byPinPadPort=[%d]",strTCT.byPinPadType,strTCT.byPinPadPort);
	if (1 == strTCT.byPinPadType)
	{
		if (usOutLen == 8)
			inPCI100_SaveMKProcess(MK_DES_KEY,usOutLen,bOutBuf);
		if (usOutLen == 16)
			inPCI100_SaveMKProcess(MK_3DES_DOUBLE_KEY,usOutLen,bOutBuf);
		if (usOutLen == 24)
			inPCI100_SaveMKProcess(MK_3DES_TRIPLE_KEY,usOutLen,bOutBuf);
	}
#endif

	return;
}


void vdCTOSS_SelectPinpadType(void)
{
    BYTE bRet;
    BYTE szInputBuf[15+1];
    int inResult,inResult1;
    TRANS_TOTAL stBankTotal;
    BYTE strOut[30],strtemp[17],key;
    USHORT ret;
    USHORT usLen;
    BYTE szTempBuf[12+1];
    BOOL isKey;
    int shHostIndex = 1;
    int inNum = 0;
    int inRet = 0;

    inRet = inTCTRead(1);  
    vdDebug_LogPrintf(". inTCTRead(%d)",inRet);

    CTOS_LCDTClearDisplay();
    vdDispTitleString("SETTING");
    while(1)
    {
        clearLine(3);
        clearLine(4);
        clearLine(5);
        clearLine(6);
        clearLine(7);
        clearLine(8);
        setLCDPrint(3, DISPLAY_POSITION_LEFT, "PINPAD TYPE");
        if(strTCT.byPinPadType == 0)
            setLCDPrint(4, DISPLAY_POSITION_LEFT, "0");
        if(strTCT.byPinPadType == 1)
            setLCDPrint(4, DISPLAY_POSITION_LEFT, "1");        
        if(strTCT.byPinPadType == 2)
            setLCDPrint(4, DISPLAY_POSITION_LEFT, "2");
		if(strTCT.byPinPadType == 3)
            setLCDPrint(4, DISPLAY_POSITION_LEFT, "3");
  
        
        CTOS_LCDTPrintXY(1, 5, "0-None	1-PCI100");
        CTOS_LCDTPrintXY(1, 6, "2-OTHER 3-V3P");
        
        strcpy(strtemp,"New:") ;
        CTOS_LCDTPrintXY(1, 7, strtemp);
        memset(strOut,0x00, sizeof(strOut));
        ret= shCTOS_GetNum(8, 0x01,  strOut, &usLen, 1, 1, 0, d_INPUT_TIMEOUT);
        if (ret == d_KBD_CANCEL )
            break;
        else if(0 == ret )
            break;
        else if(ret==1)
        {
            if (strOut[0]==0x30 || strOut[0]==0x31 || strOut[0]==0x32 || strOut[0]==0x33)
            {
                 if(strOut[0] == 0x31)
                 {
                        strTCT.byPinPadType = 1;
                 }
                 if(strOut[0] == 0x30)
                 {
                        strTCT.byPinPadType = 0;
                 }
                 if(strOut[0] == 0x32)
                 {
                        strTCT.byPinPadType = 2;
                 }
				 if(strOut[0] == 0x33)
                 {
                        strTCT.byPinPadType = 3;
                 }
 
                
                 inRet = inTCTSave(1);
                 
                 vdDebug_LogPrintf(". inTCTSave(%d)",inRet);
                 break;
             }
             else
             {
                vduiWarningSound();
                vduiDisplayStringCenter(6,"PLEASE SELECT");
                vduiDisplayStringCenter(7,"A VALID");
                vduiDisplayStringCenter(8,"PINPAD TYPE");
                CTOS_Delay(2000);       
            }
        }
        if (ret == d_KBD_CANCEL )
            break ;
    }
       
    return ;
}


int inCTOSS_GetCtlsMode(void)
{
	//inTCTRead(1); // this TCT should read when terminal startup, not needed here
	return strTCT.byCtlsMode;
}

void vdCTOSS_CtlsMode(void)
{
    BYTE bRet;
    BYTE szInputBuf[15+1];
    int inResult,inResult1;
    TRANS_TOTAL stBankTotal;
    BYTE strOut[30],strtemp[17],key;
    USHORT ret;
    USHORT usLen;
    BYTE szTempBuf[12+1];
    BOOL isKey;
    int shHostIndex = 1;
    int inNum = 0;
    int inRet = 0;

    inRet = inTCTRead(1);  
    vdDebug_LogPrintf(". inTCTRead(%d)",inRet);

    CTOS_LCDTClearDisplay();
    vdDispTitleString("SETTING");
    while(1)
    {
        clearLine(3);
        clearLine(4);
        clearLine(5);
        clearLine(6);
        clearLine(7);
        clearLine(8);
        setLCDPrint(3, DISPLAY_POSITION_LEFT, "CTLS MODE");
        if(strTCT.byCtlsMode == 0)
            setLCDPrint(4, DISPLAY_POSITION_LEFT, "0");
        if(strTCT.byCtlsMode == 1)
            setLCDPrint(4, DISPLAY_POSITION_LEFT, "1");        
        if(strTCT.byCtlsMode == 2)
            setLCDPrint(4, DISPLAY_POSITION_LEFT, "2");
		if(strTCT.byCtlsMode == 3)
            setLCDPrint(4, DISPLAY_POSITION_LEFT, "3");
		if(strTCT.byCtlsMode == 4)
            setLCDPrint(4, DISPLAY_POSITION_LEFT, "4");
 
        
        CTOS_LCDTPrintXY(1, 5, "0-Disable 1-Internal");
        CTOS_LCDTPrintXY(1, 6, "2-External 3-V3 CTLS");
		CTOS_LCDTPrintXY(1, 7, "4-V3 INT CTLS");
        
        strcpy(strtemp,"New:") ;
        CTOS_LCDTPrintXY(1, 8, strtemp);
        memset(strOut,0x00, sizeof(strOut));
        ret= shCTOS_GetNum(8, 0x01,  strOut, &usLen, 1, 1, 0, d_INPUT_TIMEOUT);
        if (ret == d_KBD_CANCEL )
            break;
        else if(0 == ret )
            break;
        else if(ret==1)
        {
            if (strOut[0]==0x30 || strOut[0]==0x31 || strOut[0]==0x32 || strOut[0]==0x33 || strOut[0]==0x34)
            {
                 if(strOut[0] == 0x31)
                 {
                        strTCT.byCtlsMode = 1;
                 }
                 if(strOut[0] == 0x30)
                 {
                        strTCT.byCtlsMode = 0;
                 }
                 if(strOut[0] == 0x32)
                 {
                        strTCT.byCtlsMode = 2;
                 }
				 if(strOut[0] == 0x33)
                 {
                        strTCT.byCtlsMode = 3;
                 }
				 if(strOut[0] == 0x34)
                 {
                        strTCT.byCtlsMode = 4;
                 }
                
                 inRet = inTCTSave(1);
                 
                 vdDebug_LogPrintf(". inTCTSave(%d)",inRet);
                 break;
             }
             else
             {
                vduiWarningSound();
                vduiDisplayStringCenter(6,"PLEASE SELECT");
                vduiDisplayStringCenter(7,"A VALID");
                vduiDisplayStringCenter(8,"DEBUG MODE");
                CTOS_Delay(2000);       
            }
        }
        if (ret == d_KBD_CANCEL )
            break ;
    }
       
    return ;
}


void vdCTOS_DemoMode(void)
{
    BYTE bRet;
    BYTE szInputBuf[15+1];
    int inResult,inResult1;
    TRANS_TOTAL stBankTotal;
    BYTE strOut[30],strtemp[17],key;
    USHORT ret;
    USHORT usLen;
    BYTE szTempBuf[12+1];
    BOOL isKey;
    int shHostIndex = 1;
    int inNum = 0;
    int inNumOfHost = 0;
    int inNumOfMerchant = 0;
    int inLoop =0 ;
    //int inResult = 0;
    ACCUM_REC srAccumRec;
    STRUCT_FILE_SETTING strFile;

    inTCTRead(1);   
    
    CTOS_LCDTClearDisplay();
    vdDispTitleString("SETTING");
    while(1)
    {
        clearLine(3);
        clearLine(4);
        clearLine(5);
        clearLine(6);
        clearLine(7);
        clearLine(8);
        setLCDPrint(3, DISPLAY_POSITION_LEFT, "DEMO MODE");
        if(strTCT.fDemo == 0)
            setLCDPrint(4, DISPLAY_POSITION_LEFT, "0");
        if(strTCT.fDemo == 1)
            setLCDPrint(4, DISPLAY_POSITION_LEFT, "1");     
        
        CTOS_LCDTPrintXY(1, 5, "0-DISABLE      1-ENABLE");
        
   
        strcpy(strtemp,"New:") ;
        CTOS_LCDTPrintXY(1, 7, strtemp);
        memset(strOut,0x00, sizeof(strOut));
        ret= shCTOS_GetNum(8, 0x01,  strOut, &usLen, 1, 1, 0, d_INPUT_TIMEOUT);
        if (ret == d_KBD_CANCEL )
            break;
        else if(0 == ret )
            break;
        else if(ret==1)
        {
            //check host num
            inNumOfHost = inHDTNumRecord();
            
            vdDebug_LogPrintf("[inNumOfHost]-[%d]", inNumOfHost);
            for(inNum =1 ;inNum <= inNumOfHost; inNum++)
            {
                if(inHDTRead(inNum) == d_OK)
                {
                    inMMTReadNumofRecords(strHDT.inHostIndex,&inNumOfMerchant);
                
                    vdDebug_LogPrintf("[inNumOfMerchant]-[%d]strHDT.inHostIndex[%d]", inNumOfMerchant,strHDT.inHostIndex);
                    for(inLoop=1; inLoop <= inNumOfMerchant;inLoop++)
                    {
                        if((inResult = inMMTReadRecord(strHDT.inHostIndex,strMMT[inLoop-1].MITid)) !=d_OK)
                        {
                            vdDebug_LogPrintf("[read MMT fail]-Mitid[%d]strHDT.inHostIndex[%d]inResult[%d]", strMMT[inLoop-1].MITid,strHDT.inHostIndex,inResult);
                            continue;
                            //break;
                        }
                        else    // delete batch where hostid and mmtid is match  
                        {
                            strMMT[0].HDTid = strHDT.inHostIndex;
                            strMMT[0].MITid = strMMT[inLoop-1].MITid;
                            inDatabase_BatchDeleteHDTidMITid();
                            vdDebug_LogPrintf("[inDatabase_BatchDelete]-Mitid[%d]strHDT.inHostIndex[%d]", strMMT[inLoop-1].MITid,strHDT.inHostIndex);
                            
                            memset(&srAccumRec, 0x00, sizeof(ACCUM_REC));
                            memset(&strFile,0,sizeof(strFile));
                            srTransRec.MITid = strMMT[inLoop-1].MITid;
                            vdCTOS_GetAccumName(&strFile, &srAccumRec);
                            
                            if((inResult = CTOS_FileDelete(strFile.szFileName)) != d_OK)
                            {
                                vdDebug_LogPrintf("[inMyFile_SettleRecordDelete]---Delete Record error[%04x]", inResult);
                            }

                            inCTOS_DeleteBKAccumTotal(&srAccumRec,strHDT.inHostIndex,srTransRec.MITid);

                            inMyFile_ReversalDelete();

                            inMyFile_AdviceDelete();
                            
                            inMyFile_TCUploadDelete();

                        }
                    }
                }
                else
                    continue;

            }
            
            if (strOut[0]==0x30 || strOut[0]==0x31)
            {
                 if(strOut[0] == 0x31)
                 {
                        strTCT.fDemo = 1;
                 }
                 if(strOut[0] == 0x30)
                 {
                        strTCT.fDemo = 0;
                 }

                 inTCTSave(1);
                 break;
             }
             else
             {
                vduiWarningSound();
                vduiDisplayStringCenter(6,"PLEASE SELECT");
                vduiDisplayStringCenter(7,"A VALID");
                vduiDisplayStringCenter(8,"DEBUG MODE");
                CTOS_Delay(2000);       
            }
        }
        if (ret == d_KBD_CANCEL )
            break ;
    }
       
    return ;
}


void vdCTOS_TipAllowd(void)
{
    BYTE bRet;
    BYTE szInputBuf[15+1];
    int inResult,inResult1;
    TRANS_TOTAL stBankTotal;
    BYTE strOut[30],strtemp[17],key;
    USHORT ret;
    USHORT usLen;
    BYTE szTempBuf[12+1];
    BOOL isKey;
    int shHostIndex = 1;
    int inNum = 0;

    inTCTRead(1);    
    
    CTOS_LCDTClearDisplay();
    vdDispTitleString("SETTING");
    
    while(1)
    {
        clearLine(3);
        clearLine(4);
        clearLine(5);
        clearLine(6);
        clearLine(7);
        clearLine(8);
        setLCDPrint(3, DISPLAY_POSITION_LEFT, "Tip Allowed");
        if(strTCT.fTipAllowFlag == 0)
            setLCDPrint(4, DISPLAY_POSITION_LEFT, "0");
        if(strTCT.fTipAllowFlag == 1)
            setLCDPrint(4, DISPLAY_POSITION_LEFT, "1");     
        
        CTOS_LCDTPrintXY(1, 5, "0-DISABLE      1-ENABLE");
        
        strcpy(strtemp,"New:") ;
        CTOS_LCDTPrintXY(1, 7, strtemp);
        memset(strOut,0x00, sizeof(strOut));
        ret= shCTOS_GetNum(8, 0x01,  strOut, &usLen, 1, 1, 0, d_INPUT_TIMEOUT);
        if (ret == d_KBD_CANCEL )
            break;
        else if(0 == ret )
            break;
        else if(ret==1)
        {
            if (strOut[0]==0x30 || strOut[0]==0x31)
            {
                 if(strOut[0] == 0x31)
                 {
                        strTCT.fTipAllowFlag = 1;
                 }
                 if(strOut[0] == 0x30)
                 {
                        strTCT.fTipAllowFlag = 0;
                 }

                 inTCTSave(1);
                 break;
             }
             else
             {
                vduiWarningSound();
                vduiDisplayStringCenter(6,"PLEASE SELECT");
                vduiDisplayStringCenter(7,"A VALID");
                vduiDisplayStringCenter(8,"DEBUG MODE");
                CTOS_Delay(2000);       
            }
        }
        if (ret == d_KBD_CANCEL )
            break ;
    }
       
    return ;
}

void vdCTOS_GPRSSetting(void)
{
    BYTE bRet,strOut[30],strtemp[17],key;
    BYTE szInputBuf[24+1];
    BYTE szIntComBuf[2];
    BYTE szPhNoBuf[9];
    BYTE szExtNoBuf[4];
    int inResult;
    USHORT ret;
    short inRtn;
    USHORT shMaxLen = 30;
    
    inResult = inTCPRead(1);
    if (inResult != d_OK)
        return;
        
    CTOS_LCDTClearDisplay();
    vdDispTitleString("GPRS SETUP");
    
    while(1)
    {
        clearLine(3);
        clearLine(4);
        clearLine(5);
        clearLine(6);
        clearLine(7);
        clearLine(8);
        setLCDPrint(3, DISPLAY_POSITION_LEFT, "APN");
        setLCDPrint(4, DISPLAY_POSITION_LEFT, strTCP.szAPN);
        
        strcpy(strtemp,"New:") ;
        CTOS_LCDTPrintXY(1, 7, strtemp);
        memset(strOut,0x00, sizeof(strOut));
        ret= InputStringAlphaEx2(1, 8, 0x00, 0x02, strOut, &shMaxLen, 1, d_INPUT_TIMEOUT);
        if(ret==d_KBD_ENTER)
        {
            BolDetachGPRSChangeSetting=TRUE;
            memcpy(strTCP.szAPN, strOut,strlen(strOut));
            strTCP.szAPN[strlen(strOut)]=0;
            inResult = inTCPSave(1);
            break;
        }   
        if(ret == d_KBD_CANCEL)
            break;
    }               

    while(1)
    {
        clearLine(3);
        clearLine(4);
        clearLine(5);
        clearLine(6);
        clearLine(7);
        clearLine(8);
        setLCDPrint(3, DISPLAY_POSITION_LEFT, "GPRS USER NAME");
        setLCDPrint(4, DISPLAY_POSITION_LEFT, strTCP.szUserName);

        strcpy(strtemp,"New:") ;
        CTOS_LCDTPrintXY(1, 7, strtemp);
        memset(strOut,0x00, sizeof(strOut));
        ret= InputStringAlpha(1, 8, 0x00, 0x02, strOut, &shMaxLen, 1, d_INPUT_TIMEOUT);
        if(ret==d_KBD_ENTER)
        {
            BolDetachGPRSChangeSetting=TRUE;
            memcpy(strTCP.szUserName, strOut,strlen(strOut));
            strTCP.szUserName[strlen(strOut)]=0;
            inResult = inTCPSave(1);
            break;
        }   
        if(ret == d_KBD_CANCEL)
            break;
    }               

    while(1)
    {
        clearLine(3);
        clearLine(4);
        clearLine(5);
        clearLine(6);
        clearLine(7);
        clearLine(8);
        setLCDPrint(3, DISPLAY_POSITION_LEFT, "GPRS PASSWORD");
        setLCDPrint(4, DISPLAY_POSITION_CENTER, strTCP.szPassword);

        strcpy(strtemp,"New:") ;
        CTOS_LCDTPrintXY(1, 7, strtemp);
        memset(strOut,0x00, sizeof(strOut));
        ret= InputStringAlpha(1, 8, 0x00, 0x02, strOut, &shMaxLen, 1, d_INPUT_TIMEOUT);
        
        if(ret==d_KBD_ENTER)
        {
            BolDetachGPRSChangeSetting=TRUE;
            memcpy(strTCP.szPassword, strOut,strlen(strOut));
            strTCP.szPassword[strlen(strOut)]=0;
            inResult = inTCPSave(1);
            break;
        }   
        if(ret == d_KBD_CANCEL)
            break;
    }   

    if(BolDetachGPRSChangeSetting==TRUE)
    {
            srTransRec.usTerminalCommunicationMode = strCPT.inCommunicationMode;
            clearLine(3);
            clearLine(4);
            clearLine(5);
            clearLine(6);
            clearLine(7);
            clearLine(8);
            CTOS_LCDTPrintXY (1,7, "Please Wait     ");       
            CTOS_LCDTPrintXY(1, 8, "Init GPRS...     ");
            if (inCTOS_InitComm(srTransRec.usTerminalCommunicationMode) != d_OK) 
            {
               
                vdDisplayErrorMsg(1, 8, "COMM INIT ERR");
                return;
            }
                          
            srCommFuncPoint.inInitComm(&srTransRec,strCPT.inCommunicationMode);
            srCommFuncPoint.inGetCommConfig(&srTransRec);
            srCommFuncPoint.inSetCommConfig(&srTransRec);

    }

    return ;
}

/* delete a Char in string */
void DelCharInStr(char *str, char c, int flag)
{
    int i,l;

    l=strlen(str);

    if (flag == STR_HEAD)
    {
        for (i=0;i<l&&str[i]==c;i++);
        if (i>0) strcpy(str,str+i);
    }

    if (flag == STR_BOTTOM)
    {
        for (i=l-1;i>=0&&str[i]==c;i--);
        str[i+1]='\0';
    }

    if (flag == STR_ALL)
    {
        for (i=0;i<l;i++)
            if (str[i]==c)
            {
                strcpy(str+i,str+i+1);
                i--;
            }
    }
}

void vdCTOSS_DownloadMode(void)
{
	CTOS_LCDTClearDisplay();
	CTOS_EnterDownloadMode();

	inCTOS_KillALLSubAP();
	
	exit(0);
	return ;
}

void vdCTOSS_CheckMemory(void)
{
	vdCTOSS_GetMemoryStatus("MEMORY");
	return ;
}

/* ==========================================================================
 *
 * FUNCTION NAME: SetRTC
 *
 * DESCRIPTION:   Use this function to set the real-time clock's data and time.
 *
 * RETURN:        none.
 *
 * NOTES:         none.
 *
 * ========================================================================== */
void CTOSS_SetRTC(void)
{
	//Declare Local Variable //
	CTOS_RTC SetRTC;
	USHORT i = 0;
	BYTE isSet = FALSE;
	BYTE baYear[4+1] = {0},
		baMonth[2+1] = {0},
		baDay[2+1] = {0},
		baHour[2+1] = {0},
		baMinute[2+1] = {0},
		baSecond[2+1]  = {0};
	BYTE key = 0;
	BYTE babuff[d_BUFF_SIZE] = {0};
	BYTE g_DeviceModel;

	//CTOS_LCDFontSelectMode(d_FONT_FNT_MODE);

	CTOS_LCDTClearDisplay();
	CTOS_LCDTSetReverse(TRUE);
	CTOS_LCDTPrintXY(1, 1, "	 Set RTC	 ");
	CTOS_LCDTSetReverse(FALSE);

	//Read the date and the time //
	CTOS_RTCGet(&SetRTC);

	//Show on the LCD Display //
	CTOS_LCDTPrintXY(1, 2, "   Get	 Set");
	sprintf(babuff,"YY:%04d",SetRTC.bYear + 2000);
	CTOS_LCDTPrintXY(1, 3, babuff);
	sprintf(babuff,"MM:%02d",SetRTC.bMonth);
	CTOS_LCDTPrintXY(1, 4, babuff);
	sprintf(babuff,"DD:%02d",SetRTC.bDay);
	CTOS_LCDTPrintXY(1, 5, babuff);
	sprintf(babuff,"hh:%02d",SetRTC.bHour);
	CTOS_LCDTPrintXY(1, 6, babuff);
	sprintf(babuff,"mm:%02d",SetRTC.bMinute);
	CTOS_LCDTPrintXY(1, 7, babuff);
	sprintf(babuff,"ss:%02d",SetRTC.bSecond);
	CTOS_LCDTPrintXY(1, 8, babuff);
//	  sprintf(babuff,"%02d",SetRTC.bDoW);
//	  CTOS_LCDTPrintXY(15, 8, babuff);

	//Input data for the setting //
	//i = sizeof(baYear);
	i = 5;
	if (InputStringAlphaEx(10,3, 0x05, 0x00, baYear, &i, i, d_INPUT_TIMEOUT) == d_KBD_ENTER){
	//Input Year //
	SetRTC.bYear = (wub_str_2_long(baYear) - 2000);
	isSet = TRUE;
	}
	//i = sizeof(baMonth);
	i = 3;
	if (InputStringAlphaEx(10,4, 0x05, 0x00, baMonth, &i, i, d_INPUT_TIMEOUT) == d_KBD_ENTER){ //Input Month //
	SetRTC.bMonth = wub_str_2_long(baMonth);
	isSet = TRUE;
	}
	//i = sizeof(baDay);
	i = 3;
	if (InputStringAlphaEx(10,5, 0x05, 0x00, baDay, &i, i, d_INPUT_TIMEOUT) == d_KBD_ENTER){ //Input Day //
	SetRTC.bDay = wub_str_2_long(baDay);
	isSet = TRUE;
	}
	//i = sizeof(baHour);
	i = 3;
	if (InputStringAlphaEx(10,6, 0x05, 0x00, baHour, &i, i, d_INPUT_TIMEOUT) == d_KBD_ENTER){
	//Input Hour //
	SetRTC.bHour = wub_str_2_long(baHour);
	isSet = TRUE;
	}
	//i = sizeof(baMinute);
	i = 3;
	if (InputStringAlphaEx(10,7, 0x05, 0x00, baMinute, &i, i, d_INPUT_TIMEOUT) == d_KBD_ENTER){
	//Input Minute //
	SetRTC.bMinute = wub_str_2_long(baMinute);
	isSet = TRUE;
	}
	//i = sizeof(baSecond);
	i = 3;
	if (InputStringAlphaEx(10,8, 0x05, 0x00, baSecond, &i, i, d_INPUT_TIMEOUT) == d_KBD_ENTER){
	//Input Second //
	SetRTC.bSecond = wub_str_2_long(baSecond);
	isSet = TRUE;
	}

	if (isSet){
		//Set the date and time //
		if (CTOS_RTCSet(&SetRTC) == d_OK)
			CTOS_LCDTPrintXY(15, 2, "OK");
		else
			CTOS_LCDTPrintXY(13, 2, "Fail");
		isSet = FALSE;
	}

	WaitKey(10);
//	  CTOS_KBDGet ( &key );

	return;
}

void vdCTOSS_FileUpload(void)
{
    int st, uintRet;
	unsigned char szDisplay[40];
    unsigned char temp[1024];
    unsigned char public_path[50] = "/data/data/pub/";
    unsigned char private_path[50] = "./fs_data";
    FILE  *fPubKey;
    unsigned char szFileName1[50];
	char szFileName[50];
	ULONG ulSize;
	int iExp;
    STR *keyboardLayoutEnglish[]={" 0", "qzQZ1", "abcABC2", "defDEF3", "ghiGHI4",
    "jklJKL5", "mnoMNO6", "prsPRS7", "tuvTUV8", "wxyWXY9", ":;/\\|?,.<>_",
    ".!@#$%^&*()-"};
    int inRet;

	CTOS_LCDTClearDisplay ();                                                                                
                                                                                                           
                               
    CTOS_LCDTSetReverse(FALSE);    
    CTOS_USBOpen();
    
    CTOS_LCDTPrintXY(1,1,"ENTER FILENAME:");
    inRet=CTOS_UIKeypad(1, 8, keyboardLayoutEnglish, 40, 80, d_TRUE, d_FALSE, 0, 0,szFileName1, 50);
    if(inRet==d_OK)
    {
        if (strlen(szFileName1)>0)
		{
            memset(szFileName, 0x00, sizeof(szFileName));
            sprintf(szFileName,"%s%s", public_path,szFileName1);
        }
        else
        {
             CTOS_LCDTPrintXY(1,1,"TRANSFER CANCEL");
             WaitKey(3);
             return;
        }
    }
    else
    {
        CTOS_LCDTPrintXY(1,1,"TRANSFER CANCEL");
        WaitKey(3);
        return;
    }
    //strcpy(szFileName1,"V5S_VISAMASTER.prm");
    //sprintf(szFileName,"%s%s", public_path,szFileName1);
    
    ulSize = 0;
	fPubKey = fopen( (char*)szFileName1, "rb");
	if (fPubKey == NULL)
	{
        CTOS_LCDTPrintXY(1,1,"OPEN FILE ERROR");
        CTOS_LCDTPrintXY(1,2,szFileName1);
        //CTOS_LCDTPrintXY(1,3,&szFileName[19]);
        WaitKey(5);
		//memset(szFileName, 0x00, sizeof(szFileName));
        //sprintf(szFileName,"%s%s", private_path,szFileName1);
        fPubKey = fopen( (char*)szFileName, "rb");
        if (fPubKey == NULL)
        {
            CTOS_LCDTPrintXY(1,1,"OPEN FILE ERROR");
            CTOS_LCDTPrintXY(1,2,szFileName);
            CTOS_LCDTPrintXY(1,3,&szFileName[19]);
            
            memset(szFileName, 0x00, sizeof(szFileName));
            sprintf(szFileName,"%s%s", private_path,szFileName1);
            fPubKey = fopen( (char*)szFileName, "rb");
            if (fPubKey == NULL)
            {
                CTOS_LCDTPrintXY(1,1,"OPEN FILE ERROR");
                CTOS_LCDTPrintXY(1,2,szFileName);
                CTOS_LCDTPrintXY(1,3,&szFileName[19]);
                WaitKey(5);
                return;
            }
        }
		
	}
    
    CTOS_LCDTPrintXY(1,1,"                 ");
    CTOS_LCDTPrintXY(1,2,"                 ");
    CTOS_LCDTPrintXY(1,3,"                 ");
	CTOS_LCDTPrintXY(1,1,"Transfer Size");
	while(1)
	{
		uintRet = fread( temp, 1, sizeof(temp), fPubKey);
		ulSize += uintRet;
		sprintf(szDisplay,"%ld", ulSize);
		CTOS_LCDTPrintXY(1,2,szDisplay);
		if(uintRet > 0)
			CTOS_USBTxData(temp, uintRet);
		if(uintRet <=0)
			break;
		CTOS_Delay(5);
	}
	fclose(fPubKey); 
	CTOS_LCDTPrintXY(1,3,"File Transmitted");
	WaitKey(3); 
    /*if(remove(szFileName1) == -1)
    {
        CTOS_LCDTPrintXY(1,3,"File Delete Error");
   
    }
    else CTOS_LCDTPrintXY(1,3,"File Deleted");
    WaitKey(3);*/
}

void vdDisplayEnvMenuOption(char *pszTag, char *pszValue)
{
	/*display current tag & value*/
	vduiClearBelow(3);
	setLCDPrint(3, DISPLAY_POSITION_LEFT, pszTag);
	setLCDPrint(4, DISPLAY_POSITION_LEFT, pszValue);
		
	/*F1-F4 func display*/
	CTOS_LCDTPrintXY(1, 5, "1 - NEW");
	CTOS_LCDTPrintXY(20-10, 5, "2 - FIND");

	CTOS_LCDTPrintXY(1, 6, "3 - EDIT");
	CTOS_LCDTPrintXY(20-9, 6, "4 - DEL");

	CTOS_LCDTPrintXY(1, 8, "7 - PREV");
	CTOS_LCDTPrintXY(20-9, 8, "9 - NEXT");
}

void vdCTOS_EditEnvParam(void)
{
    BYTE bRet,strOut[30],strtemp[17],key;
    BYTE szInputBuf[24+1];
    BYTE szIntComBuf[2];
    BYTE szPhNoBuf[9];
    BYTE szExtNoBuf[4];
    int inResult;
    USHORT ret;
    USHORT usLen;


	unsigned char keyval;
    BOOL isKey;

	char szCurrTag[64+1];
	char szCurrValue[128+1];

	char szNewTag[64+1];
	char szNewValue[128+1];

	int inEnvTotal = 0;
	int inIdx = 0;

	int inFindIdx = 0;
    char szFindTag[64+1];

	/*title*/
    CTOS_LCDTClearDisplay();
    vdDispTitleString("EDIT ENV PARAM");

	//vdDebug_LogPrintf("=====vdCTOS_EditEnvParam=====");
	
	inEnvTotal = inCTOSS_EnvTotal();

	if (0 == inEnvTotal)
		return;
	
	inIdx = 1;

	
	//vdDebug_LogPrintf("inEnvTotal %d", inEnvTotal);

	/*retrive Env data by index*/
	memset(szCurrTag, 0, sizeof(szCurrTag));
	memset(szCurrValue, 0, sizeof(szCurrValue));
	if (inEnvTotal > 0)
	{
		inCTOSS_GetEnvByIdx(inIdx, szCurrTag, szCurrValue);
	}
	else
	{
		strcpy(szCurrTag, "__________");
		strcpy(szCurrValue, "__________");
	}
	//vdDebug_LogPrintf("inIdx %d szCurrTag [%s] szCurrValue [%s]", inIdx, szCurrTag, szCurrValue);

	vdDisplayEnvMenuOption(szCurrTag, szCurrValue);

	/*set idle timout*/
	CTOS_TimeOutSet(TIMER_ID_3, 3000);
	
    while(1)
    {
    	/*check timeout*/
		if (CTOS_TimeOutCheck(TIMER_ID_3) == d_YES)
        {      
            return;
        }

		keyval = 0;
		
		/*wait for user*/
		CTOS_KBDInKey(&isKey);
        if (isKey)
		{ //If isKey is TRUE, represent key be pressed //
			vduiLightOn();
            //Get a key from keyboard //
            CTOS_KBDGet(&keyval);

			/*set idle timout agian*/
			CTOS_TimeOutSet(TIMER_ID_3, 3000);
        }

		/*
		#define d_KBD_F1							'X'
		#define d_KBD_F2							'Y'
		#define d_KBD_F3							'I'
		#define d_KBD_F4							'J'
		#define d_KBD_UP							'U'
		#define d_KBD_DOWN							'D'
		#define d_KBD_CANCEL						'C'
		#define d_KBD_CLEAR							'R'
		#define d_KBD_ENTER							'A'
		*/
		switch (keyval)
		{
			case d_KBD_1: //New
				memset(szNewTag, 0, sizeof(szNewTag));
				memset(szNewValue, 0, sizeof(szNewValue));
						
				strcpy(strtemp,"TAG:") ; 
				CTOS_LCDTPrintXY(1, 7, strtemp);
				memset(strOut,0x00, sizeof(strOut));
				memset(strtemp, 0x00, sizeof(strtemp));
				usLen = 20;
				ret = InputStringAlpha(1, 8, 0x00, 0x02, strOut, &usLen, 0, d_INPUT_TIMEOUT);
				if(ret==d_KBD_ENTER)
				{
					if(strlen(strOut)>0)
					{
						strcpy(szNewTag, strOut);
								
						//vdDebug_LogPrintf("inIdx %d szNewTag [%s] szNewValue [%s]", inIdx, szNewTag, szNewValue);
						//inCTOSS_PutEnv(szNewTag, szNewValue);
						//break;
					}
					else
					{
					}
				}

				/*new tag not set, break*/
				if (strlen(szNewTag) <= 0)
					break;

				vduiClearBelow(7);
				strcpy(strtemp,"VAL:") ; 
				CTOS_LCDTPrintXY(1, 7, strtemp);
				memset(strOut,0x00, sizeof(strOut));
				memset(strtemp, 0x00, sizeof(strtemp));
				usLen = 20;
				ret = InputStringAlpha(1, 8, 0x00, 0x02, strOut, &usLen, 0, d_INPUT_TIMEOUT);
				if(ret==d_KBD_ENTER)
				{
					if(strlen(strOut)>0)
					{
						strcpy(szNewValue, strOut);
								
						//vdDebug_LogPrintf("inIdx %d szNewTag [%s] szNewValue [%s]", inIdx, szNewTag, szNewValue);
						//inCTOSS_PutEnv(szNewTag, szNewValue);
						//break;
					}
					else
					{
					}
				}

				/*new tag not set, break*/
				if (strlen(szNewValue) <= 0)
					break;

				
				//vdDebug_LogPrintf("inIdx %d szNewTag [%s] szNewValue [%s]", inIdx, szNewTag, szNewValue);
				inCTOSS_PutEnv(szNewTag, szNewValue);
				inEnvTotal = inCTOSS_EnvTotal();
				inIdx = inEnvTotal;

				/*update current display*/
				memset(szCurrTag, 0, sizeof(szCurrTag));
				memset(szCurrValue, 0, sizeof(szCurrValue));
				if (inEnvTotal > 0)
				{
    				inCTOSS_GetEnvByIdx(inIdx, szCurrTag, szCurrValue);
				}
				else
				{
					strcpy(szCurrTag, "__________");
					strcpy(szCurrValue, "__________");
				}

				vdDisplayEnvMenuOption(szCurrTag, szCurrValue);
				
				break;
				
			case d_KBD_2: //Find
				memset(szNewTag, 0, sizeof(szNewTag));
				memset(szNewValue, 0, sizeof(szNewValue));
						
				strcpy(strtemp,"TAG:") ; 
				CTOS_LCDTPrintXY(1, 7, strtemp);
				memset(strOut,0x00, sizeof(strOut));
				memset(strtemp, 0x00, sizeof(strtemp));
				usLen = 20;
				ret = InputStringAlpha(1, 8, 0x00, 0x02, strOut, &usLen, 0, d_INPUT_TIMEOUT);
				if(ret==d_KBD_ENTER)
				{
					if(strlen(strOut)>0)
					{
						//strcpy(szNewValue, strOut);
						//strcpy(szNewTag, szCurrTag);
						strcpy(szFindTag, strOut);

						inCTOSS_GetEnvIdx(szFindTag, &inFindIdx);
						//vdDebug_LogPrintf("inIdx %d szFindTag [%s]", inFindIdx, szFindTag);
						inIdx = inFindIdx;					}
					else
					{

					}
				}
				/*update current display*/
				memset(szCurrTag, 0, sizeof(szCurrTag));
				memset(szCurrValue, 0, sizeof(szCurrValue));
				if (inEnvTotal > 0)
				{
    				inCTOSS_GetEnvByIdx(inIdx, szCurrTag, szCurrValue);
				}
				else
				{
					strcpy(szCurrTag, "__________");
					strcpy(szCurrValue, "__________");
				}

				vdDisplayEnvMenuOption(szCurrTag, szCurrValue);
				break;
				
			case d_KBD_3: //Edit
				
				memset(szNewTag, 0, sizeof(szNewTag));
				memset(szNewValue, 0, sizeof(szNewValue));
				
				strcpy(strtemp,"New:") ; 
				CTOS_LCDTPrintXY(1, 7, strtemp);
				memset(strOut,0x00, sizeof(strOut));
				memset(strtemp, 0x00, sizeof(strtemp));
				usLen = 20;
				ret = InputStringAlpha(1, 8, 0x00, 0x02, strOut, &usLen, 0, d_INPUT_TIMEOUT);
				if(ret==d_KBD_ENTER)
				{
					if(strlen(strOut)>0)
					{
						strcpy(szNewValue, strOut);
						strcpy(szNewTag, szCurrTag);
								
						//vdDebug_LogPrintf("inIdx %d szNewTag [%s] szNewValue [%s]", inIdx, szNewTag, szNewValue);
						inCTOSS_PutEnv(szNewTag, szNewValue);
						//break;
					}
					else
					{
						vduiClearBelow(4);
				
						vduiDisplayStringCenter(5,"NO VALUE SET");
						//vduiDisplayStringCenter(7,"PREV[UP] NEXT[DOWN]");
						//vduiDisplayStringCenter(7,"PABX?");
						//vduiDisplayStringCenter(8,"NO[X] YES[OK]");
						//key=struiGetchWithTimeOut();
					}
				}

				/*update current display*/
				memset(szCurrTag, 0, sizeof(szCurrTag));
				memset(szCurrValue, 0, sizeof(szCurrValue));
				if (inEnvTotal > 0)
				{
    				inCTOSS_GetEnvByIdx(inIdx, szCurrTag, szCurrValue);
				}
				else
				{
					strcpy(szCurrTag, "__________");
					strcpy(szCurrValue, "__________");
				}

				vdDisplayEnvMenuOption(szCurrTag, szCurrValue);
				break;
				
			case d_KBD_4: //Del
				inCTOSS_DelEnv(szCurrTag);
				inEnvTotal = inCTOSS_EnvTotal();
				inIdx = 1;
				/*retrive Env data by index*/
				memset(szCurrTag, 0, sizeof(szCurrTag));
				memset(szCurrValue, 0, sizeof(szCurrValue));
				if (inEnvTotal > 0)
				{
					inCTOSS_GetEnvByIdx(inIdx, szCurrTag, szCurrValue);
				}
				else
				{
					strcpy(szCurrTag, "__________");
					strcpy(szCurrValue, "__________");
				}
				//vdDebug_LogPrintf("inIdx %d szCurrTag [%s] szCurrValue [%s]", inIdx, szCurrTag, szCurrValue);

				vdDisplayEnvMenuOption(szCurrTag, szCurrValue);
				break;
				
			case d_KBD_7: //prev
				inIdx--;
				if (inIdx < 1)
					inIdx = inEnvTotal;
				/*retrive Env data by index*/
    			memset(szCurrTag, 0, sizeof(szCurrTag));
				memset(szCurrValue, 0, sizeof(szCurrValue));
				if (inEnvTotal > 0)
				{
    				inCTOSS_GetEnvByIdx(inIdx, szCurrTag, szCurrValue);
				}
				else
				{
					strcpy(szCurrTag, "__________");
					strcpy(szCurrValue, "__________");
				}
				//vdDebug_LogPrintf("inIdx %d szCurrTag [%s] szCurrValue [%s]", inIdx, szCurrTag, szCurrValue);

				vdDisplayEnvMenuOption(szCurrTag, szCurrValue);
				break;
				
			case d_KBD_9: //next
				inIdx++;
				if (inIdx > inEnvTotal)
					inIdx = 1;
				memset(szCurrTag, 0, sizeof(szCurrTag));
				memset(szCurrValue, 0, sizeof(szCurrValue));
				if (inEnvTotal > 0)
				{
    				inCTOSS_GetEnvByIdx(inIdx, szCurrTag, szCurrValue);
				}
				else
				{
					strcpy(szCurrTag, "__________");
					strcpy(szCurrValue, "__________");
				}
				//vdDebug_LogPrintf("inIdx %d szCurrTag [%s] szCurrValue [%s]", inIdx, szCurrTag, szCurrValue);

				vdDisplayEnvMenuOption(szCurrTag, szCurrValue);
				break;
				
			case d_KBD_CANCEL: //exit
				return ;
				//break;
			default:
				break;
		}
	}

}

void vdCTOS_EditEnvParamDB(void)
{
    BYTE bRet,strOut[30],strtemp[17],key;
    BYTE szInputBuf[24+1];
    BYTE szIntComBuf[2];
    BYTE szPhNoBuf[9];
    BYTE szExtNoBuf[4];
    int inResult;
    USHORT ret;
    USHORT usLen;


	unsigned char keyval;
    BOOL isKey;

	char szCurrTag[64+1];
	char szCurrValue[128+1];

	char szNewTag[64+1];
	char szNewValue[128+1];

	int inEnvTotal = 0;
	int inIdx = 0;

	int inFindIdx = 0;
    char szFindTag[64+1];

	/*title*/
    CTOS_LCDTClearDisplay();
    vdDispTitleString("EDIT ENV PARAM");

	vdDebug_LogPrintf("=====vdCTOS_EditEnvParamDB=====");
	
	inEnvTotal = inCTOSS_EnvTotalDB();

	vdDebug_LogPrintf("inCTOSS_EnvTotal inEnvTotal[%d]", inEnvTotal);
	
	//if (0 == inEnvTotal)
		//return;
	
	inIdx = 1;

	/*retrive Env data by index*/
	memset(szCurrTag, 0, sizeof(szCurrTag));
	memset(szCurrValue, 0, sizeof(szCurrValue));
	if (inEnvTotal > 0)
	{
		inCTOSS_GetEnvByIdxDB(inIdx, szCurrTag, szCurrValue);
	}
	else
	{
		strcpy(szCurrTag, "__________");
		strcpy(szCurrValue, "__________");
	}
	//vdDebug_LogPrintf("inIdx %d szCurrTag [%s] szCurrValue [%s]", inIdx, szCurrTag, szCurrValue);

	vdDisplayEnvMenuOption(szCurrTag, szCurrValue);

	/*set idle timout*/
	CTOS_TimeOutSet(TIMER_ID_3, 3000);
	
    while(1)
    {
    	/*check timeout*/
		if (CTOS_TimeOutCheck(TIMER_ID_3) == d_YES)
        {      
            return;
        }

		keyval = 0;
		
		/*wait for user*/
		CTOS_KBDInKey(&isKey);
        if (isKey)
		{ //If isKey is TRUE, represent key be pressed //
			vduiLightOn();
            //Get a key from keyboard //
            CTOS_KBDGet(&keyval);

			/*set idle timout agian*/
			CTOS_TimeOutSet(TIMER_ID_3, 3000);
        }

		switch (keyval)
		{
			case d_KBD_1: //New
				memset(szNewTag, 0, sizeof(szNewTag));
				memset(szNewValue, 0, sizeof(szNewValue));
						
				strcpy(strtemp,"TAG:") ; 
				CTOS_LCDTPrintXY(1, 7, strtemp);
				memset(strOut,0x00, sizeof(strOut));
				memset(strtemp, 0x00, sizeof(strtemp));
				usLen = 20;
				ret = InputStringAlpha(1, 8, 0x00, 0x02, strOut, &usLen, 0, d_INPUT_TIMEOUT);
				if(ret==d_KBD_ENTER)
				{
					if(strlen(strOut)>0)
					{
						strcpy(szNewTag, strOut);
								
						//vdDebug_LogPrintf("inIdx %d szNewTag [%s] szNewValue [%s]", inIdx, szNewTag, szNewValue);
						//inCTOSS_PutEnv(szNewTag, szNewValue);
						//break;
					}
					else
					{
					}
				}

				/*new tag not set, break*/
				if (strlen(szNewTag) <= 0)
					break;

				vduiClearBelow(7);
				strcpy(strtemp,"VAL:") ; 
				CTOS_LCDTPrintXY(1, 7, strtemp);
				memset(strOut,0x00, sizeof(strOut));
				memset(strtemp, 0x00, sizeof(strtemp));
				usLen = 20;
				ret = InputStringAlpha(1, 8, 0x00, 0x02, strOut, &usLen, 0, d_INPUT_TIMEOUT);
				if(ret==d_KBD_ENTER)
				{
					if(strlen(strOut)>0)
					{
						strcpy(szNewValue, strOut);
								
						//vdDebug_LogPrintf("inIdx %d szNewTag [%s] szNewValue [%s]", inIdx, szNewTag, szNewValue);
						//inCTOSS_PutEnv(szNewTag, szNewValue);
						//break;
					}
					else
					{
					}
				}

				/*new tag not set, break*/
				if (strlen(szNewValue) <= 0)
					break;

				
				//vdDebug_LogPrintf("inIdx %d szNewTag [%s] szNewValue [%s]", inIdx, szNewTag, szNewValue);
				inCTOSS_PutEnvDB(szNewTag, szNewValue);
				inEnvTotal = inCTOSS_EnvTotalDB();
				inIdx = inEnvTotal;

				/*update current display*/
				memset(szCurrTag, 0, sizeof(szCurrTag));
				memset(szCurrValue, 0, sizeof(szCurrValue));
				if (inEnvTotal > 0)
				{
    				inCTOSS_GetEnvByIdxDB(inIdx, szCurrTag, szCurrValue);
				}
				else
				{
					strcpy(szCurrTag, "__________");
					strcpy(szCurrValue, "__________");
				}

				vdDisplayEnvMenuOption(szCurrTag, szCurrValue);
				
				break;
				
			case d_KBD_2: //Find
				memset(szNewTag, 0, sizeof(szNewTag));
				memset(szNewValue, 0, sizeof(szNewValue));
						
				strcpy(strtemp,"TAG:") ; 
				CTOS_LCDTPrintXY(1, 7, strtemp);
				memset(strOut,0x00, sizeof(strOut));
				memset(strtemp, 0x00, sizeof(strtemp));
				usLen = 20;
				ret = InputStringAlpha(1, 8, 0x00, 0x02, strOut, &usLen, 0, d_INPUT_TIMEOUT);
				if(ret==d_KBD_ENTER)
				{
					if(strlen(strOut)>0)
					{
						//strcpy(szNewValue, strOut);
						//strcpy(szNewTag, szCurrTag);
						strcpy(szFindTag, strOut);

						inCTOSS_GetEnvIdxDB(szFindTag, &inFindIdx);
						//vdDebug_LogPrintf("inIdx %d szFindTag [%s]", inFindIdx, szFindTag);
						inIdx = inFindIdx;
					}
					else
					{

					}
				}
				/*update current display*/
				memset(szCurrTag, 0, sizeof(szCurrTag));
				memset(szCurrValue, 0, sizeof(szCurrValue));
				if (inEnvTotal > 0)
				{
    				inCTOSS_GetEnvByIdxDB(inIdx, szCurrTag, szCurrValue);
				}
				else
				{
					strcpy(szCurrTag, "__________");
					strcpy(szCurrValue, "__________");
				}

				vdDisplayEnvMenuOption(szCurrTag, szCurrValue);
				break;
				
			case d_KBD_3: //Edit
				
				memset(szNewTag, 0, sizeof(szNewTag));
				memset(szNewValue, 0, sizeof(szNewValue));
				
				strcpy(strtemp,"New:") ; 
				CTOS_LCDTPrintXY(1, 7, strtemp);
				memset(strOut,0x00, sizeof(strOut));
				memset(strtemp, 0x00, sizeof(strtemp));
				usLen = 20;
				ret = InputStringAlpha(1, 8, 0x00, 0x02, strOut, &usLen, 0, d_INPUT_TIMEOUT);
				if(ret==d_KBD_ENTER)
				{
					if(strlen(strOut)>0)
					{
						strcpy(szNewValue, strOut);
						strcpy(szNewTag, szCurrTag);
								
						//vdDebug_LogPrintf("inIdx %d szNewTag [%s] szNewValue [%s]", inIdx, szNewTag, szNewValue);
						inCTOSS_PutEnvDB(szNewTag, szNewValue);
						//break;
					}
					else
					{
						vduiClearBelow(4);
				
						vduiDisplayStringCenter(5,"NO VALUE SET");
						//vduiDisplayStringCenter(7,"PREV[UP] NEXT[DOWN]");
						//vduiDisplayStringCenter(7,"PABX?");
						//vduiDisplayStringCenter(8,"NO[X] YES[OK]");
						//key=struiGetchWithTimeOut();
					}
				}

				/*update current display*/
				memset(szCurrTag, 0, sizeof(szCurrTag));
				memset(szCurrValue, 0, sizeof(szCurrValue));
				if (inEnvTotal > 0)
				{
    				inCTOSS_GetEnvByIdxDB(inIdx, szCurrTag, szCurrValue);
				}
				else
				{
					strcpy(szCurrTag, "__________");
					strcpy(szCurrValue, "__________");
				}

				vdDisplayEnvMenuOption(szCurrTag, szCurrValue);
				break;
				
			case d_KBD_4: //Del
				inCTOSS_DelEnvDB(szCurrTag);
				inEnvTotal = inCTOSS_EnvTotalDB();
				inIdx = 1;
				/*retrive Env data by index*/
				memset(szCurrTag, 0, sizeof(szCurrTag));
				memset(szCurrValue, 0, sizeof(szCurrValue));
				if (inEnvTotal > 0)
				{
					inCTOSS_GetEnvByIdxDB(inIdx, szCurrTag, szCurrValue);
				}
				else
				{
					strcpy(szCurrTag, "__________");
					strcpy(szCurrValue, "__________");
				}
				//vdDebug_LogPrintf("inIdx %d szCurrTag [%s] szCurrValue [%s]", inIdx, szCurrTag, szCurrValue);

				vdDisplayEnvMenuOption(szCurrTag, szCurrValue);
				break;
				
			case d_KBD_7: //prev
				inIdx--;
				if (inIdx < 1)
					inIdx = inEnvTotal;
				/*retrive Env data by index*/
    			memset(szCurrTag, 0, sizeof(szCurrTag));
				memset(szCurrValue, 0, sizeof(szCurrValue));
				if (inEnvTotal > 0)
				{
    				inCTOSS_GetEnvByIdxDB(inIdx, szCurrTag, szCurrValue);
				}
				else
				{
					strcpy(szCurrTag, "__________");
					strcpy(szCurrValue, "__________");
				}
				//vdDebug_LogPrintf("inIdx %d szCurrTag [%s] szCurrValue [%s]", inIdx, szCurrTag, szCurrValue);

				vdDisplayEnvMenuOption(szCurrTag, szCurrValue);
				break;
				
			case d_KBD_9: //next
				inIdx++;
				if (inIdx > inEnvTotal)
					inIdx = 1;
				memset(szCurrTag, 0, sizeof(szCurrTag));
				memset(szCurrValue, 0, sizeof(szCurrValue));
				if (inEnvTotal > 0)
				{
    				inCTOSS_GetEnvByIdxDB(inIdx, szCurrTag, szCurrValue);
				}
				else
				{
					strcpy(szCurrTag, "__________");
					strcpy(szCurrValue, "__________");
				}
				//vdDebug_LogPrintf("inIdx %d szCurrTag [%s] szCurrValue [%s]", inIdx, szCurrTag, szCurrValue);

				vdDisplayEnvMenuOption(szCurrTag, szCurrValue);
				break;
				
			case d_KBD_CANCEL: //exit
				return ;
				//break;
			default:
				break;
		}
	}

}


/*sidumili: [prompt for password]*/
int inCTOS_PromptPassword(void)
{
	int inRet = d_NO;

	//CTOS_LCDTClearDisplay();
	//vduiClearBelow(7);
	vdCTOS_SetTransType(SETUP);
	vdDispTransTitle(SETUP);

	inRet = inCTOSS_CheckMemoryStatus();
	if(d_OK != inRet)
		return inRet;
	
	inRet = inCTOS_GetTxnPassword();
	if(d_OK != inRet)
	{
		//inCTOS_IdleEventProcess();
		return inRet;
	}

	return d_OK;
}
////for COM&USB communication
int inCTOS_CommsFallback(int shHostIndex)
{
	int inResult = 0;

	vdDebug_LogPrintf("inCTOS_CommsFallback, Host Index [%d]", shHostIndex);
	vdDebug_LogPrintf("srTransRec.usTerminalCommunicationMode [%d]", srTransRec.usTerminalCommunicationMode);
	vdDebug_LogPrintf("strCPT.inCommunicationMode [%d],inSecCommunicationMode=[%d]", strCPT.inCommunicationMode,strCPT.inSecCommunicationMode);

	//if (srTransRec.usTerminalCommunicationMode == DIAL_UP_MODE)
	//	return d_NO;
	if (strCPT.inCommunicationMode == strCPT.inSecCommunicationMode)
		return d_NO;

	vduiClearBelow(3);
	CTOS_LCDTPrintXY (1,6, "Comms Fallback");
	CTOS_LCDTPrintXY (1,7, "Please Wait 	");

	//if ((srTransRec.usTerminalCommunicationMode == ETHERNET_MODE) || (srTransRec.usTerminalCommunicationMode == GPRS_MODE))
	//{
	//	CTOS_LCDTPrintXY(1, 8, "Init Modem... 	");
		srTransRec.usTerminalCommunicationMode = strCPT.inSecCommunicationMode;
	//}
	//else
	//{
	//	CTOS_LCDTPrintXY(1, 8, "Init IP...	  ");
	//	srTransRec.usTerminalCommunicationMode = ETHERNET_MODE;
	//}
	
	if (inCTOS_InitComm(srTransRec.usTerminalCommunicationMode) != d_OK) 
	{
		vdDebug_LogPrintf("inCTOS_CommsFallback, COMM INIT ERR");
		vdDisplayErrorMsg(1, 8, "COMM INIT ERR");
		return d_NO;
	}

	inCTOS_CheckInitComm(srTransRec.usTerminalCommunicationMode);

	vdDebug_LogPrintf("vdCTOS_DialBackupConfig, inCTOS_CheckInitComm");

	if (srTransRec.byOffline == CN_FALSE)
	{
		inResult = srCommFuncPoint.inCheckComm(&srTransRec);
	}

	vduiClearBelow(3);
	
	return d_OK;	
}

//format amount 10+2
void vdCTOSS_DisplayAmount(USHORT usX, USHORT usY, char *szCurSymbol,char *szAmount)
{
	int x=0;
	int len, index;

	CTOS_LCDTPrintXY(usX, usY, szCurSymbol);
	x=0;
	len=strlen(szAmount);
	for(index=0; index < len; index++)
	{
	   if(szAmount[index] == '.')
	       x+=1;
	   else
	       x+=2;
	}
	CTOS_LCDTPrintXY(37-x, usY, szAmount);
}


void vdCTOSS_EditTable(void)
{
	BYTE strOut[100];
	BYTE szdatabase[100];
	BYTE sztable[100];
    USHORT usLen;
	USHORT ret;
	char szDispay[50];

	memset(szDispay,0x00,sizeof(szDispay));
	memset(szdatabase,0x00,sizeof(szdatabase));
	memset(sztable,0x00,sizeof(sztable));
	sprintf(szDispay,"EDIT DATABASE");
	CTOS_LCDTClearDisplay();
	
    vdDispTitleString(szDispay);            
	CTOS_LCDTPrintXY(1, 3, "DATABASE NAME:");
	
	usLen = 20;
	CTOS_LCDFontSelectMode(d_FONT_FNT_MODE);
    ret = InputStringAlphaEx(1, 7, 0x00, 0x02, szdatabase, &usLen, 1, d_INPUT_TIMEOUT);
	if (strTCT.inThemesType == 1)
	{
		CTOS_LCDTTFSelect("tahoma.ttf", 0);
		CTOS_LCDTTFSwichDisplayMode(d_TTF_MODE_VIETNAM);
	}
	else
	{
		CTOS_LCDTTFSelect(d_FONT_DEFAULT_TTF, 0);
		CTOS_LCDTTFSwichDisplayMode(d_TTF_MODE_DEFAULT);
	}

	if (ret == d_KBD_CANCEL || 0 == ret )
	{
		return ;
	}
	if(ret>= 1)
    {
    	//vduiClearBelow(2);
		CTOS_LCDTClearDisplay();
		vdDispTitleString(szDispay); 
        vdDebug_LogPrintf("szdatabase[%s].usLen=[%d].",szdatabase,usLen);
		CTOS_LCDTPrintXY(1, 3, "TABLE NAME:");
	
		usLen = 20;
		CTOS_LCDFontSelectMode(d_FONT_FNT_MODE);
	    ret = InputStringAlphaEx(1, 7, 0x00, 0x02, sztable, &usLen, 1, d_INPUT_TIMEOUT);
		if (strTCT.inThemesType == 1)
		{
			CTOS_LCDTTFSelect("tahoma.ttf", 0);
			CTOS_LCDTTFSwichDisplayMode(d_TTF_MODE_VIETNAM);
		}
		else
		{
			CTOS_LCDTTFSelect(d_FONT_DEFAULT_TTF, 0);
			CTOS_LCDTTFSwichDisplayMode(d_TTF_MODE_DEFAULT);
		}
		
		if (ret == d_KBD_CANCEL || 0 == ret )
		{
			return ;
		}
		if(ret>= 1)
	    {
	        vdDebug_LogPrintf("sztable[%s].usLen=[%d].szdatabase[%s]",sztable,usLen,szdatabase);
			inCTOSS_EditTalbe(sztable,szdatabase);
	    }
	
	}
}

void vdCTOS_PaymentGateway(void)
{
    BYTE bRet,strOut[30],strtemp[17],key;
    BYTE szInputBuf[24+1];
    BYTE szIntComBuf[2];
    BYTE szPhNoBuf[9];
    BYTE szExtNoBuf[4];
    int inResult;
    USHORT ret;
    short inRtn;
    USHORT shMaxLen = 30;
    
    inResult = inGCTRead(1);
    if (inResult != d_OK)
        return;
        
    CTOS_LCDTClearDisplay();
    vdDispTitleString("EDIT PG");
    
    while(1)
    {
        clearLine(3);
        clearLine(4);
        clearLine(5);
        clearLine(6);
        clearLine(7);
        clearLine(8);
        setLCDPrint(3, DISPLAY_POSITION_LEFT, "USERNAME");
        setLCDPrint(4, DISPLAY_POSITION_LEFT, strGCT.szUserName);
        
        strcpy(strtemp,"New:") ;
        CTOS_LCDTPrintXY(1, 7, strtemp);
        memset(strOut,0x00, sizeof(strOut));
        ret= InputStringAlpha(1, 8, 0x00, 0x02, strOut, &shMaxLen, 1, d_INPUT_TIMEOUT);
        if(ret==d_KBD_ENTER)
        {
            memcpy(strGCT.szUserName, strOut,strlen(strOut));
            strGCT.szUserName[strlen(strOut)]=0;
            inResult = inGCTSave(1);
            break;
        }   
        if(ret == d_KBD_CANCEL)
            break;
    }                             

    while(1)
    {
        clearLine(3);
        clearLine(4);
        clearLine(5);
        clearLine(6);
        clearLine(7);
        clearLine(8);
        setLCDPrint(3, DISPLAY_POSITION_LEFT, "PASSWORD");
        setLCDPrint(4, DISPLAY_POSITION_LEFT, strGCT.szPassword);

        strcpy(strtemp,"New:") ;
        CTOS_LCDTPrintXY(1, 7, strtemp);
        memset(strOut,0x00, sizeof(strOut));
		shMaxLen = 30;
        ret= InputStringAlpha(1, 8, 0x00, 0x02, strOut, &shMaxLen, 1, d_INPUT_TIMEOUT);
        
        if(ret==d_KBD_ENTER)
        {
            memcpy(strGCT.szPassword, strOut,strlen(strOut));
            strGCT.szPassword[strlen(strOut)]=0;
            inResult = inGCTSave(1);
            break;
        }   
        if(ret == d_KBD_CANCEL)
            break;
    }   

	while(1)
    {
        clearLine(3);
        clearLine(4);
        clearLine(5);
        clearLine(6);
        clearLine(7);
        clearLine(8);
        setLCDPrint(3, DISPLAY_POSITION_LEFT, "POS ID");
        setLCDPrint(4, DISPLAY_POSITION_LEFT, strGCT.szPOSid);

        strcpy(strtemp,"New:") ;
        CTOS_LCDTPrintXY(1, 7, strtemp);
        memset(strOut,0x00, sizeof(strOut));
		shMaxLen = 30;
        ret= InputStringAlpha(1, 8, 0x00, 0x02, strOut, &shMaxLen, 1, d_INPUT_TIMEOUT);
        if(ret==d_KBD_ENTER)
        {
            memcpy(strGCT.szPOSid, strOut,strlen(strOut));
            strGCT.szPOSid[strlen(strOut)]=0;
            inResult = inGCTSave(1);
            break;
        }   
        if(ret == d_KBD_CANCEL)
            break;
    } 

	while(1)
    {
        clearLine(3);
        clearLine(4);
        clearLine(5);
        clearLine(6);
        clearLine(7);
        clearLine(8);
        setLCDPrint(3, DISPLAY_POSITION_LEFT, "RSC");
        setLCDPrint(4, DISPLAY_POSITION_LEFT, strGCT.szRSC);

        strcpy(strtemp,"New:") ;
        CTOS_LCDTPrintXY(1, 7, strtemp);
        memset(strOut,0x00, sizeof(strOut));
		shMaxLen = 30;
        ret= InputStringAlpha(1, 8, 0x00, 0x02, strOut, &shMaxLen, 1, d_INPUT_TIMEOUT);
        if(ret==d_KBD_ENTER)
        {
            memcpy(strGCT.szRSC, strOut,strlen(strOut));
            strGCT.szRSC[strlen(strOut)]=0;
            inResult = inGCTSave(1);
            break;
        }   
        if(ret == d_KBD_CANCEL)
            break;
    }


    return ;
}

int get_env(char *tag, char *value, int len)
{
	int inRet = 0;
	inRet = inCTOSS_GetEnvDB (tag, value);

	vdDebug_LogPrintf("get_env tag[%s] value[%s] Ret[%d]", tag, value, inRet);
	return inRet;
}

int put_env(char *tag, char *value, int len)
{
	int inRet = 0;
	
	inRet = inCTOSS_PutEnvDB (tag, value);

	vdDebug_LogPrintf("put_env tag[%s] value[%s] Ret[%d]", tag, value, inRet);
	return inRet;
}


int get_env_int (char *tag)
{
	int     ret = -1;
	char    buf[64];

    memset (buf, 0, sizeof (buf));
    if ( inCTOSS_GetEnvDB (tag, buf) == d_OK )
    {
        ret = atoi (buf);
    }

	vdDebug_LogPrintf("get_env_int [%s]=[%d]", tag, ret);

    return ret;
}

void put_env_int(char *tag, int value)
{
	int     ret = -1;
	char    buf[6];

    memset (buf, 0, sizeof (buf));
    //int2str (buf, value);
    sprintf(buf, "%d", value);
    ret = inCTOSS_PutEnvDB (tag, buf);

	vdDebug_LogPrintf("put_env_int [%s]=[%d] ret[%d]", tag, value, ret);
}

#ifdef UNUSE
#define CALC_BDK2	"\xC0\xC0\xC0\xC0\x00\x00\x00\x00\xC0\xC0\xC0\xC0\x00\x00\x00\x00"
#define CALC_KSN2	"\xFF\xFF\xFF\xFF\xFF\xFF\xFF\xE0"
void vdCTOSS_DUKPTCalcIPEK(char *szBDK,char *szKSN,char *szIPEK)
{
	unsigned char szBDK2[16+1];
	unsigned char sztmpIPEK[16+1];
	unsigned char sztmpKSN[8+1];
	

	memset(szBDK2,0x00,sizeof(szBDK2));
	memset(sztmpIPEK,0x00,sizeof(sztmpIPEK));
	memset(sztmpKSN,0x00,sizeof(sztmpKSN));

	memcpy(szBDK2,szBDK,16);
	XOR(szBDK2,CALC_BDK2,16);
	vdPCIDebug_HexPrintf("szBDK2",szBDK2,16);

	memcpy(sztmpKSN,szKSN,8);
	AND(sztmpKSN,CALC_KSN2,8);
	vdPCIDebug_HexPrintf("sztmpKSN",sztmpKSN,8);

	TripleDes_16Key(szBDK,sztmpKSN,sztmpIPEK,'E');
	vdPCIDebug_HexPrintf("szIPEK",sztmpIPEK,8);
	TripleDes_16Key(szBDK2,sztmpKSN,&sztmpIPEK[8],'E');
	vdPCIDebug_HexPrintf("szIPEK",&sztmpIPEK[8],8);

	vdPCIDebug_HexPrintf("szIPEK",sztmpIPEK,16);
	memcpy(szIPEK,sztmpIPEK,16);
	
}
#endif

void vdCTOS_TMSSetting(void)
{
	vdCTOS_InputTMSSetting();
}

void vdCTOS_TMSReSet(void)
{
	vdCTOS_InputTMSReSet();
}

void vdCTOS_TMSUploadFile(void)
{
	VdCTOSS_TMSUpload();
}



int  inCTOS_TMSPreConfigSetting(void)
{
	BYTE strOut[30],strtemp[17];
	BYTE szInputBuf[5];
    int inResult;
    int ret;
	USHORT usLen;

    CTOS_LCDTClearDisplay();
    vdDispTitleString("TMS SETTINGS");

    inResult = inTCTRead(1);
    if(inResult != d_OK)
        return d_NO;	
	
    while(1)
    {   
        vduiClearBelow(3);
        setLCDPrint(3, DISPLAY_POSITION_LEFT, "TMS COMM MODE");
		setLCDPrint(4, DISPLAY_POSITION_LEFT, "0-DIALUP 1-ETH");
		setLCDPrint(5, DISPLAY_POSITION_LEFT, "2-GPRS 4-WIFI");
        memset(szInputBuf, 0x00, sizeof(szInputBuf));
        sprintf(szInputBuf, "%d", strTCT.inTMSComMode);
        setLCDPrint(6, DISPLAY_POSITION_LEFT, szInputBuf);
        
        strcpy(strtemp,"New:") ;
        CTOS_LCDTPrintXY(1, 7, strtemp);
        memset(strOut,0x00, sizeof(strOut));
		ret= shCTOS_GetNum(8, 0x01,  strOut, &usLen, 1, 1, 0, d_INPUT_TIMEOUT);
        if (ret == d_KBD_CANCEL )
            return d_NO;
        else if(0 == ret )
            return d_NO;
        else if(ret>=1)
        {
			if(strOut[0] == 0x30 || strOut[0] == 0x31 || strOut[0] == 0x32 || strOut[0] == 0x34)
			{
                strTCT.inTMSComMode=atoi(strOut);      
                vdMyEZLib_LogPrintf("strTCT.usTMSGap %d",strTCT.inTMSComMode);
			    inResult = inTCTSave(1);
                break;				
			}
            else
                vdDisplayErrorMsg(1, 8, "INVALID INPUT");
        }   
    }	
	
    return d_OK;
}

static int firsttime = 0;
int inCTOSS_PassData(void)
{
	BYTE inRet,inRetVal;
	int inResult;
	int inSendLen, inReceLen;
    BYTE uszSendData[2048 + 1], uszReceData[2048 + 1];

	memset(&srTransRec,0x00,sizeof(TRANS_DATA_TABLE));
	memset(uszSendData,0x00,sizeof(uszSendData));
	memset(uszReceData,0x00,sizeof(uszReceData));

	CTOS_LCDTClearDisplay();
    vdDispTitleString("PASSTHROUGH");
	
	inRet = inCTOS_MultiAPGetData();
    if(d_OK != inRet)
        return inRet;

#ifdef VERSION2
	vdDebug_LogPrintf("inCommunicationMode=[%d]",srTransRec.usTrack1Len);
	vdDebug_LogPrintf("szPriTxnHostIP=[%s]",srTransRec.szTrack1Data);
	vdDebug_LogPrintf("szSecTxnHostIP=[%s]",srTransRec.szTrack2Data);
	vdDebug_LogPrintf("inPriTxnHostPortNum=[%d]",srTransRec.ulTraceNum);
	vdDebug_LogPrintf("inSecTxnHostPortNum=[%d]",srTransRec.ulOrgTraceNum);
	vdDebug_LogPrintf("inLen=[%d]",srTransRec.usTrack2Len);
#endif	
	vdDebug_LogPrintf("usAdditionalDataLen=[%d],usChipDataLen=[%d]", srTransRec.usAdditionalDataLen,srTransRec.usChipDataLen);

	if (srTransRec.usAdditionalDataLen > 0)
	{
		memcpy(uszSendData,srTransRec.baChipData,1024);

		memcpy(&uszSendData[1024],srTransRec.baAdditionalData,srTransRec.usAdditionalDataLen);
		inSendLen = 1024 + srTransRec.usAdditionalDataLen;
	}
	else
	{
		inSendLen = srTransRec.usChipDataLen;
		memcpy(uszSendData,srTransRec.baChipData,inSendLen);
	}

	inCPTRead(1);
#ifdef VERSION2	
	strCPT.inCommunicationMode = srTransRec.usTrack1Len;
	strCPT.inPriTxnHostPortNum = srTransRec.ulTraceNum;
	strCPT.inSecTxnHostPortNum = srTransRec.ulOrgTraceNum;
	strcpy(strCPT.szPriTxnHostIP,srTransRec.szTrack1Data);
	strcpy(strCPT.szSecTxnHostIP,srTransRec.szTrack2Data);
#endif	
	if (inCTOS_InitComm(strCPT.inCommunicationMode) != d_OK) 
    {
        vdDisplayErrorMsg(1,8,"COMM INIT ERR");
        return(d_NO);
	}
	inRetVal = inCTOS_CheckInitComm(strCPT.inCommunicationMode); 
	if (inRetVal != d_OK)
	{
		if (strCPT.inCommunicationMode == GPRS_MODE)
			vdDisplayErrorMsg(1,8,"GPRS NOT ESTABLISHED");
		else
        vdDisplayErrorMsg(1,8,"COMM INIT ERR");
        return(d_NO);
    }
	if (srCommFuncPoint.inCheckComm(&srTransRec) != d_OK)
	{
		inCTOS_inDisconnect();
		return(d_NO);
	}

	if (srCommFuncPoint.inConnect(&srTransRec) != ST_SUCCESS)
	{
		inCTOS_inDisconnect();
		return ST_ERROR;
	}

	vdPCIDebug_HexPrintf("uszSendData", uszSendData, inSendLen);
	srCommFuncPoint.inSendData(&srTransRec,uszSendData,inSendLen);

	inReceLen = srCommFuncPoint.inRecData(&srTransRec,uszReceData);

	vdDebug_LogPrintf("inRecData=[%d]", inReceLen);
	vdPCIDebug_HexPrintf("inRecData", uszReceData, inReceLen);
	memset(&srTransRec,0x00,sizeof(TRANS_DATA_TABLE));
	if (inReceLen <= 1024)
	{
		srTransRec.usChipDataLen = inReceLen;
		memcpy(srTransRec.baChipData,uszReceData,inReceLen);
	}
	else
	{
		srTransRec.usChipDataLen = 1024;
		memcpy(srTransRec.baChipData,uszReceData,1024);

		srTransRec.usAdditionalDataLen = inReceLen-1024;
		memcpy(srTransRec.baAdditionalData,&uszReceData[1024],srTransRec.usAdditionalDataLen);
	}
	inRet = inMultiAP_Database_BatchInsert(&srTransRec);
	vdDebug_LogPrintf("inMultiAP_Database_BatchInsert=[%d]", inRet);
	if(d_OK != inRet)
	{
		vdDisplayErrorMsg(1,8,"MultiAP BatchInsert ERR");
	}
				
//	inCTOS_inDisconnect();
	CTOS_LCDTClearDisplay();
}



int inCTOSS_PassDataSend(void)
{
	ULONG tick,offset;
	USHORT ret,inRet,bret;
    unsigned char ucTemp;
	BYTE outbuf[d_MAX_IPC_BUFFER];
	USHORT out_len = 0;
	int inLen = 232;
	BYTE uszData[2048];
	BYTE uszSendData[2048] = "600004000002003020078020C0020400000000000000011100000900500002000400355264710003795113D190722100000668000F303930303030313134303130303038383838383030303101465F2A0207025F340102820238008407A0000000041010950500000080009A031501279C01009F02060000000001119F03060000000000009F090200029F10120110A08003220000E01D00000000000000FF9F1A0207029F1E0831323334353637389F26082CFD08188B5964639F2701809F3303E0B0C89F34031E03009F3501229F360201CC9F3704D7422F239F41030000010006303030303038";

	vdDebug_LogPrintf("inCTOSS_PassData");
	wub_str_2_hex(uszSendData,uszData,inLen*2);
	memset(&srTransRec,0x00,sizeof(TRANS_DATA_TABLE));
	inCPTRead(1);
	
	{
		srTransRec.usTrack1Len = strCPT.inCommunicationMode;
		srTransRec.usTrack2Len = inLen;
		strcpy(srTransRec.szTrack1Data,strCPT.szPriTxnHostIP);
		strcpy(srTransRec.szTrack2Data,strCPT.szSecTxnHostIP);
		srTransRec.ulTraceNum = strCPT.inPriTxnHostPortNum;
		srTransRec.ulOrgTraceNum = strCPT.inSecTxnHostPortNum;
		if (inLen <= 1024)
		{
			srTransRec.usChipDataLen = inLen;
			memcpy(srTransRec.baChipData,uszData,inLen);
		}
		else
		{
			srTransRec.usChipDataLen = 1024;
			memcpy(srTransRec.baChipData,uszData,1024);

			srTransRec.usAdditionalDataLen = inLen-1024;
			memcpy(srTransRec.baAdditionalData,&uszData[1024],srTransRec.usAdditionalDataLen);
		}
		
		
		inTCTSave(1);
		
		bret= inMultiAP_Database_BatchDelete();
		vdDebug_LogPrintf("inMultiAP_Database_BatchDelete,bret=[%d]", bret);
		if(d_OK != bret)
		{
			vdSetErrorMessage("MultiAP BatchDelete ERR");
			return bret;
		}
		
		bret = inMultiAP_Database_BatchInsert(&srTransRec);
		vdDebug_LogPrintf("inMultiAP_Database_BatchInsert=[%d]", bret);
		if(d_OK != bret)
		{
			vdSetErrorMessage("MultiAP BatchInsert ERR");
			return bret;
		}
	
	
		vdDebug_LogPrintf("szAPName[%s],bret=[%d]", strHDT.szAPName,bret);
		
		inMultiAP_RunIPCCmdTypes("com.Source.V5S_Mandiri.Mandiri",d_IPC_CMD_PASSDATA,"",0, outbuf,&out_len);
	
		inTCTRead(1);


		memset(&srTransRec,0x00,sizeof(TRANS_DATA_TABLE));
		offset = 0;
		memset(uszData,0x00,sizeof(uszData));
		
        inRet = inCTOS_MultiAPGetData();
		if(d_OK != inRet)
		{
			return inRet;
		}

		if (srTransRec.usAdditionalDataLen > 0)
		{
			memcpy(uszData,srTransRec.baChipData,1024);

			memcpy(&uszData[1024],srTransRec.baAdditionalData,srTransRec.usAdditionalDataLen);
			offset = 1024 + srTransRec.usAdditionalDataLen;
		}
		else
		{
			offset = srTransRec.usChipDataLen;
			memcpy(uszData,srTransRec.baChipData,offset);
		}
	}
        return d_OK;
}


int inCTOSS_SendData(void)
{
	BYTE inRet,inRetVal;
	int inResult;
	int inReceLen;
    BYTE uszReceData[2048 + 1];
	
	BYTE uszData[2048];
	BYTE uszSendData[2048];
	//BYTE uszSendData[2048] = "\x60\x00\x01\x00\x00\x50\x4F\x53\x54\x20\x2F\x74\x6D\x73\x5F\x32\x2F\x54\x4D\x53\x32\x5F\x4E\x61\x63\x5F\x43\x6F\x6E\x6E\x65\x63\x74\x2E\x70\x68\x70\x20\x48\x54\x54\x50\x2F\x31\x2E\x31\x0D\x0A\x41\x63\x63\x65\x70\x74\x3A\x20\x2A\x2F\x2A\x0D\x0A\x43\x6F\x6E\x74\x65\x6E\x74\x2D\x54\x79\x70\x65\x3A\x20\x61\x70\x70\x6C\x69\x63\x61\x74\x69\x6F\x6E\x2F\x78\x2D\x77\x77\x77\x2D\x66\x6F\x72\x6D\x2D\x75\x72\x6C\x65\x6E\x63\x6F\x64\x65\x64\x0D\x0A\x48\x6F\x73\x74\x3A\x20\x0D\x0A\x43\x6F\x6E\x74\x65\x6E\x74\x2D\x4C\x65\x6E\x67\x74\x68\x3A\x20\x35\x30\x0D\x0A\x43\x6F\x6E\x6E\x65\x63\x74\x69\x6F\x6E\x3A\x20\x4B\x65\x65\x70\x2D\x41\x6C\x69\x76\x65\x0D\x0A\x0D\x0A\x56\x49\x44\x3D\x41\x67\x3D\x3D\x26\x53\x4E\x3D\x4D\x54\x49\x7A\x4E\x44\x55\x32\x4E\x7A\x67\x35\x4D\x44\x45\x79\x4D\x7A\x51\x31\x4E\x67\x3D\x3D\x26\x54\x50\x44\x55\x3D\x41\x47\x41\x41\x41\x51\x41\x41";
	int inSendLen = 214;

	vdDebug_LogPrintf("inCTOSS_SendData");
	memset(uszData,0x00,sizeof(uszData));
	memset(uszSendData,0x00,sizeof(uszSendData));
	strcpy(uszData,"600108000002003020058000C0008500000000000000012300082700220108003433353033393031343434343335303339303120202020018548544C4530343030313931303030353033323231310011001000000A40001E01340000000000000000C2F841DDC42E65EBEEC64367D93DF342138CFF1AE8806D6FD9ECCE9672DBF2AA7C037C60AE540BA553EB6C3D5BF6658AF8E980237B620C9A1C58D8049EADC51565FA3E519655C5C90EBBA503FD06AD922596502F317454F65F8DBBF364499B4DD0775C4897BC75205C5817A8C2B95A6691A6DD5CBFAE7660DC509007B05E5F5CE036D4DD3642BCCC0D282CC2B3BE7FF800063030303030307E11A4CD3906BCA2");
	inSendLen = strlen(uszData);
	wub_str_2_hex(uszData,uszSendData,inSendLen);
	inSendLen = strlen(uszData)/2;

	memset(&srTransRec,0x00,sizeof(TRANS_DATA_TABLE));
	memset(uszReceData,0x00,sizeof(uszReceData));

	CTOS_LCDTClearDisplay();
    vdDispTitleString("Send Data");

	inCPTRead(1);
	if (inCTOS_InitComm(strCPT.inCommunicationMode) != d_OK) 
    {
        vdDisplayErrorMsg(1,8,"COMM INIT ERR");
        return(d_NO);
	}
	inRetVal = inCTOS_CheckInitComm(strCPT.inCommunicationMode); 
	if (inRetVal != d_OK)
	{
		if (strCPT.inCommunicationMode == GPRS_MODE)
			vdDisplayErrorMsg(1,8,"GPRS NOT ESTABLISHED");
		else
        vdDisplayErrorMsg(1,8,"COMM INIT ERR");
        return(d_NO);
    }
	if (srCommFuncPoint.inCheckComm(&srTransRec) != d_OK)
	{
		inCTOS_inDisconnect();
		return(d_NO);
	}

	if (srCommFuncPoint.inConnect(&srTransRec) != ST_SUCCESS)
	{
		inCTOS_inDisconnect();
		return ST_ERROR;
	}

	vdPCIDebug_HexPrintf("uszSendData", uszSendData, inSendLen);
	srCommFuncPoint.inSendData(&srTransRec,uszSendData,inSendLen);

	inReceLen = srCommFuncPoint.inRecData(&srTransRec,uszReceData);

	vdDebug_LogPrintf("inRecData=[%d]", inReceLen);
	vdPCIDebug_HexPrintf("inRecData", uszReceData, inReceLen);
				
	inCTOS_inDisconnect();
	CTOS_LCDTClearDisplay();
}


int isCheckTerminalMP200(void)
{
	if ((strTCT.byTerminalType == 5) || (strTCT.byTerminalType == 6))
		return d_OK;

	return d_NO;
}

int isCheckTerminalUPT1000(void)
{
	if ((strTCT.byTerminalType == 7) || (strTCT.byTerminalType == 8))
		return d_OK;

	return d_NO;
}


#if 1
void vdSystem_InjectKey(void)
{
	USHORT ushRet = d_OK;
	
	//ushRet = CTOS_KeyInjectionPerform(NULL, NULL);

	vdDebug_LogPrintf("CTOS_KeyInjectionPerform ushRet=%d", ushRet);
}
#endif


int Check_OverDateTime(unsigned char S_bYear,unsigned char S_bMonth,unsigned char S_bDay,unsigned char S_bHour,unsigned char S_bMinute,unsigned char S_Second)
{
    CTOS_RTC CurRTC;
    CTOS_RTCGet(&CurRTC);
    vdDebug_LogPrintf("CurRTC = [ %d:%d:%d ]",CurRTC.bHour,CurRTC.bMinute,CurRTC.bSecond);
    if((S_bYear == 0)&&(S_bMonth == 0)&&(S_bDay == 0))
    {
        return 0;
    }
    else if((CurRTC.bYear > S_bYear)||(CurRTC.bMonth > S_bMonth))
    {
        vdDebug_LogPrintf("(CurRTC.bYear = %d)(CurRTC.bMonth = %d )[1]",CurRTC.bYear,CurRTC.bMonth); 
        return 1;
    }
    else if(CurRTC.bDay > S_bDay)
    {
        vdDebug_LogPrintf("(CurRTC.bDay = %d)[2]",CurRTC.bDay); 
        return 1;
    }
    else
    {
        if(CurRTC.bHour > S_bHour)
        {
            vdDebug_LogPrintf("(CurRTC.bHour = %d)[3]",CurRTC.bHour);
            return 1;
        }
        else if((CurRTC.bHour == S_bHour)&&(CurRTC.bMinute >= S_bMinute))
        {
            vdDebug_LogPrintf("(CurRTC.bMinute = %d)[4]",CurRTC.bMinute);
            return 1;
        }
    }
    return 0;
}

void vdGetDateFromInt(int Date,CTOS_RTC *BUFFDATE)
{
    if(Date == 0)
    {
        BUFFDATE->bYear = 0;
        BUFFDATE->bMonth = 0;
        BUFFDATE->bDay = 0;        
    }
    else
    {
        BUFFDATE->bDay = (Date%100);
        BUFFDATE->bMonth = (Date/100)%100;
        BUFFDATE->bYear = (Date/10000);
    }
}

void vdGetTimeFromInt(int Time,CTOS_RTC *BUFFDATE)
{
    if(Time == 0)
    {
        BUFFDATE->bHour = 0;
        BUFFDATE->bMinute = 0;
        BUFFDATE->bSecond = 0;        
    }
    else
    {
        BUFFDATE->bHour = (Time/100)%100;
        BUFFDATE->bMinute = (Time%100);
        BUFFDATE->bSecond = 0;
    }
}


/* Analyze a string for str1,str2 */
int SplitString(char *str, char *str1, char *str2, char c)
{
    int i,j;

	for (j=0,i=0;str[i]!='\0'&&str[i]!=c;j++,i++)
		str1[j]=str[i];
	str1[j]='\0';

	if (str[i]=='\0') str2[0]='\0';
	else
	{
		for (j=0,i++;str[i]!='\0';j++,i++)
		str2[j]=str[i];
		str2[j]='\0';
	}

	return strlen(str2);
}

int inNPX_CopySecTxnHostInfo(void)
{
	inCPTRead(NPX_HOSTINDEX);
	
	strcpy(strCPT.szSecTxnHostIP, strCPT.szPriTxnHostIP);
	strCPT.inSecTxnHostPortNum = strCPT.inPriTxnHostPortNum;

	strcpy(strCPT.szPriSettlementHostIP, strCPT.szPriTxnHostIP);
	strCPT.inPriSettlementHostPort = strCPT.inPriTxnHostPortNum;
	
	strcpy(strCPT.szSecSettlementHostIP, strCPT.szPriTxnHostIP);
	strCPT.inSecSettlementHostPort = strCPT.inPriTxnHostPortNum;

	inCPTSave(NPX_HOSTINDEX);
}

void vdNPX_TermRefresh(void)
{
    int         shHostIndex = 1;
    int         inResult,inRet;
    ACCUM_REC srAccumRec;
    STRUCT_FILE_SETTING strFile;

    
    vduiLightOn();                

    srTransRec.HDTid = NPX_HOSTINDEX;
    strHDT.inHostIndex = NPX_HOSTINDEX;
    inHDTRead(NPX_HOSTINDEX);
    inCPTRead(NPX_HOSTINDEX);
    strcpy(srTransRec.szHostLabel, strHDT.szHostLabel);

    /*if (inMultiAP_CheckMainAPStatus() == d_OK)
    {
        inRet = inCTOS_MultiAPSaveData(d_IPC_CMD_DELETE_BATCH);
        if(d_OK != inRet)
            return ;
    }
    else
    {
        if (0 == fGetAppRunBySelf() && inMultiAP_CheckSubAPStatus() == d_OK)
        {
            inRet = inCTOS_MultiAPGetData();
            if(d_OK != inRet)
                return ;

            inRet = inCTOS_MultiAPReloadHost();
            if(d_OK != inRet)
                return ;
        }
    }*/

    inResult = inCTOS_CheckAndSelectMutipleMID();
    if(d_OK != inResult)
        return;
            
    //inResult = vduiAskConfirmContinue();
    //if(inResult == d_OK)
    {

        /*if(CN_TRUE == strMMT[0].fMustSettFlag)
        {
            strMMT[0].fMustSettFlag = CN_FALSE;
            inMMTSave(strMMT[0].MMTid);
        }*/
    
        // delete batch where hostid and mmtid is matcj
        inDatabase_BatchDelete();

        memset(&srAccumRec, 0x00, sizeof(ACCUM_REC));
        memset(&strFile,0,sizeof(strFile));
        vdCTOS_GetAccumName(&strFile, &srAccumRec);
    
        if((inResult = CTOS_FileDelete(strFile.szFileName)) != d_OK)
        {
            vdDebug_LogPrintf("[inMyFile_SettleRecordDelete]---Delete Record error[%04x]", inResult);
        }
        //create the accum file
		memset(&srAccumRec, 0x00, sizeof(ACCUM_REC));
    	inCTOS_ReadAccumTotal(&srAccumRec);
		
        inCTOS_DeleteBKAccumTotal(&srAccumRec,strHDT.inHostIndex,srTransRec.MITid);

        inMyFile_ReversalDelete();

        inMyFile_AdviceDelete();
        
        inMyFile_TCUploadDelete();
    
        CTOS_LCDTClearDisplay();
//        setLCDPrint(5, DISPLAY_POSITION_CENTER, "RESET NPX");
//        setLCDPrint(6, DISPLAY_POSITION_CENTER, "RECORD DONE");
        CTOS_Delay(1000); 
    }                
}


#define CURRENCY_SYMBOL         'C'
#define CURRENCY_SYMBOL_NO_PAD  'c'
#define NUMERIC_VALUE           'N'
#define NUMERIC_VALUE_NO_PAD    'n'
#define FORMATTED_AMOUNT_SIZE    	20

char formatAmt(char *into, char*amt,char *currency, char *format, VS_BOOL print)
{
    int n, writePos = 0;
    int max;
    int amtCount = strlen(amt)-1;
    int currencyCount;// = strlen(currency)-1;
    int nCount = 0;
    int addNs = 0;
    char seenANoPad = 0;
    char forcePad = 0;
    char formatstring[FORMATTED_AMOUNT_SIZE+1];
    char newAmt[FORMATTED_AMOUNT_SIZE+1];
    VS_BOOL first = VS_TRUE;

    /* Don't want to change original format because the caller uses it later */
    memset(formatstring,0x00,sizeof(formatstring));
    strcpy(formatstring,format);

    /* Don't want to change original amt because the caller uses it later */
    memset(newAmt,0x00,sizeof(newAmt));
    strcpy(newAmt,amt);

	currencyCount = strlen(currency)-1;

    for (n=0, max = strlen(formatstring); n < max; n++)
    {
	    if(formatstring[n] == NUMERIC_VALUE || formatstring[n] == NUMERIC_VALUE_NO_PAD)
	    {
	        if (first)
	        {
	            /* If this is our first n then mark it so we can */
	            /* insert before it */
	            first = VS_FALSE;
	            writePos = n;
	        }
	        /* count how many numeric digits */
	        nCount++;
	    } /* end if numeric digit */
	} /* end for all of the chars in formatstring */

	#if 0
    /* we are probably printing on a receipt or report and we want to */
    /* make sure we print all of the numeric digits */
    if ((amtCount+1) > 0 && print)
    {
		if ((amtCount+1) > nCount)
		{
		    addNs = (amtCount+1) - nCount;
		    if ( (strlen(formatstring) + addNs) <= FORMATTED_AMOUNT_SIZE)
		    {
		        for(n=addNs; n > 0; n--)
		        {
		            insert_char(formatstring, writePos, NUMERIC_VALUE_NO_PAD);
		        }
		    } /* end if we have room in the formatstring string */
		    
		} /* end if we need more room to print the numbers in the amount */
		    
    } /* if amount count is greater than zero */
	#endif


    /* If we have a negative amount don't print it next to the last digit */
    /* print it after the padding so that NNN,NNN.NN won't look like */
    /* $ 000,0-5.00     instead it will look like  $ -00,005.00  */
    if (newAmt[0] == '-')
    {
       formatstring[writePos] = '-';
       /* Don't print the negative from the amt string, print from formatstring */
       strcpy(newAmt,&newAmt[1]);
       amtCount--;
    }
    
    
    for(n=0, max = strlen(formatstring); n < max && !forcePad; n++)
    {
        if(formatstring[n] == CURRENCY_SYMBOL)
            forcePad = 1;
		if(formatstring[n] == NUMERIC_VALUE || formatstring[n] == NUMERIC_VALUE_NO_PAD)
			break;	/* No Pad Force required if currency after amount */
    }
    into[strlen(formatstring)] = '\0';
    for(writePos = n = strlen(formatstring)-1; n >= 0; n--)
    {
        switch(formatstring[n])
        {
            case CURRENCY_SYMBOL:
            case CURRENCY_SYMBOL_NO_PAD:
				if(currencyCount >= 0)
				{
					into[writePos--] = currency[currencyCount--];
				}
                break;
            case NUMERIC_VALUE:
                if(amtCount < 0)
                {
                    if(seenANoPad)
                    {
                        if(forcePad)
                            into[writePos--] = ' ';
                    }
                    else
                        into[writePos--] = '0';
                }
                else
	            {
                   into[writePos--] = newAmt[amtCount--];
                }
               break;
           case NUMERIC_VALUE_NO_PAD:
               if(amtCount < 0)
               {
                   if(forcePad)
                       into[writePos--] = ' ';
               }
               else
               {
                   into[writePos--] = newAmt[amtCount--];
               }
               seenANoPad = 1;
               break;
           case ',':
           case '.':
			   if(formatstring[n-1] == NUMERIC_VALUE_NO_PAD && amtCount < 0)
				   seenANoPad = 1;	// Shouldn't be a ',' if no character printed after it !!
               if(!seenANoPad  || (amtCount >= 0 && newAmt[amtCount] != '-'))
                   into[writePos--] = formatstring[n];
			   else
			   if(forcePad)
				   into[writePos--] = ' ';
               break;
           default:
               into[writePos--] = formatstring[n];
               break;
       }
   }
   if(writePos >= 0)
       memmove(into, &into[writePos+1], strlen(formatstring)-writePos+1);
   
   return  amtCount <= 0;	// Returns 1 if amount fully expressed
}


void vdNPX_EmptyBatch(void)
{
    int         shHostIndex = 1;
    int         inResult,inRet;
    ACCUM_REC srAccumRec;
    STRUCT_FILE_SETTING strFile;

    
//    vduiLightOn();                

    srTransRec.HDTid = NPX_HOSTINDEX;
    strHDT.inHostIndex = NPX_HOSTINDEX;
    inHDTRead(NPX_HOSTINDEX);
    inCPTRead(NPX_HOSTINDEX);
    strcpy(srTransRec.szHostLabel, strHDT.szHostLabel);

  

    inResult = inCTOS_CheckAndSelectMutipleMID();
    if(d_OK != inResult)
        return;
            

    // delete batch where hostid and mmtid is matcj
    inDatabase_BatchDelete();

    memset(&srAccumRec, 0x00, sizeof(ACCUM_REC));
    memset(&strFile,0,sizeof(strFile));
    vdCTOS_GetAccumName(&strFile, &srAccumRec);

    if((inResult = CTOS_FileDelete(strFile.szFileName)) != d_OK)
    {
        vdDebug_LogPrintf("[inMyFile_SettleRecordDelete]---Delete Record error[%04x]", inResult);
    }
    //create the accum file
	memset(&srAccumRec, 0x00, sizeof(ACCUM_REC));
	inCTOS_ReadAccumTotal(&srAccumRec);
	
    inCTOS_DeleteBKAccumTotal(&srAccumRec,strHDT.inHostIndex,srTransRec.MITid);

//    inMyFile_ReversalDelete();

//    inMyFile_AdviceDelete();
    
//    inMyFile_TCUploadDelete();

//    CTOS_LCDTClearDisplay();
/*    setLCDPrint(5, DISPLAY_POSITION_CENTER, "RESET NPX");
    setLCDPrint(6, DISPLAY_POSITION_CENTER, "RECORD DONE");*/
//    CTOS_Delay(1000); 
               
}


