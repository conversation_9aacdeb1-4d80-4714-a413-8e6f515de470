#ifndef ___POS_SETTING_H___
#define ___POS_SETTING_H___

#include "ctosapi.h"

#define STR_HEAD            0
#define STR_BOTTOM          1
#define STR_ALL             2



void vdCTOS_uiPowerOff(void);
void vdCTOS_IPConfig(void);
int inCTOS_SelectHostSetting(void);
void vdCTOS_DialConfig(void);
void vdCTOS_ModifyEdcSetting(void);
void vdCTOS_DeleteBatch(void);
void vdCTOS_DeleteReversal(void);
void vdCTOS_EditEnvParam(void);
void vdCTOS_EditEnvParamDB(void);
int get_env(char *tag, char *value, int len);
int put_env(char *tag, char *value, int len);
int get_env_int (char *tag);
void put_env_int(char *tag, int value);


void vdCTOS_PrintEMVTerminalConfig(void);
void vdCTOS_GPRSSetting(void);
void vdCTOS_Debugmode(void);
void vdCTOSS_CtlsMode(void);
void vdCTOS_TipAllowd(void);
void vdCTOS_DemoMode(void);
void DelCharInStr(char *str, char c, int flag);
void vdCTOSS_DownloadMode(void);
void vdCTOSS_CheckMemory(void);
void CTOSS_SetRTC(void);
int inCTOSS_GetCtlsMode(void);
void vdCTOSS_PrintTerminalConfig(void);
void vdCTOSS_SelectPinpadType(void);
void vdCTOSS_InjectMKKey(void);
void vdCTOS_ThemesSetting(void);

/*sidumili: Issue#:000087 [prompt password]*/
int inCTOS_PromptPassword(void);
int inCTOS_CommsFallback(int shHostIndex);
void vdCTOSS_DisplayAmount(USHORT usX, USHORT usY, char *szCurSymbol,char *szAmount);
void vdCTOSS_EditTable(void);
void vdCTOS_uiIDLEPowerOff(void);

void vdCTOS_TMSSetting(void);
void vdCTOS_TMSReSet(void);
int  inCTOS_TMSPreConfigSetting(void);
int inCTOSS_CheckBatteryChargeStatus(void);
int inCTOSS_PassData(void);
void vdCTOS_uiIDLESleepMode(void);
void vdCTOS_uiIDLEWakeUpSleepMode(void);
void vdCTOS_TMSUploadFile(void);

void vdGetDateFromInt(int Date,CTOS_RTC *BUFFDATE);
void vdGetTimeFromInt(int Time,CTOS_RTC *BUFFDATE);
int Check_OverDateTime(unsigned char S_bYear,unsigned char S_bMonth,unsigned char S_bDay,unsigned char S_bHour,unsigned char S_bMinute,unsigned char S_Second);

int isCheckTerminalMP200(void);
void vdCTOS_MdmPPPConfig(void);
int isCheckTerminalUPT1000(void);

void vdCTOS_PaymentGateway(void);
void vdNPX_TermRefresh(void);

#endif //end ___POS_SETTING_H___

