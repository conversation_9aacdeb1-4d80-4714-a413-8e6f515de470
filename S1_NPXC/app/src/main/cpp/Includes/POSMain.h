
#ifndef ___POS_MAIN_H___
#define ___POS_MAIN_H___


int inCTOS_DisplayIdleBMP(void);
int inCTOS_DisplayComTypeICO(void);
int inCTOS_ValidFirstIdleKey(void);
BYTE chGetFirstIdleKey(void);
void vdSetFirstIdleKey(BYTE bFirstKey);
int inCTOS_IdleEventProcess(void);
int inCTOSS_InitAP(void);
BYTE chGetIdleEventSC_MSR(void);
void vdSetIdleEventSC_MSR(BYTE bIdleEventSC_MSR);
int inCTOSS_CheckAutoSettlementTime(void);
int inCTOSS_JumpToSleepMode(void);
int inCTOSS_CheckAutoRestart(void);
int inNPX_CFC_PrntLastTxnSetVirtualFuncKey(void);
void vdCTOSS_KeepClearTouchPanel(void);
int inHawker_DisableVirtualFunc<PERSON>ey(void);
int inIdleRunningFunctions(void);




#endif //end ___POS_MAIN_H___

