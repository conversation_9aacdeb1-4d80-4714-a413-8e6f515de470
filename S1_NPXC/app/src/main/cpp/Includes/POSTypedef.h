/* 
 * File:   typedef.h
 * Author: sam
 *
 * Created on 2012年8月15日, 下午 11:19
 */

#ifndef TYPEDEF_H
#define    TYPEDEF_H

#ifdef    __cplusplus
extern "C" {
#endif

//#define TLE 1
//#define CVV  //CVV enable changed to TCT.txt
//#define APP_AUTO_TEST

#define NPX_DUKPT_KEYSET	0xC102
#define NPX_DUKPT_KEYINDEX	0x000A


#define PUBLIC_PATH 				"/data/data/pub/"
#define LOCAL_PATH 					"/data/data/com.Source.S1_NPX.NPX/"
#define UNFORK_AP_FILE			"unforkap.ini"

#define IN
#define OUT
#define INOUT

#define char    char
#define UCHAR   unsigned char
#define BYTE    unsigned char

#ifndef STR
    #define STR        unsigned char    
#endif


#define SHORT   short                // 2byte
#define USHORT  unsigned short       // 2byte
#define WORD    unsigned short       // 2byte
#define int     int                  // 4byte    
#define UINT    unsigned int         // 4byte
#define DWORD   unsigned int         // 4byte
#define BOOL    unsigned char        // 1byte
#define LONG    long                 // 4byte
#define ULONG   unsigned long        // 4byte
#define DOUBLE   double        		 // 8byte

//#define NMX_LIB

#define DUKPT_3DESONLINEPIN


#define TRUE     1
#define FALSE    0

#ifndef NULL
#define NULL    0
#endif

#ifndef CTOSNULL
#define CTOSNULL    0
#endif

#define RUN_SAHRE_EMV   1


#define V3_STATUS_LINE_ROW   13
#define V3_ERROR_LINE_ROW   14
#define d_WAVE_PROCESSING	0x0004


//#define MULTIAPLIB   1
#define NPX_HOSTINDEX       17



typedef unsigned char VS_BOOL;

#define MAX_AMOUNT	999999999

#define K_STATUS        BYTE

#define SUCCESS     0
#define FAIL       -1

#define VS_FALSE           ((VS_BOOL) 0)
#define VS_TRUE            ((VS_BOOL) 1)

#define MAX_CHAR_PER_LINE       40

/*Tags definition*/

#define TAG_4F_AID                    0x004F //Application Identifier (AID)
#define TAG_50                        0x0050 //Application Label
#define TAG_52                        0x0052 //Command To Perform
#define TAG_55                        0x0055 //TSignature CVM
#define TAG_57                        0x0057 //Track 2 Equivalent Data
#define TAG_5A_PAN                    0x005A //Application Primary Account Number (PAN)
#define TAG_61                        0x0061 //Application Template
#define TAG_6F                        0x006F //FCI Template
#define TAG_70                        0x0070 //Application Elementary File (AEF) Data Template
#define TAG_71                        0x0071 //Issuer Script Template 1
#define TAG_72                        0x0072 //Issuer Script Template 2
#define TAG_73                        0x0073 //Directory Discretionary Template
#define TAG_77                        0x0077 //Response Message Template Format 2
#define TAG_80                        0x0080 //Response Message Template Format 1
#define TAG_81_BIN_AMOUNT             0x0081 //Amount, Authorised (Binary)
#define TAG_82_AIP                    0x0082 //Application Interchange Profile (AIP)
#define TAG_83                        0x0083 //Command Template
#define TAG_84_DF_NAME                0x0084 //Dedicated File (DF) Name
#define TAG_86                        0x0086 //Issuer Script Command
#define TAG_87                        0x0087 //Application Priority Indicator
#define TAG_88                        0x0088 //Short File Identifier (SFI)
#define TAG_8A_AUTH_CODE              0x008A //Authorisation Response Code
#define TAG_8C                        0x008C //Card Risk Management Data 1 (CDOL1)
#define TAG_8D                        0x008D //Card Risk Management Data 2 (CDOL2)
#define TAG_8E                        0x008E //Cardholder Verification Method (CVM) List
#define TAG_8F                        0x008F //Certification Authority Public Key Index (ICC)
#define TAG_90                        0x0090 //Issuer Public Key Certificate
#define TAG_91_ARPC                   0x0091 //Issuer Authentication Data
#define TAG_92                        0x0092 //Issuer Public Key Remainder
#define TAG_93                        0x0093 //Signed Application Data
#define TAG_94                        0x0094 //Application File Locator (AFL)
#define TAG_95                        0x0095 //Terminal Verification Results
#define TAG_97_TDOL                   0x0097 //Transaction Certificate Data Object List (TDOL)
#define TAG_98                        0x0098 //Transaction Certificate (TC) Hash Value
#define TAG_99                        0x0099 //Transaction PIN Data
#define TAG_9A_TRANS_DATE             0x009A //Transaction Date
#define TAG_9B                        0x009B //Transaction Status Information
#define TAG_9C_TRANS_TYPE             0x009C //Transaction Type
#define TAG_9D                        0x009D //Directory Definition File (DDF) Name
                                      
                                      
#define TAG_5F20                      0x5F20 //Cardholder Name
#define TAG_5F24_EXPIRE_DATE          0x5F24 //Application Expiration Date
#define TAG_5F25                      0x5F25 //Application Effective Date
#define TAG_5F28                      0x5F28 //Issuer Country Code
#define TAG_5F2A_TRANS_CURRENCY_CODE  0x5F2A //Transaction Currency Code
#define TAG_5F2D                      0x5F2D //Language Preference
#define TAG_5F30_SERVICE_CODE         0x5F30 //Service Code
#define TAG_5F34_PAN_IDENTFY_NO       0x5F34 //Application PAN Sequence Number
#define TAG_5F36                      0x5F36 //Transaction Currency Exponent
                                      
                                      
#define TAG_9F01                      0x9F01 //Acquirer Identifier
#define TAG_9F02_AUTH_AMOUNT          0x9F02 //Amount, Authorised (Numeric)
#define TAG_9F03_OTHER_AMOUNT         0x9F03 //Amount, Other (Numeric)
#define TAG_9F04                      0x9F04 //Amount, Other (Binary)
#define TAG_9F05                      0x9F05 //Application Discretionary Data
#define TAG_9F06                      0x9F06 //Application Identifier (AID) (Terminal)
#define TAG_9F07                      0x9F07 //Application Usage Control
#define TAG_9F08_IC_VER_NUMBER        0x9F08 //Application Version Number
#define TAG_9F09_TERM_VER_NUMBER      0x9F09 //Application Version Number (Terminal)
#define TAG_9F0B                      0x9F0B //Cardholder Name Extended
#define TAG_9F0D                      0x9F0D //Issuer Action Code - Default
#define TAG_9F0E                      0x9F0E //Issuer Action Code - Denial
#define TAG_9F0F                      0x9F0F //Issuer Action Code - Online
                                      
#define TAG_9F10_IAP                  0x9F10 //Issuer Application Data
#define TAG_9F11                      0x9F11 //Issuer Code Table Index
#define TAG_9F12                      0x9F12 //Application Preferred Name
#define TAG_9F13                      0x9F13 //Last Online ATC Register
#define TAG_9F14                      0x9F14 //Lower Consecutive Offline Limit (EMV Generic)
#define TAG_9F15                      0x9F15 //Merchant Category Code
#define TAG_9F16                      0x9F16 //Merchant Identifier
#define TAG_9F17                      0x9F17 //PIN Try Counter
#define TAG_9F18                      0x9F18 //Issuer Script Identifier
#define TAG_9F1A_TERM_COUNTRY_CODE    0x9F1A //Terminal Country Code
#define TAG_9F1B_TERM_FLOOR_LIMIT     0x9F1B //Terminal Floor Limit
#define TAG_9F1C_TID                  0x9F1C //Terminal Identification
#define TAG_9F1D                      0x9F1D //Terminal Risk Management Data
#define TAG_9F1E                      0x9F1E //Interface Device Serial Number
#define TAG_9F1F                      0x9F1F //Track 1 Discretionary Data
                                      
#define TAG_9F20                      0x9F20 //Track 2 Discretionary Data
#define TAG_9F21_TRANS_TIME           0x9F21 //Transaction Time
#define TAG_9F22                      0x9F22 //Certification Authority Public Key Index (Terminal)
#define TAG_9F23                      0x9F23 //Upper Consecutive Offline Limit (EMV Generic)
#define TAG_9F26_EMV_AC               0x9F26 //Application Cryptogram (AC)
#define TAG_9F27                      0x9F27 //Cryptogram Information Data (CID)
#define TAG_9F2D                      0x9F2D //ICC PIN Encipherment Public Key Certificate
#define TAG_9F2E                      0x9F2E //ICC PIN Encipherment Public Key Exponent
#define TAG_9F2F                      0x9F2F //ICC PIN Encipherment Public Key Remainder
                                      
#define TAG_9F32                      0x9F32 //Issuer Public Key Exponent
#define TAG_9F33_TERM_CAB             0x9F33 //Terminal Capabilities
#define TAG_9F34_CVM                  0x9F34 //Cardholder Verification Method (CVM) Results
#define TAG_9F35_TERM_TYPE            0x9F35 //Terminal Type
#define TAG_9F36_ATC                  0x9F36 //Application Transaction Counter (ATC)
#define TAG_9F37_UNPREDICT_NUM        0x9F37 //Unpredictable Number
#define TAG_9F38                      0x9F38 //Processing Options Data Object List (PDOL)
#define TAG_9F39                      0x9F39 //POS Entry Mode
#define TAG_9F3A_AMOUNT_REF_CURRENCY  0x9F3A //Amount, Reference Currency
#define TAG_9F3B                      0x9F3B //Application Reference Currency
#define TAG_9F3C_TRANS_REF_CURRENCY   0x9F3C //Transaction Reference Currency Code
#define TAG_9F3D                      0x9F3D //Transaction Reference Currency Exponent
                                      
#define TAG_9F40_ADD_TERM_CAB         0x9F40 //Additional Terminal Capabilities
#define TAG_9F41                      0x9F41 //Transaction Sequence Counter
#define TAG_9F42_APP_CURRENCY_CODE    0x9F42 //Application Currency Code
#define TAG_9F43                      0x9F43 //Application Reference Currency Exponent
#define TAG_9F44                      0x9F44 //Application Currency Exponent
#define TAG_9F45                      0x9F45 //Data Authentication Code
#define TAG_9F46                      0x9F46 //ICC Public Key Certificate
#define TAG_9F47                      0x9F47 //ICC Public Key Exponent
#define TAG_9F48                      0x9F48 //ICC Public Key Remainder
#define TAG_9F49_DDOL                 0x9F49 //Dynamic Data Authentication Data (DDOL)
#define TAG_9F4A                      0x9F4A //Static Data Authentication Tag List
#define TAG_9F4B                      0x9F4B //Signed Dynamic Application Data
#define TAG_9F4C                      0x9F4C //ICC Dynamic Number
                                      
#define TAG_9F52                      0x9F52 //Application Default Action
#define TAG_9F53                      0x9F53 //Transaction Category Code
#define TAG_9F58                      0x9F58 //Lower Consecutive Offline Limit (Proprietary)
#define TAG_9F59                      0x9F59 //Upper Consecutive Offline Limit (Proprietary)
#define TAG_9F5B                      0x9F5B //POS_ISS_SCRIPT_RESULT

#define TAG_9F66_TTQ                  0x9F66 // TTQ

#define TAG_9F6E                      0x9F6E //Enhanced Contactless Reader Capabilities
    
#define TAG_9F72                      0x9F72 //Consecutive Transaction Limit (international - country)
#define TAG_9F73                      0x9F73 //Currency Conversion Factor
#define TAG_9F75                      0x9F75 //Cumulative Total Transaction Amount Limit - Dual Currency
#define TAG_9F76                      0x9F76 //Secondary Application Currency Code
#define TAG_9F7F                      0x9F7F //Card Product Life Cycle History File Identifiers

#define TAG_DF31_ISR                  0xDF31//issuer script result    

/*definition of EMV status*/
#define EMV_TRANS_FAILED     101
#define EMV_CHIP_FAILED      102
#define EMV_USER_ABORT       103
#define EMV_CRITICAL_ERROR   104
#define EMV_NO_AP_FOUND      105
#define EMV_POOL_FULL        106
#define EMV_FALLBACK         107

#define EMV_CARD_BLOCKED 108 //VISA: Testcase 29 - should display "CARD BLOCKED" instead of doing fallback -- jzg
#define EMV_CHIP_NOT_DETECTED 109 //EMV - should display "CHIP NOT DETECTED" instead of doing fallback -- jzg
#define EMV_TRANS_NOT_ALLOWED 110 //EMV - If AID not found display "TRANS NOT ALLOWED" -- jzg


/*EMV chip trans result*/
#define EMV_OFFLINE_APPROVED_TC    0x0001
#define EMV_OFFLINE_DECLINE_AAC    0x0002
#define EMV_ONLINE_ARQC            0x0003
#define EMV_ONLINE_AAR             0x0004

    
#define AID_MAX                    22

#define EMV_VISA_CARD              1
#define EMV_MASTER_CARD            2
#define EMV_JCB_CARD               3
#define EMV_AMEX_CARD              4    



/* Transaction Code */
#define SALE         101
#define PRE_AUTH     102
#define PRE_COMP     103
#define TC_UPLOAD    104
#define SEND_ADVICE  105
#define REVERSAL     106
#define REFUND       107
#define VOID         108
#define SALE_TIP     109
#define SALE_OFFLINE 110
#define SETTLE       111
#define CLS_BATCH    112
#define BATCH_UPLOAD 113
#define SALE_ADJUST  114
#define SIGN_ON      115


#define REPRINT_ANY      117
#define BATCH_REVIEW    118
#define BATCH_TOTAL     119
#define VOID_REFUND         120
#define OFFLINE_VOID        121
#define OFFLINE_REFUND      122
#define VOID_REVERSAL       123
#define REFUND_REVERSAL     124
#define PREAUTH_REVERSAL    125
#define VOIDREFUND_REVERSAL 126
//SAMA project
#define PURCHASE_CASHBACK	127
#define PURCHASE_ADVICE		128
#define CASH_ADVANCE		129
#define SALE_OFFLINE_FRM_ONLINE 	   202

#define LAST_SETTLE       	130

#define TOTAL     140

#define AUTHORIZATION	133
#define TRANSINQUIRY	134

#define EPP_SALE     135
#define EPP_VOID     136
#define EPP_LIST     137

#define NW_TEST			138

#define DETAIL_REPORT	139

#define EFTSEC_TWK 			150
#define EFTSEC_TMK 			151

#define EFTSEC_TWK_RSA 		152
#define EFTSEC_TMK_RSA 		153

#define SETUP				15 //aaa issue#87 No password protected for setup menu

/* To determine the type of results */
#define CN_FALSE   0
#define CN_TRUE    1

/* Results of the state */
#define ST_SUCCESS          0
#define ST_ERROR            (-1)
#define ST_BUILD_DATD_ERR   (-2)
#define ST_SEND_DATA_ERR    (-3)
#define ST_UNPACK_DATA_ERR  (-4)
#define ST_RESP_MATCH_ERR  (-5)
#define ST_CLS_BATCH_ERR (-6)



/* Values for chip status flag */
#define NOT_USING_CHIP       0
#define EMV_CARD             1
#define EMV_EASY_ENTRY_CARD  2
#define EMV_TABLE_NOT_USED  -1

/* The status of the transaction */
#define TRANS_AUTHORIZED    99
#define TRANS_COMM_ERROR    98
#define TRANS_CALL_BANK     97
#define TRANS_CANCELLED     96
#define TRANS_REJECTED      95
#define TRANS_TERMINATE     94

/* Commuction mode */
#define DIAL_UP_MODE        0
#define ETHERNET_MODE       1
#define GPRS_MODE           2
#define MDM_PPP_MODE		3
#define WIFI_MODE			4
#define USB_MODE           	5
#define COM1_MODE           6//for COM&USB communication
#define COM2_MODE           7
#define FORWARD_MODE        8
#define NULL_MODE           9
#define BT_MODE	           10






#define STORE_ID_DIGITS            18
#define TRACK_I_BYTES              85	//85
#define TRACK_II_BYTES             41	//41
#define TRACK_III_BYTES            200	//64
#define CVV2_BYTES                 6
#define CARD_HOLD_NAME_DIGITS      30
#define AUTH_CODE_DIGITS           6
#define PAN_SIZE                   19
#define RRN_BYTES                  12
#define RESP_CODE_SIZE             2
#define TERMINAL_ID_BYTES          8
#define MERCHANT_ID_BYTES          15
#define CHIP_DATA_LEN              1024
#define ADD_DATA_LEN             1024


/*kobe added >>>*/
#define TRACE_NO_ASC_SIZE   6
#define TRACE_NO_BCD_SIZE   ((TRACE_NO_ASC_SIZE+1)/2)

#define BATCH_NO_ASC_SIZE   6
#define BATCH_NO_BCD_SIZE   ((BATCH_NO_ASC_SIZE+1)/2)

#define	EMV_TAC_SIZE            6
#define	EMV_MAX_TDOL_SIZE	    65
#define	EMV_MAX_DDOL_SIZE	    65
#define	EMV_COUNTRY_CODE_SIZE	3
#define	EMV_CURRENCY_CODE_SIZE	3
#define	EMV_TERM_CAPABILITIES_BCD_SIZE	4
#define EMV_ADD_TERM_CAPABILITIES_BCD_SIZE  6
#define	EMV_TERM_TYPE_SIZE	3
#define	EMV_MERCH_CAT_CODE_SIZE	5
#define EMV_TERM_CAT_CODE_SIZE	3
#define EMV_STRING_SIZE	25
#define TEL_DIGITS 		24				/* BCD   */
#define PABX_BYTES		4
#define PABX_DIGITS		8
#define NII_BYTES               4   //Meena 3 incresed to 4 as strNII size is 4
#define TPDU_BYTES              10   //Meena 3 incresed to 4 as strNII size is 4

#define DF_BATCH_APPEND 0
#define DF_BATCH_UPDATE 1


#define TPDU_ASC_SIZE              10
#define TPDU_BCD_SIZE           ((TPDU_ASC_SIZE+1)/2)

#define PRO_CODE_ASC_SIZE       6
#define PRO_CODE_BCD_SIZE       ((PRO_CODE_ASC_SIZE+1)/2)

#define MTI_ASC_SIZE            4
#define MTI_BCD_SIZE            ((MTI_ASC_SIZE+1)/2)

#define INVOICE_ASC_SIZE        6
#define INVOICE_BCD_SIZE        ((INVOICE_ASC_SIZE+1)/2)

#define DATE_ASC_SIZE                     4
#define DATE_BCD_SIZE                      ((DATE_ASC_SIZE+1)/2)

#define TIME_ASC_SIZE                     6
#define TIME_BCD_SIZE                      ((TIME_ASC_SIZE+1)/2)

#define EXPIRY_DATE_ASC_SIZE              4
#define EXPIRY_DATE_BCD_SIZE               ((EXPIRY_DATE_ASC_SIZE+1)/2)

#define AMT_ASC_SIZE                      12/* BCD - includes cents */
#define AMT_BCD_SIZE                       ((AMT_ASC_SIZE+1)/2)

#define GET_CARD_DATA_TIMEOUT_VALUE  60*100 //60 seconds
#define UI_TIMEOUT                   30*100

#define CARD_ENTRY_MSR                  1
#define CARD_ENTRY_MANUAL               2
#define CARD_ENTRY_ICC                  3
#define CARD_ENTRY_FALLBACK             4
#define CARD_ENTRY_WAVE                 5
#define CARD_ENTRY_EASY_ICC         	6

    
#define READ_CARD_TIMEOUT              -1001
#define USER_ABORT                     -1002
#define INVALID_CARD                   -1003
#define PLS_INSERT_CARD                -1004
#define MSR_NOT_ALLOW                  -1005
#define MANUAL_NOT_ALLOW               -1006
#define HOST_NOT_OPEN                  -1007
#define RECORD_INVALID                 -1008
#define CARD_EXPIRED                   -1009
#define TIME_OUT                       -1010
#define MODEM_FAILED                   -1011

#define CDT_AMEX                    1
#define CDT_VISA                    2
#define CDT_MASTER                  3
#define CDT_VISA1                   4
#define CDT_MASTER1                 5
#define CDT_VISA2                   6  // To include multiple host for same card type - Meena 03/01/13 
#define CDT_MASTER2                 7  // To include multiple host for same card type - Meena 03/01/13
    
#define SIGN_ON_HOST                0
#define MAIN_HOST                   1
#define MOTO_HOST                   3

#define PWDTYPE_TERMINAL	1
#define PWDTYPE_SETTLEMENT	2
#define PWDTYPE_VOID		3
#define PWDTYPE_TLE		4
    
#define d_MEGSTRIPE_SIZE   128
    
#define INTERACTTIMEOUT 3000 
#define DECIMAL_POINT_MODERATOR 10000    
    
#define GPRS_TIMEOUT_DISABLE	0
#define GPRS_TIMEOUT_ENABLE_VALUE  1500    


typedef struct
{
    BYTE byCardTypeNum;
    BYTE byEMVTransStatus;
    BYTE T5A_len;    // PAN_len
    BYTE T5A[10];    // PAN    
    BYTE T5F24[3];        // Application Expiration Date
    BYTE T5F2A[2];    // Currency Code
    BYTE T5F30[3];        // Service code.
    BYTE T5F34;        // PAN SEQ No.
    BYTE T5F34_len;        // PAN SEQ No len.
    BYTE T82[2];        // Application Interchange Profile (AIP)
    BYTE T84_len;    // AID Len
    BYTE T84[16];    // AID
    BYTE T8A[2];
    BYTE T91[16];    // Issuer Application data (IAD)
    BYTE T91Len;     //IAD length
    BYTE T95[5];        // Terminal Verification Results
    BYTE T9A[3];        // Transaction Date
    BYTE T9C;        // Trans Type
    BYTE T9F02[6];    // Amount, Authorised (Numeric)
    BYTE T9F03[6];    // Amount, others (Numeric)
    BYTE T9F06[16];    // Application Identifier (AID) ?C terminal
    BYTE T9F06_len;
    BYTE T9F09[2];    // ICC Application Version No.
    BYTE T9F0D[5];    // Issuer Action Code ?C Default
    BYTE T9F0E[5];    // IIssuer Action Code ?C Denial
    BYTE T9F0F[5];    // Issuer Action Code ?C Online
    BYTE T9F10_len;
    BYTE T9F10[32];    // Issuer Application data
    BYTE T9F1A[2];    // Terminal Country Code
    BYTE T9F26[8];    // Application Cryptogram (AC)
    BYTE T9F27;        // Cryptogram Information Data (CID)
    BYTE T9F33[3];    // Terminal Capabilities
    BYTE T9F34[3];    // Cardholder Verification Method (CVM) Results
    BYTE T9F35;        // Terminal Type
    BYTE T9F36_len;
    BYTE T9F36[2];    // Application Transaction Counter (ATC)
    BYTE T9F37[4];    // Unpredictable Number, (random number)
    BYTE T9F41[3];    // Original Trace No (Field 11)
    BYTE T9F42[2];    // applciation currency code
    BYTE T9F53;        // TCC,
    BYTE ISR[16];        // Issuer Script Result.
    BYTE ISRLen;        // Issuer Script Result.
    BYTE T9B[2];        // Transaction Status Information
      
    BYTE T71Len;    // Application Interchange Profile (AIP)
    BYTE T71[258];    // Application Interchange Profile (AIP)
    BYTE T72Len;    // Application Interchange Profile (AIP)
    BYTE T72[258];    // Application Interchange Profile (AIP)
    BYTE T9F1E[8];  // terminal hardware serial number
    BYTE T9F28[8];  // ARQC
    BYTE T9F29[8];  // TC
    BYTE szChipLabel[32];  // AP label    
    BYTE T9F66[4];  // TTQ 
}emvinfo_t;


typedef struct
{
    BYTE        byTransType;
    BYTE        byPanLen;
    BYTE        szExpireDate[EXPIRY_DATE_BCD_SIZE+1];    /* BCD YYMM        */
    BYTE        byEntryMode;
    BYTE        szTotalAmount[AMT_BCD_SIZE+1];   /* BCD total   amount    */
    BYTE        szBaseAmount[AMT_BCD_SIZE+1];
    BYTE        szTipAmount[AMT_BCD_SIZE+1];
    BYTE        szTID[TERMINAL_ID_BYTES+1];
    BYTE        szMID[MERCHANT_ID_BYTES+1];
    BYTE        szHostLabel[16];
    BYTE        szBatchNo[BATCH_NO_BCD_SIZE+1];
    BYTE        byOrgTransType ;
    BYTE        szMacBlock[8] ;
    BYTE        szYear[2];                 /* int 1997=97,2003=103    */
    BYTE        szDate[DATE_BCD_SIZE+1];     /* BCD MMDD        */
    BYTE        szTime[TIME_BCD_SIZE+1];     /* BCD hhmmss        */
    BYTE        szOrgDate[DATE_BCD_SIZE+1];     /* BCD MMDD        */
    BYTE        szOrgTime[TIME_BCD_SIZE+1];     /* BCD hhmmss        */
    BYTE        szAuthCode[AUTH_CODE_DIGITS+1];
    BYTE        szRRN[RRN_BYTES+1];
    BYTE        szInvoiceNo[INVOICE_BCD_SIZE+1];
    BYTE        szOrgInvoiceNo[INVOICE_BCD_SIZE+1];
    BYTE        byPrintType;//for trans comms dailbakup
    BYTE        byVoided;
    BYTE        byAdjusted;
    BYTE        byUploaded;
    BYTE        byTCuploaded;    /* For upload TC*/
    BYTE        szCardholderName[CARD_HOLD_NAME_DIGITS+1];
    BYTE        szzAMEX4DBC[4+1];
    BYTE        szStoreID[STORE_ID_DIGITS+1]; //use this one to store how much amount fill up in DE4 for VOID
    BYTE        szRespCode[RESP_CODE_SIZE+1];
    BYTE        szServiceCode[3+1];
    BYTE        byContinueTrans;
    BYTE        byOffline;
    BYTE        byReversal;
    BYTE        byEMVFallBack;
    SHORT       shTransResult;
    BYTE        szTpdu[TPDU_ASC_SIZE+1];
    BYTE        szIsoField03[PRO_CODE_BCD_SIZE+1];
    BYTE        szMassageType[MTI_BCD_SIZE+1];
    BYTE        szPAN[PAN_SIZE+1];
    BYTE        szCardLable[20+1];
    USHORT      usTrack1Len;
    USHORT      usTrack2Len;
    USHORT      usTrack3Len;
    BYTE        szTrack1Data[TRACK_I_BYTES+1];    
    BYTE        szTrack2Data[TRACK_II_BYTES+1];
    BYTE        szTrack3Data[TRACK_III_BYTES+1];
	USHORT      usChipDataLen;		//Chip Data
	BYTE        baChipData[CHIP_DATA_LEN+1];
	USHORT      usAdditionalDataLen;	//Additional Data
	BYTE        baAdditionalData[ADD_DATA_LEN+1];
	BYTE		bWaveSID;
	USHORT		usWaveSTransResult;	// Transction Result
	BYTE		bWaveSCVMAnalysis;
    ULONG       ulTraceNum; 
    ULONG       ulOrgTraceNum;
    USHORT      usTerminalCommunicationMode;
    BYTE        IITid;
    BYTE        HDTid; //kobe added
    ULONG       ulSavedIndex;// kobea added  
    BYTE        byPINEntryCapability;
    BYTE        byPackType;
	int  		inCardType;
	UINT 		MITid;
    BYTE        szOrgAmount[AMT_BCD_SIZE+1];
    BYTE        szCVV2[CVV2_BYTES+1];
    BYTE        byTCFailUpCnt;//20121204
    BYTE		szPINBlock[8+1];
	BYTE		szKSN[20+1];
    SHORT  		CDTid;
    emvinfo_t   stEMVinfo;
}TRANS_DATA_TABLE;

TRANS_DATA_TABLE srTransRec;

#define FLEXI_BATCH_MAX_LEN     9999

typedef struct
{
    BYTE        HDTid; 
    UINT 		MITid;
    BYTE        szHostLabel[16];
    BYTE        szBatchNo[BATCH_NO_BCD_SIZE+1];
    BYTE        szInvoiceNo[INVOICE_BCD_SIZE+1];
    int         inFlexiDataLen;
    BYTE        szFlexiData[FLEXI_BATCH_MAX_LEN];
    ULONG       ulSavedIndex;
	ULONG		ulTraceNum;
	BYTE		szECN[12 + 1]; // For NETS ECR
	BYTE		szTxnID[10 + 1]; // NPX transaction ID. easy for searching.
}TRANS_FLEXI_DATA_TABLE;


TRANS_FLEXI_DATA_TABLE srTransFlexiData;

#define FLEXI_TRAANSACTION_ID       0xA001
#define FLEXI_F102_SIZE       		0xA002
#define FLEXI_F102_VALUE       		0xA003
#define FLEXI_F103_SIZE       		0xA004
#define FLEXI_F103_VALUE       		0xA005

#define FLEXI_F104_VALUE       		0xA006
#define FLEXI_F111_VALUE       		0xA007
#define FLEXI_F112_VALUE       		0xA008
#define FLEXI_F113_VALUE       		0xA009
#define FLEXI_F114_VALUE       		0xA00A

#define FLEXI_F116_SIZE       		0xA00B
#define FLEXI_F116_VALUE       		0xA00C

#define FLEXI_F117_VALUE       		0xA00D
#define FLEXI_F119_VALUE       		0xA00E
#define FLEXI_F121_VALUE       		0xA00F
#define FLEXI_F122_VALUE       		0xA010
#define FLEXI_F123_VALUE       		0xA011

#define FLEXI_F200_VALUE       		0xA012
#define FLEXI_F201_SIZE       		0xA013
#define FLEXI_F201_VALUE       		0xA014
#define FLEXI_F202_SIZE       		0xA015
#define FLEXI_F202_VALUE       		0xA016
#define FLEXI_F203_SIZE       		0xA017
#define FLEXI_F203_VALUE       		0xA018
#define FLEXI_F204_SIZE       		0xA019
#define FLEXI_F204_VALUE       		0xA01A
#define FLEXI_F205_SIZE       		0xA01B
#define FLEXI_F205_VALUE       		0xA01C
#define FLEXI_F206_SIZE       		0xA01D
#define FLEXI_F206_VALUE       		0xA01E
#define FLEXI_F207_SIZE       		0xA01F
#define FLEXI_F207_VALUE       		0xA020
#define FLEXI_F208_SIZE       		0xA021
#define FLEXI_F208_VALUE       		0xA022
#define FLEXI_F209_SIZE       		0xA023
#define FLEXI_F209_VALUE       		0xA024
#define FLEXI_F210_SIZE       		0xA025
#define FLEXI_F210_VALUE       		0xA026
#define FLEXI_F211_SIZE       		0xA027
#define FLEXI_F211_VALUE       		0xA028
#define FLEXI_F212_SIZE       		0xA029
#define FLEXI_F212_VALUE       		0xA02A
#define FLEXI_F213_SIZE       		0xA02B
#define FLEXI_F213_VALUE       		0xA02C
#define FLEXI_F214_SIZE       		0xA02D
#define FLEXI_F214_VALUE       		0xA02E

#define FLEXI_F215_VALUE       		0xA02F
#define FLEXI_F216_VALUE       		0xA030

#define FLEXI_F998_SIZE       		0xA031
#define FLEXI_F998_VALUE       		0xA032

#define FLEXI_F999_VALUE       		0xA033

#define FLEXI_EXP_DATE				0xA034

#define FLEXI_F300_SIZE       		0xA035
#define FLEXI_F300_VALUE       		0xA036
#define FLEXI_F301_SIZE       		0xA037
#define FLEXI_F301_VALUE       		0xA038

#define FLEXI_F601_SIZE       		0xA039
#define FLEXI_F601_VALUE       		0xA03A
#define FLEXI_F602_SIZE       		0xA03B
#define FLEXI_F602_VALUE       		0xA03C
#define FLEXI_F603_SIZE       		0xA03D
#define FLEXI_F603_VALUE       		0xA03E
#define FLEXI_F604_SIZE       		0xA03F
#define FLEXI_F604_VALUE       		0xA040
#define FLEXI_F605_SIZE       		0xA041
#define FLEXI_F605_VALUE       		0xA042

#define FLEXI_F304_SIZE       		0xA043
#define FLEXI_F304_VALUE       		0xA044
#define FLEXI_F305_SIZE       		0xA045
#define FLEXI_F305_VALUE       		0xA046
#define FLEXI_F306_SIZE       		0xA047
#define FLEXI_F306_VALUE       		0xA048
#define FLEXI_F307_SIZE       		0xA049
#define FLEXI_F307_VALUE       		0xA04A
#define FLEXI_F308_SIZE       		0xA04B
#define FLEXI_F308_VALUE       		0xA04C
#define FLEXI_F309_SIZE       		0xA04D
#define FLEXI_F309_VALUE       		0xA04E
#define FLEXI_F310_SIZE       		0xA04F
#define FLEXI_F310_VALUE       		0xA050
#define FLEXI_F311_SIZE       		0xA051
#define FLEXI_F311_VALUE       		0xA052
#define FLEXI_F312_SIZE       		0xA053
#define FLEXI_F312_VALUE       		0xA054
#define FLEXI_F313_SIZE       		0xA055
#define FLEXI_F313_VALUE       		0xA056
#define FLEXI_F314_SIZE       		0xA057
#define FLEXI_F314_VALUE       		0xA058
#define FLEXI_F315_SIZE       		0xA059
#define FLEXI_F315_VALUE       		0xA05A
#define FLEXI_F316_SIZE       		0xA060
#define FLEXI_F316_VALUE       		0xA061
#define FLEXI_F317_VALUE       		0xA062

#define FLEXI_F500_SIZE       		0xA070
#define FLEXI_F500_VALUE       		0xA071
#define FLEXI_F511_SIZE       		0xA072
#define FLEXI_F511_VALUE       		0xA073
#define FLEXI_F512_SIZE       		0xA074
#define FLEXI_F512_VALUE       		0xA075
#define FLEXI_F513_SIZE       		0xA076
#define FLEXI_F513_VALUE       		0xA077
#define FLEXI_F599_SIZE       		0xA078
#define FLEXI_F599_VALUE       		0xA079

//ctls emv
#define FLEXI_TAG_9F6E_SIZE			0xA100
#define FLEXI_TAG_9F6E_VALUE		0xA101

#define FID_NETS_STORE_INV_NUM		0xA102

#define FLEXI_TXNINQ_PAN			0xA103
#define FLEXI_TXNINQ_AUTHCODE		0xA104

#define FLEXI_PAYMENT_INFO			0xA201
#define FLEXI_PAYMENT_ADDIONAL_INFO		0xA202



/*use for EMV tag 9F53*/
#define baEMVBackupT9F53 srTransRec.szzAMEX4DBC

#define RGB(r,g,b) (unsigned long)( ((DWORD)(BYTE)r&0xff)|((DWORD)((BYTE)g&0xff)<<8)|((DWORD)((BYTE)b&0xff)<<16) )

/* <<< end*/

//#define _TRANS_CONNECT_USE_RS232_

//=============================================================================================================================
//
// Virtual Functions
// for V3 touch panel
//
//=============================================================================================================================
/*
typedef struct
{
    USHORT LeftTopX;
    USHORT LeftTopY;
    USHORT RightBottomX;
    USHORT RightBottomY;
}CTOS_VIRTUAL_FUNCTION_KEY_SET_PARA;

USHORT CTOS_VirtualFunctionKeySet(IN CTOS_VIRTUAL_FUNCTION_KEY_SET_PARA* pPara, IN BYTE FuncKeyCount);
*/

/*==================================================*
 * 	Modem PPP Definition Table(MPT) Struct          *
 *==================================================*/
typedef struct
{
	int		HDTid ;
	BYTE	szHostName[50];	/*  */
	int		inCommunicationMode;
	BYTE	szPriISPPhoneNumber[30];	/* ASCII string */
	BYTE	szSecISPPhoneNumber[30];	/* ASCII string */
	
	int 	inHandShake;
	int 	inParaMode;
	int 	inCountryCode;
	
	int		inDialHandShake;	/* ASCII string */
	int		inMCarrierTimeOut;	/* ASCII string */
    int   	inMRespTimeOut;

	BYTE	szUserName[30] ;
    BYTE	szPassword[30] ;
	int 	inTxRxBlkSize;
	
} STRUCT_MPT;
STRUCT_MPT      strMPT;


typedef struct
{
	TRANS_DATA_TABLE srTransRec;
	TRANS_FLEXI_DATA_TABLE srTransFlexiData;
}TRANS_ALL_DATA_TABLE;


#define HAWKER_CFC

#define SPACE 		0x20
#define ISZERO      0x30


/* CDG Comfort DelGro Taxi*/

typedef struct TagCDG_TRANS
{
	unsigned char ucTransactionType;
	unsigned char ucPaymentMode;
	unsigned char ucEntryMode;
	unsigned char szCardAbbrev[2];
	unsigned char szCardNumber[19];
	unsigned char szCardHolderName[26];
	unsigned char szBCDExpiryDate[3];	// ddmmyy, 00 for day if not available
	
	unsigned char szTaxiNumber[12];
	unsigned char szDriverID[9];
	unsigned char szFareAmt[6];
	unsigned char szAdminAmt[6];
	unsigned char szGSTAmt[6];
	unsigned char szJobNumber[10];
	unsigned char szCompanyCode[6];
	unsigned char szTotalFare[12];
	unsigned char szMID[15];
	unsigned char szTID[8];

	unsigned char	ucTransState;
	unsigned char ucCompletionMode;
	unsigned char szBCDSTAN[3];
	unsigned char szBCDRRN[6];
	unsigned char szRespCode[2];
	unsigned char szBCDForeignAmt[6];
	unsigned char szAccountType[3];
	unsigned char szAuthCode[6];
	
	unsigned char szBCDTransDate[2];	//mmdd
	unsigned char szBCDTransTime[3];	//hhmmss
	unsigned char szApplicationName[16];

	unsigned char szAID[15];
	unsigned char szTVR[5];
	unsigned char szAppCrypt[8];
	unsigned char szPurseCounter[8];
	unsigned char szBCDPurseBalance[3];		// ezlink & flashpay
	unsigned char szBCDPrevPurseBalance[3];	// ezlink & flashpay
	unsigned char szBCDPrepaidBalance[6];
	unsigned char ucAgentID[2];					// for ezlink only

	unsigned char ucVoidAllowed;

	unsigned char szForeignMID[15];

	unsigned char szBCDAppVer[3];
	unsigned char szBCDParamVer[3];

	unsigned char ucBookingJob;
	unsigned char ucAdminGSTMessage;	// 0=No Admin&GST, 1=FEE(INCL GST), 2=GST(ON ADMIN), 3=SUBJECT TO ADMIN & GST 
	unsigned char szCardBrand[20];

	unsigned char szPromoDesc[24];
	unsigned char szDiscountAmt[10];
	unsigned char szFinalAmt[10];
	unsigned char ucPaymentSource;
	unsigned char szRFU1[20];

	unsigned char ucUseCN2PaymentMode;
	unsigned char szTripInformation[200];
	unsigned char szPaymentInfo[20];

	unsigned char szPrivateField[102];

	unsigned char ucFeeDiscountType;
	unsigned char szFeeDiscountValue[6];
	unsigned char szAdminFeeOriginal[6];
	unsigned char szAdminFeeDiscount[6];
	unsigned char szTimeStamp[16];	//yyyymmddhhmmss +2 dummy
}CDG_TRANS;

#define NETS_HDT		8
#define NETSCUP_HDT		9
#define NETSRSVP_HDT	11
#define StaticQR_HDT	12
#define NPX_HDT			17
#define PLUS_HDT		15
#define ASYS_EZL_HDT	18
#define DINERS_HOSTINDEX       3
#define DASH_HDT       21
#define CABCHARGE_HDT       22
#define ALIPAY_HDT       22


#define PAYMENT_MODE_VISA_ICON 	1
#define PAYMENT_MODE_MASTER_ICON 	2
#define PAYMENT_MODE_JCB_ICON 	3
#define PAYMENT_MODE_AMEX_ICON 	4
#define PAYMENT_MODE_CREDIT_ICON 	5

#ifdef    __cplusplus
}
#endif

// #define MY_TEST_ENV_WITHOUT_MDT 1

#endif    /* TYPEDEF_H */

