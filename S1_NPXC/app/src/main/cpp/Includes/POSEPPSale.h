
#ifndef ___POS_EPP_SALE___
#define ___POS_EPP_SALE___

#define MAX_PLAN_REC		20
#define MAX_IPP_REC			10


typedef struct tagPlanInfo
{
	BYTE szPlanName[99+1];
	BYTE szPlanID[6+1];
	BYTE szTerms[256+1]; //should be 999
}PLAN_INFO;

typedef struct tagIPPInfo
{
	BYTE szACQID[4+1];
	BYTE szACQName[99+1];
	int inPlanTotal;
	PLAN_INFO	stPlanInfo[20];
}IPP_INFO;





int inCTOS_EPPSaleFlowProcess(void);
int inCTOS_EPP_SALE(void);

#endif //end ___POS_EPP_SALE___

