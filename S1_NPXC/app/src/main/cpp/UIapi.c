
#include <string.h>
#include <stdio.h>
#include <stdlib.h>
#include <ctosapi.h>
#include "FeliCa/Util.h"
#include "Debug/debug.h"
#include "patrick-lib.h"
#include "Includes/CTOSInput.h"
#include "Includes/POSTypedef.h"
#include "Ui/Display.h"
#include "Includes/wub_lib.h"
#include "..\ApTrans\MultiApTrans.h"
#include "..\Database\DatabaseFunc.h"
#include "UIapi.h"

extern void vdGetPaymentModeIcon(int type, char *pBuff);

USHORT CTOS_LCDTSetReverse(BOOL boReverse)
{
    return d_OK;
    BYTE szMultipleMsgStr[512];

    memset(szMultipleMsgStr, 0x00, sizeof(szMultipleMsgStr));
    strcpy(szMultipleMsgStr, "|");

    strcat(szMultipleMsgStr, "test11111");
    strcat(szMultipleMsgStr, "|");

    vdDebug_LogPrintf("CTOS_LCDTSetReverse.......");
    inCallJAVA_DisplayMultipleMessage(szMultipleMsgStr);
}

USHORT CTOS_LCDTPrintAligned1(USHORT usY, UCHAR* pbBuf, BYTE bMode)
{

    BYTE szMultipleMsgStr[512];

    memset(szMultipleMsgStr, 0x00, sizeof(szMultipleMsgStr));
    strcpy(szMultipleMsgStr, "|");

    strcat(szMultipleMsgStr, "test11111");
    strcat(szMultipleMsgStr, "|");
    strcat(szMultipleMsgStr, "test22222");
    strcat(szMultipleMsgStr, "|");
    strcat(szMultipleMsgStr, "test33333");
    strcat(szMultipleMsgStr, "|");
    strcat(szMultipleMsgStr, "test44444");
    strcat(szMultipleMsgStr, "|");
    strcat(szMultipleMsgStr, "test55555");
    strcat(szMultipleMsgStr, "|");
    strcat(szMultipleMsgStr, "test66666");
    strcat(szMultipleMsgStr, "|");
    strcat(szMultipleMsgStr, "test77777");
    strcat(szMultipleMsgStr, "|");
    strcat(szMultipleMsgStr, "test88888");
    strcat(szMultipleMsgStr, "|");
    strcat(szMultipleMsgStr, "test99999");
    strcat(szMultipleMsgStr, "|");
    strcat(szMultipleMsgStr, "test00000");
    strcat(szMultipleMsgStr, "|");
    strcat(szMultipleMsgStr, "testaaaaa");
    strcat(szMultipleMsgStr, "|");
    strcat(szMultipleMsgStr, "testbbbbb");
    strcat(szMultipleMsgStr, "|");
    strcat(szMultipleMsgStr, "testccccc");
    strcat(szMultipleMsgStr, "|");
    strcat(szMultipleMsgStr, "testddddd");
    strcat(szMultipleMsgStr, "|");
    strcat(szMultipleMsgStr, "testeeeee");
    strcat(szMultipleMsgStr, "|");
    strcat(szMultipleMsgStr, "testfffff");
    strcat(szMultipleMsgStr, "|");
    vdDebug_LogPrintf("CTOS_LCDTPrintAligned.....");
    inCallJAVA_DisplayMultipleMessage(szMultipleMsgStr);
}

/*
USHORT CTOS_LCDTPrintAligned(USHORT usY, UCHAR* pbBuf, BYTE bMode)
{

    BYTE szMultipleMsgStr[512];
    BYTE szLine[12];
    BYTE szMode[12];

    memset(szMultipleMsgStr, 0x00, sizeof(szMultipleMsgStr));
    strcpy(szMultipleMsgStr, "|");

    sprintf(szLine, "%d", usY);
    sprintf(szMode, "%d", bMode);


    strcat(szMultipleMsgStr, szLine);
    strcat(szMultipleMsgStr, "|");
    strcat(szMultipleMsgStr, pbBuf);
    strcat(szMultipleMsgStr, "|");
    strcat(szMultipleMsgStr, szMode);
    strcat(szMultipleMsgStr, "|");
    vdDebug_LogPrintf("CTOS_LCDTPrintAligned.....[%s]", szMultipleMsgStr);

    vdDebug_LogPrintf("CTOS_LCDTPrintAligned.....");
    inCallJAVA_LCDTPrintAligned(szMultipleMsgStr);
}
*/

USHORT CTOS_LCDSelectModeEx(BYTE bMode,BOOL fClear)
{
}

USHORT CTOS_LCDGShowBMPPic(USHORT usX, USHORT usY, BYTE *baFilename)
{

	return 0;
	
	vdDebug_LogPrintf("CTOS_LCDGShowBMPPic Start");

	inCallJAVA_DisplayImage(usX, usY,baFilename);

	vdDebug_LogPrintf("CTOS_LCDGShowBMPPic End");

}

/*
USHORT CTOS_LCDTPrintXY(USHORT usX, USHORT usY, UCHAR* pbBuf){
    vdDebug_LogPrintf("Dummy DISP FUNC [%s]", pbBuf);

	  vdDebug_LogPrintf("CTOS_LCDTPrintXY");
	  inCallJAVA_LCDTPrintXY(usX, usY, pbBuf);

}
*/

/*
USHORT CTOS_LCDTClearDisplay(void){

}
*/

USHORT CTOS_LCDBackGndColor(ULONG ulColor){

}

USHORT CTOS_LCDForeGndColor(ULONG ulColor){

}

USHORT CTOS_LCDFontSelectMode(BYTE bMode){

}

USHORT CTOS_LCDGShowPic(USHORT usX, USHORT usY, BYTE* baPat, ULONG ulPatLen, USHORT usXSize){

}

USHORT CTOS_LCDTClear2EOL(void){

}

USHORT CTOS_LCDTGotoXY(USHORT usX,USHORT usY){

}

USHORT CTOS_LCDTPutchXY (USHORT usX, USHORT usY, UCHAR bChar){

}

USHORT CTOS_LCDTPrint(UCHAR* sBuf){

}

USHORT CTOS_LCDSetContrast(BYTE bValue){

}

USHORT CTOS_LCDTSelectFontSize(USHORT usFontSize){

}

USHORT CTOS_LCDTTFSwichDisplayMode(USHORT usMode){

}

USHORT CTOS_LCDTTFSelect(BYTE *baFilename, BYTE bIndex){

}
//extern int inCTLSDEMO;
BYTE InputAmountEx(USHORT usX, USHORT usY, BYTE *szCurSymbol, BYTE exponent, BYTE first_key, BYTE *baAmount, ULONG *ulAmount, USHORT usTimeOutMS, BYTE bIgnoreEnter)
{
    vdDebug_LogPrintf("=====InputAmountEx=====");

    //IPT_InputAmountEx(usX, usY, szCurSymbol, exponent, first_key, *baAmount, *ulAmount, usTimeOutMS, bIgnoreEnter);
    BYTE byLen = 0;
    int inRet = 0;

    BYTE szInBuf[128];
    BYTE szOutBuf[128];

    /*Still use the old way make it work first*/
//	if(inCTLSDEMO == 1)
//	{
//	       strcpy(szOutBuf, "100");
//	}
//	else
	{
       inRet = inCallJAVA_GetAmountString(szInBuf, szOutBuf, &byLen);
	}
    //inCallJAVA_EnterAnyNum(&byLen, szOutBuf);
    strcpy(baAmount, szOutBuf);
    if (strlen(baAmount)>0 && byLen>0)
        *ulAmount = atol(baAmount);
    else
        *ulAmount = 0;

    vdDebug_LogPrintf("baAmount[%s] byLen[%d]", ulAmount, byLen);
    vdDebug_LogPrintf("=====End InputAmountEx=====");

    return d_OK;
}


USHORT usCTOSS_Confirm(BYTE *szDispString)
{
    vdDebug_LogPrintf("=====usCTOSS_ConfirmMenu=====");

    BYTE byLen = 0;
    int inRet = 0;

    BYTE szInBuf[512];
    BYTE szOutBuf[512];

	

    inRet = inCallJAVA_UserConfirmMenu(szDispString, szOutBuf, &byLen);
	vdDebug_LogPrintf("=====End usCTOSS_ConfirmMenu  szOutBuf[%s]=====", szOutBuf);

    vdDebug_LogPrintf("=====End usCTOSS_ConfirmMenu=====");

    if (0 == strcmp(szOutBuf, "TO"))
        return d_NO;
    else if (0 == strcmp(szOutBuf, "CANCEL"))
        return d_NO;
    else
        return d_OK;
}

USHORT usCTOSS_UpDown(BYTE *szDispString)
{
    vdDebug_LogPrintf("=====usCTOSS_UpDown=====");

    BYTE byLen = 0;
    int inRet = 0;

    BYTE szInBuf[512];
    BYTE szOutBuf[512];

	

    inRet = inCallJAVA_UserSelectUpDown(szDispString, szOutBuf, &byLen);
	vdDebug_LogPrintf("=====End inCallJAVA_UserSelectUpDown  szOutBuf[%s]=====", szOutBuf);

    vdDebug_LogPrintf("=====End inCallJAVA_UserSelectUpDown=====");

    if (0 == strcmp(szOutBuf, "EXIT"))
        return d_KBD_CANCEL;
    else if (0 == strcmp(szOutBuf, "UP"))
        return d_KBD_UP;
    else if(0 == strcmp(szOutBuf, "DOWN"))
        return d_KBD_DOWN;
}




BYTE InputString(USHORT usX, USHORT usY, BYTE bInputMode,  BYTE bShowAttr, BYTE *pbaStr, USHORT *usStrLen, USHORT usMinLen, USHORT usTimeOutMS)
{
    vdDebug_LogPrintf("=====InputString=====");

    //IPT_InputAmountEx(usX, usY, szCurSymbol, exponent, first_key, *baAmount, *ulAmount, usTimeOutMS, bIgnoreEnter);
    BYTE byLen = 0;
    int inRet = 0;

    BYTE szInBuf[512];
    BYTE szOutBuf[512];

	memset(szInBuf, 0x00, sizeof(szInBuf));

    if (0x01 == bInputMode)
        strcpy(szInBuf, "1");
    else if (0x02 == bInputMode)
        strcpy(szInBuf, "2");
	else if(0x03 == bInputMode)
		strcpy(szInBuf, "3");
	else 
		strcpy(szInBuf, "0");

	
    vdDebug_LogPrintf("=====InputString=====szInBuf[%s]", szInBuf);

    /*Still use the old way make it work first*/
    inRet = inCallJAVA_UserInputString(szInBuf, szOutBuf, &byLen);
    strcpy(pbaStr, szOutBuf);
    *usStrLen = byLen;

    vdDebug_LogPrintf("pbaStr[%s] *usStrLen[%d]", pbaStr, *usStrLen);
    vdDebug_LogPrintf("=====End InputString=====");

    if (0 == strcmp(szOutBuf, "TO"))
        return 0xFF;
    else if (0 == strcmp(szOutBuf, "CANCEL"))
        return 'C';
    else
        return 'A';
}

BYTE InputStringAlpha(USHORT usX, USHORT usY, BYTE bInputMode,  BYTE bShowAttr, BYTE *pbaStr, USHORT *usStrLen, USHORT usMinLen, USHORT usTimeOutMS)
{
    vdDebug_LogPrintf("=====InputStringAlpha=====");

    //IPT_InputAmountEx(usX, usY, szCurSymbol, exponent, first_key, *baAmount, *ulAmount, usTimeOutMS, bIgnoreEnter);
    BYTE byLen = 0;
    int inRet = 0;

    BYTE szInBuf[128];
    BYTE szOutBuf[128];

    strcpy(szInBuf, "1");

    /*Still use the old way make it work first*/
    inRet = inCallJAVA_UserInputString(szInBuf, szOutBuf, &byLen);
    strcpy(pbaStr, szOutBuf);
    *usStrLen = byLen;

    vdDebug_LogPrintf("pbaStr[%s] *usStrLen[%d]", pbaStr, *usStrLen);
    vdDebug_LogPrintf("=====End InputStringAlpha=====");

    if (0 == strcmp(szOutBuf, "TO"))
        return 0xFF;
    else if (0 == strcmp(szOutBuf, "CANCEL"))
        return 'C';
    else
        return 'A';

    return d_OK;
}

BYTE InputStringAlphaEx(USHORT usX, USHORT usY, BYTE bInputMode,  BYTE bShowAttr, BYTE *pbaStr, USHORT *usStrLen, USHORT usMinLen, USHORT usTimeOutMS)
{
    vdDebug_LogPrintf("=====InputStringAlphaEx=====");

    //IPT_InputAmountEx(usX, usY, szCurSymbol, exponent, first_key, *baAmount, *ulAmount, usTimeOutMS, bIgnoreEnter);
    BYTE byLen = 0;
    int inRet = 0;

    BYTE szInBuf[128];
    BYTE szOutBuf[128];

    strcpy(szInBuf, "1");

    /*Still use the old way make it work first*/
    inRet = inCallJAVA_UserInputString(szInBuf, szOutBuf, &byLen);
    strcpy(pbaStr, szOutBuf);
    *usStrLen = byLen;

    vdDebug_LogPrintf("pbaStr[%s] *usStrLen[%d]", pbaStr, *usStrLen);
    vdDebug_LogPrintf("=====End InputStringAlphaEx=====");

    if (0 == strcmp(szOutBuf, "TO"))
        return 0xFF;
    else if (0 == strcmp(szOutBuf, "CANCEL"))
        return 'C';
    else
        return 'A';

    return d_OK;
}

BYTE InputStringAlphaEx2(USHORT usX, USHORT usY, BYTE bInputMode,  BYTE bShowAttr, BYTE *pbaStr, USHORT *usStrLen, USHORT usMinLen, USHORT usTimeOutMS)
{
    vdDebug_LogPrintf("=====InputStringAlphaEx2=====");

    //IPT_InputAmountEx(usX, usY, szCurSymbol, exponent, first_key, *baAmount, *ulAmount, usTimeOutMS, bIgnoreEnter);
    BYTE byLen = 0;
    int inRet = 0;

    BYTE szInBuf[128];
    BYTE szOutBuf[128];

    strcpy(szInBuf, "1");

    /*Still use the old way make it work first*/
    inRet = inCallJAVA_UserInputString(szInBuf, szOutBuf, &byLen);
    strcpy(pbaStr, szOutBuf);
    *usStrLen = byLen;

    vdDebug_LogPrintf("pbaStr[%s] *usStrLen[%d]", pbaStr, *usStrLen);
    vdDebug_LogPrintf("=====End InputStringAlphaEx2=====");

    if (0 == strcmp(szOutBuf, "TO"))
        return 0xFF;
    else if (0 == strcmp(szOutBuf, "CANCEL"))
        return 'C';
    else
        return 'A';

    return d_OK;
}

USHORT shCTOS_GetNum(IN  USHORT usY, IN  USHORT usLeftRight, OUT BYTE *baBuf, OUT  USHORT *usStrLen, USHORT usMinLen, USHORT usMaxLen, USHORT usByPassAllow, USHORT usTimeOutMS)
{
    vdDebug_LogPrintf("=====shCTOS_GetNum=====");

    //IPT_InputAmountEx(usX, usY, szCurSymbol, exponent, first_key, *baAmount, *ulAmount, usTimeOutMS, bIgnoreEnter);
    BYTE byLen = 0;
    int inRet = 0;

    BYTE szInBuf[128];
    BYTE szOutBuf[128];

    strcpy(szInBuf, "1");

    /*Still use the old way make it work first*/
    inRet = inCallJAVA_UserInputString(szInBuf, szOutBuf, &byLen);
    strcpy(baBuf, szOutBuf);
    *usStrLen = byLen;

    vdDebug_LogPrintf("baBuf[%s] *usStrLen[%d]", baBuf, *usStrLen);
    vdDebug_LogPrintf("=====End shCTOS_GetNum=====");

    return d_OK;
}

BYTE MenuDisplay(BYTE *sHeaderString, BYTE iHeaderStrLen, BYTE bHeaderAttr, BYTE iCol, BYTE center_x, BYTE *sItemsString, BYTE isReCount)
{
    vdDebug_LogPrintf("=====MenuDisplay=====");

    //IPT_InputAmountEx(usX, usY, szCurSymbol, exponent, first_key, *baAmount, *ulAmount, usTimeOutMS, bIgnoreEnter);
    BYTE byLen = 0;
    int inRet = 0;

    BYTE bySelect = 0;

    BYTE szInBuf[1024];
    BYTE szOutBuf[1024];


    if (strlen(sHeaderString)>0)
    {
        strcpy(szInBuf, sHeaderString);
    }
    else
    {
        strcpy(szInBuf, "");
    }

    strcat(szInBuf, "|");

    /*After title string are menu items*/
    strcat(szInBuf, sItemsString);

    /*Still use the old way make it work first*/
    //inRet = inCallJAVA_DOptionMenuDisplay(szInBuf, szOutBuf, &byLen);
    inRet = inCallJAVA_DPopupMenuDisplay(szInBuf, szOutBuf, &byLen);
    //strcpy(baBuf, szOutBuf);
    //*usStrLen = byLen;
    bySelect = atoi(szOutBuf);

    vdDebug_LogPrintf("=====End MenuDisplay=====");

    if (0 == strcmp(szOutBuf, "TO"))
        return 0xFF;
    else if (0 == strcmp(szOutBuf, "CANCEL"))
        return 'C';

    return bySelect;
}


USHORT usCTOSS_BackToProgress(BYTE *szDispString)
{
  
    BYTE byLen = 0;
    int inRet = 0;
	
    BYTE szOutBuf[512];

	vdDebug_LogPrintf("=====usCTOSS_BackToProgress=====");
    inRet = inCallJAVA_BackToProgress(szDispString, szOutBuf, &byLen);
	vdDebug_LogPrintf("=====End usCTOSS_BackToProgress  szOutBuf[%s]=====", szOutBuf);

    return d_OK;
}

USHORT usCTOSS_DisplayUI(BYTE *szDispString)
{

    BYTE byLen = 0;
    int inRet = 0;

    BYTE szOutBuf[512];

    vdDebug_LogPrintf("=====usCTOSS_DisplayUI=====");


    inRet = inCallJAVA_DisplayUI(szDispString, szOutBuf, &byLen);

    vdDebug_LogPrintf("=====End usCTOSS_DisplayUI  szOutBuf[%s]=====", szOutBuf);

    return d_OK;
}


USHORT usCTOSS_CardEntryUI(BYTE *szDispString)
{

    BYTE byLen = 0;
    int inRet = 0;

    BYTE szOutBuf[512];

	vdDebug_LogPrintf("=====usCTOSS_CardEntryUI=====");


    inRet = inCallJAVA_CardEntryUI(szDispString, szOutBuf, &byLen);
	
	vdDebug_LogPrintf("=====End usCTOSS_CardEntryUI  szOutBuf[%s]=====", szOutBuf);
   
    return d_OK;
}


void InsertCardUI(void)
{

    BYTE szTitle[20+1];
    BYTE szDisMsg[200];
    BYTE    szTotalAmt[12+1];
    BYTE    szStr[45];


    memset(szTitle, 0x00, sizeof(szTitle));
    memset(szDisMsg, 0x00, sizeof(szDisMsg));

    strcpy(szDisMsg, "cardidle");
    strcat(szDisMsg, "|");
    strcat(szDisMsg, "back");

    szGetTransTitle(srTransRec.byTransType, szTitle);


    vdDebug_LogPrintf("szTitle[%s]", szTitle);
    strcat(szDisMsg, "|");
    strcat(szDisMsg, szTitle);


    memset(szTotalAmt, 0x00, sizeof(szTotalAmt));
    wub_hex_2_str(srTransRec.szTotalAmount, szTotalAmt, AMT_BCD_SIZE);


    vdTrimLeadZeroes(szTotalAmt);

    memset(szStr, 0x00, sizeof(szStr));
    vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szTotalAmt, szStr);
    vdDebug_LogPrintf("szStr[%s]", szStr);
    strcat(szDisMsg, "|");
    strcat(szDisMsg, "Amount:");
    strcat(szDisMsg, "|");
    strcat(szDisMsg, szStr);

    usCTOSS_DisplayUI(szDisMsg);
}


BYTE PresentCardUI(void)
{
    BYTE byLen = 0;
    BYTE szOutBuf[128] = {0};
	BYTE szDisMsg[100];
    BYTE    szTotalAmt[12+1];
    BYTE    szStr[45];


	vdDebug_LogPrintf("PresentCardUI");

	memset(szDisMsg, 0x00, sizeof(szDisMsg));


	memset(szTotalAmt, 0x00, sizeof(szTotalAmt));
    wub_hex_2_str(srTransRec.szTotalAmount, szTotalAmt, AMT_BCD_SIZE);
	
	
	vdTrimLeadZeroes(szTotalAmt);
	
	memset(szStr, 0x00, sizeof(szStr));
	vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szTotalAmt, szStr);
	vdDebug_LogPrintf("szStr[%s]", szStr);
	strcat(szDisMsg, "FARE:");
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "$ ");
	strcat(szDisMsg, szStr);
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "FEE & GST APPLIES");
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "PROCEED BY:");

    inCallJAVA_PresentCard(szDisMsg, szOutBuf, &byLen);

	
	vdDebug_LogPrintf("PresentCardUI szOutBuf[%s]", szOutBuf);


    return d_OK;
}

#define LINE_SIZE  58
int inPadString(BYTE *szSrc, BYTE *szDest)
{
   int inPadLen = 0;
   BYTE szTemp[100];   
   int inSrcLen = strlen(szSrc);


   
   vdDebug_LogPrintf("inSrcLen[%d]", inSrcLen);
   
   inPadLen = (LINE_SIZE - strlen((char *) szSrc)) >> 1;

     
   vdDebug_LogPrintf("inPadLen[%d]", inPadLen);
     
   memset(szTemp, 0x00, sizeof(szTemp));
   wub_strpad((char *) szSrc,(char *) szTemp, SPACE, inPadLen+inSrcLen, RIGHT);


   
   vdDebug_LogPrintf("szTemp[%s]", szTemp);

   wub_strpad((char *) szTemp,(char *) szDest, SPACE, inPadLen*2+inSrcLen, LEFT);

   
   vdDebug_LogPrintf("szDest[%s]", szDest);

}


BYTE ApprovedUI1(void)
{
    BYTE byLen = 0;
    BYTE szOutBuf[128] = {0};
	BYTE szDisMsg[500];
    BYTE    szTotalAmt[12+1];
    BYTE    szStr[45];
    BYTE    szDest[45];


	memset(szDisMsg, 0x00, sizeof(szDisMsg));


	memset(szTotalAmt, 0x00, sizeof(szTotalAmt));
    wub_hex_2_str(srTransRec.szTotalAmount, szTotalAmt, AMT_BCD_SIZE);
	
	
	vdTrimLeadZeroes(szTotalAmt);
	
	memset(szStr, 0x00, sizeof(szStr));
	vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szTotalAmt, szStr);
	vdDebug_LogPrintf("szStr[%s]", szStr);
	strcat(szDisMsg, "FARE : $");
	strcat(szDisMsg, szStr);
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "Admin : $");	
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "GST : $");
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "Total : $");	
	strcat(szDisMsg, szStr);
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "PAY BY :");

	strcat(szDisMsg, "|");
	memset(szStr, 0x00, sizeof(szStr));
	memset(szDest, 0x00, sizeof(szDest));
	strcpy(szStr, "APPROVED");
	inPadString(szStr, szDest);
	strcat(szDisMsg, szDest);

    inCallJAVA_ApprovedTrans(szDisMsg, szOutBuf, &byLen);

	
	vdDebug_LogPrintf("ApprovedUI szOutBuf[%s]", szOutBuf);


    return d_OK;
}

BYTE DeclinedUI(void)
{
    BYTE byLen = 0;
    BYTE szOutBuf[128] = {0};
	BYTE szDisMsg[100];
    BYTE    szTotalAmt[12+1];
    BYTE    szStr[45];
    BYTE    szDest[45];

	memset(szDisMsg, 0x00, sizeof(szDisMsg));


	memset(szTotalAmt, 0x00, sizeof(szTotalAmt));
    wub_hex_2_str(srTransRec.szTotalAmount, szTotalAmt, AMT_BCD_SIZE);
	
	
	vdTrimLeadZeroes(szTotalAmt);
	
	memset(szStr, 0x00, sizeof(szStr));
	vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szTotalAmt, szStr);
	vdDebug_LogPrintf("szStr[%s]", szStr);
	strcat(szDisMsg, "FARE : $");
	strcat(szDisMsg, szStr);
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "Admin : $");	
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "GST : $");
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "Total : $");	
	strcat(szDisMsg, szStr);
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "PAY BY :");

	strcat(szDisMsg, "|");
    memset(szStr, 0x00, sizeof(szStr));
    memset(szDest, 0x00, sizeof(szDest));
    strcpy(szStr, "NOT APPROVED");
    inPadString(szStr, szDest);
    strcat(szDisMsg, szDest);

    inCallJAVA_DeclinedTrans(szDisMsg, szOutBuf, &byLen);

	
	vdDebug_LogPrintf("DeclinedUI szOutBuf[%s]", szOutBuf);


    return d_OK;
}


BYTE TransCancelledUI1(void)
{
    BYTE byLen = 0;
    BYTE szOutBuf[128] = {0};
	BYTE szDisMsg[100];
    BYTE    szTotalAmt[12+1];
    BYTE    szStr[45];
    BYTE    szDest[45];


	memset(szDisMsg, 0x00, sizeof(szDisMsg));


	memset(szTotalAmt, 0x00, sizeof(szTotalAmt));
    wub_hex_2_str(srTransRec.szTotalAmount, szTotalAmt, AMT_BCD_SIZE);
	
	
	vdTrimLeadZeroes(szTotalAmt);
	
	memset(szStr, 0x00, sizeof(szStr));
	vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szTotalAmt, szStr);
	vdDebug_LogPrintf("szStr[%s]", szStr);
	strcat(szDisMsg, "Amount : $");
	strcat(szDisMsg, szStr);
	strcat(szDisMsg, "|");

	memset(szStr, 0x00, sizeof(szStr));
    memset(szDest, 0x00, sizeof(szDest));
    strcpy(szStr, "TRANSACTION");
    inPadString(szStr, szDest);
    strcat(szDisMsg, szDest);	
	strcat(szDisMsg, "|");

	memset(szStr, 0x00, sizeof(szStr));
    memset(szDest, 0x00, sizeof(szDest));
    strcpy(szStr, "CANCELLED");
    inPadString(szStr, szDest);
    strcat(szDisMsg, szDest);


    inCallJAVA_TransCancel(szDisMsg, szOutBuf, &byLen);

	
	vdDebug_LogPrintf("PresentCardUI szOutBuf[%s]", szOutBuf);

    CTOS_Delay(3000);
    return d_OK;
}


USHORT usSetDateTime(BYTE *szDateTime)
{

    BYTE byLen = 0;
    int inRet = 0;

    BYTE szOutBuf_setDateTime[512];
    memset(szOutBuf_setDateTime, 0x00, sizeof(szOutBuf_setDateTime));

    vdDebug_LogPrintf("saturn =====usSetDateTime=====");

    inRet = inCallJAVA_usSetDateTime(szDateTime, szOutBuf_setDateTime, &byLen);

    //vdDebug_LogPrintf("saturn ====End usCARDENTRY  szOutBuf[%s]=====", szOutBuf);

    //return d_OK;
    return d_OK;
}

USHORT usCTOSS_ClearAllExceptTittle()
{

    BYTE byLen = 0;
    int inRet = 0;

    BYTE szOutBuf[512];

    vdDebug_LogPrintf("=====usCTOSS_ClearAllExceptTittle=====");

    inRet = inCallJAVA_ShowTxnMainLayoutEx(szOutBuf, szOutBuf, &byLen);

    vdDebug_LogPrintf("=====End usCTOSS_ClearAllExceptTittle  szOutBuf[%s]=====", szOutBuf);

    return d_OK;
}

USHORT DisplayErrorMsg(BYTE *szStringMsg)
{

    BYTE byLen = 0;
    int inRet = 0;

    BYTE szOutBuf_disp[512];

    vdDebug_LogPrintf("=====DisplayErrorMsg=====");

    inRet = inCallJAVA_DisplayErrorMsg(szStringMsg, szOutBuf_disp, &byLen);

    //vdDebug_LogPrintf("=====End DisplayErrorMsg  szOutBuf[%s]=====", szOutBuf_disp);

    return d_OK;
}


BYTE ConfirmAmountUI(int txnType)
{
    BYTE byLen = 0;
    BYTE szOutBuf[128] = {0};
	BYTE szDisMsg[100];
    BYTE    szTotalAmt[12+1];
    BYTE    szStr[45];

//new added
//    BYTE    szTotalFeeAmt[12+1];
//    BYTE    szFormattedTotalFeeAmt[64+1];
    BYTE    szBaseFareAmt[12+1];
    BYTE    szFormattedBaseFareAmt[64+1];
    BYTE    szFeeAmt[12+1];
    BYTE    szFormattedFeeAmt[64+1];
    BYTE    szGSTAmt[12+1];
    BYTE    szFormattedGSTAmt[64+1];
    int inTimeOut = 15;
    BYTE    szTimeOut[12+1];
	
    vdDebug_LogPrintf("txnType[%d]", txnType);
	vdDebug_LogPrintf("ConfirmAmountUI byEntryMode=[%d]",srTransRec.byEntryMode);
	if (srTransRec.byEntryMode == CARD_ENTRY_WAVE)
		return d_OK;

    //Admin fee
    memset(szFeeAmt, 0x00, sizeof(szFeeAmt));
    sprintf(szFeeAmt, "%06d", srPmtAddionalInfo.lnAdminFeeAmount);
    vdTrimLeadZeroes(szFeeAmt);
    memset(szFormattedFeeAmt, 0x00, sizeof(szFormattedFeeAmt));
    vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szFeeAmt, szFormattedFeeAmt);
    vdDebug_LogPrintf("szFormattedFeeAmt[%s]", szFormattedFeeAmt);

    //GST
    memset(szGSTAmt, 0x00, sizeof(szGSTAmt));
    sprintf(szGSTAmt, "%06d", srPmtAddionalInfo.lnGSTAmount);
    vdTrimLeadZeroes(szGSTAmt);
    memset(szFormattedGSTAmt, 0x00, sizeof(szFormattedGSTAmt));
    vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szGSTAmt, szFormattedGSTAmt);
    vdDebug_LogPrintf("szFormattedGSTAmt[%s]", szFormattedGSTAmt);

    memset(szBaseFareAmt, 0x00, sizeof(szBaseFareAmt));
    sprintf(szBaseFareAmt, "%06d", srPmtAddionalInfo.lnFareAmount);
    vdTrimLeadZeroes(szBaseFareAmt);
    memset(szFormattedBaseFareAmt, 0x00, sizeof(szFormattedBaseFareAmt));
    vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szBaseFareAmt, szFormattedBaseFareAmt);
    vdDebug_LogPrintf("szFormattedBaseFareAmt[%s]", szFormattedBaseFareAmt);
    //new added end

	memset(szDisMsg, 0x00, sizeof(szDisMsg));


	memset(szTotalAmt, 0x00, sizeof(szTotalAmt));
    wub_hex_2_str(srTransRec.szTotalAmount, szTotalAmt, AMT_BCD_SIZE);
	
	
	vdTrimLeadZeroes(szTotalAmt);
	
	memset(szStr, 0x00, sizeof(szStr));
	vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szTotalAmt, szStr);
	vdDebug_LogPrintf("szStr[%s]", szStr);
	strcat(szDisMsg, "FARE: $");
	strcat(szDisMsg, szFormattedBaseFareAmt);
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "ADMIN: $");
	strcat(szDisMsg, szFormattedFeeAmt);
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "GST : $");	
	strcat(szDisMsg, szFormattedGSTAmt);
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "TOTAL: $");	
	strcat(szDisMsg, szStr);
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "CONFIRM?");
    vdGetPaymentModeIcon(txnType, szDisMsg);

    //Timeout
    memset(szTimeOut, 0, sizeof(szTimeOut));
    strcat(szDisMsg, "|");
    //NETS default 10, Credit is 30
    inTimeOut = strTXN.inCreditConfirmAmtTO;
    sprintf(szTimeOut, "%d", inTimeOut);
    strcat(szDisMsg, szTimeOut);

    inCallJAVA_ConfirmAmountUI(szDisMsg, szOutBuf, &byLen);

	vdBeepSound();
	vdDebug_LogPrintf("ConfirmAmountUI szOutBuf[%s]", szOutBuf);
    if (strcmp(szOutBuf, "TO") == 0)
        return d_NO;

    if (strcmp(szOutBuf, "CANCEL") == 0)
        return d_NO;

	if(strcmp(szOutBuf, "BACK") == 0)
		return d_NO;
	
    return d_OK;
}

BYTE ProcessingUIEx(int txnType)
{
    BYTE byLen = 0;
    BYTE szOutBuf[128] = {0};
    BYTE szDisMsg[100];
    BYTE    szTotalAmt[12+1];
    BYTE    szStr[45];

//new added
    BYTE    szTotalFeeAmt[12+1];
    BYTE    szFormattedTotalFeeAmt[64+1];
    BYTE    szBaseFareAmt[12+1];
    BYTE    szFormattedBaseFareAmt[64+1];

    memset(szBaseFareAmt, 0x00, sizeof(szBaseFareAmt));
    sprintf(szBaseFareAmt, "%06d", srPmtAddionalInfo.lnFareAmount);
    vdTrimLeadZeroes(szBaseFareAmt);
    memset(szFormattedBaseFareAmt, 0x00, sizeof(szFormattedBaseFareAmt));
    vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szBaseFareAmt, szFormattedBaseFareAmt);
    vdDebug_LogPrintf("szFormattedBaseFareAmt[%s]", szFormattedBaseFareAmt);

    memset(szTotalFeeAmt, 0x00, sizeof(szTotalFeeAmt));
    sprintf(szTotalFeeAmt, "%06d", (srPmtAddionalInfo.lnAdminFeeAmount + srPmtAddionalInfo.lnGSTAmount));
    vdTrimLeadZeroes(szTotalFeeAmt);
    memset(szFormattedTotalFeeAmt, 0x00, sizeof(szFormattedTotalFeeAmt));
    vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szTotalFeeAmt, szFormattedTotalFeeAmt);
    vdDebug_LogPrintf("szFormattedTotalFeeAmt[%s]", szFormattedTotalFeeAmt);
    //new added end

    memset(szDisMsg, 0x00, sizeof(szDisMsg));


    memset(szTotalAmt, 0x00, sizeof(szTotalAmt));
    wub_hex_2_str(srTransRec.szTotalAmount, szTotalAmt, AMT_BCD_SIZE);


    vdTrimLeadZeroes(szTotalAmt);

    memset(szStr, 0x00, sizeof(szStr));
    vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szTotalAmt, szStr);
    vdDebug_LogPrintf("szStr[%s]", szStr);
    strcat(szDisMsg, "FARE: $");
    strcat(szDisMsg, szFormattedBaseFareAmt);
	vdDebug_LogPrintf("szDisMsg1[%s]", szDisMsg);
    strcat(szDisMsg, "|");
    strcat(szDisMsg, "FEES: $");
    strcat(szDisMsg, szFormattedTotalFeeAmt);
	vdDebug_LogPrintf("szDisMsg2[%s]", szDisMsg);
    strcat(szDisMsg, "|");
    strcat(szDisMsg, "TOTAL: $");
    strcat(szDisMsg, szStr);
	vdDebug_LogPrintf("szDisMsg3[%s]", szDisMsg);
    strcat(szDisMsg, "|");
    strcat(szDisMsg, "PROCESSING ORDER");
	vdDebug_LogPrintf("szDisMsg4[%s]", szDisMsg);
    strcat(szDisMsg, "|");
    strcat(szDisMsg, "PLEASE WAIT");
	vdDebug_LogPrintf("szDisMsg5[%s]", szDisMsg);
    vdGetPaymentModeIcon(txnType, szDisMsg);
	vdDebug_LogPrintf("szDisMsg6[%s]", szDisMsg);
    inCallJAVA_ProcessingUI(szDisMsg, szOutBuf, &byLen);


    vdDebug_LogPrintf("ProcessingUIEx szOutBuf[%s]", szOutBuf);

    return d_OK;
}

BYTE ApprovedUI(int txnType)
{
    BYTE byLen = 0;
    BYTE szOutBuf[128] = {0};
	BYTE szDisMsg[100];
    BYTE    szTotalAmt[12+1];
    BYTE    szStr[45];

//new added
//    BYTE    szTotalFeeAmt[12+1];
//    BYTE    szFormattedTotalFeeAmt[64+1];
    BYTE    szBaseFareAmt[12+1];
    BYTE    szFormattedBaseFareAmt[64+1];
    BYTE    szFeeAmt[12+1];
    BYTE    szFormattedFeeAmt[64+1];
    BYTE    szGSTAmt[12+1];
    BYTE    szFormattedGSTAmt[64+1];

	vdSetErrorMessage("");//fix after display apprvoed/not approved/cancel  UI, don't display error message

    //Admin fee
    memset(szFeeAmt, 0x00, sizeof(szFeeAmt));
    sprintf(szFeeAmt, "%06d", srPmtAddionalInfo.lnAdminFeeAmount);
    vdTrimLeadZeroes(szFeeAmt);
    memset(szFormattedFeeAmt, 0x00, sizeof(szFormattedFeeAmt));
    vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szFeeAmt, szFormattedFeeAmt);
    vdDebug_LogPrintf("szFormattedFeeAmt[%s]", szFormattedFeeAmt);

    //GST
    memset(szGSTAmt, 0x00, sizeof(szGSTAmt));
    sprintf(szGSTAmt, "%06d", srPmtAddionalInfo.lnGSTAmount);
    vdTrimLeadZeroes(szGSTAmt);
    memset(szFormattedGSTAmt, 0x00, sizeof(szFormattedGSTAmt));
    vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szGSTAmt, szFormattedGSTAmt);
    vdDebug_LogPrintf("szFormattedGSTAmt[%s]", szFormattedGSTAmt);

    memset(szBaseFareAmt, 0x00, sizeof(szBaseFareAmt));
    sprintf(szBaseFareAmt, "%06d", srPmtAddionalInfo.lnFareAmount);
    vdTrimLeadZeroes(szBaseFareAmt);
    memset(szFormattedBaseFareAmt, 0x00, sizeof(szFormattedBaseFareAmt));
    vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szBaseFareAmt, szFormattedBaseFareAmt);
    vdDebug_LogPrintf("szFormattedBaseFareAmt[%s]", szFormattedBaseFareAmt);
    //new added end

	memset(szDisMsg, 0x00, sizeof(szDisMsg));


	memset(szTotalAmt, 0x00, sizeof(szTotalAmt));
    wub_hex_2_str(srTransRec.szTotalAmount, szTotalAmt, AMT_BCD_SIZE);
	
	
	vdTrimLeadZeroes(szTotalAmt);
	
	memset(szStr, 0x00, sizeof(szStr));
	vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szTotalAmt, szStr);
	vdDebug_LogPrintf("szStr[%s]", szStr);
	strcat(szDisMsg, "FARE: $");
	strcat(szDisMsg, szFormattedBaseFareAmt);
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "ADMIN: $");
	strcat(szDisMsg, szFormattedFeeAmt);
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "GST: $");	
	strcat(szDisMsg, szFormattedGSTAmt);
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "TOTAL: $");	
	strcat(szDisMsg, szStr);

    vdGetPaymentModeIcon(txnType, szDisMsg);

	
    if(strTXN.fTXNVoice == 0)
    {
        strcat(szDisMsg, "|");
        strcat(szDisMsg, "NOSOUND");
    }
	

	

    inCallJAVA_ApproveUI(szDisMsg, szOutBuf, &byLen);
	CTOS_Delay(2000);

	
	vdDebug_LogPrintf("ApprovedUI szOutBuf[%s]", szOutBuf);

    return d_OK;
}




BYTE NotApprovedUI(int txnType)
{
    BYTE byLen = 0;
    BYTE szOutBuf[128] = {0};
	BYTE szDisMsg[100];
    BYTE    szTotalAmt[12+1];
    BYTE    szStr[45];
//new added
//    BYTE    szTotalFeeAmt[12+1];
//    BYTE    szFormattedTotalFeeAmt[64+1];
    BYTE    szBaseFareAmt[12+1];
    BYTE    szFormattedBaseFareAmt[64+1];
    BYTE    szFeeAmt[12+1];
    BYTE    szFormattedFeeAmt[64+1];
    BYTE    szGSTAmt[12+1];
    BYTE    szFormattedGSTAmt[64+1];


	vdSetErrorMessage("");//fix after display apprvoed/not approved/cancel  UI, don't display error message
    //Admin fee
    memset(szFeeAmt, 0x00, sizeof(szFeeAmt));
    sprintf(szFeeAmt, "%06d", srPmtAddionalInfo.lnAdminFeeAmount);
    vdTrimLeadZeroes(szFeeAmt);
    memset(szFormattedFeeAmt, 0x00, sizeof(szFormattedFeeAmt));
    vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szFeeAmt, szFormattedFeeAmt);
    vdDebug_LogPrintf("szFormattedFeeAmt[%s]", szFormattedFeeAmt);

    //GST
    memset(szGSTAmt, 0x00, sizeof(szGSTAmt));
    sprintf(szGSTAmt, "%06d", srPmtAddionalInfo.lnGSTAmount);
    vdTrimLeadZeroes(szGSTAmt);
    memset(szFormattedGSTAmt, 0x00, sizeof(szFormattedGSTAmt));
    vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szGSTAmt, szFormattedGSTAmt);
    vdDebug_LogPrintf("szFormattedGSTAmt[%s]", szFormattedGSTAmt);

    memset(szBaseFareAmt, 0x00, sizeof(szBaseFareAmt));
    sprintf(szBaseFareAmt, "%06d", srPmtAddionalInfo.lnFareAmount);
    vdTrimLeadZeroes(szBaseFareAmt);
    memset(szFormattedBaseFareAmt, 0x00, sizeof(szFormattedBaseFareAmt));
    vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szBaseFareAmt, szFormattedBaseFareAmt);
    vdDebug_LogPrintf("szFormattedBaseFareAmt[%s]", szFormattedBaseFareAmt);
    //new added end

	memset(szDisMsg, 0x00, sizeof(szDisMsg));


	memset(szTotalAmt, 0x00, sizeof(szTotalAmt));
    wub_hex_2_str(srTransRec.szTotalAmount, szTotalAmt, AMT_BCD_SIZE);
	
	
	vdTrimLeadZeroes(szTotalAmt);
	
	memset(szStr, 0x00, sizeof(szStr));
	vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szTotalAmt, szStr);
	vdDebug_LogPrintf("szStr[%s]", szStr);
	strcat(szDisMsg, "FARE : $");
	strcat(szDisMsg, szFormattedBaseFareAmt);
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "ADMIN : $");
	strcat(szDisMsg, szFormattedFeeAmt);
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "GST : $");	
	strcat(szDisMsg, szFormattedGSTAmt);
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "TOTAL : $");	
	strcat(szDisMsg, szStr);

    vdGetPaymentModeIcon(txnType, szDisMsg);

    if(strTXN.fTXNVoice == 0)
    {
        strcat(szDisMsg, "|");
        strcat(szDisMsg, "NOSOUND");
    }
    inCallJAVA_NotApproveUI(szDisMsg, szOutBuf, &byLen);
	CTOS_Delay(2000);

	
	vdDebug_LogPrintf("NotApprovedUI szOutBuf[%s]", szOutBuf);

    return d_OK;
}






BYTE TransCancelledUI(int txnType)
{
	BYTE byLen = 0;
	BYTE szOutBuf[128] = {0};
	BYTE szDisMsg[100];
	BYTE	szTotalAmt[12+1];
	BYTE	szStr[45];

//new added
//	  BYTE	  szTotalFeeAmt[12+1];
//	  BYTE	  szFormattedTotalFeeAmt[64+1];
	BYTE	szBaseFareAmt[12+1];
	BYTE	szFormattedBaseFareAmt[64+1];
	BYTE	szFeeAmt[12+1];
	BYTE	szFormattedFeeAmt[64+1];
	BYTE	szGSTAmt[12+1];
	BYTE	szFormattedGSTAmt[64+1];

	
	vdSetErrorMessage("");//fix after display apprvoed/not approved/cancel	UI, don't display error message

	//Admin fee
	memset(szFeeAmt, 0x00, sizeof(szFeeAmt));
	sprintf(szFeeAmt, "%06d", srPmtAddionalInfo.lnAdminFeeAmount);
	vdTrimLeadZeroes(szFeeAmt);
	memset(szFormattedFeeAmt, 0x00, sizeof(szFormattedFeeAmt));
	vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szFeeAmt, szFormattedFeeAmt);
	vdDebug_LogPrintf("szFormattedFeeAmt[%s]", szFormattedFeeAmt);

	//GST
	memset(szGSTAmt, 0x00, sizeof(szGSTAmt));
	sprintf(szGSTAmt, "%06d", srPmtAddionalInfo.lnGSTAmount);
	vdTrimLeadZeroes(szGSTAmt);
	memset(szFormattedGSTAmt, 0x00, sizeof(szFormattedGSTAmt));
	vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szGSTAmt, szFormattedGSTAmt);
	vdDebug_LogPrintf("szFormattedGSTAmt[%s]", szFormattedGSTAmt);

	memset(szBaseFareAmt, 0x00, sizeof(szBaseFareAmt));
	sprintf(szBaseFareAmt, "%06d", srPmtAddionalInfo.lnFareAmount);
	vdTrimLeadZeroes(szBaseFareAmt);
	memset(szFormattedBaseFareAmt, 0x00, sizeof(szFormattedBaseFareAmt));
	vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szBaseFareAmt, szFormattedBaseFareAmt);
	vdDebug_LogPrintf("szFormattedBaseFareAmt[%s]", szFormattedBaseFareAmt);
	//new added end

	memset(szDisMsg, 0x00, sizeof(szDisMsg));


	memset(szTotalAmt, 0x00, sizeof(szTotalAmt));
	wub_hex_2_str(srTransRec.szTotalAmount, szTotalAmt, AMT_BCD_SIZE);
	
	
	vdTrimLeadZeroes(szTotalAmt);
	
	memset(szStr, 0x00, sizeof(szStr));
	vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szTotalAmt, szStr);
	vdDebug_LogPrintf("szStr[%s]", szStr);
	strcat(szDisMsg, "FARE: $");
	strcat(szDisMsg, szFormattedBaseFareAmt);
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "ADMIN: $");
	strcat(szDisMsg, szFormattedFeeAmt);
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "GST: $"); 
	strcat(szDisMsg, szFormattedGSTAmt);
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "TOTAL: $");	
	strcat(szDisMsg, szStr);

	vdGetPaymentModeIcon(txnType, szDisMsg);

	

	inCallJAVA_TransCancelUI(szDisMsg, szOutBuf, &byLen);
	vdErrorBeepSound();
	CTOS_Delay(2000);

	
	vdDebug_LogPrintf("TransCancelledUI szOutBuf[%s]", szOutBuf);

	return d_OK;
}




BYTE VoidMenu(void)
{
    BYTE byLen = 0;
    BYTE szOutBuf[128] = {0};
	BYTE szDisMsg[100];

	memset(szDisMsg, 0x00, sizeof(szDisMsg));


    inCallJAVA_VoidMenu(szDisMsg, szOutBuf, &byLen);

	
	vdDebug_LogPrintf("VoidMenu szOutBuf[%s]", szOutBuf);

    return d_OK;
}




BYTE PresentCardUIVoid(int txnType)
{
    BYTE byLen = 0;
    BYTE szOutBuf[128] = {0};
	BYTE szDisMsg[100];
    BYTE    szTotalAmt[12+1];
    BYTE    szStr[45];

	vdDebug_LogPrintf("PresentCardUIVoid");

	memset(szDisMsg, 0x00, sizeof(szDisMsg));


	memset(szTotalAmt, 0x00, sizeof(szTotalAmt));
    wub_hex_2_str(srTransRec.szTotalAmount, szTotalAmt, AMT_BCD_SIZE);
	
	
	vdTrimLeadZeroes(szTotalAmt);
	
	memset(szStr, 0x00, sizeof(szStr));
	vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szTotalAmt, szStr);
	vdDebug_LogPrintf("szStr[%s]", szStr);
	strcat(szDisMsg, "VOID PAYMENT");
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "TOTAL: $");	
	strcat(szDisMsg, szStr);
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "PROCEED BY:");
    vdGetPaymentModeIcon(txnType, szDisMsg);
    inCallJAVA_PresentCardVoid(szDisMsg, szOutBuf, &byLen);

	
	vdDebug_LogPrintf("PresentCardUIVoid szOutBuf[%s]", szOutBuf);


    return d_OK;
}


BYTE ConfirmAmountUIVoid(int txnType)
{
    BYTE byLen = 0;
    BYTE szOutBuf[128] = {0};
	BYTE szDisMsg[100];
    BYTE    szTotalAmt[12+1];
    BYTE    szStr[45];
    BYTE    szPanMaskStr[45];
    int inTimeOut = 15;
    BYTE    szTimeOut[12+1];
	memset(szDisMsg, 0x00, sizeof(szDisMsg));


	memset(szTotalAmt, 0x00, sizeof(szTotalAmt));
    wub_hex_2_str(srTransRec.szTotalAmount, szTotalAmt, AMT_BCD_SIZE);
	
	
	vdTrimLeadZeroes(szTotalAmt);
	
	memset(szStr, 0x00, sizeof(szStr));
	vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szTotalAmt, szStr);
	vdDebug_LogPrintf("szStr[%s]", szStr);
	strcat(szDisMsg, "VOID PAYMENT");
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "TOTAL: $");
	strcat(szDisMsg, szStr);
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "CARD NO:");	
	strcat(szDisMsg, "|");

    memset(szPanMaskStr, 0, sizeof(szPanMaskStr));
    vdCTOS_FormatPAN(strIIT.szPANFormat, srTransRec.szPAN, szPanMaskStr);
//    vdCTOSS_PrintFormatPANNoSpace(srTransRec.szPAN, szPanMaskStr, d_LINE_SIZE, 0);
	strcat(szDisMsg, szPanMaskStr);


	strcat(szDisMsg, "|");
	strcat(szDisMsg, "CONFIRM?");
    vdGetPaymentModeIcon(txnType, szDisMsg);

    //Timeout
    memset(szTimeOut, 0, sizeof(szTimeOut));
    strcat(szDisMsg, "|");
    //NETS default 10, Credit is 30
    inTimeOut = strTXN.inCreditConfirmAmtTO;
    sprintf(szTimeOut, "%d", inTimeOut);
    strcat(szDisMsg, szTimeOut);

    inCallJAVA_ConfirmAmountUIVoid(szDisMsg, szOutBuf, &byLen);

	vdBeepSound();
	vdDebug_LogPrintf("ConfirmAmountUIVoid szOutBuf[%s]", szOutBuf);

    if (strcmp(szOutBuf, "TO") == 0)
        return d_NO;

    if (strcmp(szOutBuf, "CANCEL") == 0)
        return d_NO;

    if(strcmp(szOutBuf, "BACK") == 0)
        return d_NO;

    return d_OK;
}


BYTE ApprovedUIVoid(int txnType)
{
    BYTE byLen = 0;
    BYTE szOutBuf[128] = {0};
	BYTE szDisMsg[100];
    BYTE    szTotalAmt[12+1];
    BYTE    szStr[45];
    BYTE    szPanMaskStr[45];

	memset(szDisMsg, 0x00, sizeof(szDisMsg));

	vdSetErrorMessage("");//fix after display apprvoed/not approved/cancel  UI, don't display error message

	memset(szTotalAmt, 0x00, sizeof(szTotalAmt));
    wub_hex_2_str(srTransRec.szTotalAmount, szTotalAmt, AMT_BCD_SIZE);
	
	
	vdTrimLeadZeroes(szTotalAmt);
	
	memset(szStr, 0x00, sizeof(szStr));
	vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szTotalAmt, szStr);
	vdDebug_LogPrintf("szStr[%s]", szStr);
	strcat(szDisMsg, "VOID PAYMENT");
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "TOTAL: $");
	strcat(szDisMsg, szStr);
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "CARD NO:");	
	strcat(szDisMsg, "|");

    memset(szPanMaskStr, 0, sizeof(szPanMaskStr));
    vdCTOS_FormatPAN(strIIT.szPANFormat, srTransRec.szPAN, szPanMaskStr);
    strcat(szDisMsg, szPanMaskStr);

    vdGetPaymentModeIcon(txnType, szDisMsg);
	//strcat(szDisMsg, szStr);

    if(strTXN.fTXNVoice == 0)
    {
        strcat(szDisMsg, "|");
        strcat(szDisMsg, "NOSOUND");
    }

    inCallJAVA_ApproveUIVoid(szDisMsg, szOutBuf, &byLen);
	CTOS_Delay(2000);

	
	vdDebug_LogPrintf("ApprovedUIVoid szOutBuf[%s]", szOutBuf);

    return d_OK;
}




BYTE ReadCardErrorVoid(void)
{
    BYTE byLen = 0;
    BYTE szOutBuf[128] = {0};
	BYTE szDisMsg[100];
    BYTE    szTotalAmt[12+1];
    BYTE    szStr[45];


	memset(szDisMsg, 0x00, sizeof(szDisMsg));


	memset(szTotalAmt, 0x00, sizeof(szTotalAmt));
    wub_hex_2_str(srTransRec.szTotalAmount, szTotalAmt, AMT_BCD_SIZE);
	
	
	vdTrimLeadZeroes(szTotalAmt);
	
	memset(szStr, 0x00, sizeof(szStr));
	vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szTotalAmt, szStr);
	vdDebug_LogPrintf("szStr[%s]", szStr);
	strcat(szDisMsg, "VOID PAYMENT");
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "ADMIN: $");
	strcat(szDisMsg, szStr);



    inCallJAVA_ReadCardError(szDisMsg, szOutBuf, &byLen);

	
	vdDebug_LogPrintf("ReadCardError szOutBuf[%s]", szOutBuf);

    return d_OK;
}



BYTE TransCancelledVoid(void)
{
    BYTE byLen = 0;
    BYTE szOutBuf[128] = {0};
	BYTE szDisMsg[100];
    BYTE    szTotalAmt[12+1];
    BYTE    szStr[45];


	memset(szDisMsg, 0x00, sizeof(szDisMsg));

	vdSetErrorMessage("");//fix after display apprvoed/not approved/cancel  UI, don't display error message

	memset(szTotalAmt, 0x00, sizeof(szTotalAmt));
    wub_hex_2_str(srTransRec.szTotalAmount, szTotalAmt, AMT_BCD_SIZE);
	
	
	vdTrimLeadZeroes(szTotalAmt);
	
	memset(szStr, 0x00, sizeof(szStr));
	vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szTotalAmt, szStr);
	vdDebug_LogPrintf("szStr[%s]", szStr);
	strcat(szDisMsg, "VOID PAYMENT");
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "ADMIN: $");
	strcat(szDisMsg, szStr);
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "CARD NO:");	
	strcat(szDisMsg, "|");
	strcat(szDisMsg, srTransRec.szPAN);	
	strcat(szDisMsg, szStr);

	

    inCallJAVA_TransCancellVoid(szDisMsg, szOutBuf, &byLen);

	
	vdDebug_LogPrintf("TransCancelledVoid szOutBuf[%s]", szOutBuf);

    return d_OK;
}



BYTE NotApprovedUIVoid(int txnType)
{
    BYTE byLen = 0;
    BYTE szOutBuf[128] = {0};
	BYTE szDisMsg[100];
    BYTE    szTotalAmt[12+1];
    BYTE    szStr[45];
    BYTE    szPanMaskStr[45];


	memset(szDisMsg, 0x00, sizeof(szDisMsg));

	vdSetErrorMessage("");//fix after display apprvoed/not approved/cancel  UI, don't display error message

	memset(szTotalAmt, 0x00, sizeof(szTotalAmt));
    wub_hex_2_str(srTransRec.szTotalAmount, szTotalAmt, AMT_BCD_SIZE);
	
	
	vdTrimLeadZeroes(szTotalAmt);
	
	memset(szStr, 0x00, sizeof(szStr));
	vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szTotalAmt, szStr);
	vdDebug_LogPrintf("szStr[%s]", szStr);
	strcat(szDisMsg, "VOID PAYMENT");
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "ADMIN: $");
	strcat(szDisMsg, szStr);
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "CARD NO:");	
	strcat(szDisMsg, "|");
	memset(szPanMaskStr, 0, sizeof(szPanMaskStr));
    vdCTOS_FormatPAN(strIIT.szPANFormat, srTransRec.szPAN, szPanMaskStr);
	strcat(szDisMsg, szPanMaskStr);

    vdGetPaymentModeIcon(txnType, szDisMsg);

	
    if(strTXN.fTXNVoice == 0)
    {
        strcat(szDisMsg, "|");
        strcat(szDisMsg, "NOSOUND");
    }
	

    inCallJAVA_NotApproveUIVoid(szDisMsg, szOutBuf, &byLen);
	CTOS_Delay(2000);

	
	vdDebug_LogPrintf("ApprovedUI szOutBuf[%s]", szOutBuf);

    return d_OK;
}

void vdGetPaymentModeIcon(int type, char *pBuff)
{

    switch (type) {
        case PAYMENT_MODE_VISA_ICON:
            strcat(pBuff, "|");
            strcat(pBuff, "VISA");
            break;
        case PAYMENT_MODE_MASTER_ICON:
            strcat(pBuff, "|");
            strcat(pBuff, "MASTER");
            break;
        case PAYMENT_MODE_JCB_ICON:
            strcat(pBuff, "|");
            strcat(pBuff, "JCB");
            break;
        case PAYMENT_MODE_AMEX_ICON:
            strcat(pBuff, "|");
            strcat(pBuff, "AMEX");
            break;
        case PAYMENT_MODE_CREDIT_ICON:
            strcat(pBuff, "|");
            strcat(pBuff, "CREDIT");
            break;
        default:
            strcat(pBuff, "|");
            strcat(pBuff, "CREDIT");
            break;
    }
}

BYTE TransNotFound(int txnType)
{
    BYTE byLen = 0;
    BYTE szOutBuf[128] = {0};
	BYTE szDisMsg[100];
    BYTE    szTotalAmt[12+1];
    BYTE    szStr[45];

	vdDebug_LogPrintf("TransNotFound txnType[%d]", txnType);

	memset(szDisMsg, 0x00, sizeof(szDisMsg));


	memset(szTotalAmt, 0x00, sizeof(szTotalAmt));
    wub_hex_2_str(srTransRec.szTotalAmount, szTotalAmt, AMT_BCD_SIZE);
	
	
	vdTrimLeadZeroes(szTotalAmt);
	
	memset(szStr, 0x00, sizeof(szStr));
	vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szTotalAmt, szStr);
	vdDebug_LogPrintf("szStr[%s]", szStr);
	strcat(szDisMsg, "VOID PAYMENT");
	strcat(szDisMsg, "|");
	strcat(szDisMsg, "TOTAL: $");	
	strcat(szDisMsg, szStr);
    vdGetPaymentModeIcon(txnType, szDisMsg);
    inCallJAVA_TransNotFound(szDisMsg, szOutBuf, &byLen);

	vdErrorBeepSound();
	CTOS_Delay(2000);

	
	vdDebug_LogPrintf("TransNotFound szOutBuf[%s]", szOutBuf);


    return d_OK;
}



USHORT DisplayRemoveCard(BYTE *szStringMsg)
{

    BYTE byLen = 0;
    int inRet = 0;

    BYTE szOutBuf_disp[512];

    vdDebug_LogPrintf("=====DisplayRemoveCard=====");

    inRet = inCallJAVA_DisplayRemoveCardMsg(szStringMsg, szOutBuf_disp, &byLen);

    //vdDebug_LogPrintf("=====End DisplayMsg  szOutBuf[%s]=====", szOutBuf_disp);

    return d_OK;
}

void vdBeepSound(void)
{
	CTOS_Beep();
}

void vdErrorBeepSound(void)
{
	CTOS_Beep();
	CTOS_Delay(150);
	CTOS_Beep();
}


