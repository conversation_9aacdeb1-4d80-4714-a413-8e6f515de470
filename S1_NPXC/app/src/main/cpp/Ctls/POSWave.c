#include <string.h>
#include <stdio.h>
#include <stdlib.h>
#include <stdarg.h>
#include <ctosapi.h>
#include <vwdleapi.h>
#include <sqlite3.h>

#include "..\Includes\POSTypedef.h"
#include "..\Debug\Debug.h"

#include "..\Includes\POSMain.h"
#include "..\Includes\POSTrans.h"
#include "..\Includes\POSHost.h"
#include "..\Includes\POSSale.h"
#include "..\Includes\POSbatch.h"
#include "..\ui\Display.h"
#include "..\Includes\V5IsoFunc.h"
#include "..\Accum\Accum.h"
#include "..\print\Print.h"
#include "..\Comm\V5Comm.h"
#include "..\Includes\MultiApLib.h"
#include "..\Aptrans\MultiAptrans.h"
#include "..\Includes\Wub_lib.h"
#include "..\Database\DatabaseFunc.h"
#include "..\ApTrans\MultiShareEMV.h"
#include "..\Includes\CardUtil.h"
#include "..\Includes\POSSetting.h"
#include "..\PCI100\COMMS.h"
#include "..\erm\Poserm.h"
#include "..\Includes\POSSetting.h"

//#include "..\netsecr\ecr_cb.h"
//#include "..\netsecr\ecr.h"

#include "POSCtls.h"
#include "POSWave.h"

int inWaveTransType = 0;
extern STRUCT_DISPLAY_EXT strDisplayEXT;
int ginDoingHouseKeeping =0;

extern int inPRECONNECT;
int inEnMaestro;


int inNPXRemoveCard = 0;

void vdSetHouseKeeping(int type)
{
	ginDoingHouseKeeping = type;
}


void vdCTOSS_SetWaveTransType(int type)
{
	inWaveTransType = type;
}

int inCTOSS_GetWaveTransType(void)
{
	return inWaveTransType;
}

void vdSetNPXRemoveCard(int type)
{
	inNPXRemoveCard = type;
}

int inGetNPXRemoveCard(void)
{
	return inNPXRemoveCard;
}


int inCTOS_MultiAPReloadWaveData(void)
{
	if (srTransRec.usChipDataLen > 0)
	{			
		vdCTOSS_WaveGetEMVData(srTransRec.baChipData, srTransRec.usChipDataLen);
	}
		
	if (srTransRec.usAdditionalDataLen > 0)
	{			
		vdCTOSS_WaveGetEMVData(srTransRec.baAdditionalData, srTransRec.usAdditionalDataLen);
	}
	
	return (d_OK);
}
void vdRemoveWhiteSpaces(char * szCharBuff)
{
	char szTemp[128 + 1];
	int inLen = 0;
	int i, j;

	vdDebug_LogPrintf("REMOVE SPACES = [%s]", szCharBuff);

	memset(szTemp, 0x00, sizeof(szTemp));
	inLen = strlen(szCharBuff);

	for(i = 0, j = 0; i < inLen; i++)
	{
		if(szCharBuff[i] != SPACE)
		{
			szTemp[j] = szCharBuff[i];
			j++;
		}
	}

	memset(szCharBuff, 0x00, sizeof(*szCharBuff));
	memcpy(szCharBuff, szTemp, inLen);

	vdDebug_LogPrintf("NO SPACES = [%s]", szCharBuff);
}

int inNETS_AutoProcessNonNETSCard(void)
{
	int inRet = d_OK;
	int inNPXApp = 0;
	int inGPAPApp = 0;

	int inCallOther = 0;

	vdDebug_LogPrintf("=====inNETS_AutoProcessNonNETSCard=====");

	vdDebug_LogPrintf("srTransRec.byEntryMode[%d]", srTransRec.byEntryMode);
	vdDebug_LogPrintf("srTransRec.bWaveSID[%d]", srTransRec.bWaveSID);

	if (0 == fGetAppRunBySelf() && inMultiAP_CheckSubAPStatus() == d_OK)
        return d_OK;
	
	/*insert card, check AID see if need call non nets*/
	if (CARD_ENTRY_ICC == srTransRec.byEntryMode)
	{
		vdDebug_LogPrintf("insert card, check AID see if it's credit");
		DebugAddHEX("srTransRec.stEMVinfo.T84", srTransRec.stEMVinfo.T84, 7);
		if (0 == memcmp("\xA0\x00\x00\x05\x41", srTransRec.stEMVinfo.T84, 5))
		{
			vdDebug_LogPrintf(" NETS RID, cannot process with credit");
			inCallOther = 1;
		}
	}
	
	/*tap card, check SID see if it's non nets*/
	if (CARD_ENTRY_WAVE == srTransRec.byEntryMode)
	{
		if (d_EMVCL_SID_MEPS_MCCS == srTransRec.bWaveSID)
		{
			vdDebug_LogPrintf("tap NETS card, cannot process with credit");
			inCallOther = 1;
		}
		
	}

	if (1 == inCallOther)
	{
		vdDebug_LogPrintf("Cannot use nets card in Credit Card/UPI menu");
		memset(&strDisplayEXT, 0x00, sizeof(strDisplayEXT));
		strcpy(strDisplayEXT.szMessage4, "DECLINED");
		strDisplayEXT.ucSoundType = 0xFD;
		vduiDisplayStringCenterEXT();
		CTOS_LCDTClearDisplay();
		vdDisplayErrorMsg(1, 7, "DECLINED");
		return d_NO;
	}

	return d_OK;
}

int inNETS_AutoProcessNonMaestroCard(void)
{
	int inRet = d_OK;
	int inNPXApp = 0;
	int inGPAPApp = 0;

	int inCallOther = 0;

	vdDebug_LogPrintf("=====inNETS_AutoProcessNonMaestroCard=====");

	vdDebug_LogPrintf("srTransRec.byEntryMode[%d], inEnMaestro[%d]", srTransRec.byEntryMode, inEnMaestro);
	vdDebug_LogPrintf("srTransRec.bWaveSID[%d]", srTransRec.bWaveSID);

	/*insert card, check AID see if need call non nets*/
	if (CARD_ENTRY_ICC == srTransRec.byEntryMode)
	{
		vdDebug_LogPrintf("insert card, check AID see if it's non Maestro");
		DebugAddHEX("srTransRec.stEMVinfo.T84", srTransRec.stEMVinfo.T84, 7);
		if (inEnMaestro != 1 && 0 == memcmp("\xA0\x00\x00\x00\x04\x30\x60", srTransRec.stEMVinfo.T84, 7))
		{
			vdDebug_LogPrintf(" Maestro RID, cannot process with credit");
			inCallOther = 1;
		}
	}
	
	/*tap card, check SID see if it's non maestro*/
	if (CARD_ENTRY_WAVE == srTransRec.byEntryMode)
	{
		// 20210204 NETS TCT doesn't have Maestro contactless and WAVE.S3DB doesn't support Maestro AID, so skip check for now
		/*if (inEnMaestro != 1 && 0x90 == srTransRec.bWaveSID)
		{
			vdDebug_LogPrintf("tap Maestro card, cannot process with credit");
			inCallOther = 1;
		}*/
		
	}

	if (1 == inCallOther)
	{
		vdDebug_LogPrintf("Maestro card flag disabled in Credit Card/UPI menu");
		memset(&strDisplayEXT, 0x00, sizeof(strDisplayEXT));
		strcpy(strDisplayEXT.szMessage4, "APPL NOT SUPPORT");
		strDisplayEXT.ucSoundType = 0xFD;
		vduiDisplayStringCenterEXT();
		CTOS_LCDTClearDisplay();
		vdDisplayErrorMsg(1, 7, "APPL NOT SUPPORT");
		return d_NO;
	}

	return d_OK;
}

void vdInitCDGTrans(CDG_TRANS *psrCDGTrans, PAYMENT_INFO *psrPmtInfo){
	char szAmount[20];
	extern unsigned char gucUserCN2PaymentMode;
	vdDebug_LogPrintf("=====vdInitCDGTrans=====");
	vdGetCDGTransaction(psrCDGTrans);	// get GstMessage value - needed to print receipt because NETS app got the value during UpdateFare (UpdateFare is done in NETS app not here)
	
	sprintf(szAmount, "%06ld", psrPmtInfo->lnAmount);
	memcpy(psrCDGTrans->szFareAmt, szAmount, 6);
	sprintf(szAmount, "%06d", 0);
	memcpy(psrCDGTrans->szAdminAmt, szAmount, 6);
	memcpy(psrCDGTrans->szGSTAmt, szAmount, 6);

	sprintf(szAmount, "%012d", 0);
	memcpy(psrCDGTrans->szTotalFare, szAmount, 12);

	memcpy(psrCDGTrans->szJobNumber, psrPmtInfo->szJobNumber, 10);
	memcpy(psrCDGTrans->szTaxiNumber, psrPmtInfo->szTaxiNumber, 12);
	memcpy(psrCDGTrans->szDriverID, psrPmtInfo->szDriverId, 9);
	memset(psrCDGTrans->szCompanyCode, 0x20, 6);
	memcpy(psrCDGTrans->szCompanyCode, psrPmtInfo->szCompanyCode, 4);

	psrCDGTrans->ucBookingJob = psrPmtInfo->chBookingJob;

#ifdef DO_VIRTUAL_CABCHARGE
	psrCDGTrans->ucUseCN2PaymentMode = psrPmtInfo->chUseCN2PaymentMode;
	gucUserCN2PaymentMode = psrPmtInfo->chUseCN2PaymentMode;
	memcpy(psrCDGTrans->szTripInformation, psrPmtInfo->szTripInformation, 200);
	memcpy(psrCDGTrans->szPaymentInfo, psrPmtInfo->szPaymentInfo, 20);
#endif

	memcpy(psrCDGTrans->szPrivateField, psrPmtInfo->szPrivateField, 102);	//for NOF & Paylah

	vdSetCDGTransaction(psrCDGTrans);

	//for void
	inCTOSS_StoreBatchFieldData(&srTransFlexiData, FLEXI_PAYMENT_INFO, &srPmtInfo, sizeof(srPmtInfo));
	inCTOSS_StoreBatchFieldData(&srTransFlexiData, FLEXI_PAYMENT_ADDIONAL_INFO, &srPmtAddionalInfo, sizeof(srPmtAddionalInfo));
}

int inCTOS_WaveFlowProcess(void)
{
    int inRet = d_NO;

    USHORT ushEMVtagLen;
    BYTE   EMVtagVal[64];
    BYTE   szStr[64];
	int inEVENT_ID = 0;
	//int inConnectType = get_env_int("CONNECTTYPE");	
	//inConnectType = 0;

	memset(&stRCDataAnalyze,0x00,sizeof(EMVCL_RC_DATA_ANALYZE));

	vdSetNPXRemoveCard(0);

    vdNPX_EmptyBatch();
    
    inRet = inCTOSS_CheckMemoryStatus();
    if(d_OK != inRet)
        return inRet;

	inRet = inCheckFinancialTranAllowed();
      if(d_OK != inRet)
         return inRet;

    inRet = inCTOS_GetTxnPassword();
    if(d_OK != inRet)
        return inRet;

	inRet = inCTOS_GetTxnBaseAmount();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_GetTxnTipAmount();
    if(d_OK != inRet)
        return inRet;

	if (strTCTEX.fHawkerMode == 1)
	{
	inRet = inNETS_InputStoreInvNum();
    if(d_OK != inRet)
        return inRet;

	#ifdef HAWKER_CFC
	if (strTCTEX.fHawkerMode == 1 && strTCTEX.fRefNum == 1)
	{
		CTOS_LCDBackGndColor(RGB(255, 255, 255));
		CTOS_LCDForeGndColor(RGB(0, 0, 0));
		CTOS_LCDTSelectFontSize(d_FONT_16x30);
		CTOS_LCDTClearDisplay();
		vdDispTransTitle(srTransRec.byTransType);
	}
	#endif
	
	}

    inRet = inCTOS_UpdateTxnTotalAmount();
    if(d_OK != inRet)
        return inRet;
    
    inRet = inCTOS_WaveGetCardFields();
	if(inRet == READ_CARD_TIMEOUT ||inRet == USER_ABORT)
	{
		TransCancelledUI(inCTOSS_GetTxnType());
	}
    if(d_OK != inRet)
        return inRet;

	
	//usCTOSS_BackToProgress("TEST");
    inRet = inCTOS_SelectHost();
    if(d_OK != inRet)
        return inRet;

    if (inMultiAP_CheckMainAPStatus() == d_OK)
    {
    	if (srTransRec.byTransType == REFUND)
			inEVENT_ID = d_IPC_CMD_WAVE_REFUND;
		else
			inEVENT_ID = d_IPC_CMD_WAVE_SALE;
        inRet = inCTOS_MultiAPSaveData(inEVENT_ID);
        if(d_OK != inRet)
            return inRet;
    }
    else
    {
        if (0 == fGetAppRunBySelf() && inMultiAP_CheckSubAPStatus() == d_OK)
        {
            inRet = inCTOS_MultiAPGetData();
            if(d_OK != inRet)
                return inRet;

            //inRet = inCTOS_MultiAPReloadTable();
            inRet = inCTOS_MultiAPFastReloadTable();
            if(d_OK != inRet)
                return inRet;
                
			inRet = inCTOS_MultiAPReloadWaveData();
            if(d_OK != inRet)
                return inRet;
        }
		#if 0
        inRet = inCTOS_MultiAPCheckAllowd();
        if(d_OK != inRet)
            return inRet;
		#endif
    }

	inRet = inNETS_AutoProcessNonNETSCard();
    if(d_OK != inRet)
        return inRet;

	inRet = inNETS_AutoProcessNonMaestroCard();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_CheckAndSelectMutipleMID();
    if(d_OK != inRet)
        return inRet;

	//if(inConnectType == 0)
	{
	    if (inPRECONNECT == 1)
		{
	 	inRet = inCTOS_PreConnectEx();
	    if(d_OK != inRet)
	        return inRet;
		}
		else
		{
	 	inRet = inCTOS_PreConnect();
	    if(d_OK != inRet)
	        return inRet;
		}
	}

    inRet = inCTOS_CheckIssuerEnable();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_CheckTranAllowd();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_CheckMustSettle();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_GetInvoice();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_GetCVV2();
    if(d_OK != inRet)
        return inRet;

    inRet = inCTOS_CustComputeAndDispTotal();
    if(d_OK != inRet)
        return inRet;
 
    inRet = inCTOS_EMVProcessing();
    if(d_OK != inRet)
        return inRet;   
	
	inRet = inCTOSS_GetOnlinePIN();
    if(d_OK != inRet)
        return inRet;  

	//inCTOSS_MDTDoUpdateFinalFare();
    inRet = inBuildAndSendIsoData();
	
	
	vdDebug_LogPrintf("inBuildAndSendIsoData inRet[%d]", inRet);
	//return d_OK;
//	inNETS_2PCSSyncRespMsg();
    if(d_OK != inRet)
    {
        NotApprovedUI(inCTOSS_GetTxnType());
        return inRet;
    }
	
	//inRet = inCTOS_EMVTCUploadComfort();	
	//vdDebug_LogPrintf("inCTOS_EMVTCUploadComfort inRet[%d]", inRet);
	//if(d_OK != inRet)
	//	return inRet;

	

    inRet = inCTOS_SaveBatchTxn();
    if(d_OK != inRet)
        return inRet;
    #if 0

    inRet = inCTOS_UpdateAccumTotal();	
    vdDebug_LogPrintf("inCTOS_UpdateAccumTotal inRet[%d]", inRet);
    if(d_OK != inRet)
        return inRet;

	#endif

//	inNPX_SyncTransRec_EXT_Terminal();
	
	//if (VS_FALSE == byECRCheckNeedRcptText())
	{
	vdDebug_LogPrintf("byECRCheckNeedRcptText");
	inMultiAP_ECRSendSuccessResponse();
	inMultiAP_MDBSendSuccessResponse();
	}

	inRet = inCTOS_EMVTCUploadComfort();	
	vdDebug_LogPrintf("inCTOS_EMVTCUploadComfort inRet[%d]", inRet);
	if(d_OK != inRet)
		return inRet;
	//inRet = ushCTOS_ePadSignature();	
    //vdDebug_LogPrintf("ushCTOS_ePadSignature inRet[%d]", inRet);
    //if(d_OK != inRet)
    //    return inRet;

	//inRet = inCTOSS_ERM_ReceiptRecvVia();
    //vdDebug_LogPrintf("inCTOSS_ERM_ReceiptRecvVia inRet[%d]", inRet);
	//if(d_OK != inRet)
    //    return inRet;

	//if (isCheckTerminalMP200() == d_OK || isCheckTerminalUPT1000() == d_OK)
	//{
	//	vdCTOSS_DisplayStatus(d_OK);
	//}
	
    //inRet = ushCTOS_printReceipt();	
   // vdDebug_LogPrintf("ushCTOS_printReceipt inRet[%d]", inRet);
   // if(d_OK != inRet)
     //   return inRet;

	/*this part should quick send response after 1st receipt out. 2020-04-16*/
	//if (VS_TRUE == byECRCheckNeedRcptText())
	//{	
	//vdDebug_LogPrintf("byECRCheckNeedRcptText");
	//inRet = inMultiAP_ECRSendSuccessResponse();
	//inRet = inMultiAP_MDBSendSuccessResponse();
	//}

       #if 0
	/*it is better ask user remove card before upload TC*/
	if(CARD_ENTRY_ICC == srTransRec.byEntryMode)
	{
        vdRemoveCard();

		//vdSetNPXRemoveCard(1);
	}
   #endif
    #if 0
    inRet = inCTOS_EMVTCUpload();	
    vdDebug_LogPrintf("inCTOS_EMVTCUpload inRet[%d]", inRet);
	#endif
    if(d_OK != inRet)
        return inRet;
    else
        vdSetErrorMessage("");

    return d_OK;
}


int inCTOS_WAVE_SALE(void)
{
    int inRet = d_NO;
	CDG_TRANS srMyCDGTrans;

    //CTOS_LCDTClearDisplay();

    vdCTOS_TxnsBeginInit();

	vdCTOS_SetTransType(SALE);
	vdCTOSS_SetWaveTransType(1);
	vdDebug_LogPrintf("=====inCTOS_WAVE_SALE=====");
	//vdCTOSS_GetAmt();

	inRet = inCTOSS_CLMOpenAndGetVersion();
	if(d_OK != inRet)
        return inRet;

	inRet = inCTOSS_ERM_CheckSlipImage();
	if(d_OK != inRet)
        return inRet;

	memset(&srMyCDGTrans,0,sizeof(CDG_TRANS));
	vdInitCDGTrans(&srMyCDGTrans, &srPmtInfo);
	
	//display title
	if (memcmp(srTransRec.szBaseAmount, "\x00\x00\x00\x00\x00\x00", 6) == 0) //prevent display title overlapping with idle screen
    	vdDispTransTitle(SALE);
	
    inRet = inCTOS_WaveFlowProcess();
	//return d_OK;
    vdDebug_LogPrintf("inCTOS_WaveFlowProcess inRet[%d]", inRet);

	vdCTOSS_CLMClose();

    inCTOS_inDisconnect();

	if(d_OK == inRet)
	inCTOSS_UploadReceipt();

	if (isCheckTerminalMP200() == d_OK || isCheckTerminalUPT1000() == d_OK)
	{
		//CTOS_KBDBufFlush();
		if (d_OK != inRet)
		vdCTOSS_DisplayStatus(inRet);
		//WaitKey(5);
	}

	//inNPX_DataSyncSale_Terminal_EXT(srTransRec);
	//inNPX_DataSync_EXT_Terminal(srTransRec);
    vdDebug_LogPrintf("vdCTOS_TransEndReset inRet[%d]", inRet);
    vdCTOS_TransEndReset();

    return inRet;
}



int inCTOS_WAVE_REFUND(void)
{
    int inRet = d_NO;

    //CTOS_LCDTClearDisplay();

	if (0 == inCTOSS_GetCtlsMode())
	{
		inCTOS_SALE();
	}

    vdCTOS_TxnsBeginInit();

	vdCTOS_SetTransType(REFUND);
	vdCTOSS_SetWaveTransType(1);

	vdCTOSS_GetAmt();

	inRet = inCTOSS_CLMOpenAndGetVersion();
	if(d_OK != inRet)
        return inRet;
	//display title
    vdDispTransTitle(REFUND);

    inRet = inCTOS_WaveFlowProcess();

	vdCTOSS_CLMClose();

    inCTOS_inDisconnect();

    vdCTOS_TransEndReset();

    return inRet;
}
#if 0
void vdCTOS_InitWaveData(void)
{
	BYTE bInBuf[250];
	BYTE bOutBuf[250];
	BYTE *ptr = NULL;
	USHORT usInLen = 0;
	USHORT usOutLen = 0;
	USHORT usResult;
	int inCTLSmode = 0;

	
	inCTLSmode = inCTOSS_GetCtlsMode();
	if (NO_CTLS == inCTLSmode)
		return;

	//Open the Back Light in the LCD Display //
	vduiKeyboardBackLight(VS_TRUE);
	vduiLightOn();
	CTOS_LCDTClearDisplay();
    vdDispTitleString((BYTE *)"CONTACTLESS");
	setLCDPrint(5, DISPLAY_POSITION_LEFT, "CONFIGURE READER...");
	
	memset(bOutBuf, 0x00, sizeof(bOutBuf));
	memset(bInBuf, 0x00, sizeof(bInBuf));

	
	vdDebug_LogPrintf("d_IPC_CMD_EMV_WAVESETTAGS usInLen[%d],inCTLSmode=[%d] ",usInLen,inCTLSmode);

	if (CTLS_INTERNAL == inCTLSmode || CTLS_EXTERNAL == inCTLSmode)
	{
		usResult = inMultiAP_RunIPCCmdTypes("com.Source.SHARLS_EMV.SHARLS_EMV", d_IPC_CMD_EMV_WAVESETTAGS, bInBuf, usInLen, bOutBuf, &usOutLen);
	}

	if (CTLS_V3_SHARECTLS == inCTLSmode)
	{
		usResult = inMultiAP_RunIPCCmdTypes("com.Source.SHARLS_CTLS.SHARLS_CTLS", d_IPC_CMD_EMV_WAVESETTAGS, bInBuf, usInLen, bOutBuf, &usOutLen);
	}
	
}
#endif

void vdCTOS_InitWaveData(void)
{
	BYTE bInBuf[250];
	BYTE bOutBuf[250];
	BYTE *ptr = NULL;
	USHORT usInLen = 0;
	USHORT usOutLen = 0;
	USHORT usResult;
	int inCTLSmode = 0;
	
	inCTLSmode = inCTOSS_GetCtlsMode();
	if (NO_CTLS == inCTLSmode)
		return;

	//Open the Back Light in the LCD Display //
	vduiKeyboardBackLight(VS_TRUE);
	vduiLightOn();
	CTOS_LCDTClearDisplay();
    vdDispTitleString((BYTE *)"CONTACTLESS");
	setLCDPrint(5, DISPLAY_POSITION_LEFT, "CONFIGURE READER...");
	
	memset(bOutBuf, 0x00, sizeof(bOutBuf));
	memset(bInBuf, 0x00, sizeof(bInBuf));
	
	vdDebug_LogPrintf("d_IPC_CMD_EMV_WAVESETTAGS usInLen[%d],inCTLSmode=[%d] ",usInLen,inCTLSmode);

	if(strTCT.byPinPadMode == 0)
	{
		if(strTCT.byPinPadType == 3 && CTLS_V3_SHARECTLS == inCTLSmode)
		{
			char szV3PSendBuf[1024+1];
			int inOffSet = 0;
			int status;
			char szRecvBuf[1024+1];
			int inRecvlen = 0;
			unsigned char *pszPtr;
			
			vdCTOSS_SetV3PRS232Port(strTCT.byPinPadPort);
			inCTOSS_V3PRS232Open(strTCT.inPPBaudRate, 'N', 8, 1);

			// send STX INJECT_KEY ETX LRC
			memset(szV3PSendBuf, 0x00, sizeof(szV3PSendBuf));
			inOffSet = 0;
			szV3PSendBuf[inOffSet] = STX;
			inOffSet += 1;
			memcpy(&szV3PSendBuf[inOffSet], "INIT_WAVE", strlen("INIT_WAVE"));
			inOffSet += strlen("INIT_WAVE");
			szV3PSendBuf[inOffSet] = ETX;
			inOffSet += 1;			
			szV3PSendBuf[inOffSet] = (char) wub_lrc(&(szV3PSendBuf[1]), inOffSet-1);
			inOffSet += 1;
			inCTOSS_V3PRS232SendBuf(szV3PSendBuf, inOffSet);	
			status = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, (d_CEPAS_READ_BALANCE_CARD_PRESENT_TIMEOUT));						

			//inCTOSS_USBHostCloseEx();
			inCTOSS_V3PRS232Close();
			return ;
		}
	}

	if (CTLS_INTERNAL == inCTLSmode || CTLS_EXTERNAL == inCTLSmode)
	{
		usResult = inMultiAP_RunIPCCmdTypes("com.Source.SHARLS_EMV.SHARLS_EMV", d_IPC_CMD_EMV_WAVESETTAGS, bInBuf, usInLen, bOutBuf, &usOutLen);
	}

	if (CTLS_V3_SHARECTLS == inCTLSmode || CTLS_V3_INT_SHARECTLS == inCTLSmode)
	{
		usResult = inMultiAP_RunIPCCmdTypes("com.Source.SHARLS_CTLS.SHARLS_CTLS", d_IPC_CMD_EMV_WAVESETTAGS, bInBuf, usInLen, bOutBuf, &usOutLen);
	}	

}



void vdCTOS_PartialInitWaveData(void)
{
	BYTE bInBuf[250];
	BYTE bOutBuf[250];
	BYTE *ptr = NULL;
	USHORT usInLen = 0;
	USHORT usOutLen = 0;
	USHORT usResult;
	int inCTLSmode = 0;
	
	inCTLSmode = inCTOSS_GetCtlsMode();
	if (NO_CTLS == inCTLSmode)
		return;

	//inDatabase_WaveDelete("MCCS", "DF04");
	//inDatabase_WaveUpdate("MCCS", "DFAF11", "888888888888");
	//inDatabase_WaveUpdate("MCCS", "DFAF13", "555555555555");
	//inDatabase_WaveUpdate("MCCS", "DFAF12", "000000000011");
	//inDatabase_WaveInsert("MCCS", "DF05", "11");

	CTOS_LCDTClearDisplay();
    vdDispTitleString((BYTE *)"CONTACTLESS");
	setLCDPrint(5, DISPLAY_POSITION_LEFT, "CONFIGURE READER...");
	
	memset(bOutBuf, 0x00, sizeof(bOutBuf));
	memset(bInBuf, 0x00, sizeof(bInBuf));
	
	vdDebug_LogPrintf("d_IPC_CMD_EMV_WAVESETTAGS usInLen[%d],inCTLSmode=[%d],GetCtlsMode=[%d] ",usInLen,inCTLSmode,inCTOSS_GetCtlsMode());
#if 0
	if(strTCT.byPinPadMode == 0)
	{
		if(strTCT.byPinPadType == 3 && CTLS_V3_SHARECTLS == inCTLSmode)
		{
			char szV3PSendBuf[1024+1];
			int inOffSet = 0;
			int status;
			char szRecvBuf[1024+1];
			int inRecvlen = 0;
			unsigned char *pszPtr;
			
			vdCTOSS_SetV3PRS232Port(strTCT.byPinPadPort);
			inCTOSS_V3PRS232Open(strTCT.inPPBaudRate, 'N', 8, 1);

			// send STX INJECT_KEY ETX LRC
			memset(szV3PSendBuf, 0x00, sizeof(szV3PSendBuf));
			inOffSet = 0;
			szV3PSendBuf[inOffSet] = STX;
			inOffSet += 1;
			memcpy(&szV3PSendBuf[inOffSet], "INIT_WAVE", strlen("INIT_WAVE"));
			inOffSet += strlen("INIT_WAVE");
			szV3PSendBuf[inOffSet] = ETX;
			inOffSet += 1;			
			szV3PSendBuf[inOffSet] = (char) wub_lrc(&(szV3PSendBuf[1]), inOffSet-1);
			inOffSet += 1;
			inCTOSS_V3PRS232SendBuf(szV3PSendBuf, inOffSet);	
			status = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, (d_CEPAS_READ_BALANCE_CARD_PRESENT_TIMEOUT));						

			//inCTOSS_USBHostCloseEx();
			inCTOSS_V3PRS232Close();
			return ;
		}
	}

	if (CTLS_INTERNAL == inCTLSmode || CTLS_EXTERNAL == inCTLSmode)
	{
		usResult = inMultiAP_RunIPCCmdTypes("com.Source.SHARLS_EMV.SHARLS_EMV", d_IPC_CMD_EMV_WAVESETTAGS, bInBuf, usInLen, bOutBuf, &usOutLen);
	}
#endif
	if (CTLS_V3_SHARECTLS == inCTLSmode || CTLS_V3_INT_SHARECTLS == inCTLSmode)
	{
		usResult = inMultiAP_RunIPCCmdTypes("com.Source.SHARLS_CTLS.SHARLS_CTLS", d_IPC_CMD_CTLS_PARTIALSETTAGS, bInBuf, usInLen, bOutBuf, &usOutLen);
	}	

}

int inDoCreditUpload(void)
{
	int inRet =  -1;

    vdCTOS_TxnsBeginInit();

	// process upload offline for NPX now
	vdCTOS_SetTransType(SALE_OFFLINE);	
	inCTOS_SelectNPXAppHost();

	inRet = inCTOS_PreConnect();
    if(d_OK != inRet)
        return ST_ERROR;

	inCTOS_CheckAndSelectMutipleMID();
	if (inProcessNPXOfflineSaleUpload(&srTransRec) != ST_SUCCESS)
	{
		inCTOS_inDisconnect();
		return ST_ERROR;
	}

	inCTOS_inDisconnect();

	return d_OK;
}

int inDoCreditTCUpload(void)
{
	int inRet =  -1;

    vdCTOS_TxnsBeginInit();

	// process upload offline for NPX now
	vdCTOS_SetTransType(TC_UPLOAD);	
	inCTOS_SelectNPXAppHost();

	inRet = inCTOS_PreConnect();
    if(d_OK != inRet)
        return ST_ERROR;

	inCTOS_CheckAndSelectMutipleMID();
	if (inProcessNPXTCUpload(&srTransRec) != ST_SUCCESS)
	{
		inCTOS_inDisconnect();
		return ST_ERROR;
	}

	inCTOS_inDisconnect();

	return d_OK;
}

int inCreditReversal(void)
{
	int inRet =  -1;

    vdCTOS_TxnsBeginInit();

	// process reversal for Alipay now
	vdCTOS_SetTransType(REVERSAL);	
	inCTOS_SelectNPXAppHost();

	inRet = inCTOS_PreConnect();
    if(d_OK != inRet)
        return ST_ERROR;

	inCTOS_CheckAndSelectMutipleMID();
	vdSetSaveReversal(1);	
	if (inProcessReversal(&srTransRec) != ST_SUCCESS)
	{
		vdSetSaveReversal(0);			
		//NotApprovedUI(inCTOSS_GetTxnType());
		inCTOS_inDisconnect();
		return ST_ERROR;
	}
	vdSetSaveReversal(0);
	inCTOS_inDisconnect();

	return d_OK;
}

int inCreditSendPendingTrans(void)
{
	int inRet = d_NO;
	vdDebug_LogPrintf("inCreditSendPendingTrans....");
	//refer vx820
	//inRunFunction(pobTran, CREDIT_GET_PENDING_REV);
	//inRunFunction(pobTran, CREDIT_GET_PENDING_OFFLINE);
	//inRunFunction(pobTran, CREDIT_GET_PENDING_TCUPLOAD);
	ginDoingHouseKeeping =1;
	inRet = inCreditReversal();
	vdDebug_LogPrintf("inCreditReversal....inRet=[%d],ginDoingHouseKeeping=[%d]",inRet,ginDoingHouseKeeping);
	if (ginDoingHouseKeeping ==0)
		return d_NO;
	//if (inRet == d_OK)
	{
		inRet = inDoCreditUpload();
		vdDebug_LogPrintf("inDoCreditUpload....inRet=[%d],ginDoingHouseKeeping=[%d]",inRet,ginDoingHouseKeeping);
		if (ginDoingHouseKeeping ==0)
			return d_NO;
		//if (inRet == d_OK)
			inRet = inDoCreditTCUpload();

		vdDebug_LogPrintf("inDoCreditTCUpload....inRet=[%d],ginDoingHouseKeeping=[%d]",inRet,ginDoingHouseKeeping);
		if (ginDoingHouseKeeping ==0)
			return d_NO;
	}

	return inRet;
}


int inDoAppUpdate(void)
{
    int inRet =  -1;

    //Call main to do file copy
	inCallJAVA_CopyFileUI();
    return d_OK;
}
