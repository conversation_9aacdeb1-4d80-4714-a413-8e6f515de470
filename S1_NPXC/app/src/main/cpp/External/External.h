
#ifndef __EXTERNAL_H__
#define	__EXTERNAL_H__

#ifdef	__cplusplus
extern "C" {
#endif
#include <ctos_qrcode.h>



typedef struct tagOnlinePINParam_2PC
{
	BYTE byAcctType;
	BYTE szPINBlock[8+1];
	int inPINEntered;
	int inSupportPINBypass;
	BYTE baKSN[10];
	BYTE baRFU1[64];
	BYTE baRFU2[64];
}OLPIN_PARAM_2PC;

/*for EMV online PIN, 
better handle in share EMV but we need the para from main application*/
typedef struct tagOnlinePINParam
{
	BYTE byPinPadMode;
	BYTE byPinPadType;
	BYTE byPinPadPort;
	BYTE szPAN[19+1];
	BYTE szTransTitle[40+1];
	USHORT ushKeySet;
	USHORT ushKeyIdx;
	long lnTxnAmt;
	BYTE szFmtAmtStr[40+1];
	int inMinDigit;
	int inMaxDigit;
	BYTE byAcctType;
	BYTE szPINBlock[12+1];
	int inPINEntered;
	int inSupportPINBypass;
	BYTE baKSN[10];
	BYTE baRFU1[64];
	BYTE baRFU2[64];
}OLPIN_PARAM;

OLPIN_PARAM	stOLPinParam;


/*for EMV offline PIN, 
better handle in share EMV but we need the para from main application*/
typedef struct tagOfflinePINParam
{
	BYTE byPinPadMode;
	BYTE byPinPadType;
	BYTE byPinPadPort;
	BYTE szPAN[19+1];
	BYTE szTransTitle[40+1];
	USHORT ushKeySet;
	USHORT ushKeyIdx;
	long lnTxnAmt;
	BYTE szFmtAmtStr[40+1];
	int inMinDigit;
	int inMaxDigit;
	BYTE byAcctType;
	BYTE szPINBlock[8+1];
	int inPINEntered;
	int inSupportPINBypass;
	BYTE baKSN[10];
	BYTE baRFU1[64];
	BYTE baRFU2[64];
}OFPIN_PARAM;

OFPIN_PARAM	stOFPinParam;


typedef struct
{	 
	 int IPC_EVENT_ID;
	 int usDataLen1;
	 int usDataLen2;
	 int usDataLen3;
	 BYTE pbaData1[200+1];
	 BYTE pbaData2[1024+1];
	 BYTE pbaData3[512+1];
} EMV_Trans;
EMV_Trans strEMV_Trans;

//
//typedef struct
//{
//	unsigned char ucSoundType;
//	unsigned char szMessage1[30+1];
//	unsigned char szMessage2[30+1];
//	unsigned char szMessage3[30+1];
//	unsigned char szMessage4[30+1];
//	unsigned char szMessage5[30+1];
//	unsigned char szMessage6[30+1];
//	unsigned char szMessage7[30+1];
//	unsigned char szMessage8[30+1];
//}STRUCT_DISPLAY_EXT;
//
//
//STRUCT_DISPLAY_EXT strDisplayEXT;

typedef struct
{
	USHORT usX;
	USHORT usY;
	BYTE szQRBuf[1024+1];
	CTOS_QRCODE_INFO qrcodeInfo;
}QRDISP_EXT;
QRDISP_EXT	stQRDisp;



#define DB_SIGN_BMP "signtest.bmp"
#define DB_SIGN_BMP_GZ "signtest.bmp.gz"



USHORT usCTOSS_CtlsV3TransEXT(BYTE *inbuf, USHORT inlen);
int inCTOSS_EXTSignatureEXT(void);
int inCTOSS_EXTSignature(void);

int inCTOSS_PinPadLoad_3DES_TMK_Plaintext(BYTE *inbuf, USHORT inlen);
int inCTOSS_PinPadGetIPPPin(BYTE *inbuf, USHORT inlen);

int inCTOSS_EXTGetIPPPin(void);


int inCTOSS_EXTLoad_3DES_TMK_Plaintext(char *szKeyData,int HDTid);


int inCTOS_WaveGetCardFieldsEXT(BYTE *inbuf, USHORT inlen);
int inCTOS_WaveGetCardFieldsExternal(void);

int inCTOS_EMVGetCardEXT(BYTE *inbuf, USHORT inlen);
int inCTOS_EMVGetCardExternal(char *Appname, int IPC_EVENT_ID, BYTE *inbuf, USHORT inlen, BYTE *outbuf, USHORT *outlen);


#ifdef	__cplusplus
}
#endif

#endif

