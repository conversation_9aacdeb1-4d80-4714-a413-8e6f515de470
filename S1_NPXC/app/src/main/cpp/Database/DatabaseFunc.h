
#ifndef _DATABASEFUNC__H
#define	_DATABASEFUNC__H

#ifdef	__cplusplus
extern "C" {
#endif
#include "../Database/dct.h"
#include "../Includes/POSTypedef.h"

#include "../Includes/Trans.h"
#include "../FileModule/myFileFunc.h"

//��ע�⣬��Ҫ����ļ���Ĵ�Сд
#define DB_TERMINAL "/data/data/pub/TERMINAL.S3DB"
#define DB_BATCH	"/data/data/com.Source.S1_NPX.NPX/V5S_VISAMASTER.S3DB"
#define DB_EMV	"/data/data/pub/EMV.S3DB"
#define DB_COM	"/data/data/pub/COM.S3DB"

#define DB_MULTIAP	"/data/data/pub/MULTIAP.S3DB"
#define DB_WAVE		"/data/data/pub/WAVE.S3DB"

#define DB_ERM	"/data/data/pub/ERM.S3DB"
#define DB_CTLS	"/data/data/pub/CTLS.S3DB"
#define DB_EFT_LIB "/data/data/pub/EFT.S3DB"

#define DB_MULTIAP_JOURNAL "/data/data/pub/MULTIAP.S3DB-journal"

#define DB_TMLTMP	"/data/data/pub/TMLTMP.S3DB"
#define DB_CTLS_JOURNAL "/data/data/pub/CTLS.S3DB-journal"
#define DB_BATCH_FLEXI 	"/data/data/com.Source.S1_NPX.NPX/NPXFLEXI.S3DB"
#define DB_NETS	"/data/data/pub/NETS.S3DB"

// patrick for checking temporary store in app folder
/**/#define DB_STUCK_TXNS "/data/data/pub/STUCK.S3DB"
//#define DB_STUCK_TXNS "/data/data/com.Source.S1_NETS.NETS/STUCK.S3DB"

#define DB_TMS	"/data/data/pub/TMS.S3DB"

#define CURRENT_TRANSACTION_DATA	"/data/data/pub/CURTRAN.DAT"

int inAIDNumRecord(void);
int inHDTNumRecord(void);
int inTCTNumRecord(void);
int inCDTNumRecord(void);
int inRDTNumRecord(void);
int inEMVNumRecord(void);
int inAIDNumRecord(void);
int inMSGNumRecord(void);
int inIITNumRecord(void);
int inMITRead(int inSeekCnt);

int inCPTRead(int inSeekCnt);
int inCPTSave(int inSeekCnt);


int inHDTRead(int inSeekCnt);
int inHDTSave(int inSeekCnt);
int inCDTRead(int inSeekCnt);
int inCDTReadMulti(char *szPAN, int *inFindRecordNum);
int inCDTReadMultiByType(char *szPAN, int *inFindRecordNum, int inType);
int inCDTMAX(void);

int inEMVRead(int inSeekCnt);
int inAIDRead(int inSeekCnt);
int inAIDReadbyRID(int inSeekCnt, char * PRid);
int inTCTRead(int inSeekCnt);
int inTCTSave(int inSeekCnt);
int inTCTEXRead(int inSeekCnt);
int inTCTEXSave(int inSeekCnt);
int inTCPRead(int inSeekCnt);
int inTCPSave(int inSeekCnt);
int inIITRead(int inSeekCnt);
int inIITSave(int inSeekCnt);

int inCSTNumRecord(void);
int inCSTRead(int inSeekCnt);
int inCSTSave(int inSeekCnt);

int inMMTReadRecord(int inHDTid,int inMITid);

int inMMTNumRecord(void);

int inMMTReadNumofRecords(int inSeekCnt,int *inFindRecordNum);

int inMMTSave(int inSeekCnt);

int inPITNumRecord(void);
int inPITRead(int inSeekCnt);
int inPITSave(int inSeekCnt);

int inMSGResponseCodeRead(char* szMsg, int inMsgIndex, int inHostIndex);
int inMSGResponseCodeReadByActionCode(char* szMsg, int inActionCode, int inHostIndex);

int inDatabase_BatchDeleteHDTidMITid(void);
int inDatabase_BatchDelete(void);
int inDatabase_BatchInsert(TRANS_DATA_TABLE *transData);
int inDatabase_BatchSave(TRANS_DATA_TABLE *transData, int inStoredType);
int inDatabase_BatchRead(TRANS_DATA_TABLE *transData, int inSeekCnt);
int inDatabase_BatchSave(TRANS_DATA_TABLE *transData, int inStoredType);
int inDatabase_BatchSearch(TRANS_DATA_TABLE *transData, char *hexInvoiceNo);
int inDatabase_BatchSearchByTraceNum(TRANS_DATA_TABLE *transData, ULONG ulTraceNum);
int inDatabase_BatchCheckDuplicateInvoice(char *hexInvoiceNo);
int inBatchNumRecord(void);
int inDatabase_BatchReadByHostidAndMITid(TRANS_DATA_TABLE *transData,int inHDTid,int inMITid);
int inBatchByMerchandHost(int inNumber, int inHostIndex, int inMerchIndex, char *szBatchNo, int *inTranID);
int inDatabase_BatchReadByTransId(TRANS_DATA_TABLE *transData, int inTransDataid);


int inHDTReadHostName(char szHostName[][100], int inCPTID[]);
int inERMAP_Database_BatchDelete(void);
int inERMAP_Database_BatchInsert(ERM_TransData *strERMTransData);

int inMultiAP_Database_BatchRead(TRANS_DATA_TABLE *transData);
int inMultiAP_Database_BatchUpdate(TRANS_DATA_TABLE *transData);
int inMultiAP_Database_BatchDelete(void);
int inMultiAP_Database_BatchInsert(TRANS_DATA_TABLE *transData);

int inMultiAP_Database_EMVTransferDataInit(void);
int inMultiAP_Database_EMVTransferDataWrite(USHORT usDataLen, BYTE *bEMVData);
int inMultiAP_Database_EMVTransferDataRead(USHORT *usDataLen, BYTE *bEMVData);

int inMultiAP_Database_COM_Read(void);
int inMultiAP_Database_COM_Save(void);
int inMultiAP_Database_COM_Clear(void);
int inHDTReadByApname(char *szAPName);

int inMultiAP_Database_CTLS_Delete(void);
int inMultiAP_Database_CTLS_Read(CTLS_TransData *strCTLSTransData);
int inMultiAP_Database_CTLS_Insert(CTLS_TransData *strCTLSTransData);

int inEFTReadNumofRecords(int *inFindRecordNum);
int inEFTReadNumofRecordsByHostId(int *inFindRecordNum, int inHostId);

int inDB_GetTableAllRecid(char *pszDBName, char *pszTblName, int inRecID[]);
int inDB_DeleteConfRecOne(char *szDBName, char *szTabName, int inRecNum);
int inDB_CheckTableExist(char *pszTabName);
int inDB_ReadTableRecBySeq(char *pszTabName, int inSeqNum, char *pstBuf, int inStructSize);
int inDB_DeleteTableRecByReq(char *pszTabName, int inSeqNum);
int inDB_CreateTable(char *pszTabName, int inStructSize);
int inDB_SaveTableRecBySeq(char *pszTabName, int inSeqNum, char *pstBuf, int inStructSize);
int inDB_RemoveTable(char *pszTabName);


int inMMTReadSelectedData(int inSeekCnt);
int inMPTRead(int inSeekCnt);
int inMPTSave(int inSeekCnt);

int inVoidBatchNumRecord(void);
int inVoidBatchByTrxID(int inNumber, int *inTranID);

int inCTOSS_StoreBatchFieldData(TRANS_FLEXI_DATA_TABLE* srFlexiData, USHORT uiFieldID, BYTE* pchData, USHORT shDataLength);
int inCTOSS_GetBatchFieldData(TRANS_FLEXI_DATA_TABLE* srFlexiData,USHORT uiFieldID, BYTE* pchData,USHORT shMaxLength);

int inDatabase_FlexiBatchDeleteHDTidMITid(void);
int inDatabase_FlexiBatchDelete(void);
int inDatabase_FlexiBatchRead(TRANS_FLEXI_DATA_TABLE *transFlexiData, int inSeekCnt);
int inDatabase_FlexiBatchInsert(TRANS_FLEXI_DATA_TABLE *transFlexiData);
int inDatabase_FlexiBatchUpdate(TRANS_FLEXI_DATA_TABLE *transFlexiData);
int inDatabase_FlexiBatchSave(TRANS_FLEXI_DATA_TABLE *transFlexiData, int inStoredType);
int inDatabase_FlexiBatchSearch(TRANS_FLEXI_DATA_TABLE *transFlexiData, char *hexInvoiceNo);
int inDatabase_FlexiBatchSearchByTraceNum(TRANS_FLEXI_DATA_TABLE *transFlexiData, ULONG ulTraceNum);
int inDatabase_FlexiBatchReadByTransId(TRANS_FLEXI_DATA_TABLE *transFlexiData, int inTransDataid);
int inEFTReadNumofRecordsByHostIdAll(int *inFindRecordNum, int inHostId);



#ifdef	__cplusplus
}
#endif

#endif	/* _DATABASEFUNC__H */

