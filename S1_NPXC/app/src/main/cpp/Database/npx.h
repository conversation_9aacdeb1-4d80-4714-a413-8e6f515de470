
#ifndef _NPX_H
#define	_NPX_H

#ifdef	__cplusplus
extern "C" {
#endif


#define NPX_TAG_SIZE				4
#define NPX_LLVAR_LEN_SIZE			2
#define NPX_LLLVAR_LEN_SIZE			3
#define NPX_LLLLVAR_LEN_SIZE		4


#define NPX_DE58_MAX_SIZE			999

#define NPX_VAR_LEN					1
#define NPX_FIX_LEN					(!NPX_VAR_LEN)

#define NPX_M_FD					1	/*MANDATORY Field*/
#define NPX_O_FD					(!NPX_M_FD)


/*NPX Field size*/
#define NPX_USERNAME_SIZE			20
#define NPX_PWD_SIZE				40
#define NPX_POS_ID_SIZE				8
#define NPX_RSC_SIZE				20
#define NPX_CONV_TRANS_ID_SIZE		10
#define NPX_SOURCE_AMT_SIZE			12
#define NPX_SOURCE_CURR_SIZE		3
#define NPX_TARGET_CURR_SIZE		3
#define NPX_CURR_GROUP_NUM_SIZE		2
#define NPX_TARGET_CURR_GROUP_SIZE	99
#define NPX_TARGET_CURR_TEXT_SIZE	999
#define NPX_TARGET_CURR_CODE_SIZE	99
#define NPX_EXCHANGE_RATE_SIZE		999
#define NPX_RCPT_HEADER_01_SIZE		99
#define NPX_RCPT_HEADER_02_SIZE		99
#define NPX_RCPT_HEADER_03_SIZE		99
#define NPX_RCPT_HEADER_04_SIZE		99
#define NPX_SECURITYCODE_SIZE		6


#define NPX_PWAP_ERR_MSG_SIZE		99
#define NPX_PWAP_ERR_CODE_SIZE		4




/*NPX Tags*/
#define TAG_USERNAME			"E101"	/*user name*/
#define TAG_PASSWORD			"E102"	/*password*/
#define TAG_POS_ID				"E103"	/*pos id*/
#define TAG_CONV_TRANS_ID		"E104"	/*transaction id base on convertion*/
#define TAG_REDUND_SECURITYCODE		"E105"	/*Refund Security Code*/
#define TAG_BATCH_NUMBER		"E107"	/*EDC Batch Number*/

#define TAG_SOURCE_AOUNT		"E201"	/*source amount*/
#define TAG_SOURCE_CURRENCY		"E202"	/**/
#define TAG_TARGET_CURRENCY		"E204"	/**/
#define TAG_CARD_TYPE			"E601"

#define TAG_TARGET_AMOUNT 		"E118"	/*source amount*/
#define TAG_TARGET_ORIGINAL_AMOUNT 		"E119"	/**/
#define TAG_TARGET_TIPS_AMOUNT		"E120"	/**/
#define TAG_CONVERSION_RATE			"E121"


#define TAG_UNIQUE_SETTLE_ID	"E501"


#define TAG_EPP_PLAN_ID			"E603"
#define TAG_EPP_TERM			"E604"
#define TAG_EPP_ACQID			"E605"

#define TAG_RRN_TRANS_ID		"F101"
#define TAG_CURR_GROUP_NUM		"F200"
#define TAG_TARGET_CURR_GROUP	"F201"
#define TAG_TARGET_CURR_TEXT	"F202"
#define TAG_TARGET_CURR_CODE	"F203"

#define TAG_EXCHANGE_RATE		"F204"
#define TAG_RCPT_HEADER_01		"F209"
#define TAG_RCPT_HEADER_02		"F210"
#define TAG_RCPT_HEADER_03		"F211"
#define TAG_RCPT_HEADER_04		"F212"

#define TAG_RESP_PLAN_ID			"F304"
#define TAG_RESP_TERM				"F305"
#define TAG_RESP_INTEREST_RATE		"F306"
#define TAG_RESP_INTEREST_FREE_MONTH	"F307"
#define TAG_RESP_HANDLING_FEE_TYPE	"F308"
#define TAG_RESP_HANDLING_FEE_AMT	"F309"
#define TAG_RESP_HANDLING_FEE_PER	"F310"
#define TAG_RESP_FIRST_MONTH_AMT	"F312"
#define TAG_RESP_LAST_MONTH_AMT		"F313"
#define TAG_RESP_TOTAL_INST_AMT		"F314"
#define TAG_RESP_OUTSTAND_PRINCIPAL	"F315"
#define TAG_RESP_INTEREST_AMT		"F316"



#define TAG_PWAP_ERR_MSG		"F998"
#define TAG_PWAP_ERR_CODE		"F999"

#define FIELD59_DATA  "field59.txt"
#define FIELD59_DATA_GZ  "field59.txt.gz"



typedef int (*PFI_PSZ_INT)(char *, int);

typedef struct NPXTLVFormat
{
    char    szTag[NPX_TAG_SIZE + 1];
    int     inLen;
    char    *pValue;
	VS_BOOL	fFixLen;
	VS_BOOL fOption;
    PFI_PSZ_INT fpOperFunc;
}NPX_TLV_FMT;

#define NPX_TLV_FMT_SIZE    sizeof(NPX_TLV_FMT)





/*NPX Gateway Config table*/

typedef struct
{
	UINT GCTid;
	BYTE szUserName[NPX_USERNAME_SIZE + 1];
	BYTE szPassword[NPX_PWD_SIZE + 1];
	BYTE szPOSid[NPX_PWD_SIZE + 1];
	BYTE szRSC[NPX_RSC_SIZE + 1];
	
}STRUCT_GCT;

STRUCT_GCT strGCT;

int inGCTRead(int inSeekCnt);
int inGCTSave(int inSeekCnt);
int inGCTNumRecord(void);

int inNPXPackCreditSaleDE58(TRANS_DATA_TABLE *transData, char *pszDest);
int inNPXPackOfflineSaleDE58(TRANS_DATA_TABLE *transData, char *pszDest);
int inNPXPackRefundDE58(TRANS_DATA_TABLE *transData, char *pszDest);
int inNPXPackAuthoriseDE58(TRANS_DATA_TABLE *transData, char *pszDest);



#ifdef	__cplusplus
}
#endif

#endif	/* _NPX_H */


