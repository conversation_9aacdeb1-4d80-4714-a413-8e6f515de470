
#ifndef POSPOWRFAIL_H
#define POSPOWRFAIL_H

#include "../Includes/POSTypedef.h"


#define PFR_FIME_NAME	"powrfail.fail"
#define PFR_FLEXI_FIME_NAME	"pfrflexi.fail"


typedef enum
{
    PFR_IDLE_STATE	 = 0,
	PFR_BEGIN_SEND_ISO,
	PFR_RECEIVE_ISO,
	PFR_BEGIN_BATCH_UPDATE,
    PFR_BATCH_UPDATE_COMPLETE,	  
    PFR_BATCH_SETTLEMENT_PRINT,//1204
	PFR_SIGNATURE_COMPLETE, 
	PFR_PRINT_MERCHANTCOPY,      
}PFR_CODE_TABLE;

typedef struct TagPOWRFAIL_REC
{
	short inPowrfailtag;
	TRANS_DATA_TABLE srTransPowerRec;
	
} POWRFAIL_REC;

typedef struct TagPOWRFAIL_FLEXI_REC
{
	short inPowrfailtag;
	TRANS_FLEXI_DATA_TABLE srTransFlexiPowerRec;
	
} POWRFAIL_FLEXI_REC;


int inCTLOS_Updatepowrfail(short inPowrfailtag);
int inCTLOS_Getpowrfail(void);


#endif


