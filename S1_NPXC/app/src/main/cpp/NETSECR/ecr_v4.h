
#ifndef _ECR_V4_H
#define	_ECR_V4_H

#ifdef	__cplusplus
extern "C" {
#endif


/*Token ID defines*/
#define ID_PADDING					0x0000

#define ID_DEVICE_DATE				0x1000
#define ID_DEVICE_TIME				0x1001
#define ID_DEVICE_MID				0x1002
#define ID_DEVICE_RID				0x1003
#define ID_DEVICE_TID				0x1004
#define ID_DEVICE_MER_CODE			0x1005
#define ID_DEVICE_MER_NAME			0x1006
#define ID_DEVICE_MER_ADDRESS		0x1007

#define ID_SOF_TYPE					0x2000
#define ID_SOF_DESCRIPTION			0x2001
#define ID_SOF_PRIORITY				0x2002
#define ID_SOF_ACQUIRER				0x2003
#define ID_SOF_NAME					0x2004
#define ID_SOF_SALE_COUNT			0x2005
#define ID_SOF_SALE_TOTAL			0x2006
#define ID_SOF_REFUND_COUNT			0x2007
#define ID_SOF_REFUND_TOTAL			0x2008
#define ID_SOF_VOID_COUNT			0x2009
#define ID_SOF_VOID_TOTAL			0x200A
#define ID_SOF_TID					0x200B
#define ID_SOF_MID					0x200C


#define ID_AUTH_DATA_1				0x3000
#define ID_AUTH_DATA_2				0x3001
#define ID_AUTH_DATA_3				0x3002

#define ID_CARD_TYPE				0x4000
#define ID_CARD_CAN					0x4001
#define ID_CARD_CSN					0x4002
#define ID_CARD_BALANCE				0x4003
#define ID_CARD_COUNTER				0x4004
#define ID_CARD_EXPIRY				0x4005
#define ID_CARD_CERT				0x4006
#define ID_CARD_PREVIOUS_BAL		0x4007
#define ID_CARD_PURSE_STATUS		0x4008
#define ID_CARD_ATU_STATUS			0x4009
#define ID_CARD_NUM_MASKED			0x400A
#define ID_CARD_HOLDER_NAME			0x400B
#define ID_CARD_SCHEME_NAME			0x400C
#define ID_CARD_AID					0x400D
#define ID_CARD_CEPAS_VER			0x400E

#define ID_TXN_TYPE					0x5000
#define ID_TXN_AMOUNT				0x5001
#define ID_TXN_CASHBACK_AMOUNT		0x5002
#define ID_TXN_DATE					0x5003
#define ID_TXN_TIME					0x5004
#define ID_TXN_BATCH				0x5005
#define ID_TXN_CERT					0x5006
#define ID_TXN_CHECKSUM				0x5007
#define ID_TXN_DATA					0x5008
#define ID_TXN_STAN					0x5009
#define ID_TXN_MER					0x500A
#define ID_TXN_MER_REF_NUM			0x500B
#define ID_TXN_RESPONSE_TEXT		0x500C
#define ID_TXN_MER_NAME				0x500D
#define ID_TXN_MER_ADDRESS			0x500E
#define ID_TXN_TID					0x500F
#define ID_TXN_MID					0x5010
#define ID_TXN_APPROV_CODE			0x5011
#define ID_TXN_RRN					0x5012
#define ID_TXN_CARD_NAME			0x5013
#define ID_TXN_SERVICE_FEE			0x5014
#define ID_TXN_MARKETING_MSG		0x5015
#define ID_TXN_HOST_RESP_MSG_1		0x5016
#define ID_TXN_HOST_RESP_MSG_2		0x5017
#define ID_TXN_HOST_RESP_CODE		0x5018
#define ID_TXN_CARD_ENTRY_MODE		0x5019
#define ID_TXN_RECEIPT				0x501A
#define ID_TXN_AUTOLOAD_AMOUT		0x501B
#define ID_TXN_INV_NUM				0x501C
#define ID_TXN_TC					0x501D
#define ID_TXN_FOREIGN_AMOUNT		0x501E
#define ID_TXN_FOREIGN_MID			0x501F
#define ID_FOREIGN_CUR_NAME			0x5020
#define ID_TXN_POSID				0x5021
#define ID_TXN_ACQUIRER				0x5022
#define ID_TXN_HOST					0x5023
#define ID_TXN_LAST_HEADER			0x5024
#define ID_TXN_LAST_PAYLOAD			0x5025
#define ID_TXN_RECEIPT_REQUIRED		0x5026
#define ID_TXN_FEE_DUE_TO_MERCH		0x5027
#define ID_TXN_FEE_DUE_FROM_MERCH	0x5028


/*Token Type define*/
#define TOKEN_TYPE_NONE				0x30
#define TOKEN_TYPE_ARRAY			0x31
#define TOKEN_TYPE_DATE_01			0x32
#define TOKEN_TYPE_DATE_02			0x33
#define TOKEN_TYPE_DATE_03			0x34
#define TOKEN_TYPE_DATE_04			0x35
#define TOKEN_TYPE_TIME_01			0x36
#define TOKEN_TYPE_TIME_02			0x37
#define TOKEN_TYPE_IPV4_01			0x38
#define TOKEN_TYPE_VALUE			0x39


/*Token Encoding*/
#define ENCODING_NONE					0x30
#define ENCODING_ARRAY_ASCII			0x31
#define ENCODING_ARRAY_ASCII_HEX		0x32
#define ENCODING_ARRAY_HEX				0x33
#define ENCODING_VALUE_ASCII			0x34
#define ENCODING_VALUE_ASCII_HEX		0x35
#define ENCODING_VALUE_BCD				0x36
#define ENCODING_VALUE_HEX_BIG			0x37
#define ENCODING_VALUE_HEX_LITTLE		0x38


/*ECR V40 Error Code - Header Response Code*/
#define V40_SUCCESS						0x00000000
#define V40_PENDING						0x00000001
#define V40_TIMEOUT						0x00000002
#define V40_INVALID_PARAMETER			0x00000003
#define V40_INCORRECT_FLOW 				0x00000004
#define V40_MSG_INTEGRITY_FAILED		0x10000000
#define V40_MSG_TYPE_NOT_SUPPORTED		0x10000001
#define V40_MSG_CODE_NOT_SUPPORTED		0x10000002
#define V40_MSG_AUTHENTICATION_REQUIRED	0x10000003
#define V40_MSG_AUTHENTICATION_FAILED	0x10000004
#define V40_MSG_MAC_FAILED				0x10000005
#define V40_MSG_LENGTH_MISMATCH			0x10000006
#define V40_MSG_LENGTH_MINIMUM			0x10000007
#define V40_MSG_LENGTH_MAXIMUM			0x10000008
#define V40_MSG_VERSION_INVALID 		0x10000009
#define V40_MSG_CLASS_INVALID			0x1000000A
#define V40_MSG_STATUS_INVALID			0x1000000B
#define V40_MSG_ALGORITHM_INVALID		0x1000000C
#define V40_MSG_ALGORITHM_MANDATORY		0x1000000D
#define V40_MSG_KEY_INDEX_INVALID		0x1000000E
#define V40_MSG_NOTIFICATION_INVALID	0x1000000F
#define V40_MSG_COMPLETION_INVALID		0x10000010
#define V40_MSG_DATA_TRANSPARENCY_ERROR 0x10000011
#define V40_TOKEN_MANDATORY_MISSING		0x11000001
#define V40_TOKEN_LENGTH_MINIMUM		0x11000002
#define V40_TOKEN_LENGTH_INVALID		0x11000003
#define V40_TOKEN_TYPE_INVALID			0x11000004
#define V40_TOKEN_ENCODING_INVALID		0x11000005
#define V40_TOKEN_DATA_INVALID			0x11000006
#define V40_TOKEN_PARSING_ERROR			0x11000007
#define V40_CARD_NOT_DETECTED			0x20000000
#define V40_CARD_ERROR					0x20000001
#define V40_CARD_BLACKLISTED			0x20000002
#define V40_CARD_BLOCKED				0x20000003
#define V40_CARD_EXPIRED				0x20000004
#define V40_CARD_INVALID_ISSUER_ID		0x20000005
#define V40_CARD_INVALID_PURSE_VALUE	0x20000006
#define V40_CARD_CREDIT_NOT_ALLOWED		0x20000007
#define V40_CARD_DEBIT_NOT_ALLOWED		0x20000008
#define V40_CARD_INSUFFICIENT_FUND		0x20000009
#define V40_CARD_EXCEEDED_PURSE_VALUE	0x2000000A
#define V40_CARD_CREDIT_FAILED			0x2000000B
#define V40_CARD_CREDIT_UNCONFIRMED		0x2000000C
#define V40_CARD_DEBIT_FAILED			0x2000000D
#define V40_CARD_DEBIT_UNCONFIRMED		0x2000000E
#define V40_COMM_NO_RESPONSE			0x30000000
#define V40_COMM_ERROR					0x30000001
#define V40_SOF_INVALID_CARD			0x40000000
#define V40_SOF_INCORRECT_PIN			0x40000001
#define V40_SOF_INSUFFICIENT_FUND 		0x40000002
#define V40_SOF_CLOSED					0x40000003
#define V40_SOF_BLOCKED					0x40000004
#define V40_SOF_REFER_TO_BANK			0x40000005
#define V40_SOF_CANCEL					0x40000006
#define V40_SOF_HOST_RESP_DECLINE		0x40000007
#define V40_SOF_LOGON_REQUIRED			0x40000008


/*Message Header*/
#define EV4_MSG_HEADER_SIZE				64

#define EV4_MSG_LEGNTH_SIZE				4
#define EV4_MSG_CRC32_SIZE				4
#define EV4_MSG_VER_SIZE				1
#define EV4_MSG_DIRECTION_SIZE			1
#define EV4_MSG_TIME_SIZE				8
#define EV4_MSG_SEQ_SIZE				4
#define EV4_MSG_CLASS_SIZE				2
#define EV4_MSG_TYPE_SIZE				4
#define EV4_MSG_CODE_SIZE				4
#define EV4_MSG_COMPLETION_SIZE			1
#define EV4_MSG_NOTIFICATION_SIZE		1
#define EV4_MSG_STATUS_SIZE				4
#define EV4_DEVICE_PROVIDER_SIZE		2
#define EV4_DEVICE_TYPE_SIZE			2
//#define EV4_DEVICE_LOCATION_SIZE		1
#define EV4_DEVICE_NUM_SIZE				4
#define EV4_ENCRYPT_ALGO_SIZE			1
#define EV4_ENCRYPT_KEY_IDX_SIZE		1
#define EV4_ENCRYPT_MAC_SIZE			8
#define EV4_HEADER_RESERVED_SIZE		8

typedef struct tagECRV40MsgHeader
{
	BYTE baLength[EV4_MSG_LEGNTH_SIZE];
	BYTE baCRC32[EV4_MSG_CRC32_SIZE];
	BYTE baMsgVer[EV4_MSG_VER_SIZE];
	BYTE baMsgDirection[EV4_MSG_DIRECTION_SIZE];
	BYTE baMsgTime[EV4_MSG_TIME_SIZE];
	BYTE baMsgSeq[EV4_MSG_SEQ_SIZE];
	BYTE baMsgClass[EV4_MSG_CLASS_SIZE];
	BYTE baMsgType[EV4_MSG_TYPE_SIZE];
	BYTE baMsgCode[EV4_MSG_CODE_SIZE];
	BYTE baMsgCompletion[EV4_MSG_COMPLETION_SIZE];
	BYTE baMsgNotification[EV4_MSG_NOTIFICATION_SIZE];
	BYTE baMsgStatus[EV4_MSG_STATUS_SIZE];
	BYTE baDeviceProvider[EV4_DEVICE_PROVIDER_SIZE];
	BYTE baDeviceType[EV4_DEVICE_TYPE_SIZE];
	//BYTE baDeviceLocation[EV4_DEVICE_LOCATION_SIZE];
	BYTE baDeviceNum[EV4_DEVICE_NUM_SIZE];
	BYTE baEncryptAlgo[EV4_ENCRYPT_ALGO_SIZE];
	BYTE baEncryptKeyIndex[EV4_ENCRYPT_KEY_IDX_SIZE];
	BYTE baEncryptMAC[EV4_ENCRYPT_MAC_SIZE];
	BYTE baReserved[EV4_HEADER_RESERVED_SIZE];
	
}ECRV40_MSG_HEADER;

/*Token Header*/
#define TOKEN_BLOCK_LENGTH_SIZE			4
#define TOKEN_ID_SIZE					2
#define TOKEN_TYPE_SIZE					1
#define TOKEN_ENCODE_SIZE				1
#define TOKEN_HEADER_SIZE				8

#define PAYLOAD_PADDING_SIZE			32

typedef struct tagTokenMap
{
	USHORT 	ushTokenID;
	BYTE	byType;
	BYTE 	byEncoding;
	BYTE	*ptrValue;
	UINT 	uiMaxLen;
	
}ECRV40_TOKEN_MAP;


/*Message class value define*/
#define MSG_CLASS_NONE			0x00
#define MSG_CLASS_REQ			0x01
#define MSG_CLASS_ACK			0x02
#define MSG_CLASS_RESP			0x03

/*Message type value define*/
#define MSG_TYPE_DEVICE			0x10000000
#define MSG_TYPE_AUTH			0x20000000
#define MSG_TYPE_CARD			0x30000000
#define MSG_TYPE_PAYMENT		0x40000000
#define MSG_TYPE_CANCELLATION	0x50000000
#define MSG_TYPE_TOPUP			0x60000000
#define MSG_TYPE_RECORD			0x70000000
#define MSG_TYPE_OTHER			0xF0000000


/*MSG_TYPE_DEVICE*/
#define MSG_CODE_DEVICE_STATUS			0x10000000
#define MSG_CODE_DEVICE_RESET			0x20000000
#define MSG_CODE_DEVICE_RESET_SEQ_NUM	0x20000001
#define MSG_CODE_DEVICE_TIME_SYNC		0x30000000
#define MSG_CODE_DEVICE_PROFILE			0x40000000
#define MSG_CODE_DEVICE_SOF_DETAIL		0x50000000
#define MSG_CODE_DEVICE_SOF_PRIORISATION	0x50000001
#define MSG_CODE_DEVICE_LOGON			0x60000000
#define MSG_CODE_DEVICE_TMS				0x60000001
#define MSG_CODE_DEVICE_SETTLEMENT		0x70000000
#define MSG_CODE_DEVICE_PRE_SETTLEMENT	0x70000001
#define MSG_CODE_DEVICE_GET_LAST_TRANS	0x70000002
#define MSG_CODE_DEVICE_GET_LAST_SETTLE	0x70000003


/*MSG_AUTH*/
#define MSG_CODE_AUTH_MUTUAL_STEP_1		0x10000000
#define MSG_CODE_AUTH_MUTUAL_STEP_2		0x20000000

/*MSG_TYPE_CARD*/
#define MSG_CODE_CARD_DETECT			0x10000000
#define MSG_CODE_CARD_READ_PURSE		0x20000000
#define MSG_CODE_CARD_READ_HISTORICAL_LOG 0x30000000
#define MSG_CODE_CARD_READ_RSVP			0x40000000
//this 2 command must together
#define MSG_CODE_CARD_DETECT_NFP_NCC	0x50000000
#define MSG_CODE_CARD_PERFORM_DEBIT		0x50000001


/*MSG_TYPE_PAYMENT*/
#define MSG_CODE_PAYMENT_AUTO		0x10000000
#define MSG_CODE_PAYMENT_EFT		0x20000000
#define MSG_CODE_PAYMENT_NCC		0x30000000
#define MSG_CODE_PAYMENT_NFP		0x40000000
#define MSG_CODE_PAYMENT_RSVP		0x50000000
#define MSG_CODE_PAYMENT_CRD		0x60000000
#define MSG_CODE_PAYMENT_DEB		0x70000000
#define MSG_CODE_PAYMENT_BCA		0x80000000
#define MSG_CODE_PAYMENT_EZL		0x90000000
#define MSG_CODE_PAYMENT_HOST		0xF0000000

/*MSG_TYPE_CANCELLATION*/
#define MSG_CODE_CANCELLATION_VOID		0x10000000
#define MSG_CODE_CANCELLATION_REFUND	0x20000000

/*MSG_TYPE_TOPUP*/
#define MSG_CODE_TOPUP_NCC 			0x10000000
#define MSG_CODE_TOPUP_NFP			0x20000000
#define MSG_CODE_TOPUP_RSVP			0x30000000

/*MSG_TYPE_RECORD*/
#define MSG_CODE_RECORD_SUMMARY		0x10000000
#define MSG_CODE_RECORD_UPLOAD		0x20000000
#define MSG_CODE_RECORD_CLEAR		0x30000000

/*MSG_TYPE_OTHERS*/
#define MSG_CODE_OTHER_CASH_DEPOSIT		0x10000000


#define USHORT_BYTES2_SIZE			2
#define UINT_BYTES4_SIZE			4
#define PADDING_SIZE				64
#define EV4_REFER_NUM_SIZE			20
#define EV4_TXN_DATE_SIZE			8
#define EV4_TXN_TIME_SIZE			6
#define EV4_TXN_STAN_SIZE			6
#define EV4_TXN_INVNUM_SIZE			6
#define EV4_TXN_TID_SIZE			8
#define EV4_TXN_RCPT_REQ_SIZE		1
#define EV4_SOF_ACQUIRER_SIZE		10
#define EV4_SOF_NAME_SIZE			10
#define EV4_FOREIGN_MID_SIZE		15
#define EV4_FOREIGN_AMT_SIZE		12
#define EV4_BATCH_NUM_SIZE			6
#define EV4_CEPAS_CHKSUM_SIZE		2
#define EV4_CEPAS_SIGNCERT_SIZE		8
#define EV4_CEPAS_CAN_SIZE			8
#define EV4_CEPAS_CSN_SIZE			8
#define EV4_CEPAS_COUNTER_SIZE		8
#define EV4_CEPAS_CARDCERT_SIZE		8
#define EV4_CARD_EXP_DATE_SIZE		8
#define EV4_PURSE_STATUS_SIZE		1
#define EV4_CEPAS_VER_SIZE			2
#define EV4_CARD_SCHEME_SIZE		20
#define EV4_TXN_HOST_STR_SIZE		20
#define EV4_SOF_DESC_SIZE			32

typedef struct tagECRV40ReqData
{
	BYTE baTxnType[USHORT_BYTES2_SIZE];
	BYTE baTxnAcq[UINT_BYTES4_SIZE];
	BYTE baTxnAmt[UINT_BYTES4_SIZE];
	BYTE baCashBackAmt[UINT_BYTES4_SIZE];
	BYTE szMerchRefNum[EV4_REFER_NUM_SIZE+1];
	BYTE szTxnDate[EV4_TXN_DATE_SIZE+1];
	BYTE szTxnTime[EV4_TXN_TIME_SIZE+1];
	BYTE szTxnSTAN[EV4_TXN_STAN_SIZE+1];
	BYTE szTxnInvNum[EV4_TXN_INVNUM_SIZE+1];
	BYTE baTxnRcptReq[EV4_TXN_RCPT_REQ_SIZE];
	BYTE szECRTID[EV4_TXN_TID_SIZE+1];
	BYTE szTxnHost[EV4_TXN_HOST_STR_SIZE+1];
	BYTE baCardType[USHORT_BYTES2_SIZE];

	BYTE baPadding[PADDING_SIZE];
}ECRV40_REQ_DATA;



#define TXN_TYPE_PAY_AUTO 			0x0000 /*Payment (Automatic Detect)*/
#define TXN_TYPE_PAY_NETS_EFT 		0x1000 /*Payment by NETS EFT*/
#define TXN_TYPE_PAY_NETS_NCC 		0x1100 /*Payment by NETS NCC*/
#define TXN_TYPE_PAY_NETS_NFP 		0x1200 /*Payment by NETS NFP*/
#define TXN_TYPE_PAY_NETS_RSVP 		0x1300 /*Payment by NETS RSVP*/
#define TXN_TYPE_PAY_NETS_VCC 		0x1400 /*Payment by NETS VCC*/
#define TXN_TYPE_PAY_NETS_PLUS 		0x1500 /*Payment by NETS Plus*/
#define TXN_TYPE_PAY_NETS_BCA 		0x1600 /*Payment by BCA*/
#define TXN_TYPE_PAY_SCHEME_CREDIT 	0x2000 /*Payment by Scheme Credit*/
#define TXN_TYPE_PAY_SCHEME_DEBIT 	0x3000 /*Payment by Scheme Debit*/
#define TXN_TYPE_TOPUP_NETS_NCC 	0x8000 /*Topup NETS NCC by NETS EFT*/
#define TXN_TYPE_TOPUP_NETS_NFP		0x8100 /*Topup NETS NFP by NETS EFT*/
#define TXN_TYPE_TOPUP_NETS_RSVP	0x8200 /*Topup NETS RSVP by NETS EFT*/


#define MAX_TOKEN_SECTION		0xA0
#define MAX_TOKEN_ITEMS			0x32

typedef struct tagTokenAttrib
{
	BYTE 	byType;
	BYTE 	byEncoding;
	BYTE	szDesc[32];
}ECRV40_TOKEN_ATTRIB;



typedef struct tagNETSECRV40RespData
{	
	BYTE szSOFACQ[EV4_SOF_ACQUIRER_SIZE+1];
	BYTE szSOFName[EV4_SOF_NAME_SIZE+1];
	BYTE szTxnDate[EV4_TXN_DATE_SIZE+1];
	BYTE baTxnAmt[UINT_BYTES4_SIZE];
	BYTE szForeignMID[EV4_FOREIGN_MID_SIZE + 1];
	BYTE szForeignAmt[EV4_FOREIGN_AMT_SIZE + 1];
	BYTE baBatchNum[EV4_BATCH_NUM_SIZE];
	BYTE baCEPASChkSum[EV4_CEPAS_CHKSUM_SIZE];
	BYTE baCEPASSignCert[EV4_CEPAS_SIGNCERT_SIZE];
	BYTE baCEPASCAN[EV4_CEPAS_CAN_SIZE];
	BYTE baCEPASCSN[EV4_CEPAS_CSN_SIZE];
	BYTE baCEPASPrevCardBal[UINT_BYTES4_SIZE];
	BYTE baCEPASPostCardBal[UINT_BYTES4_SIZE];
	BYTE baCEPASCounter[EV4_CEPAS_COUNTER_SIZE];
	BYTE baCEPASCardCert[EV4_CEPAS_CARDCERT_SIZE];
	BYTE szCardExpDate[EV4_CARD_EXP_DATE_SIZE+1];
	BYTE baPurseStatus[EV4_PURSE_STATUS_SIZE];
	BYTE baAutoLoadAmt[UINT_BYTES4_SIZE];
	BYTE baCEPASVer[EV4_CEPAS_VER_SIZE];
	BYTE szCardSchemeName[EV4_CARD_SCHEME_SIZE + 1];
}NETS_ECRV40_RESP_DATA;

NETS_ECRV40_RESP_DATA	stNetECRV40RespRec;

#define EV4_HOST_ID_NETS			"NETS"
#define EV4_HOST_ID_UPOS			"UPOS"

#define EV4_CARD_TYPE_NONE			0x0000
#define EV4_CARD_TYPE_NETS_EFT		0x1000
#define EV4_CARD_TYPE_NETS_NCC		0x1001
#define EV4_CARD_TYPE_NETS_NFP		0x1002
//#define EV4_CARD_TYPE_NETS_RSVP		0x1003
//#define EV4_CARD_TYPE_NETS_VCC		0x1004
#define EV4_CARD_TYPE_EZLINK		0x7000
#define EV4_CARD_TYPE_CREDIT		0x8000
#define EV4_CARD_TYPE_DEBIT			0x9000


#define EV4_SOF_TYPE_NONE			0x0000
#define EV4_SOF_TYPE_NETS_EFT		0x1000
#define EV4_SOF_TYPE_NETS_NCC		0x1001
#define EV4_SOF_TYPE_NETS_NFP		0x1002
#define EV4_SOF_TYPE_NETS_RSVP		0x1003
#define EV4_SOF_TYPE_NETS_VCC		0x1004
#define EV4_SOF_TYPE_CREDIT			0x8000
#define EV4_SOF_TYPE_DEBIT			0x9000

typedef struct tagECRV40CSOFInfo
{
	BYTE baSOFType[USHORT_BYTES2_SIZE];
	BYTE szSOFDesc[EV4_SOF_DESC_SIZE+1];
	BYTE baSOFPriority[USHORT_BYTES2_SIZE];
}ECRV40_CSOF_INFO;


typedef struct tagNETSECRV40CardTotal
{
	BYTE szCardName[64];
	BYTE szTID[8];
	BYTE szMID[15];
	ULONG ulSaleCnt;
	ULONG ulSaleAmt;
	ULONG ulVoidCnt;
	ULONG ulVoidAmt;
	ULONG ulRefundCnt;
	ULONG ulRefundAmt;
}NETS_ECRV40_CARD_TOTAL;

#define NPX_MAX_CARD_TYPE		10

NETS_ECRV40_CARD_TOTAL	stNetsECRV40CardTotal[NPX_MAX_CARD_TYPE];
void vdECRV40_SetMsgStatus(UINT uiStatus);


#ifdef	__cplusplus
}
#endif

#endif	/* _ECR_V4_H */




