/***************************************************/
/* Author       : <PERSON>                       */
/* Version      : 1.0          Date   : 16-02-2011 */
/***************************************************/
/* Description  :                                  */
/* Flash Pay for Contactless e-purse application.  */
/***************************************************/
/* Modification :                                  */
/* By           :                                  */
/* Version      : x.x          Date   : dd-mm-yyyy */
/***************************************************/
#include <string.h>
#include <stdio.h>
#include <stdlib.h>
#include <stdarg.h>
/** These two files are necessary for calling CTOS API **/
#include <ctosapi.h>

#include "..\Includes\wub_lib.h"
#include "..\Includes\POSTypedef.h"

#include "..\Debug\Debug.h"

/*
// RTC HEX FORMAT [YY YY MM DD hh mm ss], 2010/4/30 -> 07 DA 04 1E
// RTC BCD FORMAT [YY YY MM DD hh mm ss], 2010/4/30 -> '20 10 04 30'
*/

BOOL CC_CheckForLeap(USHORT usYear)
{   
    if((usYear % 4) == 0)
    {
        if((usYear % 100) == 0)
        {
            if((usYear % 400) == 0)
            {
                return TRUE;
            }
            return FALSE;
        }
        return TRUE;       
    }
    return FALSE;
}

BYTE CC_GetDays(USHORT usYear, BYTE bMonth)
{   
    BYTE Days;
    
    if(bMonth == 2)
    {
        Days = 28;
        if(CC_CheckForLeap(usYear))
            Days++;
    }
    else if((bMonth == 4) || (bMonth == 6) || (bMonth == 9) || (bMonth == 11))
    {
        Days = 30;
    }
    else
    {
        Days = 31;
    }
    
    return Days;
}

////////////////////////////////////////////////////////////////////////////////
// pbRtcHex : output, HEX RTC Format
USHORT CC_GetRtcHex(BYTE *pbRtcHex)
{
    CTOS_RTC CurrentRTC;
    USHORT usRtn;
    USHORT usYear;
	BYTE szCurrentTime[24];
    
    //===============================================================
    // Get Current RTC of Terminal
    usRtn = CTOS_RTCGet(&CurrentRTC);  
    if(usRtn != d_OK)
    {
        return usRtn;
    }
    
    //===============================================================
    // Prepare RTC Data, RTC [YY YY MM DD hh mm ss] HEX Format, ie 2010/4/30 -> 07 DA 04 1E
    usYear = CurrentRTC.bYear + 2000;
    pbRtcHex[0] = usYear >> 8;
    pbRtcHex[1] = usYear & 0x00FF;
    pbRtcHex[2] = CurrentRTC.bMonth;
    pbRtcHex[3] = CurrentRTC.bDay;
    pbRtcHex[4] = CurrentRTC.bHour;
    pbRtcHex[5] = CurrentRTC.bMinute;
    pbRtcHex[6] = CurrentRTC.bSecond;

	#if 0
	usYear = 2007;
    pbRtcHex[0] = usYear >> 8;
    pbRtcHex[1] = usYear & 0x00FF;
    pbRtcHex[2] = 4;
    pbRtcHex[3] = 19;
    pbRtcHex[4] = 11;
    pbRtcHex[5] = 15;
    pbRtcHex[6] = 00;
	
	/*TxnDateTime is in Julian Seconds calculated starting from 1 Jan 1995.
	Eg. 19 Apr 2007 11:15:00 = 17 21 5E B4*/
	#endif

	//update to TransRec
	memset(szCurrentTime, 0x00, sizeof(szCurrentTime));
	sprintf(szCurrentTime,"%02d%02d%02d", CurrentRTC.bHour,CurrentRTC.bMinute,CurrentRTC.bSecond);
	wub_str_2_hex(szCurrentTime, srTransRec.szTime,TIME_ASC_SIZE);
	
    return d_OK;
}

#define     DAY_19950101        728293
////////////////////////////////////////////////////////////////////////////////
// p_in_RTC_HEX : input, HEX RTC Format
ULONG CC_RtcHexToJulian(BYTE *pbRtcHex)
{
    BOOL isLeap;   
    USHORT Y;
    BYTE M, D, h, m, s;
    ULONG ulDay, ulSec;
    BYTE i;
   
    //===============================================================
    // RTC [YY YY MM DD hh mm ss] HEX Format
    Y = (pbRtcHex[0] << 8) + pbRtcHex[1];
    M = pbRtcHex[2];
    D = pbRtcHex[3];
    h = pbRtcHex[4];
    m = pbRtcHex[5];
    s = pbRtcHex[6];
   
    //===============================================================
    // Convert to Julian Day
    /*
    isLeap = CC_CheckForLeap(Y);
   
    Y -= 1;
    ulDay = Y*365 + Y/4 - Y/100 + Y/400;
    for(i = 1 ; i < M ; i++)
    {
        ulDay += CC_GetDays(Y, i);
    }
   
    ulDay += (D-1);
    ulDay -= DAY_19950101;
    ulSec = 60 * ((60 * (24 * ulDay + h)) + m) + s;
   
    */
    ulDay = 0;
    for(i = 1 ; i < M ; i++)
    {
        ulDay += CC_GetDays(Y, i);
    }
   
    ulDay += (D-1);
    Y -= 1;
    ulDay += (Y*365 + Y/4 - Y/100 + Y/400);
    ulDay -= DAY_19950101;
    ulSec = 60 * ((60 * (24 * ulDay + h)) + m) + s;
   
    return ulSec;
}

// pbRtcHex : output, HEX RTC Format
USHORT CC_GetRtcBcd(BYTE *pbRtcBcd)
{
    CTOS_RTC CurrentRTC;
    USHORT usRtn;
    USHORT usYear;
    
    //===============================================================
    // Get Current RTC of Terminal
    usRtn = CTOS_RTCGet(&CurrentRTC);  
    if(usRtn != d_OK)
    {
        return usRtn;
    }
    
    //===============================================================
    // Prepare RTC Data, RTC [YY YY MM DD hh mm ss] HEX Format, ie 2010/4/30 -> 07 DA 04 1E
    usYear = CurrentRTC.bYear + 2000;
    pbRtcBcd[0] = ((usYear / 1000) << 4) + ((usYear % 1000) / 100);
    pbRtcBcd[1] = (((usYear % 100) / 10) << 4) + (usYear % 10);
    pbRtcBcd[2] = ((CurrentRTC.bMonth / 10) << 4) + (CurrentRTC.bMonth % 10);
    pbRtcBcd[3] = ((CurrentRTC.bDay / 10) << 4) + (CurrentRTC.bDay % 10);
    pbRtcBcd[4] = ((CurrentRTC.bHour / 10) << 4) + (CurrentRTC.bHour % 10);
    pbRtcBcd[5] = ((CurrentRTC.bMinute / 10) << 4) + (CurrentRTC.bMinute % 10);
    pbRtcBcd[6] = ((CurrentRTC.bSecond / 10) << 4) + (CurrentRTC.bSecond % 10);
    
    return d_OK;
}

////////////////////////////////////////////////////////////////////////////////
// p_out_Julian : output, 4 bytes Julian date(Big Ending)
USHORT CC_GetJulianTime(BYTE* pbJulian)
{
    CTOS_RTC CurrentRTC;
    BYTE baRTChex[8], tmp[4];
    USHORT usYear;
    USHORT usRtn;
    ULONG T;
    
    //===============================================================
    // Get Current RTC
    usRtn = CC_GetRtcHex(baRTChex);
    if(usRtn != d_OK)
    {
        return usRtn;
    }
       
    //===============================================================
    // Convert RTC to Julian Date Time
    T = CC_RtcHexToJulian(baRTChex);
    
    memcpy(tmp, (BYTE *)&T, 4);
    pbJulian[0] = tmp[3];
    pbJulian[1] = tmp[2];
    pbJulian[2] = tmp[1];
    pbJulian[3] = tmp[0];
    
    return d_OK;
}

////////////////////////////////////////////////////////////////////////////////
// p_in_Julian : input, 4 bytes Julian Date
// p_out_RTC_bcd : output, BCD RTC Format
void CC_JulianToRtcBcdmodeBase2003(BYTE* pbJulian, BYTE *pbRtcBcd)
{
    BYTE tmp[4];
    USHORT usYear;
    BOOL isLeap;    
    USHORT Y;
    BYTE M, D, h, m, s, v;
    ULONG ulDay, ulSec;
    ULONG i;
    
    //===============================================================
    tmp[0] = pbJulian[3];
    tmp[1] = pbJulian[2];
    tmp[2] = pbJulian[1];
    tmp[3] = pbJulian[0];
    memcpy((BYTE *)&ulSec, tmp, 4);
    
    //===============================================================
    // The Second
    s = ulSec % 60;
    
    // The Minute
    ulSec /= 60;
    m = ulSec % 60;
    
    // The Hour
    ulSec /= 60;
    h = ulSec % 24;
    
    // The Year, Month, Day
    ulDay = ulSec / 24;
    Y = 2003;
    M = 1;
    D = 1;
    
    i = 365;
    while(i <= ulDay)
    {
        Y ++;
        ulDay -= i;
        
        if(CC_CheckForLeap(Y))
            i = 366;
        else
            i = 365;
    }
    
    i = 31;
    while(i <= ulDay)
    {
        M ++;
        ulDay -= i;
        
        i = CC_GetDays(Y, M);
    }    
    
    D += ulDay;
 
    //===============================================================
    // Converting
    tmp[0] = (Y / 100);
    tmp[1] = (Y % 100);
    pbRtcBcd[0] = ((tmp[0] / 10) << 4) + (tmp[0] % 10);
    pbRtcBcd[1] = ((tmp[1] / 10) << 4) + (tmp[1] % 10);
    pbRtcBcd[2] = ((M / 10) << 4) + (M % 10);
    pbRtcBcd[3] = ((D / 10) << 4) + (D % 10);
    pbRtcBcd[4] = ((h / 10) << 4) + (h % 10);
    pbRtcBcd[5] = ((m / 10) << 4) + (m % 10);
    pbRtcBcd[6] = ((s / 10) << 4) + (s % 10);

    return ;
}

////////////////////////////////////////////////////////////////////////////////
// p_in_Julian : input, 4 bytes Julian Date
// p_out_RTC_bcd : output, BCD RTC Format
void CC_JulianToRtcBcdmode(BYTE* pbJulian, BYTE *pbRtcBcd)
{
    BYTE tmp[4];
    USHORT usYear;
    BOOL isLeap;    
    USHORT Y;
    BYTE M, D, h, m, s, v;
    ULONG ulDay, ulSec;
    ULONG i;
    
    //===============================================================
    tmp[0] = pbJulian[3];
    tmp[1] = pbJulian[2];
    tmp[2] = pbJulian[1];
    tmp[3] = pbJulian[0];
    memcpy((BYTE *)&ulSec, tmp, 4);
    
    //===============================================================
    // The Second
    s = ulSec % 60;
    
    // The Minute
    ulSec /= 60;
    m = ulSec % 60;
    
    // The Hour
    ulSec /= 60;
    h = ulSec % 24;
    
    // The Year, Month, Day
    ulDay = ulSec / 24;
    Y = 1995;
    M = 1;
    D = 1;
    
    i = 365;
    while(i <= ulDay)
    {
        Y ++;
        ulDay -= i;
        
        if(CC_CheckForLeap(Y))
            i = 366;
        else
            i = 365;
    }
    
    i = 31;
    while(i <= ulDay)
    {
        M ++;
        ulDay -= i;
        
        i = CC_GetDays(Y, M);
    }    
    
    D += ulDay;
 
    //===============================================================
    // Converting
    tmp[0] = (Y / 100);
    tmp[1] = (Y % 100);
    pbRtcBcd[0] = ((tmp[0] / 10) << 4) + (tmp[0] % 10);
    pbRtcBcd[1] = ((tmp[1] / 10) << 4) + (tmp[1] % 10);
    pbRtcBcd[2] = ((M / 10) << 4) + (M % 10);
    pbRtcBcd[3] = ((D / 10) << 4) + (D % 10);
    pbRtcBcd[4] = ((h / 10) << 4) + (h % 10);
    pbRtcBcd[5] = ((m / 10) << 4) + (m % 10);
    pbRtcBcd[6] = ((s / 10) << 4) + (s % 10);

    return ;
}

////////////////////////////////////////////////////////////////////////////////
// p_in_ExpiryDate_bcd [YY YY MM DD hh mm ss] BCD Format, ie 2010/4/30 -> '20 10 04 30'
// pResult : < 0 is Expired, 0 is aproval, > 0 is Grace Remaining Date
USHORT CC_CheckIfCardExpired(BYTE* pbExpiryDateBcd, BYTE bValidPeriod, BYTE *pbGracePeriod, short *psResult)
{
    USHORT Y;
    BYTE M, D;
    BYTE baValidDate[8], baRTC_hex[8];
    USHORT usRtn;
    USHORT R1, R2;
    BYTE i;

    *psResult = -1;
    memset(baValidDate, 0x00, 8);
    
    //===============================================================
    Y = ((pbExpiryDateBcd[0] >> 4) * 10) + (pbExpiryDateBcd[0] & 0x0F);
    Y *= 100;
    Y += (((pbExpiryDateBcd[1] >> 4) * 10) + (pbExpiryDateBcd[1] & 0x0F));
    M = ((pbExpiryDateBcd[2] >> 4) * 10) + (pbExpiryDateBcd[2] & 0x0F);
    D = ((pbExpiryDateBcd[3] >> 4) * 10) + (pbExpiryDateBcd[3] & 0x0F);
    
    M += bValidPeriod;
    if(M > 12)
    {
        Y += ((M-1) / 12);
        M %= 12;
        if(M == 0)
            M = 12;
    }
    
    //===============================================================
    baValidDate[0] = Y / 256;
    baValidDate[1] = Y % 256;
    baValidDate[2] = M ;
    baValidDate[3] = D ;
    
    //===============================================================
    usRtn = CC_GetRtcHex(baRTC_hex);
    if(usRtn != d_OK)
    {
        return usRtn;
    }
    
    //===============================================================
    // Check if Purse is Expiry
    for(i = 0 ; i < 7 ; i++)
    {
        if(baRTC_hex[i] > baValidDate[i])
        {
            break;
        }
        else if(baValidDate[i] > baRTC_hex[i])
        {
            *psResult = 0;
            return d_OK;
        }
    }
    
    //===============================================================
    baValidDate[2] += pbGracePeriod[0]; // MM
    baValidDate[3] += pbGracePeriod[1]; // DD
    Y = (baValidDate[0] << 8) + baValidDate[1];
    M = baValidDate[2];
    D = baValidDate[3];
    if(M > 12)
    {
        Y += ((M-1) / 12);
        M %= 12;
        if(M == 0)
            M = 12;
    }
    
    i = CC_GetDays(Y, M);
    while(i < D)
    {
        D -= i;
        M ++ ;
        if(M == 13)
        {
            M = 1;
            Y ++;
        }
        
        i = CC_GetDays(Y, M);
    }
    
    baValidDate[0] = Y / 256; // YY
    baValidDate[1] = Y % 256; // YY
    baValidDate[2] = M; // MM
    baValidDate[3] = D; // DD
    
    //===============================================================
    // Check if Purse is Expiry
    for(i = 0 ; i < 7 ; i++)
    {
        if(baRTC_hex[i] > baValidDate[i])
        {
            *psResult = -1;
            return d_OK;
        }
        else if(baValidDate[i] > baRTC_hex[i])
        {
            break;
        }
    }
    
    //===============================================================
    R1 = 0;
    for(i = 1 ; i < M ; i++)
    {
        R1 += CC_GetDays(Y, i);
    }
    R1 += D;
    
    Y = (baRTC_hex[0] << 8) + baRTC_hex[1];
    M = baRTC_hex[2];
    D = baRTC_hex[3];
    R2 = 0;
    for(i = 1 ; i < M ; i++)
    {
        R2 += CC_GetDays(Y, i);
    }
    R2 += D;
    
    //===============================================================
    if(R1 == R2)
    {
        *psResult = -1;
        return d_OK;
    }
    
    if(R1 < R2)
    {
        R1 += 356;
        if( CC_CheckForLeap((baValidDate[0] << 8) + baValidDate[1] -1) )
            R1 ++;          
    }
    
    *psResult = R1 - R2;
    
    return d_OK;
}

void datetime2seconds(char *pszDTStr, unsigned long *pulSecs)
{
	char szDTHex[7];
	int inLen = 0;
	unsigned long ulJulian = 0;

	if(NULL == pszDTStr)
		return;

	inLen = strlen(pszDTStr);
	if (inLen != 14)
		return;

	memset(szDTHex, 0x00, sizeof(szDTHex));
	wub_str_2_hex(pszDTStr, szDTHex, inLen);

	ulJulian = CC_RtcHexToJulian(szDTHex);
	
	*pulSecs = ulJulian;

	return;
}


#define UTC_BASE_YEAR			1970
#define MONTH_PER_YEAR			12
#define DAY_PER_YEAR			365
#define SEC_PER_DAY				86400
#define SEC_PER_HOUR			3600
#define SEC_PER_MIN				60
#define UTC_OFFSET_0000_1970	62168515200

#define bool	VS_BOOL
#define false	VS_FALSE
#define true	VS_TRUE

/* ÿ���µ����� */
const unsigned char g_day_per_mon[MONTH_PER_YEAR] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};

/* �Զ����ʱ��ṹ�� */
typedef struct
{
    unsigned short nYear;
    unsigned char nMonth;
    unsigned char nDay;
    unsigned char nHour;
    unsigned char nMin;
    unsigned char nSec;
    unsigned char DayIndex; /* 0 = Sunday */
} mytime_struct;

/*
 * ���ܣ�
 *     �ж��Ƿ�������
 * ������
 *     year����Ҫ�жϵ������
 *
 * ����ֵ��
 *     ���귵��1�����򷵻�0
 */
unsigned char applib_dt_is_leap_year(unsigned short year)
{
    /*----------------------------------------------------------------*/
    /* Local Variables                                                */
    /*----------------------------------------------------------------*/

    /*----------------------------------------------------------------*/
    /* Code Body                                                      */
    /*----------------------------------------------------------------*/
    if ((year % 400) == 0) {
        return 1;
    } else if ((year % 100) == 0) {
        return 0;
    } else if ((year % 4) == 0) {
        return 1;
    } else {
        return 0;
    }
}

/*
 * ���ܣ�
 *     �õ�ÿ�����ж�����
 * ������
 *     month����Ҫ�õ��������·���
 *     year����������Ӧ�������
 *
 * ����ֵ��
 *     �����ж�����
 *
 */
unsigned char applib_dt_last_day_of_mon(unsigned char month, unsigned short year)
{
    /*----------------------------------------------------------------*/
    /* Local Variables                                                */
    /*----------------------------------------------------------------*/

    /*----------------------------------------------------------------*/
    /* Code Body                                                      */
    /*----------------------------------------------------------------*/
    if ((month == 0) || (month > 12)) {
        return g_day_per_mon[1] + applib_dt_is_leap_year(year);
    }

    if (month != 2) {
        return g_day_per_mon[month - 1];
    } else {
        return g_day_per_mon[1] + applib_dt_is_leap_year(year);
    }
}

/*
 * ���ܣ�
 *     ���ݸ��������ڵõ���Ӧ������
 * ������
 *     year�����������
 *     month���������·�
 *     day������������
 *
 * ����ֵ��
 *     ��Ӧ����������0 - ������ ... 6 - ������
 */
unsigned char applib_dt_dayindex(unsigned short year, unsigned char month, unsigned char day)
{
    char century_code, year_code, month_code, day_code;
    int week = 0;

    century_code = year_code = month_code = day_code = 0;

    if (month == 1 || month == 2) {
        century_code = (year - 1) / 100;
        year_code = (year - 1) % 100;
        month_code = month + 12;
        day_code = day;
    } else {
        century_code = year / 100;
        year_code = year % 100;
        month_code = month;
        day_code = day;
    }

    /* ���ݲ��չ�ʽ�������� */
    week = year_code + year_code / 4 + century_code / 4 - 2 * century_code + 26 * ( month_code + 1 ) / 10 + day_code - 1;
    week = week > 0 ? (week % 7) : ((week % 7) + 7);

    return week;
}

/*
 * ���ܣ�
 *     ����UTCʱ����õ���Ӧ������
 * ������
 *     utc_sec��������UTCʱ���
 *     result��������Ľ��
 *     daylightSaving���Ƿ�������ʱ
 *
 * ����ֵ��
 *     ��
 */
void utc_sec_2_mytime(unsigned int utc_sec, mytime_struct *result, bool daylightSaving)
{
    /*----------------------------------------------------------------*/
    /* Local Variables                                                */
    /*----------------------------------------------------------------*/
    int sec, day;
    unsigned short y;
    unsigned char m;
    unsigned short d;
    unsigned char dst;

    /*----------------------------------------------------------------*/
    /* Code Body                                                      */
    /*----------------------------------------------------------------*/

    if (daylightSaving) {
        utc_sec += SEC_PER_HOUR;
    }

    /* hour, min, sec */
    /* hour */
    sec = utc_sec % SEC_PER_DAY;
    result->nHour = sec / SEC_PER_HOUR;

    /* min */
    sec %= SEC_PER_HOUR;
    result->nMin = sec / SEC_PER_MIN;

    /* sec */
    result->nSec = sec % SEC_PER_MIN;

    /* year, month, day */
    /* year */
    /* year */
    day = utc_sec / SEC_PER_DAY;
    for (y = UTC_BASE_YEAR; day > 0; y++) {
        d = (DAY_PER_YEAR + applib_dt_is_leap_year(y));
        if (day >= d)
        {
            day -= d;
        }
        else
        {
            break;
        }
    }

    result->nYear = y;

    for (m = 1; m < MONTH_PER_YEAR; m++) {
        d = applib_dt_last_day_of_mon(m, y);
        if (day >= d) {
            day -= d;
        } else {
            break;
        }
    }

    result->nMonth = m;
    result->nDay = (unsigned char) (day + 1);
    /* ���ݸ��������ڵõ���Ӧ������ */
    result->DayIndex = applib_dt_dayindex(result->nYear, result->nMonth, result->nDay);
}

/*
 * ���ܣ�
 *     ����ʱ������UTCʱ���
 * ������
 *     currTime��������ʱ��
 *     daylightSaving���Ƿ�������ʱ
 *
 * ����ֵ��
 *     UTCʱ���
 */
unsigned int mytime_2_utc_sec(mytime_struct *currTime, bool daylightSaving)
{
    /*----------------------------------------------------------------*/
    /* Local Variables                                                */
    /*----------------------------------------------------------------*/
    unsigned short i;
    unsigned int no_of_days = 0;
    int utc_time;
    unsigned char dst;

    /*----------------------------------------------------------------*/
    /* Code Body                                                      */
    /*----------------------------------------------------------------*/
    if (currTime->nYear < UTC_BASE_YEAR) {
        return 0;
    }

    /* year */
    for (i = UTC_BASE_YEAR; i < currTime->nYear; i++) {
        no_of_days += (DAY_PER_YEAR + applib_dt_is_leap_year(i));
    }

    /* month */
    for (i = 1; i < currTime->nMonth; i++) {
        no_of_days += applib_dt_last_day_of_mon((unsigned char) i, currTime->nYear);
    }

    /* day */
    no_of_days += (currTime->nDay - 1);

    /* sec */
    utc_time = (unsigned int) no_of_days * SEC_PER_DAY + (unsigned int) (currTime->nHour * SEC_PER_HOUR +
                                                                currTime->nMin * SEC_PER_MIN + currTime->nSec);

    if (dst && daylightSaving) {
        utc_time -= SEC_PER_HOUR;
    }

    return utc_time;
}

int unix_epoch_test(void)
{
    mytime_struct my_time;
    unsigned int sec;
    char *DayIndex[] = {"Sun.", "Mon.", "Tues.", "Wed.", "Thur.", "Fri.", "Sat."};

    /* �������UTCʱ������������ʱ������ʱ����ʱ�䣬�������Ҫת���ɱ���ʱ�����Ҫ���8Сʱ */
    utc_sec_2_mytime(63651006356-UTC_OFFSET_0000_1970 + 8*SEC_PER_HOUR, &my_time, false);

    vdDebug_LogPrintf("%d-%d-%d %d:%d:%d %s\n", my_time.nYear, my_time.nMonth, my_time.nDay,
            my_time.nHour, my_time.nMin, my_time.nSec, DayIndex[my_time.DayIndex]);

    sec = mytime_2_utc_sec(&my_time, false);
    vdDebug_LogPrintf("sec = %d\n", sec);

    return 0;
}

/*the UNIX epoch start from 01-Jan-1970 00:00:00, but here we start from 01-Jan-0000 00:00:00
so need handle the offset before 1970*/
ULONG ulCurrentRTC2UTCSec(void)
{
	CTOS_RTC CurrRTC;
	USHORT usRtn;
	mytime_struct my_time;
	char *DayIndex[] = {"Sun.", "Mon.", "Tues.", "Wed.", "Thur.", "Fri.", "Sat."};

	ULONG ulsec = 0;

	usRtn = CTOS_RTCGet(&CurrRTC);

	memset(&my_time, 0x00, sizeof(my_time));
	
	my_time.nYear = 2000+CurrRTC.bYear;
	my_time.nMonth = CurrRTC.bMonth;
	my_time.nDay = CurrRTC.bDay;
	my_time.nHour = CurrRTC.bHour;
	my_time.nMin = CurrRTC.bMinute;
	my_time.nSec = CurrRTC.bSecond;

	vdDebug_LogPrintf("%d-%02d-%02d %02d:%02d:%02d %s", my_time.nYear, my_time.nMonth, my_time.nDay,
            my_time.nHour, my_time.nMin, my_time.nSec, DayIndex[my_time.DayIndex]);

	ulsec = mytime_2_utc_sec(&my_time, false);
    vdDebug_LogPrintf("sec = %d\n", ulsec);

	return ulsec;
}

/*the UNIX epoch start from 01-Jan-1970 00:00:00, but here we start from 01-Jan-0000 00:00:00
so need handle the offset before 1970*/
int inUTCSec2DateTimeString(BYTE *pbaLLSec, BYTE *pszDateTime)
{
	int inRet = d_OK;
	mytime_struct my_time;

	long long u64b_Sec = 0;
	BYTE *pu64b_Sec = &u64b_Sec;

	ULONG ulSec = 0;
	#define UTC_OFFSET_0000_1970	62168515200

	vdDebug_LogPrintf("=====inUTCSec2DateTimeString=====");

	memcpy(pu64b_Sec, pbaLLSec, 8);
	vdDebug_LogPrintf("u64b_Sec = %d", u64b_Sec);
	ulSec = (long long)u64b_Sec - (long long)UTC_OFFSET_0000_1970;
	
	vdDebug_LogPrintf("sec = %d", ulSec);
	
	/* �������UTCʱ������������ʱ������ʱ����ʱ�䣬�������Ҫת���ɱ���ʱ�����Ҫ���8Сʱ */
	//utc_sec_2_mytime(ulSec + 8*SEC_PER_HOUR, &my_time, false);
	utc_sec_2_mytime(ulSec, &my_time, false);

	sprintf(pszDateTime, "%d-%d-%d %d:%d:%d", my_time.nYear, my_time.nMonth, my_time.nDay,
            my_time.nHour, my_time.nMin, my_time.nSec);

	vdDebug_LogPrintf("pszDateTime[%s]", pszDateTime);

	return d_OK;
	
}


