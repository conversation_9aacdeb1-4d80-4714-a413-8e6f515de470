/* 
 * File:   Time.h
 * Author: <PERSON>
 *
 * Created on 2011�~3��1��, �U�� 2:36
 */

#ifndef _FP_TIME_H
#define	_FP_TIME_H

#ifdef	__cplusplus
extern "C" {
#endif

//***************************************************************
// If API run correct, it will throw return code as FP_RET_NO_ERROR.
//***************************************************************
// Output : pbJulian, 4 bytes Julian Date
USHORT CC_GetJulianTime(BYTE* pbJulian);

// Intput : pbJulian, 4 bytes Julian Date
// Output : pbRtcBcd, The Date converted from Julian Date, BCD format [YY YY MM DD hh mm ss], ex 2011/4/19 16:55:38 i.e.[20 11 04 19 16 55 38]
void CC_JulianToRtcBcdmode(BYTE* pb<PERSON><PERSON><PERSON>, BYTE *pbRtcBcd);

// Intput : pbExpiryDateBcd [YY YY MM DD hh mm ss] BCD Format, ie 2010/4/30 -> '20 10 04 30'
// Intput : bValidPeriod
// Intput : pbGracePeriod
// Output : psResult, < 0 is Expired,  = 0 is aproval, > 0 is Grace Remaining Date
USHORT CC_CheckIfCardExpired(BYTE* pbExpiryDateBcd, BYTE bValidPeriod, BYTE *pbGracePeriod, short *psResult);

ULONG ulCurrentRTC2UTCSec(void);

void datetime2seconds(char *pszDTStr, unsigned long *pulSecs);

#ifdef	__cplusplus
}
#endif

#endif	/* _TIME_H */

