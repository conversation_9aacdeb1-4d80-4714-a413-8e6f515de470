#include <string.h>
#include <stdio.h>
#include <stdlib.h>
#include <stdarg.h>
#include <ctosapi.h>
#include <unistd.h>
#include <pwd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/time.h>
#include <sys/wait.h>
#include <fcntl.h>
#include <errno.h>
#include <dirent.h>
#include <signal.h>
#include <pthread.h>
#include <sys/shm.h>
#include <linux/errno.h>
#include <sys/types.h>
#include <sys/ipc.h>
#include <sys/msg.h>

#include "..\Aptrans\MultiAptrans.h"
#include "..\Includes\POSTypedef.h"
#include "..\Includes\POSTrans.h"
#include "..\Includes\POSSale.h"
#include "..\Includes\POSRefund.h"
#include "..\Includes\POSAuth.h"
#include "..\Includes\POSVoid.h"
#include "..\Includes\POSTipAdjust.h"
#include "..\Includes\POSSettlement.h"
#include "..\Includes\POSSetting.h"
#include "..\Includes\POSOffline.h"
#include "..\Includes\POSHost.h"
//#include "..\Includes\POSLogon.h"

#include "..\Includes\Wub_lib.h"
#include "..\Includes\MultiApLib.h"
#include "..\Includes\Dmenu.h"
#include "..\Includes\CTOSInput.h"

#include "..\Print\Print.h"
#include "..\UI\Display.h"
#include "..\Debug\Debug.h"
#include "..\FileModule\myFileFunc.h"
#include "..\DataBase\DataBaseFunc.h"
#include "..\powrfail\POSPOWRFAIL.h"
#include "..\Ctls\POSWave.h"
#include "..\TMS\TMS.h"
//#include "..\Pinpad\Security.h"
#include "..\Pinpad\PinPad.h"
//#include "..\FlashPay\FlashPay.h"
#include "..\pci100\COMMS.h"
//#include "..\Includes\NETSPurchase.h"


#include "ecr_cb.h"
#include "..\NETSECR\ecr.h"

#include "ecr_comm.h"

extern int inECR_Port;
#define d_READY_TIMEOUT		100
#define MAXRETRIES 3

ECR_CB_MSG_TAB stCBDispMsgTab[] =
{
	{EMI_PLS_INS_CARD,		"Please Insert Card"},
	{EMI_PLS_SWP_CARD,		"Please Swipe Card"},
	{EMI_PLS_TAP_CARD,		"Please Tap Card"},
	{EMI_PLS_IST_CARD,		"Please Insert/Swipe/Tap Card"},
	{EMI_PLS_IT_CARD,		"Please Insert/Tap Card"},
	{EMI_PLS_ST_CARD,		"Please Swipe/Tap Card"},
	{EMI_PLS_IS_CARD,		"Please Insert/Swipe Card"},
	{EMI_PLS_RM_CARD,		"Please Remove Card"},
	{EMI_PLS_NFP_CARD,		"Please Place NETS Flash Pay Card"},
	{EMI_PLS_CASH_CARD,		"Please Insert NETS Cash Card"},
	{EMI_ENTER_PIN,			"Please Enter PIN"},
	{EMI_ENTER_PIN2,		"Please Enter PIN"},
	{EMI_REENTER_PIN,		"Please Re-Enter PIN"},
	{EMI_PROCESSING,		"Processing Please Wait"},
	{EMI_PIN_STAR1,		    "*"},
	{EMI_PIN_STAR2,		    "**"},
	{EMI_PIN_STAR3,			"***"},
	{EMI_PIN_STAR4,		    "****"},
	{EMI_PIN_STAR5,		    "*****"},
	{EMI_PIN_STAR6,		    "******"},
	{EMI_PIN_STAR7,		    "*******"},
	{EMI_PIN_STAR8,		    "********"},
	{EMI_PIN_STAR9,		    "*********"},
	{EMI_PIN_STAR10,	    "**********"},
	{EMI_PIN_STAR11,	    "***********"},
	{EMI_PIN_STAR12,	    "************"},
	{EMI_PIN_BACKSPACE,		""}
};


ECR_CB_SELECT_ITEM stCBSelectTab[MAX_SELECT_NUM];
int g_inCurrSelectNum = 0;

BYTE g_szSelectType[ECR_CB_FUNC_TYPE_SIZE + 1];

void vdSetSelectionType(BYTE *pszType)
{
	if (NULL != pszType)
		strcpy(g_szSelectType, pszType);
}

BYTE *szGetSelectionType(void)
{
	return g_szSelectType;
}

int inGetCBDisplayInfoMsg(BYTE byMsgIdx, BYTE *pszMsg)
{
	int inIdx = 0;
	int inTotMsg = 0;
	BYTE szMsg[ECR_DISP_MSG_SIZE + 1];
	int inSize = 0;

	vdDebug_LogPrintf("=====inGetCBDisplayInfoMsg=====");
	
	inTotMsg = sizeof(stCBDispMsgTab) / ECR_CB_MSG_TAB_SIZE;
	vdDebug_LogPrintf("inTotMsg[%d]", inTotMsg);

	memset(szMsg, 0x00, sizeof(szMsg));
	for (inIdx=0; inIdx<inTotMsg; inIdx++)
	{
		if (byMsgIdx == stCBDispMsgTab[inIdx].byMsgIdx)
		{
			strcpy(szMsg, stCBDispMsgTab[inIdx].szMsg);
		}
	}

	strcpy(pszMsg, szMsg);
	inSize = strlen(pszMsg);

	return inSize;
}


int inECR_FormCBDisplnfoHeader(BYTE *pszECN, BYTE *pszHeader)
{
	int inPos = 0;
	BYTE szECRHeader[MESSAGE_HEADER_SIZE + 1];

	memset(szECRHeader, 0x00, sizeof(szECRHeader));
	inPos = 0;
	
	/* 1. ECN*/
	memcpy(&szECRHeader[inPos], pszECN, ECN_SIZE);
	inPos += ECN_SIZE;

	/* 2. Function Code*/
	memcpy(&szECRHeader[inPos], ECR_FC_DISP_INFO, FUNC_CODE_SIZE);
	inPos += FUNC_CODE_SIZE;

	/* 3. Version Code*/
	memcpy(&szECRHeader[inPos], ECR_VERSION_CODE, ECR_VER_CODE_SIZE);
	inPos += ECR_VER_CODE_SIZE;

	/* 4. RFU*/
	memcpy(&szECRHeader[inPos], "\x30", ECR_RFU_SIZE);
	inPos += ECR_RFU_SIZE;

	/* 5. Separator*/
	memcpy(&szECRHeader[inPos], "\x1C", SEPARATOR_SIZE);
	inPos += SEPARATOR_SIZE;

	memcpy(pszHeader, szECRHeader, inPos);

	return inPos;
}

int inECR_FormCBSubField(BYTE *pszFldCode, BYTE *pacValue, int inSize, BYTE *pacSubFld)
{
	int inPos = 0;
	BYTE szSubFld[512 + 1];
	int inlen = 0;
    char szTmp[5],szHex[5];

	memset(szSubFld, 0x00, sizeof(szSubFld));
	inPos = 0;
	
	/* 1. Field Code*/
	memcpy(&szSubFld[inPos], pszFldCode, FIELD_CODE_SIZE);
	inPos += FIELD_CODE_SIZE;

	memset(szTmp, 0x00, sizeof(szTmp));	
	memset(szHex, 0x00, sizeof(szHex));

	/* 2. Message len*/
	inlen=strlen(pacValue);
	sprintf((char *)szTmp, "%04d", inlen);
	wub_str_2_hex((char *)szTmp ,(char *)szHex, 4);

	memcpy(&szSubFld[inPos], szHex, 2);
	inPos += 2;

	/* 3. Value*/
	memcpy(&szSubFld[inPos], pacValue, inSize);
	inPos += inSize;

	/* 4. 0x1C*/
	szSubFld[inPos]=0x1C;
    inPos += 1;

	memcpy(pacSubFld, szSubFld, inPos);
	vdDebug_LogPrintf("pacSubFld[%s]", pacSubFld);
	
	return inPos;
}


int inECR_CB_FormDisplayInfoMsg(BYTE *pszECN, BYTE byMsgIdx, BYTE *pCBMsg)
{
	int inIdx = 0;
	int inTotMsg = 0;
	BYTE szDispMsg[ECR_DISP_MSG_SIZE + 1];
	BYTE szECRMsg[512 + 1];
	
	BYTE szECRHeader[MESSAGE_HEADER_SIZE + 1];
	BYTE szECRBody[512 + 1];
	BYTE szSubFld[512 + 1];

	BYTE szASCMsgIdx[ECR_CB_MSGIDX_SIZE + 1];

	int inPos = 0;
	int inRet = 0;

	vdDebug_LogPrintf("=====inECR_CB_FormDisplayInfoMsg=====");

	memset(szECRMsg, 0x00, sizeof(szECRMsg));
	inPos = 0;
	
	/*Form Header*/
	memset(szECRHeader, 0x00, sizeof(szECRHeader));
	inRet = inECR_FormCBDisplnfoHeader(pszECN, szECRHeader);
	memcpy(&szECRMsg[inPos], szECRHeader, inRet);
	inPos += inRet;

	/*Form Sub field for Body*/
	memset(szSubFld, 0x00, sizeof(szSubFld));
	inRet = inECR_FormCBSubField(ECR_CB_FC_FLD_CODE, "DMG", ECR_CB_FUNC_TYPE_SIZE, szSubFld);
	memcpy(&szECRMsg[inPos], szSubFld, inRet);
	inPos += inRet;

	memset(szASCMsgIdx, 0x00, sizeof(szASCMsgIdx));
	sprintf(szASCMsgIdx, "%02X", byMsgIdx);
	
	memset(szSubFld, 0x00, sizeof(szSubFld));
	inRet = inECR_FormCBSubField(ECR_CB_MSGIDX_FLD_CODE, szASCMsgIdx, ECR_CB_MSGIDX_SIZE, szSubFld);
	memcpy(&szECRMsg[inPos], szSubFld, inRet);
	inPos += inRet;

	/*Get Message*/
	memset(szDispMsg, 0x00, sizeof(szDispMsg));
	inRet = inGetCBDisplayInfoMsg(byMsgIdx, szDispMsg);
	
	memset(szSubFld, 0x00, sizeof(szSubFld));
	inRet = inECR_FormCBSubField(ECR_CB_MSG_FLD_CODE, szDispMsg, inRet, szSubFld);
	memcpy(&szECRMsg[inPos], szSubFld, inRet);
	inPos += inRet;

	DebugAddHEX("szECRMsg", szECRMsg, inPos);
	vdDebug_LogPrintf("szECRMsg[%s]", szECRMsg);

	memcpy(pCBMsg, szECRMsg, inPos);

	DebugAddHEX("pCBMsg", pCBMsg, inPos);

	return inPos;
}

/*ECR Callback for Display info*/
int inECR_CB_DisplayInfo(BYTE *pszECN, BYTE byMsgIdx)
{
	BYTE szECRMsg[512+1];
	int inLen = 0;
	int inRet = 0;
	ULONG tick;
	char chResp;
	int	inRecvLen = 0;
        USHORT ret;
	int inNumRetry = 0;

//	inCTOSS_CheckMemoryStatusEx("inECR_CB_DisplayInfo");

	memset(szECRMsg, 0x00, sizeof(szECRMsg));



	vdDebug_LogPrintf("inECR_Port[%d]  d_COM1[%d]", inECR_Port, d_COM1);

	inLen = inECR_CB_FormDisplayInfoMsg(pszECN, byMsgIdx, szECRMsg);


	#if RECEIVE_ACK
	do{
		inRet = inECR_RS232CommSendBuf(szECRMsg, inLen, VS_TRUE);

		inNumRetry++;

		/*Recv ACK?*/
		tick = CTOS_TickGet();
		
        ret = inECR_RS232RecvACK((char*)&chResp, &inRecvLen, 2);
        vdDebug_LogPrintf("ret[%d]", ret);
		if (ret == d_OK)
            break;

	}while (inNumRetry < MAXRETRIES);
	#else	
	inRet = inECR_RS232CommSendBuf(szECRMsg, inLen, VS_TRUE);
    #endif
	
    vdDebug_LogPrintf("chResp[%x]", chResp);
	return d_OK;
}

int inECR_CB_DisplayInfoEx(BYTE *pszECN, BYTE byMsgIdx)
{
	BYTE szECRMsg[512+1];
	int inLen = 0;
	int inRet = 0;
	ULONG tick;
	char chResp;
	int	inRecvLen = 0;
        USHORT ret;
	int inNumRetry = 0;

//	inCTOSS_CheckMemoryStatusEx("inECR_CB_DisplayInfoEx");

	memset(szECRMsg, 0x00, sizeof(szECRMsg));



	vdDebug_LogPrintf("inECR_Port[%d]  d_COM1[%d]", inECR_Port, d_COM1);

	inLen = inECR_CB_FormDisplayInfoMsg(pszECN, byMsgIdx, szECRMsg);


	#if RECEIVE_ACK
	do{
		inRet = inECR_RS232CommSendBuf(szECRMsg, inLen, VS_TRUE);

		inNumRetry++;

		/*Recv ACK?*/
		tick = CTOS_TickGet();
		
        ret = inECR_RS232RecvACK((char*)&chResp, &inRecvLen, 2);
        vdDebug_LogPrintf("ret[%d]", ret);
		if (ret == d_OK)
            break;

	}while (inNumRetry < MAXRETRIES);
	#else	
	inRet = inECR_RS232CommSendBufEx(szECRMsg, inLen, VS_TRUE);
    #endif
	
    vdDebug_LogPrintf("chResp[%x]", chResp);
	return d_OK;
}


int inECR_FormCBHostMsgPassHeader(BYTE *pszECN, BYTE *pszHeader)
{
	int inPos = 0;
	BYTE szECRHeader[MESSAGE_HEADER_SIZE + 1];

	memset(szECRHeader, 0x00, sizeof(szECRHeader));
	inPos = 0;
	
	/* 1. ECN*/
	memcpy(&szECRHeader[inPos], pszECN, ECN_SIZE);
	inPos += ECN_SIZE;

	/* 2. Function Code*/
	memcpy(&szECRHeader[inPos], ECR_FC_HSPT, FUNC_CODE_SIZE);
	inPos += FUNC_CODE_SIZE;

	/* 3. Version Code*/
	memcpy(&szECRHeader[inPos], ECR_VERSION_CODE, ECR_VER_CODE_SIZE);
	inPos += ECR_VER_CODE_SIZE;

	/* 4. RFU*/
	memcpy(&szECRHeader[inPos], "\x30", ECR_RFU_SIZE);
	inPos += ECR_RFU_SIZE;

	/* 5. Separator*/
	memcpy(&szECRHeader[inPos], "\x1C", SEPARATOR_SIZE);
	inPos += SEPARATOR_SIZE;

	memcpy(pszHeader, szECRHeader, inPos);

	return inPos;
}



int inECR_CB_FormHostPassThroughMsg(BYTE *pszECN, BYTE *pacPayLoad, int inSize, BYTE *pCBMsg)
{
	int inIdx = 0;
	int inTotMsg = 0;
	BYTE szDispMsg[ECR_DISP_MSG_SIZE + 1];
	BYTE szECRMsg[512 + 1];
	
	BYTE szECRHeader[MESSAGE_HEADER_SIZE + 1];
	BYTE szECRBody[512 + 1];
	BYTE szSubFld[512 + 1];
	BYTE acPayLaod[512 + 1];

	BYTE szASCMsgIdx[ECR_CB_MSGIDX_SIZE + 1];

	int inPos = 0;
	int inRet = 0;

	vdDebug_LogPrintf("=====inECR_CB_FormHostPassThroughMsg=====");

	memset(szECRMsg, 0x00, sizeof(szECRMsg));
	inPos = 0;
	
	/*Form Header*/
	memset(szECRHeader, 0x00, sizeof(szECRHeader));
	inRet = inECR_FormCBHostMsgPassHeader(pszECN, szECRHeader);
	memcpy(&szECRMsg[inPos], szECRHeader, inRet);
	inPos += inRet;

	/*Form Sub field for Body*/
	memset(szSubFld, 0x00, sizeof(szSubFld));
	inRet = inECR_FormCBSubField(ECR_CB_FC_FLD_CODE, "HTX", ECR_CB_FUNC_TYPE_SIZE, szSubFld);
	memcpy(&szECRMsg[inPos], szSubFld, inRet);
	inPos += inRet;
	
	memset(szSubFld, 0x00, sizeof(szSubFld));
	memset(acPayLaod, 0x00, sizeof(acPayLaod));
	sprintf(acPayLaod, "%02d", inSize); // need to know format here
	memcpy(&acPayLaod[2], pacPayLoad, inSize);
	inRet = inECR_FormCBSubField(ECR_CB_PAYLOAD_FLD_CODE, acPayLaod, inSize+2, szSubFld);
	memcpy(&szECRMsg[inPos], szSubFld, inRet);
	inPos += inRet;

	DebugAddHEX("szECRMsg", szECRMsg, inPos);
	vdDebug_LogPrintf("szECRMsg[%s]", szECRMsg);

	memcpy(pCBMsg, szECRMsg, inPos);

	return inPos;
}


int inECR_CB_HostMessage(BYTE *pszECN, BYTE *pacHostMsg, int inMsgSize)
{
	BYTE szECRMsg[512+1];
	int inLen = 0;
	int inRet = 0;
	ULONG tick;
	char chResp;
	int	inRecvLen = 0;
        USHORT ret;
	int inNumRetry = 0;

	memset(szECRMsg, 0x00, sizeof(szECRMsg));

	inLen = inECR_CB_FormHostPassThroughMsg(pszECN, pacHostMsg, inMsgSize, szECRMsg);

	do{
		
		inRet = inECR_RS232CommSendBuf(szECRMsg, inLen, VS_TRUE);	
	
		inNumRetry++;

		/*Recv ACK?*/
		tick = CTOS_TickGet();
		
        ret = inECR_RS232RecvACK((char*)&chResp, &inRecvLen, 2);
        vdDebug_LogPrintf("ret[%d]", ret);
		if (ret == d_OK)
            break;

	}while (inNumRetry < MAXRETRIES);

    
    vdDebug_LogPrintf("chResp[%x]", chResp);
	return d_OK;
}

void vdECR_ResetSelectionTable(void)
{
	
	memset(&stCBSelectTab, 0x00, sizeof(stCBSelectTab));
	g_inCurrSelectNum = 0;
}


int inECR_AddSelectionItem(BYTE *pszIdx, BYTE *pszLabel)
{
	strcpy(stCBSelectTab[g_inCurrSelectNum].szSelectIdx, pszIdx);
	strcpy(stCBSelectTab[g_inCurrSelectNum].szSelectLabel, pszLabel);

	g_inCurrSelectNum++;
}

int inECR_FormCBSelectionHeader(BYTE *pszECN, BYTE *pszHeader)
{
	int inPos = 0;
	BYTE szECRHeader[MESSAGE_HEADER_SIZE + 1];

	memset(szECRHeader, 0x00, sizeof(szECRHeader));
	inPos = 0;
	
	/* 1. ECN*/
	memcpy(&szECRHeader[inPos], pszECN, ECN_SIZE);
	inPos += ECN_SIZE;

	/* 2. Function Code*/
	memcpy(&szECRHeader[inPos], ECR_FC_SELECTION, FUNC_CODE_SIZE);
	inPos += FUNC_CODE_SIZE;

	/* 3. Version Code*/
	memcpy(&szECRHeader[inPos], ECR_VERSION_CODE, ECR_VER_CODE_SIZE);
	inPos += ECR_VER_CODE_SIZE;

	/* 4. RFU*/
	memcpy(&szECRHeader[inPos], "\x30", ECR_RFU_SIZE);
	inPos += ECR_RFU_SIZE;

	/* 5. Separator*/
	memcpy(&szECRHeader[inPos], "\x1C", SEPARATOR_SIZE);
	inPos += SEPARATOR_SIZE;

	memcpy(pszHeader, szECRHeader, inPos);

	return inPos;
}


int inECR_CB_FormSelectionMsg(BYTE *pszECN, BYTE *pszName, BYTE *pCBMsg)
{
	int inIdx = 0;
	int inTotMsg = 0;
	BYTE szDispMsg[ECR_DISP_MSG_SIZE + 1];
	BYTE szECRMsg[512 + 1];
	
	BYTE szECRHeader[MESSAGE_HEADER_SIZE + 1];
	BYTE szECRBody[512 + 1];
	BYTE szSubFld[512 + 1];

	BYTE szASCNumItem[2 + 1];

	int inPos = 0;
	int inRet = 0;

	int inNumItems = g_inCurrSelectNum;

	vdDebug_LogPrintf("=====inECR_CB_FormSelectionMsg=====");

	memset(szECRMsg, 0x00, sizeof(szECRMsg));
	inPos = 0;
	
	/*Form Header*/
	memset(szECRHeader, 0x00, sizeof(szECRHeader));
	inRet = inECR_FormCBSelectionHeader(pszECN, szECRHeader);
	memcpy(&szECRMsg[inPos], szECRHeader, inRet);
	inPos += inRet;

	/*Form Sub field for Body*/
	memset(szSubFld, 0x00, sizeof(szSubFld));
	inRet = inECR_FormCBSubField(ECR_CB_FC_FLD_CODE, szGetSelectionType(), ECR_CB_FUNC_TYPE_SIZE, szSubFld);
	memcpy(&szECRMsg[inPos], szSubFld, inRet);
	inPos += inRet;

	memset(szSubFld, 0x00, sizeof(szSubFld));
	//inRet = inECR_FormCBSubField(ECR_CB_SEL_NAME_FLD_CODE, pszName, ECR_CB_SELECT_NAME_SIZE, szSubFld);
	inRet = inECR_FormCBSubField(ECR_CB_SEL_NAME_FLD_CODE, pszName, strlen(pszName), szSubFld);
	memcpy(&szECRMsg[inPos], szSubFld, inRet);
	inPos += inRet;

	memset(szASCNumItem, 0x00, sizeof(szASCNumItem));
	sprintf(szASCNumItem, "%02d", inNumItems);

	memset(szSubFld, 0x00, sizeof(szSubFld));
	inRet = inECR_FormCBSubField(ECR_CB_SEL_NUM_FLD_CODE, szASCNumItem, ECR_CB_NUM_OF_SELECT_SIZE, szSubFld);
	memcpy(&szECRMsg[inPos], szSubFld, inRet);
	inPos += inRet;

	for (inIdx = 0; inIdx<inNumItems; inIdx++)
	{
		memset(szSubFld, 0x00, sizeof(szSubFld));
		inRet = inECR_FormCBSubField(ECR_CB_SEL_IDX_FLD_CODE, stCBSelectTab[inIdx].szSelectIdx, ECR_CB_SELECT_IDX_SIZE, szSubFld);
		memcpy(&szECRMsg[inPos], szSubFld, inRet);
		inPos += inRet;

		memset(szSubFld, 0x00, sizeof(szSubFld));
		//inRet = inECR_FormCBSubField(ECR_CB_SEL_LAB_FLD_CODE, stCBSelectTab[inIdx].szSelectLabel, ECR_CB_SELECT_LAB_SIZE, szSubFld);
		inRet = inECR_FormCBSubField(ECR_CB_SEL_LAB_FLD_CODE, stCBSelectTab[inIdx].szSelectLabel, strlen(stCBSelectTab[inIdx].szSelectLabel), szSubFld);
		memcpy(&szECRMsg[inPos], szSubFld, inRet);
		inPos += inRet;
	}


	DebugAddHEX("szECRMsg", szECRMsg, inPos);
	vdDebug_LogPrintf("szECRMsg[%s]", szECRMsg);

	memcpy(pCBMsg, szECRMsg, inPos);

	return inPos;
}


int inECR_CB_Selection(BYTE *pszECN, BYTE *pszName)
{
	BYTE szECRMsg[512+1];
	int inLen = 0;
	int inRet = 0;
	VS_BOOL fWaitForAck = VS_TRUE;
	char chResp;
	int	inRecvLen = 0;
	ULONG tick;
	int ret;//USHORT ret;
	int inFunCode=0, inRetCode = 0;
    char szRespCode[2+1];
	int inReqlen=0;
    unsigned char szRequest[1024+1];
	int inResult = 0;
	int inOffset = 0;
	char szSlctIndex[2 + 1];
	char inMsgLen = 0;
	char szAscLen[4+1], szHexLen[4+1];
	int inNumRetry = 0;

	memset(szECRMsg, 0x00, sizeof(szECRMsg));	
	

	vdSetSelectionType("ACC");
    inLen = inECR_CB_FormSelectionMsg(pszECN, pszName, szECRMsg); 
	
	do{
		
		inRet = inECR_RS232CommSendBuf(szECRMsg, inLen, VS_TRUE);	
	
		inNumRetry++;

		/*Recv ACK?*/
		tick = CTOS_TickGet();
		
        ret = inECR_RS232RecvACK((char*)&chResp, &inRecvLen, 2);
        vdDebug_LogPrintf("ret[%d]", ret);
		if (ret == d_OK)
            break;

	}while (inNumRetry < MAXRETRIES);
	

	if (ret == TIME_OUT)
	{
		return TIME_OUT;
	}
	if (ret != d_OK)
		return -1;

	
	
	tick = CTOS_TickGet();
	
	do{

        ret = inECR_RS232CommRecvBuf(szECRMsg, &inRecvLen, 2);
        vdDebug_LogPrintf("ret[%d] inRecvLen[%d]",ret, inRecvLen);
        
		if (ret == d_OK)
            break;
		if (ret == d_KBD_CANCEL)
			return d_KBD_CANCEL;
		
	}while ((CTOS_TickGet() - tick) < 18000);

	if ((CTOS_TickGet() - tick) >= 18000)
	{
		return TIME_OUT;
	}
	else if (ret == TIME_OUT)
		return TIME_OUT;
	
	if (ret != d_OK)
            return -1;

	ret = inECR_RS232SendACK();

	if (ret != d_OK)
            return -1;

	
	inReqlen = inRecvLen - 3;
	memcpy(szRequest ,&szECRMsg[1], inReqlen);
    
//    inResult = inParseECRHeader(inReqlen, szRequest,&inFunCode, szRespCode);

    
	vdDebug_LogPrintf("inFunCode[%d] szRespCode[%s]",inFunCode, szRespCode);

	if(0 == memcmp(szRespCode, "00", 2))
	{
        inOffset += LENGTH_SIZE;
        memset(szSlctIndex, 0x00, sizeof(szSlctIndex));
        inOffset += MESSAGE_HEADER_SIZE + 2;

		memset(szHexLen, 0x00, sizeof(szHexLen));
		memset(szAscLen, 0x00, sizeof(szAscLen));

		memcpy(szHexLen,szRequest+inOffset,LENGTH_SIZE);
		wub_hex_2_str(szHexLen, szAscLen, LENGTH_SIZE);

		vdDebug_LogPrintf("szAscLen[%s]",szAscLen);
		inOffset += LENGTH_SIZE;

		inMsgLen = atol(szAscLen);

		vdDebug_LogPrintf("inMsgLen[%d]",inMsgLen);

        memset(szSlctIndex, 0x00, sizeof(szSlctIndex));
        memcpy(szSlctIndex,szRequest+inOffset,inMsgLen);
	}
	
	vdDebug_LogPrintf("szSlctIndex[%s]",szSlctIndex);
	//return atol(szSlctIndex[0] + '0');
	return atol(szSlctIndex);
}



int inECR_FormCBInfoHeader(BYTE *pszECN, BYTE *pszHeader)
{
	int inPos = 0;
	BYTE szECRHeader[MESSAGE_HEADER_SIZE + 1];

	memset(szECRHeader, 0x00, sizeof(szECRHeader));
	inPos = 0;
	
	/* 1. ECN*/
	memcpy(&szECRHeader[inPos], pszECN, ECN_SIZE);
	inPos += ECN_SIZE;

	/* 2. Function Code*/
	memcpy(&szECRHeader[inPos], ECR_FC_INFO, FUNC_CODE_SIZE);
	inPos += FUNC_CODE_SIZE;

	/* 3. Version Code*/
	memcpy(&szECRHeader[inPos], ECR_VERSION_CODE, ECR_VER_CODE_SIZE);
	inPos += ECR_VER_CODE_SIZE;

	/* 4. RFU*/
	memcpy(&szECRHeader[inPos], "\x30", ECR_RFU_SIZE);
	inPos += ECR_RFU_SIZE;

	/* 5. Separator*/
	memcpy(&szECRHeader[inPos], "\x1C", SEPARATOR_SIZE);
	inPos += SEPARATOR_SIZE;

	memcpy(pszHeader, szECRHeader, inPos);

	return inPos;
}


int inECR_CB_FormInfoMsg(BYTE *pszECN, BYTE byMsgIdx, BYTE *pCBMsg)
{
	int inIdx = 0;
	int inTotMsg = 0;
	BYTE szDispMsg[ECR_DISP_MSG_SIZE + 1];
	BYTE szECRMsg[512 + 1];
	
	BYTE szECRHeader[MESSAGE_HEADER_SIZE + 1];
	BYTE szECRBody[512 + 1];
	BYTE szSubFld[512 + 1];

	BYTE szASCMsgIdx[ECR_CB_MSGIDX_SIZE + 1];

	int inPos = 0;
	int inRet = 0;

	vdDebug_LogPrintf("=====inECR_CB_FormInfoMsg=====");

	memset(szECRMsg, 0x00, sizeof(szECRMsg));
	inPos = 0;
	
	/*Form Header*/
	memset(szECRHeader, 0x00, sizeof(szECRHeader));
	inRet = inECR_FormCBInfoHeader(pszECN, szECRHeader);
	memcpy(&szECRMsg[inPos], szECRHeader, inRet);
	inPos += inRet;

	/*Form Sub field for Body*/
	memset(szSubFld, 0x00, sizeof(szSubFld));
	inRet = inECR_FormCBSubField(ECR_CB_FC_FLD_CODE, "IFO", ECR_CB_FUNC_TYPE_SIZE, szSubFld);
	memcpy(&szECRMsg[inPos], szSubFld, inRet);
	inPos += inRet;

	memset(szASCMsgIdx, 0x00, sizeof(szASCMsgIdx));
	sprintf(szASCMsgIdx, "%02X", byMsgIdx);
	
	memset(szSubFld, 0x00, sizeof(szSubFld));
	inRet = inECR_FormCBSubField(ECR_CB_MSGIDX_FLD_CODE, szASCMsgIdx, ECR_CB_MSGIDX_SIZE, szSubFld);
	memcpy(&szECRMsg[inPos], szSubFld, inRet);
	inPos += inRet;

	/*Get Message*/
	memset(szDispMsg, 0x00, sizeof(szDispMsg));
	inRet = inGetCBDisplayInfoMsg(byMsgIdx, szDispMsg);
	
	memset(szSubFld, 0x00, sizeof(szSubFld));
	inRet = inECR_FormCBSubField(ECR_CB_MSG_FLD_CODE, szDispMsg, inRet, szSubFld);
	memcpy(&szECRMsg[inPos], szSubFld, inRet);
	inPos += inRet;

	DebugAddHEX("szECRMsg", szECRMsg, inPos);
	vdDebug_LogPrintf("szECRMsg[%s]", szECRMsg);

	memcpy(pCBMsg, szECRMsg, inPos);

	return inPos;
}


int inECR_CB_Info(BYTE *pszECN, BYTE byMsgIdx)
{
	BYTE szECRMsg[512+1];
	int inLen = 0;
	int inRet = 0;
	ULONG tick;
	char chResp;
	int	inRecvLen = 0;
        USHORT ret;
		
	int inNumRetry = 0;

	memset(szECRMsg, 0x00, sizeof(szECRMsg));

	inLen = inECR_CB_FormInfoMsg(pszECN, byMsgIdx, szECRMsg);

	inRet = inECR_RS232CommSendBuf(szECRMsg, inLen, VS_TRUE);	

	return d_OK;
}

// speed for pin entry
int inECR_CB_InfoEx(BYTE *pszECN, BYTE byMsgIdx)
{
	BYTE szECRMsg[512+1];
	int inLen = 0;
	int inRet = 0;
	ULONG tick;
	char chResp;
	int	inRecvLen = 0;
        USHORT ret;
		
	int inNumRetry = 0;

	memset(szECRMsg, 0x00, sizeof(szECRMsg));

	inLen = inECR_CB_FormInfoMsg(pszECN, byMsgIdx, szECRMsg);

	inRet = inECR_RS232CommSendBufEx(szECRMsg, inLen, VS_TRUE);

	return d_OK;
}


