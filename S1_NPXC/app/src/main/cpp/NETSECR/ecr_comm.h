
#ifndef _ECR_COMM_H
#define	_ECR_COMM_H

#ifdef	__cplusplus
extern "C" {
#endif


void vdECR_SetCommPort(int CommPort);

int inECR_RS232CommPortOpen(ULONG ulBaudRate, BYTE bParity, BYTE bDataBits, BYTE bStopBits);
int inECR_RS232CommSendBuf(char *szSendBuf, int inlen, VS_BOOL fPadStxEtx);
int inECR_RS232CommRecvBuf(char *szRecvBuf,int *inlen, int timeout);
int inECR_RS232CommPortClose(void);



#ifdef	__cplusplus
}
#endif

#endif	/* _ECR_COMM_H */

