/**************************************************************************
 * @doc NETSECR
 * @module NETSECR ECRTran.H |
 * Source code for the ECR handling functions.
 * Product         :   SoftPay 2000 (Global Payment Methodology).
 * <nl>Developer   :   SoftPay for NETS Development Team (I_NETS).
 * <nl>Notes       :
 * @head3 ECRTran Functions |
 * @index | ECRTran
 * @end
 *
 * Copyright (c) 1996-2000 by VeriFone Inc. All rights reserved.
 *
 * No part of this software may be used, stored, compiled, reproduced,
 * modified, transcribed, translated, transmitted, or transferred, in any form
 * or by any means whether electronic, mechanical, magnetic, optical,
 * or otherwise, without the express prior written permission of VeriFone, Inc.
 *
 * Revision History
 * ------------------------------------------------------------------------
 $Log: ecrtran.h,v $
// #CREDITCARD_ECR from verifone		//porting @WielyRabin Nov 2011
 Revision 1.6.6.1  2011-02-01 03:52:57  kenny
 Vx227.01
 1. added the ecr message to credit application in rck file.
 2. update the credit card message with "JCB", "CUP", & "AMEX".
 3. Disabled the credit ecr commands.
 4. update external pinpad (vx810) comm format to 'A8N1'.

 Revision 1.6  2010-08-24 02:18:23  kenny
 Vx224.01 part A
 - enable atu activation feature ("ECR portion")
// #CREDITCARD_ECR from verifone		//porting @WielyRabin Nov 2011
 
 Revision 1.5  2009-06-17 13:40:56  julee
 Refer to Doc Of Chage 209.09
 1)    UI changes to menu

 2)    Fix unable to purge contactless reversal when triggered by another online transacstion

 3)    Fix TransType 96 not counted and printed on settlement receipt during batch upload

 4)    Fix Trans Code 96 not printed on receipt when Fee is turned on

 5)    Fix unable to void Contactless Purchase and GB Purchase+topup

 6)    Fix receipt not printed for Topup Card Error.

 7)    Fix reversal list printing, display �Can�t Read� message, and wrong CAN/ExpiryDate/Balance printed.

 8)    Fix when connected to Vx810 pinpad, never prompt to �Insert Nets CashCard�

 9)    Fix Mobile Phone number entry, press cancel, but continue to prompt for tap card

 10)Validate Bank Option enable/disable flag for contactless topup

 11)Fix to allow Void for contactless purchase and GB purchase+topup

 Revision 1.4  2009-06-04 06:46:37  julee
 Refer to Doc Of Chage 209.06C
 1) remove hardcode SSL for Ethernet and WIFI
 2) other bugs

 Revision 1.3  2009-05-13 10:23:14  julee
 Refer to Doc Of Chage
 1) CSM Mode
 2) Fix UI, and display to pinpad
 3) GB
 4) various bug fixes

 Revision 1.2  2009-04-11 17:01:30  julee
 cepas 2 ecr

 Revision 1.1  2006/06/14 07:36:29  julee
 inital checkin for combine base for omni 3750 and vx

 Revision 1.5  2006/04/10 04:56:43  julee
 NETS0107.04 enhancement for cashback amount in multiple of $50. plus some other bug fixes. refer to DocOfChange.txt for NETS0107.04

 Revision 1.4  2006/03/22 10:54:30  julee
 NETSV0107.01 add VOID functions plus bug fixes. refer to DocOfChange.tzt

 Revision 1.3  2006/01/09 07:23:09  yusak
 NETSV0106.10 supposed to fix bug 216, 390, 394, 395, and STAN increment when HOST CONNECT FAILURE occurs.

 Revision 1.2  2005/12/04 16:25:56  yusak
 NETSV0106m02, Yusak Rabin, 04/12/2005

 Revision 1.3  2005/06/27 11:48:11  yusak
 Release 102


********************************************************************************/

#ifndef _ECRTRAN_H_
#define _ECRTRAN_H_

#define TRANSACTION_OBJECT	TRANS_DATA_TABLE

////////////////////////////////////////////////////////////////////
//
// E X T E R N   D E C L A R A T I O N S
//


////////////////////////////////////////////////////////////////////
//
// F U N C T I O N   P R O T O T Y P E S
//
void vdAddECRTranFunctions ( void );
int inNETSECRSwitchApp (void);
int inECROperations (int inReqlen, unsigned char *stRequest, unsigned char *stResponse, int *pinRespLen);

int inECRSetDataForPurAmount(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int Amtsize);
int inECRSetDataForCashBackAmount(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int Amtsize);
int inECRSetDataForInvNum(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int RefNumsize);
int inECRSetDataForTopupOption(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int Optsize);
int inECRSetDataForRRN(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int RRNsize);
int inECRSetDataForTranDate(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int Datesize);
int inECRSetDataForTranTime(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int Timesize);
int inECRSetDataForTID(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int TIDsize);
//liu add for transcab phase 2
int inECRSetTaxiDriverID(TRANSACTION_OBJECT * pobTran, unsigned char szFuncCode, unsigned char * ucRequest, int inSize);
int inECRSetTaxiVehicleID(TRANSACTION_OBJECT * pobTran, unsigned char szFuncCode, unsigned char * ucRequest, int inSize);

int inECRGetDataForRFU(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetDataForRFUinHex(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetDataForMSN(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetDataForTranDate(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inDatesize);
int inECRGetDataForTranTime(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inTimesize);
int inECRGetDataForAppCode(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetDataForTermRefNum(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetDataForSTAN(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSTANsize);
int inECRGetDataForTID(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inTIDSize);
int inECRGetDataForMID(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inMIDSize); 
int inECRGetDataForCardIssrName(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetDataForCardNumber(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetDataForExpDate(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetDataForBatchNumber(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetDataForRRN(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetDataForCardIssrID(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetDataForCashcardCAN(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetDataForTC(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetDataForCTC(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetDataForBlktVer(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetDataForCSC(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetDataForCheckSum(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetDataForCCbalance(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRFormAndSendResponse(TRANSACTION_OBJECT* pobTran);
// Andy_F1 Singpools Phase 2
int inECRGetDataForServiceFee(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
// End Andy_F1

int inECRGetDataForRFUField02(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);

//ju add for NETS CUPS 030406
int inECRGetDataForForeignAmt(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetDataForForeignMID(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
//end ju NETS CUPS
//ju add for TOPUP with FEE 070606
int inECRGetDataForFeeToMerchant(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetDataForFeeFromMerchant(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
//end ju add for TOPUP with FEE 070606

int inECRGetDataForPurAmount(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int Amtsize);
int inECRGetDataForTopupTotalFee(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetDataForTopupFeeToMerchant(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetDataForTopupFeeFromMerchant(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetDataForExpiryDate(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetDataForResponseMessage1(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetDataForResponseMessage2(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetDataForPurchaseFee(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetAdditionalTxnInfo(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetDataForCepasVersion(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetDataForTransData(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetDataForPurseStatus(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetDataForATUStatus(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetDataForATUAmt(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRSetDataForRecordIndex(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRSetDataForNumOfRec(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetDataForNumOfRecRead(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetDataForTransLog(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetDataForMaxLogCount(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRSetDataForMobileNumber(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRSetDataForCashcardCAN(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);

/*
   New functions for new ECR CUP transaction 
   @WielyRabin 26 July 2011 
*/
int inECRSetDataForAUTHID(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int AuthIDsize);


// @WielyRabin 04 Aug 2011 Cash Deposit 
int inECRGetDataForCashDepositFee(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetDataForCashDepositNett(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);


// #CREDITCARD_ECR from verifone		//porting @WielyRabin Nov 2011
int inECRSetDataForATUAmt(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRSetDataForAcquirername(TRANSACTION_OBJECT * pobTran, unsigned char szFuncCode, unsigned char * ucRequest, int inSize);
int inECRGetDataForCreditResponseText(TRANSACTION_OBJECT * pobTran, unsigned char szFuncCode, unsigned char * ucRequest, int inSize);
int inECRGetDataForCreditCardHolderName(TRANSACTION_OBJECT * pobTran, unsigned char szFuncCode, unsigned char * ucRequest, int inSize);
int inECRGetDataForEMVAID(TRANSACTION_OBJECT * pobTran, unsigned char szFuncCode, unsigned char * ucRequest, int inSize);
int inECRGetDataForEMVProfile(TRANSACTION_OBJECT * pobTran, unsigned char szFuncCode, unsigned char * ucRequest, int inSize);
int inECRGetDataForEMVCryptogram(TRANSACTION_OBJECT * pobTran, unsigned char szFuncCode, unsigned char * ucRequest, int inSize);
int inECRGetDataForEMVTransCert(TRANSACTION_OBJECT * pobTran, unsigned char szFuncCode, unsigned char * ucRequest, int inSize);
int inECRGetDataForEMVTSI(TRANSACTION_OBJECT * pobTran, unsigned char szFuncCode, unsigned char * ucRequest, int inSize);
int inECRGetDataForEMVTVR(TRANSACTION_OBJECT * pobTran, unsigned char szFuncCode, unsigned char * ucRequest, int inSize);
int inECRSetDataForInvoiceNum(TRANSACTION_OBJECT * pobTran, unsigned char szFuncCode, unsigned char * ucRequest, int inSize);
int inECRGetDataForInvoiceNum(TRANSACTION_OBJECT * pobTran, unsigned char szFuncCode, unsigned char * ucRequest, int inSize);
// #CREDITCARD_ECR from verifone		//porting @WielyRabin Nov 2011
int inSendECRTogglingSwitchResponse(TRANSACTION_OBJECT* pobTran);
// [NETSPLUS]: SN: For Release 312.01 and 604.01
// sn add for NETS Shop N Save Loyalty
int inECRSetDataForLoyaltyRedemptionFlag(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int Amtsize);
int inECRGetDataForCurrencyName(TRANSACTION_OBJECT * pobTran, unsigned char szFuncCode, unsigned char * ucRequest, int inSize);
int inECRGetDataForIssuerCategory(TRANSACTION_OBJECT * pobTran, unsigned char szFuncCode, unsigned char * ucRequest, int inSize);
int inECRGetDataForLoyaltyProgramName(TRANSACTION_OBJECT * pobTran, unsigned char szFuncCode, unsigned char * ucRequest, int inSize);
int inECRGetDataForLoyaltyProgramExpDate(TRANSACTION_OBJECT * pobTran, unsigned char szFuncCode, unsigned char * ucRequest, int inSize);
int inECRGetDataForLoyaltyType(TRANSACTION_OBJECT * pobTran, unsigned char szFuncCode, unsigned char * ucRequest, int inSize);
int inECRGetDataForLoyaltyMarketingMessage(TRANSACTION_OBJECT * pobTran, unsigned char szFuncCode, unsigned char * ucRequest, int inSize);
int inECRGetDataForLoyaltyRedemptionValue(TRANSACTION_OBJECT * pobTran, unsigned char szFuncCode, unsigned char * ucRequest, int inSize);
int inECRGetDataForLoyaltyCurrentBalance(TRANSACTION_OBJECT * pobTran, unsigned char szFuncCode, unsigned char * ucRequest, int inSize);
int inECRGetDataForSignatureRequiredFlag(TRANSACTION_OBJECT * pobTran, unsigned char szFuncCode, unsigned char * ucRequest, int inSize);
int inECRGetDataForPOSMessage(TRANSACTION_OBJECT * pobTran, unsigned char szFuncCode, unsigned char * ucRequest, int inSize);
int inECRGetDataForCardName(TRANSACTION_OBJECT * pobTran, unsigned char szFuncCode, unsigned char * ucRequest, int inSize);
int inECRGetDataForLoyaltyMarketingMessage6Lines(TRANSACTION_OBJECT * pobTran, unsigned char szFuncCode, unsigned char * ucRequest, int inSize);
//end [NETSPLUS]

int inECRSetDataForTransactionIndicator(TRANSACTION_OBJECT *pobTran, unsigned char szFuncCode, unsigned char * ucRequest, int inSize);

//liu add for transcab phase 2 project
int inECRGetDataForTaxiAcquirername(TRANSACTION_OBJECT * pobTran, unsigned char szFuncCode, unsigned char * ucRequest, int inSize);
int inECRGetDataForTaxiDBSHostNum(TRANSACTION_OBJECT * pobTran, unsigned char szFuncCode, unsigned char * ucRequest, int inSize);
//end transcab phase 2 project

//WAZER #RSVPDev ECR functions
int inECRRSVPGetGroupIDList(TRANSACTION_OBJECT *pobTran, unsigned char szFuncCode, unsigned char * ucRequest, int inSize);
int inECRRSVPSetGroupID(TRANSACTION_OBJECT *pobTran, unsigned char szFuncCode, unsigned char * ucRequest, int inSize);
int inECRRSVPSetItemCode(TRANSACTION_OBJECT *pobTran, unsigned char szFuncCode, unsigned char * ucRequest, int inSize);
int inECRRSVPSetItemCount(TRANSACTION_OBJECT *pobTran, unsigned char szFuncCode, unsigned char * ucRequest, int inSize);
int inECRRSVPSetMerchDollar(TRANSACTION_OBJECT *pobTran, unsigned char szFuncCode, unsigned char * ucRequest, int inSize);
int inECRRSVPGetPromoData(TRANSACTION_OBJECT *pobTran, unsigned char szFuncCode, unsigned char * ucRequest, int inSize);
int inECRRSVPGetGroupID(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRRSVPGetItemCode(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRRSVPGetItemCount(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRRSVPGetMerchDollar(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRRSVPGetItemCountPrev(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRRSVPGetMerchDollarPrev(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRRSVPGetItemCountFinal(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRRSVPGetMerchDollarFinal(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRRSVPGetCardData(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetCSN(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetEftposSettleData(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetC1SettleData(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetC2SettleData(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetHostData1(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetHostData2(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetHostData3(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRGetHostData4(TRANSACTION_OBJECT *pobTran,unsigned char szFuncCode,unsigned char * ucRequest, int inSize);
int inECRSendBufferedResponse(TRANSACTION_OBJECT* pobTran);
#endif /* _ECRTRAN_H_ */
