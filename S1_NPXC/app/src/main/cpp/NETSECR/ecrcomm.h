#ifndef _ECRCOMM_H_
#define _ECRCOMM_H
#define FIELD_CODE_SIZE         2

#define SHORT   short                // 2byte
#define UINT    unsigned int         // 4byte
#define DWORD   unsigned int         // 4byte
#define BOOL    unsigned char        // 1byte
#define LONG    long                 // 4byte
#define ULONG   unsigned long        // 4byte
//#define EXPIRY_DATE_ASC_SIZE              4
//#define EXPIRY_DATE_BCD_SIZE               ((EXPIRY_DATE_ASC_SIZE+1)/2)
//
//#define AMT_ASC_SIZE                      12/* BCD - includes cents */
//#define AMT_BCD_SIZE                       ((AMT_ASC_SIZE+1)/2)
//
//
//#define DATE_ASC_SIZE                     4
//#define DATE_BCD_SIZE                      ((DATE_ASC_SIZE+1)/2)
//
//#define TIME_ASC_SIZE                     6
//#define TIME_BCD_SIZE                      ((TIME_ASC_SIZE+1)/2)
//
//#define TERMINAL_ID_BYTES          8
//#define MERCHANT_ID_BYTES          15
//#define PRO_CODE_ASC_SIZE       6
//#define PRO_CODE_BCD_SIZE       ((PRO_CODE_ASC_SIZE+1)/2)
//
//#define MTI_ASC_SIZE            4
//#define MTI_BCD_SIZE            ((MTI_ASC_SIZE+1)/2)
//#define BATCH_NO_ASC_SIZE   6
//#define BATCH_NO_BCD_SIZE   ((BATCH_NO_ASC_SIZE+1)/2)
//#define AUTH_CODE_DIGITS           6
//#define RRN_BYTES                  12
//#define TPDU_ASC_SIZE              10
//#define TPDU_BCD_SIZE           ((TPDU_ASC_SIZE+1)/2)
//#define INVOICE_ASC_SIZE        6
//#define INVOICE_BCD_SIZE        ((INVOICE_ASC_SIZE+1)/2)
//#define CARD_HOLD_NAME_DIGITS      30
//#define STORE_ID_DIGITS            18
//#define RESP_CODE_SIZE             2
//#define PAN_SIZE                   19
//#define TRACK_I_BYTES              85	//85
//#define TRACK_II_BYTES             41	//41
//#define TRACK_III_BYTES            200	//64
//#define CHIP_DATA_LEN              1024
//#define ADD_DATA_LEN             1024
//#define CVV2_BYTES                 6
//
//typedef struct
//{
//    BYTE        byTransType;
//    BYTE        byPanLen;
//    BYTE        szExpireDate[EXPIRY_DATE_BCD_SIZE+1];    /* BCD YYMM        */
//    BYTE        byEntryMode;
//    BYTE        szTotalAmount[AMT_BCD_SIZE+1];   /* BCD total   amount    */
//    BYTE        szBaseAmount[AMT_BCD_SIZE+1];
//    BYTE        szTipAmount[AMT_BCD_SIZE+1];
//    BYTE        szTID[TERMINAL_ID_BYTES+1];
//    BYTE        szMID[MERCHANT_ID_BYTES+1];
//    BYTE        szHostLabel[16];
//    BYTE        szBatchNo[BATCH_NO_BCD_SIZE+1];
//    BYTE        byOrgTransType ;
//    BYTE        szMacBlock[8] ;
//    BYTE        szYear[2];                 /* int 1997=97,2003=103    */
//    BYTE        szDate[DATE_BCD_SIZE+1];     /* BCD MMDD        */
//    BYTE        szTime[TIME_BCD_SIZE+1];     /* BCD hhmmss        */
//    BYTE        szOrgDate[DATE_BCD_SIZE+1];     /* BCD MMDD        */
//    BYTE        szOrgTime[TIME_BCD_SIZE+1];     /* BCD hhmmss        */
//    BYTE        szAuthCode[AUTH_CODE_DIGITS+1];
//    BYTE        szRRN[RRN_BYTES+1];
//    BYTE        szInvoiceNo[INVOICE_BCD_SIZE+1];
//    BYTE        szOrgInvoiceNo[INVOICE_BCD_SIZE+1];
//    BYTE        byPrintType;//for trans comms dailbakup
//    BYTE        byVoided;
//    BYTE        byAdjusted;
//    BYTE        byUploaded;
//    BYTE        byTCuploaded;    /* For upload TC*/
//    BYTE        szCardholderName[CARD_HOLD_NAME_DIGITS+1];
//    BYTE        szzAMEX4DBC[4+1];
//    BYTE        szStoreID[STORE_ID_DIGITS+1]; //use this one to store how much amount fill up in DE4 for VOID
//    BYTE        szRespCode[RESP_CODE_SIZE+1];
//    BYTE        szServiceCode[3+1];
//    BYTE        byContinueTrans;
//    BYTE        byOffline;
//    BYTE        byReversal;
//    BYTE        byEMVFallBack;
//    SHORT       shTransResult;
//    BYTE        szTpdu[TPDU_ASC_SIZE+1];
//    BYTE        szIsoField03[PRO_CODE_BCD_SIZE+1];
//    BYTE        szMassageType[MTI_BCD_SIZE+1];
//    BYTE        szPAN[PAN_SIZE+1];
//    BYTE        szCardLable[20+1];
//    USHORT      usTrack1Len;
//    USHORT      usTrack2Len;
//    USHORT      usTrack3Len;
//    BYTE        szTrack1Data[TRACK_I_BYTES+1];
//    BYTE        szTrack2Data[TRACK_II_BYTES+1];
//    BYTE        szTrack3Data[TRACK_III_BYTES+1];
//    USHORT      usChipDataLen;		//Chip Data
//    BYTE        baChipData[CHIP_DATA_LEN+1];
//    USHORT      usAdditionalDataLen;	//Additional Data
//    BYTE        baAdditionalData[ADD_DATA_LEN+1];
//    BYTE		bWaveSID;
//    USHORT		usWaveSTransResult;	// Transction Result
//    BYTE		bWaveSCVMAnalysis;
//    ULONG       ulTraceNum;
//    ULONG       ulOrgTraceNum;
//    USHORT      usTerminalCommunicationMode;
//    BYTE        IITid;
//    BYTE        HDTid; //kobe added
//    ULONG       ulSavedIndex;// kobea added
//    BYTE        byPINEntryCapability;
//    BYTE        byPackType;
//    int  		inCardType;
//    UINT 		MITid;
//    BYTE        szOrgAmount[AMT_BCD_SIZE+1];
//    BYTE        szCVV2[CVV2_BYTES+1];
//    BYTE        byTCFailUpCnt;//20121204
//    BYTE		szPINBlock[8+1];
//    BYTE		szKSN[20+1];
//    SHORT  		CDTid;
//    emvinfo_t   stEMVinfo;
//}TRANS_DATA_TABLE;

#define TRANSACTION_OBJECT	TRANS_DATA_TABLE
//
////typedef int (*PFGETSET)(TRANS_DATA_TABLE *srTransPara, unsigned char , unsigned char *, int);//
typedef int (*PFGETSET)(TRANSACTION_OBJECT *pobTran, unsigned char , unsigned char *, int);
//
//typedef struct
//{
//    unsigned char stFieldCode[FIELD_CODE_SIZE + 1];
//    unsigned char ucFieldIndex;
//    int inFieldLen;
//    PFGETSET pinSetField;
//    PFGETSET pinGetField;
//}FIELDTABLE;

typedef int (*DFUNCTION_VAL)(TRANS_DATA_TABLE *srTransPara);

typedef struct
{
    int inFuncCode;
    DFUNCTION_VAL shValidationFunction;
    unsigned char stReqdField[MAX_FIELDS_SIZE+1][FIELD_CODE_SIZE + 1];
    unsigned char stOptionalField[MAX_FIELDS_SIZE+1][FIELD_CODE_SIZE + 1];
    unsigned char stRespFields[MAX_FIELDS_SIZE+1][FIELD_CODE_SIZE + 1];
}ECRTRAN;

extern ECRTRAN srECRTran[];

typedef struct _ECR_FUNC_CODE_ASCII_NUMBER_MAP_		// #CREDITCARD_ECR from verifone		//porting @WielyRabin Nov 2011
{
	char szFuncCode[3];
	int inFuncCode;
} ECR_FUNC_CODE_ASCII_NUMBER_MAP;						// #CREDITCARD_ECR from verifone		//porting @WielyRabin Nov 2011

////////////////////////////////////////////////////////////////////
//
// E X T E R N   D E C L A R A T I O N S
//


////////////////////////////////////////////////////////////////////
//
// F U N C T I O N   P R O T O T Y P E S
//
int inEcRRecvPModePacket(int inHandle, char *pchMsg, int inMaxMsgSize, VS_BOOL fValidate, int inRespTimeOut, int inMaxRetries);
int inEcRSendComPacket(int inHandle, char *pchMsg, int inMsgSize, VS_BOOL fPadStxEtx, 
                    int inACKTimeOut, VS_BOOL fWaitForAck, int inMaxRetries);
int inEcRSCTxPacket(int inPortHandle, const unsigned char *pucTxBuf, int inTxSize,
                           int inTimeOutSec, int inMaxRetries);
int inEcRSCRxPacket(int inPortHandle,unsigned char *pucRxBuf, int  inRxSize, int inTimeOutSec, int inMaxRetries);
int inEcRValidatePacket(char *pucPkt, int inSize);
int inEcRRecvCModePacket(int inHandle, char *pchRecBuf, int inMaxRecSize, VS_BOOL fStripStxEtx, 
                      int inRespTimeOut, VS_BOOL fSendAck, int inMaxRetries);
int inParseECRHeader(int inPktlength,unsigned char *pchHedBuff,int *pinFunCode,char *pchRespCode);
int inParseandStoreECRData(TRANS_DATA_TABLE *srTransPara, int inPktlength,unsigned char *pchHedBuff);//int inParseandStoreECRData(TRANSACTION_OBJECT *pobTran, int inPktlength,unsigned char *pchHedBuff);
int inSaveData(TRANS_DATA_TABLE *srTransPara, char* szFieldCode,int inLength,unsigned char *pchBuff);//int inSaveData(TRANSACTION_OBJECT *pobTran, char* szFieldCode,int inLength,unsigned char *pchBuff);
int inFormECRResponse(TRANS_DATA_TABLE *srTransPara,unsigned char *pchBuff,ECRTRAN *psrECRtran,char *pchRespCode);//int inFormECRResponse(TRANSACTION_OBJECT *pobTran,unsigned char *pchBuff,ECRTRAN *psrECRtran,char *pchRespCode);
int inFormECRHeader(unsigned char *pchHeadBuff,int inFunCode,char *pchRespCode);
int inFormECRData(TRANS_DATA_TABLE *srTransPara,int inHeaderLen,ECRTRAN *psrECRtran,unsigned char *pchHedBuff);//int inFormECRData(TRANSACTION_OBJECT *pobTran,int inHeaderLen,ECRTRAN *psrECRtran,unsigned char *pchHedBuff);
int inRetFieldTable(unsigned char *pchField,FIELDTABLE *psrField);
int inFormECRLastResponse(TRANS_DATA_TABLE *srTransPara,unsigned char *pchBuff,ECRTRAN *psrECRtran,char *pchRespCode);//int inFormECRLastResponse(TRANSACTION_OBJECT *pobTran,unsigned char *pchBuff,ECRTRAN *psrECRtran,char *pchRespCode);

#endif /* _ECRCOMM_H_ */

