
#ifndef _ECR_CB_H
#define	_ECR_CB_H

#ifdef	__cplusplus
extern "C" {
#endif


#define ECR_FC_HSPT				"CA" // Host message pass through
#define ECR_FC_DISP_INFO		"CB"
#define ECR_FC_SELECTION		"CC"
#define ECR_FC_INFO				"CD"

#define ECR_CB_FC_FLD_CODE				"M0"
#define ECR_CB_MSGIDX_FLD_CODE			"M1"
#define ECR_CB_MSG_FLD_CODE				"M2"
#define ECR_CB_SEL_NUM_FLD_CODE			"M3"
#define ECR_CB_SEL_IDX_FLD_CODE			"M4"
#define ECR_CB_SEL_LAB_FLD_CODE			"M5"
#define ECR_CB_SEL_NAME_FLD_CODE		"M6"
#define ECR_CB_PAYLOAD_FLD_CODE			"M7"

#define ECR_CB_FUNC_TYPE_SIZE		3
#define ECR_CB_MSGIDX_SIZE			2
#define ECR_CB_MSG_SIZE				69

#define ECR_CB_SELECT_IDX_SIZE		2
#define ECR_CB_SELECT_LAB_SIZE		21

#define ECR_CB_SELECT_NAME_SIZE		23
#define ECR_CB_NUM_OF_SELECT_SIZE	2

#define ECN_SIZE				12
#define FUNC_CODE_SIZE          2
#define ECR_VER_CODE_SIZE		2
#define ECR_RFU_SIZE			1
#define ECR_DISP_MSG_SIZE		69

typedef struct tagECRCallbackHeader
{
	BYTE szECN[ECN_SIZE + 1];
	BYTE szFuncCode[FUNC_CODE_SIZE + 1];
	BYTE szVerCode[ECR_VER_CODE_SIZE + 1];
	BYTE szRFU[ECR_RFU_SIZE + 1];
}ECR_CB_HEADER;

/*EMI -- ECR MSG ID*/
#define EMI_PLS_INS_CARD	0x01
#define EMI_PLS_SWP_CARD	0x02
#define EMI_PLS_TAP_CARD	0x03
#define EMI_PLS_IST_CARD	0x04
#define EMI_PLS_IT_CARD		0x05
#define EMI_PLS_ST_CARD		0x06
#define EMI_PLS_IS_CARD		0x07
#define EMI_PLS_RM_CARD		0x08
#define EMI_PLS_NFP_CARD	0x09
#define EMI_PLS_CASH_CARD	0x0A
#define EMI_ENTER_PIN		0x0B
#define EMI_ENTER_PIN2		0x0C
#define EMI_REENTER_PIN		0x0D
#define EMI_PROCESSING		0x0E
#define EMI_PIN_STAR1	    0x0F
#define EMI_PIN_STAR2	    0x10
#define EMI_PIN_STAR3		0x11
#define EMI_PIN_STAR4		0x12
#define EMI_PIN_STAR5		0x13
#define EMI_PIN_STAR6		0x14
#define EMI_PIN_STAR7		0x15
#define EMI_PIN_STAR8		0x16
#define EMI_PIN_STAR9		0x17
#define EMI_PIN_STAR10		0x18
#define EMI_PIN_STAR11		0x19
#define EMI_PIN_STAR12		0x1A
#define EMI_PIN_BACKSPACE	0x1B



typedef struct tagECRDispMsgTable
{
	BYTE byMsgIdx;
	BYTE szMsg[ECR_DISP_MSG_SIZE + 1];
}ECR_CB_MSG_TAB;

#define ECR_CB_MSG_TAB_SIZE		sizeof(ECR_CB_MSG_TAB)


typedef struct tagECRDispInfoCBBody
{
	BYTE byMsgIdx;
	BYTE szMsg[ECR_DISP_MSG_SIZE + 1];
}ECR_CB_DISP_INFO;


typedef struct tagECRSelectionItem
{
	BYTE szSelectIdx[ECR_CB_SELECT_IDX_SIZE + 1];
	BYTE szSelectLabel[ECR_CB_SELECT_LAB_SIZE + 1];
}ECR_CB_SELECT_ITEM;

#define MAX_SELECT_NUM		20

/*APIs*/
int inECR_CB_DisplayInfo(BYTE *pszECN, BYTE byMsgIdx);
int inECR_CB_DisplayInfoEx(BYTE *pszECN, BYTE byMsgIdx);
int inECR_CB_Selection(BYTE *pszECN, BYTE *pszName);
int inECR_CB_HostMessage(BYTE *pszECN, BYTE *pacHostMsg, int inMsgSize);
int inECR_CB_Info(BYTE *pszECN, BYTE byMsgIdx);
int inECR_CB_InfoEx(BYTE *pszECN, BYTE byMsgIdx);


#ifdef	__cplusplus
}
#endif

#endif	/* _ECR_CB_H */

