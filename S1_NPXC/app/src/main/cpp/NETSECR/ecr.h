
#ifndef _ECR_H
#define	_ECR_H

#ifdef	__cplusplus
extern "C" {
#endif

#define ECRLASTTXN "/home/<USER>/pub/lastecn.txt"
#define ECRLASTSETTLERCPT "/home/<USER>/pub/NPXLSRCPT.txt"
#define ECREXTRARESP	"/home/<USER>/pub/ecrextra.txt"


#define ECR_LASTTXN_FILE				"ecrlasttxn.txt"
#define ECR_LASTSETTLE_FILE				"ecrlastsettle.txt"
//#define ECR_LASTSETTLE_RCPT_FILE		"ecrlastsettlercpt.txt"


#define ECR_HOST_MESSAGE      100
#define ECR_DISPLAY_MESSAGE   101
#define ECR_SELECT_MESSAGE    102
#define ECR_INFO_MESSAGE      103

#define ECR_EFTPOS_PURCHASE   30
#define ECR_FLASHPAY_PURCHASE 24
#define ECR_LOGON             80
#define SETTLEMENT            81
#define ECR_CHECK_STATUS      55
#define ECR_GET_LASTTRAN      56
#define ECR_GET_LASTSETTLE	  85


#define FILLER_OFFSET          12
#define FUNC_CODE_SIZE          2
#define MSG_TAG_SIZE            2
#define RESPONSE_CODE_SIZE      2
#define EOM_SIZE                1
#define SEPARATOR_SIZE          1
#define MESSAGE_HEADER_SIZE    18
#define LENGTH_SIZE             2
#define FIELD_CODE_SIZE         2
#define MAX_FIELDS_SIZE        35

#define ECN_SIZE				12
#define ECR_VER_CODE_SIZE		2
#define ECR_RFU_SIZE			1
#define ECR_DISP_MSG_SIZE		69


/* ERROR TYPES */
#define INVALID_EOM            -100
#define INVALID_SEPARATOR      -101
#define INVALID_HEADER         -102
#define INVALID_DATA           -103
#define FIELD_NOT_FOUND        -104
#define INVALID_DATA_LEN       -105

/*NETS: ECR TAG -- sidumili*/
#define ECR_SEPARATOR		 	     0x1C
#define ECR_CASH_BACK_AMT            "42"
#define ECR_VERSION_CODE	         "01"
#define ECR_RESP_TEXT_TAG		     "02"
#define ECR_MERCHANT_NAME_TAG	     "D0"
#define ECR_DATE_TAG			     "03"
#define ECR_TIME_TAG			     "04"
#define ECR_TID_TAG				     "16"
#define ECR_MID_TAG				     "D1"
#define ECR_STAN_TAG		         "65"
#define ECR_AUTH_CODE_TAG		     "01"
#define ECR_RREF_TAG			     "D3"
#define ECR_CARD_NAME_TAG            "L7"
#define ECR_TRANSACTION_AMT_TAG      "40"
#define ECR_SERVICE_FEE_TAG          "41"
#define ECR_POS_MSG_TAG              "L5"
#define ECR_RESP_MSG1_TAG            "R0"
#define ECR_RESP_MSG2_TAG            "R1"
#define ECR_LOYALTY_PRGNAME_TAG      "L1"
#define ECR_LOYALTY_PRGEXPDATE_TAG   "L8"
#define ECR_LOYALTY_TYPE_TAG         "L2"
#define ECR_LOYALTY_MKTG_MSG_TAG     "L9"
#define ECR_REDEMPTION_VALUE_TAG     "L3"
#define ECR_CUR_LOYALTY_BAL_TAG      "L4"
#define ECR_HOST_RESP_CODE_TAG       "HC"
#define ECR_CARD_ENTRY_MODE_TAG      "CN"
#define ECR_ENHANCED_RREF_TAG        "HD"
#define ECR_RECEIPT_TEXT_FMT_TAG     "RP" 
#define ECR_REF_NUM                  "H4"

// NFP -- sidumili
#define ECR_BATCHNUM_TAG              "50"
#define ECR_NFP_STAN_TAG		      "H6"
#define ECR_TOTALFEETU_TAG            "ZA"
#define ECR_FEEDUETOMERCHTU_TAG       "ZB"
#define ECR_FEEDUEFROMMERCHTU_TAG     "ZC"
#define ECR_CARDNUMBER_TAG            "30"
#define ECR_EXPIRYDATE_TAG            "C2"
#define ECR_PURCHASEFEE_TAG           "ZP"
#define ECR_CARDISSUERNAME_TAG        "D2"
#define ECR_ADDTRXNINFO_TAG           "ZT"
#define ECR_CEPASVERSION_TAG          "C1"
#define ECR_CEPASTRANSDATA_TAG        "C0"

// SETTLE -- sidumili
#define ECR_NETS_PURCHASETCNT_TAG                      "N1" 
#define ECR_NETS_PURCHASETAMT_TAG                      "G1"
#define ECR_NETS_VOIDPURCHASETCNT_TAG                  "N2" 
#define ECR_NETS_VOIDPURCHASETAMT_TAG                  "G2"
#define ECR_UNIONPAY_PURCHASETCNT_TAG                  "N3" 
#define ECR_UNIONPAY_PURCHASETAMT_TAG                  "G3"
#define ECR_UNIONPAY_VOID_PURCHASETCNT_TAG             "N4" 
#define ECR_UNIONPAY_VOID_PURCHASETAMT_TAG             "G4"
#define ECR_UNIONPAY_REFUND_PURCHASETCNT_TAG           "N5" 
#define ECR_UNIONPAY_REFUND_PURCHASETAMT_TAG           "G5"
#define ECR_UNIONPAY_PREAUTH_PURCHASETCNT_TAG          "N6" 
#define ECR_UNIONPAY_PREAUTH_PURCHASETAMT_TAG          "G6"
#define ECR_UNIONPAY_VOIDPREAUTH_PURCHASETCNT_TAG      "N7" 
#define ECR_UNIONPAY_VOIDPREAUTH_PURCHASETAMT_TAG      "G7"
#define ECR_BCA_PURCHASETCNT_TAG                       "N8" 
#define ECR_BCA_PURCHASETAMT_TAG                       "G8"
#define ECR_BCA_VOIDPURCHASETCNT_TAG                   "N9"
#define ECR_BCA_VOIDPURCHASETAMT_TAG                   "G9"
#define ECR_BCA_PREAUTH_PURCHASETCNT_TAG               "NA"
#define ECR_BCA_PREAUTH_PURCHASETAMT_TAG               "GA"
#define ECR_NFP_TCNT_TAG                               "NB" 
#define ECR_NFP_TAMT_TAG                               "GB"
#define ECR_NCC_TCNT_TAG                               "NC" 
#define ECR_NCC_TAMT_TAG                               "GC"
#define ECR_NFP_TOPUP_NETSTCNT_TAG                     "ND" 
#define ECR_NFP_TOPUP_NETSTAMT_TAG                     "GD"
#define ECR_NCC_TOPUP_NETSTCNT_TAG                     "NE" 
#define ECR_NCC_TOPUP_NETSTAMT_TAG                     "GE"
#define ECR_TOTAL_CASHBACKTCNT_TAG                     "NF"
#define ECR_TOTAL_CASHBACKTAMT_TAG                     "GF"

/*NETS:ECT TAG SIZE -- sidumili*/
#define ECR_RESP_TEXT_SIZE		       40
#define ECR_MERCHANT_NAME_SIZE	       69
#define ECR_DATE_SIZE              	   6            /* Date in MMDDYY format        */
#define ECR_TIME_SIZE              	   6            /* Time in HHMMSS format        */
#define ECR_TID_SIZE                   8        /* Terminal Identification size */
#define ECR_MID_SIZE				   15
#define ECR_STAN_SIZE				   6
#define ECR_AUTH_CODE_SIZE             6        /* Authorization code size      */
#define ECR_RREF_SIZE                  12       /* Retrieval reference num size */
#define ECR_CARD_NAME_SIZE             10
#define ECR_TRANSACTION_AMT_SIZE       12
#define ECR_SERVICE_FEE_SIZE           12
#define ECR_POS_MSG_SIZE               240
#define ECR_RESP_MSG1_SIZE             20
#define ECR_RESP_MSG2_SIZE             20
#define ECR_LOYALTY_PRGNAME_SIZE       24
#define ECR_LOYALTY_PRGEXPDATE_SIZE     8
#define ECR_LOYALTY_TYPE_SIZE           1
#define ECR_LOYALTY_MKTG_MSG_SIZE      144
#define ECR_REDEMPTION_VALUE_SIZE      12
#define ECR_CUR_LOYALTY_BAL_SIZE       12
#define ECR_HOST_RESP_CODE_SIZE        2
#define ECR_CARD_ENTRY_MODE_SIZE       2
#define ECR_ENHANCED_RREF_SIZE         13
#define ECR_RECEIPT_TEXT_FMT_SIZE      10

// NFP -- sidumili
#define ECR_BATCHNUM_SIZE              12
#define ECR_TOTALFEETU_SIZE            6
#define ECR_FEEDUETU_SIZE              6
#define ECR_CARDNUMBER_SIZE            16
#define ECR_EXPIRYDATE_SIZE            8
#define ECR_PURCHASEFEE_SIZE           6
#define ECR_CARDISSUERNAME_SIZE        10
#define ECR_ADDTRXNINFO_SIZE           19
#define ECR_CEPASTRANSDATA_SIZE        64
#define ECR_CEPASVERSION_SIZE          2
#define ECR_CEPASTRANSTYPE_SIZE        2
#define ECR_CEPASTRXNAMT_SIZE          12
#define ECR_CEPASTRXNDATE_SIZE         6
#define ECR_CEPASTRXNTIME_SIZE         6
#define ECR_CEPASPRIORCARDBAL_SIZE     12
#define ECR_CEPASPOSTCARDBAL_SIZE      12
#define ECR_CEPASPOSTAUTOLOADAMT_SIZE  12
#define ECR_CEPASPOSTCARDSTATUS_SIZE   2

// SETTLE -- sidumili

#define ECR_TCNT_ASC_SIZE        2
#define ECR_TAMT_ASC_SIZE        6

#define TCOUNT_ASC_SIZE          4
#define TCOUNT_BCD_SIZE          ((TCOUNT_ASC_SIZE+1)/2)

#define TAMOUNT_ASC_SIZE          12
#define TAMOUNT_BCD_SIZE         ((TAMOUNT_ASC_SIZE+1)/2)



#define END_PRESENT_SIZE		       1
#define TAG_SIZE				       2
#define LENGTH_SIZE				       2

// Define for NETS ECR Header
#define ECR_ECN_SIZE                   12
#define ECR_FUNCTION_CODE_SIZE			2
#define ECR_RESPONSE_CODE_SIZE          2
#define ECR_VERSION_CODE_SIZE           2
#define ECR_RFU_SIZE                    1

// Define for array
#define MAX_ARRAY_ROW                  10
#define MAX_ARRAY_COL                  42

// Define Attribute
#define ATR_ASC_FMT                     1  
#define ATR_BCD_FMT                     2 
#define ATR_HEX_FMT                     3


/*Define terminal total structure here*/

typedef struct tagNPXTerminalTotal
{
	unsigned long ulPurchaseCnt;
	unsigned long ulPurchaseAmt;
	unsigned long ulPurchaseFeeAmt;
	
	unsigned long ulRefundCnt;
	unsigned long ulRefundAmt;
	
	unsigned long ulAdjRefundCnt;
	unsigned long ulAdjRefundAmt;
	
	unsigned long ulVoidCnt;
	unsigned long ulVoidAmt;
	
	unsigned long ulAdjustCnt;
	unsigned long ulAdjustAmt;
	
	unsigned long ulCashBackCnt;
	unsigned long ulCashBackAmt;
	
	unsigned long ulPurCashBackCnt;
	unsigned long ulPurCashBackAmt;
	unsigned long ulCashBackFeeAmt;
	unsigned long ulPurCashBackFeeAmt;
	
	unsigned long ulCashDepositCnt;
	unsigned long ulCashDepositAmt;
	
	unsigned long ulEDDACnt;

	unsigned long ulPreCompCnt;
	unsigned long ulPreCompAmt;
	
	unsigned long ulVoidPreCompCnt;
	unsigned long ulVoidPreCompAmt;

	unsigned long ulAwardCnt;
	unsigned long ulAwardAmt;
	
	unsigned long ulRedeemCnt;
	unsigned long ulRedeemAmt;
	
}NPX_TERM_TOTAL;

NPX_TERM_TOTAL gstECRTermTotal;


typedef struct
{
    BYTE stFieldCode[FIELD_CODE_SIZE + 1];
    BYTE stFieldValue[ECR_DISP_MSG_SIZE + 1];
	
}FIELDTABLE;

#define ECRMAXFUNCTIONS 		2048
#define ECRMAXFIELDSIZE         2048

typedef int (*DFUNCTION_LIST_ECR)(void); // sidumili

typedef struct tagECRFunc_vdFunc
{
	unsigned char stFieldCode[FIELD_CODE_SIZE + 1];
	int inFieldSize;
	int inAttribute;
	unsigned char uszFieldDesc[100];
	unsigned char uszFunctionName[100];
	DFUNCTION_LIST_ECR d_ECRFunctionP;
} Func_vdECRFunc;

typedef struct tagNETSECRData
{
	BYTE szECRRespText[ECR_RESP_TEXT_SIZE + 1];
	BYTE szECRMerchantName[ECR_MERCHANT_NAME_SIZE + 1];
	BYTE szECRDate[ECR_DATE_SIZE + 1];
	BYTE szECRTime[ECR_TIME_SIZE + 1];
	BYTE szECRTID[ECR_TID_SIZE + 1];
	BYTE szECRMID[ECR_MID_SIZE + 1];
	BYTE szECRStan[ECR_STAN_SIZE + 1];
	BYTE szECRAuthCode[ECR_AUTH_CODE_SIZE + 1];
	BYTE szECRRRef[ECR_RREF_SIZE + 1];
	BYTE szECRCardName[ECR_CARD_NAME_SIZE + 1];
	BYTE szECRTranAmt[ECR_TRANSACTION_AMT_SIZE + 1];
	BYTE szECRServiceFee[ECR_SERVICE_FEE_SIZE + 1];
	BYTE szECRPOSMsg[ECR_POS_MSG_SIZE + 1];
	BYTE szECRRespMsg1[ECR_RESP_MSG1_SIZE + 1];
	BYTE szECRRespMsg2[ECR_RESP_MSG2_SIZE + 1];
	BYTE szECRLoyaltyPrgName[ECR_LOYALTY_PRGNAME_SIZE + 1];
	BYTE szECRLoyaltyPrgExpDate[ECR_LOYALTY_PRGEXPDATE_SIZE + 1];
	BYTE szECRLoyaltyType[ECR_LOYALTY_TYPE_SIZE + 1];
	BYTE szECRLoyaltyMktgMsg[ECR_LOYALTY_MKTG_MSG_SIZE + 1];
	BYTE szECRRedemptionValue[ECR_REDEMPTION_VALUE_SIZE + 1];
	BYTE szECRCurLoyaltyBal[ECR_CUR_LOYALTY_BAL_SIZE + 1];
	BYTE szECRHostRespCode[ECR_HOST_RESP_CODE_SIZE + 1];
	BYTE szECRCardEntryMode[ECR_CARD_ENTRY_MODE_SIZE + 1];
	BYTE szECREnhancedRRef[ECR_ENHANCED_RREF_SIZE + 1];
	BYTE szECRReceiptTextFmt[ECR_RECEIPT_TEXT_FMT_SIZE + 1];

	// NFP -- sidumili
	BYTE szECRBatchNum[ECR_BATCHNUM_SIZE + 1];
	BYTE szECRNFPStan[ECR_STAN_SIZE + 1];
	BYTE szECRTotalFeeTU[ECR_TOTALFEETU_SIZE + 1];
	BYTE szECRFeeDueToMerchTU[ECR_FEEDUETU_SIZE + 1];
	BYTE szECRFeeDueFromMerchTU[ECR_FEEDUETU_SIZE + 1];
	BYTE szECRCardNumber[ECR_CARDNUMBER_SIZE + 1];
	BYTE szECRExpiryDate[ECR_EXPIRYDATE_SIZE + 1];
	BYTE szECRPurchaseFee[ECR_PURCHASEFEE_SIZE + 1];
	BYTE szECRCardIssuerName[ECR_CARDISSUERNAME_SIZE + 1];
	BYTE szECRAddTrxnInfo[ECR_ADDTRXNINFO_SIZE + 1];
	BYTE szECRCEPASVersion[ECR_CEPASVERSION_SIZE + 1];
	BYTE szECRCEPASTransData[ECR_CEPASTRANSDATA_SIZE + 1];
	BYTE szECRCEPASTransType[ECR_CEPASTRANSTYPE_SIZE + 1];
	BYTE szECRCEPASTrxnAmt[ECR_CEPASTRXNAMT_SIZE + 1];
	BYTE szECRCEPASTrxnDate[ECR_CEPASTRXNDATE_SIZE + 1];
	BYTE szECRCEPASTrxnTime[ECR_CEPASTRXNTIME_SIZE + 1];
	BYTE szECRCEPASPriorCardBal[ECR_CEPASPRIORCARDBAL_SIZE + 1];
	BYTE szECRCEPASPostCardBal[ECR_CEPASPOSTCARDBAL_SIZE + 1];
	BYTE szECRCEPASPostAutoLoadAmt[ECR_CEPASPOSTAUTOLOADAMT_SIZE + 1];
	BYTE szECRCEPASPostCardStatus[ECR_CEPASPOSTCARDSTATUS_SIZE + 1];

	// SETTLE
	BYTE szECRTNetsPurchaseTCnt[TCOUNT_BCD_SIZE + 1];
	BYTE szECRTNetsPurchaseTAmt[TAMOUNT_BCD_SIZE + 1];
	BYTE szECRTNetsVoidPurchaseTCnt[TCOUNT_BCD_SIZE + 1];
	BYTE szECRTNetsVoidPurchaseTAmt[TAMOUNT_BCD_SIZE + 1];
	BYTE szECRUnionPay_PurchaseTCnt[TCOUNT_BCD_SIZE + 1];
	BYTE szECRUnionPay_PurchaseTAmt[TAMOUNT_BCD_SIZE + 1];
	BYTE szECRUnionPay_VoidPurchaseTCnt[TCOUNT_BCD_SIZE + 1];
	BYTE szECRUnionPay_VoidPurchaseTAmt[TAMOUNT_BCD_SIZE + 1];
	BYTE szECRUnionPay_RefundPurchaseTCnt[TCOUNT_BCD_SIZE + 1];
	BYTE szECRUnionPay_RefundPurchaseTAmt[TAMOUNT_BCD_SIZE + 1];
	BYTE szECRUnionPay_PreAuthPurchaseTCnt[TCOUNT_BCD_SIZE + 1];
	BYTE szECRUnionPay_PreAuthPurchaseTAmt[TAMOUNT_BCD_SIZE + 1];
	BYTE szECRUnionPay_VoidPreAuthPurchaseTCnt[TCOUNT_BCD_SIZE + 1];
	BYTE szECRUnionPay_VoidPreAuthPurchaseTAmt[TAMOUNT_BCD_SIZE + 1];
	BYTE szECRBCA_PurchaseTCnt[TCOUNT_BCD_SIZE + 1];
	BYTE szECRBCA_PurchaseTAmt[TAMOUNT_BCD_SIZE + 1];
	BYTE szECRBCA_VoidPurchaseTCnt[TCOUNT_BCD_SIZE + 1];
	BYTE szECRBCA_VoidPurchaseTAmt[TAMOUNT_BCD_SIZE + 1];
	BYTE szECRBCA_PreAuthPurchaseTCnt[TCOUNT_BCD_SIZE + 1];
	BYTE szECRBCA_PreAuthPurchaseTAmt[TAMOUNT_BCD_SIZE + 1];
	BYTE szECRNFP_TCnt[TCOUNT_BCD_SIZE + 1];
	BYTE szECRNFP_TAmt[TAMOUNT_BCD_SIZE + 1];
	BYTE szECRNCC_TCnt[TCOUNT_BCD_SIZE + 1];
	BYTE szECRNCC_TAmt[TAMOUNT_BCD_SIZE + 1];
	BYTE szECRNFP_TopupNetsTCnt[TCOUNT_BCD_SIZE + 1];
	BYTE szECRNFP_TopupNetsTAmt[TAMOUNT_BCD_SIZE + 1];
	BYTE szECRNCC_TopupNetsTCnt[TCOUNT_BCD_SIZE + 1];
	BYTE szECRNCC_TopupNetsTAmt[TAMOUNT_BCD_SIZE + 1];
	BYTE szECRTotal_CashbackTCnt[TCOUNT_BCD_SIZE + 1];
	BYTE szECRTotal_CashbackTAmt[TAMOUNT_BCD_SIZE + 1];
	
}NETS_ECR_DATA;

NETS_ECR_DATA	stNetECRRec;

typedef struct tagNetECRHeaderData
{
	BYTE szECRECN[ECR_ECN_SIZE + 1];
	BYTE szECRFunctionCode[ECR_FUNCTION_CODE_SIZE + 1];
	BYTE szECRVersionCode[ECR_VERSION_CODE_SIZE + 1];
	BYTE szECRRFU[ECR_RFU_SIZE + 1];
	BYTE szECRResponseCode[ECR_RESPONSE_CODE_SIZE + 1];

}NETS_ECR_HEADER_DATA;

NETS_ECR_HEADER_DATA   tagNetECRHeader;


/*Start ECR Enhance, 20171023*/
//#ifdef NETS_ECR_ENHANCE

/*ECR version define*/
#define ECR_VER_01			1
#define ECR_VER_02			2
#define ECR_VER_21			21
#define ECR_VER_40			40

/*ECR work mode define*/
#define ECR_RETAIL_MODE		1
#define ECR_KIOSK_MODE		2

/*ECR void number type*/
#define ECR_VOID_BY_INV_NUM		0
#define ECR_VOID_BY_STAN		1


#define ENV_ECR_BAUD_RATE		"#ECRBUADRATE"
#define ENV_ECR_VERSION			"#ECRVER"
#define ENV_ECR_WORK_MODE		"#ECRWORKMODE"
#define ENV_ECR_VOID_NUM_TYPE	"#ECRVDNUMTYPE"

int inGetECRVersion(void);
void vdSetECRVersion(int version);
int inGetECRWorkMode(void);
void vdSetECRWorkMode(int inMode);

#define ECR_CHK_ECN				"#CHKECN"
//#endif



/*start ECR V2.1*/
#define ECRV21_HEADER					"NETSECR"
#define ECRV21_HEADER_SIZE				7

#define ECRV21_HMAC_SIZE				8
#define ECRV21_VER_SIZE					2
#define ECRV21_ECN_SIZE					12
#define ECRV21_RESP_CODE_SIZE			2

/*APP ID define*/
#define APP_ID_SIZE						2

#define APPID_DEFAULT				"00"
#define APPID_GENERAL				"01"
#define APPID_CASHCARD				"02"
#define APPID_NETS					"03"
#define APPID_UPI					"04"
#define APPID_BCA					"05"
#define APPID_NFP					"06"
#define APPID_CEPAS					"07"
#define APPID_CREDIT				"08"
#define APPID_RSVP					"09"
#define APPID_MPP					"10"
#define APPID_FEVO					"11"

/*ECR V21 Trans Type define*/
#define ECRV21_TT01_GENERAL			1
#define ECRV21_TT02_CASHCARD		2
#define ECRV21_TT03_NETS			3
#define ECRV21_TT04_UPI				4
#define ECRV21_TT05_BCA				5
#define ECRV21_TT06_NFP				6
#define ECRV21_TT07_CEPAS			7
#define ECRV21_TT08_CC				8
#define ECRV21_TT09_RSVP			9
#define ECRV21_TT10_MPP				10
#define ECRV21_TT11_FEVO			11



/*Cmd Code for each transaction*/
#define CMDCODE_SIZE					2

#define CMDCODE_GET_TML_STATUS		"01"
#define CMDCODE_GET_LAST_TRANS		"02"
#define CMDCODE_LOGON				"03"
#define CMDCODE_LAST_SETTLE_RCPT	"04"
#define CMDCODE_TMS					"05"
#define CMDCODE_CONNECT_TEST		"06"
#define CMDCODE_GET_SOF_PRIORITY	"07"
#define CMDCODE_SET_SOF_PRIORITY	"08"
#define CMDCODE_GET_RND_NUM			"09"
#define CMDCODE_GET_SEC_KEY			"0A"
#define CMDCODE_PURCHASE			"10" 
#define CMDCODE_REVERSAL			"11"
#define CMDCODE_VOID				"12"
#define CMDCODE_IPP_SALE			"13"
#define CMDCODE_VOID_IPP_SALE		"14"
#define CMDCODE_REFUND				"15"
#define CMDCODE_PRE_SETTLEMENT		"16"
#define CMDCODE_SETTLEMENT			"17"


/*Transaction Type Indicator*/
#define TTI_SIZE						2

#define TTI_PURCHASE					"01"
#define TTI_PURCH_CASHBACK				"02"
#define TTI_CASHBACK					"03"
#define TTI_PURCHASE_QR					"04"



/*Field Code define*/
#define ECRV21_FC_SIZE					2

#define ECRV21_FC_PREV_TRANS_DATA		"AK"
#define ECRV21_FC_PREV_CMD_ID			"88"
#define ECRV21_FC_SOF_APPL_ID			"32"
#define ECRV21_FC_APP_PRIORITY			"35"
#define ECRV21_FC_RND_NUM				"36"
#define ECRV21_FC_SECRET_KEY			"37"
#define ECRV21_FC_CURRENT_BALANCE		"10"
#define ECRV21_FC_PREVIOUS_BALANCE		"11"
#define ECRV21_FC_ISSUER_CATEGORY		"12"
#define ECRV21_FC_MPP_FINAL_BALANCE		"14"
#define ECRV21_FC_MPP_EXP_DATE			"15"
#define ECRV21_FC_CARD_NUMBER			"21"
#define ECRV21_FC_CARD_NANME			"22"
#define ECRV21_FC_CARD_ISSUER_NANME		"23"
#define ECRV21_FC_CARDHOLDER_NANME		"24"
#define ECRV21_FC_CARD_TYPE				"25"
#define ECRV21_FC_CARD_DESC				"28"
#define ECRV21_FC_MPP_CARD_NUMBER		"29"
#define ECRV21_FC_MPP_CARD_PROG			"2A"
#define ECRV21_FC_CARD_EXP1				"2C"
#define ECRV21_FC_CARD_EXP2				"2D"
#define ECRV21_FC_CARD_BALANCE			"2E"
#define ECRV21_FC_ACQ_NAME				"30"
#define ECRV21_FC_CHECKSUM				"33"
#define ECRV21_FC_CERTIFICATE			"34"
#define ECRV21_FC_SERVICE_FEE			"40"
#define ECRV21_FC_FEE_DUE_TO_MERCH		"41"
#define ECRV21_FC_FEE_DUE_FROM_MERCH	"42"
#define ECRV21_FC_PURCHASE_FEE			"45"
#define ECRV21_FC_TOTAL_FEE				"46"
#define ECRV21_FC_AID					"50"
#define ECRV21_FC_APP_PROFILE			"51"
#define ECRV21_FC_CID					"52"
#define ECRV21_FC_TC					"53"
#define ECRV21_FC_TVR					"54"
#define ECRV21_FC_TSI					"55"
#define ECRV21_FC_LOYALTY_TYPE			"60"
#define ECRV21_FC_LOYALTY_INFO			"61"
#define ECRV21_FC_LOYALTY_NAME			"62"
#define ECRV21_FC_LOYALTY_EXP			"63"
#define ECRV21_FC_LOYALTY_MSG			"64"
#define ECRV21_FC_LOYALTY_REDEEM		"65"
#define ECRV21_FC_REDEEM_VALUE			"66"
#define ECRV21_FC_LOYALTY_BALANCE		"6H"
#define ECRV21_FC_MPP_REFER_NUM			"6J"
#define ECRV21_FC_POSID					"80"
#define ECRV21_FC_TRANS_TYPE_IND		"81" // Transaction Type Indicator
#define ECRV21_FC_PAYMENT_TYPE			"82"
#define ECRV21_FC_REFER_NUM				"83"
#define ECRV21_FC_TRANS_REFER_NUM		"85"
#define ECRV21_FC_CEPAS_TRANS_DATA		"86"
#define ECRV21_FC_SCHEME_CATEGORY		"89"
#define ECRV21_FC_CASHIER_ID			"8A"
#define ECRV21_FC_RCPT_RP				"8C"
#define ECRV21_FC_BLACKLIS_VER			"91"
#define ECRV21_FC_CEPAS_VER				"93"
#define ECRV21_FC_APPROVAL_CODE			"A0"
#define ECRV21_FC_TRANS_DATE			"A1"
#define ECRV21_FC_TRANS_TIME			"A2"
#define ECRV21_FC_TID					"A3"
#define ECRV21_FC_MID					"A4"
#define ECRV21_FC_FOREIGN_MID			"A5"
#define ECRV21_FC_TRANS_AMT				"A6"
#define ECRV21_FC_CASHBAK_AMT			"A7"
#define ECRV21_FC_FOREIGN_AMT			"A8"
#define ECRV21_FC_ORGINAL_STAN			"A9"
#define ECRV21_FC_CURRENT_STAN			"AA"
#define ECRV21_FC_BATCH_NUM				"AB"
#define ECRV21_FC_INVOICE_NUM			"AC"
#define ECRV21_FC_RRN					"AD"
#define ECRV21_FC_RESPOSE_CODE			"AE"
#define ECRV21_FC_CURRENCY_NAME			"AG"
#define ECRV21_FC_CARD_ENTRY_MODE		"AH"
#define ECRV21_FC_SIGNATURE_REQ_IND		"AJ"
#define ECRV21_FC_PROC_GATEWAY			"AL"
#define ECRV21_FC_TRANSACTION_ID		"AM"
#define ECRV21_FC_RESP_MSG_01			"B2"
#define ECRV21_FC_RESP_MSG_02			"B3"
#define ECRV21_FC_POS_MSG				"B4"
#define ECRV21_FC_MPP_HOST_TEXT			"B5"
#define ECRV21_FC_RESP_TEXT				"B6"
#define ECRV21_FC_RCPT_TEXT_FORMAT		"B7"
#define ECRV21_FC_MERCH_NAME_ADDR		"C1"
#define ECRV21_FC_MERCH_ADDR01			"C2"
#define ECRV21_FC_MERCH_ADDR02			"C3"
#define ECRV21_FC_MERCH_ADDR03			"C4"
#define ECRV21_FC_MERCH_ADDR04			"C5"
#define ECRV21_FC_POSTAL_CODE			"C6"
#define ECRV21_FC_RFU04					"F1"
#define ECRV21_FC_RFU06					"F2"
#define ECRV21_FC_RFU08					"F3"
#define ECRV21_FC_TERMINAL_CERT			"G1"
#define ECRV21_FC_ADD_TRANS_INFO		"AN"
#define ECRV21_FC_NUM_CC_HOST			"7E"
#define ECRV21_FC_HOST_ID				"31"
#define ECRV21_FC_DATA_TYPE				"87"
#define ECRV21_FC_TOT_SALE_CNT			"70"
#define ECRV21_FC_TOT_SALE_AMT			"77"
#define ECRV21_FC_TOT_VOID_CNT			"71"
#define ECRV21_FC_TOT_VOID_AMT			"78"
#define ECRV21_FC_TOT_RFUD_CNT			"72"
#define ECRV21_FC_TOT_RFUD_AMT			"79"
#define ECRV21_FC_TOT_PAC_CNT			"73"
#define ECRV21_FC_TOT_PAC_AMT			"7A"
#define ECRV21_FC_TOT_VPAC_CNT			"74"
#define ECRV21_FC_TOT_VPAC_AMT			"7B"
#define ECRV21_FC_TOT_TOPUP_CNT			"75"
#define ECRV21_FC_TOT_TOPUP_AMT			"7C"
#define ECRV21_FC_TOT_CASHBACK_CNT		"76"
#define ECRV21_FC_TOT_CASHBACK_AMT		"7D"
#define ECRV21_FC_NUM_IPP_HOST			"7F"
#define ECRV21_FC_NUM_DCC_HOST			"7G"

#define ECRV21_FC_LAST_ECN				"H4"

/*Size define*/
#define FC_PREV_CMD_ID_SIZE			2
#define FC_SOF_APPL_ID_SIZE			2
#define FC_APP_PRIORITY_SIZE		2
#define FC_RND_NUM_SIZE				16
#define FC_SECRET_KEY_SIZE			64
#define FC_CURRENT_BALANCE_SIZE		12
#define FC_PREVIOUS_BALANCE_SIZE	12
#define FC_ISSUER_CATEGORY_SIZE		16
#define FC_MPP_FINAL_BALANCE_SIZE	12
#define FC_MPP_EXP_DATE_SIZE		8
#define FC_CARD_NUMBER_SIZE			16
#define FC_CARD_NANME_SIZE			10
#define FC_CARD_ISSUER_NANME_SIZE	10
#define FC_CARDHOLDER_NANME_SIZE	26
#define FC_CARD_TYPE_SIZE			20
#define FC_CARD_DESC_SIZE			20
#define FC_MPP_CARD_NUMBER_SIZE		20
#define FC_MPP_CARD_PROG_SIZE		30
#define FC_CARD_EXP1_SIZE			4
#define FC_CARD_EXP2_SIZE			8
#define FC_CARD_BALANCE_SIZE		12
#define FC_ACQ_NAME_SIZE			20
#define FC_CHECKSUM_SIZE			2
#define FC_CERTIFICATE_SIZE			4
#define FC_SERVICE_FEE_SIZE			12
#define FC_FEE_DUE_TO_MERCH_SIZE	6
#define FC_FEE_DUE_FROM_MERCH_SIZE	6
#define FC_PURCHASE_FEE_SIZE		6
#define FC_TOTAL_FEE_SIZE			6
#define FC_AID_SIZE					32
#define FC_APP_PROFILE_SIZE			16
#define FC_CID_SIZE					2
#define FC_TC_SIZE					16
#define FC_TVR_SIZE					10
#define FC_TSI_SIZE					4
#define FC_LOYALTY_TYPE_SIZE		1
#define FC_LOYALTY_INFO_SIZE		64
#define FC_LOYALTY_NAME_SIZE		24
#define FC_LOYALTY_EXP_SIZE			8
#define FC_LOYALTY_MSG_SIZE			144
#define FC_LOYALTY_REDEEM_SIZE		1
#define FC_REDEEM_VALUE_SIZE		12
#define FC_LOYALTY_BALANCE_SIZE		12
#define FC_MPP_REFER_NUM_SIZE		16
#define FC_POSID_SIZE				8
#define FC_TRANS_TYPE_IND_SIZE		2 // Transaction Type Indicator
#define FC_PAYMENT_TYPE_SIZE		1
#define FC_REFER_NUM_SIZE			16
#define FC_TRANS_REFER_NUM_SIZE		14
#define FC_CEPAS_TRANS_DATA_SIZE	64
#define FC_SCHEME_CATEGORY_SIZE		20
#define FC_CASHIER_ID_SIZE			12
#define FC_RCPT_RP_SIZE				1
#define FC_BLACKLIS_VER_SIZE		6
#define FC_CEPAS_VER_SIZE			2
#define FC_APPROVAL_CODE_SIZE		6
#define FC_TRANS_DATE_SIZE			6
#define FC_TRANS_TIME_SIZE			6
#define FC_TID_SIZE					8
#define FC_MID_SIZE					15
#define FC_FOREIGN_MID_SIZE			15
#define FC_TRANS_AMT_SIZE			12
#define FC_CASHBAK_AMT_SIZE			6
#define FC_FOREIGN_AMT_SIZE			12
#define FC_ORGINAL_STAN_SIZE		6
#define FC_CURRENT_STAN_SIZE		6
#define FC_BATCH_NUM_SIZE			12
#define FC_INVOICE_NUM_SIZE			6
#define FC_RRN_SIZE					12
#define FC_RESPOSE_CODE_SIZE		2
#define FC_CURRENCY_NAME_SIZE		3
#define FC_CARD_ENTRY_MODE_SIZE		2
#define FC_SIGNATURE_REQ_IND_SIZE	1
#define FC_PROC_GATEWAY_SIZE		20
#define FC_TRANSACTION_ID_SIZE		10
#define FC_RESP_MSG_01_SIZE			20
#define FC_RESP_MSG_02_SIZE			20
#define FC_POS_MSG_SIZE				240
#define FC_MPP_HOST_TEXT_SIZE		999
#define FC_RESP_TEXT_SIZE			40
#define FC_RCPT_TEXT_FORMAT_SIZE	999
#define FC_MERCH_NAME_ADDR_SIZE		69
#define FC_MERCH_ADDR01_SIZE		40
#define FC_MERCH_ADDR02_SIZE		40
#define FC_MERCH_ADDR03_SIZE		40
#define FC_MERCH_ADDR04_SIZE		40
#define FC_POSTAL_CODE_SIZE			40
#define FC_RFU04_SIZE				4
#define FC_RFU06_SIZE				6
#define FC_RFU08_SIZE				8
#define FC_TERMINAL_CERT_SIZE		6
#define FC_ADD_TRANS_INFO_SIZE		19
#define FC_NUM_CC_HOST_SIZE			2
#define FC_HOST_ID_SIZE				2
#define FC_DATA_TYPE_SIZE			99
#define FC_TOT_SALE_CNT_SIZE		2
#define FC_TOT_SALE_AMT_SIZE		6
#define FC_TOT_VOID_CNT_SIZE		2
#define FC_TOT_VOID_AMT_SIZE		6
#define FC_TOT_RFUD_CNT_SIZE		2
#define FC_TOT_RFUD_AMT_SIZE		6
#define FC_TOT_PAC_CNT_SIZE			2
#define FC_TOT_PAC_AMT_SIZE			6
#define FC_TOT_VPAC_CNT_SIZE		2
#define FC_TOT_VPAC_AMT_SIZE		6
#define FC_TOT_TOPUP_CNT_SIZE		2
#define FC_TOT_TOPUP_AMT_SIZE		6
#define FC_TOT_CASHBACK_CNT_SIZE	2
#define FC_TOT_CASHBACK_AMT_SIZE	6
#define FC_NUM_IPP_HOST_SIZE		2
#define FC_NUM_DCC_HOST_SIZE		2

#define FC_LAST_ECN_SIZE			12


/*Loyalty Info*/
#define CC_LOY_AWARDER_SIZE			12
#define CC_LOY_REDEEMED_SIZE		12
#define CC_LOY_BALANCE_SIZE			12
#define CC_LOY_CARD_NUM_SIZE		16
#define CC_LOY_MESSAGE_SIZE			50
#define CC_LOY_TXN_AMT_SIZE			12
#define CC_LOY_DISC_AMT_SIZE		12
#define CC_LOY_BASE_AMT_SIZE		12
#define CC_LOY_NETT_AMT_SIZE		12
#define CC_LOY_REDEEM_AMT1_SIZE		12
#define CC_LOY_NUM_POOLS_SIZE		1
#define CC_LOY_CLUB_ID_SIZE			3
#define CC_LOY_POOL_ID_SIZE			12
#define CC_LOY_REDEEM_AMT2_SIZE		12
#define CC_LOY_REDEEM_VALUE_SIZE	1
#define CC_LOY_AWARD_AMT_SIZE		1
#define CC_LOY_POINT_BAL_SIZE		1
#define CC_LOY_BAL_AMT_SIZE			12
#define CC_LOY_POOL_DESC_SIZE		30
#define CC_LOY_IRR_CLUB_ID_SIZE		3
#define CC_LOY_IRR_QUANTITY_SIZE	2
#define CC_LOY_IRR_DESC_SIZE		40
#define CC_LOY_IRR_REDEEM_AMT_SIZE	12


#define FC_VAR_SIZE					0
#define FC_FIX_SIZE					1

#define FC_VAR_LEN_SIZE				2

typedef struct tagECRV21_FieldCodeMap
{
	unsigned char szFieldCode[2 + 1];
	int inFieldSize;
	int inFixedSize;
	int inAttribute;
	unsigned char *pFiledValue;
}ECRV21_FILEDCODE_MAP;



typedef struct tagNETSECRV21ReqData
{
	BYTE uszECRVer[ECRV21_VER_SIZE+1];
	BYTE uszCmdCode[CMDCODE_SIZE+1];
	BYTE uszECN[ECRV21_ECN_SIZE+1];
	BYTE uszAppID[APP_ID_SIZE+1];
	BYTE uszRcptRP[FC_RCPT_RP_SIZE+1];
	BYTE uszReferNum[FC_REFER_NUM_SIZE+1];
	BYTE uszSOFApplID[FC_SOF_APPL_ID_SIZE+1];
	BYTE uszAppPriority[FC_APP_PRIORITY_SIZE+1];
	BYTE uszHostID[FC_HOST_ID_SIZE+1];
	BYTE uszTransTypeInd[FC_TRANS_TYPE_IND_SIZE+1]; //TransactionTypeIndicator
	BYTE uszCashierID[FC_CASHIER_ID_SIZE+1];
	BYTE uszTransAmt[FC_TRANS_AMT_SIZE+1];
	BYTE uszCashBackAmt[FC_CASHBAK_AMT_SIZE+1];
	BYTE uszTotCashBackAmt[FC_CASHBAK_AMT_SIZE+1];
	BYTE uszTransDate[FC_TRANS_DATE_SIZE+1];
	BYTE uszTransTime[FC_TRANS_TIME_SIZE+1];
	BYTE uszTID[FC_TID_SIZE+1];
	BYTE uszMID[FC_MID_SIZE+1];
	BYTE uszOrgSTAN[FC_ORGINAL_STAN_SIZE+1];
	BYTE uszAcqName[FC_ACQ_NAME_SIZE+1];
	BYTE uszSchemeCategory[FC_SCHEME_CATEGORY_SIZE+1];
	BYTE uszLoyaltyRedemption[FC_LOYALTY_REDEEM_SIZE+1];
	BYTE uszProcGateway[FC_PROC_GATEWAY_SIZE+1];
	BYTE uszTransID[FC_TRANSACTION_ID_SIZE+1];
	BYTE uszLastECN[FC_LAST_ECN_SIZE+1];
}NETS_ECRV21_REQ_DATA;


typedef struct tagNETSECRV21RespData
{
	
	BYTE szHeaderRespCode[ECRV21_RESP_CODE_SIZE + 1];
	BYTE szReferNum[FC_REFER_NUM_SIZE+1];
	BYTE szTransTypeInd[FC_TRANS_TYPE_IND_SIZE+1];
	BYTE szSOFApplID[FC_SOF_APPL_ID_SIZE+1];
	BYTE szAppPriority[FC_APP_PRIORITY_SIZE+1];
	BYTE szRndNum[FC_RND_NUM_SIZE+1];
	BYTE szSecKey[FC_SECRET_KEY_SIZE+1];
	
	BYTE szCashierID[FC_CASHIER_ID_SIZE+1];
	BYTE szTransAmt[FC_TRANS_AMT_SIZE+1];
	BYTE szCashBackAmt[FC_CASHBAK_AMT_SIZE+1];
	BYTE szTransDate[FC_TRANS_DATE_SIZE+1];
	BYTE szTransTime[FC_TRANS_TIME_SIZE+1];
	BYTE szTID[FC_TID_SIZE+1];
	BYTE szMID[FC_MID_SIZE+1];
	BYTE szCurrSTAN[FC_CURRENT_STAN_SIZE+1];
	BYTE szHostRespCode[FC_RESPOSE_CODE_SIZE+1];
	BYTE szProcGateWay[FC_PROC_GATEWAY_SIZE+1];
	BYTE szAcqName[FC_ACQ_NAME_SIZE+1];
	BYTE szCardHolderName[FC_CARDHOLDER_NANME_SIZE+1];
	BYTE szCardType[FC_CARD_TYPE_SIZE+1];
	BYTE szCardDesc[FC_CARD_DESC_SIZE+1];
	BYTE szTransReferNum[FC_TRANS_REFER_NUM_SIZE+1];
	BYTE szHostID[FC_HOST_ID_SIZE+1];
	BYTE szDataType[FC_DATA_TYPE_SIZE+1];

	BYTE szMPPExpDate[FC_MPP_EXP_DATE_SIZE+1];
	BYTE szMPPCardNumber[FC_MPP_CARD_NUMBER_SIZE+1];
	BYTE szMPPCardProg[FC_MPP_CARD_PROG_SIZE+1];
	BYTE szMPPHostText[FC_MPP_HOST_TEXT_SIZE+1];

	BYTE szCheckSum[FC_CHECKSUM_SIZE + 1];
	BYTE szSignCert[FC_CERTIFICATE_SIZE + 1];
	BYTE szBlackListVer[FC_BLACKLIS_VER_SIZE + 1];
	BYTE szRFU4[FC_RFU04_SIZE + 1];
	BYTE szRFU6[FC_RFU06_SIZE + 1];
	BYTE szRFU8[FC_RFU06_SIZE + 1];
	BYTE szTTC[FC_TERMINAL_CERT_SIZE + 1];
	
	BYTE szAID[FC_AID_SIZE + 1];
	BYTE szAppProfile[FC_APP_PROFILE_SIZE + 1];
	BYTE szCID[FC_CID_SIZE + 1];
	BYTE szTC[FC_TC_SIZE + 1];
	BYTE szTVR[FC_TVR_SIZE + 1];
	BYTE szTSI[FC_TSI_SIZE + 1];
	BYTE szPOSID[FC_POSID_SIZE + 1];
	BYTE szPaymentType[FC_PAYMENT_TYPE_SIZE + 1];	
	BYTE szSchemeCategory[FC_SCHEME_CATEGORY_SIZE + 1];
	BYTE szInvoiceNum[FC_INVOICE_NUM_SIZE + 1];
	BYTE szSignReqInd[FC_SIGNATURE_REQ_IND_SIZE + 1];
	BYTE szTransactionID[FC_TRANSACTION_ID_SIZE + 1];

	BYTE szForeignMID[FC_FOREIGN_MID_SIZE + 1];
	BYTE szForeignAmt[FC_FOREIGN_AMT_SIZE + 1];

	BYTE szCC_SaleCnt[TCOUNT_BCD_SIZE + 1];
	BYTE szCC_SaleAmt[TAMOUNT_BCD_SIZE + 1];
	BYTE szCC_VoidSaleCnt[TCOUNT_BCD_SIZE + 1];
	BYTE szCC_VoidSaleAmt[TAMOUNT_BCD_SIZE + 1];
	BYTE szCC_RefundCnt[TCOUNT_BCD_SIZE + 1];
	BYTE szCC_RefundAmt[TAMOUNT_BCD_SIZE + 1];
	BYTE szCC_PACCnt[TCOUNT_BCD_SIZE + 1];
	BYTE szCC_PACAmt[TAMOUNT_BCD_SIZE + 1];
	BYTE szCC_VPACCnt[TCOUNT_BCD_SIZE + 1];
	BYTE szCC_VPACAmt[TAMOUNT_BCD_SIZE + 1];

	BYTE szIPP_SaleCnt[TCOUNT_BCD_SIZE + 1];
	BYTE szIPP_SaleAmt[TAMOUNT_BCD_SIZE + 1];

	BYTE szDCC_SaleCnt[TCOUNT_BCD_SIZE + 1];
	BYTE szDCC_SaleAmt[TAMOUNT_BCD_SIZE + 1];

}NETS_ECRV21_RESP_DATA;

NETS_ECRV21_RESP_DATA	stNetECRV21Rec;



/*ECR V21 Error Code - Header Response Code*/
#define HRC_APPROVED			"00"
#define HRC_TXN_TO				"F0"
#define HRC_TXN_TO_REV			"F1"
#define HRC_TXN_VOIDED			"F2"
#define HRC_TXN_NOT_PROC		"F3"
#define HRC_REFER_HOST_RESP		"FE"
#define HRC_TXN_DECLINED		"FF"

#define HRC_REFER_TO_NETS		"10"
#define HRC_REFER_TO_BANK		"11"
#define HRC_PLS_TRY_AGAIN		"12"
#define HRC_REFER_HOST_ERR		"13"
#define HRC_LOGON_REQ			"14"

#define HRC_INVALID_AMT			"20"
#define HRC_AMT_ZERO			"21"
#define HRC_DAILY_LIMIT			"22"
#define HRC_AMT_EXCEEDED_MAX	"23"
#define HRC_AMT_BELOW_MIN		"24"
#define HRC_INVALID_HOST_AMT	"25"
#define HRC_RFUD_AMT_THAN100	"26"
#define HRC_TOPUP_AMT_EXCEEDED	"27"
#define HRC_RETAILER_LIMIT		"28"

#define HRC_APP_NOT_AVAIL		"30"
#define HRC_INVALID_TXN			"31"
#define HRC_TXN_NOT_ALLOW		"32"
#define HRC_TXN_NOT_SUPPORT		"33"
#define HRC_REVER_NOT_VIABLE	"34"
#define HRC_VOID_NOT_VIABLE		"35"
#define HRC_MAC_ERROR			"36"
#define HRC_INVALID_HOST_MSG	"37"
#define HRC_UNAUTH_RESP			"38"
#define HRC_TXN_BATCH_FULL		"39"
#define HRC_INVALID_STAN		"3A"
#define HRC_MISS_FIELD			"3B"
#define HRC_DUP_ECN				"3C"
#define HRC_ECN_TXN_NOT_FOUND	"3D"
#define HRC_MUST_SETTLE			"3E"

#define HRC_CANNOT_READ_CARD	"40"
#define HRC_INVALID_CARD		"41"
#define HRC_INVALID_SUPP_CARD	"42"
#define HRC_LOST_CARD			"43"
#define HRC_EXPIRED_CARD		"44"
#define HRC_LOCKED_CARD			"45"
#define HRC_CARD_NOT_ACTIVATED	"46"
#define HRC_REFUNDED_CARD		"47"
#define HRC_CARD_DISABLED		"48"
#define HRC_CARD_BLACKLISTED	"49"
#define HRC_CARD_BLOCKED		"4A"
#define HRC_INVALID_TOPUP_CARD	"4B"

#define HRC_TML_NOT_ACTIVE		"50"
#define HRC_TML_NOT_READY		"51"
#define HRC_INVALID_TML			"52"
#define HRC_READER_NOT_CONNECT	"53"
#define HRC_READER_NOT_READY	"54"
#define HRC_SWITCH_TO_CONTACT	"55"
#define HRC_USER_CANCEL			"56"
#define HRC_KEK_NOT_AVAIL		"57"

#define HRC_INVALID_DDA			"60"
#define HRC_INVALID_ACCOUNT		"61"
#define HRC_DUPLICATE_DDA		"62"
#define HRC_INCORRECT_PIN		"63"
#define HRC_PIN_TRIES_EXCEEDED	"64"
#define HRC_REQ_AC_DECLINED		"65"
#define HRC_ATC_DECLINED		"66"
#define HRC_CVR_DECLINED		"67"
#define HRC_TVR_DECLINED		"68"
#define HRC_FALLBACK_DECLINED	"69"
#define HRC_PIN_REQUIRED		"6A"
#define HRC_SUK_EXPIRED			"6B"
#define HRC_INSUFF_FUND			"6C"
#define HRC_INCORRECT_BAL		"6D"
#define HRC_PROBLEM_BAL			"6E"
#define HRC_ATU_DISABLED		"6F"
#define HRC_INVALID_DATA_LEN	"70"
#define HRC_CONNECT_ISSUE		"71"
#define HRC_HOST_RESP_TO		"72"
#define HRC_CASHCARD_FAILED		"73"
#define HRC_DIFF_ISSUERS		"74"
#define HRC_CERTIFICATE_ERR		"75"
#define HRC_RESEND_BATCH		"76"
#define HRC_BLOCKLIST_DL_REQ	"77"
#define HRC_CASHCARD_SETTLE_REQ	"78"
#define HRC_TXN_BLOCKED			"79"
#define HRC_RECORD_NOT_FOUND	"7A"



static int inGetECRStrFuncCode(int inFuncCode, char *pstFuncCode);

int inECRReceiveAnalyse(void);
int inParseECRHeader(int inLength,unsigned char *pchHedBuff,int *pinFunCode,char *pchRespCode);
int inParseECRAmt(int inLength,unsigned char *pchHedBuff, int inFunCode);

int inGetECRFieldValueByTag(unsigned char *szTag, int inTagSize, unsigned char *uszFieldData);
int inGetECRFieldSizeByTag(unsigned char *szTag); // retrun tag size from structure
char* szGetECRFieldDescriptionByTag(unsigned char *szTag); // return tag description from structure
int inGetECRFieldAttributeByTag(unsigned char *szTag); // return field attribute

// Function to get the value from srTranRec -- sidumili
int inECRGetResponseText(void);
int inECRGetMerchantName(void);
int inECRGetDate(void);
int inECRGetTime(void);
int inECRGetTID(void);
int inECRGetMID(void);
int inECRGetSTAN(void);
int inECRGetAuthCode(void);
int inECRGetRRefNo(void);
int inECRGetCardName(void);
int inECRGetTransactionAmt(void);
int inECRGetServiceFee(void);
int inECRGetPOSMessage(void);
int inECRGetResponseMsg1(void);
int inECRGetResponseMsg2(void);
int inECRGetLoyaltyProgramName(void);
int inECRGetLoyaltyProgramExpDate(void);
int inECRGetLoyaltyType(void);
int inECRGetLoyaltyMktgMsg(void);
int inECRGetRedemptionValue(void);
int inECRGetCurLoyaltyBal(void);
int inECRGetHostResponseCode(void);
int inECRGetCardEntryMode(void);
int inECRGetEnhancedRREF(void);
int inECRGetReceiptTextFmt(void);

// NFP Function - Set/Get Value
int inECRGetBatchNum(void);
int inECRGetStan(void);
int inECRGetTotalFeeTU(void);
int inECRGetFeeDueToMerchTU(void);
int inECRGetFeeDueFromMerchTU(void);
int inECRGetCardNumber(void);
int inECRGetExpiryDate(void);
int inECRGetPurchaseFee(void);
int inECRGetCardIssuerName(void);
int inECRAddTrxnInfo(void);
int inECRCEPASVersion(void);
int inECRSetCEPASTransData(void);
int inECRGetCEPASTransData(void);
int inECRSetCEPASTrxnType(void);
int inECRSetCEPASTrxnAmt(void);
int inECRSetCEPASTrxnDate(void);
int inECRSetCEPASTrxnTime(void);
int inECRSetCEPASPriorCardBalance(void);
int inECRSetCEPASPostCardBalance(void);
int inECRSetCEPASPostAutoLoadAmt(void);
int inECRSetCEPASPostCardStatus(void);

// SETTLE Function
int inECRTGetNetsPurchaseTCnt(void);
int inECRTGetNetsPurchaseTAmt(void);
int inECRTGetNetsVoidPurchaseTCnt(void);
int inECRTGetNetsVoidPurchaseTAmt(void);
int inECRGetUnionPay_PurchaseTCnt(void);
int inECRGetUnionPay_PurchaseTAmt(void);
int inECRGetUnionPay_VoidPurchaseTCnt(void);
int inECRGetUnionPay_VoidPurchaseTAmt(void);
int inECRGetUnionPay_RefundPurchaseTCnt(void);
int inECRGetUnionPay_RefundPurchaseTAmt(void);
int inECRGetUnionPay_PreAuthPurchaseTCnt(void);
int inECRGetUnionPay_PreAuthPurchaseTAmt(void);
int inECRGetUnionPay_VoidPreAuthPurchaseTCnt(void);
int inECRGetUnionPay_VoidPreAuthPurchaseTAmt(void);
int inECRGetBCA_PurchaseTCnt(void);
int inECRGetBCA_PurchaseTAmt(void);
int inECRGetBCA_VoidPurchaseTCnt(void);
int inECRGetBCA_VoidPurchaseTAmt(void);
int inECRGetBCA_PreAuthPurchaseTCnt(void);
int inECRGetBCA_PreAuthPurchaseTAmt(void);
int inECRGetNFP_TCnt(void);
int inECRGetNFP_TAmt(void);
int inECRGetNCC_TCnt(void);
int inECRGetNCC_TAmt(void);
int inECRGetNFP_TopupNetsTCnt(void);
int inECRGetNFP_TopupNetsTAmt(void);
int inECRGetNCC_TopupNetsTCnt(void);
int inECRGetNCC_TopupNetsTAmt(void);
int inECRGetTotal_CashbackTCnt(void);
int inECRGetTotal_CashbackTAmt(void);

// Function for Analyze And Response ECR Data
int inNETSECRSendAnalyse(char* szTag, int inTagSize, char* szTagDesc, int inAttribute, char* szOutSendData);
int inNETSECRSendResponse(char* szTag, int inTagSize, char* szTagDesc, int inAttribute, char* szOutSendData);
void vdSetLength(int inLength, char *out);
void vdSetTotalCount(int inCount, char *out);
void vdSetTotalAmount(int inAmount, char *out);

void vdSetFunctionCode(char *szFunCode);
int inGetFunctionCode(char *szFunCode);

int inECRGenerateResponseMsgByFunctionCode(char* szFunctionCode);
int inECRGenerateResponseHeader(BYTE *szOutData);



int inECRReceiveAnalyse_ECR21(void);
BYTE ucECRV21_GetReceiptRP(void);


int inECRReceiveAnalyse_ECR40(void);
int inMultiAP_ECRSendSuccessResponse_ECR40(void);


#ifdef	__cplusplus
}
#endif

#endif	/* _ECR_H */

