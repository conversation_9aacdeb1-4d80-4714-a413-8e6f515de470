#ifndef _ECRDEFS_H_
#define _ECRDEFS_H_

#define FILLER_OFFSET          12
#define FUNC_CODE_SIZE          2
#define RESPONSE_CODE_SIZE      2
#define EOM_SIZE                1
#define SEPARATOR_SIZE          1
#define MESSAGE_HEADER_SIZE    18
#define LENGTH_SIZE             2
#define FIELD_CODE_SIZE         2
#define MAX_FIELDS_SIZE        35	// sn change from 25 to cater for Contacless Debit for NETS Loyalty Shop N Save	// [NETSPLUS]: SN: For Release 312.01 and 604.01

// #CREDITCARD_ECR from verifone		//porting @WielyRabin Nov 2011 
#define ECR_AMOUNT_MAX_SIZE    12
#define ECR_RRN_MAX_SIZE       12
#define ECR_REF_NUM_MAX_SIZE   10
// End of #CREDITCARD_ECR from verifone		//porting @WielyRabin Nov 2011 

/* ERROR TYPES */
#define INVALID_EOM            -100
#define INVALID_SEPARATOR      -101
#define INVALID_HEADER         -102
#define INVALID_DATA           -103
#define FIELD_NOT_FOUND        -104
#define INVALID_DATA_LEN       -105

/* Function Codes */
#define TOTAL_FIELDS                                 4
#define ECR_FUNC_NETS_PURCHASE                      20
#define ECR_FUNC_CC_PURCHASE                        25
#define ECR_FUNC_COMBI_PURCHASE                     26
#define ECR_FUNC_NETS_REFUND                        27
#define ECR_FUNC_CC_TOPUP                           40
#define ECR_FUNC_CONC_PURCHASE			41
// Andy_F1, Singpools Phase 2
#define ECR_FUNC_NETS_PURCHASE_FEE                  50
#define ECR_FUNC_CC_PURCHASE_FEE                    51
// End Andy_F1
#define ECR_FUNC_NETS_PURCHASE_13REF_NO 30 
#define ECR_FUNC_CASHBACK_35	                    35
#define ECR_FUNC_PUR_CASH_36                        36

//ju add for VOID 130306
#define ECR_FUNC_NETS_VOID_FROM_TERM_LOG	        13
#define ECR_FUNC_NETS_VOID_MANUAL_ENTRY		        15
//end ju void

//ju add for CUPS 030406
#define ECR_FUNC_NETS_CUPS_PURCHASE	                29
//end ju CUPS

//WAZER for CUPIC
#define ECR_FUNC_NETS_CUPIC_PURCHASE	                31

//ju add for TOPUP with Fee 070606
#define ECR_FUNC_CC_TOPUP_FEE		                45
//end ju TOPUP with Fee

//ju add for LOGON and SETTLEMENT 231106
#define ECR_FUNC_LOGON		                        80
#define ECR_FUNC_SETTLEMENT	                        81
#define ECR_FUNC_TAXI_SETTLEMENT	                        82
//end ju LOGON and SETTLEMENT

//ju add for Get Terminal Status 121206
#define ECR_GET_TERM_STATUS	                        55
//end ju Get Terminal Status

//ju add for ECR_GET_LAST_RESPONSE		//daniel add for ECR DLL NETS purchase integration of CUPS, Cashback, Fee 050207
#define ECR_GET_LAST_RESPONSE	                    56  //ju add for ECR_GET_LAST_RESPONSE
//end ju 

//daniel add for ECR DLL NETS purchase integration of CUPS, Cashback, Fee 050207	// #CREDITCARD_ECR from verifone		//porting @WielyRabin Nov 2011
#define ECR_FUNC_NETS_PURCHASE_FEE_CUPS_CASHBACK    28  //daniel add for ECR DLL NETS purchase integration of CUPS, Cashback, Fee 050207

// ju add for Cepas 2 Retail 030409
#define ECR_CL2_DEBIT	                            24
#define ECR_CL2_TOPUP	                            46
#define ECR_CL2_OFFLINE_CARD_ENQUIRY	            71
#define ECR_CL2_DEBIT_STATEMENT_FEE	                72
#define ECR_CL2_READ_TRANS_LOG	                    73
#define ECR_CL2_REFUND	                            84
#define ECR_CL2_CARD_ACTIVATION	                    85
#define ECR_CSM_CARD_ACTIVATION	                    86
#define ECR_CL2_EFTPOS_PURCHASE	                    37
#define ECR_GET_TERM_FINANCIAL_TRANS_STATUS	        90  // ju add for new command Terminal Financial Transaction Status
#define ECR_SWITCH_TO_NETS		                    58  //ju add for new switching commands
#define ECR_SWITCH_TO_PREPAID	                    59

//liu add for transcab phase 2 
#define ECR_TAXI_GET_TERM_FINANCIAL_TRANS_STATUS	 	 97
#define ECR_TAXI_LOGON		98

// *** NETS CUP ECR functions , @WielyRabin 25 July 2011 
#define ECR_NETSCUP_VOID_STAN 91 
#define ECR_NETSCUP_VOID_MANUAL 92  
#define ECR_NETSCUP_PREAUTH 93 
#define ECR_NETSCUP_PREAUTH_COMP 94
#define ECR_NETSCUP_PREAUTH_VOID 95 
#define ECR_NETSCUP_PREAUTH_COMP_VOID 96

// #CREDITCARD_ECR from verifone		//porting @WielyRabin Nov 2011
#define ECR_CREDIT_SALE                            100 // 'I0' is mapped to 100 for credit sale txn -- vxk  20110922
#define ECR_CREDIT_VOID                            101 // 'I1' is mapped to 100 for credit sale void txn -- vxk  20110922
#define ECR_CREDIT_IPPSALE                         102 // 'I2' is mapped to 100 for IPP sale txn -- vxk  20110922
#define ECR_CREDIT_IPPVOID                         103 // 'I3' is mapped to 100 for IPP sale void txn -- vxk  20110922
#define ECR_CREDIT_REFUND                          104 // 'I4' is mapped to 100 for credit refund txn -- vxk  20110922
#define ECR_CREDIT_SETTLEMENT                    105 // liu add for transcab phase 2 project  July 2012


#define ECR_CREDIT_SALE_BASE_START                 100 // Base value being currently used for all 'In' Credit card commands
// #CREDITCARD_ECR from verifone		//porting @WielyRabin Nov 2011


// @WielyRabin 28 July 2011 
#define ECR_NETS_CASHDEPOSIT 17

// [NETSPLUS]: SN: For Release 312.01 and 604.01. Added from 226.15 ecrcomm.h
//sn add for NETS Shop N Save Loaylty
#define ECR_FUNC_NETS_LOYALTY_PURCHASE	38
#define ECR_FUNC_NETS_LOYALTY_INQUIRY	70
#define ECR_CL2_EFTPOS_LOYALTY_PURCHASE	47
#define ECR_CL2_LOYALTY_DEBIT				34
// end [NETSPLUS]

// #BCA_RINTIS @April 2012 //#MERGERING314
#define ECR_BCA_PURCHASE 65
#define ECR_BCA_PREAUTH 66
#define ECR_BCA_PREAUTH_COMP 67
#define ECR_BCA_VOID_STAN 68
#define ECR_BCA_VOID_MANUAL 69
// End of - #BCA_RINTIS @April 2012  //#MERGERING314

/* Field Codes */
#define ECR_END_FIELD                       "\0\0"
#define ECR_TRANSAC_AMNT                    "40"
#define ECR_CASH_BACK_AMNT                  "42"
#define ECR_REF_NUM                         "H4"
#define ECR_REF_NUM_13ALNUM	"HD"	//#EFTPOSCHIP Jan 2013, 13 alphanumeric for merchant ref no 
#define ECR_TOPUP_OPTION                    "T1"
#define ECR_RFU02                           "02"
#define ECR_MRCHNT_NAME_ADDR                "D0"
#define ECR_TRANSAC_DATE                    "03"
#define ECR_TRANSAC_TIME                    "04"
#define ECR_APPRV_CODE                      "01"
#define ECR_TERM_REF_NUM                    "65"
#define ECR_TID                             "16"
#define ECR_TAXI_DID                 "10"		//liu add for transcab project Phase 2
#define ECR_TAXI_VID                 "11"		//liu add for transcab project Phase 2
#define ECR_MID                             "D1"
//liu add for transcab phase 2 start=================================
#define ECR_DBS_NUM_HOST		"9I"
#define ECR_HOST_DATA_1	"D7"
#define ECR_HOST_DATA_2	"D8"
#define ECR_HOST_DATA_3	"D9"
#define ECR_HOST_DATA_4	"DA"
//liu add for transcab phase 2 end======================================
#define ECR_CARD_ISSUE_NAME                 "D2"
#define ECR_CARD_NUM                        "30"
#define ECR_CARD_EXP_DATE                   "31"
#define ECR_BATCH_NUM                       "50"
#define ECR_RETRV_REF_NUM                   "D3"
#define ECR_CARD_ISSUE_ID                   "D4"
#define ECR_CAN                             "30"
#define ECR_RFU31                           "31"
#define ECR_TTC                             "H5"
#define ECR_STAN                            "H6"
#define ECR_CTC                             "H7"
#define ECR_BLACK_LIST_VERS                 "H8"
#define ECR_SIGN_CERT                       "H9"
#define ECR_CHECKSUM                        "HA"
#define ECR_RFUHB                           "HB"
#define ECR_CASH_CARD_BAL                   "HC"
// Andy_F1 Singpools Phase 2
#define ECR_SERVICE_FEE                     "41"
// End
//ju add for TOPUP with FEE 070606
#define ECR_FEE_TO_MERCHANT		            "46"
#define ECR_FEE_FROM_MERCHANT	            "47"
//end ju for TOPUP with FEE

//ju add for CUPS purchase 030406
#define ECR_FOREIGN_AMT	                    "FA"
#define ECR_FOREIGN_MID	                    "F0"
//end ju CUPS

// ju add for cepas 2 
#define ECR_TOPUP_TOTAL_FEE	                "ZA"
#define ECR_TOPUP_FEE_TO_MERCHANT	        "ZB"
#define ECR_TOPUP_FEE_FROM_MERCHANT	        "ZC"
#define ECR_EXPIRY_DATE		                "C2"
#define ECR_RESP_MSG_1		                "R0"
#define ECR_RESP_MSG_2		                "R1"
#define ECR_PURCHASE_FEE	                "ZP"
#define ECR_ADDITION_TXN_INFO		        "ZT"
#define ECR_CEPAS_VERSION	                "C1"
#define ECR_TRANS_DATA		                "C0"
#define ECR_PURSE_STATUS	                "S3"
#define ECR_ATU_STATUS		                "S2"
#define ECR_ATU_AMT			                "C8"
#define ECR_RECORD_INDEX	                "S4"
#define ECR_NUM_OF_REC		                "S5"
#define ECR_NUM_OF_REC_READ	                "S7"
#define ECR_TRANS_LOG		                "S8"
#define ECR_MAX_LOG_COUNT	                "S9"
#define ECR_MOBILE_NUM		                "MN"

/* @WielyRabin 25 July 2011 For CUP Txn */
#define ECR_CUP_AUTH_ID	"17"	 

// @WielyRabin 03 Aug 2011 for Cash Deposit Txn 
#define ECR_NETS_CASHDEPOSIT_FEE "49"
#define ECR_NETS_CASHDEPOSIT_NETT "48"


// #CREDITCARD_ECR from verifone		//porting @WielyRabin Nov 2011
// kenny add for credit card function codes for comparing
// vsk 20110922 - placeholder, not being used
#define ECR_CREDIT_SALE_BASE_CODE           'I'
#define ECR_CREDIT_SALE_CODE		        "I0"
#define ECR_CREDIT_VOID_CODE		        "I1"
#define ECR_CREDIT_IPP_SALE_CODE	        "I2"
#define ECR_CREDIT_IPP_VOID_CODE	        "I3"
#define ECR_CREDIT_REFUND_CODE	            "I4"
#define ECR_CREDIT_SETTLEMENT_CODE	            "I5"

// nhdung add for credit functions
#define ECR_CARD_HOLDER_NAME		        "D6"
#define ECR_EMV_AID					        "9A"
#define ECR_EMV_APPL_PROFILE		        "9B"
#define ECR_EMV_CRYPTOGRAM		            "9C"
#define ECR_EMV_TRANSAC_CERT		        "9D"
#define ECR_EMV_TVR					        "9E"
#define ECR_EMV_TSI					        "9F"
#define ECR_INVOICE_NUM			            "9H"
#define ECR_ACQUIRER_NAME			        "9G"
// #CREDITCARD_ECR from verifone		//porting @WielyRabin Nov 2011

// [NETSPLUS]: SN: For Release 312.01 and 604.01. Added from 226.15 ecrcomm.h
// sn add for NETS Shop N Save Loyalty
#define ECR_LYT_REDEEM_FLAG				"43"
#define ECR_CURRENCY_NAME					"F2"
#define ECR_ISSUER_CATEGORY				"F1"
#define ECR_LYT_PROG_NAME					"L1"
#define ECR_LYT_PROG_EXP_DATE				"L8"
#define ECR_LYT_TYPE						"L2"
#define ECR_LYT_MKT_MSG					"L6"
#define ECR_LYT_REDEEM_VAL					"L3"
#define ECR_LYT_CURRENT_BAL				"L4"
#define ECR_SIGNATURE_REQ					"D5"
#define ECR_POS_MSG						"L5"
#define ECR_CARD_NAME						"L7"
#define ECR_LYT_MKT_6LINES_MSG					"L9"

//end [NETSPLUS]
#define ECR_TXN_INDICATOR	"T2"


/* RESPONSE CODES */
#define APPROVED                            "00"
#define REFER_TO_NETS_01                    "01"
#define REFER_TO_BANK_02                    "02"
#define MERCH_ID_NOT_FOUND                  "03"
#define INVALID_TRAN_12                     "12"
#define INVALID_AMOUNT                      "13"
#define DDA_NOT_FOUND                       "14"
#define RE_ENTER_TRANS                      "19"
#define ADDR_NOT_FOUND                      "21"
#define FOREIGN_ADDR                        "22"
#define INVALID_ADDR                        "23"
#define INVALID_DDA_DATA                    "24"
#define DUPLICATE_DDA                       "25"
#define AMOUNT_ZERO                         "26"
#define AMNT_EQUAL_OLD_AMNT                 "27"
#define ACCNT_NOT_BELONGS                   "28"
#define DECLINED_ONE                        "30"
#define INVALID_CARD_31                     "31"
#define INVALID_CARD_41                     "41"
#define INVALID_CARD_42                     "42"
#define INVALID_CARD_43                     "43"
#define NAC_ERROR                           "47"
#define INSUFF_FUND                         "51"
#define EXPIRED_CRD                         "54"
#define RE_ENTER_PIN                        "55"
#define INVALID_TRAN_58                     "58"
#define LMT_EXCEEDS                         "61"
#define INVALID_TRAN_62                     "62"
#define REFER_TO_BANK_75                    "75"
#define OTHER_ERROR                         "76"
#define DUPLICATE_TRANS                     "77"
#define REFER_TO_NETS_78                    "78"
#define RE_ENTER_SUP_PIN                    "79"
#define INVALID_CARD_80                     "80"
#define INVALID_CARD_81                     "81"
#define REFER_TO_NETS_82                    "82"
#define INVALID_CARD_85                     "85"
#define DECLINED_86                         "86"
#define LIMIT_EXCEEDS                       "87"
#define NO_REFUND                           "88"
#define INVALID_TERM                        "89" 
#define NO_REPLY_FROM_BNK                   "91"
#define UNAUTH_RESP                         "IM"
#define INVALID_RESP                        "IR"
#define INVALID_TERM_IT                     "IT"
#define INCORRECT_AMNT                      "IA"
#define INVALID_CARD_IC                     "IC"
#define CONNECTION_PROB                     "HS"
#define UNAUTH_BY_HOST                      "RC"
#define TIMEOUT_RESP                        "TO"
#define CANCELLED_BY_USER                   "US"
#define TRAN_BATCH_FULL                     "BF"
#define CASHCARD_UNSUCC                     "SC"
#define LOYALTY_UNSUCC                      "NA"
#define REQUEST_SUCCC                       "OK"
//ju add new response for VOID 130306
#define INVALID_STAN		                "IS"
#define RECORD_NOT_FOUND	                "RN"
//end ju
// ju add new response for ECR logon and settlement 290307
#define MANUAL_LOGON_REQUIRED		        "LR"
//end ju

#define BLOCK_PURSE		                    "BP"
#define BLOCKLIST_DOWNLOAD_REQUIRED	        "DK"
#define SETTLEMENT_REQUIRED                 "DS"
#define READER_NOT_READY	                "RE"

#define ECR_PRINTER_ERROR	                "PE"

//yusak add new responses for "SC":
#define CASHCARD_UNSUCC_PIN_BLOCKED			"Z0"
#define CASHCARD_UNSUCC_BATCH_ERROR			"Z1"
#define CASHCARD_UNSUCC_TOPUP_NOT_ALLOWED	"Z3"
#define CASHCARD_UNSUCC_CARD_ERROR_6003		"Z4"
#define CASHCARD_UNSUCC_CARD_ERROR_6004		"Z5"
#define CASHCARD_UNSUCC_CARD_ERROR_6005		"Z6"
#define CASHCARD_UNSUCC_CARD_ERROR_9201		"Z7"
#define CASHCARD_UNSUCC_CARD_ERROR_9202		"Z8"
#define CASHCARD_UNSUCC_CARD_ERROR_9203		"Z9"
#define CASHCARD_UNSUCC_CARD_ERROR_9204		"ZA"
#define CASHCARD_UNSUCC_CARD_ERROR_9501		"ZB"
#define CASHCARD_UNSUCC_CARD_ERROR_9502		"ZC"
#define CASHCARD_UNSUCC_CARD_ERROR_9503		"ZD"
#define CASHCARD_UNSUCC_CARD_ERROR_9301		"ZE"
#define CASHCARD_UNSUCC_CARD_ERROR_9302		"ZF"
#define CASHCARD_UNSUCC_CARD_ERROR_9303		"ZG"
#define CASHCARD_UNSUCC_CARD_ERROR_9304		"ZH"
#define CASHCARD_UNSUCC_CARD_ERROR_9305		"ZI"
#define CASHCARD_UNSUCC_CARD_ERROR_9306		"ZJ"
#define CASHCARD_UNSUCC_CARD_ERROR_9308		"ZK"
#define CASHCARD_UNSUCC_CARD_ERROR_9401		"ZL"
#define CASHCARD_UNSUCC_CARD_ERROR_9402		"ZM"
#define CASHCARD_UNSUCC_CARD_ERROR_9601		"ZN"
#define CASHCARD_UNSUCC_CARD_ERROR_9701		"ZO"
#define CASHCARD_UNSUCC_CARD_ERROR_9702		"ZP"
#define CASHCARD_UNSUCC_CARD_ERROR_9703		"ZQ"
#define CASHCARD_UNSUCC_CARD_ERROR_9801		"ZR"
#define CASHCARD_UNSUCC_CARD_ERROR_9802		"ZS"
#define CASHCARD_UNSUCC_CARD_ERROR_9907		"ZT"
#define CASHCARD_UNSUCC_CARD_USG_LMT		"ZU"
#define CASHCARD_UNSUCC_INSUFF_FUND_ERROR	"ZV"
#define CASHCARD_UNSUCC_TOPUP_CARD_ERROR	"ZW"
#define CASHCARD_UNSUCC_SAM_ERR				"ZX"
#define CASHCARD_UNSUCC_NO_SAM_RESP			"ZY"
#define CASHCARD_UNSUCC_SAM_CRYPTO_ERR		"ZZ"
#define CASHCARD_UNSUCC_UPDATE_BATCH_ERR	"W0"
#define CASHCARD_UNSUCC_PFAILURE			"W1"


//WAZER #RSVPDev
//Function headers
#define ECR_RSVP_GET_GROUP_ID			74
#define ECR_RSVP_PARA_DLD				75
#define ECR_RSVP_TOPUP					76
#define ECR_RSVP_REDEEM					77
#define ECR_RSVP_BAL_ENQ				78

//Data fields
#define ECR_RSVP_ALL_GID					"70"
#define ECR_RSVP_GROUP_ID				"71"
#define ECR_RSVP_PROMO_DATA			"72"
#define ECR_RSVP_ITEM_CODE				"73"
#define ECR_RSVP_ITEM_COUNT				"74"
#define ECR_RSVP_DOLLAR_DENOM			"75"
#define ECR_RSVP_ITEM_COUNT_PREV		"76"
#define ECR_RSVP_DOLLAR_DENOM_PREV	"77"
#define ECR_RSVP_ITEM_COUNT_FINAL		"78"
#define ECR_RSVP_DOLLAR_DENOM_FINAL	"79"
#define ECR_RSVP_CARD_DATA				"7A"
#define ECR_CSN_DATA						"29"

#define ECR_EFTPOS_SETTLE_DATA "DB"
#define ECR_C1_SETTLE_DATA "DC"
#define ECR_C2_SETTLE_DATA "DD"
	
#define COMBI_BLOCK_UNCONFIRM	            "5011"



// start ECRDefns.h
#define ECR_VERSION "3.00"

// Files Defined

// #Defines
#define ERR_COM_CLOSE                   	(-12) /* port close failed                  */
#define ERR_COM_SEND                    	(-13) /* Write to Com port failed           */
#define ERR_COM_READ                        (-15) /* Error setting port parameters      */
#define ERR_COM_WRITE                       (-16) /* Error setting port parameters      */

// Protocol related defines -- may need to be removed later as it will be part of the ECR.out application
#define SC_ACK_TIMEOUT                     	    2   		/* 2000 ms = 200 * 10 ms  venu reduced it from 3 to 2 on 07082003 */
#define SEND_BUF_SIZE                         1024//jty fix for terminal reboot during responding back to ECR
#define RECV_BUF_SIZE                         1024//jty fix for terminal reboot during responding back to ECR
// end ECRDefns.h


// start PP1000.h
#ifndef	PACKET_DELAY
#define 	PACKET_DELAY    				10 
#endif
// end PP1000.h


#endif // _ECRDEFS_H_


