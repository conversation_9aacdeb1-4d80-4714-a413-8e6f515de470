/************************************************************************** * @doc NETS
 * @module NETSECR |
 * Source code for the ECR handling functions.
 *
 * Product         :   SoftPay 2000 (Global Payment Methodology).
 * <nl>Developer   :   SoftPay Development Team (I_SP2000).
 * <nl>Notes       :
 *
 * @head3 NETSECR Functions |
 * @index | ECR
 * @end
 *
 * Copyright (c) 1996-2000 by VeriFone Inc. All rights reserved.
 *
 * No part of this software may be used, stored, compiled, reproduced,
 * modified, transcribed, translated, transmitted, or transferred, in any form
 * or by any means whether electronic, mechanical, magnetic, optical,
 * or otherwise, without the express prior written permission of VeriFone, Inc.
 *
 * Revision History
 * ------------------------------------------------------------------------
 $Log: ECRIds.h,v $
 Revision 1.10  2009-09-08 07:55:09  julee
 <PERSON>fer to Doc Of Chage 212.02_B
 1.	fix bug should do auto settlement when cepas 1 batches is left with space for 1 transaction.
 2.	fix bug, do not download blocklist automatically when NETS application is not running in foreground, when loaded with VMAC
 3.	fix bug, turn off contactless antenna when reader is in idle stage.
 4.	fix bug to handle power failure during batch upload and cause "Update combi Batch Error" on subsequent combi transactions.
 5.	fix big to hang up when user press cancel when prompted for topup card during cash topup
 6.	New ECR command, ECR_GET_TERM_FINANCIAL_TRANS_STATUS

 Revision 1.9  2009-06-23 04:13:11  julee
 Refer to Doc Of Chage 209.10
 1)         Change all Card Sales Activation wordings to Card Activation

 2)         Fix ECR response when transactions are not successful

 3)         Fix Cepas 1 debit successful receipt, date and amount printed wrongly

 4)         Fix to display Bank Limit, when bank limit exceeded

 5)         Fix debit fee, when enabled, and disabled, printed on receipt with big amount of fee.

 6)         Fix statement print, wrongly report No transaction to print. Should print whatever is available for cepas 2, or report number to hugh for cepas 1.

 Revision 1.8  2009-06-11 08:03:48  hoangdung
 1) Trigger Contactless EFTPOS purchase from ECR

 Revision 1.7  2009-04-11 17:01:29  julee
 cepas 2 ecr

 Revision 1.6  2007/01/08 05:03:37  julee
 0202.12, refer to DocOfChange.txt for details
 1) Addition of SSL on LAN
 2) Addition of EDDA function
 3) New ECR functions for logon, settlement, and get terminal status, get last response

 Revision 1.5  2007/01/04 10:58:04  yusak
 NETSV0202_11, changes:
 1)Bugzilla #1781 add flag to store enable/disable setting for Cash Refund
 2) Batch number and MAC error for statement printing
 3)Bugzilla #1449 no record found for reprint settlement report when power failure after combi settlement
 4)total combi+cashcard under "CC CREDIT Transaction - Purchase"
 5)print Ref number for ECR Combi trans when available
 6)uplicate receipt printed when ECR/Combi Receipt disabled in Multiplexer mode
 7)supervisor password changed to "237888"
 8)upgrade VMAC for Vx to version 1.70

 Revision 1.1  2006/06/14 07:36:29  julee
 inital checkin for combine base for omni 3750 and vx

 Revision 1.4  2006/03/22 10:54:30  julee
 NETSV0107.01 add VOID functions plus bug fixes. refer to DocOfChange.tzt

 Revision 1.3  2006/01/09 07:23:09  yusak
 NETSV0106.10 supposed to fix bug 216, 390, 394, 395, and STAN increment when HOST CONNECT FAILURE occurs.

 Revision 1.2  2005/12/04 16:25:56  yusak
 NETSV0106m02, Yusak Rabin, 04/12/2005

 Revision 1.3  2005/06/27 11:48:11  yusak
 Release 102


********************************************************************************/


#ifndef _ECRIDS_H_
#define _ECRIDS_H_
////////////////////////////////////////////////////////////////////
//
// D E F I N E S
//

//ECR Reader Type for Pinpad
#define ECR_READER                                   8

//Custom Transaction IDs (Used in trt & menuct.txt) 850 - 875

//Custom Operation IDs (Used in opt.txt and menuct.txt) 950 - 1000
#define ECR_EN_DIS_OPERATION                       951
#define ECR_TOPUP_EN_DIS_OPERATION                 952


//Custom Function IDs (Used in trt as function pointers 0x0800 onwards
#define ECR_EN_DIS                              0x0801
#define ECR_TOPUP_EN_DIS                        0x0802
#define ECR_CHECK_ECR_TOPUP_ENABLED             0x0803
#define ECR_VALIDATE_NETS_PURCHASE              0x0804
#define ECR_VALIDATE_CC_PURCHASE                0x0805
#define ECR_VALIDATE_NETS_REFUND                0x0806
#define ECR_VALIDATE_CC_TOPUP                   0x0807
#define ECR_FORM_SEND_RESPONSE                  0x0808
#define ECR_INIT_COM1PORT		    		    0x0809
#define ECR_VALIDATE_NETS_VOID_MANUAL	        0x080A	//ju add for void 130306
#define ECR_VALIDATE_NETS_VOID_TERM_LOG	        0x080B	//ju add for void 130306
#define ECR_VALIDATE_COMBI_PURCHASE	            0x080C
#define ECR_VALIDATE_LOGON			            0x080D	//ju add for ecr logon 231106
#define ECR_VALIDATE_SETTLEMENT	                0x080E	//ju add for ecr settlement 231106
#define ECR_VALIDATE_TRANS_LOG	                0x080F	
#define ECR_VALIDATE_CL2_REFUND	                0x0810
#define ECR_VALIDATE_CL2_TOPUP		            0x0811
#define ECR_VALIDATE_CL2_OFFLINE_CARD_ENQUIRY	0x0812
#define ECR_VALIDATE_CL2_DEBIT_STATEMENT_FEE	0x0813
#define ECR_VALIDATE_CL2_CARD_ACTIVATION    	0x0814
#define ECR_VALIDATE_CL2_EFTPOS_PURCHASE	    0x0815
#define ECR_OBSOLETE_COMMAND    	            0x0816
#define ECR_VALIDATE_TERM_STATUS    	        0x0817

/* ECR NETS CUP PREAUTH Txn @WielyRabin 25 July 2011 */
#define ECR_VALIDATE_NETSCUP_PREAUTH	0x0818
#define ECR_VALIDATE_NETSCUP_PREAUTH_VOID 0x0819
#define ECR_VALIDATE_NETSCUP_PREAUTH_COMP 0x081A
#define ECR_VALIDATE_NETSCUP_PREAUTH_COMP_VOID 0x081B
#define ECR_VALIDATE_CUP_VOID 0x0824	//[#SELFSERV] Jan 2012 

// @WielyRabin 02 Aug 2011 
#define ECR_VALIDATE_NETS_CASHDEPOSIT 0x081C


// @vsk 20110922 -- Credit Card related validation and trigger routines.	// #CREDITCARD_ECR from verifone		//porting @WielyRabin Nov 2011
#define ECR_VALIDATE_CREDIT_SALE                0x081D
#define ECR_VALIDATE_CREDIT_VOID                0x081E
#define ECR_VALIDATE_CREDIT_IPPSALE             0x081F
#define ECR_VALIDATE_CREDIT_IPPVOID             0x0820
#define ECR_VALIDATE_CREDIT_REFUND              0x0821
#define ECR_EESL_EVENT_HANDLER                  0x0822
#define ECR_VALIDATE_CREDIT_SETTLEMENT     0x828		//liu add for transcab phase 2 project July 2012
// #CREDITCARD_ECR from verifone		//porting @WielyRabin Nov 2011

#define ECR_VALIDATE_NETS_CUPPURCHASE 0x0823	// [#SELFSERV] Dec 2011 

//liu add for ECR toggling
#define ECR_TOGGLING_SWITCH_RESPONSE  0x0825
#define ECR_VALIDATE_CUPS_VOID_TERM_LOG 0x0826	//#310_MERGER to set different txn label which will be used when printing receipt [fail / success]
#define ECR_VALIDATE_NETS_LOYALTY_INQUIRY	0x0827	//sn add for NETS Loyalty Shop N Save	// [NETSPLUS]: SN: For Release 312.01 and 604.01

#define ECR_VALIDATE_CONC_PURCHASE 0x0829

//#BCA_RINTIS //#311_BCA //#MERGERING314
#define ECR_VALIDATE_BCA_PURCHASE 0x082A
#define ECR_VALIDATE_BCA_PREAUTH 0x082B
#define ECR_VALIDATE_BCA_PREAUTHCOMP 0x082C
#define ECR_VALIDATE_BCA_VOID_TERMLOG 0x082D
#define ECR_VALIDATE_BCA_VOID_MANUAL 0x082E
//#BCA_RINTIS //#311_BCA  //#MERGERING314

#define ECR_VALIDATE_NETS_FN30 0x082F

//unique ids for storing in Tran Flexi data
#define ECR_FUNC_CODE_INDEX                     0x08F0 //Praveen 10032004


// #CREDITCARD_ECR from verifone		//porting @WielyRabin Nov 2011
#define ECR_FUNC_CODE				            0x08F1
#define ECR_CREDIT_TRANS_AMOUNT	                0x08F2
#define ECR_CREDIT_ACQ_NAME		                0x08F3
#define ECR_CREDIT_INVOICE_NUMBER	            0x08F4
//#define ECR_INVOICE_NUMBER	                  0x08F5
// #CREDITCARD_ECR from verifone		//porting @WielyRabin Nov 2011

//WAZER #RSVPDev
#define ECR_RSVP_DATA_GROUPID_ALL		0x0830
#define ECR_RSVP_DATA_GROUPID			0x0831
#define ECR_RSVP_DATA_PROMO_DATA		0x0832
#define ECR_RSVP_DATA_ITEM_CODE			0x0833
#define ECR_RSVP_DATA_ITEM_COUNT		0x0834
#define ECR_RSVP_DATA_MERCH_DOLLAR		0x0835
#define ECR_RSVP_DATA_BAL_ENQ_DATA		0x0836
#define ECR_CSN							0x0837
#define ECR_RSVP_DATA_ITEM_COUNT_PREV		0x0838
#define ECR_RSVP_DATA_MERCH_DOLLAR_PREV	0x0839
#define ECR_RSVP_DATA_ITEM_COUNT_FINAL		0x083A
#define ECR_RSVP_DATA_MERCH_DOLLAR_FINAL	0x083B

#define ECR_FORM_SEND_BUFFERED_RESPONSE                  0x0900

#define ECR_RSVP_VALIDATE_GROUP_ID			0x8850
#define ECR_RSVP_VALIDATE_GROUP_TOPUP		0x8851
#define ECR_RSVP_VALIDATE_GROUP_REDEEM		0x8852
#define ECR_RSVP_VALIDATE_GROUP_BALENQ		0x8853

//For Pfr recovery routines

//MENU IDs 

#endif /* _ECRIDS_H_ */

