#include <string.h>
#include <stdio.h>
#include <stdlib.h>
#include <stdarg.h>
#include <ctosapi.h>
#include <unistd.h>
#include <pwd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/time.h>
#include <sys/wait.h>
#include <fcntl.h>
#include <errno.h>
#include <dirent.h>
#include <signal.h>
#include <pthread.h>
#include <sys/shm.h>
#include <linux/errno.h>
#include <sys/types.h>
#include <sys/ipc.h>
#include <sys/msg.h>

#include "..\Aptrans\MultiAptrans.h"
#include "..\Includes\POSTypedef.h"
#include "..\Includes\POSTrans.h"
#include "..\Includes\POSSale.h"
#include "..\Includes\POSRefund.h"
#include "..\Includes\POSAuth.h"
#include "..\Includes\POSVoid.h"
#include "..\Includes\POSTipAdjust.h"
#include "..\Includes\POSSettlement.h"
#include "..\Includes\POSSetting.h"
#include "..\Includes\POSOffline.h"
#include "..\Includes\POSHost.h"

#include "..\Includes\Wub_lib.h"
#include "..\Includes\MultiApLib.h"
#include "..\Includes\Dmenu.h"
#include "..\Includes\CTOSInput.h"

#include "..\Print\Print.h"
#include "..\UI\Display.h"
#include "..\Debug\Debug.h"
#include "..\FileModule\myFileFunc.h"
#include "..\DataBase\DataBaseFunc.h"
#include "..\powrfail\POSPOWRFAIL.h"
#include "..\Ctls\POSWave.h"
#include "..\TMS\TMS.h"

#include "..\Pinpad\PinPad.h"

#include "..\pci100\COMMS.h"



#include "ecr.h"
#include "ecr_cb.h"
#include "ecr_comm.h"

int inECR_Port = 0;


#define USB_HOST_PORT	9
#define USB_PORT	8


void vdECR_SetCommPort(int CommPort)
{
	switch (CommPort)
	{
		case 1:
			inECR_Port = d_COM1;
			break;
		case 2:
			inECR_Port = d_COM2;
			break;
		case 3:
			inECR_Port = d_COM3;
			break;
		case 8:
			inECR_Port = USB_PORT;
			break;
		case 9:
			inECR_Port = USB_HOST_PORT;
			break;
		case 10:
			inECR_Port = d_COM1;
			break;
		case 12:
			inECR_Port = USB_PORT;
			break;
		default:
			inECR_Port = -1;
			break;
	}

	return;
}

int inECR_RS232CommPortOpen(ULONG ulBaudRate, BYTE bParity, BYTE bDataBits, BYTE bStopBits)
{
	USHORT ret;

	/*should call vdECR_SetCommPort before*/
	if (inECR_Port < 0)
	{
		vdDisplayErrorMsg(1, 8, "COM Port Error");
		return d_NO;
	}

	ret = CTOS_RS232Open(inECR_Port, ulBaudRate, bParity, bDataBits, bStopBits);
	if(ret != d_OK) 
	{ 
		if (strTCTEX.fHawkerMode == 1 && strTCT.byRS232ECRPort == 12) // do not display error for hawker ECR
			;
		else
			vdDisplayErrorMsg(1, 8, "Open COM Error");
		return ret;
	}
	else
	{
		//vdDisplayErrorMsg(1, 8, "Open COM OK");
		//CTOS_LCDTPrintXY(1, 8, "										     ");
	}

	CTOS_RS232FlushTxBuffer(inECR_Port);
	CTOS_RS232FlushRxBuffer(inECR_Port);
	
	return d_OK;
}

unsigned char ucGetLRC(char *pucPacket, int inSize)
{
    unsigned char ucLRC = *pucPacket++;

    while (--inSize > 0)
        ucLRC ^= *pucPacket++;
    return(ucLRC);
}



int inECR_RS232CommSendBuf(char *szSendBuf, int inlen, VS_BOOL fPadStxEtx)
{
	char str[50];
	BYTE key;
	int times,i;
	int inSize = 0;
    char szECRSendBuf[4096 + 1];
	char szTmp[5],szHex[5];

	
	vdDebug_LogPrintf("inECR_Port[%d]  d_COM1[%d] fPadStxEtx[%d] VS_TRUE[%d]", inECR_Port, d_COM1,fPadStxEtx, VS_TRUE);
	
	if (inECR_Port < 0)
	{
		vdDisplayErrorMsg(1, 8, "COM Port Error");
		return d_NO;
	}

	memset(szECRSendBuf, 0, (int)sizeof(szECRSendBuf));
    if (fPadStxEtx == VS_TRUE) {
    	
        szECRSendBuf[inSize] = STX; 
        inSize++;

		memset(szTmp, 0x00, sizeof(szTmp));
		memset(szHex, 0x00, sizeof(szHex));
		 		 
	    sprintf((char *)szTmp, "%04d", inlen);
	    wub_str_2_hex((char *)szTmp ,(char *)szHex, 4);

	    memcpy(&(szECRSendBuf[1]), szHex, 2);
		inSize += 2;
		
        memcpy(&(szECRSendBuf[3]), szSendBuf, inlen);	
        inSize += inlen;
		
        szECRSendBuf[inSize] = ETX; 
        inSize++;
    } else {
        memcpy(szECRSendBuf, szSendBuf, inlen); 
        inSize = inlen;
    }

//    szECRSendBuf[inSize] = (char) SVC_CRC_CALC(0, &(szECRSendBuf[1]), (inSize - 1));
    szECRSendBuf[inSize] = (char) ucGetLRC(&(szECRSendBuf[1]), (inSize - 1));
    inSize++;

	// Check if COM1 is ready to send data 
	//CTOS_LCDTPrintXY(1, 7, "sending...");
	CTOS_Delay(25);
	CTOS_RS232FlushRxBuffer(inECR_Port);
	CTOS_RS232FlushTxBuffer(inECR_Port); // also flush Tx buffer before send
	while (1)
	{
        
		vdDebug_LogPrintf("inECR_Port[%d]  d_COM1[%d]", inECR_Port, d_COM1);
		// Check if Cancel key is pressed //
		CTOS_KBDHit(&key); 
		if(key == d_KBD_CANCEL) 
		{ 
			return d_NO; 
		}

		if (CTOS_RS232TxReady(inECR_Port) == d_OK)
			break;
	}

	{
		// Send data via COM1 port
		if(CTOS_RS232TxData(inECR_Port, szECRSendBuf, inSize) != d_OK) 
		{ 
			vdDisplayErrorMsg(1, 8, "COM Send Buf Error");
			//Flushing the RS232 receive buffer // 
			CTOS_RS232FlushRxBuffer(inECR_Port);
			return d_NO;
		}
	}
	//memset(str,0x00,sizeof(str));
	//sprintf(str,"send     [%d]       ",inlen);
	//CTOS_LCDTPrintXY(1, 7, str);
	CTOS_Delay(10);
	vdPCIDebug_HexPrintf("inECR_RS232CommSendBuf",szECRSendBuf,inSize);
	return d_OK;
}

// speed for pin entry
int inECR_RS232CommSendBufEx(char *szSendBuf, int inlen, VS_BOOL fPadStxEtx)
{
	char str[50];
	BYTE key;
	int times,i;
	int inSize = 0;
    char szECRSendBuf[4096 + 1];
	char szTmp[5],szHex[5];

	
	vdDebug_LogPrintf("inECR_Port[%d]  d_COM1[%d] fPadStxEtx[%d] VS_TRUE[%d]", inECR_Port, d_COM1,fPadStxEtx, VS_TRUE);
	
	if (inECR_Port < 0)
	{
		vdDisplayErrorMsg(1, 8, "COM Port Error");
		return d_NO;
	}

	memset(szECRSendBuf, 0, (int)sizeof(szECRSendBuf));
    if (fPadStxEtx == VS_TRUE) {
    	
        szECRSendBuf[inSize] = STX; 
        inSize++;

		memset(szTmp, 0x00, sizeof(szTmp));
		memset(szHex, 0x00, sizeof(szHex));
		 		 
	    sprintf((char *)szTmp, "%04d", inlen);
	    wub_str_2_hex((char *)szTmp ,(char *)szHex, 4);

	    memcpy(&(szECRSendBuf[1]), szHex, 2);
		inSize += 2;
		
        memcpy(&(szECRSendBuf[3]), szSendBuf, inlen);	
        inSize += inlen;
		
        szECRSendBuf[inSize] = ETX; 
        inSize++;
    } else {
        memcpy(szECRSendBuf, szSendBuf, inlen); 
        inSize = inlen;
    }

//    szECRSendBuf[inSize] = (char) SVC_CRC_CALC(0, &(szECRSendBuf[1]), (inSize - 1));
    szECRSendBuf[inSize] = (char) ucGetLRC(&(szECRSendBuf[1]), (inSize - 1));
    inSize++;

	// Check if COM1 is ready to send data 
	//CTOS_LCDTPrintXY(1, 7, "sending...");
	//CTOS_Delay(10);
	//CTOS_RS232FlushRxBuffer(inECR_Port);
	while (1)
	{
        
		vdDebug_LogPrintf("inECR_Port[%d]  d_COM1[%d]", inECR_Port, d_COM1);
		// Check if Cancel key is pressed //
		CTOS_KBDHit(&key); 
		if(key == d_KBD_CANCEL) 
		{ 
			return d_NO; 
		}

		if (CTOS_RS232TxReady(inECR_Port) == d_OK)
			break;
	}

	{
		// Send data via COM1 port 
		if(CTOS_RS232TxData(inECR_Port, szECRSendBuf, inSize) != d_OK) 
		{ 
			vdDisplayErrorMsg(1, 8, "COM Send Buf Error");
			//Flushing the RS232 receive buffer // 
			CTOS_RS232FlushRxBuffer(inECR_Port); 
			return d_NO; 
		}
	}
	//memset(str,0x00,sizeof(str));
	//sprintf(str,"send     [%d]       ",inlen);
	//CTOS_LCDTPrintXY(1, 7, str);
	//CTOS_Delay(10);
	vdPCIDebug_HexPrintf("inECR_RS232CommSendBuf",szECRSendBuf,inSize);
	return d_OK;
}


int inECRPadStxEtx(char* pchMsg, int inMsgSize)
{
   int inSize = 0;
   char szECRSendBuf[4096 + 1];
   
   szECRSendBuf[inSize] = STX; 
   inSize++;
   memcpy(&(szECRSendBuf[1]), pchMsg, inMsgSize); 
   inSize += inMsgSize;
   szECRSendBuf[inSize] = ETX; 
   inSize++;

   szECRSendBuf[inSize] = (char) ucGetLRC(&(szECRSendBuf[1]), (inSize - 1));
   inSize++;
} 


int inECR_RS232CommRecvBuf(char *szRecvBuf,int *inlen, int timeout)
{
	USHORT ret = d_NO;
	BYTE key;
	USHORT len = 0;
	USHORT len1=0;
	int    inSize;
	char str[50];

	if (inECR_Port < 0)
	{
		vdDisplayErrorMsg(1, 8, "COM Port Error");
		return d_NO;
	}
	CTOS_RS232FlushTxBuffer(inECR_Port);
	CTOS_TimeOutSet (TIMER_ID_1 , timeout*100);
	
	while (1)
	{
		if(CTOS_TimeOutCheck(TIMER_ID_1)  == d_YES)
            return TIME_OUT;
		
		// Check if Cancel key is pressed // 
		CTOS_KBDHit(&key); 
		if(key == d_KBD_CANCEL) 
		{ 
			return d_KBD_CANCEL;//break; 
		}
		//CTOS_LCDTPrintXY(1, 8, "receiving...");
		CTOS_Delay(10);
		// Check if data is available in COM1 port // 
		ret = CTOS_RS232RxReady(inECR_Port, &len); 
		//CTOS_Delay(1500);
		CTOS_Delay(100);
		if(ret == d_OK && len) 
		{ 
			// Get Data from COM1 port 
			CTOS_RS232RxData(inECR_Port, szRecvBuf, &len);
			if (szRecvBuf[len-1] != 0x03)
			{
				//CTOS_Delay(1000);
				CTOS_Delay(500);
				CTOS_RS232RxData(inECR_Port, &szRecvBuf[len], &len1);
				len = len+len1;
			}
			//memset(str,0x00,sizeof(str));
			//sprintf(str,"receive     [%d]       ",len);
			//CTOS_LCDTPrintXY(1, 8, str);

			
			CTOS_Delay(10);
			
			*inlen = len;

			
			vdPCIDebug_HexPrintf("inECR_RS232CommRecvBuf",szRecvBuf,len);

			return d_OK;
		} 
	}

	return d_NO;
}

int inECR_RS232SendACK(char *szSendBuf, int inlen)
{
	char str[50];
	BYTE key;
	int times,i;
	unsigned char ucTemp;

	ucTemp = ACK;

	
	vdDebug_LogPrintf("inECR_Port[%d]  d_COM1[%d]", inECR_Port, d_COM1);
	
	if (inECR_Port < 0)
	{
		vdDisplayErrorMsg(1, 8, "COM Port Error");
		return d_NO;
	}

	// Check if COM1 is ready to send data 
	//CTOS_LCDTPrintXY(1, 7, "sending...");
	CTOS_Delay(10);
	CTOS_RS232FlushRxBuffer(inECR_Port);
	while (1)
	{
        
		vdDebug_LogPrintf("inECR_Port[%d]  d_COM1[%d]", inECR_Port, d_COM1);
		// Check if Cancel key is pressed //
		CTOS_KBDHit(&key); 
		if(key == d_KBD_CANCEL) 
		{ 
			return d_NO; 
		}

		if (CTOS_RS232TxReady(inECR_Port) == d_OK)
			break;
	}

	{
		// Send data via COM1 port 
		if(CTOS_RS232TxData(inECR_Port, &ucTemp, 1) != d_OK) 
		{ 
			vdDisplayErrorMsg(1, 8, "COM Send Buf Error");
			//Flushing the RS232 receive buffer // 
			CTOS_RS232FlushRxBuffer(inECR_Port); 
			return d_NO; 
		}
	}
	//memset(str,0x00,sizeof(str));
	//sprintf(str,"send     [%d]       ",inlen);
	//CTOS_LCDTPrintXY(1, 7, str);
	CTOS_Delay(10);
	
	return d_OK;
}

int inECR_RS232RecvACK(char *szRecvBuf,int *inlen, int timeout)
{
	USHORT ret = d_NO;
	BYTE key;
	USHORT len = 0;
	USHORT len1=0;
	USHORT usTotalLen=0;
	char str[50];
	VS_BOOL fWaitForAck = VS_TRUE;
    //char chResp;
	ULONG tick;
	szRecvBuf[0] = NAK;

	vdDebug_LogPrintf("inECR_RS232RecvACK");
	if (inECR_Port < 0)
	{
		vdDisplayErrorMsg(1, 8, "COM Port Error");
		return d_NO;
	}
	CTOS_RS232FlushTxBuffer(inECR_Port);
	
   	vdDebug_LogPrintf("inECR_RS232RecvACK1");
	if (fWaitForAck == VS_TRUE) 
	{
		szRecvBuf[0] = NAK;				/* Wait for reply till Timeout */
        vdDebug_LogPrintf("inECR_RS232RecvACK2");
		CTOS_TimeOutSet (TIMER_ID_1 , timeout*100);

		do{

			if(CTOS_TimeOutCheck(TIMER_ID_1)  == d_YES)
			{
				return TIME_OUT;//d_NO;
			}

			// Check if Cancel key is pressed // 
			CTOS_KBDHit(&key); 
			if(key == d_KBD_CANCEL) 
			{ 
				return d_NO;
			}
			
			*inlen = 1;
			len = 1;

			ret = CTOS_RS232RxReady(inECR_Port, &len); 
			if (ret == d_OK)
			{	
				len= 1;
				CTOS_RS232RxData(inECR_Port, szRecvBuf, &len);
				if (len > 0)
					break;
			}		

			CTOS_Delay(50);

		}while (1);
	 } 
	else
	 	szRecvBuf[0] = ACK;

	vdDebug_LogPrintf("szRecvBuf[0][%x]", szRecvBuf[0]);

	 if (szRecvBuf[0] != ACK)
		 return d_NO;
	 else
		 return d_OK;
	 	
}


int inECR_RS232CommPortClose(void)
{
	USHORT ret;

	if (inECR_Port < 0)
	{
		vdDisplayErrorMsg(1, 8, "COM Port Error");
		return d_NO;
	}
	
	ret = CTOS_RS232Close(inECR_Port);

	return ret;
}




