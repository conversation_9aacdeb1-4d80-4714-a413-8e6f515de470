#include <string.h>
#include <stdio.h>
#include <stdlib.h>
#include <stdarg.h>
#include <ctosapi.h>
#include <unistd.h>
#include <pwd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/time.h>
#include <sys/wait.h>
#include <fcntl.h>
#include <errno.h>
#include <dirent.h>
#include <signal.h>
#include <pthread.h>
#include <sys/shm.h>
#include <linux/errno.h>
#include <sys/types.h>
#include <sys/ipc.h>
#include <sys/msg.h>


#include "..\Includes\POSTypedef.h"
#include "..\Includes\POSSettlement.h"
#include "..\Includes\POSTrans.h"
#include "..\FileModule\myFileFunc.h"

#include "..\Debug\Debug.h"
//#include "..\FlashPay\FlashPay.h"
#include "..\DataBase\npx.h"
#include "..\NETSECR\CC_Time.h"

#include "ecr.h"
#include "..\NETSECR\ECR_V4.h"

extern char gECRRecvData[4096];
extern USHORT gECRRecvLen;
extern BYTE szNETSECRSendData[4096 + 2];
extern int inECRSendDataLen;

extern BYTE gAmountBuff[AMT_BCD_SIZE + 1];
extern BYTE gCBAmountBuff[AMT_BCD_SIZE + 1];
extern BYTE g_ECRReferNum[20 + 1];
extern ULONG gulECRVoidSTAN;
extern ULONG gulECRVoidInvNum;

extern BYTE g_szECRRcptText[4096];
extern int g_inECRRcptTextLen;

#ifdef NETS_ECR_CODE
//extern NETS_TERM_TOTAL gstECRTermTotal;
extern NFP_TXN_TOTAL gstECRNFPTotal;
#endif
extern NETS_ECR_DATA	stNetECRRec;
extern NPX_TERM_TOTAL gstECRTermTotal;

BYTE g_szNPXAcqTID[8+1];
BYTE g_szNPXAcqMID[15+1];

extern BYTE szECRLastTrans[4096 + 2];
extern int inECRLastTransLen;


BYTE g_MerchRefNum[20+1];
UINT g_MSGStatus = 0x00;
BYTE g_byRcptStatus	= 0x01;//default with receipt text

ECRV40_TOKEN_ATTRIB stTokenAttr[MAX_TOKEN_SECTION][MAX_TOKEN_ITEMS];


BYTE g_baECRV4ReqMsgHeader[EV4_MSG_HEADER_SIZE];
BYTE g_baECRV4RespMsgHeader[EV4_MSG_HEADER_SIZE];

ECRV40_REQ_DATA		g_stReqPayload;
ECRV40_MSG_HEADER	g_stReqMsgHeader;

ECRV40_TOKEN_MAP stECRV40ReqTokenMap[] =
{
	{ID_TXN_TYPE, 			TOKEN_TYPE_VALUE,	ENCODING_VALUE_HEX_LITTLE, 		g_stReqPayload.baTxnType,		USHORT_BYTES2_SIZE},
	{ID_TXN_ACQUIRER, 		TOKEN_TYPE_VALUE,	ENCODING_VALUE_HEX_LITTLE, 		g_stReqPayload.baTxnAcq,		UINT_BYTES4_SIZE},
	{ID_TXN_AMOUNT, 		TOKEN_TYPE_VALUE,	ENCODING_VALUE_HEX_LITTLE, 		g_stReqPayload.baTxnAmt,		UINT_BYTES4_SIZE},
	{ID_TXN_MER_REF_NUM, 	TOKEN_TYPE_VALUE,	ENCODING_ARRAY_ASCII,			g_stReqPayload.szMerchRefNum,	EV4_REFER_NUM_SIZE},
	{ID_TXN_DATE,			TOKEN_TYPE_VALUE,	ENCODING_ARRAY_ASCII,			g_stReqPayload.szTxnDate,		EV4_TXN_DATE_SIZE},
	{ID_TXN_TIME,			TOKEN_TYPE_VALUE,	ENCODING_ARRAY_ASCII,			g_stReqPayload.szTxnTime,		EV4_TXN_TIME_SIZE},
	{ID_TXN_STAN,			TOKEN_TYPE_VALUE,	ENCODING_ARRAY_ASCII,			g_stReqPayload.szTxnSTAN,		EV4_TXN_STAN_SIZE},
	{ID_TXN_INV_NUM,		TOKEN_TYPE_VALUE,	ENCODING_ARRAY_ASCII,			g_stReqPayload.szTxnInvNum,		EV4_TXN_INVNUM_SIZE},
	{ID_TXN_TID,			TOKEN_TYPE_ARRAY,	ENCODING_ARRAY_ASCII,			g_stReqPayload.szECRTID, 		EV4_TXN_TID_SIZE},
	{ID_TXN_HOST,			TOKEN_TYPE_ARRAY,	ENCODING_ARRAY_ASCII,			g_stReqPayload.szTxnHost,		EV4_TXN_HOST_STR_SIZE},
	{ID_TXN_RECEIPT_REQUIRED,TOKEN_TYPE_ARRAY,	ENCODING_ARRAY_HEX, 			g_stReqPayload.baTxnRcptReq,	EV4_TXN_RCPT_REQ_SIZE},
	//{ID_SOF_TYPE,			TOKEN_TYPE_VALUE,	ENCODING_VALUE_HEX_LITTLE,		g_stReqCSOFInfo.baSOFType,		USHORT_BYTES2_SIZE},
	//{ID_SOF_DESCRIPTION,	TOKEN_TYPE_VALUE,	ENCODING_ARRAY_ASCII,			g_stReqCSOFInfo.szSOFDesc,		EV4_SOF_DESC_SIZE},
	//{ID_SOF_PRIORITY,		TOKEN_TYPE_VALUE,	ENCODING_VALUE_HEX_LITTLE,		g_stReqCSOFInfo.baSOFPriority, 	USHORT_BYTES2_SIZE},	
	{ID_DEVICE_DATE,		TOKEN_TYPE_VALUE,	ENCODING_ARRAY_ASCII,			g_stReqPayload.szTxnDate,		EV4_TXN_DATE_SIZE},
	{ID_DEVICE_TIME,		TOKEN_TYPE_VALUE,	ENCODING_ARRAY_ASCII,			g_stReqPayload.szTxnTime,		EV4_TXN_TIME_SIZE},
	{ID_CARD_TYPE,			TOKEN_TYPE_VALUE,	ENCODING_VALUE_HEX_LITTLE,		g_stReqPayload.baCardType,		USHORT_BYTES2_SIZE},

	{ID_PADDING, 			TOKEN_TYPE_ARRAY,	ENCODING_ARRAY_HEX, 			g_stReqPayload.baPadding,		PADDING_SIZE},

	{0, 					0, 					0, 								NULL},
};



void vdECRV40_SetRcptTextStatus(BYTE byStatus)
{
	g_byRcptStatus = byStatus;
}

BYTE byECRV40_GetRcptTextStatus(void)
{
	return g_byRcptStatus;
}


/*After ECR receive data*/
int inECR40_Transparency2Raw(BYTE *pszTPCData, int inTPCLen, BYTE *pszRawData, int *pRawLen)
{
	int inTPCIdx = 0;
	int inRawIdx = 0;
	
	vdDebug_LogPrintf("=====inECR40_Transparency2Raw=====");
	
	
	DebugAddHEX("pszTPCData", pszTPCData, inTPCLen);

	/*must skip the STX*/
	while (inTPCIdx<inTPCLen)
	{
		if (0x10 == pszTPCData[inTPCIdx] && 0x00 == pszTPCData[inTPCIdx+1])
		{
			pszRawData[inRawIdx] = 0x02;
			inRawIdx++;
			
			inTPCIdx += 2;
		}
		else if (0x10 == pszTPCData[inTPCIdx] && 0x01 == pszTPCData[inTPCIdx+1])
		{
			pszRawData[inRawIdx] = 0x04;
			inRawIdx++;
			
			inTPCIdx += 2;
		}
		else if (0x10 == pszTPCData[inTPCIdx] && 0x10 == pszTPCData[inTPCIdx+1])
		{
			pszRawData[inRawIdx] = 0x10;
			inRawIdx++;
			
			inTPCIdx += 2;
		}
		else
		{
			pszRawData[inRawIdx] = pszTPCData[inTPCIdx];
			inRawIdx++;
			inTPCIdx++;
		}
		
	}

	*pRawLen = inRawIdx;
	DebugAddHEX("pszRawData", pszRawData, *pRawLen);

	return d_OK;//inRawIdx;
	
}


/*Before send out*/
int inECR40_Raw2Transparency(BYTE *pszRawData, int inRawLen, BYTE *pszTPCData, int *pTPCLen)
{
	int inTPCIdx = 0;
	int inRawIdx = 0;
	
	vdDebug_LogPrintf("=====inECR40_Raw2Transparency=====");
	
	
	DebugAddHEX("pszRawData", pszRawData, inRawLen);

	/*must skip the STX*/
	while (inRawIdx<inRawLen)
	{
		if (0x02 == pszRawData[inRawIdx])
		{
			pszTPCData[inTPCIdx] = 0x10;
			inTPCIdx++;
			pszTPCData[inTPCIdx] = 0x00;
			inTPCIdx++;

			inRawIdx++;
		}
		else if (0x04 == pszRawData[inRawIdx])
		{
			pszTPCData[inTPCIdx] = 0x10;
			inTPCIdx++;
			pszTPCData[inTPCIdx] = 0x01;
			inTPCIdx++;

			inRawIdx++;
		}
		else if (0x10 == pszRawData[inRawIdx])
		{
			pszTPCData[inTPCIdx] = 0x10;
			inTPCIdx++;
			pszTPCData[inTPCIdx] = 0x10;
			inTPCIdx++;

			inRawIdx++;
		}
		else
		{
			pszTPCData[inTPCIdx] = pszRawData[inRawIdx];
			inTPCIdx++;

			inRawIdx++;
		}
		
	}

	*pTPCLen = inTPCIdx;
	DebugAddHEX("pszTPCData", pszTPCData, *pTPCLen);

	return inTPCIdx;
	
}

int inECRV40_ParseReqMessageHeader(BYTE *pbaMsgHeader)
{
	int inRet = d_OK;
	int inOffset = 0;

	BYTE szDateTime[32];

	vdDebug_LogPrintf("=====inECRV40_ParseReqMessageHeader=====");

	memset(&g_stReqMsgHeader, 0x00, sizeof(g_stReqMsgHeader));
	inOffset = 0;

	/*Message Len*/
	memcpy(g_stReqMsgHeader.baLength, pbaMsgHeader+inOffset, EV4_MSG_LEGNTH_SIZE);
	inOffset += EV4_MSG_LEGNTH_SIZE;
	DebugAddHEX("baLength", g_stReqMsgHeader.baLength, EV4_MSG_LEGNTH_SIZE);

	/*CRC32*/
	memcpy(g_stReqMsgHeader.baCRC32, pbaMsgHeader+inOffset, EV4_MSG_CRC32_SIZE);
	inOffset += EV4_MSG_CRC32_SIZE;
	DebugAddHEX("baCRC32", g_stReqMsgHeader.baCRC32, EV4_MSG_CRC32_SIZE);

	/*Message Version*/
	memcpy(g_stReqMsgHeader.baMsgVer, pbaMsgHeader+inOffset, EV4_MSG_VER_SIZE);
	inOffset += EV4_MSG_VER_SIZE;
	DebugAddHEX("baMsgVer", g_stReqMsgHeader.baMsgVer, EV4_MSG_VER_SIZE);
	if (0)//(memcmp(g_stReqMsgHeader.baMsgVer,"\x01",EV4_MSG_VER_SIZE) != 0)
	{
		vdECRV40_SetMsgStatus((UINT)V40_MSG_VERSION_INVALID);
		return d_NO;
	}

	/*Message Direction*/
	memcpy(g_stReqMsgHeader.baMsgDirection, pbaMsgHeader+inOffset, EV4_MSG_DIRECTION_SIZE);
	inOffset += EV4_MSG_DIRECTION_SIZE;
	DebugAddHEX("baMsgDirection", g_stReqMsgHeader.baMsgDirection, EV4_MSG_DIRECTION_SIZE);

	/*Message Time*/
	memcpy(g_stReqMsgHeader.baMsgTime, pbaMsgHeader+inOffset, EV4_MSG_TIME_SIZE);
	inOffset += EV4_MSG_TIME_SIZE;
	DebugAddHEX("baMsgTime", g_stReqMsgHeader.baMsgTime, EV4_MSG_TIME_SIZE);
	memset(szDateTime, 0x00, sizeof(szDateTime));
	inUTCSec2DateTimeString(g_stReqMsgHeader.baMsgTime, szDateTime);

	/*Message Sequence*/
	memcpy(g_stReqMsgHeader.baMsgSeq, pbaMsgHeader+inOffset, EV4_MSG_SEQ_SIZE);
	inOffset += EV4_MSG_SEQ_SIZE;
	DebugAddHEX("baMsgSeq", g_stReqMsgHeader.baMsgSeq, EV4_MSG_SEQ_SIZE);

	/*Message Class*/
	memcpy(g_stReqMsgHeader.baMsgClass, pbaMsgHeader+inOffset, EV4_MSG_CLASS_SIZE);
	inOffset += EV4_MSG_CLASS_SIZE;
	DebugAddHEX("baMsgClass", g_stReqMsgHeader.baMsgClass, EV4_MSG_CLASS_SIZE);
	if (0)//(memcmp(g_stReqMsgHeader.baMsgVer,"\x01",EV4_MSG_VER_SIZE) != 0)
	{
		vdECRV40_SetMsgStatus((UINT)V40_MSG_CLASS_INVALID);
		return d_NO;
	}
	
	/*Message Type*/
	memcpy(g_stReqMsgHeader.baMsgType, pbaMsgHeader+inOffset, EV4_MSG_TYPE_SIZE);
	inOffset += EV4_MSG_TYPE_SIZE;
	DebugAddHEX("baMsgType", g_stReqMsgHeader.baMsgType, EV4_MSG_TYPE_SIZE);
	
	/*Message Code*/
	memcpy(g_stReqMsgHeader.baMsgCode, pbaMsgHeader+inOffset, EV4_MSG_CODE_SIZE);
	inOffset += EV4_MSG_SEQ_SIZE;
	DebugAddHEX("baMsgCode", g_stReqMsgHeader.baMsgCode, EV4_MSG_CODE_SIZE);

	/*Message Completion*/
	memcpy(g_stReqMsgHeader.baMsgCompletion, pbaMsgHeader+inOffset, EV4_MSG_COMPLETION_SIZE);
	inOffset += EV4_MSG_COMPLETION_SIZE;
	DebugAddHEX("baMsgCompletion", g_stReqMsgHeader.baMsgCompletion, EV4_MSG_COMPLETION_SIZE);
	if (memcmp(g_stReqMsgHeader.baMsgCompletion,"\x01",EV4_MSG_COMPLETION_SIZE) != 0)
	{
		vdECRV40_SetMsgStatus((UINT)V40_MSG_COMPLETION_INVALID);
		return d_NO;
	}

	/*Message Notification*/
	memcpy(g_stReqMsgHeader.baMsgNotification, pbaMsgHeader+inOffset, EV4_MSG_NOTIFICATION_SIZE);
	inOffset += EV4_MSG_NOTIFICATION_SIZE;
	DebugAddHEX("baMsgNotification", g_stReqMsgHeader.baMsgNotification, EV4_MSG_NOTIFICATION_SIZE);
	if (memcmp(g_stReqMsgHeader.baMsgNotification,"\x00",EV4_MSG_NOTIFICATION_SIZE) != 0)
	{
		vdECRV40_SetMsgStatus((UINT)V40_MSG_NOTIFICATION_INVALID);
		return d_NO;
	}
	
	/*Message Status*/
	memcpy(g_stReqMsgHeader.baMsgStatus, pbaMsgHeader+inOffset, EV4_MSG_STATUS_SIZE);
	inOffset += EV4_MSG_STATUS_SIZE;
	DebugAddHEX("baMsgStatus", g_stReqMsgHeader.baMsgStatus, EV4_MSG_STATUS_SIZE);
	if (0)//(memcmp(g_stReqMsgHeader.baMsgVer,"\x01",EV4_MSG_VER_SIZE) != 0)
	{
		vdECRV40_SetMsgStatus((UINT)V40_MSG_STATUS_INVALID);
		return d_NO;
	}
	
	/*Device Provider*/
	memcpy(g_stReqMsgHeader.baDeviceProvider, pbaMsgHeader+inOffset, EV4_DEVICE_PROVIDER_SIZE);
	inOffset += EV4_DEVICE_PROVIDER_SIZE;
	DebugAddHEX("baDeviceProvider", g_stReqMsgHeader.baDeviceProvider, EV4_DEVICE_PROVIDER_SIZE);

	/*Device Type*/
	memcpy(g_stReqMsgHeader.baDeviceType, pbaMsgHeader+inOffset, EV4_DEVICE_TYPE_SIZE);
	inOffset += EV4_DEVICE_TYPE_SIZE;
	DebugAddHEX("baDeviceType", g_stReqMsgHeader.baDeviceType, EV4_DEVICE_TYPE_SIZE);

	/*Device Location*/
	//memcpy(g_stReqMsgHeader.baDeviceLocation, pbaMsgHeader+inOffset, EV4_DEVICE_LOCATION_SIZE);
	//inOffset += EV4_DEVICE_LOCATION_SIZE;
	//DebugAddHEX("baDeviceLocation", g_stReqMsgHeader.baDeviceLocation, EV4_DEVICE_LOCATION_SIZE);

	/*Device Number*/
	memcpy(g_stReqMsgHeader.baDeviceNum, pbaMsgHeader+inOffset, EV4_DEVICE_NUM_SIZE);
	inOffset += EV4_DEVICE_NUM_SIZE;
	DebugAddHEX("baDeviceNum", g_stReqMsgHeader.baDeviceNum, EV4_DEVICE_NUM_SIZE);

	/*Encrypt Algorithm*/
	memcpy(g_stReqMsgHeader.baEncryptAlgo, pbaMsgHeader+inOffset, EV4_ENCRYPT_ALGO_SIZE);
	inOffset += EV4_ENCRYPT_ALGO_SIZE;
	DebugAddHEX("baEncryptAlgo", g_stReqMsgHeader.baEncryptAlgo, EV4_ENCRYPT_ALGO_SIZE);
	if (memcmp(g_stReqMsgHeader.baEncryptAlgo,"\x00",EV4_ENCRYPT_ALGO_SIZE) != 0)
	{
		vdECRV40_SetMsgStatus((UINT)V40_MSG_ALGORITHM_MANDATORY);
		return d_NO;
	}

	/*Encrypt KeyIndex*/
	memcpy(g_stReqMsgHeader.baEncryptKeyIndex, pbaMsgHeader+inOffset, EV4_ENCRYPT_KEY_IDX_SIZE);
	inOffset += EV4_ENCRYPT_KEY_IDX_SIZE;
	DebugAddHEX("baEncryptKeyIndex", g_stReqMsgHeader.baEncryptKeyIndex, EV4_ENCRYPT_KEY_IDX_SIZE);
	if (0)//(memcmp(g_stReqMsgHeader.baMsgVer,"\x01",EV4_MSG_VER_SIZE) != 0)
	{
		vdECRV40_SetMsgStatus((UINT)V40_MSG_KEY_INDEX_INVALID);
		return d_NO;
	}

	/*Encrypt MAC*/
	memcpy(g_stReqMsgHeader.baEncryptMAC, pbaMsgHeader+inOffset, EV4_ENCRYPT_MAC_SIZE);
	inOffset += EV4_ENCRYPT_MAC_SIZE;
	DebugAddHEX("baEncryptMAC", g_stReqMsgHeader.baEncryptMAC, EV4_ENCRYPT_MAC_SIZE);
	if (0)//(memcmp(g_stReqMsgHeader.baMsgVer,"\x01",EV4_MSG_VER_SIZE) != 0)
	{
		vdECRV40_SetMsgStatus((UINT)V40_MSG_MAC_FAILED);
		return d_NO;
	}
		
	/*baReserved*/
	memcpy(g_stReqMsgHeader.baReserved, pbaMsgHeader+inOffset, EV4_HEADER_RESERVED_SIZE);
	inOffset += EV4_HEADER_RESERVED_SIZE;
	DebugAddHEX("baReserved", g_stReqMsgHeader.baReserved, EV4_HEADER_RESERVED_SIZE);

	if (inOffset != 64)
	{
		vdECRV40_SetMsgStatus((UINT)V40_MSG_LENGTH_MINIMUM);
		return d_NO;
	}
	return d_OK;
}

int inECRV40_ParsePayload(BYTE *pszPayload, int inPayloadSize)
{
	int inRet = d_OK;
	int inOffset = 0;
	BYTE baTokenHeader[TOKEN_HEADER_SIZE];

	BYTE baTokenBlkLen[TOKEN_BLOCK_LENGTH_SIZE];
	BYTE baTokenID[TOKEN_ID_SIZE];
	BYTE byTokenType;
	BYTE byTokenEncode;

	UINT uiTokenBlkLen = 0;
	USHORT ushTokenID = 0;

	UINT uiValueLen = 0;

	int inFindCode = 0;

	int inIdx = 0;
	int inMaxRec = 0;
	BYTE szDispMsg[64];

	vdDebug_LogPrintf("=====inECRV40_ParsePayload=====");
	
	DebugAddHEX("pszPayload", pszPayload, inPayloadSize);
	
	inMaxRec = sizeof(stECRV40ReqTokenMap)/sizeof(ECRV40_TOKEN_MAP);
	
	vdDebug_LogPrintf("inMaxRec[%d] inPayloadSize[%d]", inMaxRec, inPayloadSize);
	while (inOffset<inPayloadSize)
	{
		memset(baTokenBlkLen, 0x00, sizeof(baTokenBlkLen));
		DebugAddHEX("TOKEN_BLOCK_LENGTH", pszPayload+inOffset, TOKEN_BLOCK_LENGTH_SIZE);
		memcpy(baTokenBlkLen, pszPayload+inOffset, TOKEN_BLOCK_LENGTH_SIZE);
		memcpy(&uiTokenBlkLen, baTokenBlkLen, TOKEN_BLOCK_LENGTH_SIZE);
		vdDebug_LogPrintf("uiTokenBlkLen[%d][%04X]",uiTokenBlkLen, uiTokenBlkLen);
		inOffset += TOKEN_BLOCK_LENGTH_SIZE;
		
		memset(baTokenID, 0x00, sizeof(baTokenID));
		DebugAddHEX("TOKEN_ID", pszPayload+inOffset, TOKEN_ID_SIZE);
		memcpy(baTokenID, pszPayload+inOffset, TOKEN_ID_SIZE);
		memcpy(&ushTokenID, baTokenID, TOKEN_ID_SIZE);
		vdDebug_LogPrintf("ushTokenID[%04X]",ushTokenID);
		inOffset += TOKEN_ID_SIZE;

		memcpy(&byTokenType, pszPayload+inOffset, TOKEN_TYPE_SIZE);
		inOffset += TOKEN_TYPE_SIZE;
		vdDebug_LogPrintf("byTokenType[%02X]",byTokenType);
		
		memcpy(&byTokenEncode, pszPayload+inOffset, TOKEN_ENCODE_SIZE);
		inOffset += TOKEN_ENCODE_SIZE;
		vdDebug_LogPrintf("byTokenEncode[%02X]",byTokenEncode);

		uiValueLen = uiTokenBlkLen-TOKEN_HEADER_SIZE;
		vdDebug_LogPrintf("uiValueLen[%d]",uiValueLen);
				
		inFindCode =  0;
		for (inIdx=0; inIdx<inMaxRec; inIdx++)
		{
			//vdDebug_LogPrintf("inIdx[%d] Table ushTokenID[%04X]",inIdx, stECRV40ReqTokenMap[inIdx].ushTokenID);
			if(ushTokenID == stECRV40ReqTokenMap[inIdx].ushTokenID)
			{
				vdDebug_LogPrintf("inIdx[%d] Table ushTokenID[%04X]",inIdx, stECRV40ReqTokenMap[inIdx].ushTokenID);
				inFindCode =  1;// find token

				if (NULL != stECRV40ReqTokenMap[inIdx].ptrValue)
				{
					memcpy(stECRV40ReqTokenMap[inIdx].ptrValue, pszPayload+inOffset, uiValueLen);
					DebugAddHEX("ptrValue", stECRV40ReqTokenMap[inIdx].ptrValue, uiValueLen);
				}
			}
		}

		inOffset += uiValueLen;
		
		/*after check all table*/
		if (1 != inFindCode)
		{
			memset(szDispMsg, 0x00, sizeof(szDispMsg));
			sprintf(szDispMsg, "Unknown ID[%02X]", ushTokenID);
			vdDisplayErrorMsg(1, 8, szDispMsg);
			//vdDebug_PrintOnPaper("%s", szDispMsg);
			/*if get unknown field , still go process*/
			//return(d_NO);
		}
		
	}
	
	return inRet;
}



int inECRV40_AssigneTransData(void)
{
    int inRet = d_OK;

	UINT uiTxnAmt = 0;
	BYTE szAmtStr[AMT_ASC_SIZE+1];

	vdDebug_LogPrintf("=====inECRV40_AssigneTransData=====");

	memset(gAmountBuff, 0x00, sizeof(gAmountBuff));
	memset(gCBAmountBuff, 0x00, sizeof(gCBAmountBuff));

	/*Purchase Get Amount*/
	memcpy(&uiTxnAmt, g_stReqPayload.baTxnAmt, 4);
	vdDebug_LogPrintf("uiTxnAmt[%d]", uiTxnAmt);
	memset(szAmtStr, 0x00, sizeof(szAmtStr));
	sprintf(szAmtStr, "%012d", uiTxnAmt);
	vdDebug_LogPrintf("szAmtStr[%s]", szAmtStr);
	wub_str_2_hex(szAmtStr, gAmountBuff, AMT_ASC_SIZE);

	/*Merchant Ref Num*/
	memcpy(g_MerchRefNum, g_stReqPayload.szMerchRefNum, EV4_REFER_NUM_SIZE);
	g_MerchRefNum[EV4_REFER_NUM_SIZE] = 0x00;
	vdDebug_LogPrintf("baMerchRefNum[%s]", g_MerchRefNum);

	memset(g_ECRReferNum, 0x00, sizeof(g_ECRReferNum));
	memcpy(g_ECRReferNum, g_MerchRefNum, sizeof(g_ECRReferNum));
	g_ECRReferNum[FC_REFER_NUM_SIZE] = 0x00; // Please make sure not more than nets Ref Num Max define
	vdDebug_LogPrintf("g_ECRReferNum[%s]", g_ECRReferNum);
	
	/*STAN use for void*/
	vdDebug_LogPrintf("g_stReqPayload.szTxnSTAN[%s]", g_stReqPayload.szTxnSTAN);
	gulECRVoidSTAN = atol(g_stReqPayload.szTxnSTAN);
	vdDebug_LogPrintf("gulECRVoidSTAN[%d]", gulECRVoidSTAN);

	/*Invoice number use for void*/
	vdDebug_LogPrintf("g_stReqPayload.szTxnInvNum[%s]", g_stReqPayload.szTxnInvNum);
	gulECRVoidInvNum = atol(g_stReqPayload.szTxnInvNum);
	vdDebug_LogPrintf("gulECRVoidInvNum[%d]", gulECRVoidInvNum);

	//gulECRVoidInvNum = get_env_int("#ECRINV");
	//vdDebug_LogPrintf("test gulECRVoidInvNum[%d]", gulECRVoidInvNum);
	
	/*for now, we still use STAN for void, because ECR send back STAN as Inv number in SALE response*/
	//if (gulECRVoidInvNum>0)
		//gulECRVoidSTAN = gulECRVoidInvNum;

	/*Receipt Text control*/
	if (g_stReqPayload.baTxnRcptReq[0] == 0x00)
		vdECRV40_SetRcptTextStatus(0x00);

	/*Txn Host String indicate which host for settle/void*/
	vdDebug_LogPrintf("g_stReqPayload.szTxnHost[%d]", g_stReqPayload.szTxnHost);
	
	return inRet;
}


void vdECRV40_SetMsgStatus(UINT uiStatus)
{
	vdDebug_LogPrintf("vdECRV40_SetMsgStatus  =[%x]",uiStatus);
	g_MSGStatus = uiStatus;
}

UINT uiECRV40_GetMsgStatus(void)
{
	vdDebug_LogPrintf("uiECRV40_GetMsgStatus  =[%x]",g_MSGStatus);
	return g_MSGStatus;
}

int inECRV40_TransNotSupport(void)
{
	int inRet = d_OK;

    CTOS_LCDTClearDisplay();
    
	CTOS_Beep();
	CTOS_Delay(300);
    CTOS_Beep();
	CTOS_LCDTPrintXY(1, 8, "Trans Not Supported");
	WaitKey(5);

	/*set header response code*/
	//vdECRV40_SetMsgStatus(0);
	inMultiAP_ECRSendSuccessResponse();
	return d_OK;
}


int inECRV40_GetTerminalStatus(void)
{
	int inChkKey = 0;
	int inChkTIDMID = 0;
	
	vdDebug_LogPrintf("=====inECRV40_GetTerminalStatus=====");
		
	/*Check terminal status, and set header response code*/
	vdECRV40_SetMsgStatus((UINT)0);
	
	return d_OK;
}

int inECRV40_ProcessFuncs(void)
{
	int inRet = d_OK;

	UINT uiMsgType = 0;
	UINT uiMsgCode = 0;
	
	BYTE *puiValue = &uiMsgCode;

	vdDebug_LogPrintf("=====inECRV40_ProcessFuncs=====");

	puiValue = &uiMsgType;
	memcpy(puiValue, g_stReqMsgHeader.baMsgType, EV4_MSG_TYPE_SIZE);
	vdDebug_LogPrintf("uiMsgType[%08X]", uiMsgType);
	
	puiValue = &uiMsgCode;
	memcpy(puiValue, g_stReqMsgHeader.baMsgCode, EV4_MSG_CODE_SIZE);
	vdDebug_LogPrintf("uiMsgCode[%08X]", uiMsgCode);

	switch (uiMsgType)
	{	
		case MSG_TYPE_DEVICE:
			switch(uiMsgCode)
			{
				case MSG_CODE_DEVICE_STATUS:
					inRet = inECRV40_GetTerminalStatus();
					inMultiAP_ECRSendSuccessResponse();
					break;
				case MSG_CODE_DEVICE_RESET:
				case MSG_CODE_DEVICE_TIME_SYNC:
				case MSG_CODE_DEVICE_PROFILE:
				case MSG_CODE_DEVICE_SOF_DETAIL:
				case MSG_CODE_DEVICE_SOF_PRIORISATION:
				case MSG_CODE_DEVICE_LOGON:
					inRet = inECRV40_TransNotSupport();
					break;
				case MSG_CODE_DEVICE_TMS:
					inRet = inECRV40_TransNotSupport();
					break;
				case MSG_CODE_DEVICE_SETTLEMENT:
				case MSG_CODE_DEVICE_PRE_SETTLEMENT:
                    break;
				default:
					inRet = inECRV40_TransNotSupport();
					break;
			}
			break;
			
		case MSG_TYPE_AUTH:
			switch(uiMsgCode)
			{
				case MSG_CODE_AUTH_MUTUAL_STEP_1:
				case MSG_CODE_AUTH_MUTUAL_STEP_2:
                	inRet = inECRV40_TransNotSupport();
					break;
				default:
					inRet = inECRV40_TransNotSupport();
					break;
			}
			break;
			
		case MSG_TYPE_CARD:
			switch(uiMsgCode)
			{
				case MSG_CODE_CARD_DETECT:
				case MSG_CODE_CARD_READ_PURSE:
				case MSG_CODE_CARD_READ_HISTORICAL_LOG:
				case MSG_CODE_CARD_READ_RSVP:
					inRet = inECRV40_TransNotSupport();
					break;
				default:
					inRet = inECRV40_TransNotSupport();
					break;
			}
			break;

		case MSG_TYPE_PAYMENT:
			switch(uiMsgCode)
			{
				case MSG_CODE_PAYMENT_AUTO:
					inRet = inECRV40_TransNotSupport();
					break;
				case MSG_CODE_PAYMENT_EFT:
					inRet = inECRV40_TransNotSupport();
					break;
				case MSG_CODE_PAYMENT_NCC:
					inRet = inECRV40_TransNotSupport();
					break;
				case MSG_CODE_PAYMENT_NFP:
					inRet = inECRV40_TransNotSupport();
					break;
				case MSG_CODE_PAYMENT_RSVP:
					inRet = inECRV40_TransNotSupport();
					break;
				case MSG_CODE_PAYMENT_CRD:
					inRet = inECRV40_TransNotSupport();
					break;
				case MSG_CODE_PAYMENT_DEB:
					inRet = inECRV40_TransNotSupport();
					break;
				case MSG_CODE_PAYMENT_BCA:
					inRet = inECRV40_TransNotSupport();
					break;
				default:
					vdECRV40_SetMsgStatus((UINT)V40_MSG_CODE_NOT_SUPPORTED);
					inRet = inECRV40_TransNotSupport();
					break;
			}
			break;
		case MSG_TYPE_CANCELLATION:
			switch(uiMsgCode)
			{
				case MSG_CODE_CANCELLATION_VOID:
					inRet = inECRV40_TransNotSupport();
					break;
				case MSG_CODE_CANCELLATION_REFUND:
					inRet = inECRV40_TransNotSupport();
					break;
				default:
					vdECRV40_SetMsgStatus((UINT)V40_MSG_CODE_NOT_SUPPORTED);
					inRet = inECRV40_TransNotSupport();
					break;
			}
			break;
			
		case MSG_TYPE_TOPUP:
			switch(uiMsgCode)
			{
				case MSG_CODE_TOPUP_NCC:
				case MSG_CODE_TOPUP_NFP:
				case MSG_CODE_TOPUP_RSVP:
					inRet = inECRV40_TransNotSupport();
					break;
				default:
					vdECRV40_SetMsgStatus((UINT)V40_MSG_CODE_NOT_SUPPORTED);
					inRet = inECRV40_TransNotSupport();
					break;
			}
			break;
			
		case MSG_TYPE_RECORD:
			switch(uiMsgCode)
			{
				case MSG_CODE_RECORD_SUMMARY:
				case MSG_CODE_RECORD_UPLOAD:
				case MSG_CODE_RECORD_CLEAR:
					inRet = inECRV40_TransNotSupport();
					break;
				default:
					vdECRV40_SetMsgStatus((UINT)V40_MSG_CODE_NOT_SUPPORTED);
					inRet = inECRV40_TransNotSupport();
					break;
			}
			break;

		default:
			vdECRV40_SetMsgStatus((UINT)V40_MSG_TYPE_NOT_SUPPORTED);
			inRet = inECRV40_TransNotSupport();
			break;
	}
	
	return inRet;
}


int inECRReceiveAnalyse_ECR40(void)
{
	unsigned char uszECRAscData[500+1];
	unsigned char uszECRHexData[500+1];

	unsigned char szECRRecvData[4096+2];
	USHORT lECRRecvLen = 0;

	int inOffset = 0;
	unsigned char uszECRDataLen[4+1];
	int inRcvLen = 0;

	BYTE baPayload[4096+2];
	USHORT ulPayloadLen = 0;

	int inRet = 0;
	extern int inECRPrntDbg;
	
	inECRPrntDbg = get_env_int("#ECRPRNT");

	//lECRRecvLen = gECRRecvLen;
	//memcpy(szECRRecvData, gECRRecvData, lECRRecvLen);
	/*shareECR pass back the whole data, so we still need do covert again*/
	inRet = inECR40_Transparency2Raw(gECRRecvData, gECRRecvLen, szECRRecvData, &lECRRecvLen);
	if (inRet != d_OK)
	{
		vdECRV40_SetMsgStatus((UINT)V40_MSG_DATA_TRANSPARENCY_ERROR);
		return d_NO;
	}
	DebugAddHEX("ECR DATA", szECRRecvData, lECRRecvLen);
	
	vdECRDebug_PrintOnPaper("ECR DATA[%2X %2X ... %2X %2X]", szECRRecvData[0], szECRRecvData[1], szECRRecvData[lECRRecvLen-2], szECRRecvData[lECRRecvLen-1]);

	memset(&g_stReqMsgHeader, 0x00, sizeof(g_stReqMsgHeader));
	memset(&g_stReqPayload, 0x00, sizeof(g_stReqPayload));
	//memset(&stNetECRRec, 0x00, sizeof(stNetECRRec));
	//memset(&stNetECRV21Rec, 0x00, sizeof(stNetECRV21Rec));

	inOffset += 1;

	/*Parse Header*/
	inRet = inECRV40_ParseReqMessageHeader(&szECRRecvData[1]);
	if (inRet != d_OK)
	{
		vdECRV40_SetMsgStatus((UINT)V40_MSG_INTEGRITY_FAILED);
		return d_NO;
	}

	memset(baPayload, 0x00, sizeof(baPayload));
	ulPayloadLen = lECRRecvLen-EV4_MSG_HEADER_SIZE-2;
	memcpy(baPayload, &szECRRecvData[1+EV4_MSG_HEADER_SIZE], ulPayloadLen);
	
	/*parse payload*/
	if (ulPayloadLen>8)
	{
		inRet = inECRV40_ParsePayload(baPayload, ulPayloadLen);
		if (inRet != d_OK)
		{
			vdECRV40_SetMsgStatus((UINT)V40_TOKEN_PARSING_ERROR);
			return d_NO;
		}
	}

	/*Assigne ECR data as trans data, Amount....*/
	inECRV40_AssigneTransData();

	/*Before Process funcs, init status*/
	vdECRV40_SetMsgStatus((UINT)V40_INCORRECT_FLOW);
	
	inECRV40_ProcessFuncs();
	//inMultiAP_ECRSendSuccessResponse();

	return d_OK;

}


/*Message Sequence should get from table*/
UINT uinECR40_GetMsgSequence(void)
{
	return 1;
}

USHORT ushECR40_GetMsgClass(void)
{
	USHORT ushClass = 0x02;
	
	return ushClass;
}

void vdECR40_GetSOFAcquirer(void)
{
	strcpy(stNetECRV40RespRec.szSOFACQ, EV4_HOST_ID_UPOS);
}

void vdECR40_GetSOFName(void)
{
	strcpy(stNetECRV40RespRec.szSOFName, EV4_HOST_ID_UPOS);
}

int inECR40_GetTxnDate(void)
{
	BYTE szTemp[EV4_TXN_DATE_SIZE + 1] = {0};

	memset(stNetECRV40RespRec.szTxnDate, 0x00, sizeof(stNetECRV40RespRec.szTxnDate));

	// Current date, same with receipt
	wub_hex_2_str(srTransRec.szDate, szTemp, DATE_BCD_SIZE);
	memcpy(stNetECRV40RespRec.szTxnDate, "20", 2);
	memcpy(&stNetECRV40RespRec.szTxnDate[2], srTransRec.szYear, 2);
    memcpy(&stNetECRV40RespRec.szTxnDate[4], szTemp, 4);
	
	return(d_OK);
}

int inECR40_GetVoidTxnAmt(void)
{
	BYTE szAmtStr[12+1];
	UINT uiTxnAmt = 0;

	memset(szAmtStr, 0x00, sizeof(szAmtStr));

	wub_hex_2_str(srTransRec.szTotalAmount, szAmtStr, AMT_BCD_SIZE);
	szAmtStr[12] = 0x00;

	uiTxnAmt = atoi(szAmtStr);

	memcpy(stNetECRV40RespRec.baTxnAmt, &uiTxnAmt, UINT_BYTES4_SIZE);
}

void vdECR40_GetCardEntryMode(void)
{
	BYTE szTemp[ECR_CARD_ENTRY_MODE_SIZE + 1] = {0};

	// Trans Data Entry Mode 
	sprintf(szTemp, "%02d", srTransRec.byEntryMode);
	if (srTransRec.byEntryMode == CARD_ENTRY_ICC)
		stNetECRRec.szECRCardEntryMode[0] = 0x01;
	else if (srTransRec.byEntryMode == CARD_ENTRY_WAVE)
		stNetECRRec.szECRCardEntryMode[0] = 0x02;
	else if (srTransRec.byEntryMode == CARD_ENTRY_MSR)
		stNetECRRec.szECRCardEntryMode[0] = 0x04;
	else if (srTransRec.byEntryMode == CARD_ENTRY_FALLBACK)
		stNetECRRec.szECRCardEntryMode[0] = 0x08;
	else
		stNetECRRec.szECRCardEntryMode[0] = 0x00;

	//wub_strpad((char *) szTemp,(char *) szTemp, ISZERO, ECR_CARD_ENTRY_MODE_SIZE, LEFT);
	//memcpy(stNetECRRec.szECRCardEntryMode, szTemp, ECR_CARD_ENTRY_MODE_SIZE);

	return;
}

int g_inSettleCardTotal = 0;

void vdECR40_SetSettleCardTotal(int inTotal)
{
	g_inSettleCardTotal = inTotal;
}

int inECR40_GetSettleCardTotal(void)
{
	return g_inSettleCardTotal;
}

int inECR40_GetSettleSOFAcquirer(void)
{
	int inHostLogoAcq = 0;

	
	vdDebug_LogPrintf("=====inECR40_GetSettleSOFAcquirer=====");

	inHostLogoAcq = strTCTEX.inRcptHostLogoAcq; //get_env_int("#RCPTHOSTLOGOACQ");

	if (inHostLogoAcq == 1)
		strcpy(stNetECRV40RespRec.szSOFACQ, "NETS");
	else if (inHostLogoAcq == 2)
		strcpy(stNetECRV40RespRec.szSOFACQ, "DBS");
	else if (inHostLogoAcq == 3)
		strcpy(stNetECRV40RespRec.szSOFACQ, "OCBC");
	else if (inHostLogoAcq == 4)
		strcpy(stNetECRV40RespRec.szSOFACQ, "UOB");
	else if (inHostLogoAcq == 9)
		strcpy(stNetECRV40RespRec.szSOFACQ, "UPOS");
	else
		strcpy(stNetECRV40RespRec.szSOFACQ, "UPOS");
}


/*should use APPID/HOST ID*/
void vdECR40_LastSettleFileName(BYTE *szAppID, BYTE *pszFileName)
{
	sprintf(pszFileName, "%s%s%s", PUBLIC_PATH, szAppID, ECR_LASTSETTLE_FILE);
	vdDebug_LogPrintf("vdECR40_LastTransFileName[%s]", pszFileName);
}

int inECRV40_GetLastSettleData(void)
{
	int inRet = d_OK;
	BYTE szFileName[64+1];
	BYTE szTxnHost[EV4_TXN_HOST_STR_SIZE+1];

	vdDebug_LogPrintf("=====inECRV40_GetLastSettleData=====");
	
	memset(szECRLastTrans, 0x00, sizeof(szECRLastTrans));
	inECRLastTransLen = 0;

	vdDebug_LogPrintf("g_stReqPayload.szTxnHost[%s]", g_stReqPayload.szTxnHost);

	memset(szTxnHost, 0x00, sizeof(szTxnHost));
	strcpy(szTxnHost, g_stReqPayload.szTxnHost);
	if (strlen(szTxnHost)<0 || strlen(szTxnHost) == 0)
		strcpy(szTxnHost, EV4_HOST_ID_UPOS);
	
	vdDebug_LogPrintf("szTxnHost[%s]", szTxnHost);

	memset(szFileName, 0x00, sizeof(szFileName));
	vdECR40_LastSettleFileName(szTxnHost, szFileName);

	inRet = inCTOS_GetFileSize(szFileName);
	inECRLastTransLen = inRet;
	inCTOS_ReadFile(szFileName, szECRLastTrans, inECRLastTransLen);

	if (inRet>0)
	{
		//read success, set the msg status
		vdECRV40_SetMsgStatus((UINT)V40_SUCCESS);
	}
	else
	{
		vdECRV40_SetMsgStatus((UINT)V40_INVALID_PARAMETER);
		inECRLastTransLen = 0;
	}
	return inECRLastTransLen;
}

int inECR40_SaveLastSettleData(void)
{
	BYTE szFileName[64];
	BYTE szTxnHost[EV4_TXN_HOST_STR_SIZE+1];
	
	UINT uiMsgType = 0;
	UINT uiMsgCode = 0;

	USHORT ushTxnType = 0;
	
	BYTE *puiValue = &uiMsgCode;

	int inRetVal = d_OK;

	vdDebug_LogPrintf("=====inECR40_SaveLastSettleData=====");

	puiValue = &uiMsgType;
	memcpy(puiValue, g_stReqMsgHeader.baMsgType, EV4_MSG_TYPE_SIZE);
	vdDebug_LogPrintf("uiMsgType[%08X]", uiMsgType);
	
	puiValue = &uiMsgCode;
	memcpy(puiValue, g_stReqMsgHeader.baMsgCode, EV4_MSG_CODE_SIZE);
	vdDebug_LogPrintf("uiMsgCode[%08X]", uiMsgCode);

	vdDebug_LogPrintf("g_stReqPayload.szTxnHost[%s]", g_stReqPayload.szTxnHost);

	memset(szTxnHost, 0x00, sizeof(szTxnHost));
	strcpy(szTxnHost, g_stReqPayload.szTxnHost);
	if (strlen(szTxnHost)<0 || strlen(szTxnHost) == 0)
		strcpy(szTxnHost, EV4_HOST_ID_UPOS);
	
	vdDebug_LogPrintf("szTxnHost[%s]", szTxnHost);

	memset(szFileName, 0x00, sizeof(szFileName));
	vdECR40_LastSettleFileName(szTxnHost, szFileName);

	/*Write to file*/
	remove(szFileName);
	CTOS_Delay(50);
	inRetVal = inCTOS_WriteFile(szFileName, szNETSECRSendData, inECRSendDataLen);		
	return d_OK;
}


/*Here is the function to check if current cmd need save as last trans.
As checked with NETS, we only save for the financial commands*/
int inECR40_ChkNeedSaveTrans(UINT uiMsgType, UINT uiMsgCode)
{
	int inRet = d_NO; // default set as no

	vdDebug_LogPrintf("=====inECR40_ChkNeedSaveTrans=====");
	
	switch (uiMsgType)
	{	
		case MSG_TYPE_DEVICE:
			switch(uiMsgCode)
			{
				case MSG_CODE_DEVICE_SETTLEMENT:
					inRet = d_OK;
					break;
				default:
					inRet = d_NO;
					break;
			}
			break;
			
		case MSG_TYPE_AUTH:
			inRet = d_NO;
			break;
			
		case MSG_TYPE_CARD:
			switch(uiMsgCode)
			{
				case MSG_CODE_CARD_PERFORM_DEBIT:
					inRet = d_OK;
					break;
				default:
					inRet = d_NO;
					break;
			}
			break;

		case MSG_TYPE_PAYMENT:
			switch(uiMsgCode)
			{
				case MSG_CODE_PAYMENT_AUTO:
					inRet = d_OK;
					break;
				case MSG_CODE_PAYMENT_EFT:
					inRet = d_OK;
					break;
				case MSG_CODE_PAYMENT_NCC:
					inRet = d_OK;
					break;
				case MSG_CODE_PAYMENT_NFP:
					inRet = d_OK;
					break;
				case MSG_CODE_PAYMENT_RSVP:
					inRet = d_OK;
					break;
				case MSG_CODE_PAYMENT_CRD:
					inRet = d_OK;
					break;
				case MSG_CODE_PAYMENT_DEB:
					inRet = d_OK;
					break;
				case MSG_CODE_PAYMENT_BCA:
					inRet = d_OK;
					break;
				case MSG_CODE_PAYMENT_EZL:
					inRet = d_OK;
					break;
				default:
					inRet = d_NO;
					break;
			}
			break;
		case MSG_TYPE_CANCELLATION:
			switch(uiMsgCode)
			{
				case MSG_CODE_CANCELLATION_VOID:
					inRet = d_OK;
					break;
				case MSG_CODE_CANCELLATION_REFUND:
					inRet = d_OK;
					break;
				default:
					inRet = d_NO;
					break;
			}
			break;
			
		case MSG_TYPE_TOPUP:
			switch(uiMsgCode)
			{
				case MSG_CODE_TOPUP_NCC:
					inRet = d_OK;
					break;
				case MSG_CODE_TOPUP_NFP:
					inRet = d_OK;
					break;
				case MSG_CODE_TOPUP_RSVP:
					inRet = d_OK;
					break;
				default:
					inRet = d_NO;
					break;
			}
			break;
			
		case MSG_TYPE_RECORD:
			inRet = d_NO;
			break;
			
		default:
			inRet = d_NO;
			break;
	}
	
	vdDebug_LogPrintf("uiMsgType[%04X] uiMsgCode[%04X] inRet[%d]", uiMsgType, uiMsgCode, inRet);

	return inRet;
}


/*should no need APPID/HOST ID*/
void vdECR40_LastTransFileName(BYTE *pszFileName)
{
	sprintf(pszFileName, "%s%s", PUBLIC_PATH, ECR_LASTTXN_FILE);
	vdDebug_LogPrintf("vdECR40_LastTransFileName[%s]", pszFileName);
}

int inECRV40_GetLastTransData(void)
{
	int inRet = d_OK;
	BYTE szFileName[64+1];

	vdDebug_LogPrintf("=====inECRV40_GetLastTransData=====");
	
	memset(szECRLastTrans, 0x00, sizeof(szECRLastTrans));
	inECRLastTransLen = 0;

	memset(szFileName, 0x00, sizeof(szFileName));
	vdECR40_LastTransFileName(szFileName);

	inRet = inCTOS_GetFileSize(szFileName);
	inECRLastTransLen = inRet;
	inCTOS_ReadFile(szFileName, szECRLastTrans, inECRLastTransLen);

	if (inRet>0)
	{
		vdECRV40_SetMsgStatus((UINT)V40_SUCCESS);
	}
	else
	{
		vdECRV40_SetMsgStatus((UINT)V40_INVALID_PARAMETER);
		inECRLastTransLen = 0;
	}

	DebugAddHEX("szECRLastTrans", szECRLastTrans, inECRLastTransLen);
	
	return inECRLastTransLen;
}


int inECR40_SaveLastTransData(void)
{
	BYTE szFileName[64];
	UINT uiMsgType = 0;
	UINT uiMsgCode = 0;

	USHORT ushTxnType = 0;
	
	BYTE *puiValue = &uiMsgCode;

	int inRetVal = d_OK;

	vdDebug_LogPrintf("=====inECR40_SaveLastTransData=====");

	puiValue = &uiMsgType;
	memcpy(puiValue, g_stReqMsgHeader.baMsgType, EV4_MSG_TYPE_SIZE);
	vdDebug_LogPrintf("uiMsgType[%08X]", uiMsgType);
	
	puiValue = &uiMsgCode;
	memcpy(puiValue, g_stReqMsgHeader.baMsgCode, EV4_MSG_CODE_SIZE);
	vdDebug_LogPrintf("uiMsgCode[%08X]", uiMsgCode);

	/*we check&handle Settelment first*/
	if (MSG_TYPE_DEVICE == uiMsgType)
	{
		if (MSG_CODE_DEVICE_SETTLEMENT == uiMsgCode)
		{
			inECR40_SaveLastSettleData();
			return d_OK;
		}
	}
	
	/*check if current cmd/trans need to be saved*/
	inRetVal = inECR40_ChkNeedSaveTrans(uiMsgType, uiMsgCode);
	if (inRetVal != d_OK)
		return d_OK;

	memset(szFileName, 0x00, sizeof(szFileName));
	vdECR40_LastTransFileName(szFileName);

	/*Write to file*/
	remove(szFileName);
	CTOS_Delay(50);
	inRetVal = inCTOS_WriteFile(szFileName, szNETSECRSendData, inECRSendDataLen);		

	DebugAddHEX("szNETSECRSendData", szNETSECRSendData, inECRSendDataLen);
	
	return d_OK;
}


int inECR40_FormMessageHeader(BYTE *pszMsg)
{
	int inRet = 0;
	BYTE baHeader[64];
	int inOffset = 0;
	BYTE baRespACK[64+2];

	int inMsgLen = 0;
	int inCalcCRC = 0;
	
	ULONG ulCurrSec = 0;
	long long u64b_CurrSec = 0;
	UINT uinMsgSeq = 0;
	USHORT ushMsgClass = 0;
	UINT uiMsgStatus = 0;
	USHORT ushDeviceProvider = 0;
	USHORT ushDeviceType = 0;
	BYTE byDeviceLocation = 0;


	vdDebug_LogPrintf("=====inECR40_FormMessageHeader=====");


	memset(baHeader, 0x00, sizeof(baHeader));
	inOffset = 0;
	
	/*Length*/
	inMsgLen = 64;
	memcpy(&baHeader[inOffset], &inMsgLen, EV4_MSG_LEGNTH_SIZE);
	inOffset += EV4_MSG_LEGNTH_SIZE;

	/*CRC32*/
	inOffset += 4;

	/*Msg Version*/
	memcpy(&baHeader[inOffset], "\x00", EV4_MSG_VER_SIZE);
	inOffset += EV4_MSG_VER_SIZE;

	/*Msg Direction*/
	memcpy(&baHeader[inOffset], "\x01", EV4_MSG_DIRECTION_SIZE);
	inOffset += EV4_MSG_DIRECTION_SIZE;

	/*Msg Time*/
	ulCurrSec = ulCurrentRTC2UTCSec();
	u64b_CurrSec = (long long)ulCurrSec + (long long)62168515200;
	memcpy(&baHeader[inOffset], &u64b_CurrSec, EV4_MSG_TIME_SIZE);
	inOffset += EV4_MSG_TIME_SIZE;
	DebugAddHEX("u64b_CurrSec", &u64b_CurrSec, EV4_MSG_TIME_SIZE);

	/*Msg Sequence - ACK just echo back the POS Seq num*/
	memcpy(&baHeader[inOffset], g_stReqMsgHeader.baMsgSeq, EV4_MSG_SEQ_SIZE);
	inOffset += EV4_MSG_SEQ_SIZE;

	/*Msg Class*/
	ushMsgClass = MSG_CLASS_RESP;
	memcpy(&baHeader[inOffset], &ushMsgClass, EV4_MSG_CLASS_SIZE);
	inOffset += EV4_MSG_CLASS_SIZE;

	/*Msg Type*/
	memcpy(&baHeader[inOffset], g_stReqMsgHeader.baMsgType, EV4_MSG_TYPE_SIZE);
	inOffset += EV4_MSG_TYPE_SIZE;

	/*Msg Code*/
	memcpy(&baHeader[inOffset], g_stReqMsgHeader.baMsgCode, EV4_MSG_CODE_SIZE);
	inOffset += EV4_MSG_CODE_SIZE;

	/*Msg Completion*/
	memcpy(&baHeader[inOffset], g_stReqMsgHeader.baMsgCompletion, EV4_MSG_COMPLETION_SIZE);
	inOffset += EV4_MSG_COMPLETION_SIZE;

	/*Msg baMsgNotification*/
	memcpy(&baHeader[inOffset], g_stReqMsgHeader.baMsgNotification, EV4_MSG_NOTIFICATION_SIZE);
	inOffset += EV4_MSG_NOTIFICATION_SIZE;

	/*Msg Status*/
	if (0 == strncmp(srTransRec.szRespCode, "00", 2) && V40_PENDING  == uiECRV40_GetMsgStatus())
	{
		vdECRV40_SetMsgStatus((UINT)V40_SUCCESS);
	}

	if (0 == strncmp(srTransRec.szRespCode, "00", 2) && V40_INCORRECT_FLOW  == uiECRV40_GetMsgStatus())
	{
		vdECRV40_SetMsgStatus((UINT)V40_SUCCESS);
	}

	if (0 == strncmp(srTransRec.szRespCode, "00", 2) && V40_CARD_ERROR  == uiECRV40_GetMsgStatus())
	{
		vdECRV40_SetMsgStatus((UINT)V40_CARD_ERROR);
	}
	
	if (2 == strlen(srTransRec.szRespCode) && 0 != strncmp(srTransRec.szRespCode, "00", 2))
	{
		if (0 == strncmp(srTransRec.szRespCode, "UC", 2) ||
			0 == strncmp(srTransRec.szRespCode, "XX", 2))
		{
			// Special Resp Code set by our application. not real host response
		}
		else
		{
			// this would be the host response.
			vdECRV40_SetMsgStatus((UINT)V40_SOF_HOST_RESP_DECLINE);
		}	
	}
	
	uiMsgStatus = uiECRV40_GetMsgStatus();
	memcpy(&baHeader[inOffset], &uiMsgStatus, EV4_MSG_STATUS_SIZE);
	inOffset += EV4_MSG_STATUS_SIZE;

	/*Device Provider*/
	ushDeviceProvider = 0x00;
	memcpy(&baHeader[inOffset], &ushDeviceProvider, EV4_DEVICE_PROVIDER_SIZE);
	inOffset += EV4_DEVICE_PROVIDER_SIZE;

	/*Device Type*/
	ushDeviceType = 0x00;
	memcpy(&baHeader[inOffset], &ushDeviceType, EV4_DEVICE_TYPE_SIZE);
	inOffset += EV4_DEVICE_TYPE_SIZE;
	
	/*Device Location*/
	//byDeviceLocation = 0x00;
	//memcpy(&baHeader[inOffset], &byDeviceLocation, EV4_DEVICE_LOCATION_SIZE);
	//inOffset += EV4_DEVICE_LOCATION_SIZE;

	/*Device Number*/
	memcpy(&baHeader[inOffset], "\x00\x00\x00\x00", EV4_DEVICE_NUM_SIZE);
	inOffset += EV4_DEVICE_NUM_SIZE;

	/*Encryption Algorithm*/
	memcpy(&baHeader[inOffset], g_stReqMsgHeader.baEncryptAlgo, EV4_ENCRYPT_ALGO_SIZE);
	inOffset += EV4_ENCRYPT_ALGO_SIZE;

	/*Encryption Key index*/
	memcpy(&baHeader[inOffset], g_stReqMsgHeader.baEncryptKeyIndex, EV4_ENCRYPT_KEY_IDX_SIZE);
	inOffset += EV4_ENCRYPT_KEY_IDX_SIZE;

	/*Encryption MAC*/
	memcpy(&baHeader[inOffset], g_stReqMsgHeader.baEncryptMAC, EV4_ENCRYPT_MAC_SIZE);
	inOffset += EV4_ENCRYPT_MAC_SIZE;

	/*Reserved*/
	memcpy(&baHeader[inOffset], g_stReqMsgHeader.baReserved, EV4_HEADER_RESERVED_SIZE);
	inOffset += EV4_HEADER_RESERVED_SIZE;
	
	inOffset = 64;
	memcpy(pszMsg, baHeader, 64);
	DebugAddHEX("pszMsg", pszMsg, inOffset);
	
	return inOffset;
}

int inECRV40_PackToken(USHORT ushTokenID, BYTE byTokenType, BYTE byTokenEncode, BYTE *pacVal, int inValLen, BYTE *pacTokenBlk)
{
	UINT uiTokenTotLen = 0;
	int inOffset = 0;
	BYTE baTemp[2048];

	USHORT ushID;
	BYTE byType;
	BYTE byEncode;

	vdDebug_LogPrintf("inECRV40_PackToken ushTokenID[%02X]", ushTokenID);

	memset(baTemp, 0x00, sizeof(baTemp));

	inOffset = 0;
	uiTokenTotLen = inValLen+TOKEN_HEADER_SIZE;
	memcpy(&baTemp[inOffset], &uiTokenTotLen, TOKEN_BLOCK_LENGTH_SIZE);
	inOffset += TOKEN_BLOCK_LENGTH_SIZE;

	ushID = ushTokenID;
	memcpy(&baTemp[inOffset], &ushID, TOKEN_ID_SIZE);
	inOffset += TOKEN_ID_SIZE;
	
	byType = byTokenType;
	memcpy(&baTemp[inOffset], &byType, TOKEN_TYPE_SIZE);
	inOffset += TOKEN_TYPE_SIZE;
	
	byEncode = byTokenEncode;
	memcpy(&baTemp[inOffset], &byEncode, TOKEN_ENCODE_SIZE);
	inOffset += TOKEN_ENCODE_SIZE;

	memcpy(&baTemp[inOffset], pacVal, inValLen);
	inOffset += inValLen;

	memcpy(pacTokenBlk, baTemp, inOffset);

	DebugAddHEX("pacTokenBlk", pacTokenBlk, inOffset);

	return inOffset;
}


int inECRV40_PackPurchaseResponse(BYTE *pszRespMsg)
{
	BYTE szTemp[512];
	BYTE szBodyResp[4096];
	BYTE szLenBCD[2];
	BYTE szLenStr[4+1];
	int inOffset = 0;

	int inValLen = 0;

	UINT uiIntVal = 0;
	BYTE baIntValBuf[4];

	USHORT ushShortVal = 0;
	BYTE baShortValBuf[2];

	int inPaddingLen = 0;
	int inMod = 0;
	BYTE baPadding[64];

	USHORT ushTxnType = 0;
	BYTE *puiValue = &ushTxnType;

	vdDebug_LogPrintf("=====inECRV40_PackPurchaseResponse=====");

	memset(szBodyResp, 0x00, sizeof(szBodyResp));
	inOffset = 0;

	/*If Payment Auto will covert baTxnType as current txn type*/
	puiValue = &ushTxnType;
	memcpy(puiValue, g_stReqPayload.baTxnType, USHORT_BYTES2_SIZE);
	if (TXN_TYPE_PAY_AUTO == ushTxnType)
	{
		ushTxnType = TXN_TYPE_PAY_SCHEME_CREDIT;
		memcpy(g_stReqPayload.baTxnType, &ushTxnType, USHORT_BYTES2_SIZE);
	}

	/*ID_TXN_TYPE*/
	inValLen = USHORT_BYTES2_SIZE;
 	inOffset += inECRV40_PackToken(ID_TXN_TYPE, TOKEN_TYPE_VALUE, ENCODING_VALUE_HEX_LITTLE, 
		g_stReqPayload.baTxnType, inValLen, &szBodyResp[inOffset]);

	/*ID_TXN_AMOUNT*/
	inValLen = UINT_BYTES4_SIZE;
 	inOffset += inECRV40_PackToken(ID_TXN_AMOUNT, TOKEN_TYPE_VALUE, ENCODING_VALUE_HEX_LITTLE, 
		g_stReqPayload.baTxnAmt, inValLen, &szBodyResp[inOffset]);

	/*ID_TXN_DATE*/
	inECRGetDate();
	inValLen = strlen(stNetECRRec.szECRDate);
	inOffset += inECRV40_PackToken(ID_TXN_DATE, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		stNetECRRec.szECRDate, inValLen, &szBodyResp[inOffset]);

	/*ID_TXN_TIME*/
	inECRGetTime();
	inValLen = strlen(stNetECRRec.szECRTime);
	inOffset += inECRV40_PackToken(ID_TXN_TIME, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		stNetECRRec.szECRTime, inValLen, &szBodyResp[inOffset]);

	/*ID_TXN_STAN*/
	inECRGetSTAN();
	inValLen = strlen(stNetECRRec.szECRStan);
	inOffset += inECRV40_PackToken(ID_TXN_STAN, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		stNetECRRec.szECRStan, inValLen, &szBodyResp[inOffset]);

	/*ID_TXN_MER_REF_NUM*/
	inValLen = strlen(g_stReqPayload.szMerchRefNum);
	inOffset += inECRV40_PackToken(ID_TXN_MER_REF_NUM, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		g_stReqPayload.szMerchRefNum, inValLen, &szBodyResp[inOffset]);

	/*ID_TXN_RESPONSE_TEXT*/
	inECRGetResponseText();
	inValLen = strlen(stNetECRRec.szECRRespText);
	inOffset += inECRV40_PackToken(ID_TXN_RESPONSE_TEXT, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		stNetECRRec.szECRRespText, inValLen, &szBodyResp[inOffset]);

	/*ID_TXN_MER_NAME*/
	inECRGetMerchantName();
	inValLen = strlen(stNetECRRec.szECRMerchantName);
	inOffset += inECRV40_PackToken(ID_TXN_MER_NAME, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		stNetECRRec.szECRMerchantName, inValLen, &szBodyResp[inOffset]);

	/*ID_TXN_MER_ADDRESS*/
	inECRGetMerchantAddress();
	inValLen = strlen(stNetECRRec.szECRMerchantName);
	inOffset += inECRV40_PackToken(ID_TXN_MER_ADDRESS, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		stNetECRRec.szECRMerchantName, inValLen, &szBodyResp[inOffset]);

	/*ID_TXN_TID*/
	inECRGetTID();
	inValLen = strlen(stNetECRRec.szECRTID);
	inOffset += inECRV40_PackToken(ID_TXN_TID, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		stNetECRRec.szECRTID, inValLen, &szBodyResp[inOffset]);

	/*ID_TXN_MID*/
	inECRGetMID();
	inValLen = strlen(stNetECRRec.szECRMID);
	inOffset += inECRV40_PackToken(ID_TXN_MID, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		stNetECRRec.szECRMID, inValLen, &szBodyResp[inOffset]);

	/*ID_TXN_APPROV_CODE*/
	inECRGetAuthCode();
	inValLen = strlen(stNetECRRec.szECRAuthCode);
	inOffset += inECRV40_PackToken(ID_TXN_APPROV_CODE, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		stNetECRRec.szECRAuthCode, inValLen, &szBodyResp[inOffset]);

	/*ID_TXN_RRN*/
	inECRGetRRefNo();
	inValLen = strlen(stNetECRRec.szECRRRef);
	inOffset += inECRV40_PackToken(ID_TXN_RRN, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		stNetECRRec.szECRRRef, inValLen, &szBodyResp[inOffset]);

	/*ID_TXN_CARD_NAME*/
	//inECRGetCardName();
	inValLen = strlen(srTransRec.stEMVinfo.szChipLabel);
	inOffset += inECRV40_PackToken(ID_TXN_CARD_NAME, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		srTransRec.stEMVinfo.szChipLabel, inValLen, &szBodyResp[inOffset]);

	/*ID_TXN_HOST_RESP_CODE*/
	inECRGetHostResponseCode();
	inValLen = strlen(stNetECRRec.szECRHostRespCode);
	inOffset += inECRV40_PackToken(ID_TXN_HOST_RESP_CODE, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		stNetECRRec.szECRHostRespCode, inValLen, &szBodyResp[inOffset]);
	
	/*ID_TXN_CARD_ENTRY_MODE*/
	vdECR40_GetCardEntryMode();
	inValLen = 1;
	inOffset += inECRV40_PackToken(ID_TXN_CARD_ENTRY_MODE, TOKEN_TYPE_ARRAY, ENCODING_VALUE_HEX_LITTLE, 
		stNetECRRec.szECRCardEntryMode, inValLen, &szBodyResp[inOffset]);

	/*ID_TXN_INV_NUM*/
	/*Here for NPX void we use STAN number for void, 
	so here we just send back STAN as Inv num.*/
	//inECRGetSTAN();
	// -- 20190619, here we change to use Invoice number
	vdDebug_LogPrintf("inGetECRVoidNumType[%d]", inGetECRVoidNumType());
	if (ECR_VOID_BY_STAN == inGetECRVoidNumType())
		inECRGetSTAN();
	else
		inECRGetInvNum();
	inValLen = strlen(stNetECRRec.szECRStan);
	inOffset += inECRV40_PackToken(ID_TXN_INV_NUM, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		stNetECRRec.szECRStan, inValLen, &szBodyResp[inOffset]);

	/*ID_CARD_NUM_MASKED*/
	inECRGetCardNumber();
	inValLen = strlen(stNetECRRec.szECRCardNumber);
	inOffset += inECRV40_PackToken(ID_CARD_NUM_MASKED, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		stNetECRRec.szECRCardNumber, inValLen, &szBodyResp[inOffset]);

	/*ID_CARD_EXPIRY*/
	inECRGetExpiryDate();
	inValLen = strlen(stNetECRRec.szECRExpiryDate);
	inOffset += inECRV40_PackToken(ID_CARD_EXPIRY, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		stNetECRRec.szECRExpiryDate, inValLen, &szBodyResp[inOffset]);

	/*ID_TXN_BATCH???? ARRAY_HEX?????*/
	inECRGetBatchNum();
	inValLen = strlen(stNetECRRec.szECRBatchNum);
	inOffset += inECRV40_PackToken(ID_TXN_BATCH, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		stNetECRRec.szECRBatchNum, inValLen, &szBodyResp[inOffset]);

	/*ID_CARD_HOLDER_NAME*/
	inValLen = strlen(srTransRec.szCardholderName);
	inOffset += inECRV40_PackToken(ID_CARD_HOLDER_NAME, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		srTransRec.szCardholderName, inValLen, &szBodyResp[inOffset]);

	/*ID_CARD_SCHEME_NAME*/
	inECRGetCardSchemeName();
	inValLen = strlen(stNetECRRec.szECRCardName);
	inOffset += inECRV40_PackToken(ID_CARD_SCHEME_NAME, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		stNetECRRec.szECRCardName, inValLen, &szBodyResp[inOffset]);
	
	/*ID_CARD_AID*/
	inValLen = srTransRec.stEMVinfo.T84_len;
	inOffset += inECRV40_PackToken(ID_CARD_AID, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_HEX, 
		srTransRec.stEMVinfo.T84, inValLen, &szBodyResp[inOffset]);

	/*ID_TXN_TC*/
	inValLen = 8;
	inOffset += inECRV40_PackToken(ID_TXN_TC, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_HEX, 
		srTransRec.stEMVinfo.T9F26, inValLen, &szBodyResp[inOffset]);
	
	/*ID_FOREIGN_AMOUNT*/

	/*ID_FOREIGN_MID*/

	/*ID_FOREIGN_CUR_NAME*/

	/*ID_POSID*/
	inValLen = strlen(strGCT.szPOSid);
	inOffset += inECRV40_PackToken(ID_TXN_POSID, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		strGCT.szPOSid, inValLen, &szBodyResp[inOffset]);

	/*ID_TXN_HOST*/
	inValLen = strlen(EV4_HOST_ID_UPOS);
	inOffset += inECRV40_PackToken(ID_TXN_HOST, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		EV4_HOST_ID_UPOS, inValLen, &szBodyResp[inOffset]);
	
	/*ID_TXN_RECEIPT*/
	if (VS_TRUE == byECRCheckNeedRcptText())
	{
	inValLen = strlen(g_szECRRcptText);
	vdDebug_LogPrintf("g_szECRRcptText inValLen[%d]", inValLen);
	//inValLen = inValLen/2;
	inOffset += inECRV40_PackToken(ID_TXN_RECEIPT, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		g_szECRRcptText, inValLen, &szBodyResp[inOffset]);
	}
	
	
	/*ID_PADDING*/
	inMod = inOffset%PAYLOAD_PADDING_SIZE;
	if (inMod>23)
		inPaddingLen = PAYLOAD_PADDING_SIZE*2 - inMod - TOKEN_HEADER_SIZE;
	else
		inPaddingLen = PAYLOAD_PADDING_SIZE - inMod - TOKEN_HEADER_SIZE;
	vdDebug_LogPrintf("inOffset[%d] inMod[%d] inPaddingLen[%d]", inOffset, inMod, inPaddingLen);
	memset(baPadding, 0x00, sizeof(baPadding));
	inValLen = inPaddingLen;
	inOffset += inECRV40_PackToken(ID_PADDING, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_HEX, 
		baPadding, inValLen, &szBodyResp[inOffset]);
	
	memcpy(pszRespMsg, szBodyResp, inOffset);

	return inOffset;
	
}

int inECRV40_PackVoidResponse(BYTE *pszRespMsg)
{
	BYTE szTemp[512];
	BYTE szBodyResp[4096];
	BYTE szLenBCD[2];
	BYTE szLenStr[4+1];
	int inOffset = 0;

	int inValLen = 0;

	UINT uiIntVal = 0;
	BYTE baIntValBuf[4];

	USHORT ushShortVal = 0;
	BYTE baShortValBuf[2];

	int inPaddingLen = 0;
	int inMod = 0;
	BYTE baPadding[64];

	vdDebug_LogPrintf("=====inECRV40_PackVoidResponse=====");

	memset(szBodyResp, 0x00, sizeof(szBodyResp));
	inOffset = 0;

	#if 0
	/*ID_TXN_TYPE*/
	inValLen = USHORT_BYTES2_SIZE;
 	inOffset += inECRV40_PackToken(ID_TXN_TYPE, TOKEN_TYPE_VALUE, ENCODING_VALUE_HEX_LITTLE, 
		g_stReqPayload.baTxnType, inValLen, &szBodyResp[inOffset]);
	//vdDebug_LogPrintf("ID_TXN_TYPE inOffset=[%d]",inOffset);
	#endif
	
	/*ID_TXN_AMOUNT*/
	inECR40_GetVoidTxnAmt();
	inValLen = UINT_BYTES4_SIZE;
 	inOffset += inECRV40_PackToken(ID_TXN_AMOUNT, TOKEN_TYPE_VALUE, ENCODING_VALUE_HEX_LITTLE, 
		stNetECRV40RespRec.baTxnAmt, inValLen, &szBodyResp[inOffset]);
	vdDebug_LogPrintf("ID_TXN_AMOUNT inOffset=[%d]",inOffset);
	
	/*ID_TXN_DATE*/
	inECR40_GetTxnDate();
	inValLen = strlen(stNetECRV40RespRec.szTxnDate);
	inOffset += inECRV40_PackToken(ID_TXN_DATE, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		stNetECRV40RespRec.szTxnDate, inValLen, &szBodyResp[inOffset]);
	vdDebug_LogPrintf("ID_TXN_DATE inOffset=[%d]",inOffset);
	
	/*ID_TXN_TIME*/
	inECRGetTime();
	inValLen = strlen(stNetECRRec.szECRTime);
	inOffset += inECRV40_PackToken(ID_TXN_TIME, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		stNetECRRec.szECRTime, inValLen, &szBodyResp[inOffset]);
	vdDebug_LogPrintf("ID_TXN_TIME inOffset=[%d]",inOffset);
	
	/*ID_TXN_STAN*/
	inECRGetSTAN();
	inValLen = strlen(stNetECRRec.szECRStan);
	inOffset += inECRV40_PackToken(ID_TXN_STAN, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		stNetECRRec.szECRStan, inValLen, &szBodyResp[inOffset]);
	vdDebug_LogPrintf("ID_TXN_STAN inOffset=[%d]",inOffset);

	/*ID_TXN_MER_REF_NUM*/
	inValLen = strlen(g_stReqPayload.szMerchRefNum);
	inOffset += inECRV40_PackToken(ID_TXN_MER_REF_NUM, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		g_stReqPayload.szMerchRefNum, inValLen, &szBodyResp[inOffset]);

	/*ID_TXN_RESPONSE_TEXT*/
	inECRGetResponseText();
	inValLen = strlen(stNetECRRec.szECRRespText);
	inOffset += inECRV40_PackToken(ID_TXN_RESPONSE_TEXT, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		stNetECRRec.szECRRespText, inValLen, &szBodyResp[inOffset]);
	vdDebug_LogPrintf("ID_TXN_RESPONSE_TEXT inOffset=[%d]",inOffset);
	
	/*ID_TXN_MER_NAME*/
	inECRGetMerchantName();
	inValLen = strlen(stNetECRRec.szECRMerchantName);
	inOffset += inECRV40_PackToken(ID_TXN_MER_NAME, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		stNetECRRec.szECRMerchantName, inValLen, &szBodyResp[inOffset]);
	vdDebug_LogPrintf("ID_TXN_MER_NAME inOffset=[%d]",inOffset);
	
	/*ID_TXN_MER_ADDRESS*/
	inECRGetMerchantAddress();
	inValLen = strlen(stNetECRRec.szECRMerchantName);
	inOffset += inECRV40_PackToken(ID_TXN_MER_ADDRESS, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		stNetECRRec.szECRMerchantName, inValLen, &szBodyResp[inOffset]);
	vdDebug_LogPrintf("ID_TXN_MER_ADDRESS inOffset=[%d]",inOffset);
	
	/*ID_TXN_TID*/
	inECRGetTID();
	inValLen = strlen(stNetECRRec.szECRTID);
	inOffset += inECRV40_PackToken(ID_TXN_TID, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		stNetECRRec.szECRTID, inValLen, &szBodyResp[inOffset]);
	vdDebug_LogPrintf("ID_TXN_TID inOffset=[%d]",inOffset);
	
	/*ID_TXN_MID*/
	inECRGetMID();
	inValLen = strlen(stNetECRRec.szECRMID);
	inOffset += inECRV40_PackToken(ID_TXN_MID, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		stNetECRRec.szECRMID, inValLen, &szBodyResp[inOffset]);
	vdDebug_LogPrintf("ID_TXN_MID inOffset=[%d]",inOffset);
	
	/*ID_TXN_APPROV_CODE*/
	inECRGetAuthCode();
	inValLen = strlen(stNetECRRec.szECRAuthCode);
	inOffset += inECRV40_PackToken(ID_TXN_APPROV_CODE, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		stNetECRRec.szECRAuthCode, inValLen, &szBodyResp[inOffset]);
	vdDebug_LogPrintf("ID_TXN_APPROV_CODE inOffset=[%d]",inOffset);
	
	/*ID_TXN_RRN*/
	inECRGetRRefNo();
	inValLen = strlen(stNetECRRec.szECRRRef);
	inOffset += inECRV40_PackToken(ID_TXN_RRN, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		stNetECRRec.szECRRRef, inValLen, &szBodyResp[inOffset]);
	vdDebug_LogPrintf("ID_TXN_RRN inOffset=[%d]",inOffset);

	/*ID_TXN_CARD_NAME*/
	inECRGetCardName();
	inValLen = strlen(stNetECRRec.szECRCardName);
	inOffset += inECRV40_PackToken(ID_TXN_CARD_NAME, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		stNetECRRec.szECRCardName, inValLen, &szBodyResp[inOffset]);
	vdDebug_LogPrintf("ID_TXN_CARD_NAME inOffset=[%d]",inOffset);

	/*ID_TXN_HOST_RESP_CODE*/
	inECRGetHostResponseCode();
	inValLen = strlen(stNetECRRec.szECRHostRespCode);
	inOffset += inECRV40_PackToken(ID_TXN_HOST_RESP_CODE, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		stNetECRRec.szECRHostRespCode, inValLen, &szBodyResp[inOffset]);
	vdDebug_LogPrintf("ID_TXN_HOST_RESP_CODE inOffset=[%d]",inOffset);
	
	/*ID_TXN_CARD_ENTRY_MODE*/
	vdECR40_GetCardEntryMode();
	inValLen = 1;
	inOffset += inECRV40_PackToken(ID_TXN_CARD_ENTRY_MODE, TOKEN_TYPE_ARRAY, ENCODING_VALUE_HEX_LITTLE, 
		stNetECRRec.szECRCardEntryMode, inValLen, &szBodyResp[inOffset]);
	vdDebug_LogPrintf("ID_TXN_CARD_ENTRY_MODE inOffset=[%d]",inOffset);
	
	/*ID_TXN_RECEIPT*/
	if (VS_TRUE == byECRCheckNeedRcptText())
	{
	inValLen = strlen(g_szECRRcptText);
	vdDebug_LogPrintf("g_szECRRcptText inValLen[%d]", inValLen);
	//inValLen = inValLen/2;
	inOffset += inECRV40_PackToken(ID_TXN_RECEIPT, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		g_szECRRcptText, inValLen, &szBodyResp[inOffset]);
	vdDebug_LogPrintf("ID_TXN_RECEIPT inOffset=[%d]",inOffset);
	}

	#if 0
	/*ID_TXN_FOREIGN_MID*/
	inECRV40_GetForeignMID();
	inValLen = strlen(stNetECRV40RespRec.szForeignMID);
	inOffset += inECRV40_PackToken(ID_TXN_FOREIGN_MID, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		stNetECRV40RespRec.szForeignMID, inValLen, &szBodyResp[inOffset]);

	/*ID_TXN_FOREIGN_AMOUNT*/
	inECRV40_GetForeignAmt();
	uiIntVal = atoi(stNetECRV40RespRec.szForeignAmt);
	inValLen = UINT_BYTES4_SIZE;
 	inOffset += inECRV40_PackToken(ID_TXN_FOREIGN_AMOUNT, TOKEN_TYPE_VALUE, ENCODING_VALUE_HEX_LITTLE, 
		&uiIntVal, inValLen, &szBodyResp[inOffset]);
	#endif

	/*ID_TXN_INV_NUM*/
	/*Here for NPX void we use STAN number for void, so here we just send back STAN as Inv num*/
	inECRGetSTAN();
	inValLen = strlen(stNetECRRec.szECRStan);
	inOffset += inECRV40_PackToken(ID_TXN_INV_NUM, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		stNetECRRec.szECRStan, inValLen, &szBodyResp[inOffset]);

	/*ID_TXN_HOST*/
	inValLen = strlen(EV4_HOST_ID_UPOS);
	inOffset += inECRV40_PackToken(ID_TXN_HOST, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		EV4_HOST_ID_UPOS, inValLen, &szBodyResp[inOffset]);
	
	/*ID_PADDING*/
	inMod = inOffset%PAYLOAD_PADDING_SIZE;
	if (inMod>23)
		inPaddingLen = PAYLOAD_PADDING_SIZE*2 - inMod - TOKEN_HEADER_SIZE;
	else
		inPaddingLen = PAYLOAD_PADDING_SIZE - inMod - TOKEN_HEADER_SIZE;
	vdDebug_LogPrintf("inOffset[%d] inMod[%d] inPaddingLen[%d]", inOffset, inMod, inPaddingLen);
	memset(baPadding, 0x00, sizeof(baPadding));
	inValLen = inPaddingLen;
	inOffset += inECRV40_PackToken(ID_PADDING, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_HEX, 
		baPadding, inValLen, &szBodyResp[inOffset]);

	vdDebug_LogPrintf("ID_PADDING inOffset=[%d]",inOffset);
	memcpy(pszRespMsg, szBodyResp, inOffset);

	return inOffset;
	
}


int inECRV40_PackCreditTotalInfo(BYTE *pstTotal)
{
	BYTE szTemp[512];
	BYTE szBodyResp[1024];
	BYTE szLenBCD[2];
	BYTE szLenStr[4+1];
	int inOffset = 0;

	int inValLen = 0;

	UINT uiIntVal = 0;
	BYTE baIntValBuf[4];

	vdDebug_LogPrintf("=====inECRV40_PackCreditTotalInfo=====");

	memset(szBodyResp, 0x00, sizeof(szBodyResp));
	inOffset = 0;

	/*ID_SOF_ACQUIRER*/
	//vdECR40_GetSOFAcquirer();
 	inValLen = strlen(stNetECRV40RespRec.szSOFACQ);
 	inOffset += inECRV40_PackToken(ID_SOF_ACQUIRER, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		stNetECRV40RespRec.szSOFACQ, inValLen, &szBodyResp[inOffset]);

	/*ID_SOF_NAME*/
	//vdECR40_GetSOFName();
	inValLen = strlen(stNetECRV40RespRec.szSOFName);
 	inOffset += inECRV40_PackToken(ID_SOF_NAME, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		stNetECRV40RespRec.szSOFName, inValLen, &szBodyResp[inOffset]);

	/*ID_SOF_TID*/
	inValLen = strlen(g_szNPXAcqTID);
	inOffset += inECRV40_PackToken(ID_SOF_TID, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		g_szNPXAcqTID, inValLen, &szBodyResp[inOffset]);

	/*ID_SOF_MID*/
	inValLen = strlen(g_szNPXAcqMID);
	inOffset += inECRV40_PackToken(ID_SOF_MID, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_ASCII, 
		g_szNPXAcqMID, inValLen, &szBodyResp[inOffset]);

	/*ID_SOF_SALE_COUNT*/
	vdDebug_LogPrintf("ulPurchaseCnt=[%ld]",gstECRTermTotal.ulPurchaseCnt);
	uiIntVal = gstECRTermTotal.ulPurchaseCnt;
	memset(baIntValBuf, 0x00, sizeof(baIntValBuf));
	memcpy(baIntValBuf, &uiIntVal, sizeof(UINT));
	inValLen = sizeof(UINT);
 	inOffset += inECRV40_PackToken(ID_SOF_SALE_COUNT, TOKEN_TYPE_VALUE, ENCODING_VALUE_HEX_LITTLE, 
		baIntValBuf, inValLen, &szBodyResp[inOffset]);

	/*ID_SOF_SALE_TOTAL*/
	vdDebug_LogPrintf("ulPurchaseAmt=[%ld]",gstECRTermTotal.ulPurchaseAmt);
	uiIntVal = gstECRTermTotal.ulPurchaseAmt;
	memset(baIntValBuf, 0x00, sizeof(baIntValBuf));
	memcpy(baIntValBuf, &uiIntVal, sizeof(UINT));
	inValLen = sizeof(UINT);
 	inOffset += inECRV40_PackToken(ID_SOF_SALE_TOTAL, TOKEN_TYPE_VALUE, ENCODING_VALUE_HEX_LITTLE, 
		baIntValBuf, inValLen, &szBodyResp[inOffset]);

	/*ID_SOF_REFUND_COUNT*/
	vdDebug_LogPrintf("ulRefundCnt=[%ld]",gstECRTermTotal.ulRefundCnt);
	uiIntVal = gstECRTermTotal.ulRefundCnt;
	memset(baIntValBuf, 0x00, sizeof(baIntValBuf));
	memcpy(baIntValBuf, &uiIntVal, sizeof(UINT));
	inValLen = sizeof(UINT);
 	inOffset += inECRV40_PackToken(ID_SOF_REFUND_COUNT, TOKEN_TYPE_VALUE, ENCODING_VALUE_HEX_LITTLE, 
		baIntValBuf, inValLen, &szBodyResp[inOffset]);

	/*ID_SOF_REFUND_TOTAL*/
	vdDebug_LogPrintf("ulRefundAmt=[%ld]",gstECRTermTotal.ulRefundAmt);
	uiIntVal = gstECRTermTotal.ulRefundAmt;
	memset(baIntValBuf, 0x00, sizeof(baIntValBuf));
	memcpy(baIntValBuf, &uiIntVal, sizeof(UINT));
	inValLen = sizeof(UINT);
 	inOffset += inECRV40_PackToken(ID_SOF_REFUND_TOTAL, TOKEN_TYPE_VALUE, ENCODING_VALUE_HEX_LITTLE, 
		baIntValBuf, inValLen, &szBodyResp[inOffset]);

	/*ID_SOF_VOID_COUNT*/
	vdDebug_LogPrintf("ulVoidCnt=[%ld]",gstECRTermTotal.ulVoidCnt);
	uiIntVal = gstECRTermTotal.ulVoidCnt;
	memset(baIntValBuf, 0x00, sizeof(baIntValBuf));
	memcpy(baIntValBuf, &uiIntVal, sizeof(UINT));
	inValLen = sizeof(UINT);
 	inOffset += inECRV40_PackToken(ID_SOF_VOID_COUNT, TOKEN_TYPE_VALUE, ENCODING_VALUE_HEX_LITTLE, 
		baIntValBuf, inValLen, &szBodyResp[inOffset]);

	/*ID_SOF_VOID_TOTAL*/
	vdDebug_LogPrintf("ulVoidAmt=[%ld]",gstECRTermTotal.ulVoidAmt);
	uiIntVal = gstECRTermTotal.ulVoidAmt;
	memset(baIntValBuf, 0x00, sizeof(baIntValBuf));
	memcpy(baIntValBuf, &uiIntVal, sizeof(UINT));
	inValLen = sizeof(UINT);
 	inOffset += inECRV40_PackToken(ID_SOF_VOID_TOTAL, TOKEN_TYPE_VALUE, ENCODING_VALUE_HEX_LITTLE, 
		baIntValBuf, inValLen, &szBodyResp[inOffset]);

	memcpy(pstTotal, szBodyResp, inOffset);
	DebugAddHEX("pstTotal", pstTotal, inOffset);

	return inOffset;
}


int inECRV40_PackSettleResponse(BYTE *pszRespMsg)
{
	BYTE szTemp[512];
	BYTE szBodyResp[1024];
	BYTE szLenBCD[2];
	BYTE szLenStr[4+1];
	int inOffset = 0;

	UINT uiTokenTotLen = 0;

	int inValLen = 0;

	int inPaddingLen = 0;
	int inMod = 0;
	BYTE baPadding[64];

	int inIdx = 0;
	int inTotalCardType = 0;

	vdDebug_LogPrintf("=====inECRV40_PackSettleResponse=====");

	memset(szBodyResp, 0x00, sizeof(szBodyResp));
	inOffset = 0;

	memset(&gstECRTermTotal, 0x00, sizeof(gstECRTermTotal));
	inECR40_GetSettleSOFAcquirer();

	inTotalCardType = inECR40_GetSettleCardTotal();
	vdDebug_LogPrintf("inTotalCardType[%d]", inTotalCardType);
	
	for (inIdx=0; inIdx<inTotalCardType; inIdx++)
	{
		vdDebug_LogPrintf("stNetsECRV40CardTotal[%d].szCardName[%s]", inIdx, stNetsECRV40CardTotal[inIdx].szCardName);
		strcpy(stNetECRV40RespRec.szSOFName, stNetsECRV40CardTotal[inIdx].szCardName);

		/*NPX Acq TID/MID*/
		memset(g_szNPXAcqTID, 0x00, sizeof(g_szNPXAcqTID));
		memset(g_szNPXAcqMID, 0x00, sizeof(g_szNPXAcqMID));

		memcpy(g_szNPXAcqTID, stNetsECRV40CardTotal[inIdx].szTID, 8);
		memcpy(g_szNPXAcqMID, stNetsECRV40CardTotal[inIdx].szMID, 15);

		gstECRTermTotal.ulPurchaseCnt = stNetsECRV40CardTotal[inIdx].ulSaleCnt;
		gstECRTermTotal.ulPurchaseAmt = stNetsECRV40CardTotal[inIdx].ulSaleAmt;

		gstECRTermTotal.ulVoidCnt = stNetsECRV40CardTotal[inIdx].ulVoidCnt;
		gstECRTermTotal.ulVoidAmt = stNetsECRV40CardTotal[inIdx].ulVoidAmt;
		
		gstECRTermTotal.ulRefundCnt = stNetsECRV40CardTotal[inIdx].ulRefundCnt;
		gstECRTermTotal.ulRefundAmt = stNetsECRV40CardTotal[inIdx].ulRefundAmt;
		
		inOffset += inECRV40_PackCreditTotalInfo(&szBodyResp[inOffset]);
	}
		
	/*ID_PADDING*/
	inMod = inOffset%PAYLOAD_PADDING_SIZE;
	if (inMod>23)
		inPaddingLen = PAYLOAD_PADDING_SIZE*2 - inMod - TOKEN_HEADER_SIZE;
	else
		inPaddingLen = PAYLOAD_PADDING_SIZE - inMod - TOKEN_HEADER_SIZE;
	vdDebug_LogPrintf("inOffset[%d] inMod[%d] inPaddingLen[%d]", inOffset, inMod, inPaddingLen);
	memset(baPadding, 0x00, sizeof(baPadding));
	inValLen = inPaddingLen;
	inOffset += inECRV40_PackToken(ID_PADDING, TOKEN_TYPE_ARRAY, ENCODING_ARRAY_HEX, 
		baPadding, inValLen, &szBodyResp[inOffset]);
	
	memcpy(pszRespMsg, szBodyResp, inOffset);

	return inOffset;
	
}




int inECR40_FormPayloadBody(BYTE *pszBody)
{
	int inOffset = 0;
		
	int inRet = d_OK;

	UINT uiMsgType = 0;
	UINT uiMsgCode = 0;
	
	BYTE *puiValue = &uiMsgCode;

	vdDebug_LogPrintf("=====inECR40_FormPayloadBody=====");

	puiValue = &uiMsgType;
	memcpy(puiValue, g_stReqMsgHeader.baMsgType, EV4_MSG_TYPE_SIZE);
	vdDebug_LogPrintf("uiMsgType[%08X]", uiMsgType);
	
	puiValue = &uiMsgCode;
	memcpy(puiValue, g_stReqMsgHeader.baMsgCode, EV4_MSG_CODE_SIZE);
	vdDebug_LogPrintf("uiMsgCode[%08X]", uiMsgCode);

	switch (uiMsgType)
	{	
		case MSG_TYPE_DEVICE:
			switch(uiMsgCode)
			{
				case MSG_CODE_DEVICE_STATUS:
					
					break;
				case MSG_CODE_DEVICE_RESET:
				case MSG_CODE_DEVICE_TIME_SYNC:
				case MSG_CODE_DEVICE_PROFILE:
				case MSG_CODE_DEVICE_SOF_DETAIL:
				case MSG_CODE_DEVICE_SOF_PRIORISATION:
					break;
				case MSG_CODE_DEVICE_LOGON:
					//inOffset = inECRV40_PackLogonResponse(pszBody);
					break;
				case MSG_CODE_DEVICE_TMS:
					
					break;
				case MSG_CODE_DEVICE_SETTLEMENT:
					inOffset = inECRV40_PackSettleResponse(pszBody);
					break;
				case MSG_CODE_DEVICE_PRE_SETTLEMENT:
                    inOffset = inECRV40_PackSettleResponse(pszBody);
					break;
				default:
					inOffset = 0;
					break;
			}
			break;
			
		case MSG_TYPE_AUTH:
			switch(uiMsgCode)
			{
				case MSG_CODE_AUTH_MUTUAL_STEP_1:
				case MSG_CODE_AUTH_MUTUAL_STEP_2:
                	inOffset = 0;
					break;
				default:
					inOffset = 0;
					break;
			}
			break;
			
		case MSG_TYPE_CARD:
			switch(uiMsgCode)
			{
				case MSG_CODE_CARD_DETECT:
				case MSG_CODE_CARD_READ_PURSE:
				case MSG_CODE_CARD_READ_HISTORICAL_LOG:
				case MSG_CODE_CARD_READ_RSVP:
					inOffset = 0;
					break;
				default:
					inOffset = 0;
					break;
			}
			break;

		case MSG_TYPE_PAYMENT:
			switch(uiMsgCode)
			{
				case MSG_CODE_PAYMENT_AUTO:
					inOffset = inECRV40_PackPurchaseResponse(pszBody);
					break;
				case MSG_CODE_PAYMENT_EFT:
					inOffset = inECRV40_PackPurchaseResponse(pszBody);
					break;
				case MSG_CODE_PAYMENT_NCC:
					inOffset = 0;
					break;
				case MSG_CODE_PAYMENT_NFP:
					inOffset = inECRV40_PackPurchaseResponse(pszBody);
					break;
				case MSG_CODE_PAYMENT_RSVP:
					inOffset = 0;
					break;
				case MSG_CODE_PAYMENT_CRD:
					inOffset = inECRV40_PackPurchaseResponse(pszBody);
					break;
				case MSG_CODE_PAYMENT_DEB:
					inOffset = 0;
					break;
				case MSG_CODE_PAYMENT_BCA:
					inOffset = 0;
					break;
				default:
					inOffset = 0;
					break;
			}
			break;

		case MSG_TYPE_CANCELLATION:
			switch(uiMsgCode)
			{
				case MSG_CODE_CANCELLATION_VOID:
					inOffset = inECRV40_PackVoidResponse(pszBody);
					break;
				case MSG_CODE_CANCELLATION_REFUND:
					inOffset = inECRV40_PackVoidResponse(pszBody);
					break;
				default:
					inOffset = 0;
					break;
			}
			break;
			
		case MSG_TYPE_TOPUP:
			switch(uiMsgCode)
			{
				case MSG_CODE_TOPUP_NCC:
				case MSG_CODE_TOPUP_NFP:
				case MSG_CODE_TOPUP_RSVP:
					inOffset = 0;
					break;
				default:
					inOffset = 0;
					break;
			}
			break;
			
		case MSG_TYPE_RECORD:
			switch(uiMsgCode)
			{
				case MSG_CODE_RECORD_SUMMARY:
				case MSG_CODE_RECORD_UPLOAD:
				case MSG_CODE_RECORD_CLEAR:
					inOffset = 0;
					break;
				default:
					inOffset = 0;
					break;
			}
			break;

		default:
			inOffset = 0;
			break;
	}

	DebugAddHEX("TOKENS BODY ", pszBody, inOffset);
	
	return inOffset;
}


int inSubApECRV40DataInit(void)
{
	vdSetECRTransactionFlg(VS_TRUE);

	 // must make sure NETS call Stop ECR, 
	 //here NPX no need call stop but need set status and call resume before send msg back
	//inNPX_HoldECRApp(1);
	vdSetECRAppHoldingStatus(1);
	
	memset(&g_stReqPayload, 0x00, sizeof(g_stReqPayload));
	memset(&stNetECRRec, 0x00, sizeof(stNetECRRec));

	memset(&stNetECRV40RespRec, 0x00, sizeof(stNetECRV40RespRec));

	memset(&gstECRTermTotal, 0x00, sizeof(gstECRTermTotal));

	/*for ECR get total cards from Host*/
	vdECR40_SetSettleCardTotal(0);
	memset(&stNetsECRV40CardTotal, 0x00, sizeof(stNetsECRV40CardTotal));

	/*Before Process funcs, Must init status*/
	vdECRV40_SetMsgStatus((UINT)V40_INCORRECT_FLOW);

	/*by Default will send back receipt text*/
	vdECRV40_SetRcptTextStatus(0x01);
	g_stReqPayload.baTxnRcptReq[0] = 0x01;
	vdECRInitReceiptTextInfo();
}

