#include <ctosapi.h>

#include <stdio.h>		//printf
#include <string.h>		//strlen
#include <memory.h>
#include <stdlib.h>

#include "crc16.h"

#include "..\PCI100\COMMS.h"
#include "..\Debug\Debug.h"

extern BYTE bV3PPort;
extern int bUH_Port;
extern int readFromCOM(int fdcom, unsigned char *rbuf, int length);

#define USB_HOST_PORT	9
#define USB_PORT	8

#define SOH  0x01
#define STX  0x02
#define EOT  0x04
#define ACK  0x06
#define NAK  0x15
#define CAN  0x18
#define CTRLZ 0x1A

#define DLY_1S 1000
#define MAXRETRANS 25

char szXmodemRecvData[1024 + 1] = {0};

char _inbyte(int inTimeOut)
{
	int maxRevSize = 1;
        int ret = 0;

	memset(szXmodemRecvData, 0x00, sizeof(szXmodemRecvData));
	/*add USB host for V3P*/
	if (bV3PPort == USB_HOST_PORT)
	{
        //CTOS_Delay(inTimeOut);
		maxRevSize = readFromCOM(bUH_Port, szXmodemRecvData , maxRevSize);
		vdDebug_LogPrintf("maxRevSize [%d]", maxRevSize);
		vdDebug_LogPrintf("szXmodemRecvData[%x] [%d]", szXmodemRecvData[0], szXmodemRecvData[0]);
		if (maxRevSize > 0)
			return szXmodemRecvData[0];
	}
	else if (bV3PPort == USB_PORT)
	{
		ret = CTOS_USBRxReady(&maxRevSize); 
		if (ret == d_OK)
		{	
			maxRevSize= 1;
			CTOS_USBRxData(szXmodemRecvData, &maxRevSize);
//			vdDebug_LogPrintf("%c %02X \n", szXmodemRecvData[0], szXmodemRecvData[0]);
			if (maxRevSize > 0)
				return szXmodemRecvData[0];
		}
	}
	else
	{
		ret = CTOS_RS232RxReady(bV3PPort, &maxRevSize); 
		if (ret == d_OK)
		{	
			maxRevSize= 1;
			CTOS_RS232RxData(bV3PPort, szXmodemRecvData, &maxRevSize);
//			vdDebug_LogPrintf("%c %02X \n", szXmodemRecvData[0], szXmodemRecvData[0]);
			if (maxRevSize > 0)
				return szXmodemRecvData[0];
		}
	}		
	return -1;
}

void _outbyte(char c)
{
	inCTOSS_V3PRS232SendBuf(&c, 1);	
//	vdDebug_LogPrintf("%c", c);
}

static int check(int crc, const char *buf, int sz)
{
	return 1;

	if (crc) {
		unsigned short crc = crc16_ccitt(buf, sz);
		unsigned short tcrc = (buf[sz]<<8)+buf[sz+1];
		if (crc == tcrc)
			return 1;
	}
	else {
		int i;
		unsigned char cks = 0;
		for (i = 0; i < sz; ++i) {
			cks += buf[i];
		}
		if (cks == buf[sz])
		return 1;
	}

	return 0;
}

static void flushinput(void)
{
//	while (_inbyte(((DLY_1S)*3)>>1) >= 0)
//		;
}

int xmodemReceive(unsigned char *dest, int destsz)
{
	char xbuff[1030] = {0}; /* 1024 for XModem 1k + 3 head chars + 2 crc + nul */
	char *p;
	int bufsz, crc = 0;
	unsigned char trychar = 'C';
	unsigned char packetno = 1;
	int i, c, len = 0;
	int retry, retrans = MAXRETRANS;
	int inLength = 0;
	
	memset(xbuff, 0x00, sizeof(xbuff));
	for(;;) {
		for( retry = 0; retry < 16; ++retry) {
			//vdDebug_LogPrintf("trychar[%c] [%d]", trychar, trychar);
			if (trychar) _outbyte(trychar);
			if ((c = _inbyte((DLY_1S)<<1)) >= 0) {
				//vdDebug_LogPrintf("c[%d]", c);
				switch (c) {
				case SOH:
					bufsz = 128;
					goto start_recv;
				case STX:
					bufsz = 1024;
					goto start_recv;
				case EOT:
					flushinput();
					_outbyte(ACK);
					return len; /* normal end */
				case CAN:
					if ((c = _inbyte(DLY_1S)) == CAN) {
						flushinput();
						_outbyte(ACK);
						return -1; /* canceled by remote */
					}
					break;
				default:
					break;
				}
			}
		}
		if (trychar == 'C') { trychar = NAK; continue; }
		flushinput();

		_outbyte(ACK);
		//vdDebug_LogPrintf("len[%d]", len);
		return len; /* normal end */
					
		_outbyte(CAN);
		_outbyte(CAN);
		_outbyte(CAN);
		return -2; /* sync error */

	start_recv:
		CTOS_Delay(50);

		vdDebug_LogPrintf("start_recv");
		
		if (trychar == 'C') crc = 1;
		trychar = 0;
		p = xbuff;
		*p++ = c;
		for (i = 0;  i < (bufsz+(crc?1:0)+3); ++i) {
//			if ((c = _inbyte(DLY_1S)) < 0) goto reject;
			c = _inbyte(DLY_1S);
			*p++ = c;
		}
			
		if (xbuff[1] == (unsigned char)(~xbuff[2]) && 
			(xbuff[1] == packetno || xbuff[1] == (unsigned char)packetno-1) &&
			check(crc, &xbuff[3], bufsz)) {
			if (xbuff[1] == packetno)	{
				vdDebug_LogPrintf("len[%d]", len);
				vdDebug_LogPrintf("destsz[%d]", destsz);
				register int count = destsz - len;
				if (count > bufsz) count = bufsz;
				if (count > 0) {
					memcpy (&dest[len], &xbuff[3], count);
					len += count;
				}
				++packetno;
				retrans = MAXRETRANS+1;
			}
			if (--retrans <= 0) {
				flushinput();
				_outbyte(CAN);
				_outbyte(CAN);
				_outbyte(CAN);
				return -3; /* too many retry error */
			}
			_outbyte(ACK);
			continue;
		}
	reject:
		flushinput();
		_outbyte(NAK);
	}

	return len;
}

int xmodemTransmit(unsigned char *src, int srcsz)
{
	char xbuff[1030] =  {0}; /* 1024 for XModem 1k + 3 head chars + 2 crc + nul */
	int bufsz, crc = -1;
	unsigned char packetno = 1;
	int i, c, len = 0;
	int retry;

	memset(xbuff, 0x00, sizeof(xbuff));
	for(;;) {
		for( retry = 0; retry < 16; ++retry) {
			if ((c = _inbyte((DLY_1S)<<1)) >= 0) {
				switch (c) {
				case 'C':
					crc = 1;
					goto start_trans;
				case NAK:
					crc = 0;
					goto start_trans;
				case CAN:
					if ((c = _inbyte(DLY_1S)) == CAN) {
						_outbyte(ACK);
						flushinput();
						return -1; /* canceled by remote */
					}
					break;
				default:
					break;
				}
			}
		}
		_outbyte(CAN);
		_outbyte(CAN);
		_outbyte(CAN);
		flushinput();
		return -2; /* no sync */

		for(;;) {
		start_trans:
//			xbuff[0] = SOH; bufsz = 128;
			xbuff[0] = STX; bufsz = 1024;
			xbuff[1] = packetno;
			xbuff[2] = ~packetno;
			c = srcsz - len;
			if (c > bufsz) c = bufsz;
			if (c >= 0) {
				memset (&xbuff[3], 0, bufsz);
				if (c == 0) {
					xbuff[3] = CTRLZ;
				}
				else {
					memcpy (&xbuff[3], &src[len], c);
					if (c < bufsz) xbuff[3+c] = CTRLZ;
				}
				if (crc) {
					unsigned short ccrc = crc16_ccitt(&xbuff[3], bufsz);
					xbuff[bufsz+3] = (ccrc>>8) & 0xFF;
					xbuff[bufsz+4] = ccrc & 0xFF;
				}
				else {
					unsigned char ccks = 0;
					for (i = 3; i < bufsz+3; ++i) {
						ccks += xbuff[i];
					}
					xbuff[bufsz+3] = ccks;
				}
				for (retry = 0; retry < MAXRETRANS; ++retry) {
					for (i = 0; i < bufsz+4+(crc?1:0); ++i) {
						_outbyte(xbuff[i]);
					}
					if ((c = _inbyte(DLY_1S)) >= 0 ) {
						switch (c) {
						case ACK:
							++packetno;
							len += bufsz;
							goto start_trans;
						case CAN:
							if ((c = _inbyte(DLY_1S)) == CAN) {
								_outbyte(ACK);
								flushinput();
								return -1; /* canceled by remote */
							}
							break;
						case NAK:
						default:
							break;
						}
					}
				}
				_outbyte(CAN);
				_outbyte(CAN);
				_outbyte(CAN);
				flushinput();
				return -4; /* xmit error */
			}
			else {
				for (retry = 0; retry < 10; ++retry) {
					_outbyte(EOT);
					if ((c = _inbyte((DLY_1S)<<1)) == ACK) break;
				}
				flushinput();
				return (c == ACK)?len:-5;
			}
		}
	}
}

