#include <ctosapi.h>

#include <stdio.h>		//printf
#include <string.h>		//strlen
#include <memory.h>
#include <stdlib.h>
#include "..\Includes\POSTypedef.h"

#include "XModem.h"
#include "..\FileModule\myFileFunc.h"
#include "..\Debug\Debug.h"
#include "..\PCI100\COMMS.h"
#include "..\Includes\Wub_lib.h"
#include "..\comm\v5Comm.h"
#include "../Accum/accum.h"
#include "..\POWRFAIL\POSPOWRFAIL.h"

#define PRINT_BMP		"/home/<USER>/pub/Print_BMP.bmp"

#define USB_HOST_PORT	9
#define USB_PORT	8

typedef struct
{
	unsigned char uszFileTransferName[100+1];
	int inFileTransferNameSize;
}STRUCT_FILE_TRANSFER_EXT;

STRUCT_FILE_TRANSFER_EXT strFileTransfer_Ext;

static int gCommModeex;
BYTE gExtAmountBuff[AMT_BCD_SIZE + 1];

#define VS_SUCCESS            0 

typedef struct
{
	int inmode;
	int hdt;
	int cpt;
}STRUCT_COMMS_INIT_EXT;

extern int g_inTermDetectcard;
extern int g_inPinpadDetectcard;


int inCOMMS_Init_Terminal_Ext(char *inbuf)
{
	USHORT inRetVal;
	int inMode = 0;
	STRUCT_COMMS_INIT_EXT inCommsInit;

	memcpy(&inCommsInit,&inbuf[1],sizeof(STRUCT_COMMS_INIT_EXT));

	srTransRec.usTerminalCommunicationMode = inMode = inCommsInit.inmode;
	vdDebug_LogPrintf("inCOMMS_Init_Terminal_Ext inMode=[%d],hostindex=[%d]",inCommsInit.inmode,inCommsInit.hdt);
	inCPTRead(inCommsInit.hdt);
	if (inCTOS_InitComm(inMode) != d_OK) 
    {
		//if (strTCTEX.fHawkerMode == 1)	//need to allow to do NFP purchase
		//	return d_OK;
		
        vdSetErrorMessage("COMM INIT ERR");
//		inECRSetRespCode("XX", HRC_CONNECT_ISSUE);
        return(d_NO);
    }
    
    inRetVal = inCTOS_CheckInitComm(srTransRec.usTerminalCommunicationMode); 
	if (inRetVal != d_OK)
	{
		//if (strTCTEX.fHawkerMode == 1)	//need to allow to do NFP purchase
		//	return d_OK;
		
		//if (srTransRec.usTerminalCommunicationMode == GPRS_MODE)
		if (inMode == GPRS_MODE)
			vdSetErrorMessage("GPRS NOT ESTABLISHED");
		else
			vdSetErrorMessage("COMM INIT ERR");

			//inECRSetRespCode("XX", HRC_CONNECT_ISSUE);
			return(d_NO);
	}

	
}

int inCOMMS_Connect_Terminal_Ext(char *inbuf)
{
	int inResult;
	vdDebug_LogPrintf("inCOMMS_Connect_Terminal_Ext...........");
	
	inResult = srCommFuncPoint.inConnect(&srTransRec);
	return inResult;
}

int inCOMMS_Send_Terminal_Ext(char *inbuf)
{
	int inResult;
	unsigned long ulSendLen;
	unsigned char uszSendData[2048];
	int offset;

	vdDebug_LogPrintf("inCOMMS_Send_Terminal_Ext...........");
	
	offset = 1;
	memcpy(&ulSendLen,&inbuf[offset],sizeof(unsigned long));
	offset += sizeof(unsigned long);
	offset += 1;
	memset(uszSendData,0x00,sizeof(uszSendData));
	memcpy(uszSendData,&inbuf[offset],ulSendLen);

	inResult = srCommFuncPoint.inSendData(&srTransRec,uszSendData,ulSendLen);

	return inResult;
}


int inCOMMS_Recieve_Terminal_Ext(unsigned char *szRecvCommBuf, int *inRecCommlen)
{
	char szRecvBuf[3000+1];
	int inRecvlen = 0;

	char szV3PSendBuf[3000+1] = {0};
	int inOffSet = 0;
	
	long inResult = 0;

	vdDebug_LogPrintf("inCOMMS_Recieve_Terminal_Ext...........");
	
	memset(szRecvBuf,0x00,sizeof(szRecvBuf));
	inResult = srCommFuncPoint.inRecData(&srTransRec,szRecvBuf);
	vdDebug_LogPrintf("inCOMMS_Recieve_Terminal_Ext.inResult=[%d]..",inResult);
	if (inResult > 0)
	{
		//*inRecCommlen = inResult;
		memset(szV3PSendBuf, 0x00, sizeof(szV3PSendBuf));
		inOffSet = 0;
		szV3PSendBuf[inOffSet] = STX;
		inOffSet += 1;
		//////////////////////////////////////////////////////////////////////////
		memcpy(&szV3PSendBuf[inOffSet], &inResult, sizeof(long));
		inOffSet += sizeof(long);
		szV3PSendBuf[inOffSet] = '|';
		inOffSet += 1;
		//////////////////////////////////////////////////////////////////////////
		memcpy(&szV3PSendBuf[inOffSet], szRecvBuf, inResult);
		inOffSet += inResult;
		///////////////////////////////////////////////////////////////////////
		szV3PSendBuf[inOffSet] = ETX;
		inOffSet += 1;			
		szV3PSendBuf[inOffSet] = (char) wub_lrc(&(szV3PSendBuf[1]), inOffSet-1);
		inOffSet += 1;

		*inRecCommlen = inOffSet;
		memcpy(szRecvCommBuf,szV3PSendBuf,inOffSet);
		return d_OK;
	}
	else
	{
		inResult = 0;
		//*inRecCommlen = inResult;
		memset(szV3PSendBuf, 0x00, sizeof(szV3PSendBuf));
		inOffSet = 0;
		szV3PSendBuf[inOffSet] = STX;
		inOffSet += 1;
		//////////////////////////////////////////////////////////////////////////
		memcpy(&szV3PSendBuf[inOffSet], &inResult, sizeof(long));
		inOffSet += sizeof(long);
		szV3PSendBuf[inOffSet] = '|';
		inOffSet += 1;
		//////////////////////////////////////////////////////////////////////////
		memcpy(&szV3PSendBuf[inOffSet], szRecvBuf, inResult);
		inOffSet += inResult;
		///////////////////////////////////////////////////////////////////////
		szV3PSendBuf[inOffSet] = ETX;
		inOffSet += 1;			
		szV3PSendBuf[inOffSet] = (char) wub_lrc(&(szV3PSendBuf[1]), inOffSet-1);
		inOffSet += 1;

		*inRecCommlen = inOffSet;
		memcpy(szRecvCommBuf,szV3PSendBuf,inOffSet);
		return d_OK;
	}
		return d_NO;
}


int inCOMMS_Disconnect_Terminal_Ext()
{
	int inResult;
	vdDebug_LogPrintf("inCOMMS_Disconnect_Terminal_Ext...........");
	
	inResult = srCommFuncPoint.inDisconnect(&srTransRec);
	return inResult;
}


void vdNETS_Analyse(BYTE *inbuf, USHORT inlen)
{
	CTLS_Trans strCTLS_Trans;

	vdDebug_LogPrintf("vdNETS_Analyse...");
	memset(&strCTLS_Trans, 0x00, sizeof(CTLS_Trans));
	memcpy(&strCTLS_Trans,inbuf,sizeof(CTLS_Trans));

	memset(gExtAmountBuff,0x00,sizeof(gExtAmountBuff));
	memcpy(gExtAmountBuff,strCTLS_Trans.szAmount,6);
	vdPCIDebug_HexPrintf("szBaseAmount",strCTLS_Trans.szAmount,6);

}

void vdNETS_Purchase_Terminal_EXT(void)
{
	CTLS_Trans strCTLSTrans;
	CTLS_TransData strCTLSTransData;
	unsigned char *pszPtr = NULL;
	USHORT usResult = 0;

	char szV3PSendBuf[1024+1] = {0};
	int inOffSet = 0;
	unsigned char szRecvBuf[1024+1] = {0};
	int inRecvlen = 0;
	unsigned char uszDataBuffer[10240+1] = {0};
	unsigned char uszFileBuffer[10240+1] = {0};
	char  szSystemCmdPath[250];
	unsigned char szRecvCommBuf[2048+1] = {0};
	int inRecvCommlen = 0; 
	char szFileName[20];
	int inRet;

	vdDebug_LogPrintf("vdNETS_Purchase_Terminal_EXT..strTCT.byPinPadType=[%d].strTCT.byPinPadMode=[%d].strTCT.byPinPadPort=[%d]..",strTCT.byPinPadType,strTCT.byPinPadMode,strTCT.byPinPadPort);
	vdPCIDebug_HexPrintf("szBaseAmount",srTransRec.szBaseAmount,6);

	if(strTCT.byPinPadMode == 0)
	{
		if(strTCT.byPinPadType == 4)
		{
			char szV3PSendBuf[1024+1] = {0};
			int inOffSet = 0;
			char szRecvBuf[1024+1];
			int inRecvlen = 0;
			int status;

			vdDebug_LogPrintf("NETS_PURCHASE EXT CMD");
			
			vdCTOSS_SetV3PRS232Port(strTCT.byPinPadPort);
			inCTOSS_V3PRS232Open(strTCT.inPPBaudRate, 'N', 8, 1);

			// send STX INJECT_KEY ETX LRC
			memset(szV3PSendBuf, 0x00, sizeof(szV3PSendBuf));
			inOffSet = 0;
			szV3PSendBuf[inOffSet] = STX;
			inOffSet += 1;
			memcpy(&szV3PSendBuf[inOffSet], "NETS_PURCHASE", strlen("NETS_PURCHASE"));
			inOffSet += strlen("NETS_PURCHASE");
			szV3PSendBuf[inOffSet] = '|';
			inOffSet += 1;

			memset(&strCTLSTrans, 0x00, sizeof(CTLS_Trans));
			memcpy(strCTLSTrans.szAmount, srTransRec.szBaseAmount, 7);
			memcpy(&szV3PSendBuf[inOffSet], &strCTLSTrans, sizeof(CTLS_Trans));
			inOffSet += sizeof(CTLS_Trans);

			szV3PSendBuf[inOffSet] = ETX;
			inOffSet += 1;			
			szV3PSendBuf[inOffSet] = (char) wub_lrc(&(szV3PSendBuf[1]), inOffSet-1);
			inOffSet += 1;
			inCTOSS_V3PRS232SendBuf(szV3PSendBuf, inOffSet);	

			
			memset(szRecvBuf,0x00,sizeof(szRecvBuf));
			usResult = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, 500);
			vdDebug_LogPrintf("inCTOSS_V3PRS232RecvACK usResult[%d] szRecvBuf[%d]", usResult, szRecvBuf[0]);
			DebugAddHEX("inCTOSS_V3PRS232RecvACK", szRecvBuf, inRecvlen);

			//inCTOSS_V3PRS232Close();
		}
	}
}


int inNETS_Purchase_Terminal_EXTStatus(void)
{
	CTLS_Trans strCTLSTrans;
	CTLS_TransData strCTLSTransData;
	unsigned char *pszPtr = NULL;
	USHORT usResult = 0;

	char szV3PSendBuf[1024+1] = {0};
	int inOffSet = 0;
	unsigned char szRecvBuf[1024+1] = {0};
	int inRecvlen = 0;
	unsigned char uszDataBuffer[10240+1] = {0};
	unsigned char uszFileBuffer[10240+1] = {0};
	char  szSystemCmdPath[250];
	unsigned char szRecvCommBuf[2048+1] = {0};
	int inRecvCommlen = 0; 
	char szFileName[20];
	int inRet;

	vdDebug_LogPrintf("inNETS_Purchase_Terminal_EXTStatus..strTCT.byPinPadType=[%d].strTCT.byPinPadMode=[%d].strTCT.byPinPadPort=[%d]..",strTCT.byPinPadType,strTCT.byPinPadMode,strTCT.byPinPadPort);

	if(strTCT.byPinPadMode == 0)
	{
		if(strTCT.byPinPadType == 4)
		{
			char szV3PSendBuf[1024+1] = {0};
			int inOffSet = 0;
			char szRecvBuf[1024+1];
			int inRecvlen = 0;
			int status;

			memset(szRecvBuf,0x00,sizeof(szRecvBuf));
			usResult = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, 100);
			vdDebug_LogPrintf("inCTOSS_V3PRS232RecvACK usResult[%d] szRecvBuf[%d]", usResult, szRecvBuf[0]);
			if (usResult == d_OK)
				return d_OK;

		}
	}

	return d_NO;
}



void vdNETS_Purchase_End_Terminal_EXT(void)
{
	CTLS_Trans strCTLSTrans;
	CTLS_TransData strCTLSTransData;
	unsigned char *pszPtr = NULL;
	USHORT usResult = 0;

	char szV3PSendBuf[1024+1] = {0};
	int inOffSet = 0;
	unsigned char szRecvBuf[1024+1] = {0};
	int inRecvlen = 0;
	unsigned char uszDataBuffer[10240+1] = {0};
	unsigned char uszFileBuffer[10240+1] = {0};
	char  szSystemCmdPath[250];
	unsigned char szRecvCommBuf[2048+1] = {0};
	int inRecvCommlen = 0; 
	char szFileName[20];
	int inRet;

	vdDebug_LogPrintf("vdNETS_Purchase_End_Terminal_EXT..strTCT.byPinPadType=[%d].strTCT.byPinPadMode=[%d].strTCT.byPinPadPort=[%d]..",strTCT.byPinPadType,strTCT.byPinPadMode,strTCT.byPinPadPort);
	//if (g_inPinpadDetectcard == 1)
	//{
		//if detect card in pinpad, V3 no need send this command to V3P		
	//	vdDebug_LogPrintf("detect card in pinpad");
	//	return;
	//}

	if(strTCT.byPinPadMode == 0)
	{
		if(strTCT.byPinPadType == 4)
		{
			char szV3PSendBuf[1024+1] = {0};
			int inOffSet = 0;
			char szRecvBuf[1024+1];
			int inRecvlen = 0;
			int status;

			vdDebug_LogPrintf("NETS_PURCHASE END EXT CMD");
			
			szV3PSendBuf[0] = ACK;
			inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);

			inCTOSS_V3PRS232Close();
		}
	}
}


int inNETS_Purchase_Ext(void)
{
	CTLS_Trans strCTLSTrans;
	CTLS_TransData strCTLSTransData;
	unsigned char *pszPtr = NULL;
	USHORT usResult = 0;

	char szV3PSendBuf[1024+1] = {0};
	int inOffSet = 0;
	unsigned char szRecvBuf[1024+1] = {0};
	int inRecvlen = 0;
	unsigned char uszDataBuffer[50240+1] = {0};
	unsigned char uszFileBuffer[50240+1] = {0};
	char  szSystemCmdPath[250];
	unsigned char szRecvCommBuf[2048+1] = {0};
	int inRecvCommlen = 0; 
    char szFileName[20];
	int inRet;

	vdDebug_LogPrintf("inNETS_Purchase_USB..strTCT.byPinPadType=[%d].strTCT.byPinPadMode=[%d].strTCT.byPinPadPort=[%d]..",strTCT.byPinPadType,strTCT.byPinPadMode,strTCT.byPinPadPort);
//for testing ext pinpad
//	memcpy(srTransRec.szBaseAmount, "\x00\x00\x00\x00\x10\x00", 6);

	CTOS_LCDTClearDisplay();


	if(strTCT.byPinPadMode == 0)
	{
		if(strTCT.byPinPadType == 4)
		{
			char szV3PSendBuf[1024+1] = {0};
			int inOffSet = 0;
			char szRecvBuf[50240+1];
			int inRecvlen = 0;
			int status;

			vdDebug_LogPrintf("NETS_PURCHASE EXT CMD");
			
			vdCTOSS_SetV3PRS232Port(strTCT.byPinPadPort);
			inCTOSS_V3PRS232Open(strTCT.inPPBaudRate, 'N', 8, 1);	

			memset(szRecvBuf,0x00,sizeof(szRecvBuf));
			usResult = inCTOSS_V3PRS232RecvBuf_EXT(szRecvBuf, &inRecvlen, 300);
			//CTOS_LCDTPrintXY(1, 10, "APDU OUT...");
			
			vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf_EXT usResult[%d] inRecvlen[%d]", usResult, inRecvlen);
			//inPrintISOPacket(VS_FALSE, szRecvBuf, inRecvlen);
			DebugAddHEX("inCTOSS_V3PRS232RecvBuf", szRecvBuf, inRecvlen);
			if (usResult != d_OK)
			{
				inCTOSS_V3PRS232Close();
				return usResult;
			}

			do{
				// patrick now keep waiting command and process only. Until received "END_TRX" EXT command then means end transaction.
				//CTOS_LCDTPrintXY(1, 6, "WAITING EXT COMMAND IN...");
				
				if ((memcmp((unsigned char *)&szRecvBuf[1],"PRINT_RECEIPT", strlen("PRINT_RECEIPT"))==0)||(memcmp((unsigned char *)&szRecvBuf[0],"PRINT_RECEIPT", strlen("PRINT_RECEIPT"))==0))
				{

				    vdDebug_LogPrintf("PRINT_RECEIPT");
					

					pszPtr = NULL;
					pszPtr = (char*)memchr(szRecvBuf, '|', inRecvlen); // check STX
					if (NULL == pszPtr)
					{
						//sometime the response error, may can not get |, 
						//pszPtr will be NULL, copy will cause crash
						// return d_NO;
					}
					else
					{
					
						szV3PSendBuf[0] = ACK;
						//vdDebug_LogPrintf("Send ACK Start");
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);

						
						vdDebug_LogPrintf("Send ACK End");

						FILE *ptr_myfile;
				
						memset(&strFileTransfer_Ext, 0x00, sizeof(STRUCT_FILE_TRANSFER_EXT));	
						memcpy(&strFileTransfer_Ext, &pszPtr[1], sizeof(STRUCT_FILE_TRANSFER_EXT)); // copy transaction amount?

						unlink((char *)strFileTransfer_Ext.uszFileTransferName);
					
						ptr_myfile = fopen(strFileTransfer_Ext.uszFileTransferName, "wb");
						if (!ptr_myfile)
						{
							vdDebug_LogPrintf("Unable to open file!");
							//inCTOSS_V3PRS232Close();
							//return 1;
							//can't return must wait V3P send END_TRX, if not, V3 will exit first, but V3P still process something
							continue;
						}
					
						fseek(ptr_myfile, 0L, SEEK_SET);
 
						memset(uszDataBuffer, 0x00, sizeof(uszDataBuffer));
						vdDebug_LogPrintf("xmodemReceive start");
						//usResult = xmodemReceive((unsigned char *)uszDataBuffer, strFileTransfer_Ext.inFileTransferNameSize);


						vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf");
						memset(szRecvBuf,0x00,sizeof(szRecvBuf));
						memset(uszFileBuffer, 0x00, sizeof(uszFileBuffer));
						
						usResult = inCTOSS_V3PRS232RecvBuf_EXT(uszDataBuffer, &inRecvlen, 5000);
						vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf usResult[%d] inRecvlen[%d]", usResult, inRecvlen);

						memcpy(uszFileBuffer, &uszDataBuffer[1], inRecvlen-3);
						//CTOS_LCDTPrintXY(1, 10, "APDU OUT...");
						vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf");
						//inPrintISOPacket(VS_FALSE, szRecvBuf, inRecvlen);
						DebugAddHEX("inCTOSS_V3PRS232RecvBuf", uszDataBuffer, inRecvlen);
						//DebugAddHEX("uszFileBuffer", uszFileBuffer, inRecvlen-3);

						
						szV3PSendBuf[0] = ACK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
						vdDebug_LogPrintf("Send ACK");
					

						vdDebug_LogPrintf("xmodemReceive end usResult [%d]", usResult);
						fwrite(uszFileBuffer, strFileTransfer_Ext.inFileTransferNameSize, 1, ptr_myfile);
						fclose(ptr_myfile);
						vdDebug_LogPrintf("strFileTransfer_Ext.uszFileTransferName[%s]", strFileTransfer_Ext.uszFileTransferName);
						memset(szSystemCmdPath, 0x00, sizeof(szSystemCmdPath)); 			
						sprintf(szSystemCmdPath, "gzip -f -d %s", strFileTransfer_Ext.uszFileTransferName);
						system(szSystemCmdPath);

						memset(szFileName, 0x00, sizeof(szFileName));
						strncpy(szFileName, strFileTransfer_Ext.uszFileTransferName, strlen(strFileTransfer_Ext.uszFileTransferName) -3);
						vdDebug_LogPrintf("szFileName[%s]", szFileName);
						
						displaybmpEx(0, 32, szFileName);

						vdPrintReceipt(szFileName);

						//return 0;	
						
					}				
				}

				if ((memcmp((unsigned char *)&szRecvBuf[1],"SYNC_DATA", strlen("SYNC_DATA"))==0)||(memcmp((unsigned char *)&szRecvBuf[0],"SYNC_DATA", strlen("SYNC_DATA"))==0))
				{

				    vdDebug_LogPrintf("SYNC_DATA........");
					//CTOS_LCDTPrintAligned(8, "RECV DATA...", d_LCD_ALIGNLEFT);

					pszPtr = NULL;
					pszPtr = (char*)memchr(szRecvBuf, '|', inRecvlen); // check STX
					if (NULL == pszPtr)
					{
						//sometime the response error, may can not get |, 
						//pszPtr will be NULL, copy will cause crash
						// return d_NO;
					}
					else
					{
					
						szV3PSendBuf[0] = ACK;
						//vdDebug_LogPrintf("Send ACK Start");
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);

						
						vdDebug_LogPrintf("Send ACK End");

						FILE *ptr_myfile;
				
						memset(&strFileTransfer_Ext, 0x00, sizeof(STRUCT_FILE_TRANSFER_EXT));	
						memcpy(&strFileTransfer_Ext, &pszPtr[1], sizeof(STRUCT_FILE_TRANSFER_EXT)); // copy transaction amount?

						vdDebug_LogPrintf("uszFileTransferName=[%s]",strFileTransfer_Ext.uszFileTransferName);
						vdDebug_LogPrintf("inFileTransferNameSize=[%d]",strFileTransfer_Ext.inFileTransferNameSize);

		
						unlink((char *)strFileTransfer_Ext.uszFileTransferName);
					
						ptr_myfile = fopen(strFileTransfer_Ext.uszFileTransferName, "wb");
						if (!ptr_myfile)
						{
							vdDebug_LogPrintf("Unable to open file!");
							//inCTOSS_V3PRS232Close();
							//return 1;
							//can't return must wait V3P send END_TRX, if not, V3 will exit first, but V3P still process something
							continue;
						}
					
						fseek(ptr_myfile, 0L, SEEK_SET);
 
						memset(uszDataBuffer, 0x00, sizeof(uszDataBuffer));
						vdDebug_LogPrintf("xmodemReceive start");
						//usResult = xmodemReceive((unsigned char *)uszDataBuffer, strFileTransfer_Ext.inFileTransferNameSize);


						vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf");
						memset(szRecvBuf,0x00,sizeof(szRecvBuf));
						memset(uszFileBuffer, 0x00, sizeof(uszFileBuffer));
						
						usResult = inCTOSS_V3PRS232RecvBuf_EXT(uszDataBuffer, &inRecvlen, 5000);
						vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf usResult[%d] inRecvlen[%d]", usResult, inRecvlen);

						memcpy(uszFileBuffer, &uszDataBuffer[1], inRecvlen-3);
						//CTOS_LCDTPrintXY(1, 10, "APDU OUT...");
						vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf");
						//inPrintISOPacket(VS_FALSE, szRecvBuf, inRecvlen);
						DebugAddHEX("inCTOSS_V3PRS232RecvBuf", uszDataBuffer, inRecvlen);
						//DebugAddHEX("uszFileBuffer", uszFileBuffer, inRecvlen-3);

						
						szV3PSendBuf[0] = ACK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
						vdDebug_LogPrintf("Send ACK");
					

						vdDebug_LogPrintf("xmodemReceive end usResult [%d]", usResult);
						fwrite(uszFileBuffer, strFileTransfer_Ext.inFileTransferNameSize, 1, ptr_myfile);
						fclose(ptr_myfile);
						vdDebug_LogPrintf("strFileTransfer_Ext.uszFileTransferName[%s]", strFileTransfer_Ext.uszFileTransferName);
						memset(szSystemCmdPath, 0x00, sizeof(szSystemCmdPath)); 			
						sprintf(szSystemCmdPath, "gzip -f -d %s", strFileTransfer_Ext.uszFileTransferName);
						system(szSystemCmdPath);

						if (strcmp(strFileTransfer_Ext.uszFileTransferName,"/home/<USER>/pub/TERMINAL.S3DB.gz") == 0)
						{
							CTOS_Delay(100);
							inUpdateTerminalConfig();
						}
						
					}				
				}
				
				
				if (memcmp((unsigned char *)&szRecvBuf[1],"COMM_INIT", strlen("COMM_INIT"))==0)
				{
					vdDebug_LogPrintf("COMM_INIT.....");
					
					inOffSet = 1 + strlen("COMM_INIT");
					usResult = inCOMMS_Init_Terminal_Ext(&szRecvBuf[inOffSet]);
					if (usResult != d_OK)
					{
						szV3PSendBuf[0] = NAK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
						//inCTOSS_V3PRS232Close();
						//return 0;
						//can't return must wait V3P send END_TRX, if not, V3 will exit first, but V3P still process something
						continue;
					}
					else
					{
						szV3PSendBuf[0] = ACK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
					}
				}
				if (memcmp((unsigned char *)&szRecvBuf[1],"COMM_CONNECT", strlen("COMM_CONNECT"))==0)
				{
					vdDebug_LogPrintf("COMM_CONNECT.....");
					
					inOffSet = 1 + strlen("COMM_CONNECT");
					usResult = inCOMMS_Connect_Terminal_Ext(&szRecvBuf[inOffSet]);
					if (usResult != d_OK)
					{
						szV3PSendBuf[0] = NAK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
						//inCTOSS_V3PRS232Close();
						//return 0;
						//can't return must wait V3P send END_TRX, if not, V3 will exit first, but V3P still process something
						continue;
					}
					else
					{
						szV3PSendBuf[0] = ACK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
					}
				}
				if (memcmp((unsigned char *)&szRecvBuf[1],"COMM_SEND", strlen("COMM_SEND"))==0)
				{
					vdDebug_LogPrintf("COMM_SEND.....");
					
					inOffSet = 1 + strlen("COMM_SEND");
					usResult = inCOMMS_Send_Terminal_Ext(&szRecvBuf[inOffSet]);
					if (usResult != d_OK)
					{
						szV3PSendBuf[0] = NAK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
						//inCTOSS_V3PRS232Close();
						//return 0;
						//can't return must wait V3P send END_TRX, if not, V3 will exit first, but V3P still process something
						continue;
					}
					else
					{
						szV3PSendBuf[0] = ACK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
					}
				}
				if (memcmp((unsigned char *)&szRecvBuf[1],"COMM_RECEVIE", strlen("COMM_RECEVIE"))==0)
				{
					vdDebug_LogPrintf("COMM_RECEVIE.....");
					
					memset(szRecvCommBuf,0x00,sizeof(szRecvCommBuf));
					inOffSet = 1 + strlen("COMM_RECEVIE");
					usResult = inCOMMS_Recieve_Terminal_Ext(szRecvCommBuf,&inRecvCommlen);
					if (usResult != d_OK)
					{
						szV3PSendBuf[0] = NAK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
						//inCTOSS_V3PRS232Close();
						//return 0;
						//can't return must wait V3P send END_TRX, if not, V3 will exit first, but V3P still process something
						continue;
					}
					else
					{
						//szV3PSendBuf[0] = ACK;
						//inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
						//CTOS_Delay(100);
						vdPCIDebug_HexPrintf("szRecvCommBuf",szRecvCommBuf,inRecvCommlen);
						inCTOSS_V3PRS232SendBuf(szRecvCommBuf, inRecvCommlen);
					}
				}
				if (memcmp((unsigned char *)&szRecvBuf[1],"COMM_DISCONNECT", strlen("COMM_DISCONNECT"))==0)
				{
					vdDebug_LogPrintf("COMM_DISCONNECT.....");
					
					inOffSet = 1 + strlen("COMM_DISCONNECT");
					usResult = inCOMMS_Disconnect_Terminal_Ext();
					vdDebug_LogPrintf("COMM_DISCONNECT usResult[%d]", usResult);
					if (usResult != d_OK)
					{
						szV3PSendBuf[0] = NAK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
						//return 0;
					}
					else
					{
						szV3PSendBuf[0] = ACK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
						//return 0;
					}
				}


				
				
				if (memcmp((unsigned char *)&szRecvBuf[1],"END_TRX", strlen("END_TRX"))==0)
				{
					szV3PSendBuf[0] = ACK;
					inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
					inCTOSS_V3PRS232Close();
					vdDebug_LogPrintf("END_TRX ACK");

					return 0;				
				}



				if ((memcmp((unsigned char *)&szRecvBuf[1],"UPD_TRNS", strlen("UPD_TRNS"))==0))
				{

				    vdDebug_LogPrintf("UPD_TRNS");
					

					pszPtr = NULL;
					pszPtr = (char*)memchr(szRecvBuf, '|', inRecvlen); // check STX
					if (NULL == pszPtr)
					{
						//sometime the response error, may can not get |, 
						//pszPtr will be NULL, copy will cause crash
						// return d_NO;
					}
					else
					{
					
						szV3PSendBuf[0] = ACK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);

						
						vdDebug_LogPrintf("Send ACK End");

						memset( &srTransRec, 0x00, sizeof(TRANS_DATA_TABLE));
						memcpy(&srTransRec, &pszPtr[1], sizeof(TRANS_DATA_TABLE)); 

						inRet = inCTOS_SaveBatchTxn();
						vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf inRet[%d]", inRet);
					    //if(d_OK != inRet)
					       // return inRet;

					    inRet = inCTOS_UpdateAccumTotal();
						vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf inRet[%d]", inRet);
					    //if(d_OK != inRet)
					        //return inRet;
						
						
					}				
				}

				
				vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf");
				memset(szRecvBuf,0x00,sizeof(szRecvBuf));
				usResult = inCTOSS_V3PRS232RecvBuf_EXT(szRecvBuf, &inRecvlen, 500);
				//CTOS_LCDTPrintXY(1, 10, "APDU OUT...");
				
				//inPrintISOPacket(VS_FALSE, szRecvBuf, inRecvlen);
				DebugAddHEX("inCTOSS_V3PRS232RecvBuf", szRecvBuf, inRecvlen);
				vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf usResult=[%d]",usResult);
				if (usResult != d_OK)
				{
					inCTOSS_V3PRS232Close();
					return usResult;
				}
				
			}while(1);

			inCTOSS_V3PRS232Close();
		}
	}
}



int inNETS_Purchase_USB(void)
{
	CTLS_Trans strCTLSTrans;
	CTLS_TransData strCTLSTransData;
	unsigned char *pszPtr = NULL;
	USHORT usResult = 0;

	char szV3PSendBuf[1024+1] = {0};
	int inOffSet = 0;
	unsigned char szRecvBuf[1024+1] = {0};
	int inRecvlen = 0;
	unsigned char uszDataBuffer[10240+1] = {0};
	unsigned char uszFileBuffer[10240+1] = {0};
	char  szSystemCmdPath[250];
	unsigned char szRecvCommBuf[2048+1] = {0};
	int inRecvCommlen = 0; 
    char szFileName[20];
	int inRet;

	vdDebug_LogPrintf("inNETS_Purchase_USB..strTCT.byPinPadType=[%d].strTCT.byPinPadMode=[%d].strTCT.byPinPadPort=[%d]..",strTCT.byPinPadType,strTCT.byPinPadMode,strTCT.byPinPadPort);
//for testing ext pinpad
	memcpy(srTransRec.szBaseAmount, "\x00\x00\x00\x00\x10\x00", 6);

	CTOS_LCDTClearDisplay();


	if(strTCT.byPinPadMode == 0)
	{
		if(strTCT.byPinPadType == 4)
		{
			char szV3PSendBuf[1024+1] = {0};
			int inOffSet = 0;
			char szRecvBuf[1024+1];
			int inRecvlen = 0;
			int status;

			vdDebug_LogPrintf("NETS_PURCHASE EXT CMD");
			
			vdCTOSS_SetV3PRS232Port(strTCT.byPinPadPort);
			inCTOSS_V3PRS232Open(strTCT.inPPBaudRate, 'N', 8, 1);

			// send STX INJECT_KEY ETX LRC
			memset(szV3PSendBuf, 0x00, sizeof(szV3PSendBuf));
			inOffSet = 0;
			szV3PSendBuf[inOffSet] = STX;
			inOffSet += 1;
			memcpy(&szV3PSendBuf[inOffSet], "NETS_PURCHASE", strlen("NETS_PURCHASE"));
			inOffSet += strlen("NETS_PURCHASE");
			szV3PSendBuf[inOffSet] = '|';
			inOffSet += 1;

			memset(&strCTLSTrans, 0x00, sizeof(CTLS_Trans));
			memcpy(strCTLSTrans.szAmount, srTransRec.szBaseAmount, 7);
			// strcpy(strCTLSTrans.szOtherAmt, szOtherAmt);
			// strcpy(strCTLSTrans.szTransType, szTransType);
			// strcpy(strCTLSTrans.szCatgCode, szCatgCode);
			// strcpy(strCTLSTrans.szCurrCode, szCurrCode);
			// strCTLSTrans.bTagNum = strCTLSTransData.inReserved1;
			// if (strCTLSTrans.bTagNum > 0)
			// {
			//	strCTLSTrans.usTransactionDataLen = strCTLSTransData.inReserved2;
			//	memcpy(strCTLSTrans.pbaTransactionData, strCTLSTransData.szReserved1,strCTLSTransData.inReserved2);
			// }
			//strCTLSTrans.inForcedOnl = g_isForcedOnl;
			memcpy(&szV3PSendBuf[inOffSet], &strCTLSTrans, sizeof(CTLS_Trans));
			inOffSet += sizeof(CTLS_Trans);

			szV3PSendBuf[inOffSet] = ETX;
			inOffSet += 1;			
			szV3PSendBuf[inOffSet] = (char) wub_lrc(&(szV3PSendBuf[1]), inOffSet-1);
			inOffSet += 1;
			inCTOSS_V3PRS232SendBuf(szV3PSendBuf, inOffSet);	

			
			memset(szRecvBuf,0x00,sizeof(szRecvBuf));
			usResult = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, 500);
			vdDebug_LogPrintf("inCTOSS_V3PRS232RecvACK usResult[%d] szRecvBuf[%d]", usResult, szRecvBuf[0]);
			DebugAddHEX("inCTOSS_V3PRS232RecvACK", szRecvBuf, inRecvlen);

			memset(szRecvBuf,0x00,sizeof(szRecvBuf));
			usResult = inCTOSS_V3PRS232RecvBuf_EXT(szRecvBuf, &inRecvlen, 30);
			//CTOS_LCDTPrintXY(1, 10, "APDU OUT...");
			
			vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf_EXT usResult[%d] inRecvlen[%d]", usResult, inRecvlen);
			//inPrintISOPacket(VS_FALSE, szRecvBuf, inRecvlen);
			DebugAddHEX("inCTOSS_V3PRS232RecvBuf", szRecvBuf, inRecvlen);
			if (usResult != d_OK)
			{
				inCTOSS_V3PRS232Close();
				return usResult;
			}

			do{
				// patrick now keep waiting command and process only. Until received "END_TRX" EXT command then means end transaction.
				//CTOS_LCDTPrintXY(1, 6, "WAITING EXT COMMAND IN...");
				
				if ((memcmp((unsigned char *)&szRecvBuf[1],"PRINT_RECEIPT", strlen("PRINT_RECEIPT"))==0)||(memcmp((unsigned char *)&szRecvBuf[0],"PRINT_RECEIPT", strlen("PRINT_RECEIPT"))==0))
				{

				    vdDebug_LogPrintf("PRINT_RECEIPT");
					

					pszPtr = NULL;
					pszPtr = (char*)memchr(szRecvBuf, '|', inRecvlen); // check STX
					if (NULL == pszPtr)
					{
						//sometime the response error, may can not get |, 
						//pszPtr will be NULL, copy will cause crash
						// return d_NO;
					}
					else
					{
					
						szV3PSendBuf[0] = ACK;
						//vdDebug_LogPrintf("Send ACK Start");
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);

						
						vdDebug_LogPrintf("Send ACK End");

						FILE *ptr_myfile;
				
						memset(&strFileTransfer_Ext, 0x00, sizeof(STRUCT_FILE_TRANSFER_EXT));	
						memcpy(&strFileTransfer_Ext, &pszPtr[1], sizeof(STRUCT_FILE_TRANSFER_EXT)); // copy transaction amount?

						unlink((char *)strFileTransfer_Ext.uszFileTransferName);
					
						ptr_myfile = fopen(strFileTransfer_Ext.uszFileTransferName, "wb");
						if (!ptr_myfile)
						{
							vdDebug_LogPrintf("Unable to open file!");
							inCTOSS_V3PRS232Close();
							return 1;
						}
					
						fseek(ptr_myfile, 0L, SEEK_SET);
 
						memset(uszDataBuffer, 0x00, sizeof(uszDataBuffer));
						vdDebug_LogPrintf("xmodemReceive start");
						//usResult = xmodemReceive((unsigned char *)uszDataBuffer, strFileTransfer_Ext.inFileTransferNameSize);


						vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf");
						memset(szRecvBuf,0x00,sizeof(szRecvBuf));
						memset(uszFileBuffer, 0x00, sizeof(uszFileBuffer));
						
						usResult = inCTOSS_V3PRS232RecvBuf_EXT(uszDataBuffer, &inRecvlen, 5000);
						vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf usResult[%d] inRecvlen[%d]", usResult, inRecvlen);

						memcpy(uszFileBuffer, &uszDataBuffer[1], inRecvlen-3);
						//CTOS_LCDTPrintXY(1, 10, "APDU OUT...");
						vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf");
						//inPrintISOPacket(VS_FALSE, szRecvBuf, inRecvlen);
						DebugAddHEX("inCTOSS_V3PRS232RecvBuf", uszDataBuffer, inRecvlen);
						//DebugAddHEX("uszFileBuffer", uszFileBuffer, inRecvlen-3);

						
						szV3PSendBuf[0] = ACK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
						vdDebug_LogPrintf("Send ACK");
					

						vdDebug_LogPrintf("xmodemReceive end usResult [%d]", usResult);
						fwrite(uszFileBuffer, strFileTransfer_Ext.inFileTransferNameSize, 1, ptr_myfile);
						fclose(ptr_myfile);
						vdDebug_LogPrintf("strFileTransfer_Ext.uszFileTransferName[%s]", strFileTransfer_Ext.uszFileTransferName);
						memset(szSystemCmdPath, 0x00, sizeof(szSystemCmdPath)); 			
						sprintf(szSystemCmdPath, "gzip -f -d %s", strFileTransfer_Ext.uszFileTransferName);
						system(szSystemCmdPath);

						memset(szFileName, 0x00, sizeof(szFileName));
						strncpy(szFileName, strFileTransfer_Ext.uszFileTransferName, strlen(strFileTransfer_Ext.uszFileTransferName) -3);
						vdDebug_LogPrintf("szFileName[%s]", szFileName);
						
						displaybmpEx(0, 32, szFileName);

						vdPrintReceipt(szFileName);

						//return 0;	
						
					}				
				}
				
				if (memcmp((unsigned char *)&szRecvBuf[1],"COMM_INIT", strlen("COMM_INIT"))==0)
				{
					vdDebug_LogPrintf("COMM_INIT.....");
					
					inOffSet = 1 + strlen("COMM_INIT");
					usResult = inCOMMS_Init_Terminal_Ext(&szRecvBuf[inOffSet]);
					if (usResult != d_OK)
					{
						szV3PSendBuf[0] = NAK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
						inCTOSS_V3PRS232Close();
						return 0;
					}
					else
					{
						szV3PSendBuf[0] = ACK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
					}
				}
				if (memcmp((unsigned char *)&szRecvBuf[1],"COMM_CONNECT", strlen("COMM_CONNECT"))==0)
				{
					vdDebug_LogPrintf("COMM_CONNECT.....");
					
					inOffSet = 1 + strlen("COMM_CONNECT");
					usResult = inCOMMS_Connect_Terminal_Ext(&szRecvBuf[inOffSet]);
					if (usResult != d_OK)
					{
						szV3PSendBuf[0] = NAK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
						inCTOSS_V3PRS232Close();
						return 0;
					}
					else
					{
						szV3PSendBuf[0] = ACK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
					}
				}
				if (memcmp((unsigned char *)&szRecvBuf[1],"COMM_SEND", strlen("COMM_SEND"))==0)
				{
					vdDebug_LogPrintf("COMM_SEND.....");
					
					inOffSet = 1 + strlen("COMM_SEND");
					usResult = inCOMMS_Send_Terminal_Ext(&szRecvBuf[inOffSet]);
					if (usResult != d_OK)
					{
						szV3PSendBuf[0] = NAK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
						inCTOSS_V3PRS232Close();
						return 0;
					}
					else
					{
						szV3PSendBuf[0] = ACK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
					}
				}
				if (memcmp((unsigned char *)&szRecvBuf[1],"COMM_RECEVIE", strlen("COMM_RECEVIE"))==0)
				{
					vdDebug_LogPrintf("COMM_RECEVIE.....");
					
					memset(szRecvCommBuf,0x00,sizeof(szRecvCommBuf));
					inOffSet = 1 + strlen("COMM_RECEVIE");
					usResult = inCOMMS_Recieve_Terminal_Ext(szRecvCommBuf,&inRecvCommlen);
					if (usResult != d_OK)
					{
						szV3PSendBuf[0] = NAK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
						inCTOSS_V3PRS232Close();
						return 0;
					}
					else
					{
						//szV3PSendBuf[0] = ACK;
						//inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
						//CTOS_Delay(100);
						vdPCIDebug_HexPrintf("szRecvCommBuf",szRecvCommBuf,inRecvCommlen);
						inCTOSS_V3PRS232SendBuf(szRecvCommBuf, inRecvCommlen);
					}
				}
				if (memcmp((unsigned char *)&szRecvBuf[1],"COMM_DISCONNECT", strlen("COMM_DISCONNECT"))==0)
				{
					vdDebug_LogPrintf("COMM_DISCONNECT.....");
					
					inOffSet = 1 + strlen("COMM_DISCONNECT");
					usResult = inCOMMS_Disconnect_Terminal_Ext();
					vdDebug_LogPrintf("COMM_DISCONNECT usResult[%d]", usResult);
					if (usResult != d_OK)
					{
						szV3PSendBuf[0] = NAK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
						//return 0;
					}
					else
					{
						szV3PSendBuf[0] = ACK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
						//return 0;
					}
				}


				
				
				if (memcmp((unsigned char *)&szRecvBuf[1],"END_TRX", strlen("END_TRX"))==0)
				{
					szV3PSendBuf[0] = ACK;
					inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
					inCTOSS_V3PRS232Close();
					vdDebug_LogPrintf("END_TRX ACK");

					return 0;				
				}



				if ((memcmp((unsigned char *)&szRecvBuf[1],"UPD_TRNS", strlen("UPD_TRNS"))==0))
				{

				    vdDebug_LogPrintf("UPD_TRNS");
					

					pszPtr = NULL;
					pszPtr = (char*)memchr(szRecvBuf, '|', inRecvlen); // check STX
					if (NULL == pszPtr)
					{
						//sometime the response error, may can not get |, 
						//pszPtr will be NULL, copy will cause crash
						// return d_NO;
					}
					else
					{
					
						szV3PSendBuf[0] = ACK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);

						
						vdDebug_LogPrintf("Send ACK End");

						memset( &srTransRec, 0x00, sizeof(TRANS_DATA_TABLE));
						memcpy(&srTransRec, &pszPtr[1], sizeof(TRANS_DATA_TABLE)); 

						inRet = inCTOS_SaveBatchTxn();
						vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf inRet[%d]", inRet);
					    //if(d_OK != inRet)
					       // return inRet;

					    inRet = inCTOS_UpdateAccumTotal();
						vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf inRet[%d]", inRet);
					    //if(d_OK != inRet)
					        //return inRet;
						
						
					}				
				}

				
				vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf");
				memset(szRecvBuf,0x00,sizeof(szRecvBuf));
				usResult = inCTOSS_V3PRS232RecvBuf_EXT(szRecvBuf, &inRecvlen, 500);
				//CTOS_LCDTPrintXY(1, 10, "APDU OUT...");
				
				//inPrintISOPacket(VS_FALSE, szRecvBuf, inRecvlen);
				DebugAddHEX("inCTOSS_V3PRS232RecvBuf", szRecvBuf, inRecvlen);
				vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf usResult=[%d]",usResult);
				if (usResult != d_OK)
				{
					inCTOSS_V3PRS232Close();
					return usResult;
				}
				
			}while(1);

			inCTOSS_V3PRS232Close();
		}
	}
}



int inNETS_SETTLEMENT_USB(void)
{
	CTLS_Trans strCTLSTrans;
	CTLS_TransData strCTLSTransData;
	unsigned char *pszPtr = NULL;
	USHORT usResult = 0;

	char szV3PSendBuf[1024+1] = {0};
	int inOffSet = 0;
	unsigned char szRecvBuf[1024+1] = {0};
	int inRecvlen = 0;
	unsigned char uszDataBuffer[10240+1] = {0};
	unsigned char uszFileBuffer[10240+1] = {0};
	char  szSystemCmdPath[250];
	unsigned char szRecvCommBuf[2048+1] = {0};
	int inRecvCommlen = 0; 
    char szFileName[20];
	int inRet;

	vdDebug_LogPrintf("inNETS_SETTLEMENT_USB.strTCT.byPinPadType=[%d].strTCT.byPinPadMode=[%d].strTCT.byPinPadPort=[%d]..",strTCT.byPinPadType,strTCT.byPinPadMode,strTCT.byPinPadPort);
//for testing ext pinpad
	memcpy(srTransRec.szBaseAmount, "\x00\x00\x00\x00\x10\x00", 6);

	CTOS_LCDTClearDisplay();


	if(strTCT.byPinPadMode == 0)
	{
		if(strTCT.byPinPadType == 4)
		{
			char szV3PSendBuf[1024+1] = {0};
			int inOffSet = 0;
			char szRecvBuf[1024+1];
			int inRecvlen = 0;
			int status;

			vdDebug_LogPrintf("inNETS_SETTLEMENT EXT CMD");
			
			vdCTOSS_SetV3PRS232Port(strTCT.byPinPadPort);
			inCTOSS_V3PRS232Open(strTCT.inPPBaudRate, 'N', 8, 1);

			// send STX INJECT_KEY ETX LRC
			memset(szV3PSendBuf, 0x00, sizeof(szV3PSendBuf));
			inOffSet = 0;
			szV3PSendBuf[inOffSet] = STX;
			inOffSet += 1;
			memcpy(&szV3PSendBuf[inOffSet], "inNETS_SETTLEMENT", strlen("inNETS_SETTLEMENT"));
			inOffSet += strlen("inNETS_SETTLEMENT");
			szV3PSendBuf[inOffSet] = '|';
			inOffSet += 1;

			memset(&strCTLSTrans, 0x00, sizeof(CTLS_Trans));
			memcpy(strCTLSTrans.szAmount, srTransRec.szBaseAmount, 7);
			// strcpy(strCTLSTrans.szOtherAmt, szOtherAmt);
			// strcpy(strCTLSTrans.szTransType, szTransType);
			// strcpy(strCTLSTrans.szCatgCode, szCatgCode);
			// strcpy(strCTLSTrans.szCurrCode, szCurrCode);
			// strCTLSTrans.bTagNum = strCTLSTransData.inReserved1;
			// if (strCTLSTrans.bTagNum > 0)
			// {
			//	strCTLSTrans.usTransactionDataLen = strCTLSTransData.inReserved2;
			//	memcpy(strCTLSTrans.pbaTransactionData, strCTLSTransData.szReserved1,strCTLSTransData.inReserved2);
			// }
			//strCTLSTrans.inForcedOnl = g_isForcedOnl;
			memcpy(&szV3PSendBuf[inOffSet], &strCTLSTrans, sizeof(CTLS_Trans));
			inOffSet += sizeof(CTLS_Trans);

			szV3PSendBuf[inOffSet] = ETX;
			inOffSet += 1;			
			szV3PSendBuf[inOffSet] = (char) wub_lrc(&(szV3PSendBuf[1]), inOffSet-1);
			inOffSet += 1;
			inCTOSS_V3PRS232SendBuf(szV3PSendBuf, inOffSet);	

			
			memset(szRecvBuf,0x00,sizeof(szRecvBuf));
			usResult = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, 500);
			vdDebug_LogPrintf("inCTOSS_V3PRS232RecvACK usResult[%d] szRecvBuf[%d]", usResult, szRecvBuf[0]);
			DebugAddHEX("inCTOSS_V3PRS232RecvACK", szRecvBuf, inRecvlen);

			memset(szRecvBuf,0x00,sizeof(szRecvBuf));
			usResult = inCTOSS_V3PRS232RecvBuf_EXT(szRecvBuf, &inRecvlen, 500);
			//CTOS_LCDTPrintXY(1, 10, "APDU OUT...");
			
			vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf_EXT usResult[%d] inRecvlen[%d]", usResult, inRecvlen);
			//inPrintISOPacket(VS_FALSE, szRecvBuf, inRecvlen);
			DebugAddHEX("inCTOSS_V3PRS232RecvBuf", szRecvBuf, inRecvlen);

			do{
				// patrick now keep waiting command and process only. Until received "END_TRX" EXT command then means end transaction.
				//CTOS_LCDTPrintXY(1, 6, "WAITING EXT COMMAND IN...");
				
				if ((memcmp((unsigned char *)&szRecvBuf[1],"PRINT_RECEIPT", strlen("PRINT_RECEIPT"))==0)||(memcmp((unsigned char *)&szRecvBuf[0],"PRINT_RECEIPT", strlen("PRINT_RECEIPT"))==0))
				{

				    vdDebug_LogPrintf("PRINT_RECEIPT");
					

					pszPtr = NULL;
					pszPtr = (char*)memchr(szRecvBuf, '|', inRecvlen); // check STX
					if (NULL == pszPtr)
					{
						//sometime the response error, may can not get |, 
						//pszPtr will be NULL, copy will cause crash
						// return d_NO;
					}
					else
					{
					
						szV3PSendBuf[0] = ACK;
						//vdDebug_LogPrintf("Send ACK Start");
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);

						
						vdDebug_LogPrintf("Send ACK End");

						FILE *ptr_myfile;
				
						memset(&strFileTransfer_Ext, 0x00, sizeof(STRUCT_FILE_TRANSFER_EXT));	
						memcpy(&strFileTransfer_Ext, &pszPtr[1], sizeof(STRUCT_FILE_TRANSFER_EXT)); // copy transaction amount?

						unlink((char *)strFileTransfer_Ext.uszFileTransferName);
					
						ptr_myfile = fopen(strFileTransfer_Ext.uszFileTransferName, "wb");
						if (!ptr_myfile)
						{
							vdDebug_LogPrintf("Unable to open file!");
							inCTOSS_V3PRS232Close();
							return 1;
						}
					
						fseek(ptr_myfile, 0L, SEEK_SET);
 
						memset(uszDataBuffer, 0x00, sizeof(uszDataBuffer));
						vdDebug_LogPrintf("xmodemReceive start");
						//usResult = xmodemReceive((unsigned char *)uszDataBuffer, strFileTransfer_Ext.inFileTransferNameSize);


						vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf");
						memset(szRecvBuf,0x00,sizeof(szRecvBuf));
						memset(uszFileBuffer, 0x00, sizeof(uszFileBuffer));
						
						usResult = inCTOSS_V3PRS232RecvBuf_EXT(uszDataBuffer, &inRecvlen, 5000);
						vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf usResult[%d] inRecvlen[%d]", usResult, inRecvlen);

						memcpy(uszFileBuffer, &uszDataBuffer[1], inRecvlen-3);
						//CTOS_LCDTPrintXY(1, 10, "APDU OUT...");
						vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf");
						//inPrintISOPacket(VS_FALSE, szRecvBuf, inRecvlen);
						DebugAddHEX("inCTOSS_V3PRS232RecvBuf", uszDataBuffer, inRecvlen);
						//DebugAddHEX("uszFileBuffer", uszFileBuffer, inRecvlen-3);

						
						szV3PSendBuf[0] = ACK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
						vdDebug_LogPrintf("Send ACK");
					

						vdDebug_LogPrintf("xmodemReceive end usResult [%d]", usResult);
						fwrite(uszFileBuffer, strFileTransfer_Ext.inFileTransferNameSize, 1, ptr_myfile);
						fclose(ptr_myfile);
						vdDebug_LogPrintf("strFileTransfer_Ext.uszFileTransferName[%s]", strFileTransfer_Ext.uszFileTransferName);
						memset(szSystemCmdPath, 0x00, sizeof(szSystemCmdPath)); 			
						sprintf(szSystemCmdPath, "gzip -f -d %s", strFileTransfer_Ext.uszFileTransferName);
						system(szSystemCmdPath);

						memset(szFileName, 0x00, sizeof(szFileName));
						strncpy(szFileName, strFileTransfer_Ext.uszFileTransferName, strlen(strFileTransfer_Ext.uszFileTransferName) -3);
						vdDebug_LogPrintf("szFileName[%s]", szFileName);
						
						displaybmpEx(0, 32, szFileName);

						vdPrintReceipt(szFileName);

						//return 0;	
						
					}				
				}
				
				if (memcmp((unsigned char *)&szRecvBuf[1],"COMM_INIT", strlen("COMM_INIT"))==0)
				{
					inOffSet = 1 + strlen("COMM_INIT");
					usResult = inCOMMS_Init_Terminal_Ext(&szRecvBuf[inOffSet]);
					if (usResult != d_OK)
					{
						szV3PSendBuf[0] = NAK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
						inCTOSS_V3PRS232Close();
						return 0;
					}
					else
					{
						szV3PSendBuf[0] = ACK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
					}
				}
				if (memcmp((unsigned char *)&szRecvBuf[1],"COMM_CONNECT", strlen("COMM_CONNECT"))==0)
				{
					inOffSet = 1 + strlen("COMM_CONNECT");
					usResult = inCOMMS_Connect_Terminal_Ext(&szRecvBuf[inOffSet]);
					if (usResult != d_OK)
					{
						szV3PSendBuf[0] = NAK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
						inCTOSS_V3PRS232Close();
						return 0;
					}
					else
					{
						szV3PSendBuf[0] = ACK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
					}
				}
				if (memcmp((unsigned char *)&szRecvBuf[1],"COMM_SEND", strlen("COMM_SEND"))==0)
				{
					inOffSet = 1 + strlen("COMM_SEND");
					usResult = inCOMMS_Send_Terminal_Ext(&szRecvBuf[inOffSet]);
					if (usResult != d_OK)
					{
						szV3PSendBuf[0] = NAK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
						inCTOSS_V3PRS232Close();
						return 0;
					}
					else
					{
						szV3PSendBuf[0] = ACK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
					}
				}
				if (memcmp((unsigned char *)&szRecvBuf[1],"COMM_RECEVIE", strlen("COMM_RECEVIE"))==0)
				{
					memset(szRecvCommBuf,0x00,sizeof(szRecvCommBuf));
					inOffSet = 1 + strlen("COMM_RECEVIE");
					usResult = inCOMMS_Recieve_Terminal_Ext(szRecvCommBuf,&inRecvCommlen);
					if (usResult != d_OK)
					{
						szV3PSendBuf[0] = NAK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
						inCTOSS_V3PRS232Close();
						return 0;
					}
					else
					{
						//szV3PSendBuf[0] = ACK;
						//inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
						//CTOS_Delay(100);
						vdPCIDebug_HexPrintf("szRecvCommBuf",szRecvCommBuf,inRecvCommlen);
						inCTOSS_V3PRS232SendBuf(szRecvCommBuf, inRecvCommlen);
					}
				}
				if (memcmp((unsigned char *)&szRecvBuf[1],"COMM_DISCONNECT", strlen("COMM_DISCONNECT"))==0)
				{
					inOffSet = 1 + strlen("COMM_DISCONNECT");
					usResult = inCOMMS_Disconnect_Terminal_Ext();
					vdDebug_LogPrintf("COMM_DISCONNECT usResult[%d]", usResult);
					if (usResult != d_OK)
					{
						szV3PSendBuf[0] = NAK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
						//return 0;
					}
					else
					{
						szV3PSendBuf[0] = ACK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
						//return 0;
					}
				}


				
				
				if (memcmp((unsigned char *)&szRecvBuf[1],"END_TRX", strlen("END_TRX"))==0)
				{
					szV3PSendBuf[0] = ACK;
					inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
					inCTOSS_V3PRS232Close();
					vdDebug_LogPrintf("END_TRX ACK");

					return 0;				
				}



				if ((memcmp((unsigned char *)&szRecvBuf[1],"UPD_TRNS", strlen("UPD_TRNS"))==0))
				{

				    vdDebug_LogPrintf("UPD_TRNS");
					

					pszPtr = NULL;
					pszPtr = (char*)memchr(szRecvBuf, '|', inRecvlen); // check STX
					if (NULL == pszPtr)
					{
						//sometime the response error, may can not get |, 
						//pszPtr will be NULL, copy will cause crash
						// return d_NO;
					}
					else
					{
					
						szV3PSendBuf[0] = ACK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);

						
						vdDebug_LogPrintf("Send ACK End");

						memset( &srTransRec, 0x00, sizeof(TRANS_DATA_TABLE));
						memcpy(&srTransRec, &pszPtr[1], sizeof(TRANS_DATA_TABLE)); 

						inRet = inCTOS_SaveBatchTxn();
						vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf inRet[%d]", inRet);
					    //if(d_OK != inRet)
					       // return inRet;

					    inRet = inCTOS_UpdateAccumTotal();
						vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf inRet[%d]", inRet);
					    //if(d_OK != inRet)
					        //return inRet;
						
						
					}				
				}

				
				vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf");
				memset(szRecvBuf,0x00,sizeof(szRecvBuf));
				usResult = inCTOSS_V3PRS232RecvBuf_EXT(szRecvBuf, &inRecvlen, 500);
				//CTOS_LCDTPrintXY(1, 10, "APDU OUT...");
				
				//inPrintISOPacket(VS_FALSE, szRecvBuf, inRecvlen);
				DebugAddHEX("inCTOSS_V3PRS232RecvBuf", szRecvBuf, inRecvlen);
				
			}while(1);

			inCTOSS_V3PRS232Close();
		}
	}
}



int inNETS_DataSyncLogon_Terminal_EXT(void)
{
	CTLS_Trans strCTLSTrans;
	CTLS_TransData strCTLSTransData;
	unsigned char *pszPtr = NULL;
	USHORT usResult = 0;

	char szV3PSendBuf[1024+1] = {0};
	int inOffSet = 0;
	unsigned char szRecvBuf[1024+1] = {0};
	int inRecvlen = 0;
	unsigned char uszDataBuffer[10240+1] = {0};
	unsigned char uszFileBuffer[10240+1] = {0};
	char  szSystemCmdPath[250];
	unsigned char szRecvCommBuf[2048+1] = {0};
	int inRecvCommlen = 0; 
    char szFileName[20];

	vdDebug_LogPrintf("inNETS_Purchase_USB..strTCT.byPinPadType=[%d].strTCT.byPinPadMode=[%d].strTCT.byPinPadPort=[%d]..",strTCT.byPinPadType,strTCT.byPinPadMode,strTCT.byPinPadPort);

	if(strTCT.byPinPadMode == 0)
	{
		if(strTCT.byPinPadType == 4)
		{
			char szV3PSendBuf[1024+1] = {0};
			int inOffSet = 0;
			char szRecvBuf[1024+1];
			int inRecvlen = 0;
			int status;

			vdDebug_LogPrintf("LOGON_DATA_SYNC  CMD");
			//CTOS_LCDTPrintAligned(8, "SYNC DATA TO PINPAD", d_LCD_ALIGNLEFT);
			
			vdDebug_LogPrintf("strTCT.byPinPadPort[%d]", strTCT.byPinPadPort);
			
			vdCTOSS_SetV3PRS232Port(strTCT.byPinPadPort);
			inCTOSS_V3PRS232Open(strTCT.inPPBaudRate, 'N', 8, 1);

//////////////////////////////////////////////////////////////////////////////
/////////1111111111111111111
//////////////////////////////////////////////////////////////////////////////
			memset(szV3PSendBuf, 0x00, sizeof(szV3PSendBuf));
			inOffSet = 0;
			szV3PSendBuf[inOffSet] = STX;
			inOffSet += 1;
			memcpy(&szV3PSendBuf[inOffSet], "LOGON_DATA_SYNC", strlen("LOGON_DATA_SYNC"));
			inOffSet += strlen("LOGON_DATA_SYNC");
			szV3PSendBuf[inOffSet] = '|';
			inOffSet += 1;

			memset(&strCTLSTrans, 0x00, sizeof(CTLS_Trans));
			memcpy(strCTLSTrans.szAmount, srTransRec.szBaseAmount, 7);
			
			memcpy(&szV3PSendBuf[inOffSet], &strCTLSTrans, sizeof(CTLS_Trans));
			inOffSet += sizeof(CTLS_Trans);

			szV3PSendBuf[inOffSet] = ETX;
			inOffSet += 1;			
			szV3PSendBuf[inOffSet] = (char) wub_lrc(&(szV3PSendBuf[1]), inOffSet-1);
			inOffSet += 1;
			inCTOSS_V3PRS232SendBuf(szV3PSendBuf, inOffSet);	

			
			memset(szRecvBuf,0x00,sizeof(szRecvBuf));
			usResult = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, 500);
			vdDebug_LogPrintf("inCTOSS_V3PRS232RecvACK usResult[%d] szRecvBuf[%d]", usResult, szRecvBuf[0]);
			DebugAddHEX("inCTOSS_V3PRS232RecvACK", szRecvBuf, inRecvlen);
///////////////////////////////////////////////////////////////////////////
/* 1 above buff sent to PINPAD sharls_EXT, sharls_EXT return ACK*/
///////////////////////////////////////////////////////////////////////////

			//for stable need V5S_Nets send ACK first
			memset(szRecvBuf,0x00,sizeof(szRecvBuf));
			usResult = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, 500);
			vdDebug_LogPrintf("inCTOSS_V3PRS232RecvACK usResult[%d] szRecvBuf[%d]", usResult, szRecvBuf[0]);
			DebugAddHEX("inCTOSS_V3PRS232RecvACK", szRecvBuf, inRecvlen);


////////////////////////////////////////////////////////////////////////////////
///////////////222222222222222222222222222
////////////////////////////////////////////////////////////////////////////////
            //CTOS_Delay(1000);
			memset(szFileName, 0x00, sizeof(szFileName));
			strcpy(szFileName, "TERMINAL.S3DB");
			inSendSyncData(szFileName);
//////////////////////////////////////////////////////////////////////////////////////////
/*2 above directly send to PINPAD V5S_nets*/
//////////////////////////////////////////////////////////////////////////////////////////


			//CTOS_Delay(1000);
			memset(szFileName, 0x00, sizeof(szFileName));
			strcpy(szFileName, "NETS.S3DB");
			inSendSyncData(szFileName);

			//CTOS_Delay(1000);
			memset(szFileName, 0x00, sizeof(szFileName));
			strcpy(szFileName, "ENV.S3DB");
			inSendSyncData(szFileName);

			memset(szFileName, 0x00, sizeof(szFileName));
			strcpy(szFileName, "NFP.S3DB");
			inSendSyncData(szFileName);

			//CTOS_Delay(1000);
			//memset(szFileName, 0x00, sizeof(szFileName));
			//strcpy(szFileName, "V5S_NETS.S3DB");
			//inSendSyncAPPData(szFileName);

			//CTOS_Delay(1000);
			//memset(szFileName, 0x00, sizeof(szFileName));
			//strcpy(szFileName, "NETSFLEXI.S3DB");
			//inSendSyncAPPData(szFileName);
//////////////////////////////////////////////////////////////////////////////
/////////0000000000000000000000000000000000000000000
//////////////////////////////////////////////////////////////////////////////
			//CTOS_Delay(1000);
			memset(szV3PSendBuf, 0x00, sizeof(szV3PSendBuf));
			inOffSet = 0;
			szV3PSendBuf[inOffSet] = STX;
			inOffSet += 1;
			memcpy(&szV3PSendBuf[inOffSet], "END_LOGON_DATA_SYNC", strlen("END_LOGON_DATA_SYNC"));
			inOffSet += strlen("END_LOGON_DATA_SYNC");
			szV3PSendBuf[inOffSet] = '|';
			inOffSet += 1;

			szV3PSendBuf[inOffSet] = ETX;
			inOffSet += 1;			
			szV3PSendBuf[inOffSet] = (char) wub_lrc(&(szV3PSendBuf[1]), inOffSet-1);
			inOffSet += 1;
			inCTOSS_V3PRS232SendBuf(szV3PSendBuf, inOffSet);	

			
			memset(szRecvBuf,0x00,sizeof(szRecvBuf));
			usResult = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, 500);
			vdDebug_LogPrintf("inCTOSS_V3PRS232RecvACK usResult[%d] szRecvBuf[%d]", usResult, szRecvBuf[0]);
			DebugAddHEX("inCTOSS_V3PRS232RecvACK", szRecvBuf, inRecvlen);
///////////////////////////////////////////////////////////////////////////
/* 0 above directly send to PINPAD V5S_nets*/
///////////////////////////////////////////////////////////////////////////

		    inCTOSS_V3PRS232Close();
		}
	}
}



int inNETS_DataSyncPurchase_Terminal_EXT(TRANS_DATA_TABLE srTranstmpRec)
{
	CTLS_Trans strCTLSTrans;
	CTLS_TransData strCTLSTransData;
	unsigned char *pszPtr = NULL;
	USHORT usResult = 0;

	char szV3PSendBuf[1024+1] = {0};
	int inOffSet = 0;
	unsigned char szRecvBuf[1024+1] = {0};
	int inRecvlen = 0;
	unsigned char uszDataBuffer[10240+1] = {0};
	unsigned char uszFileBuffer[10240+1] = {0};
	char  szSystemCmdPath[250];
	unsigned char szRecvCommBuf[2048+1] = {0};
	int inRecvCommlen = 0; 
    char szFileName[20];
	ACCUM_REC srAccumRec;
    STRUCT_FILE_SETTING strFile;

	vdDebug_LogPrintf("inNETS_DataSyncPurchase_Terminal_EXT..strTCT.byPinPadType=[%d].strTCT.byPinPadMode=[%d].strTCT.byPinPadPort=[%d]..",strTCT.byPinPadType,strTCT.byPinPadMode,strTCT.byPinPadPort);

	if(strTCT.byPinPadMode == 0)
	{
		if(strTCT.byPinPadType == 4)
		{
			char szV3PSendBuf[1024+1] = {0};
			int inOffSet = 0;
			char szRecvBuf[1024+1];
			int inRecvlen = 0;
			int status;

			vdDebug_LogPrintf("LOGON_DATA_SYNC  CMD");
			//CTOS_LCDTPrintAligned(8, "SYNC DATA TO PINPAD", d_LCD_ALIGNLEFT);
			
			vdDebug_LogPrintf("strTCT.byPinPadPort[%d]", strTCT.byPinPadPort);
			
			vdCTOSS_SetV3PRS232Port(strTCT.byPinPadPort);
			inCTOSS_V3PRS232Open(strTCT.inPPBaudRate, 'N', 8, 1);

//////////////////////////////////////////////////////////////////////////////
/////////1111111111111111111
//////////////////////////////////////////////////////////////////////////////
			memset(szV3PSendBuf, 0x00, sizeof(szV3PSendBuf));
			inOffSet = 0;
			szV3PSendBuf[inOffSet] = STX;
			inOffSet += 1;
			memcpy(&szV3PSendBuf[inOffSet], "LOGON_DATA_SYNC", strlen("LOGON_DATA_SYNC"));
			inOffSet += strlen("LOGON_DATA_SYNC");
			szV3PSendBuf[inOffSet] = '|';
			inOffSet += 1;

			memset(&strCTLSTrans, 0x00, sizeof(CTLS_Trans));
			memcpy(strCTLSTrans.szAmount, srTransRec.szBaseAmount, 7);
			
			memcpy(&szV3PSendBuf[inOffSet], &strCTLSTrans, sizeof(CTLS_Trans));
			inOffSet += sizeof(CTLS_Trans);

			szV3PSendBuf[inOffSet] = ETX;
			inOffSet += 1;			
			szV3PSendBuf[inOffSet] = (char) wub_lrc(&(szV3PSendBuf[1]), inOffSet-1);
			inOffSet += 1;
			inCTOSS_V3PRS232SendBuf(szV3PSendBuf, inOffSet);	

			
			memset(szRecvBuf,0x00,sizeof(szRecvBuf));
			usResult = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, 500);
			vdDebug_LogPrintf("inCTOSS_V3PRS232RecvACK usResult[%d] szRecvBuf[%d]", usResult, szRecvBuf[0]);
			DebugAddHEX("inCTOSS_V3PRS232RecvACK", szRecvBuf, inRecvlen);
///////////////////////////////////////////////////////////////////////////
/* 1 above buff sent to PINPAD sharls_EXT, sharls_EXT return ACK*/
///////////////////////////////////////////////////////////////////////////

			//for stable need V5S_Nets send ACK first
			memset(szRecvBuf,0x00,sizeof(szRecvBuf));
			usResult = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, 500);
			vdDebug_LogPrintf("inCTOSS_V3PRS232RecvACK usResult[%d] szRecvBuf[%d]", usResult, szRecvBuf[0]);
			DebugAddHEX("inCTOSS_V3PRS232RecvACK", szRecvBuf, inRecvlen);


////////////////////////////////////////////////////////////////////////////////
///////////////222222222222222222222222222
////////////////////////////////////////////////////////////////////////////////
            //CTOS_Delay(1000);
			memset(szFileName, 0x00, sizeof(szFileName));
			strcpy(szFileName, "TERMINAL.S3DB");
			inSendSyncData(szFileName);
//////////////////////////////////////////////////////////////////////////////////////////
/*2 above directly send to PINPAD V5S_nets*/
//////////////////////////////////////////////////////////////////////////////////////////


			//CTOS_Delay(1000);
			memset(szFileName, 0x00, sizeof(szFileName));
			strcpy(szFileName, "NETS.S3DB");
			inSendSyncData(szFileName);

			//CTOS_Delay(1000);
			memset(szFileName, 0x00, sizeof(szFileName));
			strcpy(szFileName, "ENV.S3DB");
			inSendSyncData(szFileName);

			//CTOS_Delay(1000);
			memset(szFileName, 0x00, sizeof(szFileName));
			strcpy(szFileName, "V5S_NETS.S3DB");
			inSendSyncAPPData(szFileName);

			//CTOS_Delay(1000);
			memset(szFileName, 0x00, sizeof(szFileName));
			strcpy(szFileName, "NETSFLEXI.S3DB");
			inSendSyncAPPData(szFileName);

			//memset(&srAccumRec, 0x00, sizeof(ACCUM_REC));
    		//memset(&strFile,0,sizeof(strFile));
    		//vdCTOS_GetAccumName(&strFile, &srAccumRec);
			memset(szFileName,0x00,sizeof(szFileName));
			sprintf(szFileName, "ACC%02d%02d.total"
                                , srTranstmpRec.HDTid
                                , srTranstmpRec.MITid);
			inSendSyncAPPData(szFileName);
//////////////////////////////////////////////////////////////////////////////
/////////0000000000000000000000000000000000000000000
//////////////////////////////////////////////////////////////////////////////
			//CTOS_Delay(1000);
			memset(szV3PSendBuf, 0x00, sizeof(szV3PSendBuf));
			inOffSet = 0;
			szV3PSendBuf[inOffSet] = STX;
			inOffSet += 1;
			memcpy(&szV3PSendBuf[inOffSet], "END_LOGON_DATA_SYNC", strlen("END_LOGON_DATA_SYNC"));
			inOffSet += strlen("END_LOGON_DATA_SYNC");
			szV3PSendBuf[inOffSet] = '|';
			inOffSet += 1;

			szV3PSendBuf[inOffSet] = ETX;
			inOffSet += 1;			
			szV3PSendBuf[inOffSet] = (char) wub_lrc(&(szV3PSendBuf[1]), inOffSet-1);
			inOffSet += 1;
			inCTOSS_V3PRS232SendBuf(szV3PSendBuf, inOffSet);	

			
			memset(szRecvBuf,0x00,sizeof(szRecvBuf));
			usResult = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, 500);
			vdDebug_LogPrintf("inCTOSS_V3PRS232RecvACK usResult[%d] szRecvBuf[%d]", usResult, szRecvBuf[0]);
			DebugAddHEX("inCTOSS_V3PRS232RecvACK", szRecvBuf, inRecvlen);
///////////////////////////////////////////////////////////////////////////
/* 0 above directly send to PINPAD V5S_nets*/
///////////////////////////////////////////////////////////////////////////

		    inCTOSS_V3PRS232Close();
		}
	}
}


int inNETS_DataSyncSettle_Terminal_EXT(void)
{
	CTLS_Trans strCTLSTrans;
	CTLS_TransData strCTLSTransData;
	unsigned char *pszPtr = NULL;
	USHORT usResult = 0;

	char szV3PSendBuf[1024+1] = {0};
	int inOffSet = 0;
	unsigned char szRecvBuf[1024+1] = {0};
	int inRecvlen = 0;
	unsigned char uszDataBuffer[10240+1] = {0};
	unsigned char uszFileBuffer[10240+1] = {0};
	char  szSystemCmdPath[250];
	unsigned char szRecvCommBuf[2048+1] = {0};
	int inRecvCommlen = 0; 
    char szFileName[20];

	vdDebug_LogPrintf("inNETS_DataSyncSettle_Terminal_EXT..strTCT.byPinPadType=[%d].strTCT.byPinPadMode=[%d].strTCT.byPinPadPort=[%d]..",strTCT.byPinPadType,strTCT.byPinPadMode,strTCT.byPinPadPort);

	if(strTCT.byPinPadMode == 0)
	{
		if(strTCT.byPinPadType == 4)
		{
			char szV3PSendBuf[1024+1] = {0};
			int inOffSet = 0;
			char szRecvBuf[1024+1];
			int inRecvlen = 0;
			int status;

			vdDebug_LogPrintf("inNETS_DataSyncSettle_Terminal_EXT  CMD");
			//CTOS_LCDTPrintAligned(8, "SYNC DATA TO PINPAD", d_LCD_ALIGNLEFT);
			
			vdDebug_LogPrintf("strTCT.byPinPadPort[%d]", strTCT.byPinPadPort);
			
			vdCTOSS_SetV3PRS232Port(strTCT.byPinPadPort);
			inCTOSS_V3PRS232Open(strTCT.inPPBaudRate, 'N', 8, 1);

//////////////////////////////////////////////////////////////////////////////
/////////1111111111111111111
//////////////////////////////////////////////////////////////////////////////
			memset(szV3PSendBuf, 0x00, sizeof(szV3PSendBuf));
			inOffSet = 0;
			szV3PSendBuf[inOffSet] = STX;
			inOffSet += 1;
			memcpy(&szV3PSendBuf[inOffSet], "LOGON_DATA_SYNC", strlen("LOGON_DATA_SYNC"));
			inOffSet += strlen("LOGON_DATA_SYNC");
			szV3PSendBuf[inOffSet] = '|';
			inOffSet += 1;

			memset(&strCTLSTrans, 0x00, sizeof(CTLS_Trans));
			memcpy(strCTLSTrans.szAmount, srTransRec.szBaseAmount, 7);
			
			memcpy(&szV3PSendBuf[inOffSet], &strCTLSTrans, sizeof(CTLS_Trans));
			inOffSet += sizeof(CTLS_Trans);

			szV3PSendBuf[inOffSet] = ETX;
			inOffSet += 1;			
			szV3PSendBuf[inOffSet] = (char) wub_lrc(&(szV3PSendBuf[1]), inOffSet-1);
			inOffSet += 1;
			inCTOSS_V3PRS232SendBuf(szV3PSendBuf, inOffSet);	

			
			memset(szRecvBuf,0x00,sizeof(szRecvBuf));
			usResult = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, 500);
			vdDebug_LogPrintf("inCTOSS_V3PRS232RecvACK usResult[%d] szRecvBuf[%d]", usResult, szRecvBuf[0]);
			DebugAddHEX("inCTOSS_V3PRS232RecvACK", szRecvBuf, inRecvlen);
///////////////////////////////////////////////////////////////////////////
/* 1 above buff sent to PINPAD sharls_EXT, sharls_EXT return ACK*/
///////////////////////////////////////////////////////////////////////////

			//for stable need V5S_Nets send ACK first
			memset(szRecvBuf,0x00,sizeof(szRecvBuf));
			usResult = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, 500);
			vdDebug_LogPrintf("inCTOSS_V3PRS232RecvACK usResult[%d] szRecvBuf[%d]", usResult, szRecvBuf[0]);
			DebugAddHEX("inCTOSS_V3PRS232RecvACK", szRecvBuf, inRecvlen);


////////////////////////////////////////////////////////////////////////////////
///////////////222222222222222222222222222
////////////////////////////////////////////////////////////////////////////////
            //CTOS_Delay(1000);
			memset(szFileName, 0x00, sizeof(szFileName));
			strcpy(szFileName, "TERMINAL.S3DB");
			inSendSyncData(szFileName);
//////////////////////////////////////////////////////////////////////////////////////////
/*2 above directly send to PINPAD V5S_nets*/
//////////////////////////////////////////////////////////////////////////////////////////


			//CTOS_Delay(1000);
			memset(szFileName, 0x00, sizeof(szFileName));
			strcpy(szFileName, "NETS.S3DB");
			inSendSyncData(szFileName);

			//CTOS_Delay(1000);
			memset(szFileName, 0x00, sizeof(szFileName));
			strcpy(szFileName, "ENV.S3DB");
			inSendSyncData(szFileName);

			//CTOS_Delay(1000);
			memset(szFileName, 0x00, sizeof(szFileName));
			strcpy(szFileName, "V5S_NETS.S3DB");
			inSendSyncAPPData(szFileName);

			//CTOS_Delay(1000);
			memset(szFileName, 0x00, sizeof(szFileName));
			strcpy(szFileName, "NETSFLEXI.S3DB");
			inSendSyncAPPData(szFileName);

			//pass 
			inSendSyncTransRecData(srTransRec);
//////////////////////////////////////////////////////////////////////////////
/////////0000000000000000000000000000000000000000000
//////////////////////////////////////////////////////////////////////////////
			//CTOS_Delay(1000);
			memset(szV3PSendBuf, 0x00, sizeof(szV3PSendBuf));
			inOffSet = 0;
			szV3PSendBuf[inOffSet] = STX;
			inOffSet += 1;
			memcpy(&szV3PSendBuf[inOffSet], "END_LOGON_DATA_SYNC", strlen("END_LOGON_DATA_SYNC"));
			inOffSet += strlen("END_LOGON_DATA_SYNC");
			szV3PSendBuf[inOffSet] = '|';
			inOffSet += 1;

			szV3PSendBuf[inOffSet] = ETX;
			inOffSet += 1;			
			szV3PSendBuf[inOffSet] = (char) wub_lrc(&(szV3PSendBuf[1]), inOffSet-1);
			inOffSet += 1;
			inCTOSS_V3PRS232SendBuf(szV3PSendBuf, inOffSet);	

			
			memset(szRecvBuf,0x00,sizeof(szRecvBuf));
			usResult = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, 500);
			vdDebug_LogPrintf("inCTOSS_V3PRS232RecvACK usResult[%d] szRecvBuf[%d]", usResult, szRecvBuf[0]);
			DebugAddHEX("inCTOSS_V3PRS232RecvACK", szRecvBuf, inRecvlen);
///////////////////////////////////////////////////////////////////////////
/* 0 above directly send to PINPAD V5S_nets*/
///////////////////////////////////////////////////////////////////////////

		    inCTOSS_V3PRS232Close();
		}
	}
}



int inNPX_DataSyncSettle_Terminal_EXT(void)
{
	CTLS_Trans strCTLSTrans;
	CTLS_TransData strCTLSTransData;
	unsigned char *pszPtr = NULL;
	USHORT usResult = 0;

	char szV3PSendBuf[1024+1] = {0};
	int inOffSet = 0;
	unsigned char szRecvBuf[1024+1] = {0};
	int inRecvlen = 0;
	unsigned char uszDataBuffer[10240+1] = {0};
	unsigned char uszFileBuffer[10240+1] = {0};
	char  szSystemCmdPath[250];
	unsigned char szRecvCommBuf[2048+1] = {0};
	int inRecvCommlen = 0; 
    char szFileName[20];

	vdDebug_LogPrintf("inNPX_DataSyncSettle_Terminal_EXT..strTCT.byPinPadType=[%d].strTCT.byPinPadMode=[%d].strTCT.byPinPadPort=[%d]..",strTCT.byPinPadType,strTCT.byPinPadMode,strTCT.byPinPadPort);

	if(strTCT.byPinPadMode == 0)
	{
		if(strTCT.byPinPadType == 4)
		{
			char szV3PSendBuf[1024+1] = {0};
			int inOffSet = 0;
			char szRecvBuf[1024+1];
			int inRecvlen = 0;
			int status;

			vdDebug_LogPrintf("inNPX_DataSyncSettle_Terminal_EXT  CMD");
			//CTOS_LCDTPrintAligned(8, "SYNC DATA TO PINPAD", d_LCD_ALIGNLEFT);
			
			vdDebug_LogPrintf("strTCT.byPinPadPort[%d]", strTCT.byPinPadPort);
			
			vdCTOSS_SetV3PRS232Port(strTCT.byPinPadPort);
			inCTOSS_V3PRS232Open(strTCT.inPPBaudRate, 'N', 8, 1);

//////////////////////////////////////////////////////////////////////////////
/////////1111111111111111111
//////////////////////////////////////////////////////////////////////////////
			memset(szV3PSendBuf, 0x00, sizeof(szV3PSendBuf));
			inOffSet = 0;
			szV3PSendBuf[inOffSet] = STX;
			inOffSet += 1;
			memcpy(&szV3PSendBuf[inOffSet], "LOGON_DATA_SYNC", strlen("LOGON_DATA_SYNC"));
			inOffSet += strlen("LOGON_DATA_SYNC");
			szV3PSendBuf[inOffSet] = '|';
			inOffSet += 1;

			memset(&strCTLSTrans, 0x00, sizeof(CTLS_Trans));
			memcpy(strCTLSTrans.szAmount, srTransRec.szBaseAmount, 7);
			
			memcpy(&szV3PSendBuf[inOffSet], &strCTLSTrans, sizeof(CTLS_Trans));
			inOffSet += sizeof(CTLS_Trans);

			szV3PSendBuf[inOffSet] = ETX;
			inOffSet += 1;			
			szV3PSendBuf[inOffSet] = (char) wub_lrc(&(szV3PSendBuf[1]), inOffSet-1);
			inOffSet += 1;
			inCTOSS_V3PRS232SendBuf(szV3PSendBuf, inOffSet);	

			
			memset(szRecvBuf,0x00,sizeof(szRecvBuf));
			usResult = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, 500);
			vdDebug_LogPrintf("inCTOSS_V3PRS232RecvACK usResult[%d] szRecvBuf[%d]", usResult, szRecvBuf[0]);
			DebugAddHEX("inCTOSS_V3PRS232RecvACK", szRecvBuf, inRecvlen);
///////////////////////////////////////////////////////////////////////////
/* 1 above buff sent to PINPAD sharls_EXT, sharls_EXT return ACK*/
///////////////////////////////////////////////////////////////////////////

			//for stable need V5S_Nets send ACK first
			memset(szRecvBuf,0x00,sizeof(szRecvBuf));
			usResult = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, 500);
			vdDebug_LogPrintf("inCTOSS_V3PRS232RecvACK usResult[%d] szRecvBuf[%d]", usResult, szRecvBuf[0]);
			DebugAddHEX("inCTOSS_V3PRS232RecvACK", szRecvBuf, inRecvlen);



//////////////////////////////////////////////////////////////////////////////////////////
/*2 above directly send to PINPAD V5S_nets*/
//////////////////////////////////////////////////////////////////////////////////////////

			//pass 
			inSendSyncTransRecData(srTransRec);
//////////////////////////////////////////////////////////////////////////////
/////////0000000000000000000000000000000000000000000
//////////////////////////////////////////////////////////////////////////////
			//CTOS_Delay(1000);
			memset(szV3PSendBuf, 0x00, sizeof(szV3PSendBuf));
			inOffSet = 0;
			szV3PSendBuf[inOffSet] = STX;
			inOffSet += 1;
			memcpy(&szV3PSendBuf[inOffSet], "END_LOGON_DATA_SYNC", strlen("END_LOGON_DATA_SYNC"));
			inOffSet += strlen("END_LOGON_DATA_SYNC");
			szV3PSendBuf[inOffSet] = '|';
			inOffSet += 1;

			szV3PSendBuf[inOffSet] = ETX;
			inOffSet += 1;			
			szV3PSendBuf[inOffSet] = (char) wub_lrc(&(szV3PSendBuf[1]), inOffSet-1);
			inOffSet += 1;
			inCTOSS_V3PRS232SendBuf(szV3PSendBuf, inOffSet);	

			
			memset(szRecvBuf,0x00,sizeof(szRecvBuf));
			usResult = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, 500);
			vdDebug_LogPrintf("inCTOSS_V3PRS232RecvACK usResult[%d] szRecvBuf[%d]", usResult, szRecvBuf[0]);
			DebugAddHEX("inCTOSS_V3PRS232RecvACK", szRecvBuf, inRecvlen);
///////////////////////////////////////////////////////////////////////////
/* 0 above directly send to PINPAD V5S_nets*/
///////////////////////////////////////////////////////////////////////////

		    inCTOSS_V3PRS232Close();
		}
	}
}



int inNPX_DataSyncSale_Terminal_EXT(TRANS_DATA_TABLE srTranstmpRec)
{
	CTLS_Trans strCTLSTrans;
	CTLS_TransData strCTLSTransData;
	unsigned char *pszPtr = NULL;
	USHORT usResult = 0;

	char szV3PSendBuf[1024+1] = {0};
	int inOffSet = 0;
	unsigned char szRecvBuf[1024+1] = {0};
	int inRecvlen = 0;
	unsigned char uszDataBuffer[10240+1] = {0};
	unsigned char uszFileBuffer[10240+1] = {0};
	char  szSystemCmdPath[250];
	unsigned char szRecvCommBuf[2048+1] = {0};
	int inRecvCommlen = 0; 
    char szFileName[20];
	ACCUM_REC srAccumRec;
    STRUCT_FILE_SETTING strFile;

	vdDebug_LogPrintf("inNETS_DataSyncPurchase_Terminal_EXT..strTCT.byPinPadType=[%d].strTCT.byPinPadMode=[%d].strTCT.byPinPadPort=[%d]..",strTCT.byPinPadType,strTCT.byPinPadMode,strTCT.byPinPadPort);

	if(strTCT.byPinPadMode == 0)
	{
		if(strTCT.byPinPadType == 4)
		{
			char szV3PSendBuf[1024+1] = {0};
			int inOffSet = 0;
			char szRecvBuf[1024+1];
			int inRecvlen = 0;
			int status;

			vdDebug_LogPrintf("inNPX_DataSyncSale_Terminal_EXT  CMD");
			//CTOS_LCDTPrintAligned(8, "SYNC DATA TO PINPAD", d_LCD_ALIGNLEFT);
			
			vdDebug_LogPrintf("strTCT.byPinPadPort[%d]", strTCT.byPinPadPort);
			
			vdCTOSS_SetV3PRS232Port(strTCT.byPinPadPort);
			inCTOSS_V3PRS232Open(strTCT.inPPBaudRate, 'N', 8, 1);

//////////////////////////////////////////////////////////////////////////////
/////////1111111111111111111
//////////////////////////////////////////////////////////////////////////////
			memset(szV3PSendBuf, 0x00, sizeof(szV3PSendBuf));
			inOffSet = 0;
			szV3PSendBuf[inOffSet] = STX;
			inOffSet += 1;
			memcpy(&szV3PSendBuf[inOffSet], "NPX_DATA_SYNC", strlen("NPX_DATA_SYNC"));
			inOffSet += strlen("NPX_DATA_SYNC");
			szV3PSendBuf[inOffSet] = '|';
			inOffSet += 1;

			memset(&strCTLSTrans, 0x00, sizeof(CTLS_Trans));
			memcpy(strCTLSTrans.szAmount, srTransRec.szBaseAmount, 7);
			
			memcpy(&szV3PSendBuf[inOffSet], &strCTLSTrans, sizeof(CTLS_Trans));
			inOffSet += sizeof(CTLS_Trans);

			szV3PSendBuf[inOffSet] = ETX;
			inOffSet += 1;			
			szV3PSendBuf[inOffSet] = (char) wub_lrc(&(szV3PSendBuf[1]), inOffSet-1);
			inOffSet += 1;
			inCTOSS_V3PRS232SendBuf(szV3PSendBuf, inOffSet);	

			
			memset(szRecvBuf,0x00,sizeof(szRecvBuf));
			usResult = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, 500);
			vdDebug_LogPrintf("inCTOSS_V3PRS232RecvACK usResult[%d] szRecvBuf[%d]", usResult, szRecvBuf[0]);
			DebugAddHEX("inCTOSS_V3PRS232RecvACK", szRecvBuf, inRecvlen);
///////////////////////////////////////////////////////////////////////////
/* 1 above buff sent to PINPAD sharls_EXT, sharls_EXT return ACK*/
///////////////////////////////////////////////////////////////////////////

			//for stable need V5S_Nets send ACK first
			memset(szRecvBuf,0x00,sizeof(szRecvBuf));
			usResult = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, 500);
			vdDebug_LogPrintf("inCTOSS_V3PRS232RecvACK usResult[%d] szRecvBuf[%d]", usResult, szRecvBuf[0]);
			DebugAddHEX("inCTOSS_V3PRS232RecvACK", szRecvBuf, inRecvlen);


////////////////////////////////////////////////////////////////////////////////
///////////////222222222222222222222222222
////////////////////////////////////////////////////////////////////////////////

//////////////////////////////////////////////////////////////////////////////////////////
/*2 above directly send to PINPAD V5S_nets*/
//////////////////////////////////////////////////////////////////////////////////////////

			//CTOS_Delay(1000);
			memset(szFileName, 0x00, sizeof(szFileName));
			strcpy(szFileName, "V5S_VISAMASTER.S3DB");
			inSendSyncAPPData(szFileName);

			//CTOS_Delay(1000);
			memset(szFileName, 0x00, sizeof(szFileName));
			strcpy(szFileName, "NPXFLEXI.S3DB");
			inSendSyncAPPData(szFileName);

			//memset(&srAccumRec, 0x00, sizeof(ACCUM_REC));
    		//memset(&strFile,0,sizeof(strFile));
    		//vdCTOS_GetAccumName(&strFile, &srAccumRec);
			memset(szFileName,0x00,sizeof(szFileName));
			sprintf(szFileName, "ACC%02d%02d.total"
                                , srTranstmpRec.HDTid
                                , srTranstmpRec.MITid);
			inSendSyncAPPData(szFileName);
//////////////////////////////////////////////////////////////////////////////
/////////0000000000000000000000000000000000000000000
//////////////////////////////////////////////////////////////////////////////
			//CTOS_Delay(1000);
			memset(szV3PSendBuf, 0x00, sizeof(szV3PSendBuf));
			inOffSet = 0;
			szV3PSendBuf[inOffSet] = STX;
			inOffSet += 1;
			memcpy(&szV3PSendBuf[inOffSet], "END_LOGON_DATA_SYNC", strlen("END_LOGON_DATA_SYNC"));
			inOffSet += strlen("END_LOGON_DATA_SYNC");
			szV3PSendBuf[inOffSet] = '|';
			inOffSet += 1;

			szV3PSendBuf[inOffSet] = ETX;
			inOffSet += 1;			
			szV3PSendBuf[inOffSet] = (char) wub_lrc(&(szV3PSendBuf[1]), inOffSet-1);
			inOffSet += 1;
			inCTOSS_V3PRS232SendBuf(szV3PSendBuf, inOffSet);	

			
			memset(szRecvBuf,0x00,sizeof(szRecvBuf));
			usResult = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, 500);
			vdDebug_LogPrintf("inCTOSS_V3PRS232RecvACK usResult[%d] szRecvBuf[%d]", usResult, szRecvBuf[0]);
			DebugAddHEX("inCTOSS_V3PRS232RecvACK", szRecvBuf, inRecvlen);
///////////////////////////////////////////////////////////////////////////
/* 0 above directly send to PINPAD V5S_nets*/
///////////////////////////////////////////////////////////////////////////

		    inCTOSS_V3PRS232Close();
		}
	}
}



int inUpdateTerminalConfig(void)
{

	vdDebug_LogPrintf("inUpdateTerminalConfig");
	//vdSyncData();

	inTCTRead(1);
	vdDebug_LogPrintf("byPinPadType=[%d],byPinPadPort=[%d],byRS232ECRPort=[%d]",strTCT.byPinPadType,strTCT.byPinPadPort,strTCT.byRS232ECRPort);
    strTCT.byPinPadType = 4;
	strTCT.byPinPadPort=9;
	strTCT.byRS232ECRPort=1;
	strTCT.byTerminalType=3;
	//inTCTSave(1);//don't save byRS232ECRPort
	//inTCTSaveEx(1);
	
	vdDebug_LogPrintf("byPinPadType=[%d],byPinPadPort=[%d],byRS232ECRPort=[%d]",strTCT.byPinPadType,strTCT.byPinPadPort,strTCT.byRS232ECRPort);

	inTCTRead(1);

	
	vdDebug_LogPrintf("byPinPadType=[%d],byPinPadPort=[%d],byRS232ECRPort=[%d]",strTCT.byPinPadType,strTCT.byPinPadPort,strTCT.byRS232ECRPort);

	inTCTEXRead(1);
	strTCTEX.fEXT=0;
	strTCTEX.fHawkerMode=0;
	strTCTEX.inCFCRetailMode=2;
	inTCTEXSave(1);
	vdDebug_LogPrintf("inUpdateTerminalConfig End");
	vdSetECRTransactionFlg(0);
	

	return d_OK;
}



//////////////////////////////////////////////////////////////////////////////////////////

//above function for terminal side, terminal pass date to EXT pinpad /////////

///////////////////////////////////////////////////////////////////////////////






///////////////////////////////////////////////////////////////////////////////////

//below function for EXT pinpad side, EXT pinpad pass date to terminal/////////

///////////////////////////////////////////////////////////////////////////

int inUpdatePinPadConfig(void)
{

	vdDebug_LogPrintf("inUpdatePinPadConfig");
	//vdSyncData();

	inTCTRead(1);
	vdDebug_LogPrintf("byPinPadType=[%d],byPinPadPort=[%d],byRS232ECRPort=[%d]",strTCT.byPinPadType,strTCT.byPinPadPort,strTCT.byRS232ECRPort);
    strTCT.byPinPadType = 0;
	strTCT.byPinPadPort=8;
	strTCT.byRS232ECRPort=8;
	strTCT.byTerminalType=4;
	//inTCTSave(1);//don't save byRS232ECRPort
	//inTCTSaveEx(1);
	
	vdDebug_LogPrintf("byPinPadType=[%d],byPinPadPort=[%d],byRS232ECRPort=[%d]",strTCT.byPinPadType,strTCT.byPinPadPort,strTCT.byRS232ECRPort);

	inTCTRead(1);

	
	vdDebug_LogPrintf("byPinPadType=[%d],byPinPadPort=[%d],byRS232ECRPort=[%d]",strTCT.byPinPadType,strTCT.byPinPadPort,strTCT.byRS232ECRPort);

	inTCTEXRead(1);
	strTCTEX.fEXT=1;
	strTCTEX.fHawkerMode=1;
	strTCTEX.inCFCRetailMode=1;
	inTCTEXSave(1);
	vdDebug_LogPrintf("inUpdatePinPadConfig End");
	//inNDTRead(1);

	vdSetECRTransactionFlg(0);
	

	return d_OK;
}


int inReceiveSyncDataMulti_InEXT(void)
{
	CTLS_Trans strCTLSTrans;
	CTLS_TransData strCTLSTransData;
	unsigned char *pszPtr = NULL;
	USHORT usResult = 0;

	char szV3PSendBuf[1024+1] = {0};
	int inOffSet = 0;
	unsigned char szRecvBuf[55000+1] = {0};
	int inRecvlen = 0, inRecvlen1 = 0;
	unsigned char uszDataBuffer[55000+1] = {0};
	unsigned char uszFileBuffer[55000+1] = {0};
	char  szSystemCmdPath[250];
	unsigned char szRecvCommBuf[2048+1] = {0};
	int inRecvCommlen = 0; 
    char szFileName[200];
	int inRet = 0;


	memcpy(srTransRec.szBaseAmount, "\x00\x00\x00\x00\x10\x00", 6);

	//CTOS_LCDTClearDisplay();
	if (strTCTEX.fEXT == 1)
	{
		vdDisplayProcessing();
	}


	int status;

	vdDebug_LogPrintf("inReceiveSyncDataMulti...........");

	vdDebug_LogPrintf("strTCT.byRS232ECRPort[%d]", strTCT.byRS232ECRPort);

	vdCTOSS_SetV3PRS232Port(strTCT.byRS232ECRPort);
	inCTOSS_V3PRS232Open(strTCT.inPPBaudRate, 'N', 8, 1);

	//vdCTOSS_SetV3PRS232Port(strTCT.byPinPadPort);
	//inCTOSS_V3PRS232Open(strTCT.inPPBaudRate, 'N', 8, 1);
/////////////////////////////////////////////////////////////////////////
///////////22222222222222222 directly receive data from V5S_Nets
/////////////////////////////////////////////////////////////////////////
	szV3PSendBuf[0] = ACK;
    inRet = inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
	vdDebug_LogPrintf("inCTOSS_V3PRS232SendBuf inRet[%d]", inRet);

	//CTOS_LCDTPrintAligned(8, "RECV DATA...", d_LCD_ALIGNLEFT);
	
	while (1)
	{
	
	//CTOS_Delay(1000);
	
	memset(szRecvBuf,0x00,sizeof(szRecvBuf));
	usResult = inCTOSS_V3PRS232RecvBuf_EXT(szRecvBuf, &inRecvlen, 500);
	
	vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf_EXT usResult[%d] inRecvlen[%d]", usResult, inRecvlen);
	DebugAddHEX("inCTOSS_V3PRS232RecvBuf", szRecvBuf, inRecvlen);


	if ((memcmp((unsigned char *)&szRecvBuf[1],"SYNC_DATA", strlen("SYNC_DATA"))==0)||(memcmp((unsigned char *)&szRecvBuf[0],"SYNC_DATA", strlen("SYNC_DATA"))==0))
	{

	    vdDebug_LogPrintf("SYNC_DATA..........");
		

		pszPtr = NULL;
		pszPtr = (char*)memchr(szRecvBuf, '|', inRecvlen); // check STX
		if (NULL == pszPtr)
		{
			//sometime the response error, may can not get |, 
			//pszPtr will be NULL, copy will cause crash
			// return d_NO;
		}
		else
		{
		
			szV3PSendBuf[0] = ACK;
			//vdDebug_LogPrintf("Send ACK Start");
			inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);

			
			vdDebug_LogPrintf("Send ACK End");

			FILE *ptr_myfile;
	
			memset(&strFileTransfer_Ext, 0x00, sizeof(STRUCT_FILE_TRANSFER_EXT));	
			memcpy(&strFileTransfer_Ext, &pszPtr[1], sizeof(STRUCT_FILE_TRANSFER_EXT)); // copy transaction amount?

			unlink((char *)strFileTransfer_Ext.uszFileTransferName);
		
			ptr_myfile = fopen(strFileTransfer_Ext.uszFileTransferName, "wb");
			if (!ptr_myfile)
			{
				vdDebug_LogPrintf("Unable to open file!");
				//inCTOSS_V3PRS232Close();
				//return 1;
				//can't return must wait V3 send END, if not, V3P will exit first, but V3 still process something
				continue;
			}
		
			fseek(ptr_myfile, 0L, SEEK_SET);

			vdDebug_LogPrintf("uszFileTransferName=[%s]",strFileTransfer_Ext.uszFileTransferName);
			vdDebug_LogPrintf("inFileTransferNameSize=[%d]",strFileTransfer_Ext.inFileTransferNameSize);
			
			memset(uszDataBuffer, 0x00, sizeof(uszDataBuffer));
			vdDebug_LogPrintf("xmodemReceive start");
			//usResult = xmodemReceive((unsigned char *)uszDataBuffer, strFileTransfer_Ext.inFileTransferNameSize);


			vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf");
			memset(szRecvBuf,0x00,sizeof(szRecvBuf));
			memset(uszFileBuffer, 0x00, sizeof(uszFileBuffer));
			
			usResult = inCTOSS_V3PRS232RecvBuf_EXT(uszDataBuffer, &inRecvlen, 5000);
			vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf usResult[%d] inRecvlen[%d]", usResult, inRecvlen);
			if (usResult != d_OK)
			{
				//inCTOSS_V3PRS232Close();
				//return usResult;
				//can't return must wait V3 send END, if not, V3P will exit first, but V3 still process something
				continue;
			}
			while (1)
			{
				if (strFileTransfer_Ext.inFileTransferNameSize > inRecvlen-3)
				{
					inRecvlen1 = 0;
					usResult = inCTOSS_V3PRS232RecvBuf_EXT(&uszDataBuffer[inRecvlen], &inRecvlen1, 5000);
					inRecvlen += inRecvlen1;
					vdDebug_LogPrintf(" inFileTransferNameSize[%d] inRecvlen[%d],inRecvlen1=[%d]", strFileTransfer_Ext.inFileTransferNameSize, inRecvlen,inRecvlen1);
				}
				else
					break;
			}

			memcpy(uszFileBuffer, &uszDataBuffer[1], inRecvlen-3);
			//CTOS_LCDTPrintXY(1, 10, "APDU OUT...");
			vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf");
			//inPrintISOPacket(VS_FALSE, szRecvBuf, inRecvlen);
			DebugAddHEX("inCTOSS_V3PRS232RecvBuf", uszDataBuffer, inRecvlen);
			//DebugAddHEX("uszFileBuffer", uszFileBuffer, inRecvlen-3);

			
			szV3PSendBuf[0] = ACK;
			inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
			vdDebug_LogPrintf("Send ACK");
		
			//inCTOSS_V3PRS232Close();

			vdDebug_LogPrintf("xmodemReceive end usResult [%d]", usResult);
			fwrite(uszFileBuffer, strFileTransfer_Ext.inFileTransferNameSize, 1, ptr_myfile);
			fclose(ptr_myfile);
			vdDebug_LogPrintf("strFileTransfer_Ext.uszFileTransferName[%s]", strFileTransfer_Ext.uszFileTransferName);
			memset(szSystemCmdPath, 0x00, sizeof(szSystemCmdPath)); 			
			sprintf(szSystemCmdPath, "gzip -f -d %s", strFileTransfer_Ext.uszFileTransferName);
			system(szSystemCmdPath);

			//memset(szFileName, 0x00, sizeof(szFileName));
			//strncpy(szFileName, strFileTransfer_Ext.uszFileTransferName, strlen(strFileTransfer_Ext.uszFileTransferName) -3);
			//vdDebug_LogPrintf("szFileName[%s]", szFileName);
			
			//ShowBmpEx(0, 32, szFileName);

			//vdPrintReceipt(szFileName);

			//return 0;	
			
		}				
	}

	if ((memcmp((unsigned char *)&szRecvBuf[1],"TRANSREC_DATA", strlen("TRANSREC_DATA"))==0)||(memcmp((unsigned char *)&szRecvBuf[0],"TRANSREC_DATA", strlen("TRANSREC_DATA"))==0))
	{

	    vdDebug_LogPrintf("TRANSREC_DATA..........");
		

		pszPtr = NULL;
		pszPtr = (char*)memchr(szRecvBuf, '|', inRecvlen); // check STX
		if (NULL == pszPtr)
		{
			//sometime the response error, may can not get |, 
			//pszPtr will be NULL, copy will cause crash
			// return d_NO;
		}
		else
		{
		
			szV3PSendBuf[0] = ACK;
			//vdDebug_LogPrintf("Send ACK Start");
			inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);

			
			vdDebug_LogPrintf("Send ACK End");
	
			memset(&srTransRec, 0x00, sizeof(TRANS_DATA_TABLE));	
			memcpy(&srTransRec, &pszPtr[1], sizeof(TRANS_DATA_TABLE)); // copy transaction amount?

			//inCTOSS_SettlementClearAccumExt(srTransRec);
				
			
		}				
	}
	

		if ((memcmp((unsigned char *)&szRecvBuf[1],"END_LOGON_DATA_SYNC", strlen("END_LOGON_DATA_SYNC"))==0)||(memcmp((unsigned char *)&szRecvBuf[0],"END_LOGON_DATA_SYNC", strlen("END_LOGON_DATA_SYNC"))==0))
		{
			szV3PSendBuf[0] = ACK;
			inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
			vdDebug_LogPrintf("Send ACK End");
			break;
		}
	}

	inCTOSS_V3PRS232Close();
	
}


int inReceiveSyncData(void)
{
	CTLS_Trans strCTLSTrans;
	CTLS_TransData strCTLSTransData;
	unsigned char *pszPtr = NULL;
	USHORT usResult = 0;

	char szV3PSendBuf[1024+1] = {0};
	int inOffSet = 0;
	unsigned char szRecvBuf[1024+1] = {0};
	int inRecvlen = 0;
	unsigned char uszDataBuffer[15000+1] = {0};
	unsigned char uszFileBuffer[15000+1] = {0};
	char  szSystemCmdPath[250];
	unsigned char szRecvCommBuf[2048+1] = {0};
	int inRecvCommlen = 0; 
    char szFileName[200];
	int inRet = 0;


	memcpy(srTransRec.szBaseAmount, "\x00\x00\x00\x00\x10\x00", 6);

	CTOS_LCDTClearDisplay();
	


	int status;

	vdDebug_LogPrintf("LOGON_DATA_SYNC CMD");

	vdDebug_LogPrintf("strTCT.byRS232ECRPort[%d]", strTCT.byRS232ECRPort);

	vdCTOSS_SetV3PRS232Port(strTCT.byRS232ECRPort);
	inCTOSS_V3PRS232Open(strTCT.inPPBaudRate, 'N', 8, 1);

	//vdCTOSS_SetV3PRS232Port(strTCT.byPinPadPort);
	//inCTOSS_V3PRS232Open(strTCT.inPPBaudRate, 'N', 8, 1);
	CTOS_Delay(1000);

	
	szV3PSendBuf[0] = ACK;
	vdDebug_LogPrintf("ACK[%d]", szV3PSendBuf[0]);

    inRet = inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
	vdDebug_LogPrintf("inCTOSS_V3PRS232SendBuf inRet[%d]", inRet);
	CTOS_Delay(1000);
	
	memset(szRecvBuf,0x00,sizeof(szRecvBuf));
	usResult = inCTOSS_V3PRS232RecvBuf_EXT(szRecvBuf, &inRecvlen, 500);
	
	vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf_EXT usResult[%d] inRecvlen[%d]", usResult, inRecvlen);
	DebugAddHEX("inCTOSS_V3PRS232RecvBuf", szRecvBuf, inRecvlen);


	if ((memcmp((unsigned char *)&szRecvBuf[1],"SYNC_DATA", strlen("SYNC_DATA"))==0)||(memcmp((unsigned char *)&szRecvBuf[0],"SYNC_DATA", strlen("SYNC_DATA"))==0))
	{

	    vdDebug_LogPrintf("SYNC_DATA");
		

		pszPtr = NULL;
		pszPtr = (char*)memchr(szRecvBuf, '|', inRecvlen); // check STX
		if (NULL == pszPtr)
		{
			//sometime the response error, may can not get |, 
			//pszPtr will be NULL, copy will cause crash
			// return d_NO;
		}
		else
		{
		
			szV3PSendBuf[0] = ACK;
			//vdDebug_LogPrintf("Send ACK Start");
			inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);

			
			vdDebug_LogPrintf("Send ACK End");

			FILE *ptr_myfile;
	
			memset(&strFileTransfer_Ext, 0x00, sizeof(STRUCT_FILE_TRANSFER_EXT));	
			memcpy(&strFileTransfer_Ext, &pszPtr[1], sizeof(STRUCT_FILE_TRANSFER_EXT)); // copy transaction amount?

			unlink((char *)strFileTransfer_Ext.uszFileTransferName);
		
			ptr_myfile = fopen(strFileTransfer_Ext.uszFileTransferName, "wb");
			if (!ptr_myfile)
			{
				vdDebug_LogPrintf("Unable to open file!");
				return 1;
			}
		
			fseek(ptr_myfile, 0L, SEEK_SET);

			memset(uszDataBuffer, 0x00, sizeof(uszDataBuffer));
			vdDebug_LogPrintf("xmodemReceive start");
			//usResult = xmodemReceive((unsigned char *)uszDataBuffer, strFileTransfer_Ext.inFileTransferNameSize);


			vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf");
			memset(szRecvBuf,0x00,sizeof(szRecvBuf));
			memset(uszFileBuffer, 0x00, sizeof(uszFileBuffer));
			
			usResult = inCTOSS_V3PRS232RecvBuf_EXT(uszDataBuffer, &inRecvlen, 5000);
			vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf usResult[%d] inRecvlen[%d]", usResult, inRecvlen);

			memcpy(uszFileBuffer, &uszDataBuffer[1], inRecvlen-3);
			//CTOS_LCDTPrintXY(1, 10, "APDU OUT...");
			vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf");
			//inPrintISOPacket(VS_FALSE, szRecvBuf, inRecvlen);
			DebugAddHEX("inCTOSS_V3PRS232RecvBuf", uszDataBuffer, inRecvlen);
			//DebugAddHEX("uszFileBuffer", uszFileBuffer, inRecvlen-3);

			
			szV3PSendBuf[0] = ACK;
			inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
			vdDebug_LogPrintf("Send ACK");
		
			//inCTOSS_V3PRS232Close();

			vdDebug_LogPrintf("xmodemReceive end usResult [%d]", usResult);
			fwrite(uszFileBuffer, strFileTransfer_Ext.inFileTransferNameSize, 1, ptr_myfile);
			fclose(ptr_myfile);
			vdDebug_LogPrintf("strFileTransfer_Ext.uszFileTransferName[%s]", strFileTransfer_Ext.uszFileTransferName);
			memset(szSystemCmdPath, 0x00, sizeof(szSystemCmdPath)); 			
			sprintf(szSystemCmdPath, "gzip -f -d %s", strFileTransfer_Ext.uszFileTransferName);
			system(szSystemCmdPath);

			memset(szFileName, 0x00, sizeof(szFileName));
			strncpy(szFileName, strFileTransfer_Ext.uszFileTransferName, strlen(strFileTransfer_Ext.uszFileTransferName) -3);
			vdDebug_LogPrintf("szFileName[%s]", szFileName);
			
			//ShowBmpEx(0, 32, szFileName);

			//vdPrintReceipt(szFileName);

			//return 0;	
			
		}				
	}
		
}




int inReceiveSyncDataEx(void)
{
	CTLS_Trans strCTLSTrans;
	CTLS_TransData strCTLSTransData;
	unsigned char *pszPtr = NULL;
	USHORT usResult = 0;

	char szV3PSendBuf[1024+1] = {0};
	int inOffSet = 0;
	unsigned char szRecvBuf[1024+1] = {0};
	int inRecvlen = 0;
	unsigned char uszDataBuffer[15000+1] = {0};
	unsigned char uszFileBuffer[15000+1] = {0};
	char  szSystemCmdPath[250];
	unsigned char szRecvCommBuf[2048+1] = {0};
	int inRecvCommlen = 0; 
    char szFileName[200];
	int inRet = 0;


	memcpy(srTransRec.szBaseAmount, "\x00\x00\x00\x00\x10\x00", 6);

	CTOS_LCDTClearDisplay();
	


	int status;

	vdDebug_LogPrintf("LOGON_DATA_SYNC CMD");

	vdDebug_LogPrintf("strTCT.byRS232ECRPort[%d]", strTCT.byRS232ECRPort);


	//vdCTOSS_SetV3PRS232Port(strTCT.byPinPadPort);
	//inCTOSS_V3PRS232Open(strTCT.inPPBaudRate, 'N', 8, 1);
	CTOS_Delay(1000);

	
	szV3PSendBuf[0] = ACK;
	vdDebug_LogPrintf("ACK[%d]", szV3PSendBuf[0]);

    inRet = inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
	vdDebug_LogPrintf("inCTOSS_V3PRS232SendBuf inRet[%d]", inRet);
	CTOS_Delay(1000);
	
	memset(szRecvBuf,0x00,sizeof(szRecvBuf));
	usResult = inCTOSS_V3PRS232RecvBuf_EXT(szRecvBuf, &inRecvlen, 500);
	
	vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf_EXT usResult[%d] inRecvlen[%d]", usResult, inRecvlen);
	DebugAddHEX("inCTOSS_V3PRS232RecvBuf", szRecvBuf, inRecvlen);


	if ((memcmp((unsigned char *)&szRecvBuf[1],"SYNC_DATA", strlen("SYNC_DATA"))==0)||(memcmp((unsigned char *)&szRecvBuf[0],"SYNC_DATA", strlen("SYNC_DATA"))==0))
	{

	    vdDebug_LogPrintf("SYNC_DATA");
		

		pszPtr = NULL;
		pszPtr = (char*)memchr(szRecvBuf, '|', inRecvlen); // check STX
		if (NULL == pszPtr)
		{
			//sometime the response error, may can not get |, 
			//pszPtr will be NULL, copy will cause crash
			// return d_NO;
		}
		else
		{
		
			szV3PSendBuf[0] = ACK;
			//vdDebug_LogPrintf("Send ACK Start");
			inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);

			
			vdDebug_LogPrintf("Send ACK End");

			FILE *ptr_myfile;
	
			memset(&strFileTransfer_Ext, 0x00, sizeof(STRUCT_FILE_TRANSFER_EXT));	
			memcpy(&strFileTransfer_Ext, &pszPtr[1], sizeof(STRUCT_FILE_TRANSFER_EXT)); // copy transaction amount?

			unlink((char *)strFileTransfer_Ext.uszFileTransferName);
		
			ptr_myfile = fopen(strFileTransfer_Ext.uszFileTransferName, "wb");
			if (!ptr_myfile)
			{
				vdDebug_LogPrintf("Unable to open file!");
				return 1;
			}
		
			fseek(ptr_myfile, 0L, SEEK_SET);

			memset(uszDataBuffer, 0x00, sizeof(uszDataBuffer));
			vdDebug_LogPrintf("xmodemReceive start");
			//usResult = xmodemReceive((unsigned char *)uszDataBuffer, strFileTransfer_Ext.inFileTransferNameSize);


			vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf");
			memset(szRecvBuf,0x00,sizeof(szRecvBuf));
			memset(uszFileBuffer, 0x00, sizeof(uszFileBuffer));
			
			usResult = inCTOSS_V3PRS232RecvBuf_EXT(uszDataBuffer, &inRecvlen, 5000);
			vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf usResult[%d] inRecvlen[%d]", usResult, inRecvlen);

			memcpy(uszFileBuffer, &uszDataBuffer[1], inRecvlen-3);
			//CTOS_LCDTPrintXY(1, 10, "APDU OUT...");
			vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf");
			//inPrintISOPacket(VS_FALSE, szRecvBuf, inRecvlen);
			DebugAddHEX("inCTOSS_V3PRS232RecvBuf", uszDataBuffer, inRecvlen);
			//DebugAddHEX("uszFileBuffer", uszFileBuffer, inRecvlen-3);

			
			szV3PSendBuf[0] = ACK;
			inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
			vdDebug_LogPrintf("Send ACK");
		
			//inCTOSS_V3PRS232Close();

			vdDebug_LogPrintf("xmodemReceive end usResult [%d]", usResult);
			fwrite(uszFileBuffer, strFileTransfer_Ext.inFileTransferNameSize, 1, ptr_myfile);
			fclose(ptr_myfile);
			vdDebug_LogPrintf("strFileTransfer_Ext.uszFileTransferName[%s]", strFileTransfer_Ext.uszFileTransferName);
			memset(szSystemCmdPath, 0x00, sizeof(szSystemCmdPath)); 			
			sprintf(szSystemCmdPath, "gzip -f -d %s", strFileTransfer_Ext.uszFileTransferName);
			system(szSystemCmdPath);

			memset(szFileName, 0x00, sizeof(szFileName));
			strncpy(szFileName, strFileTransfer_Ext.uszFileTransferName, strlen(strFileTransfer_Ext.uszFileTransferName) -3);
			vdDebug_LogPrintf("szFileName[%s]", szFileName);
			
			//ShowBmpEx(0, 32, szFileName);

			//vdPrintReceipt(szFileName);

			//return 0;	
			
		}				
	}

	inCTOSS_V3PRS232Close();
			
}



int inCOMMS_Init_Ext_Terminal(int inMode)
{
	char szV3PSendBuf[4096+1];
	int inOffSet = 0;
	int status;
	char szRecvBuf[3000+1];
	int inRecvlen = 0;
	USHORT usResult;
	STRUCT_COMMS_INIT_EXT inCommsInit;

	vdDebug_LogPrintf("inCOMMS_Init_Ext_Terminal inMode=[%d],srTransRec.HDTid=[%d],strTCT.byRS232ECRPort=[%d]",inMode,srTransRec.HDTid,strTCT.byRS232ECRPort);
		
	gCommModeex = inMode;
	inCommsInit.inmode = inMode;
	inCommsInit.hdt = srTransRec.HDTid;

	vdDebug_LogPrintf("11111");
	vdCTOSS_SetV3PRS232Port(strTCT.byRS232ECRPort);
	inCTOSS_V3PRS232Open(strTCT.inPPBaudRate, 'N', 8, 1);
	vdDebug_LogPrintf("22222");

	// send STX INJECT_KEY ETX LRC
	memset(szV3PSendBuf, 0x00, sizeof(szV3PSendBuf));
	inOffSet = 0;
	szV3PSendBuf[inOffSet] = STX;
	inOffSet += 1;
	vdDebug_LogPrintf("333");
	memcpy(&szV3PSendBuf[inOffSet], "COMM_INIT", strlen("COMM_INIT"));
	inOffSet += strlen("COMM_INIT");
	vdDebug_LogPrintf("44444");
	szV3PSendBuf[inOffSet] = '|';
	inOffSet += 1;				
	//////////////////////////////////////////////////////////////////////////
	memcpy(&szV3PSendBuf[inOffSet], &inCommsInit, sizeof(STRUCT_COMMS_INIT_EXT));
	inOffSet += sizeof(STRUCT_COMMS_INIT_EXT);
	vdDebug_LogPrintf("55555");
	///////////////////////////////////////////////////////////////////////
	szV3PSendBuf[inOffSet] = ETX;
	inOffSet += 1;	
	vdDebug_LogPrintf("66666");
	szV3PSendBuf[inOffSet] = (char) wub_lrc(&(szV3PSendBuf[1]), inOffSet-1);
	inOffSet += 1;
	vdDebug_LogPrintf("77777");
	inCTOSS_V3PRS232SendBuf(szV3PSendBuf, inOffSet);
	vdDebug_LogPrintf("88888");


	memset(szRecvBuf,0x00,sizeof(szRecvBuf));
	usResult = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, 500);
	vdDebug_LogPrintf("99999");
	inCTOSS_V3PRS232Close();
	
	if (usResult != d_OK)
		return d_NO;
	
    return d_OK;
}


int inCOMMS_Connect_Ext_Terminal(void)
{
	char szV3PSendBuf[4096+1];
	int inOffSet = 0;
	int status;
	char szRecvBuf[3000+1];
	int inRecvlen = 0;
	USHORT usResult;
		

	vdCTOSS_SetV3PRS232Port(strTCT.byRS232ECRPort);
	inCTOSS_V3PRS232Open(strTCT.inPPBaudRate, 'N', 8, 1);

	// send STX INJECT_KEY ETX LRC
	memset(szV3PSendBuf, 0x00, sizeof(szV3PSendBuf));
	inOffSet = 0;
	szV3PSendBuf[inOffSet] = STX;
	inOffSet += 1;
	memcpy(&szV3PSendBuf[inOffSet], "COMM_CONNECT", strlen("COMM_CONNECT"));
	inOffSet += strlen("COMM_CONNECT");
	szV3PSendBuf[inOffSet] = '|';
	inOffSet += 1;
	///////////////////////////////////////////////////////////////////////
	szV3PSendBuf[inOffSet] = ETX;
	inOffSet += 1;			
	szV3PSendBuf[inOffSet] = (char) wub_lrc(&(szV3PSendBuf[1]), inOffSet-1);
	inOffSet += 1;
	inCTOSS_V3PRS232SendBuf(szV3PSendBuf, inOffSet);


	memset(szRecvBuf,0x00,sizeof(szRecvBuf));
	usResult = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, 500);
	vdDebug_LogPrintf("inCTOSS_V3PRS232RecvACK usResult=[%d]",usResult);
	inCTOSS_V3PRS232Close();
	
	if (usResult != d_OK)
		return d_NO;
	
    return d_OK;
}



int inCOMMS_Send_Ext_Terminal(unsigned char *uszSendData,unsigned long ulSendLen)
{
	char szV3PSendBuf[4096+1];
	int inOffSet = 0;
	int status;
	char szRecvBuf[3000+1];
	int inRecvlen = 0;
	USHORT usResult;
		

	vdCTOSS_SetV3PRS232Port(strTCT.byRS232ECRPort);
	inCTOSS_V3PRS232Open(strTCT.inPPBaudRate, 'N', 8, 1);

	// send STX INJECT_KEY ETX LRC
	memset(szV3PSendBuf, 0x00, sizeof(szV3PSendBuf));
	inOffSet = 0;
	szV3PSendBuf[inOffSet] = STX;
	inOffSet += 1;
	memcpy(&szV3PSendBuf[inOffSet], "COMM_SEND", strlen("COMM_SEND"));
	inOffSet += strlen("COMM_SEND");
	szV3PSendBuf[inOffSet] = '|';
	inOffSet += 1;
	//////////////////////////////////////////////////////////////////////////
	memcpy(&szV3PSendBuf[inOffSet], &ulSendLen, sizeof(unsigned long));
	inOffSet += sizeof(unsigned long);
	szV3PSendBuf[inOffSet] = '|';
	inOffSet += 1;
	//////////////////////////////////////////////////////////////////////////
	memcpy(&szV3PSendBuf[inOffSet], uszSendData, ulSendLen);
	inOffSet += ulSendLen;
	///////////////////////////////////////////////////////////////////////
	szV3PSendBuf[inOffSet] = ETX;
	inOffSet += 1;			
	szV3PSendBuf[inOffSet] = (char) wub_lrc(&(szV3PSendBuf[1]), inOffSet-1);
	inOffSet += 1;
	inCTOSS_V3PRS232SendBuf(szV3PSendBuf, inOffSet);


	memset(szRecvBuf,0x00,sizeof(szRecvBuf));
	usResult = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, 500);

	inCTOSS_V3PRS232Close();
	
	if (usResult != d_OK)
		return d_NO;
	
    return d_OK;
}



int inCOMMS_Receive_Ext_Terminal(unsigned char *uszRecData, int *inReceiveLen)
{
	char szV3PSendBuf[4096+1];
	int inOffSet = 0;
	int status;
	char szRecvBuf[3000+1];
	int inRecvlen = 0;
	USHORT usResult;
	int inHeader;
	long inResult = 0;
	int offset = 0;
	
	inHeader = strCPT.inIPHeader;
	if (gCommModeex == DIAL_UP_MODE)//for Sharls_COM modem
		inHeader = 0;

	vdCTOSS_SetV3PRS232Port(strTCT.byRS232ECRPort);
	inCTOSS_V3PRS232Open(strTCT.inPPBaudRate, 'N', 8, 1);

	// send STX INJECT_KEY ETX LRC
	memset(szV3PSendBuf, 0x00, sizeof(szV3PSendBuf));
	inOffSet = 0;
	szV3PSendBuf[inOffSet] = STX;
	inOffSet += 1;
	memcpy(&szV3PSendBuf[inOffSet], "COMM_RECEVIE", strlen("COMM_RECEVIE"));
	inOffSet += strlen("COMM_RECEVIE");
	szV3PSendBuf[inOffSet] = '|';
	inOffSet += 1;
	///////////////////////////////////////////////////////////////////////
	szV3PSendBuf[inOffSet] = ETX;
	inOffSet += 1;			
	szV3PSendBuf[inOffSet] = (char) wub_lrc(&(szV3PSendBuf[1]), inOffSet-1);
	inOffSet += 1;
	inCTOSS_V3PRS232SendBuf(szV3PSendBuf, inOffSet);


	memset(szRecvBuf,0x00,sizeof(szRecvBuf));
	usResult = inCTOSS_V3PRS232RecvBuf_EXT(szRecvBuf, &inRecvlen, 500);

	inCTOSS_V3PRS232Close();
	
	if (usResult != d_OK)
		return d_NO;
	else
    {

		offset = 1;
		memcpy(&inResult,&szRecvBuf[offset],sizeof(long));
		offset += sizeof(long);
		offset += 1;
		vdDebug_LogPrintf("inReceiveLen=[%d]",inResult);
		if (inResult<=0)
			return d_NO;
		
		//memset(uszSendData,0x00,sizeof(uszSendData));
		memcpy(uszRecData,&szRecvBuf[offset],inResult);
        *inReceiveLen = inResult;
		vdPCIDebug_HexPrintf("uszRecData",uszRecData,inResult);
     }
	
    return d_OK;
}


int inCOMMS_Disconnect_Ext_Terminal(void)
{
	char szV3PSendBuf[4096+1];
	int inOffSet = 0;
	int status;
	char szRecvBuf[3000+1];
	int inRecvlen = 0;
	USHORT usResult;

	vdDebug_LogPrintf("inCOMMS_Disconnect_Ext_Terminal =");
	//if (g_inTermDetectcard == 1)
    //{
    	//means detect card in terminal side, so V3P don't sync data to V3
    //	vdDebug_LogPrintf("do nothing...");
	//	return d_OK;
    //}

	vdCTOSS_SetV3PRS232Port(strTCT.byRS232ECRPort);
	inCTOSS_V3PRS232Open(strTCT.inPPBaudRate, 'N', 8, 1);

	// send STX INJECT_KEY ETX LRC
	memset(szV3PSendBuf, 0x00, sizeof(szV3PSendBuf));
	inOffSet = 0;
	szV3PSendBuf[inOffSet] = STX;
	inOffSet += 1;
	memcpy(&szV3PSendBuf[inOffSet], "COMM_DISCONNECT", strlen("COMM_DISCONNECT"));
	inOffSet += strlen("COMM_DISCONNECT");
	szV3PSendBuf[inOffSet] = '|';
	inOffSet += 1;
	///////////////////////////////////////////////////////////////////////
	szV3PSendBuf[inOffSet] = ETX;
	inOffSet += 1;			
	szV3PSendBuf[inOffSet] = (char) wub_lrc(&(szV3PSendBuf[1]), inOffSet-1);
	inOffSet += 1;
	inCTOSS_V3PRS232SendBuf(szV3PSendBuf, inOffSet);


	memset(szRecvBuf,0x00,sizeof(szRecvBuf));
	usResult = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, 500);

	inCTOSS_V3PRS232Close();
	
	if (usResult != d_OK)
		return d_NO;
	
    return d_OK;
}


int vdNETS_Purchase_EXT_End_Terminal(void)
{
	char szV3PSendBuf[4096+1];
	int inOffSet = 0;
	int status;
	char szRecvBuf[3000+1];
	int inRecvlen = 0;
	USHORT usResult;

	vdDebug_LogPrintf("vdNETS_Purchase_EXT_End_Terminal fEXT[%d],", strTCTEX.fEXT);
	//if (g_inTermDetectcard == 1)
	//{
		//if detect card in terminal, V3P no need send this command to terminal
	//	vdDebug_LogPrintf("detect card in terminal");
	//	return;
	//}
	
	if (strTCTEX.fEXT == 1)
	{
		vdCTOSS_SetV3PRS232Port(strTCT.byRS232ECRPort);
		inCTOSS_V3PRS232Open(strTCT.inPPBaudRate, 'N', 8, 1);

		szV3PSendBuf[0] = ACK;
	    status = inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
		vdDebug_LogPrintf("inCTOSS_V3PRS232SendBuf status[%d]", status);

		inCTOSS_V3PRS232Close();
		
		if (usResult != d_OK)
			return d_NO;
	}
	
	return d_OK;
}

int inNETS_Purchase_EXT_TerminalStatus(void)
{
	char szV3PSendBuf[4096+1];
	int inOffSet = 0;
	int status;
	char szRecvBuf[3000+1];
	int inRecvlen = 0;
	USHORT usResult;

	vdDebug_LogPrintf("vdNETS_Purchase_EXT_End_Terminal fEXT[%d]", strTCTEX.fEXT);
	if (strTCTEX.fEXT == 1)
	{
		memset(szRecvBuf,0x00,sizeof(szRecvBuf));
		usResult = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, 100);
		vdDebug_LogPrintf("vdNETS_Purchase_EXT_End_Terminal usResult[%d]", usResult);
		if (usResult == d_OK)
			return d_OK;
	}
	
	return d_NO;
}



int inNETS_DataSync_EXT_Terminal(TRANS_DATA_TABLE srTranstmpRec)
{
	char szV3PSendBuf[1024+1] = {0};
	int inOffSet = 0;
	char szRecvBuf[1024+1];
	int inRecvlen = 0;
	int status;
	char szFileName[100];
	ACCUM_REC srAccumRec;
    STRUCT_FILE_SETTING strFile;
	//char	sztmpFileName[100];

	vdDebug_LogPrintf("inNETS_DataSync_EXT_Terminal	CMD");
	
	vdDebug_LogPrintf("strTCT.byRS232ECRPort[%d]", strTCT.byRS232ECRPort);
	if (strTCTEX.fEXT == 1)
	{
		
		vdCTOSS_SetV3PRS232Port(strTCT.byRS232ECRPort);
		inCTOSS_V3PRS232Open(strTCT.inPPBaudRate, 'N', 8, 1);

		//CTOS_LCDTPrintAligned(16, "SYNC DATA TO TERMINAL", d_LCD_ALIGNLEFT);

		memset(szFileName, 0x00, sizeof(szFileName));
		strcpy(szFileName, "TERMINAL.S3DB");
		inSendSyncData(szFileName);

		memset(szFileName, 0x00, sizeof(szFileName));
		strcpy(szFileName, "NETS.S3DB");
		inSendSyncData(szFileName);

		memset(szFileName, 0x00, sizeof(szFileName));
		strcpy(szFileName, "ENV.S3DB");
		inSendSyncData(szFileName);

		memset(szFileName, 0x00, sizeof(szFileName));
		strcpy(szFileName, "V5S_NETS.S3DB");
		inSendSyncAPPData(szFileName);

		memset(szFileName, 0x00, sizeof(szFileName));
		strcpy(szFileName, "NETSFLEXI.S3DB");
		inSendSyncAPPData(szFileName);

		//memset(&srAccumRec, 0x00, sizeof(ACCUM_REC));
		//memset(&strFile,0,sizeof(strFile));
		//vdCTOS_GetAccumName(&strFile, &srAccumRec);
		memset(szFileName,0x00,sizeof(szFileName));
		sprintf(szFileName, "ACC%02d%02d.total"
                                , srTranstmpRec.HDTid
                                , srTranstmpRec.MITid);
		inSendSyncAPPData(szFileName);

		inCTOSS_V3PRS232Close();
	}
}



int inNPX_DataSync_EXT_Terminal(TRANS_DATA_TABLE srTranstmpRec)
{
	char szV3PSendBuf[1024+1] = {0};
	int inOffSet = 0;
	char szRecvBuf[1024+1];
	int inRecvlen = 0;
	int status;
	char szFileName[100];
	ACCUM_REC srAccumRec;
    STRUCT_FILE_SETTING strFile;
	//char	sztmpFileName[100];

	vdDebug_LogPrintf("inNPX_DataSync_EXT_Terminal	CMD");
	
	vdDebug_LogPrintf("strTCT.byRS232ECRPort[%d]", strTCT.byRS232ECRPort);
	if (strTCTEX.fEXT == 1)
	{
		
		vdCTOSS_SetV3PRS232Port(strTCT.byRS232ECRPort);
		inCTOSS_V3PRS232Open(strTCT.inPPBaudRate, 'N', 8, 1);

		//CTOS_LCDTPrintAligned(16, "SYNC DATA TO TERMINAL", d_LCD_ALIGNLEFT);


		memset(szFileName, 0x00, sizeof(szFileName));
		strcpy(szFileName, "V5S_VISAMASTER.S3DB");
		inSendSyncAPPData(szFileName);

		memset(szFileName, 0x00, sizeof(szFileName));
		strcpy(szFileName, "NPXFLEXI.S3DB");
		inSendSyncAPPData(szFileName);

		//memset(&srAccumRec, 0x00, sizeof(ACCUM_REC));
		//memset(&strFile,0,sizeof(strFile));
		//vdCTOS_GetAccumName(&strFile, &srAccumRec);
		memset(szFileName,0x00,sizeof(szFileName));
		sprintf(szFileName, "ACC%02d%02d.total"
                                , srTranstmpRec.HDTid
                                , srTranstmpRec.MITid);
		inSendSyncAPPData(szFileName);

		inCTOSS_V3PRS232Close();
	}
}




int inNPX_DataSync_FromEXT(BYTE *inbuf, USHORT inlen)
{

	int inRecvlen = 0;
	int status;
	char szFileName[100];
	unsigned char uszDataBuffer[15000+1] = {0};
	char  szSystemCmdPath[250];

	vdDebug_LogPrintf("inNPX_DataSync_FromEXT.....");
	memset(szFileName,0x00,sizeof(szFileName));
	memcpy(szFileName,inbuf,inlen);

	vdDebug_LogPrintf("szFileName=[%s].....",szFileName);
	
	FILE *ptr_myfile =	NULL;
	int inmyFileSize = 0;
	ptr_myfile = fopen("/home/<USER>/pub/npxdata.txt", "rb");
	vdDebug_LogPrintf("ptr_myfile [%x]", ptr_myfile);
	if (!ptr_myfile)
	{
		vdDebug_LogPrintf("Unable to open file! ");
		return d_NO;
	}
	else
	{
		inmyFileSize = fseek(ptr_myfile, 0, SEEK_END);				
		if(inmyFileSize >= 0)
			inmyFileSize = ftell(ptr_myfile);

		fseek (ptr_myfile, 0, SEEK_SET);
		memset(uszDataBuffer, 0x00, sizeof(uszDataBuffer));
		fread(uszDataBuffer, inmyFileSize, 1, ptr_myfile);

		fclose(ptr_myfile);
		ptr_myfile = NULL;
	}


	unlink(szFileName);

	ptr_myfile = fopen(szFileName, "wb");
	if (!ptr_myfile)
	{
		vdDebug_LogPrintf("Unable to open file!");
		return d_NO;
	}

	fseek(ptr_myfile, 0L, SEEK_SET);

	fwrite(uszDataBuffer, inmyFileSize, 1, ptr_myfile);
	fclose(ptr_myfile);
	
	vdDebug_LogPrintf("strFileTransfer_Ext.uszFileTransferName[%s],inmyFileSize=[%d]", szFileName,inmyFileSize);
	memset(szSystemCmdPath, 0x00, sizeof(szSystemCmdPath)); 			
	sprintf(szSystemCmdPath, "gzip -f -d %s", szFileName);
	system(szSystemCmdPath);

	return d_OK;
}



int inNPX_UpdateTransRec_FromEXT(void)
{

	int inRecvlen = 0;
	int status;
	char szFileName[100];
	unsigned char uszDataBuffer[15000+1] = {0};
	char  szSystemCmdPath[250];
	int inRet;

	vdDebug_LogPrintf("inNPX_UpdateTransRec_FromEXT.....");
	//memset(szFileName,0x00,sizeof(szFileName));
	//memcpy(szFileName,inbuf,inlen);

	//vdDebug_LogPrintf("szFileName=[%s].....",szFileName);
	
	FILE *ptr_myfile =	NULL;
	int inmyFileSize = 0;
	ptr_myfile = fopen("/home/<USER>/pub/npxdata.txt", "rb");
	vdDebug_LogPrintf("ptr_myfile [%x]", ptr_myfile);
	if (!ptr_myfile)
	{
		vdDebug_LogPrintf("Unable to open file! ");
		return d_NO;
	}
	else
	{
		inmyFileSize = fseek(ptr_myfile, 0, SEEK_END);				
		if(inmyFileSize >= 0)
			inmyFileSize = ftell(ptr_myfile);

		vdDebug_LogPrintf("inmyFileSize=[%d]",inmyFileSize);

		fseek (ptr_myfile, 0, SEEK_SET);
		memset(uszDataBuffer, 0x00, sizeof(uszDataBuffer));
		fread(uszDataBuffer, inmyFileSize, 1, ptr_myfile);

		fclose(ptr_myfile);
		ptr_myfile = NULL;
	}

	//vdDebugprintf(uszDataBuffer,inmyFileSize);
	vdDebug_LogPrintf("TRANS_DATA_TABLE=[%d]",sizeof(TRANS_DATA_TABLE));
	memset( &srTransRec, 0x00, sizeof(TRANS_DATA_TABLE));
	memcpy(&srTransRec, uszDataBuffer, sizeof(TRANS_DATA_TABLE)); 

	vdDebug_LogPrintf("iCDTid[%d]", srTransRec.CDTid);
	vdDebug_LogPrintf("byTransType[%d]", srTransRec.byTransType);
	vdDebug_LogPrintf("HDTid[%d]", srTransRec.HDTid);
	inHDTRead(srTransRec.HDTid);

	ptr_myfile = fopen("/home/<USER>/pub/npxFlexidata.txt", "rb");
	vdDebug_LogPrintf("ptr_myfile [%x]", ptr_myfile);
	if (!ptr_myfile)
	{
		vdDebug_LogPrintf("Unable to open file! ");
		//return d_NO;
	}
	else
	{
		inmyFileSize = fseek(ptr_myfile, 0, SEEK_END);				
		if(inmyFileSize >= 0)
			inmyFileSize = ftell(ptr_myfile);

		vdDebug_LogPrintf("inmyFileSize=[%d]",inmyFileSize);

		fseek (ptr_myfile, 0, SEEK_SET);
		memset(uszDataBuffer, 0x00, sizeof(uszDataBuffer));
		fread(uszDataBuffer, inmyFileSize, 1, ptr_myfile);

		fclose(ptr_myfile);
		ptr_myfile = NULL;
	}

	vdDebug_LogPrintf("TRANS_FLEXI_DATA_TABLE=[%d]",sizeof(TRANS_FLEXI_DATA_TABLE));
	memset( &srTransFlexiData, 0x00, sizeof(TRANS_FLEXI_DATA_TABLE));
	memcpy(&srTransFlexiData, uszDataBuffer, sizeof(TRANS_FLEXI_DATA_TABLE));

	
	vdDebug_LogPrintf("HDTid[%d]", srTransFlexiData.HDTid);
	vdDebug_LogPrintf("MITid[%d]", srTransFlexiData.MITid);
	vdDebug_LogPrintf("ulTraceNum[%d]", srTransFlexiData.ulTraceNum);

	inRet = inCTOS_SaveBatchTxn();
	vdDebug_LogPrintf("inCTOS_SaveBatchTxn inRet[%d]", inRet);


    inRet = inCTOS_UpdateAccumTotal();
	vdDebug_LogPrintf("inCTOS_UpdateAccumTotal inRet[%d]", inRet);

	inCTLOS_Updatepowrfail(PFR_IDLE_STATE);

	return d_OK;
}


int inNPX_UpdateTransRec_FromTerminal(void)
{

	int inRecvlen = 0;
	int status;
	char szFileName[100];
	unsigned char uszDataBuffer[15000+1] = {0};
	char  szSystemCmdPath[250];
	int inRet;

	vdDebug_LogPrintf("inNPX_UpdateTransRec_FromTerminal.....");
	//memset(szFileName,0x00,sizeof(szFileName));
	//memcpy(szFileName,inbuf,inlen);

	//vdDebug_LogPrintf("szFileName=[%s].....",szFileName);
	
	FILE *ptr_myfile =	NULL;
	int inmyFileSize = 0;
	ptr_myfile = fopen("/home/<USER>/pub/npxdata.txt", "rb");
	vdDebug_LogPrintf("ptr_myfile [%x]", ptr_myfile);
	if (!ptr_myfile)
	{
		vdDebug_LogPrintf("Unable to open file! ");
		return d_NO;
	}
	else
	{
		inmyFileSize = fseek(ptr_myfile, 0, SEEK_END);				
		if(inmyFileSize >= 0)
			inmyFileSize = ftell(ptr_myfile);

		vdDebug_LogPrintf("inmyFileSize=[%d]",inmyFileSize);

		fseek (ptr_myfile, 0, SEEK_SET);
		memset(uszDataBuffer, 0x00, sizeof(uszDataBuffer));
		fread(uszDataBuffer, inmyFileSize, 1, ptr_myfile);

		fclose(ptr_myfile);
		ptr_myfile = NULL;
	}

	//vdDebugprintf(uszDataBuffer,inmyFileSize);
	vdDebug_LogPrintf("TRANS_DATA_TABLE=[%d]",sizeof(TRANS_DATA_TABLE));
	memset( &srTransRec, 0x00, sizeof(TRANS_DATA_TABLE));
	memcpy(&srTransRec, uszDataBuffer, sizeof(TRANS_DATA_TABLE)); 

	vdDebug_LogPrintf("szHostLabel=[%s]....",srTransRec.szHostLabel);
	 vdPCIDebug_HexPrintf("szBatchNo",srTransRec.szBatchNo, 3);
	vdDebug_LogPrintf("MITid=[%d]....",srTransRec.MITid);
	vdDebug_LogPrintf("HDTid=[%d]....",srTransRec.HDTid);
	inHDTRead(srTransRec.HDTid);

	inCTOS_SettlementClearBathAndAccum_Ext();

	inCTLOS_Updatepowrfail(PFR_IDLE_STATE);

	return d_OK;
}





void vdDisplayDebugMsg(int inColumn, int inRow,  char *msg)
{
    //return;

	//CTOS_LCDTPrintXY(inColumn, inRow, "                                        ");
    //CTOS_LCDTPrintXY(inColumn, inRow, msg);
    CTOS_Delay(100);
}

#define SEND_LEN	2048
BYTE ECRPort = 0xFF;
extern BYTE bV3PPort;
#define MAXDEBUG_LEN	100


void vdDebugprintf(BYTE *hex, USHORT inSize)
{
	int j;
	

	//for (j = 0;j<inSize;j++)
	for (j = 0;j<30;j++)
	{
		vdDebug_LogPrintfEX("%02x", hex[j]);
	}
}

int inECRSendComPacket(unsigned char* pchMsg, int inMsgSize, VS_BOOL fPadStxEtx,int inACKTimeOut,VS_BOOL fWaitForAck,int inMaxRetries) {
	unsigned char chResp;
	int inSize = 0;
	int inNumRetry = 0;
	char szECRSendBuf[18192 + 1] = {0};
	int inSendsize=0;	
	USHORT ret;
	int times = 0, i;
	ULONG tick = CTOS_TickGet();
	char szDispMsg[20];
	int inRet = -1;
	USHORT lECRRecvLen;

	vdDebug_LogPrintf("inECRSendComPacket SEND_LEN[%d]", SEND_LEN);

	// patrick fix terminal send ECR resign command
	if (bV3PPort == d_COM1 || bV3PPort == d_COM2)
	{
		CTOS_RS232FlushRxBuffer(bV3PPort);
		CTOS_RS232FlushTxBuffer(bV3PPort);
	}

    memset(szECRSendBuf, 0, (int)sizeof(szECRSendBuf));
    if (fPadStxEtx == VS_TRUE) {
    	
        szECRSendBuf[inSize] = STX; 
        inSize++;
        memcpy(&(szECRSendBuf[1]), pchMsg, inMsgSize); 
        inSize += inMsgSize;
        szECRSendBuf[inSize] = ETX; 
        inSize++;
    } else {
    		if (inMsgSize == 1)
    		{
    			szECRSendBuf[0] = pchMsg[0];
			inSize = inMsgSize;
    		}
		else
		{
			memcpy(szECRSendBuf, pchMsg, inMsgSize); 
			inSize = inMsgSize;
		}
    }

//    szECRSendBuf[inSize] = (char) SVC_CRC_CALC(0, &(szECRSendBuf[1]), (inSize - 1));
    szECRSendBuf[inSize] = (char) ucGetLRC(&(szECRSendBuf[1]), (inSize - 1));
    inSize++;

	do {
		if (bV3PPort == USB_PORT)
		  ret = CTOS_USBTxReady();
		else
		  ret = CTOS_RS232TxReady(bV3PPort);
		if (ret == d_OK)
			break;
	}while ((CTOS_TickGet() - tick) < (inACKTimeOut*100));

	if (ret == d_OK) {

		if (inSize > SEND_LEN)
		{
			times = inSize/SEND_LEN;
			
			vdDebug_LogPrintf("inECRSendComPacket times[%d]", times);
			for (i = 0;i<times;i++)
			{
				do {
					if (bV3PPort == USB_PORT)
					  ret = CTOS_USBTxReady();
					else
					  ret = CTOS_RS232TxReady(bV3PPort);
					if (ret == d_OK)
						break;
				}while ((CTOS_TickGet() - tick) < (inACKTimeOut*100));

				if (bV3PPort == USB_PORT)
					CTOS_USBTxData(&szECRSendBuf[i*SEND_LEN], SEND_LEN);
				else
					CTOS_RS232TxData(bV3PPort, &szECRSendBuf[i*SEND_LEN], SEND_LEN);

				CTOS_Delay(10);

				
				vdDebug_LogPrintf("inECRSendComPacket szECRSendBuf");
				DebugAddHEX("send",&szECRSendBuf[i*SEND_LEN],SEND_LEN);
			}

			times = inSize%SEND_LEN;
			vdDebug_LogPrintf("inECRSendComPacket times[%d]", times);
			if(times>0)
			{
				do {
					if (bV3PPort == USB_PORT)
					  ret = CTOS_USBTxReady();
					else
					  ret = CTOS_RS232TxReady(bV3PPort);
					if (ret == d_OK)
						break;
				}while ((CTOS_TickGet() - tick) < (inACKTimeOut*100));

				if (bV3PPort == USB_PORT)
					CTOS_USBTxData(&szECRSendBuf[i*SEND_LEN], times);
				else
					CTOS_RS232TxData(bV3PPort, &szECRSendBuf[i*SEND_LEN], times);

				DebugAddHEX("send",&szECRSendBuf[i*SEND_LEN],times);
			}
			
		}
		else
		{
			if (bV3PPort == USB_PORT)
				CTOS_USBTxData(szECRSendBuf, inSize);
			else
				CTOS_RS232TxData(bV3PPort, szECRSendBuf, inSize);
		}

		if (fWaitForAck == VS_TRUE) {
			 chResp = NAK;				/* Wait for reply till Timeout */

			 tick = CTOS_TickGet();

			 do {
				 if (bV3PPort == USB_PORT)
					 ret = CTOS_USBRxReady(&lECRRecvLen);
				 else
				 	ret = CTOS_RS232RxReady(bV3PPort, &lECRRecvLen); 
				 if (ret == d_OK)
					 break;
			 }while ((CTOS_TickGet() - tick) < (inACKTimeOut*1000));

			lECRRecvLen = 1;
			 memset(szDispMsg, 0x00, sizeof(szDispMsg));
			 sprintf(szDispMsg, "bV3PPort(%d)", bV3PPort);	
			 vdDisplayDebugMsg(1, 10, szDispMsg);
			 
			 vdDebug_LogPrintf("inECRSendComPacket ECRPort[%d]", bV3PPort);
			 
			 if (bV3PPort == USB_PORT)
			 {
				//vdDisplayDebugMsg(1, 10, "CTOS_USBRxData");
				//CTOS_Delay(1300);
				inRet = CTOS_USBRxData(&chResp, &lECRRecvLen);

				vdDebug_LogPrintf("inECRSendComPacket inRet[%d]", inRet);
				vdDebug_LogPrintf("inECRSendComPacket chResp[%x]", chResp);

				memset(szDispMsg, 0x00, sizeof(szDispMsg));
			    sprintf(szDispMsg, "inRet(%d)", inRet);	
			    vdDisplayDebugMsg(1, 10, szDispMsg);
			 }
			 else
			 {
				inRet = CTOS_RS232RxData(bV3PPort, &chResp, &lECRRecvLen);
			 }

			 memset(szDispMsg, 0x00, sizeof(szDispMsg));
			 sprintf(szDispMsg, "RES(%d)", inRet);	
			 vdDisplayDebugMsg(1, 10, szDispMsg);
			 
			 vdDebug_LogPrintf("inECRSendComPacket inRet[%d]", inRet);

			 memset(szDispMsg, 0x00, sizeof(szDispMsg));
			 sprintf(szDispMsg, "ACK(%d)", chResp);	
			 vdDisplayDebugMsg(1, 10, szDispMsg);

			 vdDebug_LogPrintf("inECRSendComPacket ACK[%d]", ACK);
			 
		 }else
			 chResp = ACK;
	}

	//inPrintECRPacket("ECR Send Response", szECRSendBuf, inSize);
	DebugAddHEX("ECR Send Response", szECRSendBuf, inSize);

	vdDebug_LogPrintf("inECRSendComPacket chResp[%x]", chResp);

    if (chResp == ACK)
        return (VS_SUCCESS);
    else
        return (ERR_COM_NAK);  /* Too many NAKs, so indicate to the app */   	
}


int inUSBSendComPacket(unsigned char* pchMsg, int inMsgSize, VS_BOOL fPadStxEtx, int inACKTimeOut) {
	unsigned char chResp;
	int inSize = 0;
	int inNumRetry = 0;
	BYTE  szECRSendBuf[18192 + 1] = {0};
	BYTE  sztmpECRSendBuf[3000 + 1] = {0};
	int inSendsize=0;	
	USHORT ret;
	int times = 0, i;
	ULONG tick = CTOS_TickGet();
	char szDispMsg[20];
	int inRet = -1;
	USHORT lECRRecvLen;

	vdDebug_LogPrintf("inUSBSendComPacket bV3PPort[%d]", bV3PPort);

	// patrick fix terminal send ECR resign command
	if (bV3PPort == d_COM1 || bV3PPort == d_COM2)
	{
		CTOS_RS232FlushRxBuffer(bV3PPort);
		CTOS_RS232FlushTxBuffer(bV3PPort);
	}

    memset(szECRSendBuf, 0, (int)sizeof(szECRSendBuf));
    if (fPadStxEtx == VS_TRUE) {
    	
        szECRSendBuf[inSize] = STX; 
        inSize++;
        memcpy(&(szECRSendBuf[1]), pchMsg, inMsgSize); 
        inSize += inMsgSize;
        szECRSendBuf[inSize] = ETX; 
        inSize++;
    } else {
    		if (inMsgSize == 1)
    		{
    			szECRSendBuf[0] = pchMsg[0];
			inSize = inMsgSize;
    		}
		else
		{
			memcpy(szECRSendBuf, pchMsg, inMsgSize); 
			inSize = inMsgSize;
		}
    }

    szECRSendBuf[inSize] = (char) ucGetLRC(&(szECRSendBuf[1]), (inSize - 1));
    inSize++;
	if(bV3PPort == USB_HOST_PORT)
	{
		ret = d_OK;
	}
	else{
		do {
			if (bV3PPort == USB_PORT)
			  ret = CTOS_USBTxReady();
			else
			  ret = CTOS_RS232TxReady(bV3PPort);
			if (ret == d_OK)
				break;
		}while ((CTOS_TickGet() - tick) < (inACKTimeOut*100));
	}

	
	vdDebug_LogPrintf("inUSBSendComPacket ret[%d]", ret);
	
	if (ret == d_OK) {
		vdDebug_LogPrintf("inUSBSendComPacket inSize [%d] SEND_LEN [%d]", inSize, SEND_LEN);

		if (inSize > SEND_LEN)
		{
			times = inSize/SEND_LEN;
			
			vdDebug_LogPrintf("inECRSendComPacket times[%d]", times);
			for (i = 0;i<times;i++)
            {
               if(bV3PPort == USB_HOST_PORT)
               {
				   inCTOSS_USBHostSendBufEx(&szECRSendBuf[i*SEND_LEN], SEND_LEN);
			   }
			   else
			   {
				do {
					if (bV3PPort == USB_PORT)
					  ret = CTOS_USBTxReady();
					else
					  ret = CTOS_RS232TxReady(bV3PPort);
					if (ret == d_OK)
						break;
				}while ((CTOS_TickGet() - tick) < (inACKTimeOut*100));

				if (bV3PPort == USB_PORT)
					CTOS_USBTxData(&szECRSendBuf[i*SEND_LEN], SEND_LEN);
				else
					CTOS_RS232TxData(bV3PPort, &szECRSendBuf[i*SEND_LEN], SEND_LEN);

				CTOS_Delay(10);

			   }
				vdDebug_LogPrintf("inUSBSendComPacket szECRSendBuf");
				//vdPCIDebug_HexPrintf("send",&szECRSendBuf[i*SEND_LEN],SEND_LEN);
				memset(sztmpECRSendBuf,0x00,sizeof(sztmpECRSendBuf));
				memcpy(sztmpECRSendBuf,&szECRSendBuf[i*SEND_LEN], SEND_LEN);
				 DebugAddHEX("send", sztmpECRSendBuf, SEND_LEN);
			}

			times = inSize%SEND_LEN;
			vdDebug_LogPrintf("inUSBSendComPacket times[%d]", times);
			if(times>0)
			{

			if(bV3PPort == USB_HOST_PORT)
			{
				inCTOSS_USBHostSendBufEx(&szECRSendBuf[i*SEND_LEN], times);
			}
			else
			{

				do {
					if (bV3PPort == USB_PORT)
					  ret = CTOS_USBTxReady();
					else
					  ret = CTOS_RS232TxReady(bV3PPort);
					if (ret == d_OK)
						break;
				}while ((CTOS_TickGet() - tick) < (inACKTimeOut*100));

				if (bV3PPort == USB_PORT)
					CTOS_USBTxData(&szECRSendBuf[i*SEND_LEN], times);
				else
					CTOS_RS232TxData(bV3PPort, &szECRSendBuf[i*SEND_LEN], times);
			}

				//vdPCIDebug_HexPrintf("send",&szECRSendBuf[i*SEND_LEN],times);
				 DebugAddHEX("send", &szECRSendBuf[i*SEND_LEN], times);
			}
			
		}
		else
		{
			if (bV3PPort == USB_PORT)
				CTOS_USBTxData(szECRSendBuf, inSize);
			else if(bV3PPort == USB_HOST_PORT)
			{
				vdDebug_LogPrintf("inCTOSS_USBHostSendBufEx inSize[%d]", inSize);

				inCTOSS_USBHostSendBufEx(szECRSendBuf, inSize);
			}
			else
				CTOS_RS232TxData(bV3PPort, szECRSendBuf, inSize);
		}

	
		chResp = ACK;
	}

	DebugAddHEX("USB Send Response", szECRSendBuf, inSize);
	//vdDebugprintf(szECRSendBuf,inSize);
	vdDebug_LogPrintf("inUSBSendComPacket chResp[%x]", chResp);

    if (chResp == ACK)
        return (VS_SUCCESS);
    else
        return (ERR_COM_NAK);  /* Too many NAKs, so indicate to the app */   	
}


int inNPX_SyncTransRec_EXT_Terminal(void)
{
	char  szSystemCmdPath[250] = {0};
	char  szGzipFileName[250] = {0};
    BYTE szECRSendData[10240 + 2];	
	unsigned char uszDataBuffer[15000+1] = {0};
	char szDispMsg[20];
	int inOffset = 0;
	int inRet = VS_SUCCESS;	
	int offset;
	char szRecvBuf[1024+1];
	int inRecvlen = 0;

	vdDebug_LogPrintf("inNPX_SyncTransRec_EXT_Terminal	CMD");
	
	vdDebug_LogPrintf("strTCT.byRS232ECRPort[%d]", strTCT.byRS232ECRPort);
	if (strTCTEX.fEXT == 1)
	{
		
		vdCTOSS_SetV3PRS232Port(strTCT.byRS232ECRPort);
		inCTOSS_V3PRS232Open(strTCT.inPPBaudRate, 'N', 8, 1);

		//CTOS_LCDTPrintAligned(16, "SYNC DATA TO TERMINAL", d_LCD_ALIGNLEFT);
		vdDebug_LogPrintf("iCDTid[%d]", srTransRec.CDTid);
		vdDebug_LogPrintf("byTransType[%d]", srTransRec.byTransType);
	
		// pack EXT command
		offset = 0;
		memset(szECRSendData, 0x00, sizeof(szECRSendData));
		memcpy(&szECRSendData[offset], "NPX_TRANSREC_DATA", strlen("NPX_TRANSREC_DATA"));
		offset += strlen("NPX_TRANSREC_DATA");
		szECRSendData[offset] = '|';
		offset += 1;

		memset(szDispMsg,0x00,sizeof(szDispMsg));
		sprintf(szDispMsg,"%04d",sizeof(TRANS_DATA_TABLE));
		memcpy(&szECRSendData[offset], szDispMsg, 4);
		offset += 4;
		szECRSendData[offset] = '|';
		offset += 1;
		
		memcpy(&szECRSendData[offset], &srTransRec, sizeof(TRANS_DATA_TABLE));
		offset += sizeof(TRANS_DATA_TABLE);
		
		vdDebug_LogPrintf("iCDTid[%d]", srTransRec.CDTid);
		vdDebug_LogPrintf("byTransType[%d]", srTransRec.byTransType);
		vdDebug_LogPrintf("offset [%d)]", offset);
		
		//inRet = inECRSendComPacket(szECRSendData, offset,VS_TRUE, 5, VS_TRUE, 3); 
		inRet = inUSBSendComPacket(szECRSendData, offset, VS_TRUE, 5);
		//inRet = inCTOSS_V3PRS232SendBuf(szECRSendData, offset);	
		vdDebug_LogPrintf("inRet [%d]", inRet);

		memset(szRecvBuf,0x00,sizeof(szRecvBuf));
		inRet = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, 500);
		vdDebug_LogPrintf("inCTOSS_V3PRS232RecvACK inRet[%d] szRecvBuf[%d]", inRet, szRecvBuf[0]);
		DebugAddHEX("inCTOSS_V3PRS232RecvACK", szRecvBuf, inRecvlen);

		////////////////////////////////
		
		// pack EXT command
		CTOS_Delay(200);
		offset = 0;
		memset(szECRSendData, 0x00, sizeof(szECRSendData));
		memcpy(&szECRSendData[offset], "NPX_TRANSREC_DATA", strlen("NPX_TRANSREC_DATA"));
		offset += strlen("NPX_TRANSREC_DATA");
		szECRSendData[offset] = '|';
		offset += 1;

		memset(szDispMsg,0x00,sizeof(szDispMsg));
		sprintf(szDispMsg,"%06d",sizeof(TRANS_FLEXI_DATA_TABLE));
		memcpy(&szECRSendData[offset], szDispMsg, 6);
		offset += 6;
		szECRSendData[offset] = '|';
		offset += 1;
		
		memcpy(&szECRSendData[offset], &srTransFlexiData, sizeof(TRANS_FLEXI_DATA_TABLE));
		offset += sizeof(TRANS_FLEXI_DATA_TABLE);
		
		vdDebug_LogPrintf("HDTid[%d]", srTransFlexiData.HDTid);
		vdDebug_LogPrintf("MITid[%d]", srTransFlexiData.MITid);
		vdDebug_LogPrintf("ulTraceNum[%d]", srTransFlexiData.ulTraceNum);
		vdDebug_LogPrintf("offset [%d)]", offset);
		
		//inRet = inECRSendComPacket(szECRSendData, offset,VS_TRUE, 5, VS_TRUE, 3); 
		inRet = inUSBSendComPacket(szECRSendData, offset, VS_TRUE, 5);
		//inRet = inCTOSS_V3PRS232SendBuf(szECRSendData, offset);	
		vdDebug_LogPrintf("inRet [%d]", inRet);

		memset(szRecvBuf,0x00,sizeof(szRecvBuf));
		inRet = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, 500);
		vdDebug_LogPrintf("inCTOSS_V3PRS232RecvACK inRet[%d] szRecvBuf[%d]", inRet, szRecvBuf[0]);
		DebugAddHEX("inCTOSS_V3PRS232RecvACK", szRecvBuf, inRecvlen);


		inCTOSS_V3PRS232Close();
	}
	
}


int inSendSyncData(char *szFileName)
{
		char  szSystemCmdPath[250] = {0};
		char  szGzipFileName[250] = {0};
        BYTE szECRSendData[4096 + 2];	
		unsigned char uszDataBuffer[15000+1] = {0};
		char szDispMsg[20];
		int inOffset = 0;
		int inRet = VS_SUCCESS;	
		int offset;
		char szRecvBuf[1024+1];
		int inRecvlen = 0;
			
			memset(szGzipFileName, 0x00, sizeof(szGzipFileName));
			sprintf(szGzipFileName, "%s%s%s", PUBLIC_PATH, szFileName, ".gz");
			unlink(szGzipFileName);
	
			memset(szSystemCmdPath, 0x00, sizeof(szSystemCmdPath));
			sprintf(szSystemCmdPath, "gzip -c %s/%s > %s", PUBLIC_PATH, szFileName, szGzipFileName);
			system(szSystemCmdPath);
	
			// pack EXT command
			offset = 0;
			memset(szECRSendData, 0x00, sizeof(szECRSendData));
			memcpy(&szECRSendData[offset], "SYNC_DATA", strlen("SYNC_DATA"));
			offset += strlen("SYNC_DATA");
			szECRSendData[offset] = '|';
			offset += 1;
	
			memset(&strFileTransfer_Ext, 0x00, sizeof(STRUCT_FILE_TRANSFER_EXT));		
			strcpy(strFileTransfer_Ext.uszFileTransferName, szGzipFileName);
	
			FILE *ptr_myfile =	NULL;
			int inmyFileSize = 0;
			ptr_myfile = fopen(strFileTransfer_Ext.uszFileTransferName, "rb");
			vdDebug_LogPrintf("ptr_myfile [%x]", ptr_myfile);
			if (!ptr_myfile)
			{
				vdDebug_LogPrintf("Unable to open file! [%s]", szGzipFileName);
			}
			else
			{
				inmyFileSize = fseek(ptr_myfile, 0, SEEK_END);
						
				if(inmyFileSize >= 0)
					inmyFileSize = ftell(ptr_myfile);	
		
				strFileTransfer_Ext.inFileTransferNameSize = inmyFileSize;
				vdDebug_LogPrintf("uszFileTransferName =[%s]", strFileTransfer_Ext.uszFileTransferName);
				vdDebug_LogPrintf("inFileTransferNameSize =[%d]", strFileTransfer_Ext.inFileTransferNameSize);
		
				memcpy(&szECRSendData[offset], &strFileTransfer_Ext, sizeof(STRUCT_FILE_TRANSFER_EXT));
				offset += sizeof(STRUCT_FILE_TRANSFER_EXT);
				
				memset(szDispMsg, 0x00, sizeof(szDispMsg));
				sprintf(szDispMsg, "offset(%d)", offset);	
				vdDisplayDebugMsg(1, 10, szDispMsg);	
				vdDebug_LogPrintf("offset(%d)", offset);
				
				vdDebug_LogPrintf("offset [%d)]", offset);
				
				//inRet = inECRSendComPacket(szECRSendData, offset,VS_TRUE, 5, VS_TRUE, 3); 
				inRet = inUSBSendComPacket(szECRSendData, offset, VS_TRUE, 5);
				//inRet = inCTOSS_V3PRS232SendBuf(szECRSendData, offset);	
				vdDebug_LogPrintf("inRet [%d]", inRet);
	
				memset(szDispMsg, 0x00, sizeof(szDispMsg));
				sprintf(szDispMsg, "inRet(%d)", inRet); 
				vdDisplayDebugMsg(1, 10, szDispMsg);
				vdDebug_LogPrintf("inRet(%d)", inRet);


				
				memset(szRecvBuf,0x00,sizeof(szRecvBuf));
				inRet = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, 500);
				vdDebug_LogPrintf("inCTOSS_V3PRS232RecvACK inRet[%d] szRecvBuf[%d]", inRet, szRecvBuf[0]);
				DebugAddHEX("inCTOSS_V3PRS232RecvACK", szRecvBuf, inRecvlen);

			
				
				if (VS_SUCCESS == inRet)
				{
					fseek (ptr_myfile, 0, SEEK_SET);
					memset(uszDataBuffer, 0x00, sizeof(uszDataBuffer));
					fread(uszDataBuffer, inmyFileSize, 1, ptr_myfile);
	
					memset(szDispMsg, 0x00, sizeof(szDispMsg));
					sprintf(szDispMsg, "inmyFileSize(%d)", inmyFileSize);
					vdDebug_LogPrintf("inmyFileSize(%d)", inmyFileSize);
					vdDisplayDebugMsg(1, 10, szDispMsg);
					vdDebug_LogPrintf("inmyFileSize [%d)]", inmyFileSize);
					
				    //inRet = inECRSendComPacket(uszDataBuffer, inmyFileSize,VS_TRUE, 5, VS_TRUE, 3); 
	
					//inRet = inCTOSS_V3PRS232SendBuf(uszDataBuffer, inmyFileSize); 
					
					inRet = inUSBSendComPacket(uszDataBuffer, inmyFileSize, VS_TRUE, 5); 
					memset(szDispMsg, 0x00, sizeof(szDispMsg));
					sprintf(szDispMsg, "inRet(%d)", inRet); 
					vdDebug_LogPrintf("inRet(%d)", inRet);
					vdDisplayDebugMsg(1, 10, szDispMsg);
					
					vdDebug_LogPrintf("inRet [%d]", inRet);

					memset(szRecvBuf,0x00,sizeof(szRecvBuf));
					inRet = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, 500);
					vdDebug_LogPrintf("inCTOSS_V3PRS232RecvACK inRet[%d] szRecvBuf[%d]", inRet, szRecvBuf[0]);
					DebugAddHEX("inCTOSS_V3PRS232RecvACK", szRecvBuf, inRecvlen);

	
	
					//CTOS_Delay(10000);
				}
		
				fclose(ptr_myfile);
				inRet = d_OK;
	
			}
}



int inSendSyncAPPData(char *szFileName)
{
		char  szSystemCmdPath[250] = {0};
		char  szGzipFileName[250] = {0};
        BYTE szECRSendData[4096 + 2];	
		unsigned char uszDataBuffer[15000+1] = {0};
		char szDispMsg[20];
		int inOffset = 0;
		int inRet = VS_SUCCESS;	
		int offset;
		char szRecvBuf[1024+1];
		int inRecvlen = 0;
			
			memset(szGzipFileName, 0x00, sizeof(szGzipFileName));
			sprintf(szGzipFileName, "%s%s%s", LOCAL_PATH, szFileName, ".gz");
			unlink(szGzipFileName);
	
			memset(szSystemCmdPath, 0x00, sizeof(szSystemCmdPath));
			sprintf(szSystemCmdPath, "gzip -c %s/%s > %s", LOCAL_PATH, szFileName, szGzipFileName);
			system(szSystemCmdPath);
	
			// pack EXT command
			offset = 0;
			memset(szECRSendData, 0x00, sizeof(szECRSendData));
			memcpy(&szECRSendData[offset], "SYNC_NPX_DATA", strlen("SYNC_NPX_DATA"));
			offset += strlen("SYNC_NPX_DATA");
			szECRSendData[offset] = '|';
			offset += 1;
	
			memset(&strFileTransfer_Ext, 0x00, sizeof(STRUCT_FILE_TRANSFER_EXT));		
			strcpy(strFileTransfer_Ext.uszFileTransferName, szGzipFileName);
	
			FILE *ptr_myfile =	NULL;
			int inmyFileSize = 0;
			ptr_myfile = fopen(strFileTransfer_Ext.uszFileTransferName, "rb");
			vdDebug_LogPrintf("ptr_myfile [%x]", ptr_myfile);
			if (!ptr_myfile)
			{
				vdDebug_LogPrintf("Unable to open file! [%s]", szGzipFileName);
			}
			else
			{
				inmyFileSize = fseek(ptr_myfile, 0, SEEK_END);
						
				if(inmyFileSize >= 0)
					inmyFileSize = ftell(ptr_myfile);	
		
				strFileTransfer_Ext.inFileTransferNameSize = inmyFileSize;
				vdDebug_LogPrintf("uszFileTransferName =[%s]", strFileTransfer_Ext.uszFileTransferName);
				vdDebug_LogPrintf("inFileTransferNameSize =[%d]", strFileTransfer_Ext.inFileTransferNameSize);
		
				memcpy(&szECRSendData[offset], &strFileTransfer_Ext, sizeof(STRUCT_FILE_TRANSFER_EXT));
				offset += sizeof(STRUCT_FILE_TRANSFER_EXT);
				
				memset(szDispMsg, 0x00, sizeof(szDispMsg));
				sprintf(szDispMsg, "offset(%d)", offset);	
				vdDisplayDebugMsg(1, 10, szDispMsg);	
				vdDebug_LogPrintf("offset(%d)", offset);
				
				vdDebug_LogPrintf("offset [%d)]", offset);
				
				//inRet = inECRSendComPacket(szECRSendData, offset,VS_TRUE, 5, VS_TRUE, 3); 
				inRet = inUSBSendComPacket(szECRSendData, offset, VS_TRUE, 5);
				//inRet = inCTOSS_V3PRS232SendBuf(szECRSendData, offset);	
				vdDebug_LogPrintf("inRet [%d]", inRet);
	
				memset(szDispMsg, 0x00, sizeof(szDispMsg));
				sprintf(szDispMsg, "inRet(%d)", inRet); 
				vdDisplayDebugMsg(1, 10, szDispMsg);
				vdDebug_LogPrintf("inRet(%d)", inRet);


				
				memset(szRecvBuf,0x00,sizeof(szRecvBuf));
				inRet = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, 500);
				vdDebug_LogPrintf("inCTOSS_V3PRS232RecvACK inRet[%d] szRecvBuf[%d]", inRet, szRecvBuf[0]);
				DebugAddHEX("inCTOSS_V3PRS232RecvACK", szRecvBuf, inRecvlen);

			
				
				if (VS_SUCCESS == inRet)
				{
					fseek (ptr_myfile, 0, SEEK_SET);
					memset(uszDataBuffer, 0x00, sizeof(uszDataBuffer));
					fread(uszDataBuffer, inmyFileSize, 1, ptr_myfile);
	
					memset(szDispMsg, 0x00, sizeof(szDispMsg));
					sprintf(szDispMsg, "inmyFileSize(%d)", inmyFileSize);
					vdDebug_LogPrintf("inmyFileSize(%d)", inmyFileSize);
					vdDisplayDebugMsg(1, 10, szDispMsg);
					vdDebug_LogPrintf("inmyFileSize [%d)]", inmyFileSize);
					
				    //inRet = inECRSendComPacket(uszDataBuffer, inmyFileSize,VS_TRUE, 5, VS_TRUE, 3); 
	
					//inRet = inCTOSS_V3PRS232SendBuf(uszDataBuffer, inmyFileSize); 
					
					inRet = inUSBSendComPacket(uszDataBuffer, inmyFileSize, VS_TRUE, 5); 
					memset(szDispMsg, 0x00, sizeof(szDispMsg));
					sprintf(szDispMsg, "inRet(%d)", inRet); 
					vdDebug_LogPrintf("inRet(%d)", inRet);
					vdDisplayDebugMsg(1, 10, szDispMsg);
					
					vdDebug_LogPrintf("inRet [%d]", inRet);

					memset(szRecvBuf,0x00,sizeof(szRecvBuf));
					inRet = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, 500);
					vdDebug_LogPrintf("inCTOSS_V3PRS232RecvACK inRet[%d] szRecvBuf[%d]", inRet, szRecvBuf[0]);
					DebugAddHEX("inCTOSS_V3PRS232RecvACK", szRecvBuf, inRecvlen);

	
	
					//CTOS_Delay(10000);
				}
		
				fclose(ptr_myfile);
				inRet = d_OK;
	
			}
}



int inSendSyncCtosData(char *szFileName)
{
		char  szSystemCmdPath[250] = {0};
		char  szGzipFileName[250] = {0};
        BYTE szECRSendData[4096 + 2];	
		unsigned char uszDataBuffer[15000+1] = {0};
		char szDispMsg[20];
		int inOffset = 0;
		int inRet = VS_SUCCESS;	
		int offset;
		char szRecvBuf[1024+1];
		int inRecvlen = 0;
			
			memset(szGzipFileName, 0x00, sizeof(szGzipFileName));
			sprintf(szGzipFileName, "%s%s",  szFileName, ".gz");
			unlink(szGzipFileName);
	
			memset(szSystemCmdPath, 0x00, sizeof(szSystemCmdPath));
			sprintf(szSystemCmdPath, "gzip -c %s > %s",  szFileName, szGzipFileName);
			system(szSystemCmdPath);
	
			// pack EXT command
			offset = 0;
			memset(szECRSendData, 0x00, sizeof(szECRSendData));
			memcpy(&szECRSendData[offset], "SYNC_DATA", strlen("SYNC_DATA"));
			offset += strlen("SYNC_DATA");
			szECRSendData[offset] = '|';
			offset += 1;
	
			memset(&strFileTransfer_Ext, 0x00, sizeof(STRUCT_FILE_TRANSFER_EXT));		
			strcpy(strFileTransfer_Ext.uszFileTransferName, szGzipFileName);
	
			FILE *ptr_myfile =	NULL;
			int inmyFileSize = 0;
			ptr_myfile = fopen(strFileTransfer_Ext.uszFileTransferName, "rb");
			vdDebug_LogPrintf("ptr_myfile [%x]", ptr_myfile);
			if (!ptr_myfile)
			{
				vdDebug_LogPrintf("Unable to open file! [%s]", szGzipFileName);
			}
			else
			{
				inmyFileSize = fseek(ptr_myfile, 0, SEEK_END);
						
				if(inmyFileSize >= 0)
					inmyFileSize = ftell(ptr_myfile);	
		
				strFileTransfer_Ext.inFileTransferNameSize = inmyFileSize;
				vdDebug_LogPrintf("uszFileTransferName =[%s]", strFileTransfer_Ext.uszFileTransferName);
				vdDebug_LogPrintf("inFileTransferNameSize =[%d]", strFileTransfer_Ext.inFileTransferNameSize);
		
				memcpy(&szECRSendData[offset], &strFileTransfer_Ext, sizeof(STRUCT_FILE_TRANSFER_EXT));
				offset += sizeof(STRUCT_FILE_TRANSFER_EXT);
				
				memset(szDispMsg, 0x00, sizeof(szDispMsg));
				sprintf(szDispMsg, "offset(%d)", offset);	
				vdDisplayDebugMsg(1, 10, szDispMsg);	
				vdDebug_LogPrintf("offset(%d)", offset);
				
				vdDebug_LogPrintf("offset [%d)]", offset);
				
				//inRet = inECRSendComPacket(szECRSendData, offset,VS_TRUE, 5, VS_TRUE, 3); 
				inRet = inUSBSendComPacket(szECRSendData, offset, VS_TRUE, 5);
				//inRet = inCTOSS_V3PRS232SendBuf(szECRSendData, offset);	
				vdDebug_LogPrintf("inRet [%d]", inRet);
	
				memset(szDispMsg, 0x00, sizeof(szDispMsg));
				sprintf(szDispMsg, "inRet(%d)", inRet); 
				vdDisplayDebugMsg(1, 10, szDispMsg);
				vdDebug_LogPrintf("inRet(%d)", inRet);


				
				memset(szRecvBuf,0x00,sizeof(szRecvBuf));
				inRet = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, 500);
				vdDebug_LogPrintf("inCTOSS_V3PRS232RecvACK inRet[%d] szRecvBuf[%d]", inRet, szRecvBuf[0]);
				DebugAddHEX("inCTOSS_V3PRS232RecvACK", szRecvBuf, inRecvlen);

			
				
				if (VS_SUCCESS == inRet)
				{
					fseek (ptr_myfile, 0, SEEK_SET);
					memset(uszDataBuffer, 0x00, sizeof(uszDataBuffer));
					fread(uszDataBuffer, inmyFileSize, 1, ptr_myfile);
	
					memset(szDispMsg, 0x00, sizeof(szDispMsg));
					sprintf(szDispMsg, "inmyFileSize(%d)", inmyFileSize);
					vdDebug_LogPrintf("inmyFileSize(%d)", inmyFileSize);
					vdDisplayDebugMsg(1, 10, szDispMsg);
					vdDebug_LogPrintf("inmyFileSize [%d)]", inmyFileSize);
					
				    //inRet = inECRSendComPacket(uszDataBuffer, inmyFileSize,VS_TRUE, 5, VS_TRUE, 3); 
	
					//inRet = inCTOSS_V3PRS232SendBuf(uszDataBuffer, inmyFileSize); 
					
					inRet = inUSBSendComPacket(uszDataBuffer, inmyFileSize, VS_TRUE, 5); 
					memset(szDispMsg, 0x00, sizeof(szDispMsg));
					sprintf(szDispMsg, "inRet(%d)", inRet); 
					vdDebug_LogPrintf("inRet(%d)", inRet);
					vdDisplayDebugMsg(1, 10, szDispMsg);
					
					vdDebug_LogPrintf("inRet [%d]", inRet);

					memset(szRecvBuf,0x00,sizeof(szRecvBuf));
					inRet = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, 500);
					vdDebug_LogPrintf("inCTOSS_V3PRS232RecvACK inRet[%d] szRecvBuf[%d]", inRet, szRecvBuf[0]);
					DebugAddHEX("inCTOSS_V3PRS232RecvACK", szRecvBuf, inRecvlen);

	
	
					//CTOS_Delay(10000);
				}
		
				fclose(ptr_myfile);
				inRet = d_OK;
	
			}
}


int inSendSyncTransRecData(TRANS_DATA_TABLE srTransRec)
{
		char  szSystemCmdPath[250] = {0};
		char  szGzipFileName[250] = {0};
        BYTE szECRSendData[4096 + 2];	
		unsigned char uszDataBuffer[15000+1] = {0};
		char szDispMsg[20];
		int inOffset = 0;
		int inRet = VS_SUCCESS;	
		int offset;
		char szRecvBuf[1024+1];
		int inRecvlen = 0;
	
		// pack EXT command
		offset = 0;
		memset(szECRSendData, 0x00, sizeof(szECRSendData));
		memcpy(&szECRSendData[offset], "NPX_TRANSREC_DATA", strlen("NPX_TRANSREC_DATA"));
		offset += strlen("NPX_TRANSREC_DATA");
		szECRSendData[offset] = '|';
		offset += 1;
	
		
		memset(szDispMsg,0x00,sizeof(szDispMsg));
		sprintf(szDispMsg,"%04d",sizeof(TRANS_DATA_TABLE));
		memcpy(&szECRSendData[offset], szDispMsg, 4);
		offset += 4;
		szECRSendData[offset] = '|';
		offset += 1;
		
		memcpy(&szECRSendData[offset], &srTransRec, sizeof(TRANS_DATA_TABLE));
		offset += sizeof(TRANS_DATA_TABLE);
		
		 vdDebug_LogPrintf("szHostLabel=[%s]....",srTransRec.szHostLabel);
	 	vdPCIDebug_HexPrintf("szBatchNo",srTransRec.szBatchNo, 3);
	   vdDebug_LogPrintf("MITid=[%d]....",srTransRec.MITid);
		vdDebug_LogPrintf("offset [%d)]", offset);
		
		//inRet = inECRSendComPacket(szECRSendData, offset,VS_TRUE, 5, VS_TRUE, 3); 
		inRet = inUSBSendComPacket(szECRSendData, offset, VS_TRUE, 5);
		//inRet = inCTOSS_V3PRS232SendBuf(szECRSendData, offset);	
		vdDebug_LogPrintf("inRet [%d]", inRet);

		memset(szDispMsg, 0x00, sizeof(szDispMsg));
		sprintf(szDispMsg, "inRet(%d)", inRet); 
		vdDisplayDebugMsg(1, 10, szDispMsg);
		vdDebug_LogPrintf("inRet(%d)", inRet);

		memset(szRecvBuf,0x00,sizeof(szRecvBuf));
		inRet = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, 500);
		vdDebug_LogPrintf("inCTOSS_V3PRS232RecvACK inRet[%d] szRecvBuf[%d]", inRet, szRecvBuf[0]);
		DebugAddHEX("inCTOSS_V3PRS232RecvACK", szRecvBuf, inRecvlen);

}



int inSendReceiptToTerminal(void)
{
#define PRINT_BMP		"Print_BMP.bmp"
		char  szSystemCmdPath[250] = {0};
		char  szGzipFileName[250] = {0};
        BYTE szECRSendData[4096 + 2];	
		unsigned char uszDataBuffer[10240+1] = {0};
		char szDispMsg[20];
		int inOffset = 0;
		int inRet = VS_SUCCESS;	
		int offset;
		char szRecvBuf[1024+1];
		int inRecvlen = 0;

		vdCTOSS_SetV3PRS232Port(strTCT.byRS232ECRPort);
		inCTOSS_V3PRS232Open(strTCT.inPPBaudRate, 'N', 8, 1);
		
			memset(szGzipFileName, 0x00, sizeof(szGzipFileName));
			sprintf(szGzipFileName, "%s%s%s", PUBLIC_PATH, PRINT_BMP, ".gz");
			unlink(szGzipFileName);
	
			memset(szSystemCmdPath, 0x00, sizeof(szSystemCmdPath));
			sprintf(szSystemCmdPath, "gzip -c %s/%s > %s", PUBLIC_PATH, PRINT_BMP, szGzipFileName);
			system(szSystemCmdPath);
	
			// pack EXT command
			offset = 0;
			memset(szECRSendData, 0x00, sizeof(szECRSendData));
			memcpy(&szECRSendData[offset], "PRINT_RECEIPT", strlen("PRINT_RECEIPT"));
			offset += strlen("PRINT_RECEIPT");
			szECRSendData[offset] = '|';
			offset += 1;
	
			// memset((strFileTransfer_Ext, 0x00, sizeof(STRUCT_FILE_TRANSFER_EXT));
			strcpy(strFileTransfer_Ext.uszFileTransferName, szGzipFileName);
	
			FILE *ptr_myfile =	NULL;
			int inmyFileSize = 0;
			ptr_myfile = fopen(strFileTransfer_Ext.uszFileTransferName, "rb");
			vdDebug_LogPrintf("ptr_myfile [%x]", ptr_myfile);
			if (!ptr_myfile)
			{
				vdDebug_LogPrintf("Unable to open file! [%s]", szGzipFileName);
			}
			else
			{
				inmyFileSize = fseek(ptr_myfile, 0, SEEK_END);
						
				if(inmyFileSize >= 0)
					inmyFileSize = ftell(ptr_myfile);	
		
				strFileTransfer_Ext.inFileTransferNameSize = inmyFileSize;
		
				memcpy(&szECRSendData[offset], &strFileTransfer_Ext, sizeof(STRUCT_FILE_TRANSFER_EXT));
				offset += sizeof(STRUCT_FILE_TRANSFER_EXT);
				
				memset(szDispMsg, 0x00, sizeof(szDispMsg));
				sprintf(szDispMsg, "offset(%d)", offset);	
				vdDisplayDebugMsg(1, 10, szDispMsg);	
				vdDebug_LogPrintf("offset(%d)", offset);
				
				vdDebug_LogPrintf("offset [%d)]", offset);
				
				//inRet = inECRSendComPacket(szECRSendData, offset,VS_TRUE, 5, VS_TRUE, 3); 
				inRet = inUSBSendComPacket(szECRSendData, offset, VS_TRUE, 5);
				vdDebug_LogPrintf("inRet [%d)]", inRet);
	
				memset(szDispMsg, 0x00, sizeof(szDispMsg));
				sprintf(szDispMsg, "inRet(%d)", inRet); 
				vdDisplayDebugMsg(1, 10, szDispMsg);
				vdDebug_LogPrintf("inRet(%d)", inRet);
				
				memset(szRecvBuf,0x00,sizeof(szRecvBuf));
				inRet = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, 500);
				vdDebug_LogPrintf("inCTOSS_V3PRS232RecvACK inRet[%d] szRecvBuf[%d]", inRet, szRecvBuf[0]);
				DebugAddHEX("inCTOSS_V3PRS232RecvACK", szRecvBuf, inRecvlen);
	
			
				
				if (VS_SUCCESS == inRet)
				{
					fseek (ptr_myfile, 0, SEEK_SET);
					memset(uszDataBuffer, 0x00, sizeof(uszDataBuffer));
					fread(uszDataBuffer, inmyFileSize, 1, ptr_myfile);
	
					memset(szDispMsg, 0x00, sizeof(szDispMsg));
					sprintf(szDispMsg, "inmyFileSize(%d)", inmyFileSize);
					vdDebug_LogPrintf("inmyFileSize(%d)", inmyFileSize);
					vdDisplayDebugMsg(1, 10, szDispMsg);
					vdDebug_LogPrintf("inmyFileSize [%d)]", inmyFileSize);
					
					//inRet = xmodemTransmit((unsigned char *)uszDataBuffer, inmyFileSize);
				    //inRet = inECRSendComPacket(uszDataBuffer, inmyFileSize,VS_TRUE, 5, VS_TRUE, 3);
					inRet = inUSBSendComPacket(uszDataBuffer, inmyFileSize, VS_TRUE, 5);
	
					memset(szDispMsg, 0x00, sizeof(szDispMsg));
					sprintf(szDispMsg, "inRet(%d)", inRet); 
					vdDebug_LogPrintf("inRet(%d)", inRet);
					vdDisplayDebugMsg(1, 10, szDispMsg);
					
					vdDebug_LogPrintf("inRet [%d)]", inRet);

					memset(szRecvBuf,0x00,sizeof(szRecvBuf));
					inRet = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, 500);
					vdDebug_LogPrintf("inCTOSS_V3PRS232RecvACK inRet[%d] szRecvBuf[%d]", inRet, szRecvBuf[0]);
					DebugAddHEX("inCTOSS_V3PRS232RecvACK", szRecvBuf, inRecvlen);
	
					//CTOS_Delay(10000);
				}
		
				fclose(ptr_myfile);
				inRet = d_OK;
	
			}

	inCTOSS_V3PRS232Close();
}

int inEndTransaction(void)
{
	char szV3PSendBuf[4096+1];
	int inOffSet = 0;
	int status;
	char szRecvBuf[3000+1];
	int inRecvlen = 0;
	USHORT usResult;
		
	vdDebug_LogPrintf("inEndTransaction..............");

	vdCTOSS_SetV3PRS232Port(strTCT.byRS232ECRPort);
	inCTOSS_V3PRS232Open(strTCT.inPPBaudRate, 'N', 8, 1);

	memset(szV3PSendBuf, 0x00, sizeof(szV3PSendBuf));
	inOffSet = 0;
	szV3PSendBuf[inOffSet] = STX;
	inOffSet += 1;
	memcpy(&szV3PSendBuf[inOffSet], "END_TRX", strlen("END_TRX"));
	inOffSet += strlen("END_TRX");
	szV3PSendBuf[inOffSet] = '|';
	inOffSet += 1;
	///////////////////////////////////////////////////////////////////////
	szV3PSendBuf[inOffSet] = ETX;
	inOffSet += 1;			
	szV3PSendBuf[inOffSet] = (char) wub_lrc(&(szV3PSendBuf[1]), inOffSet-1);
	inOffSet += 1;
	inCTOSS_V3PRS232SendBuf(szV3PSendBuf, inOffSet);


	memset(szRecvBuf,0x00,sizeof(szRecvBuf));
	usResult = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, 500);

	inCTOSS_V3PRS232Close();
	
	if (usResult != d_OK)
		return d_NO;
	
	return d_OK;
}


int inUpdateTransData(unsigned char *uszSendData,unsigned long ulSendLen)
{
	char szV3PSendBuf[4096+1];
	int inOffSet = 0;
	int status;
	char szRecvBuf[3000+1];
	int inRecvlen = 0;
	USHORT usResult;
		
	vdDebug_LogPrintf("inUpdateTransData..............");

	return d_OK;

	vdCTOSS_SetV3PRS232Port(strTCT.byRS232ECRPort);
	inCTOSS_V3PRS232Open(strTCT.inPPBaudRate, 'N', 8, 1);

	// send STX INJECT_KEY ETX LRC
	memset(szV3PSendBuf, 0x00, sizeof(szV3PSendBuf));
	inOffSet = 0;
	szV3PSendBuf[inOffSet] = STX;
	inOffSet += 1;
	memcpy(&szV3PSendBuf[inOffSet], "COMM_SEND", strlen("COMM_SEND"));
	inOffSet += strlen("COMM_SEND");
	szV3PSendBuf[inOffSet] = '|';
	inOffSet += 1;
	//////////////////////////////////////////////////////////////////////////
	memcpy(&szV3PSendBuf[inOffSet], &ulSendLen, sizeof(unsigned long));
	inOffSet += sizeof(unsigned long);
	szV3PSendBuf[inOffSet] = '|';
	inOffSet += 1;
	//////////////////////////////////////////////////////////////////////////
	memcpy(&szV3PSendBuf[inOffSet], uszSendData, ulSendLen);
	inOffSet += ulSendLen;
	///////////////////////////////////////////////////////////////////////
	szV3PSendBuf[inOffSet] = ETX;
	inOffSet += 1;			
	szV3PSendBuf[inOffSet] = (char) wub_lrc(&(szV3PSendBuf[1]), inOffSet-1);
	inOffSet += 1;
	inCTOSS_V3PRS232SendBuf(szV3PSendBuf, inOffSet);


	memset(szRecvBuf,0x00,sizeof(szRecvBuf));
	usResult = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, 500);

	inCTOSS_V3PRS232Close();
	
	if (usResult != d_OK)
		return d_NO;
	
	return d_OK;
}






int inNETS_Logon_USB(void)
{
	CTLS_Trans strCTLSTrans;
	CTLS_TransData strCTLSTransData;
	unsigned char *pszPtr = NULL;
	USHORT usResult = 0;

	char szV3PSendBuf[1024+1] = {0};
	int inOffSet = 0;
	unsigned char szRecvBuf[1024+1] = {0};
	int inRecvlen = 0;
	unsigned char uszDataBuffer[10240+1] = {0};
	unsigned char uszFileBuffer[10240+1] = {0};
	char  szSystemCmdPath[250];
	unsigned char szRecvCommBuf[2048+1] = {0};
	int inRecvCommlen = 0; 
    char szFileName[20];

	vdDebug_LogPrintf("inNETS_Purchase_USB..strTCT.byPinPadType=[%d].strTCT.byPinPadMode=[%d].strTCT.byPinPadPort=[%d]..",strTCT.byPinPadType,strTCT.byPinPadMode,strTCT.byPinPadPort);
	memcpy(srTransRec.szBaseAmount, "\x00\x00\x00\x00\x10\x00", 6);

	CTOS_LCDTClearDisplay();


	if(strTCT.byPinPadMode == 0)
	{
		if(strTCT.byPinPadType == 4)
		{
			char szV3PSendBuf[1024+1] = {0};
			int inOffSet = 0;
			char szRecvBuf[1024+1];
			int inRecvlen = 0;
			int status;

			vdDebug_LogPrintf("NETS_LOGON EXT CMD");
			
			vdCTOSS_SetV3PRS232Port(strTCT.byPinPadPort);
			inCTOSS_V3PRS232Open(strTCT.inPPBaudRate, 'N', 8, 1);

			memset(szV3PSendBuf, 0x00, sizeof(szV3PSendBuf));
			inOffSet = 0;
			szV3PSendBuf[inOffSet] = STX;
			inOffSet += 1;
			memcpy(&szV3PSendBuf[inOffSet], "NETS_LOGON", strlen("NETS_LOGON"));
			inOffSet += strlen("NETS_LOGON");
			szV3PSendBuf[inOffSet] = '|';
			inOffSet += 1;

			memset(&strCTLSTrans, 0x00, sizeof(CTLS_Trans));
			memcpy(strCTLSTrans.szAmount, srTransRec.szBaseAmount, 7);
			
			memcpy(&szV3PSendBuf[inOffSet], &strCTLSTrans, sizeof(CTLS_Trans));
			inOffSet += sizeof(CTLS_Trans);

			szV3PSendBuf[inOffSet] = ETX;
			inOffSet += 1;			
			szV3PSendBuf[inOffSet] = (char) wub_lrc(&(szV3PSendBuf[1]), inOffSet-1);
			inOffSet += 1;
			inCTOSS_V3PRS232SendBuf(szV3PSendBuf, inOffSet);	

			
			memset(szRecvBuf,0x00,sizeof(szRecvBuf));
			usResult = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, 500);
			vdDebug_LogPrintf("inCTOSS_V3PRS232RecvACK usResult[%d] szRecvBuf[%d]", usResult, szRecvBuf[0]);
			DebugAddHEX("inCTOSS_V3PRS232RecvACK", szRecvBuf, inRecvlen);

			memset(szRecvBuf,0x00,sizeof(szRecvBuf));
			usResult = inCTOSS_V3PRS232RecvBuf_EXT(szRecvBuf, &inRecvlen, 500);
			
			vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf_EXT usResult[%d] inRecvlen[%d]", usResult, inRecvlen);
			DebugAddHEX("inCTOSS_V3PRS232RecvBuf", szRecvBuf, inRecvlen);

			do{
				
				if ((memcmp((unsigned char *)&szRecvBuf[1],"PRINT_RECEIPT", strlen("PRINT_RECEIPT"))==0)||(memcmp((unsigned char *)&szRecvBuf[0],"PRINT_RECEIPT", strlen("PRINT_RECEIPT"))==0))
				{

				    vdDebug_LogPrintf("PRINT_RECEIPT");
					

					pszPtr = NULL;
					pszPtr = (char*)memchr(szRecvBuf, '|', inRecvlen); // check STX
					if (NULL == pszPtr)
					{
						//sometime the response error, may can not get |, 
						//pszPtr will be NULL, copy will cause crash
						// return d_NO;
					}
					else
					{
					
						szV3PSendBuf[0] = ACK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);

						
						vdDebug_LogPrintf("Send ACK End");

						FILE *ptr_myfile;
				
						memset(&strFileTransfer_Ext, 0x00, sizeof(STRUCT_FILE_TRANSFER_EXT));	
						memcpy(&strFileTransfer_Ext, &pszPtr[1], sizeof(STRUCT_FILE_TRANSFER_EXT)); // copy transaction amount?

						unlink((char *)strFileTransfer_Ext.uszFileTransferName);
					
						ptr_myfile = fopen(strFileTransfer_Ext.uszFileTransferName, "wb");
						if (!ptr_myfile)
						{
							vdDebug_LogPrintf("Unable to open file!");
							inCTOSS_V3PRS232Close();
							return 1;
						}
					
						fseek(ptr_myfile, 0L, SEEK_SET);
 
						memset(uszDataBuffer, 0x00, sizeof(uszDataBuffer));
						vdDebug_LogPrintf("xmodemReceive start");


						vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf");
						memset(szRecvBuf,0x00,sizeof(szRecvBuf));
						memset(uszFileBuffer, 0x00, sizeof(uszFileBuffer));
						
						usResult = inCTOSS_V3PRS232RecvBuf_EXT(uszDataBuffer, &inRecvlen, 5000);
						vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf usResult[%d] inRecvlen[%d]", usResult, inRecvlen);

						memcpy(uszFileBuffer, &uszDataBuffer[1], inRecvlen-3);
						vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf");
						DebugAddHEX("inCTOSS_V3PRS232RecvBuf", uszDataBuffer, inRecvlen);

						
						szV3PSendBuf[0] = ACK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
						vdDebug_LogPrintf("Send ACK");
					

						vdDebug_LogPrintf("xmodemReceive end usResult [%d]", usResult);
						fwrite(uszFileBuffer, strFileTransfer_Ext.inFileTransferNameSize, 1, ptr_myfile);
						fclose(ptr_myfile);
						vdDebug_LogPrintf("strFileTransfer_Ext.uszFileTransferName[%s]", strFileTransfer_Ext.uszFileTransferName);
						memset(szSystemCmdPath, 0x00, sizeof(szSystemCmdPath)); 			
						sprintf(szSystemCmdPath, "gzip -f -d %s", strFileTransfer_Ext.uszFileTransferName);
						system(szSystemCmdPath);

						memset(szFileName, 0x00, sizeof(szFileName));
						strncpy(szFileName, strFileTransfer_Ext.uszFileTransferName, strlen(strFileTransfer_Ext.uszFileTransferName) -3);
						vdDebug_LogPrintf("szFileName[%s]", szFileName);
						
						displaybmpEx(0, 32, szFileName);

						vdPrintReceipt(szFileName);

						
					}				
				}
				
				if (memcmp((unsigned char *)&szRecvBuf[1],"COMM_INIT", strlen("COMM_INIT"))==0)
				{
					inOffSet = 1 + strlen("COMM_INIT");
					usResult = inCOMMS_Init_Terminal_Ext(&szRecvBuf[inOffSet]);
					if (usResult != d_OK)
					{
						szV3PSendBuf[0] = NAK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
						inCTOSS_V3PRS232Close();
						return 0;
					}
					else
					{
						szV3PSendBuf[0] = ACK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
					}
				}
				if (memcmp((unsigned char *)&szRecvBuf[1],"COMM_CONNECT", strlen("COMM_CONNECT"))==0)
				{
					inOffSet = 1 + strlen("COMM_CONNECT");
					usResult = inCOMMS_Connect_Terminal_Ext(&szRecvBuf[inOffSet]);
					if (usResult != d_OK)
					{
						szV3PSendBuf[0] = NAK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
						inCTOSS_V3PRS232Close();
						return 0;
					}
					else
					{
						szV3PSendBuf[0] = ACK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
					}
				}
				if (memcmp((unsigned char *)&szRecvBuf[1],"COMM_SEND", strlen("COMM_SEND"))==0)
				{
					inOffSet = 1 + strlen("COMM_SEND");
					usResult = inCOMMS_Send_Terminal_Ext(&szRecvBuf[inOffSet]);
					if (usResult != d_OK)
					{
						szV3PSendBuf[0] = NAK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
						inCTOSS_V3PRS232Close();
						return 0;
					}
					else
					{
						szV3PSendBuf[0] = ACK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
					}
				}
				if (memcmp((unsigned char *)&szRecvBuf[1],"COMM_RECEVIE", strlen("COMM_RECEVIE"))==0)
				{
					memset(szRecvCommBuf,0x00,sizeof(szRecvCommBuf));
					inOffSet = 1 + strlen("COMM_RECEVIE");
					usResult = inCOMMS_Recieve_Terminal_Ext(szRecvCommBuf,&inRecvCommlen);
					if (usResult != d_OK)
					{
						szV3PSendBuf[0] = NAK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
						inCTOSS_V3PRS232Close();
						return 0;
					}
					else
					{
						vdPCIDebug_HexPrintf("szRecvCommBuf",szRecvCommBuf,inRecvCommlen);
						inCTOSS_V3PRS232SendBuf(szRecvCommBuf, inRecvCommlen);
					}
				}
				if (memcmp((unsigned char *)&szRecvBuf[1],"COMM_DISCONNECT", strlen("COMM_DISCONNECT"))==0)
				{
					inOffSet = 1 + strlen("COMM_DISCONNECT");
					usResult = inCOMMS_Disconnect_Terminal_Ext();
					vdDebug_LogPrintf("COMM_DISCONNECT usResult[%d]", usResult);
					if (usResult != d_OK)
					{
						szV3PSendBuf[0] = NAK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
						//return 0;
					}
					else
					{
						szV3PSendBuf[0] = ACK;
						inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
						//return 0;
					}
				}


				
				
				if (memcmp((unsigned char *)&szRecvBuf[1],"END_TRX", strlen("END_TRX"))==0)
				{
					szV3PSendBuf[0] = ACK;
					inCTOSS_V3PRS232SendBuf(szV3PSendBuf, 1);
					vdDebug_LogPrintf("END_TRX ACK");
					inCTOSS_V3PRS232Close();

					return 0;				
				}

				
				vdDebug_LogPrintf("inCTOSS_V3PRS232RecvBuf");
				memset(szRecvBuf,0x00,sizeof(szRecvBuf));
				usResult = inCTOSS_V3PRS232RecvBuf_EXT(szRecvBuf, &inRecvlen, 500);
				
				DebugAddHEX("inCTOSS_V3PRS232RecvBuf", szRecvBuf, inRecvlen);
				
			}while(1);

			inCTOSS_V3PRS232Close();
			
		}
	}
}




int inNETS_2PCSGetResponseText(BYTE *RespMsg)
{

	BYTE szErrCode[4+1];
    BYTE szErrMsg[99+1];
	BYTE szTransAmt[12+1];


	memset(szErrCode, 0x00, sizeof(szErrCode));
	memset(szErrMsg, 0x00, sizeof(szErrMsg));
	
	vdDebug_LogPrintf("inNETS_2PCSGetResponseText srTransRec.szRespCode[%s]", srTransRec.szRespCode);
	

	if (memcmp(srTransRec.szRespCode, "00", 2) == 0)
	{
			memcpy(RespMsg, "APPROVED", strlen("APPROVED"));

			memset(szTransAmt, 0x00, sizeof(szTransAmt));
			wub_hex_2_str(srTransRec.szTotalAmount, szTransAmt, 6);	
			strcat(RespMsg, szTransAmt);
			strcat(RespMsg, srTransRec.szAuthCode);
	}
	else
	{
		ushCTOS_GetHostErrMsg(szErrCode, szErrMsg);
		strcpy(RespMsg, szErrMsg);
		
	}

	vdDebug_LogPrintf("inNETS_2PCSGetResponseText RespMsg[%s]", RespMsg);
	
}

int inNETS_SyncRespMsg_EXT_Terminal(BYTE *uszRespMsg)
{
    BYTE szPPSendData[4096 + 2];	
	int inRet = VS_SUCCESS;	
	int offset=0;
	char szRecvBuf[1024+1];
	int inRecvlen = 0;

	vdDebug_LogPrintf("inNETS_SyncRespMsg_EXT_Terminal	CMD");
	
	vdDebug_LogPrintf("strTCT.byRS232ECRPort[%d]", strTCT.byRS232ECRPort);
	if (strTCTEX.fEXT == 1)
	{
		
		vdCTOSS_SetV3PRS232Port(strTCT.byRS232ECRPort);
		inCTOSS_V3PRS232Open(strTCT.inPPBaudRate, 'N', 8, 1);

		offset = 0;
		memset(szPPSendData, 0x00, sizeof(szPPSendData));
		memcpy(&szPPSendData[offset], "TRANSRESP_DATA", strlen("TRANSRESP_DATA"));
		offset += strlen("TRANSRESP_DATA");
		szPPSendData[offset] = '|';
		offset += 1;
		
		strcpy(&szPPSendData[offset], uszRespMsg);
		offset += 99+1;
		
		
		vdDebug_LogPrintf("offset [%d)]", offset);
		
		inRet = inUSBSendComPacket(szPPSendData, offset, VS_TRUE, 5);
		vdDebug_LogPrintf("inRet [%d]", inRet);

		memset(szRecvBuf,0x00,sizeof(szRecvBuf));
		inRet = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, 500);
		vdDebug_LogPrintf("inCTOSS_V3PRS232RecvACK inRet[%d] szRecvBuf[%d]", inRet, szRecvBuf[0]);
		DebugAddHEX("inCTOSS_V3PRS232RecvACK", szRecvBuf, inRecvlen);

		inCTOSS_V3PRS232Close();
	}
	
}


int inNETS_2PCSSyncRespMsg(void)
{
    BYTE RespMsg[99+1];
 
    memset(RespMsg, 0x00, sizeof(RespMsg));
	inNETS_2PCSGetResponseText(RespMsg);
	inNETS_SyncRespMsg_EXT_Terminal(RespMsg);
}


int inNETS_SyncRemoveCardMsg_EXT_Terminal(BYTE *uszRespMsg)
{
    BYTE szPPSendData[4096 + 2];	
	int inRet = VS_SUCCESS;	
	int offset=0;
	char szRecvBuf[1024+1];
	int inRecvlen = 0;

	vdDebug_LogPrintf("inNETS_SyncRemoveCardMsg_EXT_Terminal	CMD");
	
	vdDebug_LogPrintf("strTCT.byRS232ECRPort[%d]", strTCT.byRS232ECRPort);
	if (strTCTEX.fEXT == 1)
	{
		
		vdCTOSS_SetV3PRS232Port(strTCT.byRS232ECRPort);
		inCTOSS_V3PRS232Open(strTCT.inPPBaudRate, 'N', 8, 1);

		offset = 0;
		memset(szPPSendData, 0x00, sizeof(szPPSendData));
		memcpy(&szPPSendData[offset], "TRANSMSGRMC_DATA", strlen("TRANSMSGRMC_DATA"));
		offset += strlen("TRANSMSGRMC_DATA");
		szPPSendData[offset] = '|';
		offset += 1;
		
		strcpy(&szPPSendData[offset], uszRespMsg);
		offset += 21+1;
		
		
		vdDebug_LogPrintf("offset [%d)]", offset);
		
		inRet = inUSBSendComPacket(szPPSendData, offset, VS_TRUE, 5);
		vdDebug_LogPrintf("inRet [%d]", inRet);

		memset(szRecvBuf,0x00,sizeof(szRecvBuf));
		inRet = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, 500);
		vdDebug_LogPrintf("inCTOSS_V3PRS232RecvACK inRet[%d] szRecvBuf[%d]", inRet, szRecvBuf[0]);
		DebugAddHEX("inCTOSS_V3PRS232RecvACK", szRecvBuf, inRecvlen);

		inCTOSS_V3PRS232Close();
	}
	
}


int inNETS_2PCSSyncRemoveCardMsg(void)
{
    BYTE RemoveCardMsg[21+1];
	 
	if(strTCTEX.fEXT == 1)
	{

     memset(RemoveCardMsg, 0x00, sizeof(RemoveCardMsg));
	 memcpy(RemoveCardMsg, "Please remove card...", strlen("Please remove card..."));
	 inNETS_SyncRemoveCardMsg_EXT_Terminal(RemoveCardMsg);
	}
	 
}

