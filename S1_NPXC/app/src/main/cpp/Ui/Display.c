#include <string.h>
#include <stdio.h>
#include <ctosapi.h>
#include <stdlib.h>
#include <stdarg.h>

#include "../Includes/msg.h"
#include "../Includes/wub_lib.h"
#include "../Includes/myEZLib.h"
#include "../Includes/POSTypedef.h"
#include "../Includes/POSTrans.h"
#include "..\Includes\MultiApLib.h"

#include "display.h"
#include "../FileModule/myFileFunc.h"
#include "../print/Print.h"
#include "../Includes/CTOSinput.h"
#include "../UI/Display.h"
#include "../Comm/V5Comm.h"
#include "..\Includes\POSSetting.h"
#include "..\Debug\Debug.h"
#include "..\Aptrans\MultiAptrans.h"
#include "..\PCI100\COMMS.h"
#include "../External/External.h"

extern USHORT GPRSCONNETSTATUS;

#define ERRORLEN 30
static char szErrorMessage[ERRORLEN+1];
STRUCT_DISPLAY_EXT strDisplayEXT;
extern OLPIN_PARAM	stOLPinParam;

void setLCDReverse(int line,int position, char *pbBuf)
{
    int iInitX = 0;
    int lens = 0;

    //Set the reverse attribute of the character //
    CTOS_LCDTSetReverse(TRUE);  //the reverse enable // 
    
    switch(position)
    {
        case DISPLAY_POSITION_LEFT:
            //CTOS_LCDTPrintXY(1, line, pbBuf);
			CTOS_LCDTPrintAligned(line, pbBuf, d_LCD_ALIGNLEFT); 
			break;
        case DISPLAY_POSITION_CENTER:
            lens = strlen(pbBuf);
            iInitX = (16 - lens) / 2 + 1;
            //CTOS_LCDTPrintXY(iInitX, line, pbBuf);
			CTOS_LCDTPrintAligned(line, pbBuf, d_LCD_ALIGNCENTER); 
            break;
        case DISPLAY_POSITION_RIGHT:
            lens = strlen(pbBuf);
            iInitX = 16 - lens + 1;
            //CTOS_LCDTPrintXY(iInitX, line, pbBuf);            
			CTOS_LCDTPrintAligned(line, pbBuf, d_LCD_ALIGNRIGHT); 
            break;
    }

    //Set the reverse attribute of the character //
    CTOS_LCDTSetReverse(FALSE); //the reverse enable //     
}

void setLCDPrint(int line,int position, char *pbBuf)
{
    int iInitX = 0;
    int lens = 0;
    switch(position)
    {
        case DISPLAY_POSITION_LEFT:
            //CTOS_LCDTPrintXY(1, line, pbBuf);
			CTOS_LCDTPrintAligned(line, pbBuf, d_LCD_ALIGNLEFT);             
            break;
        case DISPLAY_POSITION_CENTER:
            lens = strlen(pbBuf);
            iInitX = (16 - lens) / 2 + 1;
            //CTOS_LCDTPrintXY(iInitX, line, pbBuf);
			CTOS_LCDTPrintAligned(line, pbBuf, d_LCD_ALIGNLEFT);             
            break;
        case DISPLAY_POSITION_RIGHT:
            lens = strlen(pbBuf);
            iInitX = 16 - lens + 1;
            //CTOS_LCDTPrintXY(iInitX, line, pbBuf);
			CTOS_LCDTPrintAligned(line, pbBuf, d_LCD_ALIGNRIGHT); 
            break;
    }
}

void showAmount(IN  BYTE bY, BYTE bStrLen, BYTE *baBuf)
{
    int i;
    
    if(bStrLen > 2)
    {
        CTOS_LCDTPrintXY(13, bY, "0.00");
        for(i = 0;i < bStrLen; i++)
        {
            if ((16 - bStrLen + 1 + i) > 14)
                CTOS_LCDTPutchXY(16 - bStrLen + 1 + i, bY, baBuf[i]);
            else
                CTOS_LCDTPutchXY(16 - bStrLen + i, bY, baBuf[i]);
        }
    }
    else
    {
        CTOS_LCDTPrintXY(13, bY, "0.00");
        for(i = 0;i < bStrLen; i++)
        {
            CTOS_LCDTPutchXY(16 - bStrLen + 1 + i, bY, baBuf[i]);
        }
    }
}

void vduiDisplayInvalidTLE(void)
{
    
    vduiClearBelow(2);
    vduiWarningSound();
    vduiDisplayStringCenter(3,"INVALID SESSION");
    vduiDisplayStringCenter(4,"KEY, PLEASE");
    vduiDisplayStringCenter(5,"DWD SESSION KEY");
    vduiDisplayStringCenter(6,"--INSTRUCTION---");
//    CTOS_LCDTPrintXY(1,7,"PRESS [F2] THEN");
//    CTOS_LCDTPrintXY(1,8,"PRESS [3]");
	CTOS_LCDTPrintAligned(7,"PRESS [F2] THEN", d_LCD_ALIGNLEFT);			 
	CTOS_LCDTPrintAligned(8,"PRESS [3]", d_LCD_ALIGNLEFT);			 
    CTOS_Delay(2500);
}

void szGetTransTitle(BYTE byTransType, BYTE *szTitle)
{    
    int i;
    szTitle[0] = 0x00;
    
    //vdDebug_LogPrintf("**szGetTransTitle START byTransType[%d]Orig[%d]**", byTransType, srTransRec.byOrgTransType);
    switch(byTransType)
    {
        case SALE:
			if (1 == inGetCardDrivenFlow())
				strcpy(szTitle, "PURCHASE");
			else
            strcpy(szTitle, "SALE");
            break;
        case PRE_AUTH:
			//0826
            //strcpy(szTitle, "PRE AUTH");
            //strcpy(szTitle, "CARD VERIFY");
			//0826
			strcpy(szTitle, "AUTHORIZE");//NETS need this display and receipt
            break;
        case PRE_COMP:
            //strcpy(szTitle, "AUTH COMP");
            strcpy(szTitle, "SALE COMPLETE"); //NETS need this display and receipt
            break;
        case REFUND:
            strcpy(szTitle, "REFUND");
            break;
        case VOID:
		case TRANSINQUIRY:
			vdDebug_LogPrintf("get title 2 srTransRec.byTransType[%d], srTransRec.byPackType[%d]", srTransRec.byTransType, srTransRec.byPackType);
            if(REFUND == srTransRec.byOrgTransType)
                strcpy(szTitle, "VOID REFUND");
            else if(SALE_OFFLINE == srTransRec.byOrgTransType)
            {
				vdDebug_LogPrintf("get title 3 srTransRec.byTransType[%d], srTransRec.byPackType[%d]", srTransRec.byTransType, srTransRec.byPackType);
            	strcpy(szTitle, "VOID OFFLINE SALE");
            }
			else if(PRE_AUTH == srTransRec.byOrgTransType)
                strcpy(szTitle, "SALE COMPLETE");
			else
                strcpy(szTitle, "VOID");
            break;
        case SALE_TIP:
            strcpy(szTitle, "SALE ADJUST");// NETS ask change the receipt with SALE ADJUST
            break;
        case SALE_OFFLINE:
            strcpy(szTitle, "OFFLINE");
            break;
        case SALE_ADJUST: 
            strcpy(szTitle, "ADJUST");
            break;
			
		case EPP_SALE:
			//strcpy(szTitle, "EPP SALE");
			strcpy(szTitle, "IPP SALE");//NETS want show IPP instead of EPP
			break;
		case EPP_VOID:
			//strcpy(szTitle, "EPP SALE");
			strcpy(szTitle, "IPP VOID");//NETS want show IPP instead of EPP
			break;
		case EPP_LIST:
			//strcpy(szTitle, "EPP LIST");
			strcpy(szTitle, "IPP LIST");//NETS want show IPP instead of EPP
			break;
        case SETTLE:
            //strcpy(szTitle, "SETTLE");
			if (1 == fGetNPXReqSummary())
				strcpy(szTitle, "SUMMARY REPORT");
			else
				strcpy(szTitle, "SETTLE");
            break;
        case SIGN_ON:
            strcpy(szTitle, "SIGN ON");
            break;
        case BATCH_REVIEW:
            strcpy(szTitle, "BATCH REVIEW");
            break;
        case BATCH_TOTAL:
            strcpy(szTitle, "BATCH TOTAL");
            break;
		case TOTAL:
            strcpy(szTitle, "TOTAL");
            break;
        case REPRINT_ANY:
            strcpy(szTitle, "REPRINT RECEIPT");
            break;
		case EFTSEC_TWK:
			strcpy(szTitle, "EFTSEC TWK");
			break;
		case EFTSEC_TMK:
			strcpy(szTitle, "EFTSEC TMK");
			break;
		case EFTSEC_TWK_RSA:
			strcpy(szTitle, "EFTSEC TWK RSA");
			break;
		case EFTSEC_TMK_RSA:
			strcpy(szTitle, "EFTSEC TMK RSA");
			break;
		case LAST_SETTLE:
			strcpy(szTitle, "LAST SETTLE");
			break;
		case SETUP:
			strcpy(szTitle, "SETUP");
			break;
		case NW_TEST:
			strcpy(szTitle, "NW TESTING");
			break;
		default:
            strcpy(szTitle, "");
            break;
    }
    i = strlen(szTitle);
    szTitle[i]=0x00;
    return ;
}

void vdDispTransTitle(BYTE byTransType)
{
    BYTE szTitle[MAX_CHAR_PER_LINE*2+1];
    BYTE szTitleDisplay[MAX_CHAR_PER_LINE*2+1];
    int iInitX = 1;

	return ;//new ui will display txn tittle when start txn
	if (0 == fGetAppRunBySelf() && inMultiAP_CheckSubAPStatus() == d_OK)
        return ;

    memset(szTitle, 0x00, sizeof(szTitle));
    szGetTransTitle(byTransType, szTitle);
	iInitX = ((MAX_CHAR_PER_LINE/2 - CTOSS_strlen(szTitle)/2))+1;
    memset(szTitleDisplay, 0x00, sizeof(szTitleDisplay));
    memset(szTitleDisplay, 0x20, sizeof(szTitleDisplay));
    memcpy(&szTitleDisplay[iInitX-1], szTitle, strlen(szTitle));
    CTOS_LCDTSetReverse(TRUE);
    //CTOS_LCDTPrintXY(1, 1, szTitleDisplay);
    CTOS_LCDTPrintAligned(1, "                                          ", d_LCD_ALIGNCENTER);    
	CTOS_LCDTPrintAligned(1, szTitle,d_LCD_ALIGNCENTER);	
    CTOS_LCDTSetReverse(FALSE);
}

void vdDispTitleString(BYTE *szTitle)
{
    BYTE szTitleDisplay[MAX_CHAR_PER_LINE+1];
    int iInitX = 1;
       
    iInitX = (MAX_CHAR_PER_LINE - strlen(szTitle)*2) / 2;
    memset(szTitleDisplay, 0x00, sizeof(szTitleDisplay));
    memset(szTitleDisplay, 0x20, MAX_CHAR_PER_LINE);
    memcpy(&szTitleDisplay[iInitX], szTitle, strlen(szTitle));
    CTOS_LCDTSetReverse(TRUE);
    //CTOS_LCDTPrintXY(1, 1, szTitleDisplay);
    CTOS_LCDTPrintAligned(1, "                                          ", d_LCD_ALIGNCENTER);    
	CTOS_LCDTPrintAligned(1, szTitle,d_LCD_ALIGNCENTER);	    
    CTOS_LCDTSetReverse(FALSE);
}

void vdForceDispTransTitle(BYTE byTransType)
{
    BYTE szTitle[MAX_CHAR_PER_LINE*2+1];
    BYTE szTitleDisplay[MAX_CHAR_PER_LINE*2+1];
    int iInitX = 1;

    memset(szTitle, 0x00, sizeof(szTitle));
    szGetTransTitle(byTransType, szTitle);
	iInitX = ((MAX_CHAR_PER_LINE/2 - CTOSS_strlen(szTitle)/2))+1;
    memset(szTitleDisplay, 0x00, sizeof(szTitleDisplay));
    memset(szTitleDisplay, 0x20, sizeof(szTitleDisplay));
    memcpy(&szTitleDisplay[iInitX-1], szTitle, strlen(szTitle));
    CTOS_LCDTSetReverse(TRUE);
    //CTOS_LCDTPrintXY(1, 1, szTitleDisplay);
    CTOS_LCDTPrintAligned(1, "                                          ", d_LCD_ALIGNCENTER);    
	CTOS_LCDTPrintAligned(1, szTitle,d_LCD_ALIGNCENTER);	
    CTOS_LCDTSetReverse(FALSE);
}


USHORT clearLine(int line)
{
    CTOS_LCDTGotoXY(1,line);
    CTOS_LCDTClear2EOL();
}

void vdDisplayTxnFinishUI(void)
{
	char szbuf[50+1];

    BYTE    szTotalAmt[12+1];
    BYTE    szStr[45];

	memset(szbuf, 0x00, sizeof(szbuf));
	memset(szStr, 0x00, sizeof(szStr));
	memset(szTotalAmt, 0x00, sizeof(szTotalAmt));

	if(srTransRec.byTransType == VOID)
	{
	      ApprovedUIVoid(inCTOSS_GetTxnType());
	}
	else
	{
		if (srTransRec.byTransType != TRANSINQUIRY)
          ApprovedUI(inCTOSS_GetTxnType());
	}

	return ;
	
	/*EPP Enquire List no need display Approve.*/
	if (EPP_LIST == srTransRec.byTransType)
		return;

	vdPrintTickOnPaper("Before Disp Approve");
	
	wub_hex_2_str(srTransRec.szTotalAmount, szTotalAmt, AMT_BCD_SIZE);
	szTotalAmt[AMT_BCD_SIZE*2] = 0x00;
	vdTrimLeadZeroes(szTotalAmt);
	formatAmt(szStr, szTotalAmt, "$", "cnnn,nnN.NN", VS_FALSE);
	//vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szTotalAmt, szStr);
	
	vdDebug_LogPrintf("srTransRec.byTransType[%d]", srTransRec.byTransType);

	
	if (srTransRec.byTransType == SALE)
		sprintf(szbuf, "Amount paid: %s", szStr);
	else
		sprintf(szbuf, "Amount: %s", szStr);

	vdDebug_LogPrintf("strTCTEX.fHawkerMode[%d] strTCTEX.inCFCRetailMode[%d]", strTCTEX.fHawkerMode, strTCTEX.inCFCRetailMode);

	
	#ifdef HAWKER_CFC
	if (strTCTEX.fHawkerMode == 1 && (strTCTEX.inCFCRetailMode == 0 || strTCTEX.inCFCRetailMode == 1 || strTCTEX.inCFCRetailMode == 3))
		vdCFC_DisplayApproval(szbuf, "", "", "", DISPLAY_POSITION_CENTER);
	else
	{
		 if(inGetBaseTerminalCDF()==TRUE)
		 {

			vdPinPadDisplayBmpResultUI(TRUE, szbuf, "", "", "");
			setLCDPrint(3, DISPLAY_POSITION_CENTER, "Transaction");
		    setLCDPrint(4, DISPLAY_POSITION_CENTER, "Approved");		
			setLCDPrint(5, DISPLAY_POSITION_CENTER, srTransRec.szAuthCode);   
			//CTOS_Delay(1500);//delete delay improve the speed
		 }
		 else 
		 {
		      if (strTCTEX.inCFCRetailMode == 2)
		      {
			    vdPinPadDisplayBmpResult(TRUE, "", "", "", "");
		      }


			  CTOS_LCDTClearDisplay();
		      setLCDPrint(3, DISPLAY_POSITION_CENTER, "Transaction");
		      setLCDPrint(4, DISPLAY_POSITION_CENTER, "Approved");
		      setLCDPrint(5, DISPLAY_POSITION_CENTER, srTransRec.szAuthCode); 	
		 }
		
		
		
	}
	#else
	if (strTCTEX.inCFCRetailMode == 2)
	{
		vdPinPadDisplayBmpResult(TRUE, "", "", "", "");
	}
    CTOS_LCDTClearDisplay();
    setLCDPrint(3, DISPLAY_POSITION_CENTER, "Transaction");
    setLCDPrint(4, DISPLAY_POSITION_CENTER, "Approved");
    setLCDPrint(5, DISPLAY_POSITION_CENTER, srTransRec.szAuthCode); 
	#endif



}

void vdDispErrMsg(IN BYTE *szMsg)
{
    char szDisplayMsg[40];
    BYTE byKeyBuf;

	vdDebug_LogPrintf("=====NA vduiWarningSound=====");
	
		return;
		
    CTOS_LCDTClearDisplay();
    if(srTransRec.byTransType != 0)
        vdDispTransTitle(srTransRec.byTransType);

    memset(szDisplayMsg, 0x00, sizeof(szDisplayMsg));
    strcpy(szDisplayMsg, szMsg);
    vduiClearBelow(8);
    setLCDPrint(8, DISPLAY_POSITION_LEFT, szDisplayMsg);
    CTOS_TimeOutSet (TIMER_ID_2 , 2*100);
    CTOS_Sound(1000, 50);
    
    while (1)
    {        
        CTOS_KBDHit  (&byKeyBuf);
        if (byKeyBuf == d_KBD_CANCEL ||byKeyBuf == d_KBD_ENTER)
        {
            CTOS_KBDBufFlush ();
            return ;
        }
    }
}


int vdDispTransactionInfo(void)
{
    BYTE byKeyBuf;
    BYTE szTmp1[16+1];
    BYTE szTmp2[16+1];
	BYTE szTmp[130+1];
    
    CTOS_LCDTClearDisplay();
    //vduiClearBelow(2);
    vdDispTransTitle(srTransRec.byTransType);
    
    setLCDPrint(2, DISPLAY_POSITION_LEFT, "Card NO.");
    setLCDPrint(3, DISPLAY_POSITION_LEFT, srTransRec.szPAN);
    memset(szTmp1, 0x00, sizeof(szTmp1));
    memset(szTmp2, 0x00, sizeof(szTmp2));
	memset(szTmp, 0x00, sizeof(szTmp));
    wub_hex_2_str(srTransRec.szInvoiceNo, szTmp1, 3);
    sprintf(szTmp2,"Inv No:%s", szTmp1);
    setLCDPrint(4, DISPLAY_POSITION_LEFT, szTmp2);
    
    wub_hex_2_str(srTransRec.szTotalAmount, szTmp1, 6);
    setLCDPrint(5, DISPLAY_POSITION_LEFT, "Amount:");
	//format amount 10+2
	vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szTmp1, szTmp);
	sprintf(szTmp2,"%s%s", strCST.szCurSymbol,szTmp);
    //sprintf(szTmp2,"SGD%7lu.%02lu", (atol(szTmp1)/100), (atol(szTmp1)%100));
    setLCDPrint(6, DISPLAY_POSITION_RIGHT, szTmp2);  
    setLCDPrint(8, DISPLAY_POSITION_LEFT, "PRS ENTR TO CONF");
    CTOS_TimeOutSet (TIMER_ID_2 , 30*100);
    
    while (1)
    {
        if(CTOS_TimeOutCheck(TIMER_ID_2 )  == d_OK)
            return  READ_CARD_TIMEOUT;
        
        CTOS_KBDHit  (&byKeyBuf);
        if (byKeyBuf == d_KBD_CANCEL)
        {
            CTOS_KBDBufFlush ();
            return USER_ABORT;
        }
        else if (byKeyBuf == d_KBD_ENTER)
        {
            CTOS_KBDBufFlush ();
            return d_OK;
        }
    }
}

USHORT showBatchRecord(TRANS_DATA_TABLE *strTransData)
{
    char szStr[DISPLAY_LINE_SIZE + 1];
    char szTemp[DISPLAY_LINE_SIZE + 1];
    BYTE byKeyBuf;
    CTOS_LCDTClearDisplay();
    memset(szStr, ' ', DISPLAY_LINE_SIZE);
    sprintf(szStr, "%s", strTransData->szPAN);
    setLCDPrint(1, DISPLAY_POSITION_LEFT, "Card NO:");
    setLCDPrint(2, DISPLAY_POSITION_LEFT, szStr);
    
    memset(szStr, ' ', DISPLAY_LINE_SIZE);
    memset(szTemp, ' ', DISPLAY_LINE_SIZE);
    wub_hex_2_str(strTransData->szBaseAmount, szTemp, AMT_BCD_SIZE);
	//format amount 10+2
	vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szTemp, szStr);
	//sprintf(szStr,"%10.0f.%02.0f",(atof(szTemp)/100), (atof(szTemp)%100));
    //sprintf(szStr, "%lu.%lu", atol(szTemp)/100, atol(szTemp)%100);
    setLCDPrint(3, DISPLAY_POSITION_LEFT, "Amount:");
    setLCDPrint(4, DISPLAY_POSITION_LEFT, szStr);

    
    memset(szStr, ' ', DISPLAY_LINE_SIZE);
    sprintf(szStr, "%s", strTransData->szAuthCode);
    setLCDPrint(5, DISPLAY_POSITION_LEFT, "Auth Code:");
    setLCDPrint(6, DISPLAY_POSITION_LEFT,  szStr);


    memset(szStr, ' ', DISPLAY_LINE_SIZE);
    memset(szTemp, ' ', DISPLAY_LINE_SIZE);
    wub_hex_2_str(strTransData->szInvoiceNo, szTemp, INVOICE_BCD_SIZE);
    sprintf(szStr, "%s", szTemp);
    setLCDPrint(7, DISPLAY_POSITION_LEFT, "Invoice NO:");
    setLCDPrint(8, DISPLAY_POSITION_LEFT, szTemp);
     
    CTOS_TimeOutSet (TIMER_ID_2 , 30*100);   
    while (1)
    {
        if(CTOS_TimeOutCheck(TIMER_ID_2 )  == d_OK)
        {
            CTOS_LCDTClearDisplay();
            return  READ_CARD_TIMEOUT;
        }
        CTOS_KBDHit  (&byKeyBuf);
        if (byKeyBuf == d_KBD_CANCEL)
        {
            CTOS_KBDBufFlush ();
            CTOS_LCDTClearDisplay();
            return USER_ABORT;
        }
        else if (byKeyBuf == d_KBD_ENTER)
        {
            CTOS_KBDBufFlush ();
            CTOS_LCDTClearDisplay();
            return d_OK;
        }
    }
}

void vduiLightOn(void)
{
#if 0
	if (inCTOSS_CheckBatteryChargeStatus() != d_OK)
	{
		CTOS_BackLightSet (d_BKLIT_KBD, d_OFF);
		return;
	}
#endif	
    if (strTCT.fHandsetPresent)  
        //CTOS_BackLightSetEx(d_BKLIT_LCD,d_ON,80000);
		CTOS_BackLightSetEx(d_BKLIT_KBD,d_ON,0xffffffff);
    else
        CTOS_BackLightSet (d_BKLIT_LCD, d_ON);
}

void vduiKeyboardBackLight(BOOL fKeyBoardLight)
{
	if (inCTOSS_CheckBatteryChargeStatus() != d_OK)
	{
		CTOS_BackLightSet (d_BKLIT_KBD, d_OFF);
		return;
	}
	
    if (strTCT.fHandsetPresent) 
    {
        if(VS_TRUE == fKeyBoardLight)
        {
            
            CTOS_BackLightSetEx(d_BKLIT_KBD,d_ON,0xffffffff);
            CTOS_BackLightSetEx(d_BKLIT_LCD,d_ON,0xffffffff);
        }
        else
        {
            CTOS_BackLightSetEx(d_BKLIT_KBD,d_OFF,100);
            CTOS_BackLightSetEx(d_BKLIT_LCD,d_OFF,3000);
        }

    }
    else
    {
        if(VS_TRUE == fKeyBoardLight)
            CTOS_BackLightSetEx(d_BKLIT_KBD,d_ON,0xffffffff);
        else
            CTOS_BackLightSetEx(d_BKLIT_KBD,d_OFF,100);
    }
}

void vduiPowerOff(void)
{
    BYTE block[6] = {0xff,0xff,0xff,0xff,0xff,0xff};
    USHORT ya,yb,xa,xb;
    unsigned char c;
        
    vduiClearBelow(1);
    vduiDisplayStringCenter(3,"ARE YOU SURE");
    vduiDisplayStringCenter(4,"WANT TO POWER");
    vduiDisplayStringCenter(5,"OFF TERMINAL");
    vduiDisplayStringCenter(7,"NO[X]   YES[OK] ");
    c=WaitKey(60);
    
    if(c!=d_KBD_ENTER)
    {
            return;
    }    
    
    for(ya =1; ya<5; ya++)
    {
        CTOS_Delay(100);
        CTOS_LCDTGotoXY(1,ya);
        CTOS_LCDTClear2EOL();
    }
    for(yb=8; yb>4; yb--)
    {
        CTOS_Delay(100);
        CTOS_LCDTGotoXY(1,yb);
        CTOS_LCDTClear2EOL();
    }
    CTOS_LCDTPrintXY(1,4,"----------------");
    for(xa=1; xa<8; xa++)
    {
        CTOS_Delay(25);
        CTOS_LCDTPrintXY(xa,4," ");
    }
    for(xb=16; xb>7; xb--)
    {
        CTOS_Delay(25);
        CTOS_LCDTPrintXY(xb,4," ");
    }
            
    CTOS_LCDGShowPic(58, 6, block, 0, 6);
    CTOS_Delay(250);
    CTOS_LCDTGotoXY(7,4);
    CTOS_LCDTClear2EOL();
    CTOS_Delay(250);

    CTOS_PowerOff();
}

void vduiDisplayStringCenter(unsigned char  y,unsigned char *sBuf)
{
    //CTOS_LCDTPrintXY(1,y,"                ");
    //CTOS_LCDTPrintXY(1,y,"                          ");
    //CTOS_LCDTPrintXY((20-strlen(sBuf))/2+1,y,sBuf);
	CTOS_LCDTPrintAligned(y,"                          ", d_LCD_ALIGNLEFT);	  
	CTOS_LCDTPrintAligned(y,sBuf, d_LCD_ALIGNCENTER);     
}

void vduiClearBelow(int line)
{
    int i;
	int count = 0;
	if ((strTCT.byTerminalType%2) == 0)
		count = 16;
	else
		count = 8;
    for(i=line;i<=count;i++)
        clearLine(i);
        
}

void vduiWarningSound(void)
{
	vdDebug_LogPrintf("=====NA vduiWarningSound=====");
	
		return;
		

    CTOS_LEDSet(d_LED1, d_ON);
    CTOS_LEDSet(d_LED2, d_ON);
    CTOS_LEDSet(d_LED3, d_ON);
    
    CTOS_Beep();
    CTOS_Delay(300);
    CTOS_Beep();
    
    CTOS_LEDSet(d_LED1, d_OFF);
    CTOS_LEDSet(d_LED2, d_OFF);
    CTOS_LEDSet(d_LED3, d_OFF);
}

void vdDisplayErrorMsg(int inColumn, int inRow,  char *msg)
{
	int inRowtmp;

    vdDebug_LogPrintf("=====NA vdDisplayErrorMsg=====");

    return;

	DisplayErrorMsg(msg);
    CTOS_Beep();
    CTOS_Delay(1500);
	return;
	#ifdef HAWKER_CFC
	if ((strTCTEX.fHawkerMode == 1) &&
		((srTransRec.byTransType == SALE && 1 == inGetCardDrivenFlow()) || (0 == fGetAppRunBySelf() && srTransRec.byTransType == SETTLE)))
		vdDisplayErrorMsgCFC(4, msg, "", "", "", DISPLAY_POSITION_CENTER);
	else
	#endif
	{
	if ((strTCT.byTerminalType%2) == 0)
		inRowtmp = V3_ERROR_LINE_ROW;
	else
        inRowtmp = inRow;
			
    CTOS_LCDTPrintXY(inColumn, inRowtmp, "                                        ");
    CTOS_LCDTPrintXY(inColumn, inRowtmp, msg);
    CTOS_Beep();
    CTOS_Delay(1500);
	}
}


void vdDisplayErrorMsgNoTimeout(int inColumn, int inRow,  char *msg)
{
	int inRowtmp;

	vdDebug_LogPrintf("=====NA vdDisplayErrorMsg=====");

    return;
	
	if ((strTCT.byTerminalType%2) == 0)
		inRowtmp = V3_ERROR_LINE_ROW;
	else
        inRowtmp = inRow;
			
    CTOS_LCDTPrintXY(inColumn, inRowtmp, "                                        ");
    CTOS_LCDTPrintXY(inColumn, inRowtmp, msg);
    CTOS_Beep();
}

void vdCTOS_DispStatusMessage(char* szMsg)
{
	vdDebug_LogPrintf("=====NA vdCTOS_DispStatusMessage=====");

    return;
	
	if ((strTCT.byTerminalType%2) == 0)
	{
			CTOS_LCDTPrintXY(1, 14, "                                        ");
			CTOS_LCDTPrintXY(1, 14, szMsg);
	}
	else
	{
		CTOS_LCDTPrintXY(1, 8, "                                        ");
		CTOS_LCDTPrintXY(1, 8, szMsg);
	}
	CTOS_Delay(500);
}

void vdCTOS_DispStatusMessageEx(char* szMsg, ULONG ulDelay)
{
	vdDebug_LogPrintf("=====NA vdCTOS_DispStatusMessageEx=====");

    return;
	
	if ((strTCT.byTerminalType%2) == 0)
	{
			CTOS_LCDTPrintXY(1, 14, "                                        ");
			CTOS_LCDTPrintXY(1, 14, szMsg);
	}
	else
	{
		CTOS_LCDTPrintXY(1, 8, "                                        ");
		CTOS_LCDTPrintXY(1, 8, szMsg);
	}
	if (ulDelay > 0)
		CTOS_Delay(ulDelay);
}



/* functions for loyalty - Meena 15/01/2012 - start*/
short vduiAskConfirmContinue(void)
{
    unsigned char key;
  
    vduiClearBelow(2);
    vduiDisplayStringCenter(3,"ARE YOU SURE");
    vduiDisplayStringCenter(4,"YOU WANT TO");
    vduiDisplayStringCenter(5,"CONTINUE?");
    vduiDisplayStringCenter(7,"NO[X]   YES[OK] ");
        
    while(1)
    {
        key = struiGetchWithTimeOut();
        if (key==d_KBD_ENTER)
            return d_OK;
        else if (key==d_KBD_CANCEL)
            return -1;
        else
            vduiWarningSound();
    }
    
}



BYTE struiGetchWithTimeOut(void)
{
    unsigned char c;
    BOOL isKey;
    CTOS_TimeOutSet(TIMER_ID_3,3000);
    
    while(1)//loop for time out
    {
        CTOS_KBDInKey(&isKey);
        if (isKey){ //If isKey is TRUE, represent key be pressed //
            vduiLightOn();
            //Get a key from keyboard //
            CTOS_KBDGet(&c);
            return c;   
        }
        else if (CTOS_TimeOutCheck(TIMER_ID_3) == d_YES)
        {      
            return d_KBD_CANCEL;
        }
    }
}

/* functions for loyalty - Meena 15/01/2012 - End*/

short inuiAskSettlement(void)
{
    unsigned char key;
    while(1) 
    {
        vduiClearBelow(2);
        vduiDisplayStringCenter(2,"DAILY SETTLEMENT");
        vduiDisplayStringCenter(3,"NOTIFICATION");

        vduiDisplayStringCenter(5,"PERFORM");
        vduiDisplayStringCenter(6,"SETTLEMENT?");
        vduiDisplayStringCenter(8,"NO[X] YES[OK]");

	key = usCTOSS_Confirm(" ");	
	vdDebug_LogPrintf("usCTOSS_Confirm[%d]", key);
	if(d_OK == key)
		return d_OK;
	else if(key==d_NO)
		return d_KBD_CANCEL;
//        CTOS_KBDGet(&key);
//        if(key==d_KBD_ENTER)
//            return d_OK;
        else if(key==d_KBD_CANCEL)
            return d_KBD_CANCEL;
        else if(key==d_KBD_F1)
            vduiPowerOff();
    }
        
}

void vduiDisplaySignalStrengthBatteryCapacity(void)
{
    
    BYTE bCapacity, msg2[50];
    USHORT dwRet;
    short insign;
    
    
    if(GPRSCONNETSTATUS== d_OK && strTCT.inMainLine == GPRS_MODE)
    {
        insign=incommSignal();
        if(insign==-1)
        {
            CTOS_LCDTPrintXY (9,1, "SIGNAL:NA");
        }
        else
        {           
            if(insign/6 == 0)
                CTOS_LCDTPrintXY (9,1, "NO SIGNAL");
            else if(insign/6 == 1)
            {                               
               CTOS_LCDTPrintXY (9,1, "S:l____"); 
            }
            else if(insign/6 == 2)
            {                               
               CTOS_LCDTPrintXY (9,1, "S:ll___"); 
            }
            else if(insign/6 == 3)
            {                               
               CTOS_LCDTPrintXY (9,1, "S:lll__"); 
            }
            else if(insign/6 == 4)
            {                               
               CTOS_LCDTPrintXY (9,1, "S:llll_"); 
            }
            else if(insign/6 == 5)
            {                               
               CTOS_LCDTPrintXY (9,1, "S:lllll"); 
            }
            
        }
    }
    
    dwRet= CTOS_BatteryGetCapacityByIC(&bCapacity);  
    if(dwRet==d_OK)
    {
        sprintf(msg2, "B:%d%% ", bCapacity);
        CTOS_LCDTPrintXY (3,1, msg2);
    }
                
}

void vdSetErrorMessage(char *szMessage)
{
    int inErrLen=0;

	//vdDisplayErrorMsg(1, 8, szMessage);


	
    inErrLen = strlen(szMessage);
    memset(szErrorMessage,0x00,sizeof(szErrorMessage));

    if (inErrLen > 0)
    {
        if (inErrLen > ERRORLEN)
            inErrLen = ERRORLEN;
        
        memcpy(szErrorMessage,szMessage,inErrLen);

		/*a simple way to set ECR error code*/
		vdECRSetRespCodeByErrMsg(szErrorMessage);

		//fix display error problem
		//vdDisplayErrorMsg(1, 8, szMessage);
    }
}

int inGetErrorMessage(char *szMessage)
{
    int inErrLen=0;

    inErrLen = strlen(szErrorMessage);

    if (inErrLen > 0)
    {       
        memcpy(szMessage,szErrorMessage,inErrLen);
    }
    
    return inErrLen;
}

//gcitra
void setLCDPrint27(int line,int position, char *pbBuf)
{
    int iInitX = 0;
    int lens = 0;
    switch(position)
    {
        case DISPLAY_POSITION_LEFT:
            CTOS_LCDTPrintXY(1, line, pbBuf);
            break;
        case DISPLAY_POSITION_CENTER:
            lens = strlen(pbBuf);
            iInitX = (27 - lens) / 2 + 1;
            CTOS_LCDTPrintXY(iInitX, line, pbBuf);
            break;
        case DISPLAY_POSITION_RIGHT:
            lens = strlen(pbBuf);
            iInitX = 27 - lens + 1;
            CTOS_LCDTPrintXY(iInitX, line, pbBuf);
            break;
    }
}
//gcitra
//sidumili: display message
void vdDisplayMessage(char *szLine1Msg, char *szLine2Msg, char *szLine3Msg)
{
    CTOS_LCDTClearDisplay();
		//vduiClearBelow(2);
		vduiDisplayStringCenter(4, szLine1Msg);
		vduiDisplayStringCenter(5, szLine2Msg);
		vduiDisplayStringCenter(6, szLine3Msg);
		WaitKey(1);
}
//sidumili

//sidumili: confirmation
short vduiAskEnterToConfirm(void)
{
    unsigned char key;
  
    
    CTOS_LCDTPrintXY(1,8,"CONFIRM?NO[X]YES[OK]");
        
    while(1)
    {
    	key = usCTOSS_Confirm(" ");	
	vdDebug_LogPrintf("usCTOSS_Confirm[%d]", key);
	if(d_OK == key)
		return d_OK;
	
//        key = struiGetchWithTimeOut();
//        if (key==d_KBD_ENTER)
//            return d_OK;
	else if(key==d_NO)
	{
						
							//sidumili: disconnect communication when USER PRESS CANCEL KEY
							if (strCPT.inCommunicationMode == DIAL_UP_MODE){
											inCTOS_inDisconnect();
							}
							//sidumili: disconnect communication when USER PRESS CANCEL KEY
						
				return -1;
	}
        else if (key==d_KBD_CANCEL){
					
						//sidumili: disconnect communication when USER PRESS CANCEL KEY
						if (strCPT.inCommunicationMode == DIAL_UP_MODE){
										inCTOS_inDisconnect();
						}
						//sidumili: disconnect communication when USER PRESS CANCEL KEY
					
            return -1;
        	}
        else
            vduiWarningSound();
    }
    
}
//sidumili:

void vdSpit2Line(char *szMsg,char *szMsg1,char *szMsg2)
{
	int inLen = 0;
	char * pch;
	int inPrevPos = 0, inSpacePos = 0;
	char szTempBuf[99 + 1];
	int loop = 0;
	
	memset(szTempBuf, 0x00, sizeof(szTempBuf));
	strcpy(szTempBuf, szMsg);
	inLen = strlen(szTempBuf);
	vdDebug_LogPrintf("host err msg inLen[%d]", inLen);
    if (inLen >= 21)//workaround for long message
	{
		pch=strchr(szTempBuf,' ');
		while (pch!=NULL)
		{
			inSpacePos = pch-szTempBuf;
			if (inSpacePos >= 21)
			{
				inSpacePos = inPrevPos;
				break;
			}
			pch=strchr(pch+1,' ');
			inPrevPos = inSpacePos;
		}
		memcpy(szMsg1, szTempBuf, inSpacePos);
		strcpy(szMsg2, &szTempBuf[inSpacePos+1]);
	}
}

void vdSpitMultiline(char *szErrMsg,char *szErrMsg1,char *szErrMsg2,char *szErrMsg3)
{
	int inLen = 0;
	char * pch;
	int inPrevPos = 0, inSpacePos = 0;
	char szTempBuf[99 + 1];
	char szTempBuf1[99 + 1];
	int loop = 0;
				
	memset(szTempBuf, 0x00, sizeof(szTempBuf));
	memset(szTempBuf1, 0x00, sizeof(szTempBuf1));
	strcpy(szTempBuf, szErrMsg);
	inLen = strlen(szTempBuf);
	vdDebug_LogPrintf("vdSpitMultiline inLen=[%d],szErrMsg=[%s]",inLen,szErrMsg);
    if (inLen >= 21)
    {
    	vdSpit2Line(szTempBuf,szErrMsg1,szTempBuf1);
		
		memset(szTempBuf, 0x00, sizeof(szTempBuf));
		strcpy(szTempBuf, szTempBuf1);
		inLen = strlen(szTempBuf);
		if (inLen >= 21)
	    {
	    	memset(szTempBuf1, 0x00, sizeof(szTempBuf1));
	    	vdSpit2Line(szTempBuf,szErrMsg2,szTempBuf1);
			
			memset(szTempBuf, 0x00, sizeof(szTempBuf));
			strcpy(szTempBuf, szTempBuf1);
			inLen = strlen(szTempBuf);
			if (inLen >= 21)
		    {
		    	memset(szTempBuf1, 0x00, sizeof(szTempBuf1));
		    	vdSpit2Line(szTempBuf,szErrMsg3,szTempBuf1); 
		    }
			else
				strcpy(szErrMsg3, szTempBuf);
	    }
		else
			strcpy(szErrMsg2, szTempBuf);
    }
	else
		strcpy(szErrMsg1, szTempBuf);

	vdDebug_LogPrintf("szErrMsg[%s],szErrMsg1[%s],szErrMsg2[%s],szErrMsg3[%s]", szErrMsg, szErrMsg1, szErrMsg2, szErrMsg3);
}

void vdDisplayErrorMsgCFC(int inRow, char *szMsgHeader1, char *szMsgHeader2, char *szMsgFooter1, char *SzMsgFooter2, int inAlignType)
{
	int inCTOSAlign = 0;

	vdDebug_LogPrintf("=====NA vdDisplayErrorMsgCFC=====");
		
			return;

	if(srTransRec.byTransType == TC_UPLOAD || srTransRec.byPackType == TC_UPLOAD)
		return;
	
    if (inAlignType == DISPLAY_POSITION_RIGHT)
		inCTOSAlign = d_LCD_ALIGNRIGHT;
	if (inAlignType == DISPLAY_POSITION_CENTER)
		inCTOSAlign = d_LCD_ALIGNCENTER;
	if (inAlignType == DISPLAY_POSITION_LEFT)
		inCTOSAlign = d_LCD_ALIGNLEFT;

	if (strTCTEX.inCFCRetailMode == 0)
	{
		CTOS_LCDBackGndColor(RGB(252, 36, 84));
		CTOS_LCDForeGndColor(RGB(255, 255, 255));	
		displaybmpEx(0, 0, "CFCERROR.BMP");
		CTOS_LCDTSelectFontSize(d_FONT_20x40);
		CTOS_LCDTPrintAligned(2, "Error", inCTOSAlign);
		CTOS_LCDTSelectFontSize(d_FONT_16x30);
		CTOS_LCDTPrintAligned(4, szMsgHeader1, inCTOSAlign);
		if (strlen(szMsgHeader2) > 0)
			CTOS_LCDTPrintAligned(5, szMsgHeader2, inCTOSAlign);
		if (strlen(szMsgFooter1) > 0)
			CTOS_LCDTPrintAligned(14, szMsgFooter1, inCTOSAlign);
		if (strlen(SzMsgFooter2) > 0)
			CTOS_LCDTPrintAligned(15, SzMsgFooter2, inCTOSAlign);
	}
	else
	{
		CTOS_LCDBackGndColor(RGB(240, 242, 239));
		CTOS_LCDForeGndColor(RGB(0, 0, 0));
		displaybmpEx(0, 0, "CFCERRORRETL.BMP");
		CTOS_LCDTSelectFontSize(d_FONT_12x24);
		CTOS_LCDTPrintAligned(15, szMsgHeader1, inCTOSAlign);
		if (strlen(szMsgHeader2) > 0)
			CTOS_LCDTPrintAligned(5, szMsgHeader2, inCTOSAlign);
		if (strlen(szMsgFooter1) > 0)
			CTOS_LCDTPrintAligned(14, szMsgFooter1, inCTOSAlign);
		if (strlen(SzMsgFooter2) > 0)
			CTOS_LCDTPrintAligned(15, SzMsgFooter2, inCTOSAlign);
		CTOS_LCDTSelectFontSize(d_FONT_16x30);
	}
	CTOS_LCDBackGndColor(RGB(255, 255, 255));
	CTOS_LCDForeGndColor(RGB(0, 0, 0));
	
    CTOS_Beep();
    CTOS_Delay(1500);
}

void vdDisplayProcessing(void)
{
	BYTE temp[64];
	
	CTOS_LCDBackGndColor(RGB(12, 84, 212));
	CTOS_LCDForeGndColor(RGB(255, 255, 255));	
	displaybmpEx(0, 0, "CFCPROCESSING.BMP");
	memset(temp, 0x00, sizeof(temp));
	//if (srTransRec.byTransType == SALE)
		strcpy(temp, "Secure payment");
	//else
	//	vdGetTransTitle(srTransRec.byTransType, temp);
	CTOS_LCDTPrintAligned(3, temp, d_LCD_ALIGNCENTER);
	//CTOS_LCDTSelectFontSize(d_FONT_16x30);
	CTOS_LCDTPrintAligned(4, "in progress", d_LCD_ALIGNCENTER);
	//CTOS_LCDBackGndColor(RGB(255, 255, 255));
	//CTOS_LCDForeGndColor(RGB(0, 0, 0));
}

void vdCFC_DisplayApproval(char *szMsgHeader1, char *szMsgHeader2, char *szMsgFooter1, char *SzMsgFooter2, int inAlignType)
{
	int inRet = d_OK;
	BYTE szStr[32];
	BYTE szAMTStr[12+1];
	BYTE szTemp[50];
	int inCTOSAlign = 0;

	
    if (inAlignType == DISPLAY_POSITION_RIGHT)
		inCTOSAlign = d_LCD_ALIGNRIGHT;
	if (inAlignType == DISPLAY_POSITION_CENTER)
		inCTOSAlign = d_LCD_ALIGNCENTER;
	if (inAlignType == DISPLAY_POSITION_LEFT)
		inCTOSAlign = d_LCD_ALIGNLEFT;

	/*memset(szStr, 0x00, sizeof(szStr));
	memset(szAMTStr, 0x00, sizeof(szAMTStr));
	wub_hex_2_str(srTransRec.szTotalAmount, szAMTStr, AMT_BCD_SIZE);
	szAMTStr[AMT_BCD_SIZE*2] = 0x00;
	vdTrimLeadZeroes(szAMTStr);
	formatAmt(szStr, szAMTStr, "$", "cnnn,nnN.NN", VS_FALSE);*/

	if (strTCTEX.inCFCRetailMode == 0)
	{
		CTOS_LCDBackGndColor(RGB(22, 236, 145));
		CTOS_LCDForeGndColor(RGB(255, 255, 255));	
		displaybmpEx(0, 0, "CFCAPPROVED.BMP");
		CTOS_LCDTSelectFontSize(d_FONT_20x40);
		CTOS_LCDTPrintAligned(2, "Approved", inCTOSAlign);
		CTOS_LCDTSelectFontSize(d_FONT_16x30);
		memset(szStr, 0x00, sizeof(szStr));
		CTOS_LCDTPrintAligned(4, szMsgHeader1, inCTOSAlign);
		if (strlen(szMsgHeader2) > 0)
			CTOS_LCDTPrintAligned(5, szMsgHeader2, inCTOSAlign);
		if (strlen(szMsgFooter1) > 0)
			CTOS_LCDTPrintAligned(14, szMsgFooter1, inCTOSAlign);
		if (strlen(SzMsgFooter2) > 0)
			CTOS_LCDTPrintAligned(15, SzMsgFooter2, inCTOSAlign);
	}
	else
	{
		CTOS_LCDBackGndColor(RGB(240, 242, 239));
		CTOS_LCDForeGndColor(RGB(0, 0, 0));
		displaybmpEx(0, 0, "CFCAPPROVEDRETL.BMP");
		CTOS_LCDTSelectFontSize(d_FONT_12x24);
		CTOS_LCDTPrintAligned(15, szMsgHeader1, inCTOSAlign);
		if (strlen(szMsgHeader2) > 0)
			CTOS_LCDTPrintAligned(16, szMsgHeader2, inCTOSAlign);
		if (strlen(szMsgFooter1) > 0)
			CTOS_LCDTPrintAligned(17, szMsgFooter1, inCTOSAlign);
		if (strlen(SzMsgFooter2) > 0)
			CTOS_LCDTPrintAligned(18, SzMsgFooter2, inCTOSAlign);
		CTOS_LCDTSelectFontSize(d_FONT_16x30);
	}
	CTOS_LCDBackGndColor(RGB(255, 255, 255));
	CTOS_LCDForeGndColor(RGB(0, 0, 0));
}

void vdDisplayRemoveCard(void)
{
	BYTE temp[64];
	
	CTOS_LCDTSelectFontSize(d_FONT_16x30);
	CTOS_LCDBackGndColor(RGB(12, 84, 212));
	CTOS_LCDForeGndColor(RGB(255, 255, 255));	
	displaybmpEx(0, 0, "CFCREMOVECARD.BMP");
	CTOS_LCDTPrintAligned(3, "Please", d_LCD_ALIGNCENTER);
	CTOS_LCDTPrintAligned(4, "remove card", d_LCD_ALIGNCENTER);
	CTOS_Sound(1000, 50);
	CTOS_Delay(1000);
	//CTOS_LCDBackGndColor(RGB(255, 255, 255));
	//CTOS_LCDForeGndColor(RGB(0, 0, 0));
}

int vdDisplayLastTxn(void)
{
	int i = 0, line = 3;
	char szBaseAmtStr[AMT_ASC_SIZE + 1];
	char szTotAmtStr[AMT_ASC_SIZE + 1];
    char szTemp[d_LINE_SIZE + 1];
    char szTemp1[d_LINE_SIZE + 1];
	char monthname[12][4] = {"JAN", "FEB", "MAR", "APR", "MAY", "JUN", "JUL", "AUG", "SEP", "OCT", "NOV", "DEC"};
    char szStr[d_LINE_SIZE + 1];

	char szFormattedAmt[AMT_ASC_SIZE*2 + 1];
  	CTOS_RTC SetRTC;
	char szYear[3];

	CTOS_LCDTClearDisplay();
    vdDispTitleString("LAST TXN");
	
	CTOS_RTCGet(&SetRTC);
	sprintf(szYear ,"%02d",SetRTC.bYear);
	memcpy(srTransRec.szYear,szYear,2);
	wub_hex_2_str(srTransRec.szDate, szTemp,DATE_BCD_SIZE);
	wub_hex_2_str(srTransRec.szTime, szTemp1,TIME_BCD_SIZE);
	//vdDebug_LogPrintf("date[%s],time[%s]atol(szTemp)=[%d](atol(szTemp1)=[%d]",szTemp,szTemp1,atol(szTemp),atol(szTemp1) );
    sprintf(szStr,"%02lu %s 20%02lu     %02lu:%02lu:%02lu",atol(szTemp)%100, (char*)&monthname[(atol(szTemp)/100)-1],atol(srTransRec.szYear),atol(szTemp1)/10000,atol(szTemp1)%10000/100, atol(szTemp1)%100);
	CTOS_LCDTPrintXY(1, line, szStr);
	line++;

	line++;
	memset(szStr, 0x00, sizeof(szStr));
	szGetTransTitle(srTransRec.byTransType, szStr);
	CTOS_LCDTPrintXY(1, line, szStr);
	line++;

	memset(szTemp, 0x00, sizeof(szTemp));
	memset(szTemp1, 0x00, sizeof(szTemp1));
    //wub_hex_2_str(srTransRec.szInvoiceNo, szTemp1, INVOICE_BCD_SIZE);
	//sprintf(szTemp, "INV#: %s", szTemp1);
	sprintf(szTemp, "TRC NUM: %06lu", srTransRec.ulTraceNum);
	CTOS_LCDTPrintXY(1, line, szTemp);
	line++;
	
	memset(szTotAmtStr, 0x00, sizeof(szTotAmtStr));
	memset(szBaseAmtStr, 0x00, sizeof(szBaseAmtStr));
	memcpy(szTotAmtStr, srTransRec.szTotalAmount, AMT_ASC_SIZE);
	wub_hex_2_str(szTotAmtStr, szBaseAmtStr, AMT_BCD_SIZE);
	memcpy(szTotAmtStr, szBaseAmtStr, AMT_ASC_SIZE);//wub_hex_2_str(srTransRec.szTotalAmount, szTotAmtStr, AMT_BCD_SIZE);
	memset(szFormattedAmt, 0x00, sizeof(szFormattedAmt));
	vdTrimLeadZeroes(szTotAmtStr);
	vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szTotAmtStr, szFormattedAmt);
	sprintf(szStr, "%s %s", strCST.szCurSymbol,szFormattedAmt);
	CTOS_LCDTPrintXY(1, line, szStr);
	line++;
	
	if (strcmp(srTransRec.szRespCode, "00") == 0)
	{
		if (srTransRec.stEMVinfo.T9F27 == 0x00 && srTransRec.byEntryMode == CARD_ENTRY_ICC)
			CTOS_LCDTPrintXY(1, line, "DECLINED");
		else
			CTOS_LCDTPrintXY(1, line, "APPROVED");
	}
	else
		CTOS_LCDTPrintXY(1, line, "DECLINED");
	line++;
	
	/*Display Touch Icon for PRINT Last Txn*/
	#ifdef HAWKER_CFC
	if (strTCTEX.fHawkerMode == 1 && (strTCTEX.inCFCRetailMode == 0 || strTCTEX.inCFCRetailMode == 1 || strTCTEX.inCFCRetailMode == 3))
		inNPX_CFC_SetPrintLastTxn();
	#endif

	return d_OK;
}


#define   CUPIC_RID_VALUE	  "A000000333"
int inCheckCUP(void)
{
  BYTE PreferAID[128];
  int inRet = d_NO;
  
  memset(PreferAID, 0x00, sizeof(PreferAID));
  wub_str_2_hex(CUPIC_RID_VALUE,PreferAID,10); 
  
  if (memcmp(srTransRec.stEMVinfo.T84, PreferAID, 5) == 0)
  {
	  vdDebug_LogPrintf("CUP ICC");
	    inRet = d_OK;   
  }


  if ((srTransRec.szPAN[0] == '6') || (srTransRec.szPAN[0] == '8' && srTransRec.szPAN[1] != '0')) // new bin range starting from 81... to 89...
  {	  
      vdDebug_LogPrintf("CUP SWIPE");
	    inRet = d_OK;	  
  }

  return inRet;  
}


#define PIN_POSITION_Y_CFC	10
void vdDisplayPINEntryScreen(void)
{
	BYTE baAmount[20];
	BYTE Tkey = 16;
	BYTE	szTotalAmt[12+1];
    BYTE    szStr[45];
	BYTE temp[64];
	
	vdDebug_LogPrintf("vdDisplayPINEntryScreen");

	
	//vdCTOSS_ClearTouchPanelTest();
	//CTOS_KBDBufFlush();
	vdCTOSS_KeepClearTouchPanel();
	memset(szTotalAmt, 0x00, sizeof(szTotalAmt));
	wub_hex_2_str(srTransRec.szTotalAmount, szTotalAmt, AMT_BCD_SIZE);
	vdTrimLeadZeroes(szTotalAmt);
	
	memset(szStr, 0x00, sizeof(szStr));
	memset(baAmount, 0x00, sizeof(baAmount));
	vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szTotalAmt, baAmount);
	//sprintf(szStr,"%s%s", strCST.szCurSymbol,baAmount);
	sprintf(szStr,"$%s", baAmount);
	
	CTOS_LCDBackGndColor(RGB(44, 108, 220));
	CTOS_LCDForeGndColor(RGB(255, 255, 255));			
	if (strTCTEX.inCFCRetailMode == 0)
		displaybmpEx(0, 0, "CFCBLUEGREYPATTERN.BMP");
	else
		displaybmpEx(0, 0, "CFCBLUEGREY.BMP");
	displaybmpEx(0, 390, "CFCBUTTONCONFIRM.BMP");
	memset(temp, 0x00, sizeof(temp));
	if (srTransRec.byTransType == SALE)
		strcpy(temp, "Purchase");
	else
		szGetTransTitle(srTransRec.byTransType, temp);
	strcat(temp, ":");
	//CTOS_LCDTSelectFontSize(d_FONT_9x18);
	/*CTOS_LCDTSelectFontSize(d_FONT_16x30);
	CTOS_LCDTPrintAligned(2, temp, d_LCD_ALIGNCENTER);
	CTOS_LCDTSelectFontSize(d_FONT_20x40);
	CTOS_LCDTPrintAligned(3, szStr, d_LCD_ALIGNCENTER); */
	if (strTCTEX.inCFCRetailMode == 0)
	{
		strcat(temp, ":");
		CTOS_LCDTSelectFontSize(d_FONT_9x18);
		CTOS_LCDTPrintAligned(4, temp, d_LCD_ALIGNCENTER);
		CTOS_LCDTSelectFontSize(d_FONT_20x40);
		CTOS_LCDTPrintAligned(3, szStr, d_LCD_ALIGNCENTER);	
	}
	else
	{
		BYTE temp2[64];
		memset(temp2, 0x00, sizeof(temp2));
		sprintf(temp2, " %s", temp);
		//CTOS_LCDTSelectFontSize(d_FONT_12x24);
		CTOS_LCDTSelectFontSize(d_FONT_16x30);
		CTOS_LCDTPrintAligned(1, temp2, d_LCD_ALIGNLEFT);
		CTOS_LCDTSelectFontSize(d_FONT_20x40);
		CTOS_LCDTPrintAligned(2, szStr, d_LCD_ALIGNRIGHT);	
	}
	CTOS_LCDBackGndColor(RGB(236, 236, 236));
	CTOS_LCDForeGndColor(RGB(0, 0, 0));
	CTOS_LCDTSelectFontSize(d_FONT_16x30);
	
	if(inCheckCUP()==d_OK)
	{
		CTOS_LCDTPrintAligned(7, "INPUT PIN", d_LCD_ALIGNCENTER);
		CTOS_LCDTPrintAligned(8, "or PRESS ENTER", d_LCD_ALIGNCENTER);
	}
	else
	{
	   CTOS_LCDTPrintAligned(7, "Please enter PIN", d_LCD_ALIGNCENTER);
	}
	CTOS_LCDTPrintAligned(PIN_POSITION_Y_CFC+1, "-", d_LCD_ALIGNCENTER);
	
	//vdCTOSS_KeepClearTouchPanel();//cause '*' not displayed except after pressing clear key
	
}

void vdDisplayPINEntryScreen_pinpad(void)
{
	BYTE baAmount[20];
	BYTE Tkey = 16;
	BYTE	szTotalAmt[12+1];
    BYTE    szStr[45];
	BYTE temp[64];
	
	vdDebug_LogPrintf("vdDisplayPINEntryScreen");

	
	//vdCTOSS_ClearTouchPanelTest();
	//CTOS_KBDBufFlush();
	vdCTOSS_KeepClearTouchPanel();
	memset(szTotalAmt, 0x00, sizeof(szTotalAmt));
	wub_hex_2_str(srTransRec.szTotalAmount, szTotalAmt, AMT_BCD_SIZE);
	vdTrimLeadZeroes(szTotalAmt);

	if (atoi(szTotalAmt) == 0 && strlen(stOLPinParam.szFmtAmtStr)>0)
	{
		memset(szStr, 0x00, sizeof(szStr));
		strcpy(szStr, stOLPinParam.szFmtAmtStr);
		str_replace(szStr, "SGD", "$");
		vdDebug_LogPrintf("szStr[%d]", szStr);
	}
	else
	{
		memset(szStr, 0x00, sizeof(szStr));
		memset(baAmount, 0x00, sizeof(baAmount));
		vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", szTotalAmt, baAmount);
		//sprintf(szStr,"%s%s", strCST.szCurSymbol,baAmount);
		sprintf(szStr,"$%s", baAmount);
	}
	
	CTOS_LCDBackGndColor(RGB(44, 108, 220));
	CTOS_LCDForeGndColor(RGB(255, 255, 255));			
	if (strTCTEX.inCFCRetailMode == 0)
		displaybmpEx(0, 0, "CFCBLUEGREYPATTERN.BMP");
	else
		displaybmpEx(0, 0, "CFCBLUEGREY.BMP");
	displaybmpEx(0, 390, "CFCBUTTONCONFIRM.BMP");
	memset(temp, 0x00, sizeof(temp));
	if (srTransRec.byTransType == SALE)
		strcpy(temp, "Purchase");
	else
		szGetTransTitle(srTransRec.byTransType, temp);
	/*strcat(temp, ":");
	//CTOS_LCDTSelectFontSize(d_FONT_9x18);
	CTOS_LCDTSelectFontSize(d_FONT_16x30);
	CTOS_LCDTPrintAligned(2, temp, d_LCD_ALIGNCENTER);
	CTOS_LCDTSelectFontSize(d_FONT_20x40);
	CTOS_LCDTPrintAligned(3, szStr, d_LCD_ALIGNCENTER);*/ 
	if (strTCTEX.inCFCRetailMode == 0)
	{
		strcat(temp, ":");
		CTOS_LCDTSelectFontSize(d_FONT_9x18);
		CTOS_LCDTPrintAligned(4, temp, d_LCD_ALIGNCENTER);
		CTOS_LCDTSelectFontSize(d_FONT_20x40);
		CTOS_LCDTPrintAligned(3, szStr, d_LCD_ALIGNCENTER);	
	}
	else
	{
		BYTE temp2[64];
		memset(temp2, 0x00, sizeof(temp2));
		sprintf(temp2, " %s", temp);
		//CTOS_LCDTSelectFontSize(d_FONT_12x24);
		CTOS_LCDTSelectFontSize(d_FONT_16x30);
		CTOS_LCDTPrintAligned(1, temp2, d_LCD_ALIGNLEFT);
		CTOS_LCDTSelectFontSize(d_FONT_20x40);
		CTOS_LCDTPrintAligned(2, szStr, d_LCD_ALIGNRIGHT);	
	}
	CTOS_LCDBackGndColor(RGB(236, 236, 236));
	CTOS_LCDForeGndColor(RGB(0, 0, 0));
	CTOS_LCDTSelectFontSize(d_FONT_16x30);
	
	if(1)//(inCheckCUP()==d_OK)
	{
		CTOS_LCDTPrintAligned(7, "INPUT PIN", d_LCD_ALIGNCENTER);
		CTOS_LCDTPrintAligned(8, "or PRESS ENTER", d_LCD_ALIGNCENTER);
	}
	else
	{
	   CTOS_LCDTPrintAligned(7, "Please enter PIN", d_LCD_ALIGNCENTER);
	}
	CTOS_LCDTPrintAligned(PIN_POSITION_Y_CFC+1, "-", d_LCD_ALIGNCENTER);
	
	//vdCTOSS_KeepClearTouchPanel();//cause '*' not displayed except after pressing clear key
	
}


void vduiDisplayStringCenterEXT(void)
{
	if(strTCT.byPinPadMode == 0)
	{
		if(strTCT.byPinPadType == 3)
		{
			char szV3PSendBuf[1024+1];
			int inOffSet = 0;
			int status;
			char szRecvBuf[1024+1];
			int inRecvlen = 0;
			unsigned char *pszPtr;
			
			vdCTOSS_SetV3PRS232Port(strTCT.byPinPadPort);
			inCTOSS_V3PRS232Open(strTCT.inPPBaudRate, 'N', 8, 1);
			CTOS_Delay(20); // NETS External PIN Pad short delay make it more smooth
			// send STX INJECT_KEY ETX LRC
			memset(szV3PSendBuf, 0x00, sizeof(szV3PSendBuf));
			inOffSet = 0;
			szV3PSendBuf[inOffSet] = STX;
			inOffSet += 1;
			memcpy(&szV3PSendBuf[inOffSet], "NC_DISPLAY_EXT", strlen("NC_DISPLAY_EXT"));
			inOffSet += strlen("NC_DISPLAY_EXT");
			//////////////////////////////////////////////////////////////////////
			szV3PSendBuf[inOffSet] = '|';
			inOffSet += 1;

			memcpy(&szV3PSendBuf[inOffSet], &strDisplayEXT, sizeof(STRUCT_DISPLAY_EXT));
			inOffSet += sizeof(STRUCT_DISPLAY_EXT);
			////////////////////////////////////////////////////////////////////
			szV3PSendBuf[inOffSet] = ETX;
			inOffSet += 1;			
		    szV3PSendBuf[inOffSet] = (char) wub_lrc(&(szV3PSendBuf[1]), inOffSet-1);
			inOffSet += 1;
			inCTOSS_V3PRS232SendBuf(szV3PSendBuf, inOffSet);
			CTOS_Delay(20); // NETS External PIN Pad short delay make it more smooth
			status = inCTOSS_V3PRS232RecvACK(szRecvBuf, &inRecvlen, (100));			
			/*close port after receive*/
			inCTOSS_V3PRS232Close();
			return ;
		}
	}	
}

//ucResultType TRUE: approved, FALSE: error
void vdPinPadDisplayBmpResultUI(unsigned char ucResultType, char* pszMessage1, char* pszMessage2, char* pszMessage3, char* pszMessage4)
{
	vdDebug_LogPrintf("vdPinPadDisplayBmpResult pszMessage1[%s],pszMessage2[%s],pszMessage3[%s],pszMessage4[%s]", pszMessage1, pszMessage2, pszMessage3, pszMessage4);

	memset(&strDisplayEXT, 0x00, sizeof(strDisplayEXT));
	if (ucResultType == TRUE)
		strDisplayEXT.ucSoundType = 0xFE;
	else
		strDisplayEXT.ucSoundType = 0xFD;
	strcpy(strDisplayEXT.szMessage1, pszMessage1);
	strcpy(strDisplayEXT.szMessage2, pszMessage2);
	strcpy(strDisplayEXT.szMessage3, pszMessage3);
	strcpy(strDisplayEXT.szMessage4, pszMessage4);
	vduiDisplayStringCenterEXT(); 

	CTOS_LCDTPrintXY(1, 2,"                                            ");	
	CTOS_LCDTPrintXY(1, 3,"                                            ");	
	CTOS_LCDTPrintXY(1, 4,"                                            ");	
	CTOS_LCDTPrintXY(1, 5,"                                            ");	
	CTOS_LCDTPrintXY(1, 6,"                                            ");	
	CTOS_LCDTPrintXY(1, 7,"                                            ");	
	CTOS_LCDTPrintXY(1, 8,"                                            ");	

}



//ucResultType TRUE: approved, FALSE: error
void vdPinPadDisplayBmpResult(unsigned char ucResultType, char* pszMessage1, char* pszMessage2, char* pszMessage3, char* pszMessage4)
{
	vdDebug_LogPrintf("vdPinPadDisplayBmpResult pszMessage1[%s],pszMessage2[%s],pszMessage3[%s],pszMessage4[%s]", pszMessage1, pszMessage2, pszMessage3, pszMessage4);

	memset(&strDisplayEXT, 0x00, sizeof(strDisplayEXT));
	if (ucResultType == TRUE)
		strDisplayEXT.ucSoundType = 0xFE;
	else
		strDisplayEXT.ucSoundType = 0xFD;
	strcpy(strDisplayEXT.szMessage1, pszMessage1);
	strcpy(strDisplayEXT.szMessage2, pszMessage2);
	strcpy(strDisplayEXT.szMessage3, pszMessage3);
	strcpy(strDisplayEXT.szMessage4, pszMessage4);
	vduiDisplayStringCenterEXT(); 

	CTOS_LCDTPrintXY(1, 2,"                                            ");	
	CTOS_LCDTPrintXY(1, 3,"                                            ");	
	CTOS_LCDTPrintXY(1, 4,"                                            ");	
	CTOS_LCDTPrintXY(1, 5,"                                            ");	
	CTOS_LCDTPrintXY(1, 6,"                                            ");	
	CTOS_LCDTPrintXY(1, 7,"                                            ");	
	CTOS_LCDTPrintXY(1, 8,"                                            ");	

	if (strlen(pszMessage4) > 0)
	{
		CTOS_LCDTPrintXY(1, 5, pszMessage1);
		CTOS_LCDTPrintXY(1, 6, pszMessage2);
		CTOS_LCDTPrintXY(1, 7, pszMessage3);
		CTOS_LCDTPrintXY(1, 8, pszMessage4);
    	CTOS_Delay(1500);
	}
	else if (strlen(pszMessage3) > 0)
	{
		CTOS_LCDTPrintXY(1, 6, pszMessage1);
		CTOS_LCDTPrintXY(1, 7, pszMessage2);
		CTOS_LCDTPrintXY(1, 8, pszMessage3);
    	CTOS_Delay(1500);
	}
	else if (strlen(pszMessage2) > 0)
	{
		CTOS_LCDTPrintXY(1, 7, pszMessage1);
		CTOS_LCDTPrintXY(1, 8, pszMessage2);
    	CTOS_Delay(1500);
	}
	else if (strlen(pszMessage1) > 0)
	{
		CTOS_LCDTPrintXY(1, 8, pszMessage1);
    	CTOS_Delay(1500);
	}
}

void vduiDisplayIdleBmpEXT(void)
{	
	memset(&strDisplayEXT, 0x00, sizeof(strDisplayEXT));

	strDisplayEXT.ucSoundType = 0xFF;
	
	vduiDisplayStringCenterEXT();
}

void vdResetDefaultBGFont(void)
{
	CTOS_LCDTSelectFontSize(d_FONT_16x30);
	CTOS_LCDBackGndColor(RGB(255, 255, 255));
	CTOS_LCDForeGndColor(RGB(0, 0, 0));
}


int inUseTouchGUI(void)
{
	if (4 == strTCT.byTerminalType && 1 == strTCT.byPinPadMode)
		return d_OK;

	return d_NO;

}

