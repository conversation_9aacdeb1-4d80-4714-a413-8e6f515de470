#include <string.h>
#include <stdio.h>
#include <stdlib.h>
#include <stdarg.h>

#include <ctosapi.h>

#include <unistd.h>
#include <pwd.h>
#include <sys/types.h>

#include <sys/stat.h>
#include <sys/time.h>
#include <sys/wait.h>
#include <fcntl.h>
#include <errno.h>
#include <dirent.h>
#include <signal.h>
#include <pthread.h>
#include <sys/shm.h>
#include <linux/errno.h>
#include <sys/types.h>
#include <sys/ipc.h>
#include <sys/msg.h>

#include "..\ApTrans\MultiApTrans.h"
#include "..\Database\DatabaseFunc.h"

#include "..\Includes\POSTypedef.h"
#include "..\Includes\Wub_lib.h"

#include "..\Debug\Debug.h"

#include "..\ApTrans\MultiShareEXT.h"
#include "..\Includes\MultiApLib.h"

USHORT usCTOSS_EXTInitialize(void)
{
    BYTE bInBuf[40];
    BYTE bOutBuf[40];
    BYTE *ptr = NULL;
    USHORT usInLen = 0;
    USHORT usOutLen = 0;
    USHORT usResult;
	char szAPName[100];
	int inAPPID;
	
	memset(bOutBuf,0x00,sizeof(bOutBuf));
	memset(szAPName,0x00,sizeof(szAPName));
	inMultiAP_CurrentAPNamePID(szAPName, &inAPPID);
    memset(bOutBuf, 0x00, sizeof(bOutBuf));    
	strcpy(bInBuf, szAPName);
	usInLen = strlen(szAPName);

    usResult = inMultiAP_RunIPCCmdTypes("com.Source.SHARLS_EXT.SHARLS_EXT", d_IPC_CMD_EXT_Initialize, bInBuf, usInLen, bOutBuf, &usOutLen);
    vdDebug_LogPrintf("**usCTOSS_EXTInitialize [%s][%d]**", szAPName, usResult);
    if(d_OK == usResult)
    {
        //status
/*
        ptr = ptCTOSS_FindTagAddr(SHARE_EMV_RESP_STATU, bOutBuf, usOutLen);
        if (NULL == ptr)
        {
            usResult = d_NO;
        }
        else
        {
            usResult = ptr[SHARE_EMV_DEFINE_TAGS_LEN+SHARE_EMV_DEFINE_LEN];
        }
*/
    }
    
    return usResult;
}

