
#ifndef MULTIAPTRANS_H
#define MULTIAPTRANS_H

#define d_MAX_APP				25
#define	d_AP_FLAG_DEF_SEL_EX	0xC0

#define	d_CTLS_EVENT			99
#define	d_SUCCESS				100
#define	d_FAIL					101
#define	d_NOT_RECORD			102

#define d_MAX_IPC_BUFFER		8192*2

#define MULTI_AP_READ_DB_FLAG 	"IN01"
#define MULTI_AP_INVOICE 	"IN02"
#define MULTI_AP_CDF_FLAG 	"IN03"


#define SHARE_MDB_SUB_IPC                "IPC"
#define IPC_MDB_SEND_AND_RCV             "1"
#define IPC_MDB_START_TRANS              "2"
#define IPC_MDB_EXTEND_TIMEOUT           "3"
#define IPC_MDB_UPDATE_FINALFARE         "4"
#define IPC_MDB_TRANS_COMPLETE         	 "5"
#define IPC_MDB_CANCELLED          		 "6"

#define PAYMENT_GROUP_CREDIT		1
#define PAYMENT_GROUP_NETS		2
#define PAYMENT_GROUP_CUP			3
#define PAYMENT_GROUP_CTLS		4
#define PAYMENT_GROUP_EPINS		5
#define PAYMENT_GROUP_DASH		6
#define PAYMENT_GROUP_MASTERPASS	7
#define PAYMENT_GROUP_KARHOO	8
#define PAYMENT_GROUP_QRCODE	9
#define PAYMENT_GROUP_ALIPAY	10
#define PAYMENT_GROUP_VCAB	11
#define PAYMENT_GROUP_COF	12
#define PAYMENT_GROUP_PAYLAH	13
#define PAYMENT_GROUP_NOF	14
#define PAYMENT_GROUP_ALIPREAUTH	15
#define PAYMENT_GROUP_LAZADA	16

#define CN2_PAYMODE_CREDIT 		0x01
#define CN2_PAYMODE_CABCHARGE	0x03
#define CN2_PAYMODE_NETS			0x08
#define CN2_PAYMODE_EVOUCHER		0x09
#define CN2_PAYMODE_PREPAID		0x10
#define CN2_PAYMODE_CUP			0x11
#define CN2_PAYMODE_FLASHPAY		0x12
#define CN2_PAYMODE_EPINS			0x13
#define CN2_PAYMODE_EZLINK		0x14
#define CN2_PAYMODE_DASH			0x15
#define CN2_PAYMODE_MASTERPASS	0x16
#define CN2_PAYMODE_KARHOO		0x17
#define CN2_PAYMODE_ALIPAY		0x18
#define CN2_PAYMODE_DBS_PAYLAH	0x19
#define CN2_PAYMODE_OCBC_PAYANYONE	0x20
#define CN2_PAYMODE_UOB_MIGHTY	0x21
#define CN2_PAYMODE_OPEN_PAYMENT	0x22
#define CN2_PAYMODE_UBER			0x23
#define CN2_PAYMODE_WECHAT		0x24
#define CN2_PAYMODE_LAZADA		0x26


#define IPC_MDB_PRINT                	 "8"

#define ENTRY_MODE_SWIPE			0x00
#define ENTRY_MODE_MANUAL			0x01
#define ENTRY_MODE_CHIP			0x02
#define ENTRY_MODE_CONTACTLESS	0x03
#define ENTRY_MODE_MASTERPASS		0x04
#define ENTRY_MODE_QRCODE			0x05
#define ENTRY_MODE_INAPP			0x06
#define ENTRY_MODE_H5			0x07

#define COMPLETE_ONLINE	0x01
#define COMPLETE_OFFLINE	0x02


#define PAYMODE_CREDIT 		0x01
#define PAYMODE_CABCHARGE	0x02
#define PAYMODE_NETS			0x03
#define PAYMODE_EVOUCHER		0x04
#define PAYMODE_PREPAID		0x05
#define PAYMODE_CUP			0x06
#define PAYMODE_FLASHPAY		0x07
#define PAYMODE_EPINS			0x08
#define PAYMODE_EZLINK			0x09
#define PAYMODE_CEPAS			0x0A
#define PAYMODE_MASTERPASS	0x0B
#define PAYMODE_DASH			0x10
#define PAYMODE_KARHOO		0x11
#define PAYMODE_QRCODE		0x12
#define PAYMODE_ALIPAY			0x13
#define PAYMODE_VCAB			0x14
#define PAYMODE_NOF				0x15
#define PAYMODE_COF				0x16
#define PAYMODE_ALIPREAUTH		0x17
#define PAYMODE_DBS_PAYLAH	0x19
#define PAYMODE_OCBC_PAYANYONE	0x20
#define PAYMODE_UOB_MIGHTY	0x21
#define PAYMODE_OPEN_PAYMENT	0x22
#define PAYMODE_UBER			0x23
#define PAYMODE_WECHAT		0x24
#define PAYMODE_LAZADA		0x26

#define MDT_CARD_NUMBER_SIZE			19
#define MDT_CARD_EXPIRY_SIZE			3
#define MDT_CARD_HOLDER_NAME_SIZE	26
#define MDT_COMPUTE_FINAL_FARE_RESPONSE_SIZE	88

#define DESTINATION_CREDIT	0x00
#define DESTINATION_NETS	0x01
//#define DESTINATION_EPINS	0x02
#define DESTINATION_EZLINK	0x03
#define DESTINATION_FLASHPAY	0x04
//#define DESTINATION_TMS		0x05
//#define DESTINATION_LOYALTY	0x06
#define DESTINATION_DASH	0x07
#define DESTINATION_CUPIC	0x08
//#define DESTINATION_MASTERPASS		0x09
//#define DESTINATION_KARHOO	0x10	
//#define DESTINATION_KARHOO	0x0A	
#define DESTINATION_ALIPAY	0x0C
//#define DESTINATION_NOF		0x0D
//#define DESTINATION_PAYLAH	0x0E
//#define DESTINATION_EZL_ONLINE	0x0F
//#define DESTINATION_ALIPREAUTH	0x10
//#define DESTINATION_LAZADA	0x11
#define DESTINATION_TMSS1E	0x12
#define DESTINATION_NEWEZLINK	0x15
#define DESTINATION_DINERS	0x14
#define DESTINATION_NETSNPX	0x13

#define DESTINATION_LES		0x99
#define DESTINATION_VCAB	0xF0	// just a variable/value pluck from the air, for me to get the vcab version number correctly. the actual destination is same as masterpass
#define DESTINATION_COF		0xF1	// just a variable/value pluck from the air, for me to get the cof version number correctly. the actual destination is same as masterpass


typedef struct TagCREDIT_TRANSACTION_struct
{
	long lnFareAmt;
	long lnGST;
	long lnFee;
	long lnTotalAmt;
	unsigned char ucEntryMode;
	unsigned char ucPaymentMode;	// Card Type.
	unsigned char szCardAbbrev[2];
	unsigned char szTaxiNumber[12+1];
	unsigned char szDriverID[9+1];
	unsigned char szJobNumber[10+1];
	unsigned char szTID[8];
	unsigned char szMID[15];
	unsigned char szCardNumber[19];
	unsigned char szCompanyCode[4];
	unsigned char ucBookingJob;
	unsigned char ucAdminGSTMessage;	/* 0=No Admin&GST, 1=FEE(INCL GST), 2=GST(ON ADMIN), 3=SUBJECT TO ADMIN & GST */
	unsigned char szCardBrand[20];
	// if want to add anything more fields here, need to check the size of this structure. as the TC upload is depending on it. srEMVTCRec.szCreditTransaction
}CREDIT_TRANSACTION;

typedef struct TagUPDATE_FINAL_FARE_RESPONSE_struct
{
	unsigned char ucResp;	//	0 = card is read. 1= user cancel. 2= timeout
	unsigned char ucEntryMode; // 0: swipe 2:chip
	unsigned char szCardNumber[19];		
	unsigned char szBCDCardExpiry[3];	
	unsigned char szCardHolderName[20];
	unsigned char ucCardType;	// 0: NETS, 1: CUP
	unsigned char ucAdminGSTMessage;	// 0:No Admin&GST, 1:FEE(GST INCL.), 2:GST(ON ADMIN), 3:SUBJECT TO ADMIN & GST
	long lnAdminFee;
	long lnGst;
	long lnTotalFare;
	unsigned char szFareAmt[6];
	unsigned char ucFeeDiscountType;
	unsigned char szFeeDiscountValue[6];
	unsigned char szAdminFeeOriginal[6];
	unsigned char szAdminFeeDiscount[6];
} UPDATE_FINAL_FARE_RESPONSE;

typedef struct TagMDT_TRANS_RESPONSE
{
	unsigned char ucTransactionType;
	unsigned char ucPaymentMode;
	unsigned char ucCompletionMode;
	unsigned char szJobNumber[10];
	unsigned char szBCDSTAN[3];
	unsigned char szMID[15];
	unsigned char szTID[8];
	unsigned char szBCDRRN[6];
	unsigned char szResponseCode[2];
	unsigned char szBCDForeignAmt[6];
	unsigned char ucEntryMode;
	unsigned char szAccountType[3];
	unsigned char szAuthCode[6];
	unsigned char szCardNumber[19];
	unsigned char szCardHolderName[26];
	unsigned char szBCDExpiryDate[3];	//ddmmyy
	unsigned char szBCDTransDate[3];	//ddmmyy
	unsigned char szBCDTransTime[3];	//hhmmss
	unsigned char szApplicationName[20];
	unsigned char szAID[15];
	unsigned char szTVR[5];
	unsigned char szAppCrypto[8];
	unsigned char szPurseCounter[8];
	unsigned char szBCDPurseBal[3];
	unsigned char szBCDPrepaidBal[6];
	unsigned char szForeignMID[15];
	unsigned char szBCDPrevPurseBal[3];
	unsigned char ucAgentID[2];
	unsigned char szBCDAppVer[3];
	unsigned char szBCDParamVer[3];
	unsigned char ucAdminGSTMessage;	// 0=No Admin&GST, 1=FEE(INCL GST), 2=GST(ON ADMIN), 3=SUBJECT TO ADMIN & GST 
	unsigned char szCardBrand[20];
	unsigned char ucPrintSigntureLine;	// 00: No Signature is required, 01: Signature Line is required
}MDT_TRANS_RESPONSE;

typedef struct
{
	int inResult;

}IPC_RESP_INFO;
IPC_RESP_INFO strIPCResp;


typedef enum
{
    d_IPC_CMD_ONLINES_SALE		= 0x01,
	d_IPC_CMD_OFFLINE_SALE		= 0x02,
	d_IPC_CMD_VOID_SALE			= 0x03,
    d_IPC_CMD_PRE_AUTH			= 0x04,
    d_IPC_CMD_REFUND				= 0x05,
    d_IPC_CMD_TIP_ADJUST			= 0x06,
    d_IPC_CMD_SETTLEMENT			= 0x07,
    d_IPC_CMD_BATCH_REVIEW		= 0x08,
    d_IPC_CMD_BATCH_TOTAL			= 0x09,
    d_IPC_CMD_REPRINT_LAST		= 0x0A,
    d_IPC_CMD_REPRINT_ANY			= 0x0B,
    d_IPC_CMD_REPRINT_LAST_SETT	= 0x0C,
    d_IPC_CMD_SUMMARY_REPORT		= 0x0D,
    d_IPC_CMD_DETAIL_REPORT		= 0x0E,
    d_IPC_CMD_GET_PID				= 0x10,
    d_IPC_CMD_POWER_FAIL			= 0x11,
    d_IPC_CMD_DELETE_BATCH		= 0x12,
    d_IPC_CMD_DELETE_REVERSAL	= 0x13,
    d_IPC_CMD_SETTLE_ALL			= 0x14,
    d_IPC_CMD_CHECK_DUP_INV      = 0x15,
    d_IPC_CMD_WAVE_SALE			= 0x16,
    d_IPC_CMD_WAVE_REFUND			= 0x17,
    d_IPC_CMD_FUN_KEY1			= 0x18,
    d_IPC_CMD_FUN_KEY2		    = 0x19,
    d_IPC_CMD_FUN_KEY3			= 0x1A,
    d_IPC_CMD_FUN_KEY4		    = 0x1B,
    d_IPC_CMD_CHK_BATCH_EMPTY    = 0x1C,
    d_IPC_CMD_ERM_INIT			= 0x20,

	d_IPC_CMD_CTLS_EXT_TRANS			= 0x2B,
	d_IPC_CMD_EXT_SIGNATURE			= 0x2C,

	d_IPC_CMD_GET_ACCUMDATA			= 0x30,
	d_IPC_CMD_OFFLINE_UPLOAD			= 0x33, 
	d_IPC_CMD_TC_UPLOAD 				= 0x34, 

	d_IPC_CMD_REVERSAL_UPLOAD			= 0x37, 

	d_IPC_CMD_EXT_READEMVCARD2			= 0x36,	// d_IPC_CMD_EXT_READEMVCARD is for VisaMaster // 2P CDF in progress
	d_IPC_CMD_EXT_NPX_READEMVCARD		= 0xF7,
	d_IPC_CMD_CHIP_NETS_EXT_TRANS		= 0x38,
	d_IPC_CMD_CHIP_NPX_EXT_TRANS		= 0x39,
	
	d_IPC_CMD_EXT_READCARD			= 0x40,
	d_IPC_CMD_EXT_READEMVCARD			= 0x41,

	d_IPC_CMD_EXT_REMOVECARD            = 0x47,
	
	d_IPC_CMD_EXT_LOADTMK				= 0x50,
	d_IPC_CMD_EXT_GET_ONLINEPIN			= 0x51,
	
	d_IPC_CMD_NPX_TERM_REFRESH			= 0x60,
	d_IPC_CMD_NPX_EXT_GET_ONLINEPIN		= 0x61,
	d_IPC_CMD_EXT_CHECKICCREMOVED		= 0x62,

	d_IPC_CMD_LOGON__SYNC			    = 0x72,
	d_IPC_CMD_PASSDATA_NPX			= 0x75,
	d_IPC_CMD_UPDATEDATA_NPX			= 0x77,
	d_IPC_CMD_NPX_CLEARBATCHACCUM			= 0x7D,

	d_IPC_CMD_PASSDATA				= 0x97,
	d_IPC_CMD_WIFISCAN				= 0x98,
    d_IPC_CMD_SETFONT				= 0x99,
    d_IPC_CMD_NPX_ENTER_MENU		= 0xA2,
    d_IPC_CMD_NPX_SALES				= 0xA7,
	d_IPC_CMD_ECR_NPX_SALES			= 0xA8,
	d_IPC_CMD_ECR_NPX_VOID			= 0xA9,
	d_IPC_CMD_ECR_NPX_REVERSAL		= 0xAA,
	d_IPC_CMD_ECR_NPX_NW_CONNECT	= 0xAB,
	d_IPC_CMD_ECR_NPX_CHK_ECN		= 0xAC,
	d_IPC_CMD_ECR_NPX_SETTLE		= 0xAD,
	d_IPC_CMD_ECR_NPX_PRESETTLE		= 0xAE,
	d_IPC_CMD_ECR_NPX_LASTSETTLE	= 0xAF,

	//TMS IPC command
	d_IPC_CMD_TMS_DO_LOGON					= 0xB0,
	d_IPC_CMD_TMS_DO_PARA_DL				= 0xB1,//will do para version request first, then do para download if needed
	d_IPC_CMD_TMS_DO_APPS_DL				= 0xB2,//will do app version request first, then do apps download if needed
	d_IPC_CMD_TMS_DO_LOGS_UPLOAD			= 0xB3,
	d_IPC_CMD_TMS_DO_STUCK_TXN_UPLOAD		= 0xB4,
	d_IPC_CMD_TMS_APP_UPDATED		= 0xB5,

	d_IPC_CMD_ECRV40_NPX_SALE		= 0xC0,
	d_IPC_CMD_ECRV40_NPX_VOID		= 0xC1,
	d_IPC_CMD_ECRV40_NPX_SETTLE		= 0xC2,
	d_IPC_CMD_ECRV40_NPX_PRESETTLE	= 0xC3,
	
    d_IPC_CMD_NPX_DIAGNOSTIC		= 0xFC, /*for Diagnaustic test*/

    d_IPC_CMD_APP_SUB_MENU		=0xFD,//reserved , Auto trigger other App sub menu 
	d_IPC_CMD_APP_MENU_FUNC		=0xFE,//reserved , Auto trigger other App menu function
    d_IPC_BACKUP_ALLDATA		=0xFF,//reserved

}IPC_CMD_TYPES;

typedef enum
{
	d_IPC_CMD_CTLS_TRANS		= 0x02,
	d_IPC_CMD_CTLS_CANCLE		= 0x03,
	d_IPC_CMD_CTLS_GETDATA		= 0x04,
	d_IPC_CMD_CTLS_SETPOWPARA	= 0x05,
	d_IPC_CMD_CTLS_PARTIALSETTAGS	= 0x0E,

}IPC_CTLSCMD_TYPES;


typedef struct
{
	unsigned char ucSoundType;
	unsigned char szMessage1[30+1];
	unsigned char szMessage2[30+1];
	unsigned char szMessage3[30+1];
	unsigned char szMessage4[30+1];
	unsigned char szMessage5[30+1];
	unsigned char szMessage6[30+1];
	unsigned char szMessage7[30+1];
	unsigned char szMessage8[30+1];
}STRUCT_DISPLAY_EXT;

typedef struct _PAYMENT_INFO_
{
    long 	lnAmount;
    char	szJobNumber[10+1];
    char 	chBookingJob;
    char 	szTaxiNumber[20+1];
    char 	szCompanyCode[4+1];
    char 	szDriverId[9+1];
    char 	szMaskedCardNumber[19+1];
    char 	szExpiryDate[3+1];
    char		chBookingMode;	// 0 NA, 01-masterpass, 02-Karhoo, 03 - cabcharge, 04-cof, 05-paylah, 06-nof
    char		chUseCN2PaymentMode;
    char 	szPaymentInfo[20];
    char 	szTripInformation[200+1];
    char 	szPrivateField[102+1];
} PAYMENT_INFO;
PAYMENT_INFO srPmtInfo;

typedef struct _PAYMENT_ADDIONAL_INFO_
{
    long 	lnFareAmount;
    long 	lnGSTAmount;
    long 	lnAdminFeeAmount;
    long 	lnTotalAmount;
    char 	szAddPaymentInfo[64];
} PAYMENT_ADDIONAL_INFO;
PAYMENT_ADDIONAL_INFO srPmtAddionalInfo;


int inMultiAP_RunIPCCmdTypes(char *Appname, int IPC_EVENT_ID, BYTE *inbuf, USHORT inlen, BYTE *outbuf, USHORT *outlen);
int inMultiAP_RunIPCCmdTypesEx(char *Appname, int IPC_EVENT_ID, BYTE *inbuf, USHORT inlen, BYTE *outbuf, USHORT *outlen);
USHORT inMultiAP_HandleIPC(BYTE *inbuf, USHORT inlen, BYTE *outbuf, USHORT *outlen);
int inMultiAP_GetMainroutine(void);
int inMultiAP_SendChild(BYTE *inbuf, USHORT inlen);

int inCTOS_MultiAPALLAppEventID(int IPC_EVENT_ID);
int inCTOS_MultiAPALLAppEventIDEx(int IPC_EVENT_ID);
int inCTOS_MultiAPBatchSearch(int IPC_EVENT_ID);
int inCTOS_MultiAPSaveData(int IPC_EVENT_ID);
int inCTOS_MultiAPGetData(void);
int inCTOS_MultiAPGetpowrfail(void);
int inCTOS_MultiAPGetVoid(void);
int inCTOS_MultiAPGetVoidByTraceNum(void);
int inCTOS_MultiAPReloadTable(void);
int inCTOS_MultiAPReloadHost(void);
int inCTOS_MultiAPCheckAllowd(void);
int inMultiAP_ECRGetMainroutine(void);
int inMultiAP_EXTGetMainroutine(void);
int inCTOSS_MultiDislplayMenu(int IPC_EVENT_ID);
int inMultiAP_ECRSendSuccessResponse(void);
int inCTOSS_SetALLApFont(char *font);
void vdCTOSS_MultiAPAnalysis(BYTE *inbuf, USHORT inlen);
int inMultiAP_MDBSendSuccessResponse(void);
int inMultiAP_MDBGetMainroutine(void);
int inMultiAP_HandleSubIPC(BYTE *inbuf, USHORT inlen, BYTE *outbuf, USHORT *outlen);
int inCTOSS_MDTDoCancelled(unsigned char ucReason);
int inSubApECRPrmrDataInit(BYTE *inbuf, USHORT inlen);
int inSubApECRPrmrAddionalDataInit(BYTE *inbuf, USHORT inlen);
extern int in_gConnectType;

#endif


