
#ifndef ___MULTI_SHARLS_MDB___
#define ___MULTI_SHARLS_MDB___

typedef enum
{
	d_IPC_CMD_MDB_Initialize		    = 0x02,
    d_IPC_CMD_MDB_GetPackageData		= 0x08,
    d_IPC_CMD_MDB_SetPackageData	    = 0x09,
}IPC_MDBCMD_TYPES;

USHORT usCTOSS_MDBInitialize(void);
USHORT usCTOSS_MDB_DataGet(IN USHORT usTag, INOUT USHORT *pLen, OUT BYTE *pValue);
USHORT usCTOSS_MDB_DataSet(IN USHORT usTag, IN USHORT usLen, IN BYTE *pValue);

#endif  //end ___MULTI_SHARLS_ECR___

