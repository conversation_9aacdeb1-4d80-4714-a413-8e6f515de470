#include <string.h>
#include <stdio.h>
#include <stdlib.h>
#include <stdarg.h>

#include <ctosapi.h>

#include <unistd.h>
#include <pwd.h>
#include <sys/types.h>

#include <sys/stat.h>
#include <sys/time.h>
#include <sys/wait.h>
#include <fcntl.h>
#include <errno.h>
#include <dirent.h>
#include <signal.h>
#include <pthread.h>
#include <sys/shm.h>
#include <linux/errno.h>
#include <sys/types.h>
#include <sys/ipc.h>
#include <sys/msg.h>

#include "..\ApTrans\MultiApTrans.h"
#include "..\Database\DatabaseFunc.h"

#include "..\Includes\POSTypedef.h"
#include "..\Includes\Wub_lib.h"

#include "..\Debug\Debug.h"

#include "..\ApTrans\MultiShareMDB.h"
#include "..\Includes\MultiApLib.h"
#include "..\aptrans\MultiShareEMV.h"

USHORT usCTOSS_MDBInitialize(void)
{
    BYTE bInBuf[40];
    BYTE bOutBuf[40];
    BYTE *ptr = NULL;
    USHORT usInLen = 0;
    USHORT usOutLen = 0;
    USHORT usResult;
	char szAPName[100];
	int inAPPID;
	
	memset(bOutBuf,0x00,sizeof(bOutBuf));
	memset(szAPName,0x00,sizeof(szAPName));
	inMultiAP_CurrentAPNamePID(szAPName, &inAPPID);
    memset(bOutBuf, 0x00, sizeof(bOutBuf));    
	strcpy(bInBuf, szAPName);
	usInLen = strlen(szAPName);

    usResult = inMultiAP_RunIPCCmdTypes("com.Source.SHARLS_MDB.SHARLS_MDB", d_IPC_CMD_MDB_Initialize, bInBuf, usInLen, bOutBuf, &usOutLen);
    vdDebug_LogPrintf("**usCTOSS_MDBInitialize [%d]**", usResult);
    if(d_OK == usResult)
    {
        //status
/*
        ptr = ptCTOSS_FindTagAddr(SHARE_EMV_RESP_STATU, bOutBuf, usOutLen);
        if (NULL == ptr)
        {
            usResult = d_NO;
        }
        else
        {
            usResult = ptr[SHARE_EMV_DEFINE_TAGS_LEN+SHARE_EMV_DEFINE_LEN];
        }
*/
    }
    
    return usResult;
}

USHORT usCTOSS_MDB_MultiDataGet(IN BYTE *pTagString, INOUT USHORT *pLen, OUT BYTE *pValue)
{
    BYTE bTagStringHex[256];
    BYTE bInBuf[256];
    BYTE bOutBuf[2048];
    BYTE *ptr = NULL;
    USHORT usTagStringLen = 0;
    USHORT usInLen = 0;
    USHORT usOutLen = 0;
    USHORT usResult;
    USHORT usDataLen = 0;

    inMultiAP_Database_EMVTransferDataInit();

    usTagStringLen = strlen(pTagString);
    wub_str_2_hex(pTagString, bTagStringHex, usTagStringLen);

    usInLen = 0;
    memset(bInBuf, 0x00, sizeof(bInBuf));
    //TagString
/*
    usInLen += usCTOSS_PackTagLenValue(&bInBuf[usInLen], SHARE_EMV_GET_MULTI_TAG, usTagStringLen, bTagStringHex);
    
    memset(bOutBuf, 0x00, sizeof(bOutBuf));    
    usResult = inMultiAP_RunIPCCmdTypes("com.Source.SHARLS_ECR.SHARLS_ECR", d_IPC_CMD_ECR_GetPackageData, bInBuf, usInLen, bOutBuf, &usOutLen);
*/
     if(d_OK == usResult)
    {
        //status
/*        
        ptr = ptCTOSS_FindTagAddr(SHARE_EMV_RESP_STATU, bOutBuf, usOutLen);
        if(NULL == ptr)
        {
            usResult = d_NO;
        }
        else
        {
            usResult = ptr[SHARE_EMV_DEFINE_TAGS_LEN+SHARE_EMV_DEFINE_LEN];

            //pValue
            inMultiAP_Database_EMVTransferDataRead(&usDataLen, pValue);
            *pLen =  usDataLen; 
        }
*/
    }

    return usResult;
}

USHORT usCTOSS_MDB_MultiDataSet(IN USHORT usLen, IN BYTE *pValue)
{
    BYTE bInBuf[2048];
    BYTE bOutBuf[64];
    BYTE *ptr = NULL;
    USHORT usInLen = 0;
    USHORT usOutLen = 0;
    USHORT usResult = 0;

    inMultiAP_Database_EMVTransferDataInit();
    
    usInLen = 0;
    memset(bInBuf, 0x00, sizeof(bInBuf));
    //Tags TLV data
    inMultiAP_Database_EMVTransferDataWrite(usLen, pValue);

    memset(bOutBuf, 0x00, sizeof(bOutBuf));    
    usResult = inMultiAP_RunIPCCmdTypes("com.Source.SHARLS_MDB.SHARLS_MDB", d_IPC_CMD_MDB_SetPackageData, bInBuf, usInLen, bOutBuf, &usOutLen);
    if(d_OK == usResult)
    {
        //status
/*        
        ptr = ptCTOSS_FindTagAddr(SHARE_EMV_RESP_STATU, bOutBuf, usOutLen);
        if(NULL == ptr)
        {
            usResult = d_NO;
        }
        else
        {
            usResult = ptr[SHARE_EMV_DEFINE_TAGS_LEN+SHARE_EMV_DEFINE_LEN];
        }
*/        
    }

    return usResult;
}


#define MDB_BUFF_FILE "/home/<USER>/pub/mdblog.txt"
#define MDB_BUFF_BAKFILE "/home/<USER>/pub/mdblog.bak"

#define MAX_MDB_SIZE	(100*1024)
#define MAX_MDB_BUF	6000


static long inMDB_GetFileSize(const char* pchFileName)
{
    FILE  *fPubKey;
	long curpos,length;

	fPubKey = fopen( (char*)pchFileName, "rb" );
	if(fPubKey == NULL)
		return 0;

	curpos=ftell(fPubKey);
	fseek(fPubKey,0L,SEEK_END);
	length=ftell(fPubKey);
	fseek(fPubKey,curpos,SEEK_SET);

	fclose(fPubKey);

    return(length);
}



static int inMDB_WriteFile(unsigned char *pchFileName, unsigned char *pchRecBuf, int inMaxRecSize)
{
	int h_file;
	int inRetVal = 0;
	FILE  *fPubKey;
	int times,i;
	long length;
	char szSystemCmdPath[250];
	int inResult;
	
	vdDebug_LogPrintf("inWriteFile[%s],inMaxRecSize=[%d]", pchFileName,inMaxRecSize);

	length = inMDB_GetFileSize(pchFileName);
	if(length > MAX_MDB_SIZE)
	{
		memset(szSystemCmdPath, 0x00, sizeof(szSystemCmdPath)); 			
		sprintf(szSystemCmdPath, "mv %s %s",MDB_BUFF_FILE,MDB_BUFF_BAKFILE);
		system(szSystemCmdPath);
		remove(MDB_BUFF_FILE);
		CTOS_Delay(200);
	}

	fPubKey = fopen((char *)pchFileName, "ab+" );
	if(fPubKey == NULL)
	{
		fPubKey = fopen((char *)pchFileName, "wb+" );
		if(fPubKey == NULL)
			return -1;
	}

	if (inMaxRecSize > MAX_MDB_BUF)
	{
		times = inMaxRecSize/MAX_MDB_BUF;
		for (i = 0;i<times;i++)
		{
			inRetVal = fwrite(&pchRecBuf[i*MAX_MDB_BUF],MAX_MDB_BUF, 1, fPubKey);
		}

		times = inMaxRecSize%MAX_MDB_BUF;
		if(times>0)
		{
			inRetVal = fwrite(&pchRecBuf[i*MAX_MDB_BUF],times, 1, fPubKey);
		}
		
	}
	else
	inRetVal = fwrite(pchRecBuf,inMaxRecSize, 1, fPubKey);
	fclose(fPubKey);

	//vdDebug_LogPrintf("inWriteFile[%d].inMaxRecSize=[%d]...", inRetVal,inMaxRecSize);

	return inRetVal;
}


void vdCTOSS_SaveMDBlog(char* szMDBlog,int inSize)
{
	CTOS_RTC SetRTC;
	BYTE szCurrentTime[20];
	BYTE szECRComand[10240 + 2];

	int len;
	
	CTOS_RTCGet(&SetRTC);

	memset(szCurrentTime,0x00,sizeof(szCurrentTime));
	sprintf(szCurrentTime,"%02d%02d%02d%02d%02d%02d",SetRTC.bYear, SetRTC.bMonth, SetRTC.bDay, SetRTC.bHour, SetRTC.bMinute, SetRTC.bSecond);
	vdDebug_LogPrintf("szCurrentTime=[%s].",szCurrentTime);
//dateitime | data | end
	memset(szECRComand,0x00,sizeof(szECRComand));
	len = 0;

	//save datetime
	strcpy(&szECRComand[len],szCurrentTime);
	len += strlen(szCurrentTime);

	strcpy(&szECRComand[len],"|");
	len += 1;

	//save data
	if (inSize > 0)
	{
		strcpy(&szECRComand[len],szMDBlog);
		len += inSize;
	}
	else
	{
		strcpy(&szECRComand[len],"NULL");
		len += 4;
	}

	strcpy(&szECRComand[len],"|");
	len += 1;

	//save end flag
	strcpy(&szECRComand[len],"END");
	len += 3;

	strcpy(&szECRComand[len],"\n");
	len += 1;

	vdDebug_LogPrintf("vdCTOSS_SaveMDBlog=[%s].",szECRComand);

	inMDB_WriteFile(MDB_BUFF_FILE,szECRComand,len);
}

vdCTOSS_SaveMDBAmout(BYTE *szAmount)
{
	char szSalesAmt[12+1];
	memset(szSalesAmt,0x00,sizeof(szSalesAmt));
	wub_hex_2_str(szAmount, szSalesAmt, 6);
	vdCTOSS_SaveMDBlog(szSalesAmt,12);
}

vdCTOSS_SaveMDBISO(char *pchPkt, int inSize)
{
	char szISO[4000+1];
	int len;
	memset(szISO,0x00,sizeof(szISO));
	wub_hex_2_str(pchPkt, szISO, inSize);
	len = inSize*2;
	
	vdCTOSS_SaveMDBlog(szISO,len);
}

vdCTOSS_SaveMDBStauts(char *szstatus,int flag)
{
	char sztmpbuf[200+1];

	memset(sztmpbuf,0x00,sizeof(sztmpbuf));
	strcpy(sztmpbuf,szstatus);
	vdCTOSS_SaveMDBlog(sztmpbuf,strlen(sztmpbuf));

	if (flag == 1)
	{
		memset(sztmpbuf,0x00,sizeof(sztmpbuf));
		strcpy(sztmpbuf,"---END TRANSACTION----");
		vdCTOSS_SaveMDBlog(sztmpbuf,strlen(sztmpbuf));
	}
}



