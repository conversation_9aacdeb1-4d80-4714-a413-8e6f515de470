#include <string.h>
#include <stdio.h>
#include <stdlib.h>
#include <stdarg.h>
#include <ctosapi.h>

#include "../Database/DatabaseFunc.h"
#include "../Includes/wub_lib.h"
#include "../Includes/CTOSInput.h"
#include "pinpad.h"
#include "../Includes/EFTSec.h"
#include "../Includes/POSTypedef.h"
#include "..\Debug\Debug.h"
#include "../Ui/Display.h"
#include "../External/External.h"
#include "..\Includes\showbmp.h"
#include "../pci100/COMMS.h"

extern void clearLineToLine(int iStartLine, int iStopLine);

int g_inReCipherDUPKT = 1;

int inUseReCipherDUPKT(void)
{
	vdDebug_LogPrintf("inUseReCipherDUPKT On");
	return 1;

	//vdDebug_LogPrintf("inUseReCipherDUPKT Off");
	//return 0;
}

typedef struct 
{
    IN BYTE Version; /* Should be 0x00 or 0x01 */

    struct {
        IN USHORT KeySet;
        IN USHORT KeyIndex;
    } Info;

    struct {
        IN USHORT CipherKeySet;
        IN USHORT CipherKeyIndex;
    } Protection;

    struct {
        IN BOOL IsUseCurrentKey;
    } DUKPT_PARA;

    struct {
        IN USHORT Length;
        IN BYTE *pData;
    } Input;

    struct {
        OUT USHORT Length;
        OUT BYTE *pData;
        OUT BYTE KSNLen;
        OUT BYTE *pKSN;
    } Output;

}CTOS_KMS2PINBLOCKRECIPHER_PARA;

extern USHORT CTOS_KMS2PINBlockReCipher(CTOS_KMS2PINBLOCKRECIPHER_PARA *pPara);

/* 
 * usKeySet, usKeyIndex : should be TDES Key
 * usCipherKeySet, usCipherKeyIndex : should be TDES-DUKPT Key
 */
USHORT ushPINBlockReCipher(USHORT usKeySet, USHORT usKeyIndex, BYTE *pEncPINBlock, USHORT usEncPINBlockLen,
        USHORT usCipherKeySet, USHORT usCipherKeyIndex, BYTE *pCipherPINBlock, USHORT *usCipherLen)
{
    USHORT usRtn;
    BYTE baMsg[32];
    CTOS_KMS2PINBLOCKRECIPHER_PARA stPara;
    BYTE baKSN[10];


    //CTOS_LCDTClearDisplay();
    //CTOS_LCDTPrintXY(1, 1, "\frRE-CIPHER  PIN BLOCK\fn");
	vdDebug_LogPrintf("=====ushPINBlockReCipher=====");

	vdDebug_LogPrintf("usKeySet[%04X] usKeyIndex[%04X]", usKeySet, usKeyIndex);
	usRtn = CTOS_KMS2KeyCheck(usKeySet, usKeyIndex);
	vdDebug_LogPrintf("CTOS_KMS2KeyCheck usRtn[%04X]", usRtn);

	vdDebug_LogPrintf("usCipherKeySet[%04X] usCipherKeyIndex[%04X]", usCipherKeySet, usCipherKeyIndex);
	usRtn = CTOS_KMS2KeyCheck(usCipherKeySet, usCipherKeyIndex);
	vdDebug_LogPrintf("CTOS_KMS2KeyCheck usRtn[%04X]", usRtn);

    memset(&stPara, 0x00, sizeof (stPara));
    memset(baKSN, 0x00, sizeof (baKSN));

    stPara.Version = 0x01;
    stPara.Info.KeySet = usKeySet;
    stPara.Info.KeyIndex = usKeyIndex;
    stPara.Protection.CipherKeySet = usCipherKeySet;
    stPara.Protection.CipherKeyIndex = usCipherKeyIndex;
    stPara.Input.Length = usEncPINBlockLen;
    stPara.Input.pData = pEncPINBlock;
    stPara.Output.pData = pCipherPINBlock;
    stPara.Output.pKSN = baKSN;

//    usRtn = CTOS_KMS2PINBlockReCipher(&stPara);
    if (usRtn != d_OK) 
	{
        //sprintf(baMsg, "Error = 0x%04X", usRtn);
        //CTOS_LCDTPrintXY(1, 5, baMsg);
    }
	else 
   	{
        //CTOS_LCDTPrintXY(1, 5, "Success");
        *usCipherLen = stPara.Output.Length;
		memcpy(srTransRec.szKSN, baKSN, 10);
    }

	vdDebug_LogPrintf("usRtn[%04X]", usRtn);
	DebugAddHEX("pCipherPINBlock", pCipherPINBlock, 8);
	
    return usRtn;
}
int ginPinByPass = 0;
extern BYTE g_szPAN[20+1];
extern BYTE g_szPPDispAmt[12+1];

#define PIN_POSITION_X	1
#define PIN_POSITION_Y	8
#define PIN_POSITION_Y_CFC	10
char card_holder_pin[16+1];
ULONG g_TouchTimer = 0;

BYTE byGetPINBypassStatus(void)
{
	return ginPinByPass;
}

void vdSetPINBypassStatus(BYTE byPINBypass)
{
	ginPinByPass = byPINBypass;
}

void vdSetTouchTimer(ULONG ulTick)
{
	g_TouchTimer = ulTick;
}

ULONG ulGetTouchTimer(void)
{
	return g_TouchTimer;
}


extern USHORT CTOS_VirtualFunctionKeyHit(BYTE *key);
extern USHORT CTOS_VirtualFunctionKeySet( CTOS_VIRTUAL_FUNCTION_KEY_SET_PARA* pPara, BYTE FuncKeyCount);

CTOS_VIRTUAL_FUNCTION_KEY_SET_PARA pCoordinateDoubleButton[4] = 
{{18, 390, 160, 455},	//F1
{160, 390, 302, 455},	//F2
{2, 1, 3, 2},	//F3 dummy
{3, 1, 4, 2}};	//F4 dummy

int inInitializePinPad(void)
{
	vdDebug_LogPrintf("=====inInitializePinPad=====");
		CTOS_KMS2Init();
		TEST_Write3DES_Plaintext();
		return d_OK;
}

#define d_CTOS_TOOL_INVALID_PARA d_NO
USHORT CTOS_TOOLCalculateKSNRange(IN BYTE *CurrentKSN, IN BYTE *NewKSN, OUT ULONG *Range)
{
	ULONG ulCurrentKSN, ulNewKSN;

	vdDebug_LogPrintf("=====CTOS_TOOLCalculateKSNRange=====");
	
	if( CurrentKSN == NULL ||
		NewKSN == NULL ||
		Range == NULL )
	{
		return d_CTOS_TOOL_INVALID_PARA;
	}
	
	*Range = 0;

	if(memcmp(CurrentKSN, NewKSN, 7) != 0)
	{
		vdDebug_LogPrintf("KSN 7 bytes not match");
		return d_CTOS_TOOL_INVALID_PARA;
	}

	ulCurrentKSN  = CurrentKSN[7] * 256 * 256;
	ulCurrentKSN += CurrentKSN[8] * 256;
	ulCurrentKSN += CurrentKSN[9];

	ulNewKSN  = NewKSN[7] * 256 * 256;
	ulNewKSN += NewKSN[8] * 256;
	ulNewKSN += NewKSN[9];

	if(ulNewKSN < ulCurrentKSN)
	{	
		*Range = 0;
		return d_CTOS_TOOL_INVALID_PARA;
	}	

	*Range = ulNewKSN - ulCurrentKSN;
	return d_OK;
}

int inNPX_SyncExtInternalPPKSN(void)
{
	BYTE baTmlKSN[10];
	BYTE baExtPPKSN[10];
	BYTE ksn_len = 10;
	ULONG ulRange = 0;
	
	USHORT ushRet = d_OK;

	unsigned char szV3PSendBuf[1024+1];
	unsigned char szRecvBuf[1024+1];
	int inRecvlen = 0;
	int inOffSet = 0;
	int status = 0;
	unsigned char *pszPtr;

	char szDSPBuf[100+1];

	vdDebug_LogPrintf("=====inNPX_SyncExtInternalPPKSN=====");

	if(strTCT.byPinPadMode == 0)
	{
		if(strTCT.byPinPadType == 3)
		{
			char szV3PSendBuf[1024+1];
			int inOffSet = 0;

			/*Get Tml KSN*/
			memset(baTmlKSN, 0x00, sizeof(baTmlKSN));
			ushRet = CTOS_KMS2DUKPTGetKSN(NPX_DUKPT_KEYSET, NPX_DUKPT_KEYINDEX, baTmlKSN, &ksn_len);
    		vdDebug_LogPrintf("CTOS_KMS2DUKPTGetKSN[%04X] ksn_len[%d]", ushRet, ksn_len);
    		DebugAddHEX("TML KSN:", baTmlKSN, 10);

			#if 0
			ushRet = CTOS_KMS2DUKPTIncreaseKSN(0xC000, 0x0004, 2);
			memset(baTmlKSN, 0x00, sizeof(baTmlKSN));
			ushRet = CTOS_KMS2DUKPTGetKSN(0xC000, 0x0004, baTmlKSN, &ksn_len);
    		vdDebug_LogPrintf("CTOS_KMS2DUKPTGetKSN[%04X] ksn_len[%d]", ushRet, ksn_len);
    		DebugAddHEX("Inc TML KSN:", baTmlKSN, 10);
			#endif
			
			vdDebug_LogPrintf("inNPX_SyncExtInternalPPKSN");
			
			vdCTOSS_SetV3PRS232Port(strTCT.byPinPadPort);
			inCTOSS_V3PRS232Open(strTCT.inPPBaudRate, 'N', 8, 1);
			CTOS_Delay(50); // may need adjust this delay to make it more stable
			// send STX PING ETX LRC
			memset(szV3PSendBuf, 0x00, sizeof(szV3PSendBuf));
			inOffSet = 0;
			szV3PSendBuf[inOffSet] = STX;
			inOffSet += 1;
			memcpy(&szV3PSendBuf[inOffSet], "NPX_SYNC_KSN", strlen("NPX_SYNC_KSN"));
			inOffSet += strlen("NPX_SYNC_KSN");
			szV3PSendBuf[inOffSet] = '|';
			inOffSet += 1;
			memcpy(&szV3PSendBuf[inOffSet], baTmlKSN, sizeof(baTmlKSN));
			inOffSet += sizeof(baTmlKSN);
			szV3PSendBuf[inOffSet] = ETX;
			inOffSet += 1;
		    szV3PSendBuf[inOffSet] = (char) wub_lrc(&(szV3PSendBuf[1]), inOffSet-1);
			inOffSet += 1;
			inCTOSS_V3PRS232SendBuf(szV3PSendBuf, inOffSet);
			//CTOS_Delay(get_env_int("TO"));
			CTOS_Delay(100); // may need adjust this delay to make it more stable
			memset(szRecvBuf,0x00,sizeof(szRecvBuf));
			status = inCTOSS_V3PRS232RecvBuf(szRecvBuf, &inRecvlen, 5);
			/*close port after receive*/
			inCTOSS_V3PRS232Close();

			if (status != d_OK)
			{
				//vdDispErrMsg("PinPad Not Ready(KSN)");
				vdDisplayErrorMsg(1, 8, "Sync KSN...");
				return d_NO;
			}
			
			pszPtr = (char*)memchr(szRecvBuf, '|', inRecvlen); // check STX
			if (NULL == pszPtr)
			{
				//sometime the response error, may can not get |, 
				//pszPtr will be NULL, copy will cause crash
				return d_NO;
			}

			memset(baExtPPKSN, 0x00, sizeof(baExtPPKSN));
			memcpy(baExtPPKSN, &pszPtr[1], 10);
			DebugAddHEX("ExtPP KSN:", baExtPPKSN, 10);
			ushRet = CTOS_TOOLCalculateKSNRange(baTmlKSN, baExtPPKSN, &ulRange);
			vdDebug_LogPrintf("Calc KSN Range: ushRet[%d] ulRange[%d]", ushRet, ulRange);

			/*memset(szDSPBuf, 0x00, sizeof(szDSPBuf));
			//wub_hex_2_str(baExtPPKSN, szDSPBuf, 10);
			//vdDisplayErrorMsg(1, 1, szDSPBuf);
			memset(szDSPBuf, 0x00, sizeof(szDSPBuf));
			//wub_hex_2_str(baTmlKSN, szDSPBuf, 10);
			//vdDisplayErrorMsg(1, 2, szDSPBuf);
			memset(szDSPBuf, 0x00, sizeof(szDSPBuf));
			sprintf(szDSPBuf, "ushRet[%d] ulRange[%d]", ushRet, ulRange);
			//vdDisplayErrorMsg(1, 3, szDSPBuf);*/

			if (d_OK == ushRet && ulRange>0)
			{
				CTOS_KMS2DUKPTIncreaseKSN(NPX_DUKPT_KEYSET, NPX_DUKPT_KEYINDEX, ulRange);
				vdDebug_LogPrintf("TML Inc KSN: ushRet[%d]", ushRet);
			}
			
			return d_OK;
			
		}
	}
}
//For testing and development only, hardcode the key
void TEST_Write3DES_Plaintext(void)
{
	USHORT KeySet;
	USHORT KeyIndex;
	CTOS_KMS2KEYWRITE_PARA para;
	USHORT ret;
	BYTE KeyData[16];
	BYTE str[17];
	BYTE key;
	BYTE szInitialVector[8], szDataIn[100], szMAC[8];

	vdDebug_LogPrintf("=====TEST_Write3DES_Plaintext=====");

	// patrick test key 20150706	
	KeySet = 0x0002;
	KeyIndex = 0x0002;
	memcpy(KeyData, "\x11\x11\x11\x11\x11\x11\x11\x11\x22\x22\x22\x22\x22\x22\x22\x22", 16);

	memset(&para, 0x00, sizeof(CTOS_KMS2KEYWRITE_PARA));
	para.Version = 0x01;
	para.Info.KeySet = KeySet;
	para.Info.KeyIndex = KeyIndex;
	para.Info.KeyType = KMS2_KEYTYPE_3DES;
	para.Info.KeyVersion = 0x01;
	para.Info.KeyAttribute = KMS2_KEYATTRIBUTE_PIN | KMS2_KEYATTRIBUTE_MAC;
	para.Protection.Mode = KMS2_KEYPROTECTIONMODE_PLAINTEXT;
	para.Value.pKeyData = KeyData;
	para.Value.KeyLength = IPP_TDES_KEY_SIZE;
	ret = CTOS_KMS2KeyWrite(&para);

	#if 0 //AAA
	strcpy(srTransRec.szPAN,"1234567890123456");
	srTransRec.HDTid = 6;
	srTransRec.byTransType = SALE;
	strcpy(strCST.szCurSymbol,"SGD");
	strcpy(srTransRec.szTotalAmount,"1");
	inGetIPPPin();
	memset(szDataIn, 'A', 100);
	memset(szInitialVector, 0, 8);
	memset(szMAC, 0, 8);
	inIPPGetMAC(szDataIn, 8, szInitialVector, szMAC);
	#endif
}
	
void inCTOS_DisplayCurrencyAmount(BYTE *szAmount, int inLine)
{
	char szDisplayBuf[50];
	char szDisplayBuf1[50];
	BYTE baAmount[20];

	DebugAddSTR("inCTOS_DisplayCurrencyAmount","Processing...",20);
	
	memset(baAmount, 0x00, sizeof(baAmount));
	wub_hex_2_str(szAmount, baAmount, 6);

	memset(szDisplayBuf, 0x00, sizeof(szDisplayBuf));
	memset(szDisplayBuf1, 0x00, sizeof(szDisplayBuf1));
	// patrick add code 20141216
	//sprintf(szDisplayBuf, "%s", strCST.szCurSymbol);	
	//format amount 10+2
	vdCTOSS_FormatAmount("NN,NNN,NNN,NNn.nn", baAmount, szDisplayBuf1);
	sprintf(szDisplayBuf,"%s%s", strCST.szCurSymbol,szDisplayBuf1);
	vdDebug_LogPrintf("AAA - inCTOS_DisplayCurrencyAmount szDisplayBuf:%s",szDisplayBuf);
	setLCDPrint(inLine, DISPLAY_POSITION_LEFT, szDisplayBuf);
}

void OnGetPINDigit(BYTE NoDigits)
{
	BYTE i;
	char szMaskedPIN[12 + 1];
	char szUnderline[12 + 1];

	#ifdef HAWKER_CFC
	if (strTCTEX.fHawkerMode == 1 && (strTCTEX.inCFCRetailMode == 0 || strTCTEX.inCFCRetailMode == 1 || strTCTEX.inCFCRetailMode == 3)
		|| d_OK == inUseTouchGUI())
	{
		clearLine(PIN_POSITION_Y_CFC);
		clearLine(PIN_POSITION_Y_CFC+1);
		
		memset(szMaskedPIN, 0x00, sizeof(szMaskedPIN));
		memset(szUnderline, 0x00, sizeof(szUnderline));
		for(i=0;i<NoDigits;i++)
		{
			strcat(szMaskedPIN, "*");
			strcat(szUnderline, "-");
		}
		CTOS_LCDTPrintAligned(PIN_POSITION_Y_CFC, szMaskedPIN, d_LCD_ALIGNCENTER);
		CTOS_LCDTPrintAligned(PIN_POSITION_Y_CFC+1, szUnderline, d_LCD_ALIGNCENTER);
	}
	else
	{
	for(i=0;i<NoDigits;i++)
		CTOS_LCDTPrintXY(PIN_POSITION_X+i, PIN_POSITION_Y,"*");
		
	for(i=NoDigits;i<12;i++)
		CTOS_LCDTPrintXY(PIN_POSITION_X+i, PIN_POSITION_Y," ");
	}
	#else
	for(i=0;i<NoDigits;i++)
		CTOS_LCDTPrintXY(PIN_POSITION_X+i, PIN_POSITION_Y,"*");
		
	for(i=NoDigits;i<12;i++)
		CTOS_LCDTPrintXY(PIN_POSITION_X+i, PIN_POSITION_Y," ");
	#endif
	
   DebugAddINT("OnGetPINDigit", NoDigits);

}

void OnGetPINCancel(void)
{
   DebugAddINT("OnGetPINCancel", 1);

}

void OnGetPINBackspace(BYTE NoDigits)
{
	BYTE i;
	char szMaskedPIN[12 + 1];
	char szUnderline[12 + 1];
	
	#ifdef HAWKER_CFC
	if (strTCTEX.fHawkerMode == 1 && (strTCTEX.inCFCRetailMode == 0 || strTCTEX.inCFCRetailMode == 1 || strTCTEX.inCFCRetailMode == 3)
		|| d_OK == inUseTouchGUI())
	{
		clearLine(PIN_POSITION_Y_CFC);
		clearLine(PIN_POSITION_Y_CFC+1);
		memset(szMaskedPIN, 0x00, sizeof(szMaskedPIN));
		memset(szUnderline, 0x00, sizeof(szUnderline));
		for(i=0;i<NoDigits;i++)
		{
			strcat(szMaskedPIN, "*");
			strcat(szUnderline, "-");
		}
		CTOS_LCDTPrintAligned(PIN_POSITION_Y_CFC, szMaskedPIN, d_LCD_ALIGNCENTER);
		CTOS_LCDTPrintAligned(PIN_POSITION_Y_CFC+1, szUnderline, d_LCD_ALIGNCENTER);	
		if (NoDigits == 0)
			CTOS_LCDTPrintAligned(PIN_POSITION_Y_CFC+1, "-", d_LCD_ALIGNCENTER);	
	}
	else
	{
		for(i=0;i<NoDigits;i++)
			CTOS_LCDTPrintXY(PIN_POSITION_X+i, PIN_POSITION_Y,"*");
		
		for(i=NoDigits;i<12;i++)
			CTOS_LCDTPrintXY(PIN_POSITION_X+i, PIN_POSITION_Y," ");
	}
	#else
	for(i=0;i<NoDigits;i++)
		CTOS_LCDTPrintXY(PIN_POSITION_X+i, PIN_POSITION_Y,"*");
		
	for(i=NoDigits;i<12;i++)
		CTOS_LCDTPrintXY(PIN_POSITION_X+i, PIN_POSITION_Y," ");
	#endif
	DebugAddINT("OnGetPINBackspace", NoDigits);
}

void OnGetPINOtherKeys(BYTE NoDigits)
{
	vdDebug_LogPrintf("NoDigits[%d] NoDigits[%c]", NoDigits, NoDigits);
	
	//#define d_KBD_K1                                                        'X'
    //#define d_KBD_K2                                                        'Y'
   // #define d_KBD_K3                                                        'I'
	//terminal type with F1, F2, F3 keys
	if (strTCT.byTerminalType == 1 || 
		strTCT.byTerminalType == 2 || 
		strTCT.byTerminalType == 3 || 
		strTCT.byTerminalType == 4 ||
		strTCT.byTerminalType == 8)
	{
		if ((NoDigits != 88) && (NoDigits != 89) && (NoDigits != 73))
			return;
	}
	else
	{
		if ((NoDigits != d_KBD_DOWN) && (NoDigits != d_KBD_UP))
		{
			return;
		}
	}
	
    #ifdef HAWKER_CFC
	if (strTCTEX.fHawkerMode == 1 && (strTCTEX.inCFCRetailMode == 0 || strTCTEX.inCFCRetailMode == 1 || strTCTEX.inCFCRetailMode == 3)
		|| d_OK == inUseTouchGUI())
	{
		if ((NoDigits == 88) || (NoDigits == 89))
		{
			/*Avoid previous touch*/
			if((CTOS_TickGet() - ulGetTouchTimer()) > 50/*200*/)
			{
				CTOS_KBDBufPut(d_KBD_ENTER);
				return;
			}
		}
	}
	#endif
	
   DebugAddINT("OnGetPINOtherKeys", NoDigits);
	
}

void vdDisplayErrorMessage(USHORT errorID)
{
	char szErr[50+1];

	memset(szErr,0x00,sizeof(szErr));
	switch(errorID)
	{
		case d_KMS2_GET_PIN_TIMEOUT:
			strcpy(szErr,"TIME OUT");
			break;
		case d_KMS2_GET_PIN_ABORT:
			strcpy(szErr,"GET PIN ABORT");
			break;
		default:
			strcpy(szErr,"GET PIN ABORT");
			break;
	}
	if (d_OK == inUseTouchGUI())
		vdDisplayErrorMsgCFC(4, szErr, "", "", "", DISPLAY_POSITION_CENTER);
	else
		vdDisplayErrorMsg(1, 8, szErr);
}

#if 0
int inGetIPPPin(void)
{
	CTOS_KMS2PINGET_PARA_VERSION_2 stPinGetPara;

	BYTE str[40],key;
	USHORT ret;
	int inRet;

	vdDebug_LogPrintf("inGetIPPPin start");
	
	DebugAddSTR("inGetIPPPin","Processing...",20);
//	inDCTRead(srTransRec.HDTid);
	inDCTRead(6);

// patrick test key 20150706 start
//	strDCT.usKeySet = 0x0002;
//	strDCT.usKeyIndex = 0x0002;
//	strDCT.inMinPINDigit = 6;
//	strDCT.inMaxPINDigit = 6;
//	memcpy(strDCT.szPINKey, "\x8B\x3E\xD0\xE8\xC2\x04\x1E\xAE\xEE\x7A\x7D\x0D\x63\x99\x92\x38",  16); 
//	strcpy(srTransRec.szPAN, "6279022411700004061");
// patrick test key 20150706 end

	/*check for keys if injected -- sidumili*/
    inRet = inCheckKeys(strDCT.usKeySet, strDCT.usKeyIndex);
    if (inRet != d_OK)
        return(inRet);
    /*check for keys if injected -- sidumili*/
	
    vdDebug_LogPrintf("inDCTRead [%ld] [%ld] [%ld]", srTransRec.HDTid, strDCT.usKeyIndex, strDCT.usKeySet);

	CTOS_LCDTClearDisplay();
	vdDispTransTitle(srTransRec.byTransType);
	
	inCTOS_DisplayCurrencyAmount(srTransRec.szTotalAmount, 3);
	setLCDPrint(4, DISPLAY_POSITION_LEFT, strDCT.szDisplayLine1);
	setLCDPrint(5, DISPLAY_POSITION_LEFT, strDCT.szDisplayLine2);
	
	memset(&stPinGetPara, 0x00, sizeof(CTOS_KMS2PINGET_PARA_VERSION_2));
	stPinGetPara.Version = 0x02;
	stPinGetPara.PIN_Info.BlockType = KMS2_PINBLOCKTYPE_ANSI_X9_8_ISO_0;
	stPinGetPara.PIN_Info.PINDigitMinLength = strDCT.inMinPINDigit;
	stPinGetPara.PIN_Info.PINDigitMaxLength = strDCT.inMaxPINDigit;

	stPinGetPara.Protection.CipherKeyIndex = strDCT.usKeyIndex;
	stPinGetPara.Protection.CipherKeySet = strDCT.usKeySet;
	stPinGetPara.Protection.SK_Length = IPP_TDES_KEY_SIZE;
	stPinGetPara.Protection.pSK = strDCT.szPINKey;
	stPinGetPara.Protection.CipherMethod = KMS2_PINCIPHERMETHOD_ECB;
	stPinGetPara.AdditionalData.InLength = strlen(srTransRec.szPAN);
	stPinGetPara.AdditionalData.pInData = (BYTE*)srTransRec.szPAN;
        
	stPinGetPara.Control.Timeout = 60;
	stPinGetPara.Control.NULLPIN = TRUE;
	stPinGetPara.PINOutput.EncryptedBlockLength = 8;
	stPinGetPara.PINOutput.pEncryptedBlock = srTransRec.szPINBlock;
	stPinGetPara.EventFunction.OnGetPINBackspace = OnGetPINBackspace;
	stPinGetPara.EventFunction.OnGetPINCancel = OnGetPINCancel;
	stPinGetPara.EventFunction.OnGetPINDigit = OnGetPINDigit;

    CTOS_KBDBufFlush();//cleare key buffer

	ret = CTOS_KMS2PINGet((CTOS_KMS2PINGET_PARA *)&stPinGetPara);
	if(ret != d_OK)
	{
        if(ret == d_KMS2_GET_PIN_NULL_PIN)
        {
            CTOS_LCDTPrintXY(1, 8, "PIN BY PASSED");
            CTOS_Delay(300);
			memset(srTransRec.szPINBlock,0x00,sizeof(srTransRec.szPINBlock));
			vdDebug_LogPrintf("PIN BY PASSED");
            return d_KMS2_GET_PIN_NULL_PIN;
        }
        else
        {
            vdDisplayErrorMessage(ret);
            return ret;
        }
    }

	vdPCIDebug_HexPrintf("Pin Block",srTransRec.szPINBlock,8);
    vdDebug_LogPrintf(" pin enter");
	
	return d_OK;
}
#endif

int inGetIPPPin(void)
{
	#ifdef HAWKER_CFC
    CTOS_KMS2PINGET_PARA_VERSION_4 stPinGetPara;  // no matter CFC, for new sw, we better move to new version
	#else
    CTOS_KMS2PINGET_PARA_VERSION_2 stPinGetPara;
	#endif
	
    BYTE str[40],key;
    USHORT ret;
	char szPINKey[16+1];
	BYTE Tkey = 16;

	CTOS_KMS2Init();

	vdDebug_LogPrintf("inGetIPPPin....");

    DebugAddSTR("inGetIPPPin","Processing...",20);
    inDCTRead(srTransRec.HDTid);
	#ifdef HAWKER_CFC
	if (strTCTEX.fHawkerMode == 1 && (strTCTEX.inCFCRetailMode == 0 || strTCTEX.inCFCRetailMode == 1 || strTCTEX.inCFCRetailMode == 3))
	{
		vdDisplayPINEntryScreen();
		Tkey = (sizeof(pCoordinateDoubleButton) / sizeof(CTOS_VIRTUAL_FUNCTION_KEY_SET_PARA))%0x100;
		CTOS_VirtualFunctionKeySet(pCoordinateDoubleButton,Tkey);
	}
	else
	{
		CTOS_LCDTClearDisplay();
		if (strTCT.byPinPadMode != 1)
		{
	    vdDispTransTitle(srTransRec.byTransType);
	    memset(card_holder_pin,0,sizeof(card_holder_pin));
	    
	    inCTOS_DisplayCurrencyAmount(srTransRec.szTotalAmount, 3);
	    setLCDPrint(4, DISPLAY_POSITION_LEFT, strDCT.szDisplayLine1);
	    setLCDPrint(5, DISPLAY_POSITION_LEFT, strDCT.szDisplayLine2);
		}
		else
		{
		displaybmpEx(0, 0, "Enterpin.bmp");
		}
	}
	#else
    CTOS_LCDTClearDisplay();
	if (strTCT.byPinPadMode != 1)
	{
	    vdDispTransTitle(srTransRec.byTransType);
	    memset(card_holder_pin,0,sizeof(card_holder_pin));
	    
	    inCTOS_DisplayCurrencyAmount(srTransRec.szTotalAmount, 3);
	    setLCDPrint(4, DISPLAY_POSITION_LEFT, strDCT.szDisplayLine1);
	    setLCDPrint(5, DISPLAY_POSITION_LEFT, strDCT.szDisplayLine2);
	}
	else
	{
		displaybmpEx(0, 0, "Enterpin.bmp");
	}
	#endif
	
	if ((strTCT.byPinPadMode == 0) && (strTCT.byPinPadType == 3))
	{
		setLCDPrint(5, DISPLAY_POSITION_LEFT, "Please key in PINPAD");
		
		memset(&stOLPinParam, 0x00, sizeof(stOLPinParam));
		//stOLPinParam.HDTid = srTransRec.HDTid;
		stOLPinParam.ushKeySet = strDCT.usKeySet;
		stOLPinParam.ushKeyIdx = strDCT.usKeyIndex;
		strcpy(stOLPinParam.szPAN,srTransRec.szPAN);
		//memcpy(stOLPinParam.szPINKey,strDCT.szPINKey,16);
		ret = inCTOSS_EXTGetIPPPin();
		
		memset(card_holder_pin,0,sizeof(card_holder_pin));
		if (ret == d_OK)
		{
			if (stOLPinParam.inPINEntered == 2)
			{
				memcpy(srTransRec.szPINBlock,stOLPinParam.szPINBlock,8);
				wub_hex_2_str(srTransRec.szPINBlock,card_holder_pin,8);
			}
			ret = stOLPinParam.ushKeySet;
			
		}
		else
		{
			if (ret != d_NO)
				ret = stOLPinParam.ushKeySet;
		}
		
		vdDebug_LogPrintf("card_holder_pin[%s]",card_holder_pin);
		return ret;
	}
    
	#ifdef HAWKER_CFC
    memset(&stPinGetPara, 0x00, sizeof(CTOS_KMS2PINGET_PARA_VERSION_4));
    stPinGetPara.Version = 0x04;
	#else
    memset(&stPinGetPara, 0x00, sizeof(CTOS_KMS2PINGET_PARA_VERSION_2));
    stPinGetPara.Version = 0x02;
    #endif
	stPinGetPara.Protection.SK_Length = IPP_TDES_KEY_SIZE;
	if (strDCT.inPIN3des == 0)//for des pin
	{
		memset(szPINKey,0x00,sizeof(szPINKey));
		memcpy(szPINKey,strDCT.szPINKey,8);
		memcpy(szPINKey+8,strDCT.szPINKey,8);
		stPinGetPara.Protection.pSK = szPINKey;
	}
	else
    	stPinGetPara.Protection.pSK = strDCT.szPINKey;
    
    stPinGetPara.PIN_Info.BlockType = KMS2_PINBLOCKTYPE_ANSI_X9_8_ISO_0;
    stPinGetPara.PIN_Info.PINDigitMinLength = strDCT.inMinPINDigit;
    stPinGetPara.PIN_Info.PINDigitMaxLength = strDCT.inMaxPINDigit;
    vdDebug_LogPrintf("**Min[%d]Max[%d]**",strDCT.inMinPINDigit,strDCT.inMaxPINDigit);


    stPinGetPara.Protection.CipherKeyIndex = strDCT.usKeyIndex;
    stPinGetPara.Protection.CipherKeySet = strDCT.usKeySet;

    //stPinGetPara.Protection.CipherKeyIndex = CUP_TMK_KEY_INDEX;
    //stPinGetPara.Protection.CipherKeySet = CUP_TMK_KEY_SET;
    
    stPinGetPara.Protection.CipherMethod = KMS2_PINCIPHERMETHOD_ECB;
    stPinGetPara.AdditionalData.InLength = strlen(srTransRec.szPAN);
    stPinGetPara.AdditionalData.pInData = (BYTE*)srTransRec.szPAN;
        
	#ifdef HAWKER_CFC
    stPinGetPara.Control.Timeout = 30;
	#else
    stPinGetPara.Control.Timeout = 0;
    #endif
	stPinGetPara.Control.NULLPIN = TRUE;
    stPinGetPara.PINOutput.pEncryptedBlock = srTransRec.szPINBlock;
    stPinGetPara.EventFunction.OnGetPINBackspace = OnGetPINBackspace;
    stPinGetPara.EventFunction.OnGetPINCancel = OnGetPINCancel;
    stPinGetPara.EventFunction.OnGetPINDigit = OnGetPINDigit;
	#ifdef HAWKER_CFC
	stPinGetPara.EventFunction.OnGetPINOtherKeys = OnGetPINOtherKeys;
	#endif
	
    CTOS_KBDBufFlush();//cleare key buffer

	#ifdef HAWKER_CFC
	ret = CTOS_KMS2PINGet((CTOS_KMS2PINGET_PARA_VERSION_4 *)&stPinGetPara);
    #else
	ret = CTOS_KMS2PINGet((CTOS_KMS2PINGET_PARA *)&stPinGetPara);
    #endif
	
    vdDebug_LogPrintf("**Online PIN[%d]*PINBlock[%s]*",ret,stPinGetPara.PINOutput.pEncryptedBlock);
    
    DebugAddHEX("szPINBlock=",stPinGetPara.PINOutput.pEncryptedBlock,8);
    
    if(ret != d_OK)
    {
        if(ret == d_KMS2_GET_PIN_NULL_PIN)
        {
            CTOS_LCDTPrintXY(1, 8, "PIN BY PASSED");
            CTOS_Delay(300);
            memset(card_holder_pin,0,sizeof(card_holder_pin));
            return d_KMS2_GET_PIN_NULL_PIN;
        }
        else
        {
            sprintf(str, "%s=%04X", strDCT.szDisplayProcessing, ret);
            vdDisplayErrorMsg(1, 8, str);
            return ret;
        }
    }
    if(stPinGetPara.PINOutput.EncryptedBlockLength != 8)
    {
        memset(card_holder_pin,0,sizeof(card_holder_pin));
        
        vdDebug_LogPrintf("card_holder_pin[%s]",card_holder_pin);
    }
    else
    {
        wub_hex_2_str(srTransRec.szPINBlock,card_holder_pin,8);
        vdDebug_LogPrintf("card_holder_pin[%s]",card_holder_pin);
    }
    return d_OK;
}


int inIPPGetMAC(BYTE *szDataIn, int inLengthIn, BYTE *szInitialVector, BYTE *szMAC)
{
	CTOS_KMS2MAC_PARA para;
	USHORT ret;
	BYTE key,str[40];
	
	CTOS_LCDTClearDisplay();
	DebugAddSTR("inGetIPPMAC","Processing...       ",20);    
	
	memset(&para, 0x00, sizeof(CTOS_KMS2MAC_PARA));
	para.Version = 0x01;
	para.Protection.CipherKeySet = strDCT.usKeySet;
	para.Protection.CipherKeyIndex = strDCT.usKeyIndex;
	para.Protection.CipherMethod = KMS2_MACMETHOD_CBC;
	para.Protection.SK_Length = IPP_TDES_KEY_SIZE;
	para.Protection.pSK = strDCT.szMACKey;
	para.ICV.Length = 8;
	para.ICV.pData = szInitialVector;
	para.Input.Length = inLengthIn;
	para.Input.pData = szDataIn;
	para.Output.pData = szMAC;
	
	ret = CTOS_KMS2MAC(&para);
	if(ret != d_OK)
		return ret;
	return d_OK;		
}

int inCalculateMAC(BYTE *szDataIn, int inLengthIn, BYTE *szMAC)
{
	BYTE szInitialVector[8];

	inIPPGetMAC(szDataIn, inLengthIn,  szInitialVector, szMAC);
}
	

/*   
int inOldGetIPPPin1(void)
{
	CTOS_KMS2PINGET_PARA para;
	BYTE str[17],key;
	USHORT ret;
	
	DebugAddSTR("inGetIPPPin","Processing...",20);
	inDCTRead(srTransRec.HDTid);
	CTOS_LCDTClearDisplay();
	vdDispTransTitle(srTransRec.byTransType);
	
	inCTOS_DisplayCurrencyAmount(srTransRec.szTotalAmount, 3);
	setLCDPrint(4, DISPLAY_POSITION_LEFT, strDCT.szDisplayLine1);
	setLCDPrint(5, DISPLAY_POSITION_LEFT, strDCT.szDisplayLine2);

	memset(&para, 0x00, sizeof(CTOS_KMS2PINGET_PARA));
	para.Version = 0x01;
	para.PIN_Info.BlockType = KMS2_PINBLOCKTYPE_ANSI_X9_8_ISO_0;
	para.PIN_Info.PINDigitMinLength = strDCT.inMinPINDigit;
	para.PIN_Info.PINDigitMaxLength = strDCT.inMaxPINDigit;
	para.Protection.CipherKeySet = strDCT.usKeySet;
	para.Protection.CipherKeyIndex = strDCT.usKeyIndex;
	para.Protection.CipherMethod = KMS2_PINCIPHERMETHOD_ECB;
	para.Protection.SK_Length = IPP_TDES_KEY_SIZE;
	para.Protection.pSK = strDCT.szPINKey;
	para.AdditionalData.InLength = strlen(srTransRec.szPAN);
	para.AdditionalData.pInData = (BYTE*)srTransRec.szPAN;
	para.PINOutput.EncryptedBlockLength = 8;
	para.PINOutput.pEncryptedBlock = srTransRec.szPINBlock;
	para.Control.Timeout = 0;
	para.Control.AsteriskPositionX = 8;
	para.Control.AsteriskPositionY = 7;
	para.Control.NULLPIN = FALSE;
	para.Control.piTestCancel = NULL;
	
	ret = CTOS_KMS2PINGet(&para);
	if(ret != d_OK)
	{
		sprintf(str, "%s=%04X", strDCT.szDisplayProcessing, ret);
		CTOS_LCDTPrintXY(1, 8, str);
		CTOS_KBDGet(&key);
		return ret;
	}
	return d_OK;
}
*/

void vdCTOS_PinEntryPleaseWaitDisplay(void){

/*************************************************************/
CTOS_LCDTClearDisplay();
vdDispTransTitle(srTransRec.byTransType);
setLCDPrint27(8,DISPLAY_POSITION_LEFT, "Please Wait...");

CTOS_KBDBufFlush(); // sidumili: clear buffer of keyboard
/*************************************************************/	

}

int inCheckKeys(USHORT ushKeySet, USHORT ushKeyIndex)
{
    USHORT rtn;
    
    vdDebug_LogPrintf("SET[%04X] IDX[%04X]", ushKeySet, ushKeyIndex);
    
    rtn = CTOS_KMS2KeyCheck(ushKeySet, ushKeyIndex);
    if (rtn != d_OK)
        vdDisplayErrorMsg(1, 8, "PLEASE INJECT KEY");
    
    return rtn;
}

int GetPIN_With_3DESDUKPT(void)
{
	#ifdef HAWKER_CFC
	CTOS_KMS2PINGET_PARA_VERSION_4 stPinGetPara; // no matter CFC, we better move to new version
	#else
	CTOS_KMS2PINGET_PARA stPinGetPara;
	#endif
	USHORT ret;
	BYTE str[17];
	BYTE key;
	BYTE PINBlock[16];
	BYTE *pCipherKey;
	BYTE CipherKeyLength;
	BYTE DecipherPINBlock[16];
	BYTE ExpectPINBlock[16];
	BYTE ksn[20];
	BYTE ksn_Len =  10;
	int inRet;
	BYTE Tkey = 16;
	BYTE baAmount[20];
	BYTE	szTotalAmt[12+1];
    BYTE    szStr[45];
	BYTE temp[64];
    DWORD dwWait=0, dwWakeup=0;
	
	if  ((srTransRec.byTransType == SALE ) 
		|| (srTransRec.byTransType == PRE_AUTH ))
//		|| (srTransRec.byTransType == REFUND ))
	{
	}
	else
	{
		return d_OK;
	}
		
	DebugAddSTR("GetPIN_With_3DESDUKPT","Processing...",20);
	CTOS_KMS2Init();
	ginPinByPass = 0;

//	inDCTRead(srTransRec.HDTid);
	inDCTRead(6);

	vdDebug_LogPrintf("inDCTRead IN [%ld] [%ld] [%ld]", srTransRec.HDTid, strDCT.usKeyIndex, strDCT.usKeySet);

// patrick test key 20150706 start
	strDCT.usKeySet = NPX_DUKPT_KEYSET;//0xC000;
	strDCT.usKeyIndex = NPX_DUKPT_KEYINDEX;//0x0004;
	strDCT.inMinPINDigit = 4;
	strDCT.inMaxPINDigit = 12;

    inRet = inCheckKeys(strDCT.usKeySet, strDCT.usKeyIndex);
    if (inRet != d_OK)
        return(inRet);

	CTOS_LCDTClearDisplay();
	CTOS_LCDSelectModeEx(d_LCD_TEXT_320x240_MODE, FALSE);

	if (strTCT.byPinPadMode != 1)
	{
		#ifdef HAWKER_CFC
		if (strTCTEX.fHawkerMode == 1 && (strTCTEX.inCFCRetailMode == 0 || strTCTEX.inCFCRetailMode == 1 || strTCTEX.inCFCRetailMode == 3))
		{
			vdDisplayPINEntryScreen();
			
			Tkey = (sizeof(pCoordinateDoubleButton) / sizeof(CTOS_VIRTUAL_FUNCTION_KEY_SET_PARA))%0x100;
			CTOS_VirtualFunctionKeySet(pCoordinateDoubleButton,Tkey);
			vdSetTouchTimer(CTOS_TickGet());
		}
		else
		{
			vdDispTransTitle(srTransRec.byTransType);
		//	    memset(card_holder_pin,0,sizeof(card_holder_pin));
	    
	    	inCTOS_DisplayCurrencyAmount(srTransRec.szTotalAmount, 3);
			vdDebug_LogPrintf("szDisplayLine1 [%s] szDisplayLine2[%s]", strDCT.szDisplayLine1, strDCT.szDisplayLine2);
			strcpy(strDCT.szDisplayLine1, "PLEASE ENTER");
			strcpy(strDCT.szDisplayLine2, "ONLINE PIN");
	    	setLCDPrint(4, DISPLAY_POSITION_LEFT, strDCT.szDisplayLine1);
	    	setLCDPrint(5, DISPLAY_POSITION_LEFT, strDCT.szDisplayLine2);
		}
		#else
	    vdDispTransTitle(srTransRec.byTransType);
//	    memset(card_holder_pin,0,sizeof(card_holder_pin));
	    
	    inCTOS_DisplayCurrencyAmount(srTransRec.szTotalAmount, 3);
		vdDebug_LogPrintf("szDisplayLine1 [%s] szDisplayLine2[%s]", strDCT.szDisplayLine1, strDCT.szDisplayLine2);
		strcpy(strDCT.szDisplayLine1, "PLEASE ENTER");
		strcpy(strDCT.szDisplayLine2, "ONLINE PIN");
	    setLCDPrint(4, DISPLAY_POSITION_LEFT, strDCT.szDisplayLine1);
	    setLCDPrint(5, DISPLAY_POSITION_LEFT, strDCT.szDisplayLine2);
		#endif
	}
	else
	{
		//displaybmpEx(0, 0, "Enterpin.bmp");
		vdDispTitleString(stOLPinParam.szTransTitle);
		CTOS_LCDTPrintAligned(3, "Amount:", d_LCD_ALIGNLEFT);
		CTOS_LCDTPrintAligned(4, stOLPinParam.szFmtAmtStr, d_LCD_ALIGNLEFT);
		CTOS_LCDTPrintAligned(6, "INPUT PIN", d_LCD_ALIGNLEFT);
		CTOS_LCDTPrintAligned(7, "or PRESS ENTER", d_LCD_ALIGNLEFT);
	}
	//CTOS_Delay(1000);
	CTOS_Delay(20);

	#ifdef HAWKER_CFC
	memset(&stPinGetPara, 0x00, sizeof(CTOS_KMS2PINGET_PARA_VERSION_4));
	stPinGetPara.Version = 0x04;
	#else
	memset(&stPinGetPara, 0x00, sizeof(CTOS_KMS2PINGET_PARA));
	stPinGetPara.Version = 0x01;
	#endif
	stPinGetPara.PIN_Info.BlockType = KMS2_PINBLOCKTYPE_ANSI_X9_8_ISO_0;
	stPinGetPara.PIN_Info.PINDigitMinLength = strDCT.inMinPINDigit;
	stPinGetPara.PIN_Info.PINDigitMaxLength = strDCT.inMaxPINDigit;
	stPinGetPara.Protection.CipherKeyIndex = strDCT.usKeyIndex;
	stPinGetPara.Protection.CipherKeySet = strDCT.usKeySet;
	
	stPinGetPara.Protection.CipherMethod = KMS2_PINCIPHERMETHOD_ECB;
	stPinGetPara.Protection.SK_Length = 0;
	
	stPinGetPara.AdditionalData.InLength = strlen(srTransRec.szPAN);
	stPinGetPara.AdditionalData.pInData = (BYTE*)srTransRec.szPAN;
	stPinGetPara.DUKPT_PARA.IsUseCurrentKey = FALSE;

    ret = CTOS_KMS2DUKPTGetKSN(stPinGetPara.Protection.CipherKeySet, stPinGetPara.Protection.CipherKeyIndex, ksn, &ksn_Len);
    vdDebug_LogPrintf("CTOS_KMS2DUKPTGetKSN[%d]", ret);
    DebugAddHEX("NOT RESET KSN YET", ksn, 10);
	//memset(temp, 0x00, sizeof(temp));
	//wub_hex_2_str(ksn, temp, 10);
	//vdDisplayErrorMsg(1, 2, temp);

	stPinGetPara.PINOutput.EncryptedBlockLength = 8;
	stPinGetPara.PINOutput.pEncryptedBlock = srTransRec.szPINBlock;//PINBlock; -- fix for issue DE 52 has no value
	#ifdef HAWKER_CFC
	stPinGetPara.Control.Timeout = 30;
	stPinGetPara.EventFunction.OnGetPINBackspace = OnGetPINBackspace;
	stPinGetPara.EventFunction.OnGetPINCancel = OnGetPINCancel;
	stPinGetPara.EventFunction.OnGetPINDigit = OnGetPINDigit;
	stPinGetPara.EventFunction.OnGetPINOtherKeys = OnGetPINOtherKeys;
	#else
	stPinGetPara.Control.Timeout = 20;
	stPinGetPara.Control.AsteriskPositionX = 2;
	stPinGetPara.Control.AsteriskPositionY = 7;
	#endif
	stPinGetPara.Control.NULLPIN = TRUE;
	stPinGetPara.Control.piTestCancel = NULL;
	
    ksn_Len = 10;
	#ifdef HAWKER_CFC
	if (strTCTEX.fHawkerMode == 1 && (strTCTEX.inCFCRetailMode == 0 || strTCTEX.inCFCRetailMode == 1 || strTCTEX.inCFCRetailMode == 3))
	{
		CTOS_KBDBufFlush();//cleare key buffer
	}
	else
		CTOS_KBDBufFlush();//cleare key buffer	
	#else
    CTOS_KBDBufFlush();//cleare key buffer	
	#endif

    memcpy(srTransRec.szKSN, ksn, 10);

	#ifdef HAWKER_CFC
	if (strTCTEX.fHawkerMode == 1 && (strTCTEX.inCFCRetailMode == 0 || strTCTEX.inCFCRetailMode == 1 || strTCTEX.inCFCRetailMode == 3)
		|| d_OK == inUseTouchGUI())
    	ret = CTOS_KMS2PINGet((CTOS_KMS2PINGET_PARA_VERSION_4 *)&stPinGetPara);
	else
	{
		CTOS_LCDSelectModeEx(d_LCD_TEXT_320x240_MODE, FALSE);
		ret = CTOS_KMS2PINGet((CTOS_KMS2PINGET_PARA_VERSION_4 *)&stPinGetPara); // still use V4
	}
	#else
	CTOS_LCDSelectModeEx(d_LCD_TEXT_320x240_MODE, FALSE);
    ret = CTOS_KMS2PINGet((CTOS_KMS2PINGET_PARA *)&stPinGetPara);
    #endif
	vdDebug_LogPrintf("ret[%x][%d]", ret, ret);

	
	/*the KSN will increase after get PIN/Encrypt function,
	here for online PIN + EFTSec, use one KSN , that is why after GetPIN we call inCTOSS_ResetDUKPTKSN()
	the KSN not match will happen for Terminal + PINpad, for 1Pc only, if call GetKSN and change ksn,
	the function inCTOSS_ResetDUKPTKSN(ksn) will cause problem*/
	if (1 == strTCT.byPinPadMode)
	{
	vdDebug_LogPrintf("CTOS_KMS2DUKPTGetKSN only for PINpad code");
	CTOS_KMS2DUKPTGetKSN(stPinGetPara.Protection.CipherKeySet, stPinGetPara.Protection.CipherKeyIndex, ksn, &ksn_Len);
	vdDebug_LogPrintf("After CTOS_KMS2PINGet CTOS_KMS2DUKPTGetKSN[%d]", ret);
    DebugAddHEX("After CTOS_KMS2PINGet KSN", ksn, 10);
	memcpy(srTransRec.szKSN, ksn, 10);
	}
	
	if(ret != d_OK)
    {
        if(ret == d_KMS2_GET_PIN_NULL_PIN)
        {
        	#ifdef HAWKER_CFC
			if (strTCTEX.fHawkerMode == 1 && (strTCTEX.inCFCRetailMode == 0 || strTCTEX.inCFCRetailMode == 1 || strTCTEX.inCFCRetailMode == 3)
				|| d_OK == inUseTouchGUI())
			{
//				clearLineToLine(PIN_POSITION_Y_CFC, PIN_POSITION_Y_CFC+1);
				CTOS_LCDTPrintAligned(PIN_POSITION_Y_CFC, "PIN BY PASSED", d_LCD_ALIGNCENTER);
			}
			else
			{
				CTOS_LCDTPrintXY(1, 8, "PIN BY PASSED                  ");
			}
			#else
            CTOS_LCDTPrintXY(1, 8, "PIN BY PASSED                  ");
			#endif
            CTOS_Delay(300);
			//memset(srTransRec.szKSN, 0x00, sizeof(srTransRec.szKSN));
			ginPinByPass = 1;
			inCTOSS_ResetDUKPTKSN(ksn);
			return d_KMS2_GET_PIN_NULL_PIN;
        }
        else
        {
            sprintf(str, "%s=%04X", strDCT.szDisplayProcessing, ret);
			vdDisplayErrorMessage(ret);
            return ret;
        }
    }

	inCTOSS_ResetDUKPTKSN(ksn);	
    DebugAddHEX("ResetDUKPTKSN Current KSN", ksn, 10);
	
    if(stPinGetPara.PINOutput.EncryptedBlockLength != 8)
    {        
//        vdDebug_LogPrintf("card_holder_pin[%s]","123");
    }
    else
    {
//        wub_hex_2_str(srTransRec.szPINBlock,card_holder_pin,8);
//        vdDebug_LogPrintf("card_holder_pin[%s]",card_holder_pin);
    }
    return d_OK;
}


int GetPIN_With_3DESDUKPT_V3P(void)
{
	#ifdef HAWKER_CFC
	CTOS_KMS2PINGET_PARA_VERSION_4 stPinGetPara; // no matter CFC, we better move to new version
	#else
	CTOS_KMS2PINGET_PARA stPinGetPara;
	#endif
	USHORT ret;
	BYTE str[17];
	BYTE key;
	BYTE PINBlock[16];
	BYTE *pCipherKey;
	BYTE CipherKeyLength;
	BYTE DecipherPINBlock[16];
	BYTE ExpectPINBlock[16];
	BYTE ksn[20];
	BYTE ksn_Len =  10;
	int inRet;
	BYTE Tkey = 16;
	BYTE baAmount[20];
	BYTE	szTotalAmt[12+1];
    BYTE    szStr[45];
	BYTE temp[64];
    DWORD dwWait=0, dwWakeup=0;

	/*PINpad should not check the trans type*/
	#if 0
	if  ((srTransRec.byTransType == SALE ) 
		|| (srTransRec.byTransType == PRE_AUTH ))
//		|| (srTransRec.byTransType == REFUND ))
	{
	}
	else
	{
		return d_OK;
	}
	#endif
		
	DebugAddSTR("GetPIN_With_3DESDUKPT_V3P","Processing...",20);
	CTOS_KMS2Init();
	ginPinByPass = 0;

	/*for NPX PINpad Online PIN, follow the KLD, we dp re-cipher*/
	if (1 == inUseReCipherDUPKT())
	{
		return GetPIN_With_3DESDUKPT_V3P_ReCipher();
	}

//	inDCTRead(srTransRec.HDTid);
	inDCTRead(6);

	vdDebug_LogPrintf("inDCTRead IN [%ld] [%ld] [%ld]", srTransRec.HDTid, strDCT.usKeyIndex, strDCT.usKeySet);

// patrick test key 20150706 start
	strDCT.usKeySet = NPX_DUKPT_KEYSET;//0xC000;
	strDCT.usKeyIndex = NPX_DUKPT_KEYINDEX;//0x0004;
	strDCT.inMinPINDigit = 4;
	strDCT.inMaxPINDigit = 12;

    inRet = inCheckKeys(strDCT.usKeySet, strDCT.usKeyIndex);
    if (inRet != d_OK)
        return(inRet);

	CTOS_LCDTClearDisplay();
	CTOS_LCDSelectModeEx(d_LCD_TEXT_320x240_MODE, FALSE);

	vdDebug_LogPrintf("strlen(stOLPinParam.szFmtAmtStr)[%d], stOLPinParam.lnTxnAmt[%lu]", strlen(stOLPinParam.szFmtAmtStr), stOLPinParam.lnTxnAmt);
	if (stOLPinParam.lnTxnAmt > 0)
	{
		memset(szStr, 0x00, sizeof(szStr));
		sprintf(szStr, "%lu", stOLPinParam.lnTxnAmt);
		pad(szStr, szStr, '0', 12, RIGHT);
		vdDebug_LogPrintf("pad szStr[%s]", szStr);	
		wub_str_2_hex(szStr, srTransRec.szTotalAmount, 12);
		vdDebug_LogPrintf("stOLPinParam.szFmtAmtStr[%s]", stOLPinParam.szFmtAmtStr);	
		DebugAddHEX("srTransRec.szTotalAmount from stOLPinParam", srTransRec.szTotalAmount, 12);
	}
	else
	{
		wub_str_2_hex(g_szPPDispAmt, srTransRec.szTotalAmount, 12);
		DebugAddHEX("srTransRec.szTotalAmount from g_szPPDispAmt", srTransRec.szTotalAmount, 12);
		vdDebug_LogPrintf("g_szPPDispAmt [%s]", g_szPPDispAmt);
	}
//inCTOS_DisplayCurrencyAmount(srTransRec.szTotalAmount, 4);

	//if (strTCT.byPinPadMode != 1)
	if (1)// this is PINpad code. so, no need checking. 
	{
		#ifdef HAWKER_CFC
		if (strTCTEX.fHawkerMode == 1 && (strTCTEX.inCFCRetailMode == 0 || strTCTEX.inCFCRetailMode == 1 || strTCTEX.inCFCRetailMode == 3)
			|| d_OK == inUseTouchGUI())
		{
			vdDisplayPINEntryScreen_pinpad();
			
			Tkey = (sizeof(pCoordinateDoubleButton) / sizeof(CTOS_VIRTUAL_FUNCTION_KEY_SET_PARA))%0x100;
			CTOS_VirtualFunctionKeySet(pCoordinateDoubleButton,Tkey);

			vdSetTouchTimer(CTOS_TickGet());
		}
		else
		{
			vdDispTransTitle(srTransRec.byTransType);
		//	    memset(card_holder_pin,0,sizeof(card_holder_pin));
	    
	    	inCTOS_DisplayCurrencyAmount(srTransRec.szTotalAmount, 3);
			vdDebug_LogPrintf("szDisplayLine1 [%s] szDisplayLine2[%s]", strDCT.szDisplayLine1, strDCT.szDisplayLine2);
			strcpy(strDCT.szDisplayLine1, "PLEASE ENTER");
			strcpy(strDCT.szDisplayLine2, "ONLINE PIN");
	    	setLCDPrint(4, DISPLAY_POSITION_LEFT, strDCT.szDisplayLine1);
	    	setLCDPrint(5, DISPLAY_POSITION_LEFT, strDCT.szDisplayLine2);
		}
		#else
	    vdDispTransTitle(srTransRec.byTransType);
//	    memset(card_holder_pin,0,sizeof(card_holder_pin));
	    
	    inCTOS_DisplayCurrencyAmount(srTransRec.szTotalAmount, 3);
		vdDebug_LogPrintf("szDisplayLine1 [%s] szDisplayLine2[%s]", strDCT.szDisplayLine1, strDCT.szDisplayLine2);
		strcpy(strDCT.szDisplayLine1, "PLEASE ENTER");
		strcpy(strDCT.szDisplayLine2, "ONLINE PIN");
	    setLCDPrint(4, DISPLAY_POSITION_LEFT, strDCT.szDisplayLine1);
	    setLCDPrint(5, DISPLAY_POSITION_LEFT, strDCT.szDisplayLine2);
		#endif
	}
	else
	{
		//displaybmpEx(0, 0, "Enterpin.bmp");
		vdDispTitleString(stOLPinParam.szTransTitle);
		CTOS_LCDTPrintAligned(3, "Amount:", d_LCD_ALIGNLEFT);
		CTOS_LCDTPrintAligned(4, stOLPinParam.szFmtAmtStr, d_LCD_ALIGNLEFT);
		CTOS_LCDTPrintAligned(6, "INPUT PIN", d_LCD_ALIGNLEFT);
		CTOS_LCDTPrintAligned(7, "or PRESS ENTER", d_LCD_ALIGNLEFT);
	}
	//CTOS_Delay(1000);
	CTOS_Delay(20);

	#ifdef HAWKER_CFC
	memset(&stPinGetPara, 0x00, sizeof(CTOS_KMS2PINGET_PARA_VERSION_4));
	stPinGetPara.Version = 0x04;
	#else
	memset(&stPinGetPara, 0x00, sizeof(CTOS_KMS2PINGET_PARA));
	stPinGetPara.Version = 0x01;
	#endif
	stPinGetPara.PIN_Info.BlockType = KMS2_PINBLOCKTYPE_ANSI_X9_8_ISO_0;
	stPinGetPara.PIN_Info.PINDigitMinLength = strDCT.inMinPINDigit;
	stPinGetPara.PIN_Info.PINDigitMaxLength = strDCT.inMaxPINDigit;
	stPinGetPara.Protection.CipherKeyIndex = strDCT.usKeyIndex;
	stPinGetPara.Protection.CipherKeySet = strDCT.usKeySet;
	
	stPinGetPara.Protection.CipherMethod = KMS2_PINCIPHERMETHOD_ECB;
	stPinGetPara.Protection.SK_Length = 0;
	
	stPinGetPara.AdditionalData.InLength = strlen(g_szPAN);
	stPinGetPara.AdditionalData.pInData = (BYTE*)g_szPAN;
	stPinGetPara.DUKPT_PARA.IsUseCurrentKey = FALSE;
	vdDebug_LogPrintf("g_szPAN[%s]", g_szPAN);

    ret = CTOS_KMS2DUKPTGetKSN(stPinGetPara.Protection.CipherKeySet, stPinGetPara.Protection.CipherKeyIndex, ksn, &ksn_Len);
    vdDebug_LogPrintf("CTOS_KMS2DUKPTGetKSN[%d]", ret);
    DebugAddHEX("NOT RESET KSN YET", ksn, 10);
	//memset(temp, 0x00, sizeof(temp));
	//wub_hex_2_str(ksn, temp, 10);
	//vdDisplayErrorMsg(1, 2, temp);

	stPinGetPara.PINOutput.EncryptedBlockLength = 8;
	stPinGetPara.PINOutput.pEncryptedBlock = srTransRec.szPINBlock;//PINBlock; -- fix for issue DE 52 has no value
	#ifdef HAWKER_CFC
	stPinGetPara.Control.Timeout = 30;
	stPinGetPara.EventFunction.OnGetPINBackspace = OnGetPINBackspace;
	stPinGetPara.EventFunction.OnGetPINCancel = OnGetPINCancel;
	stPinGetPara.EventFunction.OnGetPINDigit = OnGetPINDigit;
	stPinGetPara.EventFunction.OnGetPINOtherKeys = OnGetPINOtherKeys;
	#else
	stPinGetPara.Control.Timeout = 20;
	stPinGetPara.Control.AsteriskPositionX = 2;
	stPinGetPara.Control.AsteriskPositionY = 7;
	#endif
	stPinGetPara.Control.NULLPIN = TRUE;
	stPinGetPara.Control.piTestCancel = NULL;
	
    ksn_Len = 10;
	#ifdef HAWKER_CFC
	if (strTCTEX.fHawkerMode == 1 && (strTCTEX.inCFCRetailMode == 0 || strTCTEX.inCFCRetailMode == 1 || strTCTEX.inCFCRetailMode == 3))
	{
		CTOS_KBDBufFlush();//cleare key buffer
	}
	else
		CTOS_KBDBufFlush();//cleare key buffer	
	#else
    CTOS_KBDBufFlush();//cleare key buffer	
	#endif

    memcpy(srTransRec.szKSN, ksn, 10);

	#ifdef HAWKER_CFC
	if (strTCTEX.fHawkerMode == 1 && (strTCTEX.inCFCRetailMode == 0 || strTCTEX.inCFCRetailMode == 1 || strTCTEX.inCFCRetailMode == 3)
		|| d_OK == inUseTouchGUI())
    	ret = CTOS_KMS2PINGet((CTOS_KMS2PINGET_PARA_VERSION_4 *)&stPinGetPara);
	else
	{
		CTOS_LCDSelectModeEx(d_LCD_TEXT_320x240_MODE, FALSE);
		ret = CTOS_KMS2PINGet((CTOS_KMS2PINGET_PARA_VERSION_4 *)&stPinGetPara); // still use V4
	}
	#else
	CTOS_LCDSelectModeEx(d_LCD_TEXT_320x240_MODE, FALSE);
    ret = CTOS_KMS2PINGet((CTOS_KMS2PINGET_PARA *)&stPinGetPara);
    #endif
	
	vdDebug_LogPrintf("ret[%4X][%d] d_KMS2_GET_PIN_NULL_PIN[%04X][%d]", ret, ret, d_KMS2_GET_PIN_NULL_PIN, d_KMS2_GET_PIN_NULL_PIN);

	/*the KSN will increase after get PIN/Encrypt function,
	here for online PIN + EFTSec, use one KSN , that is why after GetPIN we call inCTOSS_ResetDUKPTKSN()
	the KSN not match will happen for Terminal + PINpad, for 1Pc only, if call GetKSN and change ksn,
	the function inCTOSS_ResetDUKPTKSN(ksn) will cause problem*/
	#if 0
	CTOS_KMS2DUKPTGetKSN(stPinGetPara.Protection.CipherKeySet, stPinGetPara.Protection.CipherKeyIndex, ksn, &ksn_Len);
	//vdDebug_LogPrintf("After CTOS_KMS2PINGet CTOS_KMS2DUKPTGetKSN");
    DebugAddHEX("After CTOS_KMS2PINGet KSN", ksn, 10);
	memcpy(srTransRec.szKSN, ksn, 10);
	#endif
	
	if(ret != d_OK)
    {
        if(ret == d_KMS2_GET_PIN_NULL_PIN)
        {
        	#ifdef HAWKER_CFC
			if (strTCTEX.fHawkerMode == 1 && (strTCTEX.inCFCRetailMode == 0 || strTCTEX.inCFCRetailMode == 1 || strTCTEX.inCFCRetailMode == 3)
				|| d_OK == inUseTouchGUI())
			{
//				clearLineToLine(PIN_POSITION_Y_CFC, PIN_POSITION_Y_CFC+1);
				CTOS_LCDTPrintAligned(PIN_POSITION_Y_CFC, "PIN BY PASSED", d_LCD_ALIGNCENTER);
			}
			else
			{
				CTOS_LCDTPrintXY(1, 8, "PIN BY PASSED                  ");
			}
			#else
            CTOS_LCDTPrintXY(1, 8, "PIN BY PASSED                  ");
			#endif
            CTOS_Delay(300);
			//memset(srTransRec.szKSN, 0x00, sizeof(srTransRec.szKSN));
			ginPinByPass = 1;
			inCTOSS_ResetDUKPTKSN(ksn);
			return d_KMS2_GET_PIN_NULL_PIN;
        }
        else
        {
            sprintf(str, "%s=%04X", strDCT.szDisplayProcessing, ret);	
			vdDisplayErrorMessage(ret);
				
            return ret;
        }
    }

	inCTOSS_ResetDUKPTKSN(ksn);
    DebugAddHEX("ResetDUKPTKSN Current KSN", ksn, 10);
	#if 0
	memset(szStr, 0x00, sizeof(szStr));
	sprintf(szStr, "PIN BLK[%02X%02X%02X%02X]", srTransRec.szPINBlock[0], srTransRec.szPINBlock[1], 
		srTransRec.szPINBlock[3], srTransRec.szPINBlock[4]);
	vdDisplayErrorMsg(1, 14, szStr);
	#endif
	
    if(stPinGetPara.PINOutput.EncryptedBlockLength != 8)
    {        
//        vdDebug_LogPrintf("card_holder_pin[%s]","123");
    }
    else
    {
//        wub_hex_2_str(srTransRec.szPINBlock,card_holder_pin,8);
//        vdDebug_LogPrintf("card_holder_pin[%s]",card_holder_pin);
    }
    return d_OK;
}



/*Sync the PIN entry from terminal to PinPad ---- for Non EMV trans*/
int inCTOSS_REQ_OnlinePIN_OnEPP(void)
{
	unsigned char szV3PSendBuf[1024+1];
	unsigned char szRecvBuf[1024+1];
	int inRecvlen = 0;
	int inOffSet = 0;
	int status = 0;
	unsigned char *pszPtr = NULL;

	BYTE baKSN[10];
	BYTE ksn_len = 10;
	USHORT ushRet = 0;

	BYTE baTmlKSN[10];
	BYTE baExtPPKSN[10];
	ULONG ulRange = 0;
	
	BYTE byAcctType = 0x00;

	USHORT ushReCipherLen = 0;

	if (1 == inUseReCipherDUPKT())
	{
		return inCTOSS_REQ_OnlinePIN_OnEPP_ReCipher();
	}
	
	if(stOLPinParam.byPinPadMode == 0)
	{
		if(stOLPinParam.byPinPadType == 3)
		{
			vdDebug_LogPrintf("inCTOSS_REQ_OnlinePIN_OnEPP");

			DebugAddHEX("INIT PINBLK", stOLPinParam.szPINBlock, 8);

			/*Better we can flush the keyboard to accpet cancel key*/
			CTOS_KBDBufFlush();
			
			vdCTOSS_SetV3PRS232Port(stOLPinParam.byPinPadPort);
			inCTOSS_V3PRS232Open(strTCT.inPPBaudRate, 'N', 8, 1);
			CTOS_Delay(50); // NETS External PIN Pad short delay make it more smooth
			// send STX INJECT_KEY ETX LRC
			memset(szV3PSendBuf, 0x00, sizeof(szV3PSendBuf));
			inOffSet = 0;
			szV3PSendBuf[inOffSet] = STX;
			inOffSet += 1;
			memcpy(&szV3PSendBuf[inOffSet], "NPX_REQ_PIN", strlen("NPX_REQ_PIN"));
			inOffSet += strlen("NPX_REQ_PIN");
			szV3PSendBuf[inOffSet] = '|';
			inOffSet += 1;
			memcpy(&szV3PSendBuf[inOffSet], &stOLPinParam, sizeof(stOLPinParam));
			inOffSet += sizeof(stOLPinParam);
			szV3PSendBuf[inOffSet] = ETX;
			inOffSet += 1;
		    szV3PSendBuf[inOffSet] = (char) wub_lrc(&(szV3PSendBuf[1]), inOffSet-1);
			inOffSet += 1;
			inCTOSS_V3PRS232SendBuf(szV3PSendBuf, inOffSet);			
			//CTOS_Delay(get_env_int("TO"));
			// patrick add code 20150727 start
			//inCTOSS_USBHostFlushEx();
			CTOS_Delay(50); // NETS External PIN Pad short delay make it more smooth

			memset(szRecvBuf,0x00,sizeof(szRecvBuf));
			status = inCTOSS_V3PRS232RecvBuf(szRecvBuf, &inRecvlen, (d_NETSINPUT_TIMEOUT/100));
			/*close port after receive*/
			inCTOSS_V3PRS232Close();
			//inPrintISOPacket(VS_FALSE, szRecvBuf, inRecvlen);
            DebugAddHEX("inCTOSS_V3PRS232RecvBuf", szRecvBuf, inRecvlen);
			if (status == d_NO)
				return 0xDDDD;				
			if (szRecvBuf[0]==0x15)
				return 0xDDDD;
			if (status != d_OK)
				return d_NO;
			pszPtr = (char*)memchr(szRecvBuf, '|', inRecvlen); // check STX
			if (NULL == pszPtr)
			{
				//sometime the response error, may can not get |, 
				//pszPtr will be NULL, copy will cause crash
				return d_NO;
			}
			memset(&stOLPinParam, 0x00, sizeof(stOLPinParam));
			memcpy(&stOLPinParam, &pszPtr[1], sizeof(stOLPinParam));

			vdDebug_LogPrintf("stOLPinParam.inPINEntered[%d], stOLPinParam.inSupportPINBypass[%d]", stOLPinParam.inPINEntered, stOLPinParam.inSupportPINBypass);
			if (stOLPinParam.inPINEntered == 0)	//pin error
				return d_NO;
			if (stOLPinParam.inPINEntered == 1 && stOLPinParam.inSupportPINBypass == FALSE)	//pin bypass but bypass not supported
				return d_NO;

			DebugAddHEX("OUT PINBLK", stOLPinParam.szPINBlock, 8);
			///////////////////////////////////////////////////////////////////////////

			vdDebug_LogPrintf("stOLPinParam.inPINEntered[%d], stOLPinParam.inSupportPINBypass[%d]", stOLPinParam.inPINEntered, stOLPinParam.inSupportPINBypass);
			
			memcpy(srTransRec.szPINBlock, stOLPinParam.szPINBlock, 8);
			srTransRec.byPINEntryCapability = stOLPinParam.inPINEntered;

			// remark reset to fix [posid] missing error message returned by host
			//inCTOSS_ResetDUKPTKSN(stOLPinParam.baKSN);
    		//DebugAddHEX("ResetDUKPTKSN Current KSN", stOLPinParam.baKSN, 10);

			ushRet = CTOS_KMS2DUKPTGetKSN(NPX_DUKPT_KEYSET, NPX_DUKPT_KEYINDEX, baKSN, &ksn_len);
    		vdDebug_LogPrintf("CTOS_KMS2DUKPTGetKSN[%d]", ushRet);
    		DebugAddHEX("After Ext PINpad GetPIN", baKSN, 10);
			//memcpy(srTransRec.szKSN, baKSN, 10);

			/*Compare KSN with Ext PINpad*/
			memset(baTmlKSN, 0x00, sizeof(baTmlKSN));
			memset(baExtPPKSN, 0x00, sizeof(baExtPPKSN));
			memcpy(baTmlKSN, baKSN, 10);
			memcpy(baExtPPKSN, stOLPinParam.baKSN, 10);
			DebugAddHEX("Tml KSN:", baTmlKSN, 10);
			DebugAddHEX("ExtPP KSN:", baExtPPKSN, 10);
			ushRet = CTOS_TOOLCalculateKSNRange(baTmlKSN, baExtPPKSN, &ulRange);
			vdDebug_LogPrintf("Calc KSN Range: ushRet[%d] ulRange[%d]", ushRet, ulRange);
			
			if (d_OK == ushRet && ulRange>0)
			{
				CTOS_KMS2DUKPTIncreaseKSN(NPX_DUKPT_KEYSET, NPX_DUKPT_KEYINDEX, ulRange);
				vdDebug_LogPrintf("TML Inc KSN: ushRet[%d]", ushRet);
			}

			/**/
			vdDebug_LogPrintf("Copy the KSN to srTransRec.szKSN");
			memcpy(srTransRec.szKSN, baExtPPKSN, 10);// this is important for reversal.
			
			if (1 == stOLPinParam.inPINEntered && 
				(0 == memcmp(stOLPinParam.szPINBlock, "\x00\x00\x00\x00\x00\x00\x00\x00", 8)))
			{
				vdDebug_LogPrintf("User PIN Bypass");
				CTOS_Delay(300);
				memset(srTransRec.szKSN, 0x00, sizeof(srTransRec.szKSN));
				ginPinByPass = 1;
            	return d_KMS2_GET_PIN_NULL_PIN;
			}
			else
			{
				/*20200120, here may no need reset KSN for EFT, because already sync with PINpad value*/
				//vdDebug_LogPrintf("User Enter PIN, will Reset DUKPT KSN for EFTSec");
				// un-remark reset to fix [posid] missing error message returned by host
				//inCTOSS_ResetDUKPTKSN(baExtPPKSN);
    			//DebugAddHEX("ResetDUKPTKSN Current KSN(baExtPPKSN)", baExtPPKSN, 10);
			}

			return d_OK;
		}
	}
}


/*check remove card on PinPad*/
int inCheckEMVCardExtPinPad(void)
{	
	unsigned char szV3PSendBuf[1024+1];
	unsigned char szRecvBuf[1024+1];
	int inRecvlen = 0;
	int inOffSet = 0;
	int status = 0;
	unsigned char *pszPtr;
	
	if(strTCT.byPinPadMode == 0)
	{
		if(strTCT.byPinPadType == 3)
		{
			char szV3PSendBuf[1024+1];
			int inOffSet = 0;

			vdDebug_LogPrintf("inCheckEMVCardExtPinPad");
			
			vdCTOSS_SetV3PRS232Port(strTCT.byPinPadPort);
			inCTOSS_V3PRS232Open(strTCT.inPPBaudRate, 'N', 8, 1);

			// send STX PING ETX LRC
			memset(szV3PSendBuf, 0x00, sizeof(szV3PSendBuf));
			inOffSet = 0;
			szV3PSendBuf[inOffSet] = STX;
			inOffSet += 1;
			memcpy(&szV3PSendBuf[inOffSet], "REMOVE_CARD_PP", strlen("REMOVE_CARD_PP"));
			inOffSet += strlen("REMOVE_CARD_PP");
			szV3PSendBuf[inOffSet] = '|';
			inOffSet += 1;
			szV3PSendBuf[inOffSet] = ETX;
			inOffSet += 1;
		    szV3PSendBuf[inOffSet] = (char) wub_lrc(&(szV3PSendBuf[1]), inOffSet-1);
			inOffSet += 1;
			inCTOSS_V3PRS232SendBuf(szV3PSendBuf, inOffSet);
			//CTOS_Delay(get_env_int("TO"));
			CTOS_Delay(50);
			memset(szRecvBuf,0x00,sizeof(szRecvBuf));
			status = inCTOSS_V3PRS232RecvBuf(szRecvBuf, &inRecvlen, 60);
			/*close port after receive*/
			inCTOSS_V3PRS232Close();

			if (status != d_OK)
			{
				//vdDispErrMsg("PinPad Not Ready");
				return d_NO;
			}
			
			pszPtr = (char*)memchr(szRecvBuf, '|', inRecvlen); // check STX
			
			//memcpy(&stOLPinParam, &pszPtr[1], sizeof(stOLPinParam));
			if (0 == memcmp(&pszPtr[1], "Y", 1))
			{
				return d_OK;
			}
			else
			{
				//vdDispErrMsg("Remove Card From PINpad");
				return d_NO;
			}
		}
	}
}

int GetPIN_With_3DESDUKPT_V3P_ReCipher(void)
{
	#ifdef HAWKER_CFC
	CTOS_KMS2PINGET_PARA_VERSION_4 stPinGetPara; // no matter CFC, we better move to new version
	#else
	CTOS_KMS2PINGET_PARA stPinGetPara;
	#endif
	USHORT ret;
	BYTE str[17];
	BYTE key;
	BYTE PINBlock[16];
	BYTE *pCipherKey;
	BYTE CipherKeyLength;
	BYTE DecipherPINBlock[16];
	BYTE ExpectPINBlock[16];
	BYTE ksn[20];
	BYTE ksn_Len =  10;
	int inRet;
	BYTE Tkey = 16;
	BYTE baAmount[20];
	BYTE	szTotalAmt[12+1];
    BYTE    szStr[45];
	BYTE temp[64];
    DWORD dwWait=0, dwWakeup=0;

	/*PINpad should not check the trans type*/
	#if 0
	if  ((srTransRec.byTransType == SALE ) 
		|| (srTransRec.byTransType == PRE_AUTH ))
//		|| (srTransRec.byTransType == REFUND ))
	{
	}
	else
	{
		return d_OK;
	}
	#endif
		
	DebugAddSTR("GetPIN_With_3DESDUKPT_V3P_ReCipher","Processing...",20);
	CTOS_KMS2Init();
	ginPinByPass = 0;

//	inDCTRead(srTransRec.HDTid);
	inDCTRead(6);

	vdDebug_LogPrintf("inDCTRead IN [%ld] [%ld] [%ld]", srTransRec.HDTid, strDCT.usKeyIndex, strDCT.usKeySet);

// patrick test key 20150706 start
	strDCT.usKeySet = 0x0002; //0xC000;
	strDCT.usKeyIndex = 0x0002; //0x0004;
	strDCT.inMinPINDigit = 4;
	strDCT.inMaxPINDigit = 12;

    inRet = inCheckKeys(strDCT.usKeySet, strDCT.usKeyIndex);
    if (inRet != d_OK)
        return(inRet);

	CTOS_LCDTClearDisplay();
	CTOS_LCDSelectModeEx(d_LCD_TEXT_320x240_MODE, FALSE);

	vdDebug_LogPrintf("strlen(stOLPinParam.szFmtAmtStr)[%d], stOLPinParam.lnTxnAmt[%lu]", strlen(stOLPinParam.szFmtAmtStr), stOLPinParam.lnTxnAmt);
	if (stOLPinParam.lnTxnAmt > 0)
	{
		memset(szStr, 0x00, sizeof(szStr));
		sprintf(szStr, "%lu", stOLPinParam.lnTxnAmt);
		pad(szStr, szStr, '0', 12, RIGHT);
		vdDebug_LogPrintf("pad szStr[%s]", szStr);	
		wub_str_2_hex(szStr, srTransRec.szTotalAmount, 12);
		vdDebug_LogPrintf("stOLPinParam.szFmtAmtStr[%s]", stOLPinParam.szFmtAmtStr);	
		DebugAddHEX("srTransRec.szTotalAmount from stOLPinParam", srTransRec.szTotalAmount, 12);
	}
	else
	{
		wub_str_2_hex(g_szPPDispAmt, srTransRec.szTotalAmount, 12);
		DebugAddHEX("srTransRec.szTotalAmount from g_szPPDispAmt", srTransRec.szTotalAmount, 12);
		vdDebug_LogPrintf("g_szPPDispAmt [%s]", g_szPPDispAmt);
	}
//inCTOS_DisplayCurrencyAmount(srTransRec.szTotalAmount, 4);

	//if (strTCT.byPinPadMode != 1)
	if (1)// this is PINpad code. so, no need checking. 
	{
		#ifdef HAWKER_CFC
		if (strTCTEX.fHawkerMode == 1 && (strTCTEX.inCFCRetailMode == 0 || strTCTEX.inCFCRetailMode == 1)
			|| d_OK == inUseTouchGUI())
		{
			vdDisplayPINEntryScreen_pinpad();
			
			Tkey = (sizeof(pCoordinateDoubleButton) / sizeof(CTOS_VIRTUAL_FUNCTION_KEY_SET_PARA))%0x100;
			CTOS_VirtualFunctionKeySet(pCoordinateDoubleButton,Tkey);

			vdSetTouchTimer(CTOS_TickGet());
		}
		else
		{
			vdDispTransTitle(srTransRec.byTransType);
		//	    memset(card_holder_pin,0,sizeof(card_holder_pin));
	    
	    	inCTOS_DisplayCurrencyAmount(srTransRec.szTotalAmount, 3);
			vdDebug_LogPrintf("szDisplayLine1 [%s] szDisplayLine2[%s]", strDCT.szDisplayLine1, strDCT.szDisplayLine2);
			strcpy(strDCT.szDisplayLine1, "PLEASE ENTER");
			strcpy(strDCT.szDisplayLine2, "ONLINE PIN");
	    	setLCDPrint(4, DISPLAY_POSITION_LEFT, strDCT.szDisplayLine1);
	    	setLCDPrint(5, DISPLAY_POSITION_LEFT, strDCT.szDisplayLine2);
		}
		#else
	    vdDispTransTitle(srTransRec.byTransType);
//	    memset(card_holder_pin,0,sizeof(card_holder_pin));
	    
	    inCTOS_DisplayCurrencyAmount(srTransRec.szTotalAmount, 3);
		vdDebug_LogPrintf("szDisplayLine1 [%s] szDisplayLine2[%s]", strDCT.szDisplayLine1, strDCT.szDisplayLine2);
		strcpy(strDCT.szDisplayLine1, "PLEASE ENTER");
		strcpy(strDCT.szDisplayLine2, "ONLINE PIN");
	    setLCDPrint(4, DISPLAY_POSITION_LEFT, strDCT.szDisplayLine1);
	    setLCDPrint(5, DISPLAY_POSITION_LEFT, strDCT.szDisplayLine2);
		#endif
	}
	else
	{
		//displaybmpEx(0, 0, "Enterpin.bmp");
		vdDispTitleString(stOLPinParam.szTransTitle);
		CTOS_LCDTPrintAligned(3, "Amount:", d_LCD_ALIGNLEFT);
		CTOS_LCDTPrintAligned(4, stOLPinParam.szFmtAmtStr, d_LCD_ALIGNLEFT);
		CTOS_LCDTPrintAligned(6, "INPUT PIN", d_LCD_ALIGNLEFT);
		CTOS_LCDTPrintAligned(7, "or PRESS ENTER", d_LCD_ALIGNLEFT);
	}

	CTOS_Delay(20);


	memset(&stPinGetPara, 0x00, sizeof(CTOS_KMS2PINGET_PARA_VERSION_4));
	stPinGetPara.Version = 0x04;

	stPinGetPara.PIN_Info.BlockType = KMS2_PINBLOCKTYPE_ANSI_X9_8_ISO_0;
	stPinGetPara.PIN_Info.PINDigitMinLength = strDCT.inMinPINDigit;
	stPinGetPara.PIN_Info.PINDigitMaxLength = strDCT.inMaxPINDigit;
	stPinGetPara.Protection.CipherKeyIndex = strDCT.usKeyIndex;
	stPinGetPara.Protection.CipherKeySet = strDCT.usKeySet;
	
	stPinGetPara.Protection.CipherMethod = KMS2_PINCIPHERMETHOD_ECB;
	stPinGetPara.Protection.SK_Length = 0;
	
	stPinGetPara.AdditionalData.InLength = strlen(g_szPAN);
	stPinGetPara.AdditionalData.pInData = (BYTE*)g_szPAN;
	vdDebug_LogPrintf("g_szPAN[%s]", g_szPAN);


	stPinGetPara.PINOutput.EncryptedBlockLength = 8;
	stPinGetPara.PINOutput.pEncryptedBlock = srTransRec.szPINBlock;//PINBlock; -- fix for issue DE 52 has no value
	stPinGetPara.Control.Timeout = 30;
	stPinGetPara.EventFunction.OnGetPINBackspace = OnGetPINBackspace;
	stPinGetPara.EventFunction.OnGetPINCancel = OnGetPINCancel;
	stPinGetPara.EventFunction.OnGetPINDigit = OnGetPINDigit;
	stPinGetPara.EventFunction.OnGetPINOtherKeys = OnGetPINOtherKeys;

	stPinGetPara.Control.NULLPIN = TRUE;
	stPinGetPara.Control.piTestCancel = NULL;
	
    ksn_Len = 10;
	#ifdef HAWKER_CFC
	if (strTCTEX.fHawkerMode == 1 && (strTCTEX.inCFCRetailMode == 0 || strTCTEX.inCFCRetailMode == 1))
	{
		CTOS_KBDBufFlush();//cleare key buffer
	}
	else
		CTOS_KBDBufFlush();//cleare key buffer	
	#else
    CTOS_KBDBufFlush();//cleare key buffer	
	#endif

    memcpy(srTransRec.szKSN, ksn, 10);

	//CTOS_LCDSelectModeEx(d_LCD_TEXT_320x240_MODE, FALSE);
    ret = CTOS_KMS2PINGet((CTOS_KMS2PINGET_PARA_VERSION_4 *)&stPinGetPara);
	
	vdDebug_LogPrintf("ret[%4X][%d] d_KMS2_GET_PIN_NULL_PIN[%04X][%d]", ret, ret, d_KMS2_GET_PIN_NULL_PIN, d_KMS2_GET_PIN_NULL_PIN);

	
	if(ret != d_OK)
    {
        if(ret == d_KMS2_GET_PIN_NULL_PIN)
        {
        	#ifdef HAWKER_CFC
			if (strTCTEX.fHawkerMode == 1 && (strTCTEX.inCFCRetailMode == 0 || strTCTEX.inCFCRetailMode == 1)
				|| d_OK == inUseTouchGUI())
			{
//				clearLineToLine(PIN_POSITION_Y_CFC, PIN_POSITION_Y_CFC+1);
				CTOS_LCDTPrintAligned(PIN_POSITION_Y_CFC, "PIN BY PASSED", d_LCD_ALIGNCENTER);
			}
			else
			{
				CTOS_LCDTPrintXY(1, 8, "PIN BY PASSED                  ");
			}
			#else
            CTOS_LCDTPrintXY(1, 8, "PIN BY PASSED                  ");
			#endif
            CTOS_Delay(300);
			ginPinByPass = 1;
			//inCTOSS_ResetDUKPTKSN(ksn);
			return d_KMS2_GET_PIN_NULL_PIN;
        }
        else
        {
            sprintf(str, "%s=%04X", strDCT.szDisplayProcessing, ret);	
			vdDisplayErrorMessage(ret);
				
            return ret;
        }
    }

	//inCTOSS_ResetDUKPTKSN(ksn);
    DebugAddHEX("ResetDUKPTKSN Current KSN", ksn, 10);
	#if 0
	memset(szStr, 0x00, sizeof(szStr));
	sprintf(szStr, "PIN BLK[%02X%02X%02X%02X]", srTransRec.szPINBlock[0], srTransRec.szPINBlock[1], 
		srTransRec.szPINBlock[3], srTransRec.szPINBlock[4]);
	vdDisplayErrorMsg(1, 14, szStr);
	#endif
	
    if(stPinGetPara.PINOutput.EncryptedBlockLength != 8)
    {

    }
    else
    {

    }
    return d_OK;
}


/*Sync the PIN entry from terminal to PinPad ---- for Non EMV trans*/
int inCTOSS_REQ_OnlinePIN_OnEPP_ReCipher(void)
{
	unsigned char szV3PSendBuf[1024+1];
	unsigned char szRecvBuf[1024+1];
	int inRecvlen = 0;
	int inOffSet = 0;
	int status = 0;
	unsigned char *pszPtr = NULL;

	BYTE baKSN[10];
	BYTE ksn_len = 10;
	USHORT ushRet = 0;

	BYTE baTmlKSN[10];
	BYTE baExtPPKSN[10];
	ULONG ulRange = 0;
	
	BYTE byAcctType = 0x00;

	USHORT ushReCipherLen = 0;
	
	if(stOLPinParam.byPinPadMode == 0)
	{
		if(stOLPinParam.byPinPadType == 3)
		{
			vdDebug_LogPrintf("inCTOSS_REQ_OnlinePIN_OnEPP_ReCipher");

			DebugAddHEX("INIT PINBLK", stOLPinParam.szPINBlock, 8);

			/*Better we can flush the keyboard to accpet cancel key*/
			CTOS_KBDBufFlush();
			
			vdCTOSS_SetV3PRS232Port(stOLPinParam.byPinPadPort);
			inCTOSS_V3PRS232Open(strTCT.inPPBaudRate, 'N', 8, 1);
			CTOS_Delay(50); // NETS External PIN Pad short delay make it more smooth
			// send STX INJECT_KEY ETX LRC
			memset(szV3PSendBuf, 0x00, sizeof(szV3PSendBuf));
			inOffSet = 0;
			szV3PSendBuf[inOffSet] = STX;
			inOffSet += 1;
			memcpy(&szV3PSendBuf[inOffSet], "NPX_REQ_PIN", strlen("NPX_REQ_PIN"));
			inOffSet += strlen("NPX_REQ_PIN");
			szV3PSendBuf[inOffSet] = '|';
			inOffSet += 1;
			memcpy(&szV3PSendBuf[inOffSet], &stOLPinParam, sizeof(stOLPinParam));
			inOffSet += sizeof(stOLPinParam);
			szV3PSendBuf[inOffSet] = ETX;
			inOffSet += 1;
		    szV3PSendBuf[inOffSet] = (char) wub_lrc(&(szV3PSendBuf[1]), inOffSet-1);
			inOffSet += 1;
			inCTOSS_V3PRS232SendBuf(szV3PSendBuf, inOffSet);			
			//CTOS_Delay(get_env_int("TO"));
			// patrick add code 20150727 start
			//inCTOSS_USBHostFlushEx();
			CTOS_Delay(50); // NETS External PIN Pad short delay make it more smooth

			memset(szRecvBuf,0x00,sizeof(szRecvBuf));
			status = inCTOSS_V3PRS232RecvBuf(szRecvBuf, &inRecvlen, (d_NETSINPUT_TIMEOUT/100));
			/*close port after receive*/
			inCTOSS_V3PRS232Close();
			//inPrintISOPacket(VS_FALSE, szRecvBuf, inRecvlen);
            DebugAddHEX("inCTOSS_V3PRS232RecvBuf", szRecvBuf, inRecvlen);
			if (status == d_NO)
				return 0xDDDD;				
			if (szRecvBuf[0]==0x15)
				return 0xDDDD;
			if (status != d_OK)
				return d_NO;
			pszPtr = (char*)memchr(szRecvBuf, '|', inRecvlen); // check STX
			if (NULL == pszPtr)
			{
				//sometime the response error, may can not get |, 
				//pszPtr will be NULL, copy will cause crash
				return d_NO;
			}
			memset(&stOLPinParam, 0x00, sizeof(stOLPinParam));
			memcpy(&stOLPinParam, &pszPtr[1], sizeof(stOLPinParam));

			vdDebug_LogPrintf("stOLPinParam.inPINEntered[%d], stOLPinParam.inSupportPINBypass[%d]", stOLPinParam.inPINEntered, stOLPinParam.inSupportPINBypass);
			if (stOLPinParam.inPINEntered == 0)	//pin error
				return d_NO;
			if (stOLPinParam.inPINEntered == 1 && stOLPinParam.inSupportPINBypass == FALSE)	//pin bypass but bypass not supported
				return d_NO;

			DebugAddHEX("OUT PINBLK", stOLPinParam.szPINBlock, 8);
			///////////////////////////////////////////////////////////////////////////

			vdDebug_LogPrintf("stOLPinParam.inPINEntered[%d], stOLPinParam.inSupportPINBypass[%d]", stOLPinParam.inPINEntered, stOLPinParam.inSupportPINBypass);
			
			memcpy(srTransRec.szPINBlock, stOLPinParam.szPINBlock, 8);
			srTransRec.byPINEntryCapability = stOLPinParam.inPINEntered;

			// remark reset to fix [posid] missing error message returned by host
			//inCTOSS_ResetDUKPTKSN(stOLPinParam.baKSN);
    		//DebugAddHEX("ResetDUKPTKSN Current KSN", stOLPinParam.baKSN, 10);

			#if 0
			ushRet = CTOS_KMS2DUKPTGetKSN(0xC000, 0x0004, baKSN, &ksn_len);
    		vdDebug_LogPrintf("CTOS_KMS2DUKPTGetKSN[%d]", ushRet);
    		DebugAddHEX("After Ext PINpad GetPIN", baKSN, 10);
			//memcpy(srTransRec.szKSN, baKSN, 10);

			/*Compare KSN with Ext PINpad*/
			memset(baTmlKSN, 0x00, sizeof(baTmlKSN));
			memset(baExtPPKSN, 0x00, sizeof(baExtPPKSN));
			memcpy(baTmlKSN, baKSN, 10);
			memcpy(baExtPPKSN, stOLPinParam.baKSN, 10);
			DebugAddHEX("Tml KSN:", baTmlKSN, 10);
			DebugAddHEX("ExtPP KSN:", baExtPPKSN, 10);
			ushRet = CTOS_TOOLCalculateKSNRange(baTmlKSN, baExtPPKSN, &ulRange);
			vdDebug_LogPrintf("Calc KSN Range: ushRet[%d] ulRange[%d]", ushRet, ulRange);
			
			if (d_OK == ushRet && ulRange>0)
			{
				CTOS_KMS2DUKPTIncreaseKSN(0xC000, 0x0004, ulRange);
				vdDebug_LogPrintf("TML Inc KSN: ushRet[%d]", ushRet);
			}
			
			/**/
			vdDebug_LogPrintf("Copy the KSN to srTransRec.szKSN");
			memcpy(srTransRec.szKSN, baExtPPKSN, 10);// this is important for reversal.
			#endif
			
			if (1 == stOLPinParam.inPINEntered && 
				(0 == memcmp(stOLPinParam.szPINBlock, "\x00\x00\x00\x00\x00\x00\x00\x00", 8)))
			{
				vdDebug_LogPrintf("User PIN Bypass");
				CTOS_Delay(300);
				memset(srTransRec.szKSN, 0x00, sizeof(srTransRec.szKSN));
				ginPinByPass = 1;
            	return d_KMS2_GET_PIN_NULL_PIN;
			}
			else
			{
				/*20200120, here may no need reset KSN for EFT, because already sync with PINpad value*/
				//vdDebug_LogPrintf("User Enter PIN, will Reset DUKPT KSN for EFTSec");
				// un-remark reset to fix [posid] missing error message returned by host
				//inCTOSS_ResetDUKPTKSN(baExtPPKSN);
    			//DebugAddHEX("ResetDUKPTKSN Current KSN(baExtPPKSN)", baExtPPKSN, 10);
			}
			
			ushPINBlockReCipher(0x0002, 0x0002, stOLPinParam.szPINBlock, 8, NPX_DUKPT_KEYSET, NPX_DUKPT_KEYINDEX, srTransRec.szPINBlock, &ushReCipherLen);
			DebugAddHEX("ReCipher PINBLK", srTransRec.szPINBlock, 8);
			DebugAddHEX("srTransRec.szKSN", srTransRec.szKSN, 10);

			return d_OK;
		}
	}
}


/*check icc card is still inserted in PinPad*/
int inCheckEMVCardRemovedInExtPinPad(void)
{	
	unsigned char szV3PSendBuf[1024+1];
	unsigned char szRecvBuf[1024+1];
	int inRecvlen = 0;
	int inOffSet = 0;
	int status = 0;
	unsigned char *pszPtr;
	
	if(strTCT.byPinPadMode == 0)
	{
		if(strTCT.byPinPadType == 3)
		{
			char szV3PSendBuf[1024+1];
			int inOffSet = 0;
			//Card_Trans strCard_Trans;
			
			//memset(&strCard_Trans, 0x00, sizeof(Card_Trans));

			vdDebug_LogPrintf("inCheckEMVCardRemovedInExtPinPad");
			
			vdCTOSS_SetV3PRS232Port(strTCT.byPinPadPort);
			inCTOSS_V3PRS232Open(strTCT.inPPBaudRate, 'N', 8, 1);

			// send STX PING ETX LRC
			memset(szV3PSendBuf, 0x00, sizeof(szV3PSendBuf));
			inOffSet = 0;
			szV3PSendBuf[inOffSet] = STX;
			inOffSet += 1;
			memcpy(&szV3PSendBuf[inOffSet], "CHECK_ICC_REMOVED", strlen("CHECK_ICC_REMOVED"));
			inOffSet += strlen("CHECK_ICC_REMOVED");
			szV3PSendBuf[inOffSet] = '|';
			inOffSet += 1;

			//strCard_Trans.byTransType = srTransRec.byTransType;
			//vdDebug_LogPrintf("strCard_Trans.byTransType[%d], srTransRec.byTransType[%d]", strCard_Trans.byTransType, srTransRec.byTransType);
			//memcpy(&szV3PSendBuf[inOffSet], &strCard_Trans, sizeof(strCard_Trans));
			//inOffSet += sizeof(strCard_Trans);

			szV3PSendBuf[inOffSet] = ETX;
			inOffSet += 1;
		    szV3PSendBuf[inOffSet] = (char) wub_lrc(&(szV3PSendBuf[1]), inOffSet-1);
			inOffSet += 1;
			inCTOSS_V3PRS232SendBuf(szV3PSendBuf, inOffSet);
			//CTOS_Delay(get_env_int("TO"));
			CTOS_Delay(50);
			memset(szRecvBuf,0x00,sizeof(szRecvBuf));
			vdSetDisableKey(1);//enable press key
			status = inCTOSS_V3PRS232RecvBuf(szRecvBuf, &inRecvlen, 60);
			vdSetDisableKey(0);//enable press key
			/*close port after receive*/
			inCTOSS_V3PRS232Close();

			if (status != d_OK)
			{
				//vdDispErrMsg("PinPad Not Ready");
				return d_NO;
			}
			
			pszPtr = (char*)memchr(szRecvBuf, '|', inRecvlen); // check STX
			
			//memcpy(&stOLPinParam, &pszPtr[1], sizeof(stOLPinParam));
			if (0 == memcmp(&pszPtr[1], "Y", 1))
			{
				vdDebug_LogPrintf("Return Y");
				return d_OK;
			}
			else
			{
				vdDebug_LogPrintf("Return N");
				return d_NO;
			}
		}
	}
}


