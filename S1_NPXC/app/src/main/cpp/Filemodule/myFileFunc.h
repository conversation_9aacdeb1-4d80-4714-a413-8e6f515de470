/* 
 * File:   dymaniclib.h
 * Author: PeyJiun
 *
 * Created on 2010
 */

#ifndef _MYFILEFUNC__H
#define	_MYFILEFUNC__H

#ifdef	__cplusplus
extern "C" {
#endif

#include "../Includes/POSTypedef.h"
#include "../Includes/Trans.h"

typedef enum
{
	CREDIT_HOST,	
}HOST_INDEX_TABLE;
/*==========================================*
 *              File Setting                *	
 *	            D E F I N E S               *
 *==========================================*/           
#define d_BUFF_SIZE 128  //Buffer Size

typedef enum
{
    RC_FILE_READ_OUT_WRONG_SIZE         = -1,
    RC_FILE_REC_OR_RECSIZE_IS_NULL      = -2,	
    RC_FILE_NOT_EXIST                   = -3,
    RC_FILE_EXIST                       = -4,
    RC_FILE_TYPE_WRONG                  = -5,
    RC_FILE_FILE_NOT_FOUND              = -6,
    RC_FILE_DATA_WRONG                  = -7,
    RC_FILE_READ_OUT_NO_DATA            = -8,
}RESPONSE_CODE_TABLE;
 
 /*==================================================*
 * 	HOST Definition Table(CPT) Struct               *
 *==================================================*/
typedef struct
{
	int	HDTid ;
	BYTE	szHostName[50];	/*  */
	int		inCommunicationMode;
	int		inSecCommunicationMode;//for COM&USB communication
	BYTE	szPriTxnPhoneNumber[30];	/* ASCII string */
	BYTE	szSecTxnPhoneNumber[30];	/* ASCII string */		//5
	
	BYTE	szPriSettlePhoneNumber[30];
	BYTE	szSecSettlePhoneNumber[30];	/* ASCII string */
	BOOL	fFastConnect;          /*  */
	BOOL	fDialMode;	/* ASCII string */ 		//10
	
	int	inDialHandShake;	/* ASCII string */
	BYTE	szATCommand[40];
	int		inMCarrierTimeOut;	/* ASCII string */ 		//10         
        
    int   	inMRespTimeOut;
	BOOL	fPreDial;
	BYTE	szPriTxnHostIP[30];
	BYTE	szSecTxnHostIP[30] ;
	BYTE	szPriSettlementHostIP[30];									//15
	
	BYTE	szSecSettlementHostIP[30];
	int	inPriTxnHostPortNum;
	int	inSecTxnHostPortNum;
	int	inPriSettlementHostPort;
	int	inSecSettlementHostPort;									//20
	int	inPriTxnComBaudRate;//for COM&USB communication
	int	inSecTxnComBaudRate;
	
	BOOL	fTCPFallbackDial;
	BOOL	fSSLEnable;
	int	inTCPConnectTimeout;
	int	inTCPResponseTimeout;
	int	inIPHeader;									//25
	int inCountryCode;
	int inHandShake;
	int inParaMode;
	
} STRUCT_CPT;
 
  /*==================================================*
  *  HOST Definition Table(GCPT) Struct 			  *
  *==================================================*/
 typedef struct
 {
	 int HDTid ;
	 BYTE	 szHostName[50]; /*  */
 
	 BYTE	 szPriTxnHostIP[30];
	 BYTE	 szSecTxnHostIP[30] ;
	 BYTE	 szPriSettlementHostIP[30]; 								 //15
	 
	 BYTE	 szSecSettlementHostIP[30];
	 int inPriTxnHostPortNum;
	 int inSecTxnHostPortNum;
	 int inPriSettlementHostPort;
	 int inSecSettlementHostPort;									 //20
 
	 
 } STRUCT_GCPT;
  

/*=======================================*
 *          File Setting Struct			 *
 *=======================================*/

typedef struct
{	
	ULONG   ulHandle;
	int     inSeekCnt;		
	BYTE	bSeekType;
	char	szFileName[d_BUFF_SIZE];	
	BYTE	bStorageType;	
	
	/*be cardful for the parameter
	  if value was 'TRUE', means that File Would be closed immediately after File Opened
	  if value was 'False', user must be Close File by self*/
	BOOL    fCloseFileNow;
	
	ULONG   ulRecSize;
	void    *ptrRec;

}STRUCT_FILE_SETTING;

/*=======================================*
 *          File Setting Func			 *
 *=======================================*/
int inMyFile_RecSave(void *);
int inMyFile_RecRead(void *);

/*-------------------------File Setting End----------------------------*/





/*==========================================*
 *                  TCT                     *                             
 *          (Terminal Config Table)         *	
 *		        D E F I N E S               *
 *==========================================*/  
#define PWD_LEN 	6
#define DEBUG_LOG_OFF   0
#define DEBUG_LOG_ON    1
#define DEBUG_IGNORE    2

/*==========================================*
 *                  TCT Struct              *
 *==========================================*/
typedef struct
{

    BYTE	inMainLine; 	 // Dial, RS486 or TCP/IP
    BOOL	fHandsetPresent; // ????

    BOOL    fDemo;
    BOOL    fCVVEnable;
    	
    BYTE    szSuperPW[PWD_LEN+1];
    BYTE    szSystemPW[PWD_LEN+1];
    BYTE    szEngineerPW[PWD_LEN+1];
	BYTE    szPMpassword[8+1];
	BYTE    szFunKeyPW[8+1];
    BYTE    szBankVerNo[32];    		//30
   
    BYTE    szMyVerDate[32];		
    BYTE    szMyVerName[32];
	BYTE 	inThemesType;
	BYTE 	inFontFNTMode;

	BYTE	byTerminalType;// 1 for V5S, 2 for V3
    BYTE    byRS232DebugPort;//0 not debug, 8= USB debug, 1 =COM1 debug, 2=COM2 debug
    BYTE	byPinPadType;//0 None, 1 for PCI100, 2 for Reserved
    BYTE    byPinPadPort;//0 None, 1 =COM1 , 2=COM2,3= USB , 
	USHORT	inPrinterHeatLevel;
	UINT	inWaitTime;
	BOOL	fTipAllowFlag;
	BOOL	fSignatureFlag;
	BOOL	fCancelSignFlag;
	BYTE    byCtlsMode;
	BYTE    byERMMode;// 2-ERM2, 3=ERM3
	UINT	inERMMaximum;
	BYTE    byERMInit;
	
    BOOL	fDebitFlag;
    BYTE    szFallbackTime[20];
    USHORT  inFallbackTimeGap;
    BOOL    fManualEntryInv ;   
    BYTE	szInvoiceNo[INVOICE_BCD_SIZE+1];
    BYTE	szPabx[4+1];
	BYTE	szLastInvoiceNo[INVOICE_BCD_SIZE+1];
	ULONG	ulLastBCATraceNum;
    BOOL    fECR;
    BOOL    fDebugOpenedFlag;
    BOOL    fShareComEnable;
    USHORT  inReversalCount;
    USHORT  inTipAdjustCount;
	BOOL fPrintISOMessage;

	BOOL    fNSR;
	BOOL    fNSRCustCopy;
	BYTE	szNSRLimit[12+1];
	BYTE	szCTLSLimit[12+1];

    USHORT	usTMSGap;
	USHORT	inTMSComMode;
    USHORT	usTMSRemotePort;
    BYTE    szTMSRemoteIP[30+1];
    BYTE    szTMSRemotePhone[30+1];
	BYTE    szTMSRemoteID[32+1];
    BYTE    szTMSRemotePW[32+1];
    BOOL    fFirstInit; // patrick first Init
    BYTE	byPinPadMode;
    BYTE    byRS232ECRPort;//0 not debug, 8= USB debug, 1 =COM1 debug, 2=COM2 debug     
    UINT	inPPBaudRate;
    BYTE    byQP3000SPort;//0 not debug, 8= USB debug, 1 =COM1 debug, 2=COM2 debug	
    BYTE    byExtReadCard;//0-disable, 1-enable    
	UINT	inPackMode;
    BYTE TaxiMode;
	BOOL	fHFee;
	UINT	HFeePercentage;
	UINT	GSTPercentage;
    BOOL    fAllowVisaCUPAIDSelect;
    BOOL    fAllowMasterCUPAIDSelect;
	UINT	inPrintTipAdjust;
	BOOL    fOfflinePinbypass;
	BOOL    fOffusHostDisable;
	UINT	PreAuthPercentage;
	BOOL    fHotelEnable;//future use
	BOOL    fPrintCardHolderName;
	ULONG	ulIPPMinAmount;
	BOOL    fAuthNoSign;
	BOOL    fVoidNoSign;
	BOOL    fSaleCompNoSign;
	BOOL    fRefundNoSign;
	BOOL    fAdjustNoSign;
	BOOL    fAuthTopupNoSign;
	BOOL    fApprCodeInputNumOnly;
	UINT	inAutoReversalTime;//idle auto reversal timeout, 60 mean 60 seconds
	BOOL    fSendReversalImmediately;
	BYTE	byDLNotFinished;
	
	BOOL    fPrintCustCopy;
	BOOL    fPrintMerchCopy;
	
	BOOL    fNoCTOffline;
	BOOL    fFallbackFlag;
	BOOL    fManualInput;
	BYTE	byTMSParaDL;
} STRUCT_TCT ;

/*-------------------------TCT End----------------------------*/




/*==========================================*
 *                  TCTEX Struct              *
 *==========================================*/
typedef struct
{
    int	inTMSDlConfig;	// 0: TMS download all changes (sw and prm), 1: TMS download prm change only
    int usRestartGap;
  	BYTE szRestartTime[20];
  	BYTE szRestartLastDate[20];
	BOOL    fMDB;
	BOOL    fEXT;
	BOOL	fHawkerMode;			// Cooking Mama Project
	
	// Hawker CFC
	int inRcptHostLogoAcq;	// Receipt host logo bank acquirer. Possible values: 1: NETS, 2: DBS, 3: OCBC, 4: UOB, 9: Multi Acquirer (logo based on host response)
	BOOL fAmexFixLogo;		// Enable / disable Amex logo in Amex transaction receipt regardless of acquirer

	// Retail CFC
	int inCFCRetailMode;	// Possible values: 0: non-CFC (BAU), 1: one-piece, 2: two-piece
	BOOL fRefNum;				// Enable / disable Ref Num - ignore of logon param (logon param no longer used as per ******** Jaime)
	
	BYTE szResettleID[12+1];
	BYTE szLastSettleID[12+1];
	BYTE szLastSettleBatchNo[BATCH_NO_BCD_SIZE+1];

	int inTipMode;			//0: no tip, 1: sale with tip, 2: tip adjust
	BOOL fJCBFixLogo;		// Enable / disable JCB logo in JCB transaction receipt regardless of acquirer

	int inDupRcptMode;		//0: default no printing duplicate receipt, 1: default print duplicate receipt
	int inDupRcptTimer;		// in second, up to 10 seconds

	int inBypassCVV;		//0: disable bypass, 1: enable bypass
} STRUCT_TCTEX ;
/*-------------------------TCTEX End----------------------------*/




/*==========================================*
 *                  CDT                     *
 *          (Card Definition Table)         *
 *              D E F I N E S               *
 *==========================================*/ 
#define	DF_CDT_FILE_NAME_TXT	"CDT.txt"
#define	DF_CDT_FILE_NAME_DAT	"CDT.dat"

/*=======================================*
 * Card Definition Table(CDT) Struct     *
 *=======================================*/
typedef struct
{
    char    szPANLo[19];
	char    szPANHi[19];
	BYTE	szCardLabel[30];
	int  	inType;
	int     inMinPANDigit;
	int     inMaxPANDigit;
	int     inCVV_II;
	long    InFloorLimitAmount;
	BOOL    fExpDtReqd;
	BOOL    fPinpadRequired;
	BOOL    fManEntry;
	BOOL    fCardPresent;
	BOOL    fChkServiceCode;	
	BOOL	fluhnCheck ;//luhn Check
	BOOL	fCDTEnable ;//Card Type enable?
	BOOL	fEnableTrack1 ;//Card Type enable?	
	BOOL 	fCUPCard;
	BOOL	fIsOnUsCard ;
	BOOL	fIsLoyaltyCard ;
    int     IITid;
	int     HDTid;
	int			CDTid;

	//Add those based on TMS para
	int 		PaymentType;
	int         CurrAdminType;
	int         CurrAdminGSTInc;
	int         NewAdminType;
	int         NewAdminGSTInc;
	ULONG		CurrAdminAmt;
	ULONG       NewAdminAmt;
	int         CurrAdminGSTMessage;
	int         NewAdminGSTMessage;
	char        StartDate[13];
	char        EndDate[13];
	char        NewAdminWEF[13];
	ULONG       OfflineFloorLimit;
	BOOL        OfflineAllowed;
	BOOL        CDTVoidAllowed;
	BOOL		EMVAllowed;

} STRUCT_CDT ;/*Card Definition Table*/

/*-------------------------CDT End----------------------------*/




/*=======================================*
 * Card Definition Table(CST) Struct     *
 *=======================================*/
typedef struct
{
	int	inCurrencyIndex;
	char	szCurSymbol[10];
    char	szCurCode[10];	
} STRUCT_CST ;/*Card Definition Table*/







/*==========================================*
 *                  EMVT                    *	
 *              D E F I N E S               *
 *==========================================*/ 
          
/*==========================================*
 *                  EMNT Struct             *
 *==========================================*/
typedef struct
{
	short	inSchemeReference;
	short	inIssuerReference;
	short	inTRMDataPresent;
	unsigned long	lnEMVFloorLimit;
	unsigned long	lnEMVRSThreshold;
	short	inEMVTargetRSPercent;
	short	inEMVMaxTargetRSPercent;

	// Two new fields are added for APR ID:3,5
	short	inMerchantForcedOnlineFlag;
	short	inBlackListedCardSupportFlag;

	unsigned char	szEMVTACDefault[EMV_TAC_SIZE];
	unsigned char	szEMVTACDenial[EMV_TAC_SIZE];
	unsigned char	szEMVTACOnline[EMV_TAC_SIZE];
	unsigned char	szDefaultTDOL[EMV_MAX_TDOL_SIZE];
	unsigned char	szDefaultDDOL[EMV_MAX_DDOL_SIZE];
	short	inEMVFallbackAllowed;
	short	inNextRecord;
	unsigned long    ulEMVCounter;
	short	inEMVAutoSelectAppln;
	unsigned char	szEMVTermCountryCode[EMV_COUNTRY_CODE_SIZE];
	unsigned char	szEMVTermCurrencyCode[EMV_CURRENCY_CODE_SIZE];
	short	inEMVTermCurExp;
	unsigned char	szEMVTermCapabilities[EMV_TERM_CAPABILITIES_BCD_SIZE];
	unsigned char	szEMVTermAddCapabilities[EMV_ADD_TERM_CAPABILITIES_BCD_SIZE];
	unsigned char	szEMVTermType[EMV_TERM_TYPE_SIZE];
	unsigned char	szEMVMerchantCategoryCode[EMV_MERCH_CAT_CODE_SIZE];
	unsigned char	szEMVTerminalCategoryCode[EMV_TERM_CAT_CODE_SIZE];
	short	inModifyCandListFlag;        
	short	shRFU1;
	short	shRFU2;
	short	shRFU3;
	unsigned char	szRFU1[EMV_STRING_SIZE];
	unsigned char	szRFU2[EMV_STRING_SIZE];
	unsigned char	szRFU3[EMV_STRING_SIZE];
} STRUCT_EMVT;
 



/*==================================================*
 *                      HDT                         *
 *              (HOST Definition Table)             *
 *                  D E F I N E S                   *
 *==================================================*/
#define IP_LEN			16


/*==================================================*
 * 	HOST Definition Table(HDT) Struct               *
 *==================================================*/
typedef struct
{
	BYTE	szHostLabel[16] ;
    USHORT  inHostIndex;

	BYTE	szTPDU[5+1];
	BYTE	szNII[NII_BYTES+1] ;
	BOOL	fReversalEnable;
	BOOL	fHostEnable;
	BYTE	szTraceNo[TRACE_NO_BCD_SIZE+1];
    BOOL    fSignOn ;
    ULONG   ulLastTransSavedIndex;
    USHORT inCurrencyIdx;
	char	szAPName[100];
    USHORT inFailedREV;
	USHORT inDeleteREV;
    USHORT inNumAdv;
	
        
} STRUCT_HDT;
 

/*==================================================*
 *                      MMT                         *
 *              (MMT Definition Table)             *
 *                  D E F I N E S                   *
 *==================================================*/

/*==================================================*
 * 	MMT Definition Table(MMT) Struct               *
 *==================================================*/
typedef struct
{
    UINT    MMTid;
	BYTE	szHostName[50] ;
    UINT    HDTid;
    BYTE	szMerchantName[50] ;
    UINT    MITid;
	BOOL	fMMTEnable ;
	BOOL	fEnablePSWD ;
    BYTE	szPassWord[20+1] ;
    BYTE	szTID[10] ;
    BYTE	szMID[20] ;
    BYTE	szBatchNo[BATCH_NO_BCD_SIZE+1] ;
	BYTE	szATCMD1[50] ;
	BYTE	szATCMD2[50] ;
	BYTE	szATCMD3[50] ;
	BYTE	szATCMD4[50] ;
	BYTE	szATCMD5[50] ;	
    BOOL    fMustSettFlag ;
	BYTE	szRctHdr1[50];
	BYTE	szRctHdr2[50];
	BYTE	szRctHdr3[50];
    BYTE	szRctHdr4[50];
	BYTE	szRctHdr5[50];
	BYTE	szRctFoot1[50];
	BYTE	szRctFoot2[50];
	BYTE	szRctFoot3[50];


} STRUCT_MMT;






/*==================================================*
 *                      PIT                         *
 *              (PIT Definition Table)             *
 *                  D E F I N E S                   *
 *==================================================*/

/*==================================================*
 * 	PIT Definition Table(PIT) Struct               *
 *==================================================*/
typedef struct
{
	BYTE	szTransactionTypeName[20] ;
    UINT    inTxnTypeID;
    BOOL	fTxnEnable ;
    UINT    inPasswordLevel;
    
} STRUCT_PIT;



 
/*==================================================*
 *                      TCP                         *
 *              (TCP Definition Table)             *
 *                  D E F I N E S                   *
 *==================================================*/

/*==================================================*
 * 	TCP Definition Table(TCP) Struct               *
 *==================================================*/
typedef struct
{
	BYTE	szTerminalIP[30] ;
    BYTE	szGetWay[30] ;
    BYTE	szSubNetMask[30] ;
    BYTE	szHostDNS1[30] ;
    BYTE	szHostDNS2[30] ;
    BOOL	fDHCPEnable ;
    BYTE	szAPN[30] ;
    BYTE	szUserName[30] ;
    BYTE	szPassword[30] ;
    BYTE	szWifiSSID[100] ;
    BYTE	szWifiPassword[30] ;
    BYTE	szWifiProtocal[8] ;
    BYTE	szWifiPairwise[8] ;
    BYTE	szWifiGroup[8] ;
	int		inSIMSlot;
} STRUCT_TCP;



/*==================================================*
 *                      AIDT
 *                  (AID Table)
 *                  D E F I N E S
 *==================================================*/

typedef struct 
{
	ULONG   ulRef;
	BYTE    bAIDLen;
	BYTE    pbAID[16];
	BYTE    bApplication_Selection_Indicator;
	int		inEMVid;
	int		inCAPKindex1;
	BYTE    pbExpireDate1[6+1];
	int		inCAPKindex2;
	BYTE    pbExpireDate2[6+1];
	int		inCAPKindex3;
	BYTE    pbExpireDate3[6+1];
	int		inCAPKindex4;
	BYTE    pbExpireDate4[6+1];
	int		inCAPKindex5;
	BYTE    pbExpireDate5[6+1];
	int		inCAPKindex6;
	BYTE    pbExpireDate6[6+1];
	int		inCAPKindex7;
	BYTE    pbExpireDate7[6+1];
	int		inCAPKindex8;
	BYTE    pbExpireDate8[6+1];
	int		inCAPKindex9;
	BYTE    pbExpireDate9[6+1];
	int		inCAPKindex10;
	BYTE    pbExpireDate10[6+1];
	BYTE	pbTerminalAVN[2+1];
	BYTE	pb2ndTerminalAVN[2+1];
	
} STRUCT_AIDT;


/*==================================================*
 *                  Advice Struct
 *                  D E F I N E S
 *==================================================*/
#define AUTO_GET_INVOICE     -1

typedef struct 
{
	BYTE    szInvoiceNo[INVOICE_BCD_SIZE+1];
	ULONG   ulBatchIndex;//index of batch Rec. stored in xxxx000001.bat File
	BYTE    byTransType;//sam
} STRUCT_ADVICE;
/*==================================================*
 *                  Advice Func                       
 *==================================================*/
int inMyFile_AdviceRead(int *inSeekCnt, STRUCT_ADVICE *strAdvice, TRANS_DATA_TABLE *transData);
int inMyFile_AdviceReadByIndex(int inSeekCnt, STRUCT_ADVICE *strAdvice, TRANS_DATA_TABLE *transData);
int inMyFile_AdviceUpdate(int inSeekCnt);
int inMyFile_AdviceSave(TRANS_DATA_TABLE *transData, BYTE byTransType);

int inMyFile_TCUploadFileSave(TRANS_DATA_TABLE *transData, BYTE byTransType);
int inMyFile_TCUploadFileUpdate(int inSeekCnt);
int inMyFile_TCUploadFileRead(int *inSeekCnt, STRUCT_ADVICE *strAdvice, TRANS_DATA_TABLE *transData);
int inMyFile_TCUploadDelete(void);


STRUCT_AIDT strAIDT;


#define PASSWORD_LEN     6
#define DATE_LEN	 5
#define d_BUFF1_SIZE   576





/*=======================================*
 * Card Definition Table(IIT) Struct     *
 *=======================================*/
typedef struct
{
	int		inIssuerNumber;
	char	szIssuerAbbrev[20];
	char	szIssuerLabel[20];
    char	szPANFormat[20];
	char	szMaskMerchantCopy[20];
	char	szMaskCustomerCopy[20];
	char	szMaskExpireDate[20];
	char	szMaskDisplay[20];	
	BOOL	fMerchExpDate ;// expired Check
	BOOL	fCustExpDate ;
	BOOL	fMerchPANFormat ;//Card Type enable?
	int     inCheckHost;
    ULONG   ulTransSeqCounter;
	ULONG QPAmount;
} STRUCT_IIT ;/*Card Definition Table*/


typedef struct
{
    BYTE    szAppName[50];
    BYTE    szAppVer[6+1];
    BYTE    szParaVer[6+1];
    BYTE    szParaTMSVer[6+1];
    BYTE    szParaFile[50];
} STRUCT_APT ;

STRUCT_APT      strAPTAll[10];



typedef struct
{
    int      inCommMode;
    int      inHeaderFormat; // 2 is BCD exclude header len, 1 is HEX exclude header len, 4 is BCD include header len, 3 is HEX include header len

    BOOL     bSSLFlag;   
    BYTE     szCAFileName[30];
    BYTE     szClientFileName[30];
    BYTE     szClientKeyFileName[30];
        
    BYTE     szPriPhoneNum[30];
    BYTE     szSecPhoneNum[30];
    
    BYTE     szPriHostIP[50];
    ULONG    ulPriHostPort;
    BYTE     szSecHostIP[50];
    ULONG    ulSecHostPort;
    
    int      inSendLen;
    //BYTE     szSendData[2048];//change DB to txt file
    int      inReceiveLen;
    //BYTE     szReceiveData[2048];//change DB to txt file

	BOOL     bDialBackUp;
    BOOL     bPredialFlag;  
    int      inParaMode;
    int      inHandShake;
    int      inCountryCode;
	BYTE	szATCMD1[50];
	BYTE	szATCMD2[50];
	BYTE	szATCMD3[50];
	BYTE	szATCMD4[50];
	BYTE	szATCMD5[50];
    int      inConnectionTimeOut;
    int      inReceiveTimeOut;
    int      inGPRSSingal;
       
} STRUCT_COM;

typedef struct
{
	BYTE	byEnableExt;	//Enable COM ext data
	BYTE	bySSLVerCtrl;	// for SSL Version control , 20171012
	BYTE	byReserve01;
	BYTE	byReserve02;
	BYTE	byReserve03;

	
	int		inReserve01;
	int		inReserve02;
	int		inReserve03;

	BYTE 	szReserve01[64];
	BYTE 	szReserve02[64];
	BYTE 	szReserve03[128];

} S_COM_EXT;



typedef struct
{
	short shType;	
	
	char szTPDU[10+1];									// TPDU
	char szPAN[19+1];										// 02
	char szAmt[12+1];										// 04
	char szSTAN[6+1];									// 11
	char szTime[6+1];									// 12
	char szDate[8+1];									// 13
	char szExpDate[4+1];							// 14
	char szRefNum[12+1];								// 37
	char szApprCode[6+1];							// 38
	char szTID[8+1];										// 41
	char szMID[15+1];										// 42
	char szTerminalSerialNO[17+1];			// 46
	char szBankCode[22+1];							// 55
	char szMerchantCode[22+1];					// 56
	char szStoreCode[22+1];						// 57
	char szPaymentType[20+1];					// 58
	char szPaymentMedia[20+1];					// 59
	char szBatchNum[6+1];							// 60
	char szReceiptImageFileName[19+1];					// 61   store image for txn, init will not use this buf
	char szInvNum[6+1];									// 62
	char szLogoFileName[19+1];						    // 63   store Logo for init, store INV for txn

	char szSingatureStatus[2+1];									// signature status
	char szRSAKEKVersion[4+1];									// KEK version
	char szReceiptLogoIndex[2+1];								// receipt logo index
	
	VS_BOOL 	fGzipReceiptImage;								// 0 - the Receipt Image havn't zip,  1 - the Receipt Image zip already
	VS_BOOL		fSettle;											// 0 - transaction receipt, 1 - settlement receipt
	
	VS_BOOL		fReserved1;
	VS_BOOL		fReserved2;
	VS_BOOL		fReserved3;

	char 		szReserved1[65];								
	char 		szReserved2[65];
	char 		szReserved3[65];

	
} ERM_TransData;

typedef struct
{	 
	 char szAmount[12+1];//szAmount    Tag 9F02   (Amount Authorized(Numeric))
	 char szOtherAmt[12+1];//szOtherAmt  Tag 9F03	(Amount Other(Numeric))
	 char szTransType[2+1];//szTransType Tag 9C 	(Transaction Type)
	 char szCatgCode[4+1];//szCatgCode	Tag 9F53   (Transaction Category Code)
	 char szCurrCode[4+1];//szCurrCode	Tag 5F2A   (Transaction Currency Code)
	 int bTagNum;
	 int usTransactionDataLen;
	 BYTE pbaTransactionData[100+1];
} CTLS_Trans;


typedef struct
{	 
	 char szAmount[12+1];//szAmount    Tag 9F02   (Amount Authorized(Numeric))
	 char szOtherAmt[12+1];//szOtherAmt  Tag 9F03	(Amount Other(Numeric))
	 char szTransType[2+1];//szTransType Tag 9C 	(Transaction Type)
	 char szCatgCode[4+1];//szCatgCode	Tag 9F53   (Transaction Category Code)
	 char szCurrCode[4+1];//szCurrCode	Tag 5F2A   (Transaction Currency Code)
 
	 int status;
	 BYTE		 bSID;				 //Scheme Identifier
	 BYTE		 baDateTime[15];	 //YYYYMMDDHHMMSS format
	 BYTE		 bTrack1Len;
	 BYTE		 baTrack1Data[100];  //ANS 
	 BYTE		 bTrack2Len;
	 BYTE		 baTrack2Data[100];  //ASCII
	 USHORT 	 usChipDataLen; 	 //Chip Data
	 BYTE		 baChipData[1024];
	 USHORT 	 usAdditionalDataLen;	 //Additional Data
	 BYTE		 baAdditionalData[1024];

	 USHORT	usTransResult;		// Transction Result
	 BYTE	bCVMAnalysis;		//
	 BYTE	baCVMResults[3];	//CVM Result
	 BYTE	bVisaAOSAPresent;	//Visa Available Offline Spending Amount(AOSA) Present
	 BYTE	baVisaAOSA[6];
	 BOOL	bODAFail;			//Offline Data Authentication Fail , ODA FAIL -> TRUE, ODA OK --> FALSE
	
	 int	 inReserved1;//bTagNum
	 int	 inReserved2;//usTransactionDataLen
	 int	 inReserved3;//CLTS/EMV/Swipe
 
	 char		 szReserved1[100];//pbaTransactionData								 
	 char		 szReserved2[100];
	 char		 szReserved3[100];
 
 } CTLS_TransData;


/*==================================================*
 *                  Reversal Func                       
 *==================================================*/
int inMyFile_ReversalSave(BYTE *szReversal8583Data, ULONG ulReversalSize);
int inMyFile_ReversalRead(BYTE *szReadOutBuffer, ULONG ulReadOutBufferSize);
int inMyFile_ReversalDelete(void);

/*==================================================*
 *                  Other Func                       
 *==================================================*/
BOOL fGetNumber(BYTE *szInBuffer, void *szOutBuffer, int inInBufferLen);
BOOL fGetBoolean(BYTE *szInBuffer);
BOOL fGetString(BYTE *szInBuffer, BYTE *szOutBuffer, int inInBufferLen);
BOOL inMyFile_feof(ULONG ulFileHandle);

int inMyFile_CheckFileExist(char *szFileName);
int inMyFile_GetFileLine(ULONG ulFileHandle, char *szBuffer, int inMaxOneLine);
int inMyFile_BatchSearch(TRANS_DATA_TABLE *trasnData, char *hexInvoiceNo);
int inMyFile_BatchSave(TRANS_DATA_TABLE *transData, int inStoredType);
int inMyFile_BatchDelete(void);
int inMyFile_ContinueReadRec(void *p1);
int inMyFile_BatchRead(TRANS_DATA_TABLE *transData, int inSeekCnt);

int inMyFile_HDTTraceNoAdd(BYTE byHostIndex) ;

void vdMyFile_TotalInit(STRUCT_FILE_SETTING *strFile, TRANS_TOTAL *strTotal);
int inMyFile_TotalRead(TRANS_TOTAL *strTotal);
int inMyFile_TotalSave(TRANS_TOTAL *strTotal);
int inMyFile_TotalDelete(void);
int inMyFile_AdviceDelete(void);
int inExceedMaxTimes_ReversalDelete(void);


int inCheckFileExist(const char *szFileName);
int inLoadConfRec (const char *szFileName, int inRecSize, int inRecNum, char *pchConfRec);
int inSaveConfRec (const char *szFileName, int inRecSize, int inRecNum, char *pchConfRec);
int inGetNumberOfConfRecs (char *szFileName, int inRecSize);
int inAppendConfRec (char *szFileName, int inRecSize, char *pchConfRec);
int inDeleteConfRec (char *szFileName, int inRecSize, int inRecNum);
int inSearchConfRec (char *szFileName, int inRecSize, int *inRecNum,  char *pchConfRec,
							int (*inCompare)(char *, char *), char *pchCompRec);
int inRemoveConfRecFile(char *szFileName);




/*==================================================*
 *                  Global Var                       
 *==================================================*/
STRUCT_TCT      strTCT;
STRUCT_TCTEX    strTCTEX;
STRUCT_CDT      strCDT;
STRUCT_CDT      strMCDT[10];

STRUCT_IIT      strIIT;
STRUCT_CST      strCST;
STRUCT_EMVT     strEMVT;
STRUCT_HDT      strHDT;
STRUCT_CPT      strCPT;
STRUCT_GCPT		strGCPT;

STRUCT_MMT      strMMT[10];
STRUCT_TCP      strTCP;
STRUCT_PIT      strPIT;

STRUCT_COM      strCOM;
S_COM_EXT      	strCOMExt;

typedef struct
{
	int inRevRetries;		 /* 4 */
	int inAutoRetries;		 /* 4*/
	int inSettRetries;		 /* 4 */
	int inTxnSettRetries;	 /* 4 */
	int inRfndCount;		 /* 4 */
	int inRfnddayCount;		 /* 4 */
	int inForcedRetries;		 /* 4 */
	int inLogonStatus;		 /* 4 */
	int inForceBlocklistDldRetry;		 /* 4 */
	int inForceSettlementRetry;		 /* 4 */
	int inForceBlocklistDldPrompt;		 /* 4 */
	int inForceSettlementPrompt;		 /* 4 */
	int inECRReceipt;		 /* 4 */
	int inECREnDis;		 /* 4 */
	int inCSMEnDis;		 /* 4 */
	int inEnhcLgnTries;		 /* 4 */
	int inEnhcLgnMax;		 /* 4 */
	int inTMLReseted;		 /* 4 */
	int inTMKOption;		 /* 4 */
	int inAdTMKIndex;		 /* 4 */
	int inNewTMKIndex;		 /* 4 */
	int inNewDSKIndex;		 /* 4 */
	int inNewAdTMKIndex;		 /* 4 */
	long lnTTC;		 /* 4 */
	long lnRfndAmt;		 /* 4 */
	long lnRfndDayAmt;		 /* 4 */
	long lnRevCnt;		 /* 4 */
	long lnTopupBalAmt;		 /* 4 */
	long lnTopupSTAN;		 /* 4 */
	char szSoftwareVer[3+1];		 /* 3 Hex */ 
	char szBlackListVer[3+1];		 /* 3 Hex */
	char szPPBlockListVer[3+1];		 /* 3 Hex */
	char szHostBlockListVer[3+1];		 /* 3 Hex */
	char szParamVer[3+1];		 /* 3 Hex */
	char szSuperTrack[200+1];		 /* 200 String */ 
	char szSuperPinBlock[8+1];		 /* 8 Hex */
	char szKeySetIndex[11+1];		 /* 11 Hex */
	char szMerchShortName[6+1];		 /* 6 Hex */
	char szVersionNumber[22+1];		 /* 22 String */ 
	char szPPVerNum[22+1];		 /* 22 String */ 
	char szPP1KFirmVer[22+1];		 /* 22 String */ 
	char szAutoLogDt[6+1];		 /* 6 String */ 
	char szAutoStlDt[6+1];		 /* 6 String */ 
	char szCashierId[4+1];		 /* 4 String */ 
	char szCurrBatchNum[6+1];		 /* 6 Hex */ 
	char szSCSerialNo[16+1];		 /* 16 String */ 
	char szLastOnlineTime[6+1];		 /* 6 String */ 
	char szLastAutoTime[6+1];		 /* 6 String */ 
	char szLastSettTime[6+1];		 /* 6 String */ 
	char szLastTxnSettTime[6+1];		 /* 6 String */ 
	char szRfndDate[12+1];		 /* 12 String */ 
	char szTopupCashierId[4+1];		 /* 4 String */ 
	char szTopupDate[12+1];		 /* 12 String */ 
	char szTopupTime[6+1];		 /* 6 String */ 
	char szSCTMKSeed[16+1];		 /* 16 Hex */
	char szAdTMKSeed[16+1];		 /* 16 Hex */
	char szNewTMKSeed[16+1];		 /* 16 Hex */
	char szNewDSKSeed[16+1];		 /* 16 Hex */
	char szNewAdTMKSeed[16+1];		 /* 16 Hex */
	char szForcedTime[6+1];		 /* 6 String */ 
	char szLastBlocklistTimeStamp[16+1];		 /* 16 String */ 
	char szFirstContactlessTimeStamp[16+1];		 /* 16 String */ 
	char szLastLogonTimeStamp[16+1];		 /* 16 String */ 
	char szLastEnhcLgnTry[16+1];		 /* 16 String */ 
	char szSuperCardFlag[1+1];		 /* 1 String */
	BOOL fRevFlag;		 /* 0 */
	BOOL fPrintDuplicate;		 /* 0 */
	BOOL fPrintISODump;		 /* 0 */
	BOOL fAllowRedeem;		 /* 0 */
	BOOL f9802Reversal;		 /* 0 */
	BOOL fCCReversal;		 /* 0 */
	BOOL fMIDChecking;		 /* 0 */
	BOOL fECRTOPUPEnDis;		 /* 0 */
	BOOL fLuckyDraw;		 /* 0 */
	BOOL fFinancialTran;		 /* 0 */
	BOOL fCashBkManual;		 /* 0 */
	BOOL fCashBkFix;		 /* 0 */
	BOOL fMultiUploadTran;		 /* 0 */
	BOOL fManualLogon;		 /* 0 */
	BOOL fEnPOSMsg;		 /* 0 */
	int inRSVPEnDis;
       
} STRUCT_NDT;

STRUCT_NDT      strNDT;

/*==================================================*
 * 	EFT transaction Ext Definition Table(EFT) Struct               *
 *==================================================*/
typedef struct
{
	int inHDTid;

    int inEFTEnable;
    int inKeySet;
    int inKeyIndex;
    int inSHA1MAC;
    
    char szEncryptMode[4 + 1];
    char szEncrypBitmap[16 + 1];
    char szAcquirerID[20 + 1];
    char szVendorID[20 + 1];
    char szEFTNII[4 + 1];
    char szEFTVersion[3 + 1];
}STRUCT_EFT;

STRUCT_EFT      strEFT[10];

/*=======================================*
 * Credit Offline Definition Table(CRO) Struct     *
 *=======================================*/
typedef struct
{
	int		CROid;
	int		InScreenTimeout;
	int		InCommTimeout;	
	BOOL    fAuthorisation;
	int		inVoidDuration;
	long	InCumulativeOffAmt;
	long    InMaxOfflineCount;
	int		inCurrentGST;
	int		inNewGST;	
	long    InCurrOfflineAmt;
	long    InCurrOfflineCount;
	BYTE	szDateNewGST[14];	
} STRUCT_CRO ;/*Credit Definition Table*/

/*-------------------------CRO End----------------------------*/

STRUCT_CRO      strCRO;

typedef struct
{
    //In Master app para txt
    int 		inScreenTO;
    int 		inAlertItv;

    BOOL		fCREnable;
    BOOL		fNEEnable;
    BOOL		fEZEnable;
    BOOL		fFPEnable;
    BOOL		fCUEnable;
    BOOL		fDAEnable;
    BOOL		fQREnable;
    BOOL		fAPEnable;

    int 		inMenuTO;
    int 		inMAXLog;
    BOOL		fTXNVoice;
    long 		lMaxFareAmt;

    //in EZL para
    BOOL 		fCEPASVA;//cepas void allow,

	int 		inCreditConfirmAmtTO;
	int 		inNETSConfirmAmtTO;
	int 		inAliDashConfirmAmtTO;
} STRUCT_TXN;
STRUCT_TXN		strTXN;

typedef struct
{
	ULONG       ulSavedIndex;
	BYTE szTxnTimeStamp[6 + 1];//N12, yymmddhhmmss
	BYTE szStuckTxnType[3 + 1];//ASC3, NFP, EZL,CCO, CRO, CRV, CTC, CCR, APR, DAR
	BYTE szMaskCardNum[5 + 1];//N10, first 6, last 4
	BYTE szFare[3 + 1];//N6
	BYTE szGST[3 + 1];//N6
	BYTE szAdmin[3 + 1];//N6
	BYTE szTotalAmount[6 + 1];//N12, Total amount in cents
	BYTE szApprCodeRRN[20 + 1];//AN20, Approval code + RRN, 3+7?
	BYTE szJobNum[10 + 1];//ASC10
	BYTE szDriverID[9 + 1];//ASC9
	BYTE szTaxiNumber[20+1];//for upload reversal/tc/offline, need send back to Nets pack DE61 in LES
	BYTE szCompanyCode[4+1];//for upload reversal/tc/offline, need send back to Nets pack DE61 in LES
	BYTE szMID[15+ 1];//ASC15
	BYTE szTID[8+ 1];//ASC8
    BYTE szReleaseVer[6+ 1];//ASC6
	int inStuckTxnStatus; // A->Auto cleared after PIN pad retry, D->Cleared based on the host request, 1-> already uploaded tms, 0 init status

    BYTE fileName[40 + 1]; // offline file name
	int fileSize; // offline file size
    BYTE fileContent[9999]; // offline file ISO message
    BYTE keyWord[40 + 1]; // same as offline file name and this is keyword search.
	int inUploadTried; // offline ISO tried, 0 not upload yet, 1.2. means 4 is max which means need upload stuck trasaction to comfort TMS host.

	BYTE inTMSUploaded;//if it is 1, then it means record already uploaded, so next time we do update, this falg will be set after upload succ
} STRUCT_STU ;

STRUCT_STU      strSTU;

typedef struct
{
    int 	byTransType;
    BYTE    szSWDldApplName[10+1];
    BYTE    szSWDldApplVer[6+1];
    BYTE    szNextParaFileNum[7];
    BYTE    szNextFileNum[7];
    BYTE    szTerminalReleaseVersion[7];
    int		appsDlInstallStatus;
    int		paraDlInstallStatus;
    int		inNumOfTMSApp;
    BYTE    szSWDldNextTimeWindow[12+1];
    ULONG    lnSWDldLRC;
    ULONG    lnSWDldByteCount;
    int 	SWDldState;

} STRUCT_TMSEX ;
STRUCT_TMSEX      strTMSEx;

#endif	/* _MYFILEFUNC__H */

