<?xml version="1.0" encoding="utf-8"?>  
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">  
    <item android:top="2dp">   
        <shape android:shape="rectangle" android:right ="1dp" android:left="1dp">  
            <corners
		    android:bottomLeftRadius="2dp"
		    android:bottomRightRadius="2dp"
		    android:topLeftRadius="2dp"
		    android:topRightRadius="2dp" />
            <solid android:color="#9D9D9D" />  
        </shape>  
    </item>  
    <item android:top="2dp" android:bottom="1dp" android:right ="1dp" android:left="1dp">   
        <shape android:shape="rectangle"> 
            <corners
		    android:bottomLeftRadius="2dp"
		    android:bottomRightRadius="2dp"
		    android:topLeftRadius="2dp"
		    android:topRightRadius="2dp" /> 
            <gradient
		        android:startColor="#C3C3C3"
		        android:endColor="#696969"
		        android:angle="-90"/>
        </shape>  
    </item>  
    
    <item android:top="2dp" android:bottom="2dp">   
        <shape android:shape="rectangle"> 
            <corners
		    android:bottomLeftRadius="2dp"
		    android:bottomRightRadius="2dp"
		    android:topLeftRadius="2dp"
		    android:topRightRadius="2dp" /> 
            <solid android:color="#B5B5B5" />  
        </shape>  
    </item> 
    
    <item android:bottom="2dp" android:top="2dp" android:right ="1dp" android:left="1dp">  
        <shape android:shape="rectangle">  
            <corners
		    android:bottomLeftRadius="2dp"
		    android:bottomRightRadius="2dp"
		    android:topLeftRadius="2dp"
		    android:topRightRadius="2dp" />
            <solid android:color="#FDA514" /> 
        </shape>     
    </item>  
</layer-list>