<resources>

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorStatusBar</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>

    <!-- Start: Default theme. -->
    <style name="DefalutTheme" parent="AppTheme">
        <item name="android:actionBarStyle">@style/FullscreenActionBarStyle</item>
        <item name="android:windowActionBarOverlay">true</item>
        <item name="android:windowBackground">@color/colorThemeBackground1</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="metaButtonBarStyle">?android:attr/buttonBarStyle</item>
        <item name="metaButtonBarButtonStyle">?android:attr/buttonBarButtonStyle</item>
    </style>
    <style name="DefaultTextViewNormal" >
        <item name="android:textSize">@dimen/normal_text_size</item>
        <item name="android:textColor">@color/colorTextViewText</item>
    </style>

    <style name="DefaultTextViewLarge" >
        <item name="android:textSize">@dimen/large_text_size</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/colorTextViewText</item>
    </style>

    <style name="DefaultEditTextLarge" parent="Theme.AppCompat.Light">
        <item name="android:textSize">@dimen/large_text_size</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/colorTextViewText</item>
        <item name="android:textCursorDrawable">@color/black</item>
        <!--δ��ѡ��ʱ�»��ߵ���ɫ-->
        <item name="colorControlNormal">@color/black</item>
        <!--��ѡ��ʱ�»��ߵ���ɫ-->
        <item name="colorControlActivated">@color/black</item>
    </style>

    <style name="DefaultEditTextNormal" parent="Theme.AppCompat.Light">
        <item name="android:textSize">@dimen/normal_text_size</item>
        <item name="android:textColor">@color/colorTextViewText</item>
        <!--δ��ѡ��ʱ�»��ߵ���ɫ-->
        <item name="colorControlNormal">@color/black</item>
        <!--��ѡ��ʱ�»��ߵ���ɫ-->
        <item name="colorControlActivated">@color/black</item>
    </style>

    <style name="DefaultButtonNormal">
        <item name="android:textSize">@dimen/button_text_size</item>
        <item name="android:textColor">@color/colorButtonText</item>
        <item name="android:background">@drawable/shape_blue_button</item>
        <item name="android:layout_height">@dimen/sixty_dp</item>
        <item name="android:layout_width">@dimen/one_hundred_fifty_dp</item>
    </style>

    <!-- End: Default theme. -->

    <style name="FullscreenTheme" parent="AppTheme">
        <item name="android:actionBarStyle">@style/FullscreenActionBarStyle</item>
        <item name="android:windowActionBarOverlay">true</item>
        <item name="android:windowBackground">@null</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="metaButtonBarStyle">?android:attr/buttonBarStyle</item>
        <item name="metaButtonBarButtonStyle">?android:attr/buttonBarButtonStyle</item>
    </style>

    <style name="FullscreenActionBarStyle" parent="Widget.AppCompat.ActionBar">
    <item name="android:background">@color/black_overlay</item>
    </style>

    <style name="activityNoAnimTheme" parent="android:Theme">
        <item name="android:windowAnimationStyle">@null</item>
    </style>

    <style name="AppTheme.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="AppTheme.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />

    <style name="AppTheme.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />
    <style name="DialogStyle2" parent="@android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item> <!-- �߿� -->
        <item name="android:windowIsFloating">true</item> <!-- �Ƿ�����activity֮�� -->
        <item name="android:windowNoTitle">true</item> <!-- �ޱ��� -->
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>
    <style name="dialogAnim" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/dialog_show</item>
        <item name="android:windowExitAnimation">@anim/dialog_dismiss</item>
    </style>

    <style name="CustomDialog" parent="Theme.AppCompat.Light.Dialog">
        <item name="android:background">@drawable/frame</item>
    </style>

    <style name="DefaultButtonOK">
        <item name="android:textSize">@dimen/button_text_size</item>
        <item name="android:textColor">@color/colorButtonText</item>
        <item name="android:background">@drawable/frame_button_ok</item>
        <item name="android:layout_height">@dimen/sixty_dp</item>
        <item name="android:layout_width">@dimen/one_hundred_fifty_dp</item>
    </style>

    <style name="DefaultButtonCANCEL">
        <item name="android:textSize">@dimen/button_text_size</item>
        <item name="android:textColor">@color/colorButtonText</item>
        <item name="android:background">@drawable/frame_button_cancel</item>
        <item name="android:layout_height">@dimen/sixty_dp</item>
        <item name="android:layout_width">@dimen/one_hundred_fifty_dp</item>

    </style>

    <style name="DefaultTextViewList">
        <item name="android:textSize">@dimen/large_text_size</item>
        <item name="android:textColor">@color/colorButtonText</item>
        <item name="android:background">@drawable/frame_listview</item>
    </style>

</resources>
