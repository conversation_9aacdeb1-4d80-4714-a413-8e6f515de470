<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/activity_socket"
    android:background="@drawable/images"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingBottom="@dimen/activity_vertical_margin"
    android:paddingLeft="@dimen/activity_horizontal_margin"
    android:paddingRight="@dimen/activity_horizontal_margin"
    android:paddingTop="@dimen/activity_vertical_margin"
    tools:context="com.Source.S1_NPX.NPX.SocketActivity">
    <EditText
        android:id="@+id/ip"
        android:hint="**************"
        android:layout_width="190dp"
        android:layout_height="wrap_content" />
    <EditText
        android:id="@+id/port"
        android:hint="5010"
        android:layout_toRightOf="@+id/ip"
        android:layout_width="80dp"
        android:layout_height="wrap_content" />
    <Button
        android:layout_below="@+id/ip"
        android:text="init client"
        android:id="@+id/init_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />
    <EditText
        android:layout_below="@+id/init_button"
        android:layout_toRightOf="@+id/text_hint"
        android:id="@+id/send_edit"
        android:layout_width="190dp"
        android:layout_height="wrap_content" />
    <Button
        android:layout_below="@+id/ip"
        android:layout_toRightOf="@+id/init_button"
        android:id="@+id/send_button"
        android:text="send"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />
    <TextView
        android:layout_below="@+id/send_button"
        android:id="@+id/text_hint"
        android:text="Send Data:"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />
    <TextView
        android:layout_below="@+id/send_edit"
        android:id="@+id/text_hint1"
        android:text="Recv Data:"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />
    <EditText
        android:layout_below="@+id/text_hint1"
        android:layout_toRightOf="@+id/text_hint1"
        android:id="@+id/recv_edit"
        android:layout_width="190dp"
        android:layout_height="wrap_content" />
    <Button
        android:layout_below="@+id/ip"
        android:layout_toRightOf="@+id/send_button"
        android:text="close"
        android:id="@+id/close_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

</RelativeLayout>
