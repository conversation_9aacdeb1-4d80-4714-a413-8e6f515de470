<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:background="@drawable/rndkbd_bg"
    android:orientation="horizontal"
    android:id="@+id/root">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="120dp"
        android:layout_marginLeft="1dp"
        android:orientation="vertical" >

        <TextView
            android:id="@+id/ng_msg_01"
            android:layout_width="420dp"
            android:layout_height="50dp"
            android:text=""
            android:textColor="#000000"
            android:textSize="38dp"/>

        <EditText
            android:id="@+id/n_num_edit"
            android:layout_width="300dp"
            android:layout_height="60sp"
            android:layout_marginLeft="50dp"
            android:cursorVisible="true"
            android:digits="1234567890"
            android:gravity="right"
            android:hint="ENTER NUM"
            android:inputType="number"
            android:singleLine="true"
            android:textSize="28sp"
            android:textStyle="bold" />
        />

        <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:layout_marginTop="1dp" >
        </LinearLayout>

    </LinearLayout>

</ScrollView>