<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.Source.S1_NPX.NPX.Main.MainActivity">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="935px"
        android:id="@+id/gif_container"
        android:orientation="vertical">


        <pl.droidsonroids.gif.GifImageView
            android:id="@+id/gif1"
            android:adjustViewBounds="true"
            android:scaleType="fitXY"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </LinearLayout>

<!--    <LinearLayout-->
<!--        android:id="@+id/btn_ui_container"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:orientation="horizontal">-->

<!--        <Button-->
<!--            android:id="@+id/Manual"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_weight="1"-->
<!--            android:visibility="visible"-->
<!--            android:text="Manual" />-->

<!--        <Button-->
<!--            android:id="@+id/Cancel"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_weight="1"-->
<!--            android:text="Cancel" />-->

<!--    </LinearLayout>-->


</LinearLayout>