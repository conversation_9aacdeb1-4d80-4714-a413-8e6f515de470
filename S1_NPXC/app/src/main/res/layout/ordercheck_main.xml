<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/images"
    tools:context="com.Source.S1_NPX.NPX.Main.MainActivity">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:background="@drawable/p1">
    </LinearLayout>
    <LinearLayout
        android:background="@drawable/bg_footer"
        android:layout_width="match_parent"
        android:layout_height="50dp">
        <TextView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:textColor="#FFFFFF"
            android:textSize="25sp"
            android:gravity="center_vertical"
            android:text="Confirm Item"
            android:textAlignment="center"/>
    </LinearLayout>
    <TableLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center">
        <TableRow
            android:layout_marginTop="20dp">
            <ImageView
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:background="@drawable/food_1"
                android:id="@+id/food_1"/>
            <TableLayout>
                <TableRow>
                    <TextView
                        android:layout_width="50dp"
                        android:layout_height="60dp"
                        android:text="$45"
                        android:gravity="bottom"
                        android:textAlignment="center"
                        android:textColor="#000000"
                        android:textSize="20sp"
                        />
                </TableRow>
                <TableRow>
                    <TextView
                        android:id="@+id/food_1_count"
                        android:layout_width="50dp"
                        android:layout_height="40dp"
                        android:text=""
                        android:gravity="bottom"
                        android:textAlignment="center"
                        android:textColor="#FFFFFF"
                        android:textSize="20sp"
                        />
                </TableRow>
            </TableLayout>
            <ImageView
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:background="@drawable/food_2"
                android:id="@+id/food_2"/>
            <TableLayout>
                <TableRow>
                    <TextView
                        android:layout_width="50dp"
                        android:layout_height="60dp"
                        android:text="$35"
                        android:gravity="bottom"
                        android:textAlignment="center"
                        android:textColor="#000000"
                        android:textSize="20sp"
                        />
                </TableRow>
                <TableRow>
                    <TextView
                        android:id="@+id/food_2_count"
                        android:layout_width="50dp"
                        android:layout_height="40dp"
                        android:text=""
                        android:gravity="bottom"
                        android:textAlignment="center"
                        android:textColor="#FFFFFF"
                        android:textSize="20sp"
                        />
                </TableRow>
            </TableLayout>
        </TableRow>
        <TableRow
            android:layout_marginTop="20dp">
            <ImageView
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:background="@drawable/food_3"
                android:id="@+id/food_3"/>
            <TableLayout>
                <TableRow>
                    <TextView
                        android:layout_width="50dp"
                        android:layout_height="60dp"
                        android:text="$160"
                        android:gravity="bottom"
                        android:textAlignment="center"
                        android:textColor="#000000"
                        android:textSize="20sp"
                        />
                </TableRow>
                <TableRow>
                    <TextView
                        android:id="@+id/food_3_count"
                        android:layout_width="50dp"
                        android:layout_height="40dp"
                        android:text=""
                        android:gravity="bottom"
                        android:textAlignment="center"
                        android:textColor="#FFFFFF"
                        android:textSize="20sp"
                        />
                </TableRow>
            </TableLayout>
            <ImageView
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:background="@drawable/food_4"
                android:id="@+id/food_4"/>
            <TableLayout>
                <TableRow>
                    <TextView
                        android:layout_width="50dp"
                        android:layout_height="60dp"
                        android:text="$30"
                        android:gravity="bottom"
                        android:textAlignment="center"
                        android:textColor="#000000"
                        android:textSize="20sp"
                        />
                </TableRow>
                <TableRow>
                    <TextView
                        android:id="@+id/food_4_count"
                        android:layout_width="50dp"
                        android:layout_height="40dp"
                        android:text=""
                        android:gravity="bottom"
                        android:textAlignment="center"
                        android:textColor="#FFFFFF"
                        android:textSize="20sp"
                        />
                </TableRow>
            </TableLayout>
        </TableRow>
        <TableRow
            android:layout_marginTop="20dp">
            <ImageView
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:background="@drawable/food_5"
                android:id="@+id/food_5"/>
            <TableLayout>
                <TableRow>
                    <TextView
                        android:layout_width="50dp"
                        android:layout_height="60dp"
                        android:text="$40"
                        android:gravity="bottom"
                        android:textAlignment="center"
                        android:textColor="#000000"
                        android:textSize="20sp"
                        />
                </TableRow>
                <TableRow>
                    <TextView
                        android:id="@+id/food_5_count"
                        android:layout_width="50dp"
                        android:layout_height="40dp"
                        android:text=""
                        android:gravity="bottom"
                        android:textAlignment="center"
                        android:textColor="#FFFFFF"
                        android:textSize="20sp"
                        />
                </TableRow>
            </TableLayout>
            <ImageView
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:background="@drawable/food_6"
                android:id="@+id/food_6"/>
            <TableLayout>
                <TableRow>
                    <TextView
                        android:layout_width="50dp"
                        android:layout_height="60dp"
                        android:text="$25"
                        android:gravity="bottom"
                        android:textAlignment="center"
                        android:textColor="#000000"
                        android:textSize="20sp"
                        />
                </TableRow>
                <TableRow>
                    <TextView
                        android:id="@+id/food_6_count"
                        android:layout_width="50dp"
                        android:layout_height="40dp"
                        android:text=""
                        android:gravity="bottom"
                        android:textAlignment="center"
                        android:textColor="#FFFFFF"
                        android:textSize="20sp"
                        />
                </TableRow>
            </TableLayout>
        </TableRow>
    </TableLayout>
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">
        <ImageView
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:background="@drawable/refresh"
            android:id="@+id/refresh"/>
        <TextView
            android:id="@+id/result"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:textAlignment="center"
            android:textColor="#FFFFFF"
            android:textSize="25sp"
            android:text=""/>
        <ImageView
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_alignParentRight="true"
            android:background="@drawable/check"
            android:id="@+id/check"/>
    </RelativeLayout>
</LinearLayout>
