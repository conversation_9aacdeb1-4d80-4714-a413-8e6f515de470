<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/activity_popup_menu"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/images"
    android:orientation="vertical"
    tools:context="com.Source.S1_NPX.NPX.model.DPopupMenuActivity">

    <Button
        android:id="@+id/popup_menu_button11"
        android:layout_width="400dp"
        android:layout_height="60dp"


        android:layout_alignParentLeft="true"
        android:layout_alignParentStart="true"
        android:layout_alignParentTop="true"
        android:text="USER SELECT (POPUP MENU)" />

</RelativeLayout>
