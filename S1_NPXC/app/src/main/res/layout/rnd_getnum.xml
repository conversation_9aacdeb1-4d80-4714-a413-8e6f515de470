<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:background="@drawable/rndkbd_bg"
    android:orientation="horizontal"
    android:id="@+id/root">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="1dp"
        android:layout_marginLeft="10dp"
        android:orientation="vertical" >

        <EditText
            android:id="@+id/num_edit"
            android:layout_width="220dp"
            android:layout_height="65sp"
            android:layout_marginLeft="200dp"
            android:digits="1234567890"
            android:singleLine="true"
            android:inputType="number"
            android:gravity="right"
            android:cursorVisible="true"
            android:hint="ENTER PIN"
            android:textStyle="bold"
            android:textSize="43sp"/>
        />
        <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:layout_marginTop="1dp" >


        
    </LinearLayout>
    <Button 
     android:id="@+id/pay_button"  
	 android:layout_width="1dp"
     android:layout_height="1dp"
     android:layout_gravity="center_horizontal"
     android:layout_marginTop="60dp"
     android:text=" "
     android:textSize="18dp"
     android:background="@drawable/rndbtn_bg"
     android:focusable="true"
     android:focusableInTouchMode="true"
     />
    </LinearLayout>

</ScrollView>