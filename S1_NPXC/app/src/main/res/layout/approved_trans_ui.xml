<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

<!--    <LinearLayout-->
<!--       android:layout_width="fill_parent"-->
<!--        android:layout_height="0dp"-->
<!--        android:layout_weight="1"-->
<!--        android:orientation="vertical">-->

<!--    </LinearLayout>-->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">
        <TextView
            android:id="@+id/tv_input_amt_msg11"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="FARE : $3.00"
            android:layout_gravity="center_horizontal"
            android:textSize="20dp"
            android:visibility="invisible"/>
        <TextView
            android:id="@+id/tv_input_amt_msg12"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="FARE : $3.00"
            android:layout_gravity="center_horizontal"
            android:textSize="20dp"
            android:visibility="invisible"/>
        <TextView
            android:id="@+id/tv_input_amt_msg13"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="FARE : $3.00"
            android:layout_gravity="center_horizontal"
            android:textSize="20dp"
            android:visibility="invisible"/>

        <TextView
            android:id="@+id/tv_input_amt_msg1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="FARE : $3.00"
            android:layout_gravity="center_horizontal"
            android:textSize="20dp"/>
        <TextView
            android:id="@+id/tv_input_amt_msg2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="FEE GST APPLIES"
            android:layout_gravity="center_horizontal"
            android:textSize="20dp"/>
        <TextView
            android:id="@+id/tv_input_amt_msg3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="PLEASE PRESENT CARD"
            android:textSize="20dp"
            android:layout_gravity="center_horizontal"
            android:visibility="visible"/>

        <TextView
            android:id="@+id/tv_input_amt_msg4"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="PLEASE PRESENT CARD"
            android:textSize="20dp"
            android:layout_gravity="center_horizontal"
            android:visibility="visible"/>

        <TextView
            android:id="@+id/tv_input_amt_msg5"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="PLEASE PRESENT CARD"
            android:textSize="20dp"
            android:layout_gravity="center_horizontal"
            android:visibility="visible"/>

        <TextView
            android:id="@+id/tv_input_amt_msg6"
            android:background="@color/red"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="PLEASE PRESENT CARD"
            android:textSize="20dp"
            android:layout_gravity="center_horizontal"
            android:visibility="visible"/>
    </LinearLayout>

<!--    <LinearLayout-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:gravity="bottom"-->
<!--        android:orientation="vertical">-->
<!--        <Button-->
<!--            android:id="@+id/idle_view"-->
<!--            android:layout_width="720px"-->
<!--            android:layout_height="806px"-->
<!--            android:background="@drawable/presentcard" />-->
<!--    </LinearLayout>-->

</LinearLayout>