<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/rndkbd_bg"
    android:orientation="vertical" >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="3dp"
        android:layout_marginRight="3dp"
        android:layout_marginTop="160dp"
        android:orientation="horizontal" >

        <Button
            android:id="@+id/keyboard_btn_f1"
            android:layout_width="fill_parent"
            android:layout_height="55dp"
            android:layout_weight="1"
            android:background="@drawable/rndkbd_bg_small"
            android:gravity="center_horizontal|center_vertical"
            android:text="F1"
            android:textSize="20dp" />

        <Button
            android:id="@+id/keyboard_btn_f2"
            android:layout_width="fill_parent"
            android:layout_height="55dp"
            android:layout_marginLeft="3dp"
            android:layout_weight="1"
            android:background="@drawable/rndkbd_bg_small"
            android:gravity="center_horizontal|center_vertical"
            android:text="F2"
            android:textSize="20dp" />

        <Button
            android:id="@+id/keyboard_btn_f3"
            android:layout_width="fill_parent"
            android:layout_height="55dp"
            android:layout_marginLeft="3dp"
            android:layout_weight="1"
            android:background="@drawable/rndkbd_bg_small"
            android:gravity="center_horizontal|center_vertical"
            android:text="F3"
            android:textSize="20dp" />

        <Button
            android:id="@+id/keyboard_btn_f4"
            android:layout_width="fill_parent"
            android:layout_height="55dp"
            android:layout_marginLeft="3dp"
            android:layout_weight="1"
            android:background="@drawable/rndkbd_bg_small"
            android:gravity="center_horizontal|center_vertical"
            android:text="F4"
            android:textSize="20dp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="3dp"
        android:layout_marginRight="3dp"
        android:layout_marginTop="3dp"
        android:orientation="horizontal" >

        <Button
            android:id="@+id/keyboard_btn1"
            android:layout_width="fill_parent"
            android:layout_height="55dp"
            android:layout_weight="1"
            android:background="@drawable/rndkbd_bg_small"
            android:gravity="center_horizontal|center_vertical"
            android:text="1"
            android:textSize="20dp" />

        <Button
            android:id="@+id/keyboard_btn2"
            android:layout_width="fill_parent"
            android:layout_height="55dp"
            android:layout_marginLeft="3dp"
            android:layout_weight="1"
            android:background="@drawable/rndkbd_bg_small"
            android:gravity="center_horizontal|center_vertical"
            android:text="2"
            android:textSize="20dp" />

        <Button
            android:id="@+id/keyboard_btn3"
            android:layout_width="fill_parent"
            android:layout_height="55dp"
            android:layout_marginLeft="3dp"
            android:layout_weight="1"
            android:background="@drawable/rndkbd_bg_small"
            android:gravity="center_horizontal|center_vertical"
            android:text="3"
            android:textSize="20dp" />

        <Button
            android:id="@+id/keyboard_btn9"
            android:layout_width="fill_parent"
            android:layout_height="55dp"
            android:layout_marginLeft="3dp"
            android:layout_weight="1"
            android:background="@drawable/rndkbd_bg_small"
            android:gravity="center_horizontal|center_vertical"
            android:text="9"
            android:textSize="20dp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="3dp"
        android:layout_marginRight="3dp"
        android:layout_marginTop="3dp"
        android:orientation="horizontal" >

        <Button
            android:id="@+id/keyboard_btn4"
            android:layout_width="fill_parent"
            android:layout_height="55dp"
            android:layout_weight="1"
            android:background="@drawable/rndkbd_bg_small"
            android:gravity="center_horizontal|center_vertical"
            android:text="4"
            android:textSize="20dp" />

        <Button
            android:id="@+id/keyboard_btn5"
            android:layout_width="fill_parent"
            android:layout_height="55dp"
            android:layout_marginLeft="3dp"
            android:layout_weight="1"
            android:background="@drawable/rndkbd_bg_small"
            android:gravity="center_horizontal|center_vertical"
            android:text="5"
            android:textSize="20dp" />

        <Button
            android:id="@+id/keyboard_btn6"
            android:layout_width="fill_parent"
            android:layout_height="55dp"
            android:layout_marginLeft="3dp"
            android:layout_weight="1"
            android:background="@drawable/rndkbd_bg_small"
            android:gravity="center_horizontal|center_vertical"
            android:text="6"
            android:textSize="20dp" />

        <Button
            android:id="@+id/keyboard_btn0"
            android:layout_width="fill_parent"
            android:layout_height="55dp"
            android:layout_marginLeft="3dp"
            android:layout_weight="1"
            android:background="@drawable/rndkbd_bg_small"
            android:gravity="center_horizontal|center_vertical"
            android:text="0"
            android:textSize="20dp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="3dp"
        android:layout_marginRight="3dp"
        android:layout_marginTop="3dp"
        android:orientation="horizontal" >

        <Button
            android:id="@+id/keyboard_btn7"
            android:layout_width="fill_parent"
            android:layout_height="55dp"
            android:layout_weight="3"
            android:background="@drawable/rndkbd_bg_small"
            android:gravity="center_horizontal|center_vertical"
            android:text="7"
            android:textSize="20dp" />

        <Button
            android:id="@+id/keyboard_btn8"
            android:layout_width="fill_parent"
            android:layout_height="55dp"
            android:layout_marginLeft="3dp"
            android:layout_weight="3"
            android:background="@drawable/rndkbd_bg_small"
            android:gravity="center_horizontal|center_vertical"
            android:text="8"
            android:textSize="20dp" />


        <Button
            android:id="@+id/keyboard_btn_del"
            android:layout_width="fill_parent"
            android:layout_height="55dp"
            android:layout_marginLeft="3dp"
            android:layout_weight="2"
            android:background="@drawable/rndkbd_backspace_bg_big"
            android:gravity="center_horizontal|center_vertical"
            android:text="BACKSPACE"
            android:textSize="20dp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="3dp"
        android:layout_marginLeft="3dp"
        android:layout_marginRight="3dp"
        android:layout_marginTop="3dp"
        android:orientation="horizontal" >

        <Button
            android:id="@+id/keyboard_btn_clear"
            android:layout_width="fill_parent"
            android:layout_height="55dp"
            android:layout_weight="1"
            android:background="@drawable/rndkbd_cancel_bg_big"
            android:gravity="center_horizontal|center_vertical"
            android:text="CANCEL"
            android:textSize="20dp" />

        <Button
            android:id="@+id/keyboard_btn_conf"
            android:layout_width="fill_parent"
            android:layout_height="55dp"
            android:layout_marginLeft="3dp"
            android:layout_weight="1"
            android:background="@drawable/rndkbd_enter_bg_big"
            android:gravity="center_horizontal|center_vertical"
            android:text="CONFIRM"
            android:textSize="20dp" />
    </LinearLayout>

</LinearLayout>
