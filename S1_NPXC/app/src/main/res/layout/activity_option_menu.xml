<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/activity_option_menu"
    android:background="@drawable/images"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.Source.S1_NPX.NPX.model.DOptionMenu">

    <Button
        android:id="@+id/option_menu_button11"
        android:layout_width="400dp"
        android:layout_height="60dp"


        android:layout_alignParentLeft="true"
        android:layout_alignParentStart="true"
        android:layout_alignParentTop="true"
        android:text="USER SELECT (OPTION MENU)" />

</RelativeLayout>
