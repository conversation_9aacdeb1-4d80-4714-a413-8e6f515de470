<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:orientation="vertical"
        android:background="@color/white">

    </LinearLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_input_amt_msg1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text="FARE:"
            android:textColor="@color/text_blue"
            android:textSize="20dp" />

        <TextView
            android:id="@+id/tv_input_amt_msg2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text="$3.00"
            android:textColor="@color/text_blue"
            android:textSize="40dp" />

        <TextView
            android:id="@+id/tv_input_amt_msg3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text="FEES GST APPLIES"
            android:textColor="@color/text_blue"
            android:textSize="20dp"
            android:visibility="visible" />


    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/imageview"
            android:layout_width="match_parent"
            android:layout_height="162dp"
            android:layout_gravity="center"
            android:scaleType="fitCenter"
            android:src="@drawable/credit_card" />


        <TextView
            android:id="@+id/tv_input_amt_msg4"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text="PROCEED BY:"
            android:textColor="@color/text_blue"
            android:textSize="20dp"
            android:visibility="visible" />

        <VideoView
            android:id="@+id/video_view"
            android:layout_width="175dp"
            android:layout_height="260dp"
            android:layout_gravity="center" />


    </LinearLayout>


</LinearLayout>