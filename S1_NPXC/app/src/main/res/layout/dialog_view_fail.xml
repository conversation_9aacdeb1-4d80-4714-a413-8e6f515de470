<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2009 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:background="#05f5dc"
    >
    
    
<LinearLayout
	android:orientation="vertical"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:background="#ffffff"
    android:layout_weight="4">

<RelativeLayout 
	android:orientation="vertical"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:background="#ffffff"
    android:padding="10px">
    

    
    <EditText android:id="@+id/EditResult_fail"
        	android:text="Result : "
		  android:layout_width="fill_parent"
		  android:layout_height="fill_parent"
		  android:background="@null"
		  android:editable="false"
		  android:textSize="20px" 
		  android:textColor="#666666"
	/> 
	
    <TextView 
          android:id="@+id/Result_fail"
		  android:layout_width="fill_parent"
		  android:layout_height="wrap_content"
		  android:layout_marginLeft="10dip"
		  android:textSize="15px"
		  android:layout_below="@id/EditResult_fail"
		  android:textColor="#FF0000"
		  android:textStyle="bold"
	/>
    
    
   		</RelativeLayout>
 	</LinearLayout>
</ScrollView>