<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/white_bg_land"
    android:orientation="vertical"
    tools:context="com.Source.S1_NPX.NPX.Main.MainActivity">

    <TextView
        android:id="@+id/msg_text_01"
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:text=""
        android:textColor="#000000"
        android:textSize="32sp" />

    <TextView
        android:id="@+id/msg_text_02"
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:text=""
        android:textColor="#000000"
        android:textSize="32sp" />

    <TextView
        android:id="@+id/msg_text_03"
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:gravity="left|right|center"
        android:text=""
        android:textColor="#000000"
        android:textSize="32sp" />

    <TextView
        android:id="@+id/msg_text_04"
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:gravity="center"
        android:text=""
        android:textColor="#000000"
        android:textSize="32sp" />

    <TextView
        android:id="@+id/msg_text_05"
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:gravity="center"
        android:text=""
        android:textColor="#000000"
        android:textSize="32sp" />

    <TextView
        android:id="@+id/msg_text_06"
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:gravity="center"
        android:text=""
        android:textColor="#000000"
        android:textSize="32sp" />

    <TextView
        android:id="@+id/msg_text_07"
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:gravity="center"
        android:text=""
        android:textColor="#000000"
        android:textSize="32sp" />

    <TextView
        android:id="@+id/msg_text_08"
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:gravity="center"
        android:text=""
        android:textColor="#000000"
        android:textSize="32sp" />

    <TextView
        android:id="@+id/msg_text_09"
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:gravity="center"
        android:text=""
        android:textColor="#000000"
        android:textSize="32sp" />

    <TextView
        android:id="@+id/msg_text_10"
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:gravity="center"
        android:text=""
        android:textColor="#000000"
        android:textSize="32sp" />

    <TextView
        android:id="@+id/msg_text_11"
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:gravity="center"
        android:text=""
        android:textColor="#000000"
        android:textSize="32sp" />

    <TextView
        android:id="@+id/msg_text_12"
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:gravity="center"
        android:text=""
        android:textColor="#000000"
        android:textSize="32sp" />

    <TextView
        android:id="@+id/msg_text_13"
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:gravity="center"
        android:text=""
        android:textColor="#000000"
        android:textSize="32sp" />

    <TextView
        android:id="@+id/msg_text_14"
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:gravity="center"
        android:text=""
        android:textColor="#000000"
        android:textSize="32sp" />

    <TextView
        android:id="@+id/msg_text_15"
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:gravity="center"
        android:text=""
        android:textColor="#000000"
        android:textSize="32sp" />

    <TextView
        android:id="@+id/msg_text_16"
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:gravity="center"
        android:text=""
        android:textColor="#000000"
        android:textSize="32sp" />


</LinearLayout>
