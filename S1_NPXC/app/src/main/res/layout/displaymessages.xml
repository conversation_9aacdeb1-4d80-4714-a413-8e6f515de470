<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/images"
    tools:context="com.Source.S1_PP1000.PP1000.Main.MainActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="50dp">

        <TextView
            android:id="@+id/text_line_1"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:gravity="center_vertical"
            android:text="LINE 1"
            android:textAlignment="center"
            android:textColor="#FFFFFF"
            android:textSize="25sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="50dp">

        <TextView
            android:id="@+id/text_line_2"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:gravity="center_vertical"
            android:text="LINE 2"
            android:textAlignment="center"
            android:textColor="#FFFFFF"
            android:textSize="25sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="50dp">

        <TextView
            android:id="@+id/text_line_3"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:text="LINE 3"
            android:textAlignment="center"
            android:textColor="#FFFFFF"
            android:textSize="25sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="50dp">

        <TextView
            android:id="@+id/text_line_4"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:text="LINE 4"
            android:textAlignment="center"
            android:textColor="#FFFFFF"
            android:textSize="25sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="50dp">

        <TextView
            android:id="@+id/text_line_5"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:text="LINE 5"
            android:textAlignment="center"
            android:textColor="#FFFFFF"
            android:textSize="25sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="50dp">

        <TextView
            android:id="@+id/text_line_6"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:text="LINE 6"
            android:textAlignment="center"
            android:textColor="#FFFFFF"
            android:textSize="25sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="50dp">

        <TextView
            android:id="@+id/text_line_7"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:text="LINE 7"
            android:textAlignment="center"
            android:textColor="#FFFFFF"
            android:textSize="25sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="50dp">

        <TextView
            android:id="@+id/text_line_8"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:text="LINE 8"
            android:textAlignment="center"
            android:textColor="#FFFFFF"
            android:textSize="25sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="50dp">

        <TextView
            android:id="@+id/text_line_9"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:text="LINE 9"
            android:textAlignment="center"
            android:textColor="#FFFFFF"
            android:textSize="25sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="50dp">

        <TextView
            android:id="@+id/text_line_10"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:text="LINE 10"
            android:textAlignment="center"
            android:textColor="#FFFFFF"
            android:textSize="25sp" />
    </LinearLayout>
</LinearLayout>

