<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/white_bg_land"
    android:orientation="vertical"
    tools:context="com.Source.S1_NPX.NPX.Main.MainActivity">

    <TextView
        android:id="@+id/PinDigitTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/activity_login_enter_access_code"
        android:textColor="#000000"
        android:textSize="@dimen/twenty_dp"
        android:tag="Medium"
        android:gravity="center"
        android:paddingTop="@dimen/thirty_dp"
        android:paddingBottom="@dimen/ten_dp"/>

    <RelativeLayout
        android:id="@+id/login_text_button_layout"
        android:layout_below="@id/PinDigitTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center">

        <EditText
            android:id="@+id/PinDigitEditText"
            android:layout_centerHorizontal="true"
            android:background="@drawable/pin_edit_text"
            android:textColor="@color/blue"
            android:layout_width="@dimen/one_hundred_fourty_five_dp"
            android:layout_height="@dimen/thirty_dp"
            android:gravity="center"
            android:focusable="false"
            android:focusableInTouchMode="false" />

        <EditText
            android:layout_below="@id/PinDigitEditText"
            android:id="@+id/FunctionkeyText"
            android:layout_centerHorizontal="true"
            android:background="@drawable/pin_edit_text"
            android:textColor="@color/blue"
            android:layout_width="@dimen/one_hundred_fourty_five_dp"
            android:layout_height="@dimen/thirty_dp"
            android:gravity="center"
            android:focusable="false"
            android:focusableInTouchMode="false" />

    </RelativeLayout>

    <TableLayout
        android:id="@+id/button_layout"
        android:gravity="center"
        android:layout_alignParentBottom="true"
        android:paddingBottom="@dimen/two_hundred_dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TableRow
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/eight_dp"
            android:gravity="center">

            <Button
                android:text="Start"
                android:layout_width="@dimen/one_hundred_fourty_dp"
                android:layout_height="@dimen/sixty_dp"
                android:id="@+id/button_start" />

            <Button
                android:text="Start2"
                android:layout_width="@dimen/one_hundred_fourty_dp"
                android:layout_height="@dimen/sixty_dp"
                android:id="@+id/button_start2" />



        </TableRow>

    </TableLayout>

</LinearLayout>
