<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/images"
    tools:context="com.Source.S1_NPX.NPX.Main.MainActivity">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:background="@drawable/p1">
    </LinearLayout>
    <LinearLayout
        android:background="@drawable/bg_footer"
        android:layout_width="match_parent"
        android:layout_height="50dp">
        <TextView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:textColor="#FFFFFF"
            android:textSize="25sp"
            android:gravity="center_vertical"
            android:text="Enter PIN"
            android:textAlignment="center"/>
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="30dp">
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="Please Input PIN Code"
            android:textSize="30sp"
            android:textColor="#FFFFFF"/>
    </LinearLayout>
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TableLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true">
            <TableRow>
                <TextView
                    android:id="@+id/pin1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text=""
                    android:textSize="50sp"
                    android:textColor="#FFFFFF"/>
                <TextView
                    android:id="@+id/pin2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text=""
                    android:textSize="50sp"
                    android:textColor="#FFFFFF"/>
                <TextView
                    android:id="@+id/pin3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text=""
                    android:textSize="50sp"
                    android:textColor="#FFFFFF"/>
                <TextView
                    android:id="@+id/pin4"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text=""
                    android:textSize="50sp"
                    android:textColor="#FFFFFF"/>
            </TableRow>
        </TableLayout>
    </RelativeLayout>
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TableLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="0dp"
            android:layout_centerHorizontal="true">
            <TableRow
                android:layout_marginTop="10dp">
                <Button
                    android:id="@+id/button1"
                    android:text="1"
                    android:textColor="#FFFFFF"
                    android:textSize="50sp"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:background="@drawable/buttonshape"
                    android:layout_marginLeft="10dp"
                    />
                <Button
                    android:id="@+id/button2"
                    android:text="2"
                    android:textColor="#FFFFFF"
                    android:textSize="50sp"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:background="@drawable/buttonshape"
                    android:layout_marginLeft="10dp"
                    />
                <Button
                    android:id="@+id/button3"
                    android:text="3"
                    android:textColor="#FFFFFF"
                    android:textSize="50sp"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:background="@drawable/buttonshape"
                    android:layout_marginLeft="10dp"
                    />
                <Button
                    android:id="@+id/clear"
                    android:text="Clear"
                    android:textColor="#FFFFFF"
                    android:textSize="14sp"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:layout_marginTop="-18dp"
                    android:background="@drawable/buttonshape"
                    android:layout_marginLeft="10dp"
                    />
            </TableRow>
            <TableRow
                android:layout_marginTop="10dp"
                android:orientation="vertical">
                <Button
                    android:id="@+id/button4"
                    android:text="4"
                    android:textColor="#FFFFFF"
                    android:textSize="50sp"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:background="@drawable/buttonshape"
                    android:layout_marginLeft="10dp"
                    />
                <Button
                    android:id="@+id/button5"
                    android:text="5"
                    android:textColor="#FFFFFF"
                    android:textSize="50sp"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:background="@drawable/buttonshape"
                    android:layout_marginLeft="10dp"
                    />
                <Button
                    android:id="@+id/button6"
                    android:text="6"
                    android:textColor="#FFFFFF"
                    android:textSize="50sp"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:background="@drawable/buttonshape"
                    android:layout_marginLeft="10dp"
                    />
                <Button
                    android:id="@+id/button0"
                    android:text="0"
                    android:textColor="#FFFFFF"
                    android:textSize="50sp"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:background="@drawable/buttonshape"
                    android:layout_marginLeft="10dp"
                    />
            </TableRow>
            <TableRow
                android:layout_marginTop="10dp">
                <Button
                    android:id="@+id/button7"
                    android:text="7"
                    android:textColor="#FFFFFF"
                    android:textSize="50sp"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:background="@drawable/buttonshape"
                    android:layout_marginLeft="10dp"
                    />
                <Button
                    android:id="@+id/button8"
                    android:text="8"
                    android:textColor="#FFFFFF"
                    android:textSize="50sp"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:background="@drawable/buttonshape"
                    android:layout_marginLeft="10dp"
                    />
                <Button
                    android:id="@+id/button9"
                    android:text="9"
                    android:textColor="#FFFFFF"
                    android:textSize="50sp"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:background="@drawable/buttonshape"
                    android:layout_marginLeft="10dp"
                    />
                <Button
                    android:id="@+id/enter"
                    android:text="Enter"
                    android:textColor="#FFFFFF"
                    android:textSize="14sp"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:layout_marginTop="-18dp"
                    android:background="@drawable/buttonshape"
                    android:layout_marginLeft="10dp"
                    />
            </TableRow>
            <TableRow
                android:layout_marginTop="10dp">
            </TableRow>
        </TableLayout>
        <Button
            android:layout_centerHorizontal="true"
            android:layout_alignParentBottom="true"
            android:id="@+id/cancel"
            android:textColor="#FFFFFF"
            android:textSize="14sp"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginTop="-18dp"
            android:background="@drawable/cancel"
            android:layout_marginLeft="10dp"
            />
    </RelativeLayout>


</LinearLayout>

