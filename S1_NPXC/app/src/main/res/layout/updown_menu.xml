<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/images"
    tools:context="com.Source.S1_NPX.NPX.Main.MainActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:background="@drawable/p1">
    </LinearLayout>

    <LinearLayout
        android:background="@drawable/bg_footer"
        android:layout_width="match_parent"
        android:layout_height="50dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:text="BATCH REVIEW"
            android:textAlignment="center"
            android:textColor="#FFFFFF"
            android:textSize="25sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:id="@+id/msg_text_01"
            android:layout_width="match_parent"
            android:layout_height="35dp"
            android:layout_alignParentLeft="true"
            android:layout_alignParentStart="true"
            android:layout_alignParentTop="true"
            android:gravity="center_vertical"
            android:text=""
            android:textAppearance="@style/TextAppearance.AppCompat"
            android:textSize="32sp"
            android:textColor="@color/black"
            android:visibility="visible" />

        <TextView
            android:id="@+id/msg_text_02"
            android:layout_width="match_parent"
            android:layout_height="35dp"
            android:layout_alignParentLeft="true"
            android:layout_alignParentStart="true"
            android:layout_alignParentTop="true"
            android:gravity="center_vertical"
            android:text=""
            android:textAppearance="@style/TextAppearance.AppCompat"
            android:textSize="32sp"
            android:textColor="@color/black"
            android:visibility="visible" />

        <TextView
            android:id="@+id/msg_text_03"
            android:layout_width="match_parent"
            android:layout_height="35dp"
            android:layout_alignParentLeft="true"
            android:layout_alignParentStart="true"
            android:layout_alignParentTop="true"
            android:gravity="center_vertical"
            android:text=""
            android:textAppearance="@style/TextAppearance.AppCompat"
            android:textSize="32sp"
            android:textColor="@color/black"
            android:visibility="visible" />

        <TextView
            android:id="@+id/msg_text_04"
            android:layout_width="match_parent"
            android:layout_height="35dp"
            android:layout_alignParentLeft="true"
            android:layout_alignParentStart="true"
            android:layout_alignParentTop="true"
            android:gravity="center_vertical"
            android:text=""
            android:textAppearance="@style/TextAppearance.AppCompat"
            android:textSize="32sp"
            android:textColor="@color/black"
            android:visibility="visible" />

        <TextView
            android:id="@+id/msg_text_05"
            android:layout_width="match_parent"
            android:layout_height="35dp"
            android:layout_alignParentLeft="true"
            android:layout_alignParentStart="true"
            android:layout_alignParentTop="true"
            android:gravity="center_vertical"
            android:text=""
            android:textAppearance="@style/TextAppearance.AppCompat"
            android:textSize="32sp"
            android:textColor="@color/black"
            android:visibility="visible" />

        <TextView
            android:id="@+id/msg_text_06"
            android:layout_width="match_parent"
            android:layout_height="35dp"
            android:layout_alignParentLeft="true"
            android:layout_alignParentStart="true"
            android:layout_alignParentTop="true"
            android:gravity="center_vertical"
            android:text=""
            android:textAppearance="@style/TextAppearance.AppCompat"
            android:textSize="32sp"
            android:textColor="@color/black"
            android:visibility="visible" />

        <TextView
            android:id="@+id/msg_text_07"
            android:layout_width="match_parent"
            android:layout_height="35dp"
            android:layout_alignParentLeft="true"
            android:layout_alignParentStart="true"
            android:layout_alignParentTop="true"
            android:gravity="center_vertical"
            android:text=""
            android:textAppearance="@style/TextAppearance.AppCompat"
            android:textSize="32sp"
            android:textColor="@color/black"
            android:visibility="visible" />

        <TextView
            android:id="@+id/msg_text_08"
            android:layout_width="match_parent"
            android:layout_height="35dp"
            android:layout_alignParentLeft="true"
            android:layout_alignParentStart="true"
            android:layout_alignParentTop="true"
            android:gravity="center_vertical"
            android:text=""
            android:textAppearance="@style/TextAppearance.AppCompat"
            android:textSize="32sp"
            android:textColor="@color/black"
            android:visibility="visible" />

    </LinearLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">


        <Button
            android:id="@+id/button_up"
            android:layout_width="120dp"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_alignParentBottom="true"
            android:text="Up"
            android:textColor="@color/black"
            android:textSize="30sp" />

        <Button
            android:id="@+id/button_exit"
            android:layout_width="120dp"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginLeft="0dp"
            android:layout_marginRight="0dp"
            android:layout_marginBottom="0dp"
            android:layout_toLeftOf="@id/button_exit"
            android:layout_toRightOf="@id/button_up"
            android:text="EXIT"
            android:textColor="@color/black"
            android:textSize="30sp"
            android:visibility="visible" />

        <Button
            android:id="@+id/button_down"
            android:layout_width="120dp"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:text="Down"
            android:textColor="@color/black"
            android:textSize="30sp"
            android:visibility="visible" />

    </RelativeLayout>
</LinearLayout>