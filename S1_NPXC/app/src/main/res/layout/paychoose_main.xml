<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/images"
    tools:context="com.Source.S1_NPX.NPX.Main.MainActivity">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:background="@drawable/p1">
    </LinearLayout>
    <LinearLayout
        android:background="@drawable/bg_footer"
        android:layout_width="match_parent"
        android:layout_height="50dp">
        <TextView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:textColor="#FFFFFF"
            android:textSize="25sp"
            android:gravity="center_vertical"
            android:text="Select Payment Method"
            android:textAlignment="center"/>
    </LinearLayout>
    <TableLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center">
        <TableRow
            android:layout_marginTop="20dp">
            <ImageView
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:background="@drawable/card_1"
                android:id="@+id/food_1"/>
            <ImageView
                android:layout_marginLeft="30dp"
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:background="@drawable/card_2"
                android:id="@+id/food_2"/>
        </TableRow>
        <TableRow
            android:layout_marginTop="20dp">
            <ImageView
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:background="@drawable/card_3"
                android:id="@+id/food_3"/>
            <ImageView
                android:layout_marginLeft="30dp"
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:background="@drawable/card_4"
                android:id="@+id/food_4"/>
        </TableRow>
        <TableRow
            android:layout_marginTop="20dp">
            <ImageView
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:background="@drawable/card_5"
                android:id="@+id/food_5"/>
            <ImageView
                android:layout_marginLeft="30dp"
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:background="@drawable/card_6"
                android:id="@+id/food_6"/>
        </TableRow>
    </TableLayout>
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">
        <ImageView
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_centerInParent="true"
            android:background="@drawable/cancel"
            android:id="@+id/cancel"/>
    </RelativeLayout>
</LinearLayout>

