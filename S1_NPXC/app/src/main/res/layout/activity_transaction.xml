<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorTxnBackground"
    tools:context="com.Source.S1_NPX.NPX.Main.MainActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="45dp">
        <TextView
            android:id="@+id/tv_trans_tittle"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:text="Payment Type"
            android:textAlignment="center"
            style="@style/DefaultTextViewLarge" />

    </LinearLayout>

    <LinearLayout
        android:layout_height="wrap_content"
        android:layout_width="match_parent"
        android:id="@+id/container_trans_main"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/container_trans_dsp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_display_msg1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text=""
                android:layout_marginLeft="10dp"
                style="@style/DefaultTextViewNormal"/>
            <TextView
                android:id="@+id/tv_display_msg2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text=""
                android:layout_marginLeft="10dp"
                style="@style/DefaultTextViewNormal"/>
            <TextView
                android:id="@+id/tv_display_msg3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text=""
                android:layout_marginLeft="10dp"
                style="@style/DefaultTextViewNormal"
                android:visibility="visible"/>
            <TextView
                android:id="@+id/tv_display_msg4"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text=""
                android:layout_marginLeft="10dp"
                style="@style/DefaultTextViewNormal"
                android:visibility="visible"/>
            <TextView
                android:id="@+id/tv_display_msg5"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text=""
                android:layout_marginLeft="10dp"
                style="@style/DefaultTextViewNormal"
                android:visibility="visible"/>
            <TextView
                android:id="@+id/tv_display_msg6"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text=""
                android:layout_marginLeft="10dp"
                style="@style/DefaultTextViewNormal"
                android:visibility="visible"/>
            <TextView
                android:id="@+id/tv_display_msg7"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text=""
                android:layout_marginLeft="10dp"
                style="@style/DefaultTextViewNormal"
                android:visibility="visible"/>
            <TextView
                android:id="@+id/tv_display_msg8"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text=""
                android:layout_marginLeft="10dp"
                style="@style/DefaultTextViewNormal"
                android:visibility="visible"/>
        </LinearLayout>

        <RelativeLayout
        android:id="@+id/container_trans_input_main"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <!--
                <Button
                    android:id="@+id/btn_cancel1"
                    android:layout_width="150dp"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_alignParentTop="true"
                    android:text="Cancel"
                    android:textSize="30sp"
                    android:background="#FF0000"
                    android:layout_alignParentLeft="true" />

                <Button
                    android:id="@+id/btn_ok1"
                    android:layout_width="150dp"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_alignParentTop="true"
                    android:text="OK"
                    android:textSize="30sp"
                    android:background="#008000"
                    android:visibility="visible"
                    android:layout_alignParentRight="true" />-->

    </RelativeLayout>
    </LinearLayout>


</LinearLayout>