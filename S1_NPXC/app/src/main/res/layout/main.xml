<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2009 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/images"
    >
    
 <!-- android:background="#EBBE0C" -->
	<LinearLayout
		android:layout_width="match_parent"
		android:layout_height="100dp"
		android:background="@drawable/p1">
	</LinearLayout>
	<LinearLayout
		android:layout_width="match_parent"
		android:layout_height="match_parent"

		android:background="#ffffff"
		android:orientation="vertical">

		<LinearLayout
			android:layout_width="match_parent"
			android:layout_height="50dp"
			android:background="@drawable/bg_footer">

			<TextView
				android:layout_width="match_parent"
				android:layout_height="match_parent"
				android:gravity="center_vertical"
				android:text="Process Payment"
				android:textAlignment="center"
				android:textColor="#FFFFFF"
				android:textSize="25sp" />
		</LinearLayout>

		<RelativeLayout
			android:layout_width="match_parent"
			android:layout_height="match_parent"
			android:background="@drawable/wait"
			android:orientation="vertical">

			<TextView
				android:layout_width="match_parent"
				android:layout_height="wrap_content"
				android:layout_marginTop="140dp"
				android:textColor="#FFFFFF"
				android:gravity="center"
				android:text="Please Swipe, \n Insert or Tap Card"
				android:lineSpacingExtra="10dp"
				android:textSize="30dp"/>

			<ImageView
				android:id="@+id/qr_code"
				android:layout_width="150dp"
				android:layout_height="180dp"
				android:layout_centerInParent="true"
				android:layout_alignParentTop="true"
				android:layout_marginTop="100dp"
				android:background="#00000000" />
			<ImageView
				android:id="@+id/cancle"
				android:layout_width="80dp"
				android:layout_height="80dp"
				android:layout_centerInParent="true"
				android:layout_alignParentBottom="true"
				android:background="@drawable/cancel" />
			<ImageView
				android:id="@+id/canvas_space"
				android:layout_width="fill_parent"
				android:layout_height="fill_parent"
				android:scaleType="fitXY"
				/>
			<ImageView
				android:id="@+id/show_pic"

				android:layout_width="fill_parent"
				android:layout_height="fill_parent"
				android:scaleType="fitXY"

				/>
			<TextView
				android:id="@+id/up_row_view"
				android:layout_width="fill_parent"
				android:layout_height="wrap_content"
				android:layout_centerHorizontal="true"
				android:layout_marginTop="100dp"
				android:gravity="center"
				android:text="Enter Amount:"
				android:textColor="#00000000"
				android:textSize="18sp" />
			<TextView
				android:id="@+id/bottom_row_view"
				android:layout_width="fill_parent"
				android:layout_height="wrap_content"
				android:layout_marginTop="125dp"
				android:gravity="center"
				android:textColor="#00000000"
				android:textSize="18sp" />
		</RelativeLayout>
	</LinearLayout>

	<LinearLayout
	android:orientation="vertical"
    android:layout_width="0dp"
    android:layout_height="0dp"
    android:layout_weight="5">

       <RelativeLayout 
	android:orientation="vertical"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:layout_marginTop="2dip"
    android:padding="2dip">
	
    <ImageView
		android:id="@+id/imageview_welcome"
		android:src="@drawable/welcome_320_240"
		android:layout_width="fill_parent"
		android:layout_height="fill_parent"
		android:scaleType="fitXY"
		android:visibility = "gone"
		>
		</ImageView>       
 	    
	<TableLayout 
	    android:id="@+id/tablelayout_touch"
	    android:layout_width="0dp"
	    android:layout_height="0dp"
	    android:stretchColumns="0,1,2,3"
	    
	    >
<TableRow android:paddingTop="0dip" android:paddingBottom="0dip" android:layout_marginTop="5dip" >
		

		<ImageButton
		android:id="@+id/ImageButton01"
		android:src="@drawable/kbd1"
		android:layout_width="70dp"
		android:layout_height="35dp"
		android:background="@drawable/button_selector"
		android:scaleType="fitXY"
	    android:layout_margin="2dp" 
	    android:padding="1dip"
		>
		</ImageButton>
		
	
		
		<ImageButton
		android:id="@+id/ImageButton02"
		android:src="@drawable/kbd2"
		android:layout_width="70dp"
		android:layout_height="35dp"
		android:background="@drawable/button_selector"
		android:scaleType="fitXY"
		android:layout_margin="2dp" 
		android:padding="1dip"
		>
		</ImageButton>
		
	
		
		<ImageButton
		android:id="@+id/ImageButton03"
		android:src="@drawable/kbd3"
		android:layout_width="70dp"
		android:layout_height="35dp"
		android:background="@drawable/button_selector"
		android:scaleType="fitXY"
		android:layout_margin="2dp" 
		android:padding="1dip"
		>
		</ImageButton>
		
		<ImageButton
		android:id="@+id/ImageButtoncancel"
		android:src="@drawable/kbdcancel"
		android:layout_width="70dp"
		android:layout_height="35dp"
		android:background="@drawable/button_selector"
		android:scaleType="fitXY"
		android:layout_margin="2dp" 
		android:padding="1dip"
		>
		</ImageButton>
	</TableRow>
<TableRow android:paddingTop="0dip" android:paddingBottom="0dip" android:layout_marginTop="10dip" >
		<ImageButton
		android:id="@+id/ImageButton04"
		android:src="@drawable/kbd4"
		android:layout_width="70dp"
		android:layout_height="35dp"
		android:background="@drawable/button_selector"
		android:scaleType="fitXY"
	    android:layout_margin="2dp" 
	    android:padding="1dip"
		>
		</ImageButton>
		
	
		
		<ImageButton
		android:id="@+id/ImageButton05"
		android:src="@drawable/kbd5"
		android:layout_width="70dp"
		android:layout_height="35dp"
		android:background="@drawable/button_selector"
		android:scaleType="fitXY"
		android:layout_margin="2dp" 
		android:padding="1dip"
		>
		</ImageButton>
		
	
		
		<ImageButton
		android:id="@+id/ImageButton06"
		android:src="@drawable/kbd6"
		android:layout_width="70dp"
		android:layout_height="35dp"
		android:background="@drawable/button_selector"
		android:scaleType="fitXY"
		android:layout_margin="2dp" 
		android:padding="1dip"
		>
		</ImageButton>
		
		<ImageButton
		android:id="@+id/ImageButtonclear"
		android:src="@drawable/kbdclear"
		android:layout_width="70dp"
		android:layout_height="35dp"
		android:background="@drawable/button_selector"
		android:scaleType="fitXY"
		android:layout_margin="2dp" 
		android:padding="1dip"
		>
		</ImageButton>
	</TableRow>
	
    <TableRow android:paddingTop="0dip" android:paddingBottom="0dip" android:layout_marginTop="10dip" >
		<ImageButton
		android:id="@+id/ImageButton07"
		android:src="@drawable/kbd7"
		android:layout_width="70dp"
		android:layout_height="35dp"
		android:background="@drawable/button_selector"
		android:scaleType="fitXY"
	    android:layout_margin="2dp" 
	    android:padding="1dip"
		>
		</ImageButton>
		
	
		
		<ImageButton
		android:id="@+id/ImageButton08"
		android:src="@drawable/kbd8"
		android:layout_width="70dp"
		android:layout_height="35dp"
		android:background="@drawable/button_selector"
		android:scaleType="fitXY"
		android:layout_margin="2dp" 
		android:padding="1dip"
		>
		</ImageButton>
		
	
		
		<ImageButton
		android:id="@+id/ImageButton09"
		android:src="@drawable/kbd9"
		android:layout_width="70dp"
		android:layout_height="35dp"
		android:background="@drawable/button_selector"
		android:scaleType="fitXY"
		android:layout_margin="2dp" 
		android:padding="1dip"
		>
		</ImageButton>
		
		<ImageButton
		android:id="@+id/ImageButtonf"
		android:src="@drawable/kbdf"
		android:layout_width="70dp"
		android:layout_height="35dp"
		android:background="@drawable/button_selector"
		android:scaleType="fitXY"
		android:layout_margin="2dp" 
		android:padding="1dip"
		>
		</ImageButton>
	</TableRow>
<TableRow android:paddingTop="0dip" android:paddingBottom="0dip" android:layout_marginTop="10dip" >
		<ImageButton
		android:id="@+id/ImageButtondown"
		android:src="@drawable/kbddown"
		android:layout_width="70dp"
		android:layout_height="35dp"
		android:background="@drawable/button_selector"
		android:scaleType="fitXY"
	    android:layout_margin="2dp" 
	    android:padding="1dip"
		>
		</ImageButton>
		
	
		
		<ImageButton
		android:id="@+id/ImageButton00"
		android:src="@drawable/kbd0"
		android:layout_width="70dp"
		android:layout_height="35dp"
		android:background="@drawable/button_selector"
		android:scaleType="fitXY"
		android:layout_margin="2dp" 
		android:padding="1dip"
		>
		</ImageButton>
		
	
		
		<ImageButton
		android:id="@+id/ImageButtonup"
		android:src="@drawable/kbdup"
		android:layout_width="70dp"
		android:layout_height="35dp"
		android:background="@drawable/button_selector"
		android:scaleType="fitXY"
		android:layout_margin="2dp" 
		android:padding="1dip"
		>
		</ImageButton>
		
		<ImageButton
		android:id="@+id/ImageButtonenter"
		android:src="@drawable/kbdenter"
		android:layout_width="70dp"
		android:layout_height="35dp"
		android:background="@drawable/button_selector"
		android:scaleType="fitXY"
		android:layout_margin="2dp" 
		android:padding="1dip"
		>
		</ImageButton>
	</TableRow>
	
	</TableLayout>

	
	
 
    </RelativeLayout>
 	</LinearLayout>
</LinearLayout>

