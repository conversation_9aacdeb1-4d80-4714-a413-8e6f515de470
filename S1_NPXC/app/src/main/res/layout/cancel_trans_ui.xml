<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <LinearLayout
       android:layout_width="fill_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical">

    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">
        <TextView
            android:id="@+id/tv_input_amt_msg1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="FARE : $3.00"
            android:layout_gravity="center_horizontal"
            android:textSize="20dp"/>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="bottom"
        android:orientation="vertical">
        <Button
            android:id="@+id/idle_view"
            android:layout_width="720px"
            android:layout_height="806px"
            android:background="@drawable/presentcard" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">
    <TextView
        android:id="@+id/tv_input_amt_msg2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:background="@color/red"
        android:textSize="20dp"/>
    <TextView
        android:id="@+id/tv_input_amt_msg3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@color/red"
        android:textSize="20dp"
        android:layout_gravity="center_horizontal"
        android:visibility="visible"/>
    </LinearLayout>
</LinearLayout>