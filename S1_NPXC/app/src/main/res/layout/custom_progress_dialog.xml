<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/custom_dialog"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:layout_marginBottom="10dp"
            android:background="@drawable/frame"
            android:orientation="vertical"
            android:paddingLeft="10dp"
            android:paddingTop="20dp"
            android:paddingBottom="20dp">

            <TextView
                android:id="@+id/dialog_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:textColor="@color/black"
                android:textSize="18sp"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="20dp">

                <ProgressBar
                    android:id="@+id/progress_bar"
                    style="@android:style/Widget.DeviceDefault.Light.TextView.SpinnerItem"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical" >


                    <TextView
                        android:id="@+id/progress_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"
                        android:text="Success!"
                        android:textColor="@color/black"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/progress_detail1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"
                        android:textColor="@color/black"
                        android:textSize="14sp"
                        android:visibility="gone" />

                    <TextView
                        android:id="@+id/progress_detail2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"
                        android:textColor="@color/black"
                        android:textSize="14sp"
                        android:visibility="gone" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical" >


                    <TextView
                        android:id="@+id/trans_text1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"
                        android:textColor="@color/black"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:visibility="gone" />

                    <TextView
                        android:id="@+id/trans_text2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"
                        android:textColor="@color/black"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:visibility="gone" />

                    <TextView
                        android:id="@+id/trans_text3"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"
                        android:textColor="@color/black"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:visibility="gone" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</LinearLayout>