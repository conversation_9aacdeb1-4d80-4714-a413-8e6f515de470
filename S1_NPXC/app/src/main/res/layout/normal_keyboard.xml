<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/rndkbd_bg"
    android:orientation="vertical" >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="40dp"
        android:layout_marginRight="30dp"
        android:layout_marginTop="100dp"
        android:orientation="horizontal">

        <Button
            android:id="@+id/n_keyboard_btn1"
            android:layout_width="75dp"
            android:layout_height="60dp"
            android:layout_weight="0"
            android:background="@drawable/rndkbd_bg_small"
            android:gravity="center_horizontal|center_vertical"
            android:text="1"
            android:textSize="32dp" />

        <Button
            android:id="@+id/n_keyboard_btn2"
            android:layout_width="75dp"
            android:layout_height="60dp"
            android:layout_marginLeft="3dp"
            android:layout_weight="0"
            android:background="@drawable/rndkbd_bg_small"
            android:gravity="center_horizontal|center_vertical"
            android:text="2"
            android:textSize="32dp" />

        <Button
            android:id="@+id/n_keyboard_btn3"
            android:layout_width="75dp"
            android:layout_height="60dp"
            android:layout_marginLeft="3dp"
            android:layout_weight="0"
            android:background="@drawable/rndkbd_bg_small"
            android:gravity="center_horizontal|center_vertical"
            android:text="3"
            android:textSize="32dp" />

        <Button
            android:id="@+id/n_keyboard_btn_f0"
            android:layout_width="75dp"
            android:layout_height="60dp"
            android:layout_marginLeft="3dp"
            android:layout_weight="0"
            android:visibility="invisible"
            android:background="@drawable/rndkbd_bg_small"
            android:gravity="center_horizontal|center_vertical"
            android:text="F0"
            android:textSize="32dp" />

        <Button
            android:id="@+id/n_keyboard_btn_f1"
            android:layout_width="75dp"
            android:layout_height="60dp"
            android:layout_marginLeft="100dp"
            android:layout_weight="0"
            android:visibility="invisible"
            android:background="@drawable/rndkbd_bg_small"
            android:gravity="center_horizontal|center_vertical"
            android:text="F1"
            android:textSize="32dp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="40dp"
        android:layout_marginRight="30dp"
        android:layout_marginTop="3dp"
        android:orientation="horizontal" >

        <Button
            android:id="@+id/n_keyboard_btn4"
            android:layout_width="75dp"
            android:layout_height="60dp"
            android:layout_weight="0"
            android:background="@drawable/rndkbd_bg_small"
            android:gravity="center_horizontal|center_vertical"
            android:text="4"
            android:textSize="32dp" />

        <Button
            android:id="@+id/n_keyboard_btn5"
            android:layout_width="75dp"
            android:layout_height="60dp"
            android:layout_marginLeft="3dp"
            android:layout_weight="0"
            android:background="@drawable/rndkbd_bg_small"
            android:gravity="center_horizontal|center_vertical"
            android:text="5"
            android:textSize="32dp" />

        <Button
            android:id="@+id/n_keyboard_btn6"
            android:layout_width="75dp"
            android:layout_height="60dp"
            android:layout_marginLeft="3dp"
            android:layout_weight="0"
            android:background="@drawable/rndkbd_bg_small"
            android:gravity="center_horizontal|center_vertical"
            android:text="6"
            android:textSize="32dp" />


        <Button
            android:id="@+id/n_keyboard_btn_del"
            android:layout_width="75dp"
            android:layout_height="60dp"
            android:layout_marginLeft="3dp"
            android:layout_weight="0"
            android:background="@drawable/rndkbd_backspace_bg_big"
            android:gravity="center_horizontal|center_vertical"
            android:text="BS"
            android:textSize="32dp" />

        <Button
            android:id="@+id/n_keyboard_btn_f2"
            android:layout_width="75dp"
            android:layout_height="60dp"
            android:layout_marginLeft="3dp"
            android:layout_weight="0"
            android:visibility="invisible"
            android:background="@drawable/rndkbd_bg_small"
            android:gravity="center_horizontal|center_vertical"
            android:text="F2"
            android:textSize="32dp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="40dp"
        android:layout_marginRight="30dp"
        android:layout_marginTop="3dp"
        android:orientation="horizontal" >

        <Button
            android:id="@+id/n_keyboard_btn7"
            android:layout_width="75dp"
            android:layout_height="60dp"
            android:layout_weight="0"
            android:background="@drawable/rndkbd_bg_small"
            android:gravity="center_horizontal|center_vertical"
            android:text="7"
            android:textSize="32dp" />

        <Button
            android:id="@+id/n_keyboard_btn8"
            android:layout_width="75dp"
            android:layout_height="60dp"
            android:layout_marginLeft="3dp"
            android:layout_weight="0"
            android:background="@drawable/rndkbd_bg_small"
            android:gravity="center_horizontal|center_vertical"
            android:text="8"
            android:textSize="32dp" />

        <Button
            android:id="@+id/n_keyboard_btn9"
            android:layout_width="75dp"
            android:layout_height="60dp"
            android:layout_marginLeft="3dp"
            android:layout_weight="0"
            android:background="@drawable/rndkbd_bg_small"
            android:gravity="center_horizontal|center_vertical"
            android:text="9"
            android:textSize="32dp" />

        <Button
            android:id="@+id/n_keyboard_btn_clear"
            android:layout_width="75dp"
            android:layout_height="60dp"
            android:layout_marginLeft="3dp"
            android:layout_weight="0"
            android:background="@drawable/rndkbd_cancel_bg_big"
            android:gravity="center_horizontal|center_vertical"
            android:text="X"
            android:textSize="32dp" />


        <Button
            android:id="@+id/n_keyboard_btn_f3"
            android:layout_width="75dp"
            android:layout_height="60dp"
            android:layout_marginLeft="3dp"
            android:layout_weight="0"
            android:visibility="invisible"
            android:background="@drawable/rndkbd_bg_small"
            android:gravity="center_horizontal|center_vertical"
            android:text="F3"
            android:textSize="32dp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="40dp"
        android:layout_marginRight="30dp"
        android:layout_marginTop="3dp"
        android:orientation="horizontal">

        <Button
            android:id="@+id/n_keyboard_btn_star"
            android:layout_width="75dp"
            android:layout_height="60dp"
            android:layout_marginLeft="0dp"
            android:layout_weight="0"
            android:background="@drawable/rndkbd_bg_small"
            android:gravity="center_horizontal|center_vertical"
            android:text="*"
            android:textSize="32dp" />

        <Button
            android:id="@+id/n_keyboard_btn0"
            android:layout_width="75dp"
            android:layout_height="60dp"
            android:layout_marginLeft="3dp"
            android:layout_weight="0"
            android:background="@drawable/rndkbd_bg_small"
            android:gravity="center_horizontal|center_vertical"
            android:text="0"
            android:textSize="32dp" />

        <Button
            android:id="@+id/n_keyboard_btn_hash"
            android:layout_width="75dp"
            android:layout_height="60dp"
            android:layout_marginLeft="3dp"
            android:layout_weight="0"
            android:background="@drawable/rndkbd_bg_small"
            android:gravity="center_horizontal|center_vertical"
            android:text="#"
            android:textSize="32dp" />

        <Button
            android:id="@+id/n_keyboard_btn_conf"
            android:layout_width="75dp"
            android:layout_height="60dp"
            android:layout_marginLeft="3dp"
            android:layout_weight="0"
            android:background="@drawable/rndkbd_enter_bg_big"
            android:gravity="center_horizontal|center_vertical"
            android:text="OK"
            android:textSize="32dp" />


        <Button
            android:id="@+id/n_keyboard_btn_f4"
            android:layout_width="75dp"
            android:layout_height="60dp"
            android:layout_marginLeft="3dp"
            android:layout_weight="0"
            android:visibility="invisible"
            android:background="@drawable/rndkbd_bg_small"
            android:gravity="center_horizontal|center_vertical"
            android:text="F4"
            android:textSize="32dp" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="3dp"
        android:layout_marginLeft="40dp"
        android:layout_marginRight="40dp"
        android:layout_marginTop="3dp"
        android:orientation="horizontal" >


    </LinearLayout>

</LinearLayout>
