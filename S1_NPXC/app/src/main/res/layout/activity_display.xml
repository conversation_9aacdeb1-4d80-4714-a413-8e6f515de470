<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:orientation="vertical"
        android:background="@color/white">

    </LinearLayout>

    <TextView
        android:id="@+id/tv_msg1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:text=""
        android:layout_margin="8dp"
        android:textColor="@color/black"
        android:textSize="30dp" />



    <TextView
        android:id="@+id/tv_msg2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:text=""
        android:layout_margin="8dp"
        android:textColor="@color/black"
        android:textSize="30dp" />

    <TextView
        android:id="@+id/tv_msg3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:text=""
        android:layout_margin="8dp"
        android:textColor="@color/black"
        android:textSize="30dp" />

    <TextView
        android:id="@+id/tv_msg4"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:text=""
        android:layout_margin="8dp"
        android:textColor="@color/black"
        android:textSize="30dp" />

    <TextView
        android:id="@+id/tv_msg5"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:text=""
        android:layout_margin="8dp"
        android:textColor="@color/black"
        android:textSize="30dp" />

    <TextView
        android:id="@+id/tv_msg6"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:text=""
        android:layout_margin="8dp"
        android:textColor="@color/black"
        android:textSize="30dp" />

    <TextView
        android:id="@+id/tv_msg7"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:text=""
        android:layout_margin="8dp"
        android:textColor="@color/black"
        android:textSize="30dp" />

    <TextView
        android:id="@+id/tv_msg8"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:text=""
        android:layout_margin="8dp"
        android:textColor="@color/black"
        android:textSize="30dp" />

</LinearLayout>
