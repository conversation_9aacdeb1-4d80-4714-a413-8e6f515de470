<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/images"
    tools:context="com.Source.S1_NPX.NPX.Main.MainActivity">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:background="@drawable/p1">
    </LinearLayout>
    <LinearLayout
        android:background="@drawable/bg_footer"
        android:layout_width="match_parent"
        android:layout_height="50dp">
        <TextView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:textColor="#FFFFFF"
            android:textSize="25sp"
            android:gravity="center_vertical"
            android:text="Payment Approved"
            android:textAlignment="center"/>
    </LinearLayout>
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">
        <ImageView
            android:id="@+id/qr_code"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_centerInParent="true"
            android:layout_marginTop="25dp" />
    </RelativeLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp">
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Payment method:               Credit card"
            android:layout_marginLeft="10dp"
            android:textColor="#FFFFFF"
            android:textSize="20sp"/>
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Cardtype:                                         VISA"
            android:layout_marginLeft="10dp"
            android:textColor="#FFFFFF"
            android:textSize="20sp"/>
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:id="@+id/card_number"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:textColor="#FFFFFF"
            android:textSize="20sp"/>
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Holder Name:                     David Wang"
            android:layout_marginLeft="10dp"
            android:textColor="#FFFFFF"
            android:textSize="20sp"/>
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Expire Date:                           OCT. 2020"
            android:layout_marginLeft="10dp"
            android:textColor="#FFFFFF"
            android:textSize="20sp"/>
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:id="@+id/price"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Amount:                                          $45"
            android:layout_marginLeft="10dp"
            android:textColor="#FFFFFF"
            android:textSize="20sp"/>
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:id="@+id/time"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Transaction time:"
            android:layout_marginLeft="10dp"
            android:textColor="#FFFFFF"
            android:textSize="20sp"/>
    </LinearLayout>
    <RelativeLayout
        android:layout_width="fill_parent"
        android:layout_height="fill_parent">
        <ImageView
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:background="@drawable/ok"
            android:id="@+id/check"
            android:layout_centerInParent="true"
            android:layout_alignParentBottom="true"
            android:visibility="invisible"/>
    </RelativeLayout>
</LinearLayout>

