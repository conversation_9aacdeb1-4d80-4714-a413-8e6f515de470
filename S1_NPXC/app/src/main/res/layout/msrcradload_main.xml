<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/images"
    tools:context="com.Source.S1_NPX.NPX.Main.MainActivity">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:background="@drawable/p1">
    </LinearLayout>
    <LinearLayout
        android:background="@drawable/bg_footer"
        android:layout_width="match_parent"
        android:layout_height="50dp">
        <TextView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:textColor="#FFFFFF"
            android:textSize="25sp"
            android:gravity="center_vertical"
            android:text="Signature"
            android:textAlignment="center"/>
    </LinearLayout>
    <LinearLayout
        android:id="@+id/sign"
        android:layout_width="150dp"
        android:layout_height="150dp"
        android:layout_gravity="center"
        android:layout_marginTop="50dp"
        android:background="@drawable/load">
    </LinearLayout>
    <RelativeLayout
        android:layout_width="fill_parent"
        android:layout_height="fill_parent">
        <ImageView
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:background="@drawable/ok"
            android:id="@+id/check"
            android:layout_centerInParent="true"
            android:layout_alignParentBottom="true"/>
    </RelativeLayout>


</LinearLayout>

