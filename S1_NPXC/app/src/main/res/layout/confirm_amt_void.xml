<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/white"
    tools:context="com.Source.S1_NPX.NPX.Main.MainActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:orientation="vertical"
        android:background="@color/white">

    </LinearLayout>

    <ImageView
        android:id="@+id/imageView"
        android:layout_width="100dp"
        android:layout_height="50dp"
        android:layout_gravity="center"
        android:scaleType="fitCenter"
        android:src="@drawable/visa" />




    <LinearLayout
        android:layout_width="300dp"
        android:layout_height="280dp"
        android:layout_gravity="center"
        android:background="@color/white"
        android:layout_marginTop="40dp"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_input_amt_msg1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text="VOID PAYMENT"
            android:textColor="@color/text_blue"
            android:textSize="30dp" />

        <TextView
            android:id="@+id/tv_input_amt_msg2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text="ADMIN: 3.30"
            android:textColor="@color/text_blue"
            android:textSize="30dp" />

        <TextView
            android:id="@+id/tv_input_amt_msg6"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text=""
            android:textColor="@color/text_blue"
            android:textSize="20dp" />

        <TextView
            android:id="@+id/tv_input_amt_msg3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text="CARD NO:"
            android:textColor="@color/text_blue"
            android:textSize="30dp" />

        <TextView
            android:id="@+id/tv_input_amt_msg4"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text=""
            android:textColor="@color/text_blue"
            android:textSize="30dp" />



        <TextView
            android:id="@+id/tv_input_amt_msg7"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text=""
            android:textColor="@color/text_blue"
            android:textSize="20dp" />

        <TextView
            android:id="@+id/tv_input_amt_msg5"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text=""
            android:textColor="@color/text_blue"
            android:textSize="20dp" />




    </LinearLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="bottom"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_input_amt_msg9"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text=""
            android:textColor="@color/text_blue"
            android:textSize="20dp" />

        <TextView
            android:id="@+id/tv_input_amt_msg8"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text="CONFIRM?"
            android:textColor="@color/black"
            android:textSize="30dp"
            android:visibility="visible" />

        <TextView
            android:id="@+id/tv_input_amt_msg10"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text=""
            android:textColor="@color/text_blue"
            android:textSize="20dp" />



            <RelativeLayout
                android:layout_width="fill_parent"
                android:layout_height="fill_parent"
                android:gravity="bottom"
                android:orientation="vertical">

                <Button
                    android:id="@+id/IPT_OKButton"
                    style="@style/DefaultButtonOK"
                    android:layout_marginLeft="20dp"
                    android:background="@drawable/yes"
                    android:text=""
                    android:textColor="@color/black"
                    android:textSize="20sp" />

                <Button
                    android:id="@+id/IPT_CancelButton"
                    style="@style/DefaultButtonCANCEL"
                    android:layout_width="150dp"
                    android:layout_height="60dp"
                    android:layout_alignParentRight="true"
                    android:layout_marginRight="20dp"
                    android:background="@drawable/no"
                    android:text=""
                    android:textColor="@color/black"
                    android:textSize="20sp" />

            </RelativeLayout>


    </LinearLayout>

</LinearLayout>