<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="50dp"
    android:focusable="false"
    android:focusableInTouchMode="false"
    android:orientation="horizontal" >

    <ImageView
        android:id="@+id/pop_item_img"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="10dp"
        android:layout_marginLeft="10dp" />

    <TextView
        android:id="@+id/pop_item_header"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:background="@drawable/bg_pop_item"
        android:clickable="false"
        android:drawablePadding="3.0dip"
        android:gravity="center_vertical"
        android:textAlignment="center"
        android:padding="7.0dip"
        android:textColor="@color/white"
        android:layout_marginLeft="1dp"
        android:layout_weight="1"
        android:textSize="20.0sp" />

</LinearLayout>