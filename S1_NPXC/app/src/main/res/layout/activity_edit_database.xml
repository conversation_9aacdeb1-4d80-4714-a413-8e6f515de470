<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"

    tools:context="com.Source.S1_NPX.NPX.EditDatabaseActivity">

    <Button
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/edit_terminal"
        android:layout_margin="8dp"
        style="@style/DefaultButtonNormal"
        android:id="@+id/btn_edit_terminal"/>

    <Button
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/edit_wave"
        android:layout_margin="8dp"
        style="@style/DefaultButtonNormal"
        android:id="@+id/btn_edit_wave"/>

    <Button
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/edit_emv"
        android:layout_margin="8dp"
        style="@style/DefaultButtonNormal"
        android:id="@+id/btn_edit_emv"/>

    <Button
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/edit_com"
        android:layout_margin="8dp"
        style="@style/DefaultButtonNormal"
        android:id="@+id/btn_edit_com"/>

    <Button
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/edit_env"
        android:layout_margin="8dp"
        style="@style/DefaultButtonNormal"
        android:id="@+id/btn_edit_env"/>

    <Button
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/edit_eftnac"
        android:layout_margin="8dp"
        style="@style/DefaultButtonNormal"
        android:id="@+id/btn_edit_eftnac"/>
    <Button
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/edit_erm"
        android:layout_margin="8dp"
        style="@style/DefaultButtonNormal"
        android:id="@+id/btn_edit_erm"/>
    <Button
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/exit"
        android:layout_margin="8dp"
        style="@style/DefaultButtonNormal"
        android:id="@+id/btn_exit"/>

</LinearLayout>
