<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/images"
    tools:context="com.Source.S1_NPX.NPX.Main.MainActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:background="@drawable/p1">
    </LinearLayout>

    <LinearLayout
        android:background="@drawable/bg_footer"
        android:layout_width="match_parent"
        android:layout_height="50dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:text="Prompt Payment"
            android:textAlignment="center"
            android:textColor="#FFFFFF"
            android:textSize="25sp" />
    </LinearLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/AmountLabel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_alignParentTop="true"
            android:layout_marginTop="76dp"
            android:gravity="center_vertical"
            android:text="Amount:"
            android:textAppearance="@style/TextAppearance.AppCompat"
            android:textSize="32sp"
            android:visibility="visible" />

        <EditText
            android:id="@+id/AmountInput"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_alignTop="@+id/AmountLabel"
            android:layout_gravity="end"
            android:ems="10"
            android:imeOptions="actionDone"
            android:inputType="numberDecimal"
            android:maxLength="11"
            android:selectAllOnFocus="false"
            android:singleLine="false"
            android:text="0.00"
            android:textAlignment="textEnd"
            android:textSize="30sp" />

    </RelativeLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/MessageLabel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="136dp"
            android:layout_weight="1"
            android:gravity="left"
            android:text=""
            android:textAlignment="viewStart"
            android:textAppearance="@style/TextAppearance.AppCompat.Display1"
            android:visibility="visible" />
    </LinearLayout>


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <Button
            android:id="@+id/CancelButton"
            android:layout_width="150dp"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_alignParentTop="true"
            android:text="Cancel"
            android:textSize="30sp" />

        <Button
            android:id="@+id/OKButton"
            android:layout_width="150dp"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_alignParentTop="true"
            android:text="OK"
            android:textSize="30sp"
            android:visibility="visible" />

    </RelativeLayout>

</LinearLayout>