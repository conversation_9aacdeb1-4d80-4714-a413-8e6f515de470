<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/white"
    tools:context="com.Source.S1_NPX.NPX.Main.MainActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:orientation="vertical"
        android:background="@color/white">

    </LinearLayout>

    <ImageView
        android:id="@+id/ivHeaderlogo"
        android:layout_width="100dp"
        android:layout_height="50dp"
        android:layout_gravity="center"
        android:scaleType="fitCenter"
        android:src="@drawable/menulogo" />




    <LinearLayout
        android:layout_width="260dp"
        android:layout_height="220dp"
        android:layout_gravity="center"
        android:background="@drawable/corners_bg"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_input_amt_msg1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text="FARE : $3.00"
            android:textColor="@color/text_blue"
            android:textSize="30dp" />

        <TextView
            android:id="@+id/tv_input_amt_msg4"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text="+"
            android:textColor="@color/text_blue"
            android:textSize="30dp" />

        <TextView
            android:id="@+id/tv_input_amt_msg2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text="FEE GST APPLIES"
            android:textColor="@color/text_blue"
            android:textSize="30dp" />

        <TextView
            android:id="@+id/tv_input_amt_msg5"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text="="
            android:textColor="@color/text_blue"
            android:textSize="30dp" />

        <TextView
            android:id="@+id/tv_input_amt_msg6"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text="Total"
            android:textColor="@color/text_blue"
            android:textSize="30dp" />



    </LinearLayout>

    <!--    <LinearLayout-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="100dp"-->
    <!--        android:orientation="vertical">-->

    <!--    </LinearLayout>-->

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="bottom"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_input_amt_msg8"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text=""
            android:textColor="@color/black"
            android:textSize="30dp"
            android:visibility="visible" />

        <TextView
            android:id="@+id/tv_input_amt_msg3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text="CONFIRM?"
            android:textColor="@color/black"
            android:textSize="30dp"
            android:visibility="visible" />

        <TextView
            android:id="@+id/tv_input_amt_msg7"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:textColor="@color/black"
            android:textSize="30dp"
            android:visibility="visible" />

        <pl.droidsonroids.gif.GifImageView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:src="@drawable/loading" />


    </LinearLayout>


</LinearLayout>