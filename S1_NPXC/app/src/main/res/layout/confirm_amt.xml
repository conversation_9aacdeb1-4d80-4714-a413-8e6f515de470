<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/white"
    tools:context="com.Source.S1_NPX.NPX.Main.MainActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:orientation="vertical"
        android:background="@color/white">

    </LinearLayout>

    <ImageView
        android:id="@+id/imageView"
        android:layout_width="100dp"
        android:layout_height="50dp"
        android:layout_gravity="center"
        android:scaleType="fitCenter"
        android:src="@drawable/visa" />




    <LinearLayout
        android:layout_width="260dp"
        android:layout_height="280dp"
        android:layout_gravity="center"
        android:background="@drawable/corners_bg"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_input_amt_msg1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text="FARE: $3.00"
            android:textColor="@color/text_blue"
            android:textSize="30dp" />

        <TextView
            android:id="@+id/tv_input_amt_msg2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text="+"
            android:textColor="@color/text_blue"
            android:textSize="30dp" />

        <TextView
            android:id="@+id/tv_input_amt_msg3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text="Admin: $3.00"
            android:textColor="@color/text_blue"
            android:textSize="30dp" />

        <TextView
            android:id="@+id/tv_input_amt_msg4"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text="+"
            android:textColor="@color/text_blue"
            android:textSize="30dp" />

        <TextView
            android:id="@+id/tv_input_amt_msg5"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text="GST: $3.00"
            android:textColor="@color/text_blue"
            android:textSize="30dp" />

        <TextView
            android:id="@+id/tv_input_amt_msg6"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text="="
            android:textColor="@color/text_blue"
            android:textSize="30dp" />

        <TextView
            android:id="@+id/tv_input_amt_msg7"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text="Total: $3.00"
            android:textColor="@color/text_blue"
            android:textSize="30dp" />


    </LinearLayout>

<!--    <LinearLayout-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="100dp"-->
<!--        android:orientation="vertical">-->

<!--    </LinearLayout>-->

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="bottom"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_input_amt_msg9"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text=""
            android:textColor="@color/text_blue"
            android:textSize="30dp" />

        <TextView
            android:id="@+id/tv_input_amt_msg8"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text="CONFIRM?"
            android:textColor="@color/black"
            android:textSize="30dp"
            android:visibility="visible" />

        <TextView
            android:id="@+id/tv_input_amt_msg10"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text=""
            android:textColor="@color/text_blue"
            android:textSize="30dp" />



            <RelativeLayout
                android:layout_width="fill_parent"
                android:layout_height="fill_parent"
                android:gravity="bottom"
                android:orientation="vertical">

                <Button
                    android:id="@+id/IPT_OKButton"
                    style="@style/DefaultButtonOK"
                    android:layout_marginLeft="20dp"
                    android:background="@drawable/yes"
                    android:text=""
                    android:textColor="@color/black"
                    android:textSize="30sp" />

                <Button
                    android:id="@+id/IPT_CancelButton"
                    style="@style/DefaultButtonCANCEL"
                    android:layout_width="150dp"
                    android:layout_height="60dp"
                    android:layout_alignParentRight="true"
                    android:layout_marginRight="20dp"
                    android:background="@drawable/no"
                    android:text=""
                    android:textColor="@color/black"
                    android:textSize="30sp" />

            </RelativeLayout>

<!--        <Button-->
<!--            android:id="@+id/idle_view"-->
<!--            android:layout_width="720px"-->
<!--            android:layout_height="400px"-->
<!--            android:background="@drawable/login_button_selector" />-->


    </LinearLayout>

<!--    <LinearLayout-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="40dp"-->
<!--        android:orientation="vertical">-->


<!--    <RelativeLayout-->
<!--        android:layout_width="fill_parent"-->
<!--        android:layout_height="fill_parent"-->
<!--        android:gravity="bottom"-->
<!--        android:orientation="vertical">-->

<!--        <Button-->
<!--            android:id="@+id/IPT_CancelButton"-->
<!--            style="@style/DefaultButtonCANCEL"-->
<!--            android:background="@drawable/shape_shadow_cancel"-->
<!--            android:text="CANCEL"-->
<!--            android:textColor="@color/white"-->
<!--            android:textSize="20sp" />-->

<!--        <Button-->
<!--            android:id="@+id/IPT_OKButton"-->
<!--            style="@style/DefaultButtonOK"-->
<!--            android:layout_width="150dp"-->
<!--            android:layout_height="60dp"-->
<!--            android:layout_alignParentRight="true"-->
<!--            android:layout_marginRight="0dp"-->
<!--            android:background="@drawable/shape_shadow_enter"-->
<!--            android:text="ENTER"-->
<!--            android:textColor="@color/bg_white"-->
<!--            android:textSize="20sp" />-->

<!--    </RelativeLayout>-->
<!--    </LinearLayout>-->
</LinearLayout>