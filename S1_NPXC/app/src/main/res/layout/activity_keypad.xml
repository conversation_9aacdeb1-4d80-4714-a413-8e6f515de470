<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    tools:context="com.Source.S1_NPX.NPX.Main.MainActivity">

    <android.support.constraint.ConstraintLayout
        android:id="@+id/trans_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <android.support.constraint.ConstraintLayout
            android:id="@+id/constraintLayout"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginBottom="16dp"
            android:layout_marginEnd="8dp"
            android:layout_marginLeft="8dp"
            android:layout_marginRight="8dp"
            android:layout_marginStart="8dp"
            android:layout_marginTop="8dp"
            android:background="@drawable/shape"
            app:layout_constraintBottom_toTopOf="@+id/section_label_Amt"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:minHeight="?android:attr/listPreferredItemHeight">

        </android.support.constraint.ConstraintLayout>

        <ListView
            android:id="@+id/lsv_LCDTPrintxx"
            android:layout_width="0dp"
            android:layout_height="280dp"
            android:layout_marginEnd="16dp"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:divider="@null"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/sample_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="8dp"
            android:layout_marginStart="8dp"
            android:layout_marginTop="8dp"
            android:gravity="bottom"
            android:maxHeight="280dp"
            android:scrollbars="vertical"
            android:textColor="@android:color/black"
            android:textSize="20sp"
            android:textStyle="bold"
            android:visibility="visible"
            app:layout_constraintEnd_toEndOf="@+id/constraintLayout"
            app:layout_constraintStart_toStartOf="@+id/constraintLayout"
            app:layout_constraintTop_toTopOf="@+id/constraintLayout" />

        <TextView
            android:id="@+id/section_label_Amt"
            android:layout_width="63dp"
            android:layout_height="21dp"
            android:layout_marginLeft="8dp"
            android:layout_marginStart="8dp"
            android:layout_marginTop="320dp"
            android:text="Amount :"
            android:textColor="@android:color/black"
            android:textSize="15sp"
            android:textStyle="bold"
            android:visibility="invisible"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <EditText
            android:id="@+id/edtAmt"
            android:layout_width="0dp"
            android:layout_height="46dp"
            android:layout_marginStart="8dp"
            android:layout_marginLeft="8dp"
            android:layout_marginTop="8dp"
            android:ems="10"
            android:inputType="phone"
            android:maxLength="12"
            android:text="1000"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/section_label_Amt"
            app:layout_constraintStart_toEndOf="@+id/section_label_Amt"
            app:layout_constraintTop_toTopOf="@+id/section_label_Amt" />

        <LinearLayout
            android:id="@+id/linearLayout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:layout_marginLeft="8dp"
            android:layout_marginRight="8dp"
            android:layout_marginStart="8dp"
            android:layout_marginTop="16dp"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/constraintLayout">

            <Button
                android:id="@+id/btnKey"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_alignParentLeft="true"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:background="@drawable/shape_gray"
                android:minHeight="60dp"
                android:onClick="btnKey_Click"
                android:text="Key"
                android:textAllCaps="false"
                android:textSize="15sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/edtAmt" />
        </LinearLayout>

        <EditText
            android:id="@+id/edtLogxx"
            android:layout_width="0dp"
            android:layout_height="90dp"
            android:layout_centerVertical="true"
            android:layout_marginBottom="16dp"
            android:layout_marginEnd="8dp"
            android:layout_marginLeft="8dp"
            android:layout_marginRight="8dp"
            android:layout_marginStart="8dp"
            android:clickable="true"
            android:editable="false"
            android:ems="12"
            android:focusable="false"
            android:inputType="textMultiLine"
            android:maxLines="15"
            android:text=""
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:ignore="KeyboardInaccessibleWidget" />

    </android.support.constraint.ConstraintLayout>


    <RelativeLayout
        android:id="@+id/activity_keypad1"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clickable="true"
        android:focusable="true"
        android:orientation="horizontal"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/txvOnlinePin"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_alignParentStart="true"
            android:layout_alignParentTop="true"
            android:gravity="center_horizontal"
            android:text="Enter Online Pin : "
            android:textColor="@android:color/black"
            android:textSize="20sp" />

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/activity_keypad"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/txvOfflinePin"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_alignParentStart="true"
            android:layout_alignParentTop="true"
            android:layout_centerHorizontal="true"
            android:gravity="center_horizontal"
            android:textColor="@android:color/black"
            android:textSize="20sp" />

        <TextView
            android:id="@+id/textViewPinDigitEx"
            android:layout_width="210dp"
            android:layout_height="40dp"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="30dp"
            android:background="#4f000000"
            android:gravity="center"
            android:textColor="@android:color/black"
            android:textSize="20sp" />

    </RelativeLayout>


</android.support.constraint.ConstraintLayout>
