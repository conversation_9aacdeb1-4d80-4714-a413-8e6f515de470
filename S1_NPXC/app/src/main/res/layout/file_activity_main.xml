<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/images"
    android:orientation="vertical"
    tools:context="com.Source.S1_NPX.NPX.Main.MainActivity">

    <include layout="@layout/content_main" />
    
<!--
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:background="@drawable/p1"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@drawable/bg_footer">

        <TextView
            android:id="@+id/title"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:text="Select Item"
            android:textAlignment="center"
            android:textColor="#FFFFFF"
            android:textSize="25sp" />
    </LinearLayout>

    <TableLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center">

        <TableRow android:layout_marginTop="20dp">

            <ImageView
                android:id="@+id/food_1"
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:background="@drawable/food_1" />

            <TableLayout>

                <TableRow>

                    <TextView
                        android:layout_width="50dp"
                        android:layout_height="60dp"
                        android:gravity="bottom"
                        android:text="$45"
                        android:textAlignment="center"
                        android:textColor="#000000"
                        android:textSize="20sp" />
                </TableRow>

                <TableRow>

                    <TextView
                        android:id="@+id/food_1_count"
                        android:layout_width="50dp"
                        android:layout_height="40dp"
                        android:gravity="bottom"
                        android:text=""
                        android:textAlignment="center"
                        android:textColor="#FFFFFF"
                        android:textSize="20sp" />
                </TableRow>
            </TableLayout>

            <ImageView
                android:id="@+id/food_2"
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:background="@drawable/food_2" />

            <TableLayout>

                <TableRow>

                    <TextView
                        android:layout_width="50dp"
                        android:layout_height="60dp"
                        android:gravity="bottom"
                        android:text="$35"
                        android:textAlignment="center"
                        android:textColor="#000000"
                        android:textSize="20sp" />
                </TableRow>

                <TableRow>

                    <TextView
                        android:id="@+id/food_2_count"
                        android:layout_width="50dp"
                        android:layout_height="40dp"
                        android:gravity="bottom"
                        android:text=""
                        android:textAlignment="center"
                        android:textColor="#FFFFFF"
                        android:textSize="20sp" />
                </TableRow>
            </TableLayout>
        </TableRow>

        <TableRow android:layout_marginTop="20dp">

            <ImageView
                android:id="@+id/food_3"
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:background="@drawable/food_3" />

            <TableLayout>

                <TableRow>

                    <TextView
                        android:layout_width="50dp"
                        android:layout_height="60dp"
                        android:gravity="bottom"
                        android:text="$160"
                        android:textAlignment="center"
                        android:textColor="#000000"
                        android:textSize="20sp" />
                </TableRow>

                <TableRow>

                    <TextView
                        android:id="@+id/food_3_count"
                        android:layout_width="50dp"
                        android:layout_height="40dp"
                        android:gravity="bottom"
                        android:text=""
                        android:textAlignment="center"
                        android:textColor="#FFFFFF"
                        android:textSize="20sp" />
                </TableRow>
            </TableLayout>

            <ImageView
                android:id="@+id/food_4"
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:background="@drawable/food_4" />

            <TableLayout>

                <TableRow>

                    <TextView
                        android:layout_width="50dp"
                        android:layout_height="60dp"
                        android:gravity="bottom"
                        android:text="$30"
                        android:textAlignment="center"
                        android:textColor="#000000"
                        android:textSize="20sp" />
                </TableRow>

                <TableRow>

                    <TextView
                        android:id="@+id/food_4_count"
                        android:layout_width="50dp"
                        android:layout_height="40dp"
                        android:gravity="bottom"
                        android:text=""
                        android:textAlignment="center"
                        android:textColor="#FFFFFF"
                        android:textSize="20sp" />
                </TableRow>
            </TableLayout>
        </TableRow>

        <TableRow android:layout_marginTop="20dp">

            <ImageView
                android:id="@+id/food_5"
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:background="@drawable/food_5" />

            <TableLayout>

                <TableRow>

                    <TextView
                        android:layout_width="50dp"
                        android:layout_height="60dp"
                        android:gravity="bottom"
                        android:text="$40"
                        android:textAlignment="center"
                        android:textColor="#000000"
                        android:textSize="20sp" />
                </TableRow>

                <TableRow>

                    <TextView
                        android:id="@+id/food_5_count"
                        android:layout_width="50dp"
                        android:layout_height="40dp"
                        android:gravity="bottom"
                        android:text=""
                        android:textAlignment="center"
                        android:textColor="#FFFFFF"
                        android:textSize="20sp" />
                </TableRow>
            </TableLayout>

            <ImageView
                android:id="@+id/food_6"
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:background="@drawable/food_6" />

            <TableLayout>

                <TableRow>

                    <TextView
                        android:layout_width="50dp"
                        android:layout_height="60dp"
                        android:gravity="bottom"
                        android:text="$25"
                        android:textAlignment="center"
                        android:textColor="#000000"
                        android:textSize="20sp" />
                </TableRow>

                <TableRow>

                    <TextView
                        android:id="@+id/food_6_count"
                        android:layout_width="50dp"
                        android:layout_height="40dp"
                        android:gravity="bottom"
                        android:text=""
                        android:textAlignment="center"
                        android:textColor="#FFFFFF"
                        android:textSize="20sp" />
                </TableRow>
            </TableLayout>
        </TableRow>
    </TableLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/refresh"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:background="@drawable/refresh"
            android:visibility="invisible" />

        <TextView
            android:id="@+id/result"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:text="No Item selected"
            android:textAlignment="center"
            android:textColor="#FFFFFF"
            android:textSize="25sp" />

        <ImageView
            android:id="@+id/check"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_alignParentRight="true"
            android:background="@drawable/check"
            android:visibility="invisible" />
    </RelativeLayout>
-->
</LinearLayout>
