<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/images"
    android:orientation="vertical"
    tools:context="com.Source.S1_PP1000.PP1000.Main.MainActivity">


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#FFFFFF">

        <android.support.v4.view.ViewPager
            android:id="@+id/qr_viewpager"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/s1l_idle_land" >

        </android.support.v4.view.ViewPager>

        <ImageView
            android:id="@+id/img_result_qr"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:src="@drawable/qrcode" />

    </RelativeLayout>
</LinearLayout>
