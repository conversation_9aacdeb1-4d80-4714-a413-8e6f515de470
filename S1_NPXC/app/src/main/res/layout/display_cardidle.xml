<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.Source.S1_NPX.NPX.Main.MainActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!--<TextView-->
            <!--android:id="@+id/msg_text_01"-->
            <!--android:layout_width="match_parent"-->
            <!--android:layout_height="15dp"-->
            <!--android:layout_alignParentLeft="true"-->
            <!--android:layout_alignParentStart="true"-->
            <!--android:layout_alignParentTop="true"-->
            <!--android:gravity="center_vertical"-->
            <!--android:text=""-->
            <!--android:textAppearance="@style/TextAppearance.AppCompat"-->
            <!--android:textSize="32sp"-->
            <!--android:visibility="visible" />-->

        <!--<TextView-->
            <!--android:id="@+id/msg_text_02"-->
            <!--android:layout_width="match_parent"-->
            <!--android:layout_height="15dp"-->
            <!--android:layout_alignParentLeft="true"-->
            <!--android:layout_alignParentStart="true"-->
            <!--android:layout_alignParentTop="true"-->
            <!--android:gravity="center_vertical"-->
            <!--android:text=""-->
            <!--android:textAppearance="@style/TextAppearance.AppCompat"-->
            <!--android:textSize="32sp"-->
            <!--android:visibility="visible" />-->

        <!--<TextView-->
            <!--android:id="@+id/msg_text_03"-->
            <!--android:layout_width="match_parent"-->
            <!--android:layout_height="30dp"-->
            <!--android:layout_alignParentLeft="true"-->
            <!--android:layout_alignParentStart="true"-->
            <!--android:layout_alignParentTop="true"-->
            <!--android:gravity="center_vertical"-->
            <!--android:text=""-->
            <!--android:textAppearance="@style/TextAppearance.AppCompat"-->
            <!--android:textSize="32sp"-->
            <!--android:visibility="visible" />-->


        <pl.droidsonroids.gif.GifImageView
            android:id="@+id/gif1"
            android:adjustViewBounds="true"
            android:scaleType="fitCenter"
            android:layout_width="match_parent"
            android:layout_height="1280px" />

    </LinearLayout>


</LinearLayout>