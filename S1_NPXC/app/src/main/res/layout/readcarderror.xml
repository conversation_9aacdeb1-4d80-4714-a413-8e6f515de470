<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/white"
    tools:context="com.Source.S1_NPX.NPX.Main.MainActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:orientation="vertical"
        android:background="@color/white">

    </LinearLayout>

    <ImageView
        android:id="@+id/imageview"
        android:layout_width="100dp"
        android:layout_height="50dp"
        android:layout_gravity="center"
        android:scaleType="fitCenter"
        android:src="@drawable/visa" />




    <LinearLayout
        android:layout_width="300dp"
        android:layout_height="100dp"
        android:layout_gravity="center"
        android:background="@drawable/corners_bg"
        android:layout_marginTop="40dp"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_input_amt_msg1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text="VOID PAYMENT"
            android:textColor="@color/text_blue"
            android:textSize="30dp" />

        <TextView
            android:id="@+id/tv_input_amt_msg2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text="ADMIN: 3.30"
            android:textColor="@color/text_blue"
            android:textSize="30dp" />

    </LinearLayout>

<!--    <LinearLayout-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="100dp"-->
<!--        android:orientation="vertical">-->

<!--    </LinearLayout>-->

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="bottom"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_input_amt_msg9"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text=""
            android:textColor="@color/text_blue"
            android:textSize="20dp" />

        <ImageView
            android:id="@+id/imageviewapprove"
            android:layout_width="720px"
            android:layout_height="140px"
            android:layout_gravity="center"
            android:scaleType="fitXY"
            android:layout_marginBottom="40dp"
            android:src="@drawable/readcarderror" />

    </LinearLayout>

</LinearLayout>