<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                xmlns:tools="http://schemas.android.com/tools"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:paddingLeft="@dimen/activity_horizontal_margin"
                android:paddingRight="@dimen/activity_horizontal_margin"
                android:paddingTop="@dimen/activity_vertical_margin"
                android:paddingBottom="@dimen/activity_vertical_margin"
                tools:context=".db.ChooseFileActivityFragment">

  <Button
      android:id="@+id/btn_choose_a_file"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_centerHorizontal="true"
      android:text="@string/btn_choose_a_file"
      android:visibility="visible"/>

  <Button
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:text="@string/btn_choose_a_folder"
      android:id="@+id/btn_choose_a_folder"
      android:layout_below="@+id/btn_choose_a_file"
      android:layout_centerHorizontal="true"
      android:visibility="gone"/>

  <Button
      android:id="@+id/btn_choose_any_file"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_below="@+id/btn_choose_a_folder"
      android:layout_centerHorizontal="true"
      android:text="@string/btn_open_db"/>

  <Button
      android:id="@+id/btn_choose_multiple"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_below="@+id/btn_choose_any_file"
      android:layout_centerHorizontal="true"
      android:text="@string/btn_choose_multiple"
      android:visibility="gone"/>

  <TextView
      android:text="@string/hello_world"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:id="@+id/textView"
      android:layout_below="@+id/btn_choose_multiple"
      android:layout_centerHorizontal="true"/>

  <ImageView
      android:layout_width="match_parent"
      android:layout_height="match_parent"
      android:id="@+id/imageView"
      android:layout_below="@+id/textView"
      android:layout_centerHorizontal="true"
      android:background="@android:color/transparent"/>

</RelativeLayout>
