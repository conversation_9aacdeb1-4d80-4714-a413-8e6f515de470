<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/white_bg_land"
    android:orientation="vertical"
    tools:context="com.Source.S1_NPX.NPX.Main.MainActivity">

    <TextView
        android:id="@+id/msg_text"
        android:layout_width="320dp"
        android:layout_height="35dp"
        android:gravity="center"
        android:text=""
        android:textColor="#000000"
        android:maxEms="10"
        android:textSize="28sp" />

</LinearLayout>
