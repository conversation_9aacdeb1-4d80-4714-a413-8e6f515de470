# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in C:\Users\<USER>\AppData\Local\Android\Sdk/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Add any project specific keep options here:

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile
# to keep the NativePlatformThread:

-keep class com.android.support.** { *; }
-keep class com.squareup.okhttp.** { *; }
-keep class com.github.bumptech.glide.** { *; }
-keep class com.android.support.constraint.** { *; }
-keep class com.jakewharton.timber.** { *; }
-keep class com.qmuiteam.** { *; }
-keep class pl.droidsonroids.gif.** { *; }
-keep class com.google.zxing.** { *; }

-keep class com.Source.S1_NPX.NPX.Main.** { *; }

#  Preserve all native method names and the names of their classes
-keepclasseswithmembernames class * {
    native <methods>;
}