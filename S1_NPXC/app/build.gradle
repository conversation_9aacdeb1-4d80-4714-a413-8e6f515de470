apply plugin: 'com.android.application'
def  java_lib_dir = "C:/Source/VEGA_Library/ANDROID/libs/S1F/Ver0001"
def  jni_lib_dir = 'C:/Source/VEGA_Library/ANDROID/jniLibs/S1F/Ver0001'

android {
    compileSdkVersion 26
    buildToolsVersion "28.0.3"
    defaultConfig {
        applicationId 'com.Source.S1_NPX.NPX'
        minSdkVersion 16
        targetSdkVersion 26
        versionCode 16916
        versionName "000005"
        externalNativeBuild {
            cmake {
                cppFlags ""
                arguments '-DPLATFORM_TARGET=android',
                        "-DJNI_LIB_PATH=${jni_lib_dir}/armeabi-v7a",
                        '-DANDROID_TOOLCHAIN=clang'
            }
        }
    }

    sourceSets.main {
//        jniLibs.srcDirs = ['src/main/jniLibs']
        jniLibs.srcDirs = [jni_lib_dir]
        jni.srcDirs = []
    }

    packagingOptions {
        pickFirst 'lib/armeabi-v7a/libcrypto.so'
        pickFirst 'lib/armeabi-v7a/libcurl.so'
        pickFirst 'lib/armeabi-v7a/libssl.so'
    }

    signingConfigs {
        release {
            storeFile file("./SignAPK/SignDemo.jks")
            storePassword '123456'
            keyAlias 'demo'
            keyPassword '123456'
        }

        debug {
            storeFile file("./SignAPK/SignDemo.jks")
            storePassword '123456'
            keyAlias 'demo'
            keyPassword '123456'
        }
    }

    buildTypes {
        release {
            // Enables code shrinking, obfuscation, and optimization
            minifyEnabled true
            // Enables resource shrinking
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }
        debug {
            jniDebuggable true
            //            shrinkResources true
            minifyEnabled true
            // Enables resource shrinking
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.debug
        }
    }

    flavorDimensions 'cpuArch'
    productFlavors {
        arm7 {
            dimension 'cpuArch'
            ndk {
                abiFilter 'armeabi-v7a'
            }
        }

    }
    applicationVariants.all { variant ->
        variant.outputs.all { output ->
            def project = "S1_NPX"
            def newApkName = project + ".apk"
            outputFileName = new File(newApkName)
        }
    }

    externalNativeBuild {
        cmake {
            path "CMakeLists.txt"
        }
    }

}
repositories {
    mavenCentral()
    flatDir {
//        dirs 'libs' //this way we can find the .aar file in libs folder
        dirs java_lib_dir
    }
}
dependencies {
    implementation fileTree(include: ['*.jar', '*.aar'], dir: java_lib_dir)
    implementation 'com.android.support:appcompat-v7:25.3.1'
    implementation 'com.android.support:support-v4:25.3.1'
    implementation 'com.squareup.okhttp:okhttp:2.5.0'
    implementation 'com.github.bumptech.glide:glide:3.7.0'
    implementation 'com.android.support.constraint:constraint-layout:1.0.2'
    implementation 'com.jakewharton.timber:timber:4.7.0'
    //compile 'com.qmuiteam:qmui:1.1.3'
    compile 'pl.droidsonroids.gif:android-gif-drawable:1.2.7'

	//Check Java Memory Leak, add new module to check java memory
    // debugImplementation 'com.squareup.leakcanary:leakcanary-android:1.6.3'
    // releaseImplementation 'com.squareup.leakcanary:leakcanary-android-no-op:1.6.3'
    // debugImplementation 'com.squareup.leakcanary:leakcanary-support-fragment:1.6.3'

}
