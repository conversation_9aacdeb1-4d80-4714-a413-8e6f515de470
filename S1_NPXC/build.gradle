// Top-level build file where you can add configuration options common to all sub-projects/modules.
apply plugin: 'org.sonarqube'
buildscript {
    repositories {
        jcenter()
        google()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:3.5.3'
        classpath 'org.sonarsource.scanner.gradle:sonarqube-gradle-plugin:2.7'
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        jcenter()
        google()
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
